{"date": "July 20", "url": "https://wikipedia.org/wiki/July_20", "data": {"Events": [{"year": "70", "text": "Siege of Jerusalem: <PERSON>, son of emperor <PERSON><PERSON><PERSON><PERSON>, storms the Fortress of Antonia north of the Temple Mount. The Roman army is drawn into street fights with the Zealots.", "html": "70 - <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">Siege of Jerusalem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of emperor <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a>, storms the <a href=\"https://wikipedia.org/wiki/Antonia_Fortress\" title=\"Antonia Fortress\">Fortress of Antonia</a> north of the <a href=\"https://wikipedia.org/wiki/Temple_Mount\" title=\"Temple Mount\">Temple Mount</a>. The <a href=\"https://wikipedia.org/wiki/Roman_army\" title=\"Roman army\">Roman army</a> is drawn into street fights with the <a href=\"https://wikipedia.org/wiki/Zealots_(Judea)\" class=\"mw-redirect\" title=\"Zealots (Judea)\">Zealots</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">Siege of Jerusalem</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of emperor <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a>, storms the <a href=\"https://wikipedia.org/wiki/Antonia_Fortress\" title=\"Antonia Fortress\">Fortress of Antonia</a> north of the <a href=\"https://wikipedia.org/wiki/Temple_Mount\" title=\"Temple Mount\">Temple Mount</a>. The <a href=\"https://wikipedia.org/wiki/Roman_army\" title=\"Roman army\">Roman army</a> is drawn into street fights with the <a href=\"https://wikipedia.org/wiki/Zealots_(Judea)\" class=\"mw-redirect\" title=\"Zealots (Judea)\">Zealots</a>.", "links": [{"title": "Siege of Jerusalem (AD 70)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}, {"title": "Antonia Fortress", "link": "https://wikipedia.org/wiki/Antonia_Fortress"}, {"title": "Temple Mount", "link": "https://wikipedia.org/wiki/Temple_Mount"}, {"title": "Roman army", "link": "https://wikipedia.org/wiki/Roman_army"}, {"title": "Zealots (Judea)", "link": "https://wikipedia.org/wiki/Zealots_(Judea)"}]}, {"year": "792", "text": "Kardam of Bulgaria defeats Byzantine Emperor <PERSON> at the Battle of Marcellae.", "html": "792 - <a href=\"https://wikipedia.org/wiki/Kardam_of_Bulgaria\" title=\"Kardam of Bulgaria\"><PERSON><PERSON><PERSON> of Bulgaria</a> defeats <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Constantine_VI\" title=\"Constantine VI\"><PERSON> VI</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marcellae_(792)\" class=\"mw-redirect\" title=\"Battle of Marcellae (792)\">Battle of Marcellae</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kardam_of_Bulgaria\" title=\"Kardam of Bulgaria\"><PERSON><PERSON><PERSON> of Bulgaria</a> defeats <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/Constantine_VI\" title=\"Constantine VI\"><PERSON> VI</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marcellae_(792)\" class=\"mw-redirect\" title=\"Battle of Marcellae (792)\">Battle of Marcellae</a>.", "links": [{"title": "Kardam of Bulgaria", "link": "https://wikipedia.org/wiki/Kardam_of_Bulgaria"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "Constantine VI", "link": "https://wikipedia.org/wiki/Constantine_VI"}, {"title": "Battle of Marcellae (792)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>_(792)"}]}, {"year": "911", "text": "<PERSON><PERSON> lays siege to Chartres.", "html": "911 - <a href=\"https://wikipedia.org/wiki/Rollo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Siege_of_Chartres_(911)\" title=\"Siege of Chartres (911)\">lays siege to Chartres</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rollo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Siege_of_Chartres_(911)\" title=\"Siege of Chartres (911)\">lays siege to Chartres</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rollo"}, {"title": "Siege of Chartres (911)", "link": "https://wikipedia.org/wiki/Siege_of_Chartres_(911)"}]}, {"year": "1189", "text": "<PERSON> of England officially invested as Duke of Normandy.", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> officially invested as <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> officially invested as <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Duke of Normandy", "link": "https://wikipedia.org/wiki/Duke_of_Normandy"}]}, {"year": "1225", "text": "Treaty of San Germano is signed at San Germano between Holy Roman Emperor <PERSON> and Pope <PERSON>. A Dominican named <PERSON><PERSON><PERSON> is responsible for the negotiations.", "html": "1225 - <a href=\"https://wikipedia.org/wiki/Treaty_of_San_Germano\" title=\"Treaty of San Germano\">Treaty of San Germano</a> is signed at <a href=\"https://wikipedia.org/wiki/Cassino\" title=\"Cassino\">San Germano</a> between Holy Roman Emperor <PERSON> and <PERSON>. A Dominican named <PERSON><PERSON><PERSON> is responsible for the negotiations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_San_Germano\" title=\"Treaty of San Germano\">Treaty of San Germano</a> is signed at <a href=\"https://wikipedia.org/wiki/Cassino\" title=\"Cassino\">San Germano</a> between Holy Roman Emperor <PERSON> and Pope <PERSON>. A Dominican named <PERSON><PERSON><PERSON> is responsible for the negotiations.", "links": [{"title": "Treaty of San Germano", "link": "https://wikipedia.org/wiki/Treaty_of_San_Germano"}, {"title": "Cassino", "link": "https://wikipedia.org/wiki/Cassino"}]}, {"year": "1398", "text": "The Battle of Kellistown was fought on this day between the forces of the English led by <PERSON>, 4th Earl of March against the O'Byrnes and O'Tooles under the command of <PERSON>, the most powerful Chieftain in Leinster.", "html": "1398 - The Battle of Kellistown was fought on this day between the forces of the <a href=\"https://wikipedia.org/wiki/English_people\" title=\"English people\">English</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl of <PERSON>\"><PERSON>, 4th Earl of <PERSON></a> against the <PERSON> and <PERSON> under the command of <a href=\"https://wikipedia.org/wiki/Art_%C3%93g_mac_Murchadha_Caomh%C3%A1nach\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the most powerful Chieftain in Leinster.", "no_year_html": "The Battle of Kellistown was fought on this day between the forces of the <a href=\"https://wikipedia.org/wiki/English_people\" title=\"English people\">English</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_March\" title=\"<PERSON>, 4th Earl of <PERSON>\"><PERSON>, 4th Earl of <PERSON></a> against the <PERSON> and <PERSON> under the command of <a href=\"https://wikipedia.org/wiki/Art_%C3%93g_mac_Murchadha_Caomh%C3%A1nach\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the most powerful Chieftain in Leinster.", "links": [{"title": "English people", "link": "https://wikipedia.org/wiki/English_people"}, {"title": "<PERSON>, 4th Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_%C3%93g_mac_<PERSON><PERSON><PERSON>_Caomh%C3%A1nach"}]}, {"year": "1402", "text": "Ottoman-Timurid Wars: Battle of Ankara: <PERSON><PERSON>, ruler of Timurid Empire, defeats forces of the Ottoman Empire sultan <PERSON><PERSON><PERSON>.", "html": "1402 - Ottoman-Timurid Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Ankara\" title=\"Battle of Ankara\">Battle of Ankara</a>: <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Timurid_Empire\" title=\"Timurid Empire\">Timurid Empire</a>, defeats forces of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> sultan <a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\">Bayezid I</a>.", "no_year_html": "Ottoman-Timurid Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_Ankara\" title=\"Battle of Ankara\">Battle of Ankara</a>: <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Timurid_Empire\" title=\"Timurid Empire\">Timurid Empire</a>, defeats forces of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> sultan <a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\">Bayezid I</a>.", "links": [{"title": "Battle of Ankara", "link": "https://wikipedia.org/wiki/Battle_of_Ankara"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "Timurid Empire", "link": "https://wikipedia.org/wiki/Timurid_Empire"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Bayezid I", "link": "https://wikipedia.org/wiki/<PERSON>ez<PERSON>_I"}]}, {"year": "1592", "text": "During the first Japanese invasion of Korea, Japanese forces led by <PERSON><PERSON><PERSON> captured Pyongyang, although they were ultimately unable to hold it.", "html": "1592 - During the <a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-98)\">first Japanese invasion of Korea</a>, Japanese forces led by <a href=\"https://wikipedia.org/wiki/Toyoto<PERSON>_Hideyoshi\" title=\"Toyotomi Hideyoshi\"><PERSON><PERSON><PERSON> Hi<PERSON>yoshi</a> captured <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>, although they were ultimately unable to hold it.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-98)\">first Japanese invasion of Korea</a>, Japanese forces led by <a href=\"https://wikipedia.org/wiki/Toy<PERSON><PERSON>_Hideyoshi\" title=\"Toyotomi Hideyoshi\"><PERSON><PERSON><PERSON></a> captured <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>, although they were ultimately unable to hold it.", "links": [{"title": "Japanese invasions of Korea (1592-98)", "link": "https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>deyoshi"}, {"title": "Pyongyang", "link": "https://wikipedia.org/wiki/Pyongyang"}]}, {"year": "1705", "text": "A fire in Oulu, Finland almost completely destroyed the fourth district, which covered the southern part of the city and was by far the largest of the city districts.", "html": "1705 - A fire in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> almost <a href=\"https://wikipedia.org/wiki/Great_Oulu_fire_of_1705\" title=\"Great Oulu fire of 1705\">completely destroyed the fourth district</a>, which covered the southern part of the city and was by far the largest of the city districts.", "no_year_html": "A fire in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu</a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a> almost <a href=\"https://wikipedia.org/wiki/Great_Oulu_fire_of_1705\" title=\"Great Oulu fire of 1705\">completely destroyed the fourth district</a>, which covered the southern part of the city and was by far the largest of the city districts.", "links": [{"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Great Oulu fire of 1705", "link": "https://wikipedia.org/wiki/Great_Oulu_fire_of_1705"}]}, {"year": "1715", "text": "Seventh Ottoman-Venetian War: The Ottoman Empire captures Nauplia, the capital of the Republic of Venice's \"Kingdom of the Morea\", thereby opening the way to the swift Ottoman reconquest of the Morea.", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Seventh_Ottoman%E2%80%93Venetian_War\" class=\"mw-redirect\" title=\"Seventh Ottoman-Venetian War\">Seventh Ottoman-Venetian War</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Nauplia_(1715)\" title=\"Siege of Nauplia (1715)\">captures</a> <a href=\"https://wikipedia.org/wiki/Nauplia\" class=\"mw-redirect\" title=\"Nauplia\">Nauplia</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>'s \"<a href=\"https://wikipedia.org/wiki/Kingdom_of_the_Morea\" title=\"Kingdom of the Morea\">Kingdom of the Morea</a>\", thereby opening the way to the swift <a href=\"https://wikipedia.org/wiki/Ottoman_reconquest_of_the_Morea\" title=\"Ottoman reconquest of the Morea\">Ottoman reconquest of the Morea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seventh_Ottoman%E2%80%93Venetian_War\" class=\"mw-redirect\" title=\"Seventh Ottoman-Venetian War\">Seventh Ottoman-Venetian War</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Nauplia_(1715)\" title=\"Siege of Nauplia (1715)\">captures</a> <a href=\"https://wikipedia.org/wiki/Nauplia\" class=\"mw-redirect\" title=\"Nauplia\">Nauplia</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>'s \"<a href=\"https://wikipedia.org/wiki/Kingdom_of_the_Morea\" title=\"Kingdom of the Morea\">Kingdom of the Morea</a>\", thereby opening the way to the swift <a href=\"https://wikipedia.org/wiki/Ottoman_reconquest_of_the_Morea\" title=\"Ottoman reconquest of the Morea\">Ottoman reconquest of the Morea</a>.", "links": [{"title": "Seventh Ottoman-Venetian War", "link": "https://wikipedia.org/wiki/Seventh_Ottoman%E2%80%93Venetian_War"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Siege of Nauplia (1715)", "link": "https://wikipedia.org/wiki/Siege_of_Nauplia_(1715)"}, {"title": "Nauplia", "link": "https://wikipedia.org/wiki/Nauplia"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Kingdom of the Morea", "link": "https://wikipedia.org/wiki/Kingdom_of_the_Morea"}, {"title": "Ottoman reconquest of the Morea", "link": "https://wikipedia.org/wiki/Ottoman_reconquest_of_the_Morea"}]}, {"year": "1738", "text": "Canadian explorer <PERSON>nnes et de La Vérendrye reaches the western shore of Lake Michigan.", "html": "1738 - Canadian explorer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON> Varennes, sieur de La Vérendrye\"><PERSON> Varennes et de La Vérendrye</a> reaches the western shore of <a href=\"https://wikipedia.org/wiki/Lake_Michigan\" title=\"Lake Michigan\">Lake Michigan</a>.", "no_year_html": "Canadian explorer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON> Varennes, sieur de La Vérendrye\"><PERSON> Varennes et de La Vérendrye</a> reaches the western shore of <a href=\"https://wikipedia.org/wiki/Lake_Michigan\" title=\"Lake Michigan\">Lake Michigan</a>.", "links": [{"title": "<PERSON>, <PERSON><PERSON> de La Vérendrye", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>eur_de_La_V%C3%A9rendrye"}, {"title": "Lake Michigan", "link": "https://wikipedia.org/wiki/Lake_Michigan"}]}, {"year": "1799", "text": "<PERSON><PERSON> begins his first of six reigns as Emperor of Ethiopia.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Giyorgis_I\" title=\"<PERSON>kle Giyorgis I\"><PERSON><PERSON> Giyorgis I</a> begins his first of six reigns as <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Giyorgis_I\" title=\"<PERSON>kle Giyorgis I\"><PERSON><PERSON> Giyorgis I</a> begins his first of six reigns as <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "links": [{"title": "<PERSON><PERSON> Giyorgis I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}]}, {"year": "1807", "text": "Nicéphore <PERSON> is awarded a patent by Napoleon for the Pyréolophore, the world's first internal combustion engine, after it successfully powered a boat upstream on the river Saône in France.", "html": "1807 - <a href=\"https://wikipedia.org/wiki/Nic%C3%A9phore_Ni%C3%A9pce\" title=\"Nicéph<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is awarded a patent by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> for the <a href=\"https://wikipedia.org/wiki/Pyr%C3%A9olophore\" title=\"Pyréolophore\">Pyréolophore</a>, the world's first <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">internal combustion engine</a>, after it successfully powered a boat upstream on the river <a href=\"https://wikipedia.org/wiki/Sa%C3%B4ne\" title=\"Saône\">Saône</a> in France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nic%C3%A9phore_Ni%C3%A9pce\" title=\"Nicéphore <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is awarded a patent by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> for the <a href=\"https://wikipedia.org/wiki/Pyr%C3%A9olophore\" title=\"Pyréolophore\">Pyréolophore</a>, the world's first <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">internal combustion engine</a>, after it successfully powered a boat upstream on the river <a href=\"https://wikipedia.org/wiki/Sa%C3%B4ne\" title=\"Saône\">Saône</a> in France.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nic%C3%A9phore_Ni%C3%A9pce"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Pyréolophore", "link": "https://wikipedia.org/wiki/Pyr%C3%A9olophore"}, {"title": "Internal combustion engine", "link": "https://wikipedia.org/wiki/Internal_combustion_engine"}, {"title": "Saône", "link": "https://wikipedia.org/wiki/Sa%C3%B4ne"}]}, {"year": "1810", "text": "Citizens of Bogotá, New Granada declare independence from Spain.", "html": "1810 - Citizens of <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_New_Granada\" title=\"Viceroyalty of New Granada\">New Granada</a> declare independence from Spain.", "no_year_html": "Citizens of <a href=\"https://wikipedia.org/wiki/Bogot%C3%A1\" title=\"Bogotá\">Bogotá</a>, <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_New_Granada\" title=\"Viceroyalty of New Granada\">New Granada</a> declare independence from Spain.", "links": [{"title": "Bogotá", "link": "https://wikipedia.org/wiki/Bogot%C3%A1"}, {"title": "Viceroyalty of New Granada", "link": "https://wikipedia.org/wiki/Viceroyalty_of_New_Granada"}]}, {"year": "1831", "text": "Seneca and Shawnee people agree to relinquish their land in western Ohio for 60,000 acres west of the Mississippi River.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Seneca_people\" title=\"Seneca people\">Seneca</a> and <a href=\"https://wikipedia.org/wiki/Shawnee\" title=\"Shawnee\">Shawnee</a> people agree to relinquish their land in western <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> for 60,000 acres west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seneca_people\" title=\"Seneca people\">Seneca</a> and <a href=\"https://wikipedia.org/wiki/Shawnee\" title=\"Shawnee\">Shawnee</a> people agree to relinquish their land in western <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> for 60,000 acres west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "links": [{"title": "Seneca people", "link": "https://wikipedia.org/wiki/Seneca_people"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1848", "text": "The first Women's Rights Convention in Seneca Falls, New York, a two-day event, concludes.", "html": "1848 - The first <a href=\"https://wikipedia.org/wiki/Seneca_Falls_Convention\" title=\"Seneca Falls Convention\">Women's Rights Convention</a> in <a href=\"https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York\" class=\"mw-redirect\" title=\"Seneca Falls (hamlet), New York\">Seneca Falls, New York</a>, a two-day event, concludes.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Seneca_Falls_Convention\" title=\"Seneca Falls Convention\">Women's Rights Convention</a> in <a href=\"https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York\" class=\"mw-redirect\" title=\"Seneca Falls (hamlet), New York\">Seneca Falls, New York</a>, a two-day event, concludes.", "links": [{"title": "Seneca Falls Convention", "link": "https://wikipedia.org/wiki/Seneca_Falls_Convention"}, {"title": "Seneca Falls (hamlet), New York", "link": "https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York"}]}, {"year": "1864", "text": "American Civil War: Battle of Peachtree Creek: Near Atlanta, Georgia, Confederate forces led by General <PERSON> unsuccessfully attack Union troops under General <PERSON>.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Peachtree_Creek\" title=\"Battle of Peachtree Creek\">Battle of Peachtree Creek</a>: Near <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> unsuccessfully attack <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">troops</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Peachtree_Creek\" title=\"Battle of Peachtree Creek\">Battle of Peachtree Creek</a>: Near <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> unsuccessfully attack <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">troops</a> under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Peachtree Creek", "link": "https://wikipedia.org/wiki/Battle_of_Peachtree_Creek"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "Austro-Prussian War: Battle of Lissa: The Austrian Navy, led by Admiral <PERSON>, defeats the Italian Navy near the island of Vis in the Adriatic Sea.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lissa_(1866)\" title=\"Battle of Lissa (1866)\">Battle of Lissa</a>: The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Navy\" title=\"Austro-Hungarian Navy\">Austrian Navy</a>, led by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defeats the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Italian Navy</a> near the island of <a href=\"https://wikipedia.org/wiki/Vis_(island)\" title=\"Vis (island)\">Vis</a> in the <a href=\"https://wikipedia.org/wiki/Adriatic_Sea\" title=\"Adriatic Sea\">Adriatic Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austro-Prussian_War\" title=\"Austro-Prussian War\">Austro-Prussian War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Lissa_(1866)\" title=\"Battle of Lissa (1866)\">Battle of Lissa</a>: The <a href=\"https://wikipedia.org/wiki/Austro-Hungarian_Navy\" title=\"Austro-Hungarian Navy\">Austrian Navy</a>, led by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defeats the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Italian Navy</a> near the island of <a href=\"https://wikipedia.org/wiki/Vis_(island)\" title=\"Vis (island)\">Vis</a> in the <a href=\"https://wikipedia.org/wiki/Adriatic_Sea\" title=\"Adriatic Sea\">Adriatic Sea</a>.", "links": [{"title": "Austro-Prussian War", "link": "https://wikipedia.org/wiki/Austro-Prussian_War"}, {"title": "Battle of Lissa (1866)", "link": "https://wikipedia.org/wiki/Battle_of_Lissa_(1866)"}, {"title": "Austro-Hungarian Navy", "link": "https://wikipedia.org/wiki/Austro-Hungarian_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "Vis (island)", "link": "https://wikipedia.org/wiki/Vis_(island)"}, {"title": "Adriatic Sea", "link": "https://wikipedia.org/wiki/Adriatic_Sea"}]}, {"year": "1871", "text": "British Columbia joins the Canadian Confederation.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a> joins the <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a> joins the <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>.", "links": [{"title": "British Columbia", "link": "https://wikipedia.org/wiki/British_Columbia"}, {"title": "Canadian Confederation", "link": "https://wikipedia.org/wiki/Canadian_Confederation"}]}, {"year": "1885", "text": "The Football Association legalizes professionalism in association football under pressure from the British Football Association.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/The_Football_Association\" title=\"The Football Association\">The Football Association</a> legalizes professionalism in <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> under pressure from the <a href=\"https://wikipedia.org/wiki/British_Football_Association\" title=\"British Football Association\">British Football Association</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Football_Association\" title=\"The Football Association\">The Football Association</a> legalizes professionalism in <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a> under pressure from the <a href=\"https://wikipedia.org/wiki/British_Football_Association\" title=\"British Football Association\">British Football Association</a>.", "links": [{"title": "The Football Association", "link": "https://wikipedia.org/wiki/The_Football_Association"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}, {"title": "British Football Association", "link": "https://wikipedia.org/wiki/British_Football_Association"}]}, {"year": "1903", "text": "The Ford Motor Company ships its first automobile.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> ships its first automobile.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a> ships its first automobile.", "links": [{"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}]}, {"year": "1906", "text": "In Finland, a new electoral law is ratified, guaranteeing the country the first and equal right to vote in the world. Finnish women are the first in Europe to receive the right to vote.", "html": "1906 - In <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, a new electoral law is ratified, guaranteeing the country the first and equal right to vote in the world. Finnish women are the first in Europe to <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">receive the right to vote</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, a new electoral law is ratified, guaranteeing the country the first and equal right to vote in the world. Finnish women are the first in Europe to <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">receive the right to vote</a>.", "links": [{"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Women's suffrage", "link": "https://wikipedia.org/wiki/Women%27s_suffrage"}]}, {"year": "1917", "text": "World War I: The Corfu Declaration, which leads to the creation of the post-war Kingdom of Yugoslavia, is signed by the Yugoslav Committee and Kingdom of Serbia.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Corfu_Declaration\" title=\"Corfu Declaration\">Corfu Declaration</a>, which leads to the creation of the post-war <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Yugoslavia</a>, is signed by the Yugoslav Committee and Kingdom of Serbia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Corfu_Declaration\" title=\"Corfu Declaration\">Corfu Declaration</a>, which leads to the creation of the post-war <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Yugoslavia</a>, is signed by the Yugoslav Committee and Kingdom of Serbia.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Corfu Declaration", "link": "https://wikipedia.org/wiki/Corfu_Declaration"}, {"title": "Kingdom of Yugoslavia", "link": "https://wikipedia.org/wiki/Kingdom_of_Yugoslavia"}]}, {"year": "1920", "text": "The Greek Army takes control of Silivri after Greece is awarded the city by the Paris Peace Conference; by 1923 Greece effectively lost control to the Turks.", "html": "1920 - The Greek Army takes control of <a href=\"https://wikipedia.org/wiki/Silivri\" title=\"Silivri\">Sili<PERSON>ri</a> after <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> is awarded the city by the <a href=\"https://wikipedia.org/wiki/Paris_Peace_Conference,_1919\" class=\"mw-redirect\" title=\"Paris Peace Conference, 1919\">Paris Peace Conference</a>; by 1923 Greece effectively lost control to the Turks.", "no_year_html": "The Greek Army takes control of <a href=\"https://wikipedia.org/wiki/Silivri\" title=\"Silivri\">Sili<PERSON><PERSON></a> after <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> is awarded the city by the <a href=\"https://wikipedia.org/wiki/Paris_Peace_Conference,_1919\" class=\"mw-redirect\" title=\"Paris Peace Conference, 1919\">Paris Peace Conference</a>; by 1923 Greece effectively lost control to the Turks.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silivri"}, {"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Paris Peace Conference, 1919", "link": "https://wikipedia.org/wiki/Paris_Peace_Conference,_1919"}]}, {"year": "1922", "text": "The League of Nations awards mandates of Togoland to France and Tanganyika to the United Kingdom.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> awards mandates of <a href=\"https://wikipedia.org/wiki/Togoland\" title=\"Togoland\">Togoland</a> to France and <a href=\"https://wikipedia.org/wiki/Tanganyika_(territory)\" class=\"mw-redirect\" title=\"Tanganyika (territory)\">Tanganyika</a> to the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> awards mandates of <a href=\"https://wikipedia.org/wiki/Togoland\" title=\"Togoland\">Togoland</a> to France and <a href=\"https://wikipedia.org/wiki/Tanganyika_(territory)\" class=\"mw-redirect\" title=\"Tanganyika (territory)\">Tanganyika</a> to the United Kingdom.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}, {"title": "Togoland", "link": "https://wikipedia.org/wiki/Togoland"}, {"title": "Tanganyika (territory)", "link": "https://wikipedia.org/wiki/Tanganyika_(territory)"}]}, {"year": "1932", "text": "In the Preußenschlag, German President <PERSON><PERSON><PERSON> places Prussia directly under the rule of the national government.", "html": "1932 - In the <i><a href=\"https://wikipedia.org/wiki/Preu%C3%9Fenschlag\" class=\"mw-redirect\" title=\"Preußenschlag\">Preußenschlag</a></i>, German President <PERSON><PERSON><PERSON> places Prussia directly under the rule of the national government.", "no_year_html": "In the <i><a href=\"https://wikipedia.org/wiki/Preu%C3%9Fenschlag\" class=\"mw-redirect\" title=\"Preußenschlag\">Preußenschlag</a></i>, German President <PERSON><PERSON><PERSON> places Prussia directly under the rule of the national government.", "links": [{"title": "Preußenschlag", "link": "https://wikipedia.org/wiki/Preu%C3%9Fenschlag"}]}, {"year": "1934", "text": "Labor unrest in the U.S.: Police in Minneapolis fire upon striking truck drivers, during the Minneapolis Teamsters Strike of 1934, killing two and wounding sixty-seven.", "html": "1934 - Labor unrest in the U.S.: Police in <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis</a> fire upon striking <a href=\"https://wikipedia.org/wiki/Trucker\" class=\"mw-redirect\" title=\"Trucker\">truck drivers</a>, during the <a href=\"https://wikipedia.org/wiki/Minneapolis_Teamsters_Strike_of_1934\" class=\"mw-redirect\" title=\"Minneapolis Teamsters Strike of 1934\">Minneapolis Teamsters Strike of 1934</a>, killing two and wounding sixty-seven.", "no_year_html": "Labor unrest in the U.S.: Police in <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis</a> fire upon striking <a href=\"https://wikipedia.org/wiki/Trucker\" class=\"mw-redirect\" title=\"Trucker\">truck drivers</a>, during the <a href=\"https://wikipedia.org/wiki/Minneapolis_Teamsters_Strike_of_1934\" class=\"mw-redirect\" title=\"Minneapolis Teamsters Strike of 1934\">Minneapolis Teamsters Strike of 1934</a>, killing two and wounding sixty-seven.", "links": [{"title": "Minneapolis", "link": "https://wikipedia.org/wiki/Minneapolis"}, {"title": "Trucker", "link": "https://wikipedia.org/wiki/Trucker"}, {"title": "Minneapolis Teamsters Strike of 1934", "link": "https://wikipedia.org/wiki/Minneapolis_Teamsters_Strike_of_1934"}]}, {"year": "1934", "text": "West Coast waterfront strike: In Seattle, police fire tear gas on and club 2,000 striking longshoremen. The governor of Oregon calls out the National Guard to break a strike on the Portland docks.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/1934_West_Coast_waterfront_strike\" title=\"1934 West Coast waterfront strike\">West Coast waterfront strike</a>: In <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>, police fire <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a> on and club 2,000 striking <a href=\"https://wikipedia.org/wiki/Stevedore\" class=\"mw-redirect\" title=\"Stevedore\">longshoremen</a>. The <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">governor of Oregon</a> calls out the National Guard to break a strike on the <a href=\"https://wikipedia.org/wiki/Portland,_Oregon\" title=\"Portland, Oregon\">Portland</a> docks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1934_West_Coast_waterfront_strike\" title=\"1934 West Coast waterfront strike\">West Coast waterfront strike</a>: In <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle</a>, police fire <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a> on and club 2,000 striking <a href=\"https://wikipedia.org/wiki/Stevedore\" class=\"mw-redirect\" title=\"Stevedore\">longshoremen</a>. The <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">governor of Oregon</a> calls out the National Guard to break a strike on the <a href=\"https://wikipedia.org/wiki/Portland,_Oregon\" title=\"Portland, Oregon\">Portland</a> docks.", "links": [{"title": "1934 West Coast waterfront strike", "link": "https://wikipedia.org/wiki/1934_West_Coast_waterfront_strike"}, {"title": "Seattle", "link": "https://wikipedia.org/wiki/Seattle"}, {"title": "Tear gas", "link": "https://wikipedia.org/wiki/Tear_gas"}, {"title": "Stevedore", "link": "https://wikipedia.org/wiki/Stevedore"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}, {"title": "Portland, Oregon", "link": "https://wikipedia.org/wiki/Portland,_Oregon"}]}, {"year": "1935", "text": "Switzerland: A Royal Dutch Airlines plane en route from Milan to Frankfurt crashes into a Swiss mountain, killing thirteen.", "html": "1935 - Switzerland: A <a href=\"https://wikipedia.org/wiki/Royal_Dutch_Airlines\" class=\"mw-redirect\" title=\"Royal Dutch Airlines\">Royal Dutch Airlines</a> plane en route from <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> to <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a> crashes into a Swiss mountain, killing thirteen.", "no_year_html": "Switzerland: A <a href=\"https://wikipedia.org/wiki/Royal_Dutch_Airlines\" class=\"mw-redirect\" title=\"Royal Dutch Airlines\">Royal Dutch Airlines</a> plane en route from <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a> to <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a> crashes into a Swiss mountain, killing thirteen.", "links": [{"title": "Royal Dutch Airlines", "link": "https://wikipedia.org/wiki/Royal_Dutch_Airlines"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "Frankfurt", "link": "https://wikipedia.org/wiki/Frankfurt"}]}, {"year": "1936", "text": "The Montreux Convention is signed in Switzerland, authorizing Turkey to fortify the Dardanelles and Bosphorus but guaranteeing free passage to ships of all nations in peacetime.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Montreux_Convention_Regarding_the_Regime_of_the_Straits\" title=\"Montreux Convention Regarding the Regime of the Straits\">Montreux Convention</a> is signed in <a href=\"https://wikipedia.org/wiki/Montreux\" title=\"Montreux\">Switzerland</a>, authorizing <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> to fortify the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a> and <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a> but guaranteeing free passage to ships of all nations in peacetime.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montreux_Convention_Regarding_the_Regime_of_the_Straits\" title=\"Montreux Convention Regarding the Regime of the Straits\">Montreux Convention</a> is signed in <a href=\"https://wikipedia.org/wiki/Montreux\" title=\"Montreux\">Switzerland</a>, authorizing <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> to fortify the <a href=\"https://wikipedia.org/wiki/Dardanelles\" title=\"Dardanelles\">Dardanelles</a> and <a href=\"https://wikipedia.org/wiki/Bosphorus\" class=\"mw-redirect\" title=\"Bosphorus\">Bosphorus</a> but guaranteeing free passage to ships of all nations in peacetime.", "links": [{"title": "Montreux Convention Regarding the Regime of the Straits", "link": "https://wikipedia.org/wiki/Montreux_Convention_Regarding_the_Regime_of_the_Straits"}, {"title": "Montreux", "link": "https://wikipedia.org/wiki/<PERSON>reux"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Dardanelles", "link": "https://wikipedia.org/wiki/Dardanelles"}, {"title": "Bosphor<PERSON>", "link": "https://wikipedia.org/wiki/Bosphorus"}]}, {"year": "1938", "text": "The United States Department of Justice files suit in New York City against the motion picture industry charging violations of the Sherman Antitrust Act in regards to the studio system.  The case would eventually result in a break-up of the industry in 1948.", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> files suit in New York City against the <a href=\"https://wikipedia.org/wiki/Motion_picture\" class=\"mw-redirect\" title=\"Motion picture\">motion picture</a> industry charging violations of the <a href=\"https://wikipedia.org/wiki/Sherman_Antitrust_Act\" title=\"Sherman Antitrust Act\">Sherman Antitrust Act</a> in regards to the <a href=\"https://wikipedia.org/wiki/Studio_system\" title=\"Studio system\">studio system</a>. The case would eventually result in a break-up of the industry in <a href=\"https://wikipedia.org/wiki/1948\" title=\"1948\">1948</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> files suit in New York City against the <a href=\"https://wikipedia.org/wiki/Motion_picture\" class=\"mw-redirect\" title=\"Motion picture\">motion picture</a> industry charging violations of the <a href=\"https://wikipedia.org/wiki/Sherman_Antitrust_Act\" title=\"Sherman Antitrust Act\">Sherman Antitrust Act</a> in regards to the <a href=\"https://wikipedia.org/wiki/Studio_system\" title=\"Studio system\">studio system</a>. The case would eventually result in a break-up of the industry in <a href=\"https://wikipedia.org/wiki/1948\" title=\"1948\">1948</a>.", "links": [{"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}, {"title": "Motion picture", "link": "https://wikipedia.org/wiki/Motion_picture"}, {"title": "Sherman Antitrust Act", "link": "https://wikipedia.org/wiki/Sherman_Antitrust_Act"}, {"title": "Studio system", "link": "https://wikipedia.org/wiki/Studio_system"}, {"title": "1948", "link": "https://wikipedia.org/wiki/1948"}]}, {"year": "1940", "text": "Denmark leaves the League of Nations.", "html": "1940 - Denmark leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "Denmark leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1940", "text": "California opens its first freeway, the Arroyo Seco Parkway.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> opens its first <a href=\"https://wikipedia.org/wiki/Freeway\" class=\"mw-redirect\" title=\"Freeway\">freeway</a>, the <a href=\"https://wikipedia.org/wiki/Arroyo_Seco_Parkway\" title=\"Arroyo Seco Parkway\">Arroyo Seco Parkway</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> opens its first <a href=\"https://wikipedia.org/wiki/Freeway\" class=\"mw-redirect\" title=\"Freeway\">freeway</a>, the <a href=\"https://wikipedia.org/wiki/Arroyo_Seco_Parkway\" title=\"Arroyo Seco Parkway\">Arroyo Seco Parkway</a>.", "links": [{"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Freeway", "link": "https://wikipedia.org/wiki/Freeway"}, {"title": "Arroyo Seco Parkway", "link": "https://wikipedia.org/wiki/Arroyo_Seco_Parkway"}]}, {"year": "1941", "text": "Soviet leader <PERSON> consolidates the Commissariats of Home Affairs and National Security to form the NKVD and names <PERSON><PERSON><PERSON><PERSON> its chief.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet leader</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> consolidates the Commissariats of Home Affairs and National Security to form the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"N<PERSON><PERSON>\">NKVD</a> and names <a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\">Lavrentiy Beria</a> its chief.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet leader</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> consolidates the Commissariats of Home Affairs and National Security to form the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NK<PERSON>\">NKVD</a> and names <a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\">Lavrentiy Beria</a> its chief.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "NKVD", "link": "https://wikipedia.org/wiki/NKVD"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavrentiy_Beria"}]}, {"year": "1944", "text": "World War II: <PERSON> survives an assassination attempt led by German Army Colonel <PERSON>.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> survives <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">an assassination attempt</a> led by German Army Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> survives <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">an assassination attempt</a> led by German Army Colonel <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "20 July plot", "link": "https://wikipedia.org/wiki/20_July_plot"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "The Israel-Syria Mixed Armistice Commission brokers the last of four ceasefire agreements to end the 1948 Arab-Israeli War.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Syria_Mixed_Armistice_Commission\" title=\"Israel-Syria Mixed Armistice Commission\">Israel-Syria Mixed Armistice Commission</a> brokers the last of four ceasefire agreements to end the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Syria_Mixed_Armistice_Commission\" title=\"Israel-Syria Mixed Armistice Commission\">Israel-Syria Mixed Armistice Commission</a> brokers the last of four ceasefire agreements to end the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "links": [{"title": "Israel-Syria Mixed Armistice Commission", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Syria_Mixed_Armistice_Commission"}, {"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}]}, {"year": "1950", "text": "Cold War: In Philadelphia, <PERSON> pleads guilty to spying for the Soviet Union by passing secrets from atomic scientist <PERSON>.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Harry Gold\"><PERSON></a> pleads guilty to spying for the Soviet Union by passing secrets from atomic scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Harry <PERSON>\"><PERSON></a> pleads guilty to spying for the Soviet Union by passing secrets from atomic scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "After a month-long campaign, the majority of North Korea's Air Force was destroyed by anti-communist forces.", "html": "1950 - After a month-long campaign, the majority of North Korea's <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Air_Force\" class=\"mw-redirect\" title=\"Korean People's Air Force\">Air Force</a> <a href=\"https://wikipedia.org/wiki/Air_Battle_of_South_Korea\" title=\"Air Battle of South Korea\">was destroyed</a> by anti-communist forces.", "no_year_html": "After a month-long campaign, the majority of North Korea's <a href=\"https://wikipedia.org/wiki/Korean_People%27s_Air_Force\" class=\"mw-redirect\" title=\"Korean People's Air Force\">Air Force</a> <a href=\"https://wikipedia.org/wiki/Air_Battle_of_South_Korea\" title=\"Air Battle of South Korea\">was destroyed</a> by anti-communist forces.", "links": [{"title": "Korean People's Air Force", "link": "https://wikipedia.org/wiki/Korean_People%27s_Air_Force"}, {"title": "Air Battle of South Korea", "link": "https://wikipedia.org/wiki/Air_Battle_of_South_Korea"}]}, {"year": "1951", "text": "King <PERSON> of Jordan is assassinated by a Palestinian while attending Friday prayers in Jerusalem.", "html": "1951 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> is assassinated by a Palestinian while attending Friday prayers in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> is assassinated by a Palestinian while attending Friday prayers in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1954", "text": "Germany: <PERSON>, head of West Germany's secret service, defects to East Germany.", "html": "1954 - Germany: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of West Germany's secret service, defects to <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>.", "no_year_html": "Germany: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of West Germany's secret service, defects to <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1960", "text": "Ceylon (now Sri Lanka) elects <PERSON><PERSON><PERSON> Prime Minister, the world's first elected female head of government.", "html": "1960 - Ceylon (now <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>) elects <a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\">Sir<PERSON><PERSON> Bandaranaike</a> <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister</a>, the world's first elected female head of government.", "no_year_html": "Ceylon (now <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>) elects <a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\">Sir<PERSON><PERSON> Bandaranaike</a> <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka\" title=\"Prime Minister of Sri Lanka\">Prime Minister</a>, the world's first elected female head of government.", "links": [{"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Sirimavo Bandaranaike", "link": "https://wikipedia.org/wiki/Sirimavo_Bandaranaike"}, {"title": "Prime Minister of Sri Lanka", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sri_Lanka"}]}, {"year": "1960", "text": "The Polaris missile is successfully launched from a submarine, the USS George Washington, for the first time.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Polaris_missile\" class=\"mw-redirect\" title=\"Polaris missile\">Polaris missile</a> is successfully launched from a submarine, the <a href=\"https://wikipedia.org/wiki/USS_George_Washington_(SSBN-598)\" title=\"USS George Washington (SSBN-598)\">USS <i><PERSON></i></a>, for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Polaris_missile\" class=\"mw-redirect\" title=\"Polaris missile\">Polaris missile</a> is successfully launched from a submarine, the <a href=\"https://wikipedia.org/wiki/USS_George_Washington_(SSBN-598)\" title=\"USS George Washington (SSBN-598)\">USS <i><PERSON></i></a>, for the first time.", "links": [{"title": "Polaris missile", "link": "https://wikipedia.org/wiki/Polaris_missile"}, {"title": "USS George Washington (SSBN-598)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(SSBN-598)"}]}, {"year": "1961", "text": "French military forces break the Tunisian siege of Bizerte.", "html": "1961 - French military forces break the <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisian</a> <a href=\"https://wikipedia.org/wiki/Bizerte_crisis\" title=\"Bizerte crisis\">siege of Bizerte</a>.", "no_year_html": "French military forces break the <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisian</a> <a href=\"https://wikipedia.org/wiki/Bizerte_crisis\" title=\"Bizerte crisis\">siege of Bizerte</a>.", "links": [{"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}, {"title": "Bizerte crisis", "link": "https://wikipedia.org/wiki/Bizerte_crisis"}]}, {"year": "1964", "text": "Vietnam War: Viet Cong forces attack the capital of Định Tường Province, Cái Bè, killing 11 South Vietnamese military personnel and 40 civilians (30 of whom are children).", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> forces attack the capital of <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BB%8Bnh_T%C6%B0%E1%BB%9Dng_Province\" class=\"mw-redirect\" title=\"Định Tường Province\">Định Tường Province</a>, <a href=\"https://wikipedia.org/wiki/C%C3%A1i_B%C3%A8\" title=\"Cái <PERSON>\"><PERSON><PERSON><PERSON></a>, killing 11 <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> military personnel and 40 civilians (30 of whom are children).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> forces attack the capital of <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BB%8Bnh_T%C6%B0%E1%BB%9Dng_Province\" class=\"mw-redirect\" title=\"Định Tường Province\">Định Tường Province</a>, <a href=\"https://wikipedia.org/wiki/C%C3%A1i_B%C3%A8\" title=\"Cái <PERSON>\"><PERSON><PERSON><PERSON></a>, killing 11 <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> military personnel and 40 civilians (30 of whom are children).", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "Định Tường Province", "link": "https://wikipedia.org/wiki/%C4%90%E1%BB%8Bnh_T%C6%B0%E1%BB%9Dng_Province"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A1i_B%C3%A8"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1968", "text": "The first International Special Olympics Summer Games are held at Soldier Field in Chicago, with about 1,000 athletes with intellectual disabilities.", "html": "1968 - The first International <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> Summer Games are held at <a href=\"https://wikipedia.org/wiki/Soldier_Field\" title=\"Soldier Field\">Soldier Field</a> in Chicago, with about 1,000 athletes with intellectual disabilities.", "no_year_html": "The first International <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> Summer Games are held at <a href=\"https://wikipedia.org/wiki/Soldier_Field\" title=\"Soldier Field\">Soldier Field</a> in Chicago, with about 1,000 athletes with intellectual disabilities.", "links": [{"title": "Special Olympics", "link": "https://wikipedia.org/wiki/Special_Olympics"}, {"title": "Soldier Field", "link": "https://wikipedia.org/wiki/Soldier_Field"}]}, {"year": "1969", "text": "Apollo program: Apollo 11's crew successfully makes the first human landing on the Moon in the Sea of Tranquility. Americans <PERSON> and <PERSON> become the first humans to walk on the Moon six and a half hours later.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a>'s crew successfully makes the first human landing on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a> in the <a href=\"https://wikipedia.org/wiki/Mare_Tranquillitatis\" title=\"Mare Tranquillitatis\">Sea of Tranquility</a>. Americans <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Buzz_Aldrin\" title=\"Buzz Aldrin\"><PERSON></a> become the first humans to walk on the Moon six and a half hours later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a>'s crew successfully makes the first human landing on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Mare_Tranquillitatis\" title=\"Mare Tranquillitatis\">Sea of Tranquility</a>. Americans <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Aldrin\" title=\"Buzz Aldrin\"><PERSON></a> become the first humans to walk on the Moon six and a half hours later.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Mare Tranquillitatis", "link": "https://wikipedia.org/wiki/Mare_Tranquillitatis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "1969", "text": "A cease fire is announced between Honduras and El Salvador, six days after the beginning of the \"Football War\".", "html": "1969 - A cease fire is announced between <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> and <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, six days after the beginning of the \"<a href=\"https://wikipedia.org/wiki/Football_War\" title=\"Football War\">Football War</a>\".", "no_year_html": "A cease fire is announced between <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> and <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>, six days after the beginning of the \"<a href=\"https://wikipedia.org/wiki/Football_War\" title=\"Football War\">Football War</a>\".", "links": [{"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}, {"title": "Football War", "link": "https://wikipedia.org/wiki/Football_War"}]}, {"year": "1974", "text": "Turkish invasion of Cyprus: Forces from Turkey invade Cyprus after a coup d'état, organised by the dictator of Greece, against president <PERSON><PERSON><PERSON>.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Turkish_invasion_of_Cyprus\" title=\"Turkish invasion of Cyprus\">Turkish invasion of Cyprus</a>: Forces from <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> invade <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> after a <i>coup d'état</i>, organised by the dictator of Greece, against <a href=\"https://wikipedia.org/wiki/Makarios_III\" title=\"<PERSON><PERSON><PERSON> III\">president <PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turkish_invasion_of_Cyprus\" title=\"Turkish invasion of Cyprus\">Turkish invasion of Cyprus</a>: Forces from <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> invade <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> after a <i>coup d'état</i>, organised by the dictator of Greece, against <a href=\"https://wikipedia.org/wiki/Makarios_III\" title=\"Ma<PERSON><PERSON> III\">president <PERSON><PERSON><PERSON></a>.", "links": [{"title": "Turkish invasion of Cyprus", "link": "https://wikipedia.org/wiki/Turkish_invasion_of_Cyprus"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kari<PERSON>_III"}]}, {"year": "1976", "text": "The American Viking 1 lander successfully lands on Mars.", "html": "1976 - The American <i><a href=\"https://wikipedia.org/wiki/Viking_1\" title=\"Viking 1\">Viking 1</a></i> lander successfully lands on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "The American <i><a href=\"https://wikipedia.org/wiki/Viking_1\" title=\"Viking 1\">Viking 1</a></i> lander successfully lands on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Viking 1", "link": "https://wikipedia.org/wiki/Viking_1"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1977", "text": "The Central Intelligence Agency releases documents under the Freedom of Information Act revealing it had engaged in mind-control experiments.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a> releases documents under the <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Freedom of Information Act</a> revealing it had engaged in <a href=\"https://wikipedia.org/wiki/Project_MKUltra\" class=\"mw-redirect\" title=\"Project MKUltra\">mind-control experiments</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a> releases documents under the <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Freedom of Information Act</a> revealing it had engaged in <a href=\"https://wikipedia.org/wiki/Project_MKUltra\" class=\"mw-redirect\" title=\"Project MKUltra\">mind-control experiments</a>.", "links": [{"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "Freedom of Information Act (United States)", "link": "https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)"}, {"title": "Project MKUltra", "link": "https://wikipedia.org/wiki/Project_MKUltra"}]}, {"year": "1977", "text": "The Johnstown flood of 1977 kills 84 people and causes millions of dollars in damages.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Johnstown_flood_of_1977\" title=\"Johnstown flood of 1977\">Johnstown flood of 1977</a> kills 84 people and causes millions of dollars in damages.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Johnstown_flood_of_1977\" title=\"Johnstown flood of 1977\">Johnstown flood of 1977</a> kills 84 people and causes millions of dollars in damages.", "links": [{"title": "Johnstown flood of 1977", "link": "https://wikipedia.org/wiki/Johnstown_flood_of_1977"}]}, {"year": "1977", "text": "Aeroflot Flight B-2 crashes after takeoff from Vilim Airport in the Sakha Republic, killing 39.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_B-2\" title=\"Aeroflot Flight B-2\">Aeroflot Flight B-2</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Vitim_Airport\" title=\"Vitim Airport\">Vilim Airport</a> in the <a href=\"https://wikipedia.org/wiki/Sakha_Republic\" title=\"Sakha Republic\">Sakha Republic</a>, killing 39.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_B-2\" title=\"Aeroflot Flight B-2\">Aeroflot Flight B-2</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Vitim_Airport\" title=\"Vitim Airport\">Vilim Airport</a> in the <a href=\"https://wikipedia.org/wiki/Sakha_Republic\" title=\"Sakha Republic\">Sakha Republic</a>, killing 39.", "links": [{"title": "Aeroflot Flight B-2", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_B-2"}, {"title": "Vitim Airport", "link": "https://wikipedia.org/wiki/Vitim_Airport"}, {"title": "Sakha Republic", "link": "https://wikipedia.org/wiki/Sakha_Republic"}]}, {"year": "1981", "text": "Somali Airlines Flight 40 crashes in the Balad District of Somalia, killing 50 people.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Somali_Airlines_Flight_40\" title=\"Somali Airlines Flight 40\">Somali Airlines Flight 40</a> crashes in the <a href=\"https://wikipedia.org/wiki/Balad_District,_Somalia\" title=\"Balad District, Somalia\">Balad District</a> of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, killing 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somali_Airlines_Flight_40\" title=\"Somali Airlines Flight 40\">Somali Airlines Flight 40</a> crashes in the <a href=\"https://wikipedia.org/wiki/Balad_District,_Somalia\" title=\"Balad District, Somalia\">Balad District</a> of <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, killing 50 people.", "links": [{"title": "Somali Airlines Flight 40", "link": "https://wikipedia.org/wiki/Somali_Airlines_Flight_40"}, {"title": "Balad District, Somalia", "link": "https://wikipedia.org/wiki/Balad_District,_Somalia"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}]}, {"year": "1982", "text": "Hyde Park and Regent's Park bombings: The Provisional IRA detonates two bombs in Hyde Park and Regent's Park in central London, killing eight soldiers, wounding forty-seven people, and leading to the deaths of seven horses.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Hyde_Park_and_Regent%27s_Park_bombings\" title=\"Hyde Park and Regent's Park bombings\">Hyde Park and Regent's Park bombings</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonates two <a href=\"https://wikipedia.org/wiki/Bomb\" title=\"Bomb\">bombs</a> in <a href=\"https://wikipedia.org/wiki/Hyde_Park,_London\" title=\"Hyde Park, London\">Hyde Park</a> and <a href=\"https://wikipedia.org/wiki/Regent%27s_Park\" title=\"Regent's Park\">Regent's Park</a> in central London, killing eight soldiers, wounding forty-seven people, and leading to the deaths of seven horses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyde_Park_and_Regent%27s_Park_bombings\" title=\"Hyde Park and Regent's Park bombings\">Hyde Park and Regent's Park bombings</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonates two <a href=\"https://wikipedia.org/wiki/Bomb\" title=\"Bomb\">bombs</a> in <a href=\"https://wikipedia.org/wiki/Hyde_Park,_London\" title=\"Hyde Park, London\">Hyde Park</a> and <a href=\"https://wikipedia.org/wiki/Regent%27s_Park\" title=\"Regent's Park\">Regent's Park</a> in central London, killing eight soldiers, wounding forty-seven people, and leading to the deaths of seven horses.", "links": [{"title": "Hyde Park and Regent's Park bombings", "link": "https://wikipedia.org/wiki/Hyde_Park_and_Regent%27s_Park_bombings"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Bomb", "link": "https://wikipedia.org/wiki/Bomb"}, {"title": "Hyde Park, London", "link": "https://wikipedia.org/wiki/Hyde_Park,_London"}, {"title": "Regent's Park", "link": "https://wikipedia.org/wiki/Regent%27s_Park"}]}, {"year": "1985", "text": "The government of Aruba passes legislation to secede from the Netherlands Antilles.", "html": "1985 - The government of <a href=\"https://wikipedia.org/wiki/Aruba\" title=\"Aruba\">Aruba</a> passes legislation to secede from the <a href=\"https://wikipedia.org/wiki/Netherlands_Antilles\" title=\"Netherlands Antilles\">Netherlands Antilles</a>.", "no_year_html": "The government of <a href=\"https://wikipedia.org/wiki/Aruba\" title=\"Aruba\">Aruba</a> passes legislation to secede from the <a href=\"https://wikipedia.org/wiki/Netherlands_Antilles\" title=\"Netherlands Antilles\">Netherlands Antilles</a>.", "links": [{"title": "Aruba", "link": "https://wikipedia.org/wiki/Aruba"}, {"title": "Netherlands Antilles", "link": "https://wikipedia.org/wiki/Netherlands_Antilles"}]}, {"year": "1989", "text": "Burma's ruling junta puts opposition leader <PERSON><PERSON> under house arrest.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>'s ruling junta puts opposition leader <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> under house arrest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a>'s ruling junta puts opposition leader <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> under house arrest.", "links": [{"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}, {"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON> resigns as president of Czechoslovakia.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> resigns as president of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> resigns as president of <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>l"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}]}, {"year": "1992", "text": "A Tupolev Tu-154 crashes during takeoff from Tbilisi International Airport, killing all 24 aboard and four more people on the ground.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> <a href=\"https://wikipedia.org/wiki/1992_Tbilisi_Tupolev_Tu-154_crash\" title=\"1992 Tbilisi Tupolev Tu-154 crash\">crashes</a> during takeoff from Tbilisi International Airport, killing all 24 aboard and four more people on the ground.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> <a href=\"https://wikipedia.org/wiki/1992_Tbilisi_Tupolev_Tu-154_crash\" title=\"1992 Tbilisi Tupolev Tu-154 crash\">crashes</a> during takeoff from Tbilisi International Airport, killing all 24 aboard and four more people on the ground.", "links": [{"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "1992 Tbilisi Tupolev Tu-154 crash", "link": "https://wikipedia.org/wiki/1992_Tbilisi_Tupolev_Tu-154_crash"}]}, {"year": "1997", "text": "The fully restored USS Constitution (a.k.a. Old Ironsides) celebrates its 200th birthday by setting sail for the first time in 116 years.", "html": "1997 - The fully restored <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> (a.k.a. <i>Old Ironsides</i>) celebrates its 200th birthday by setting sail for the first time in 116 years.", "no_year_html": "The fully restored <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> (a.k.a. <i>Old Ironsides</i>) celebrates its 200th birthday by setting sail for the first time in 116 years.", "links": [{"title": "USS Constitution", "link": "https://wikipedia.org/wiki/USS_Constitution"}]}, {"year": "1999", "text": "The Chinese Communist Party begins a persecution campaign against <PERSON><PERSON><PERSON>, arresting thousands nationwide.", "html": "1999 - The Chinese Communist Party begins a <a href=\"https://wikipedia.org/wiki/Persecution_of_Falun_Gong\" title=\"Persecution of Falun Gong\">persecution</a> campaign against <a href=\"https://wikipedia.org/wiki/Falun_Gong\" title=\"Falun Gong\">Fal<PERSON></a>, arresting thousands nationwide.", "no_year_html": "The Chinese Communist Party begins a <a href=\"https://wikipedia.org/wiki/Persecution_of_Falun_Gong\" title=\"Persecution of Falun Gong\">persecution</a> campaign against <a href=\"https://wikipedia.org/wiki/Falun_Gong\" title=\"Falun Gong\">Falun Gong</a>, arresting thousands nationwide.", "links": [{"title": "Persecution of Falun Gong", "link": "https://wikipedia.org/wiki/Persecution_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>al<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "The Civil Marriage Act legalizes same-sex marriage in Canada.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Civil_Marriage_Act\" title=\"Civil Marriage Act\">Civil Marriage Act</a> legalizes same-sex marriage in Canada.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Civil_Marriage_Act\" title=\"Civil Marriage Act\">Civil Marriage Act</a> legalizes same-sex marriage in Canada.", "links": [{"title": "Civil Marriage Act", "link": "https://wikipedia.org/wiki/Civil_Marriage_Act"}]}, {"year": "2012", "text": "<PERSON> opened fire at a movie theater in Aurora, Colorado, killing 12 and injuring 70 others.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2012_Aurora,_Colorado_shooting\" class=\"mw-redirect\" title=\"2012 Aurora, Colorado shooting\">opened fire</a> at a movie theater in <a href=\"https://wikipedia.org/wiki/Aurora,_Colorado\" title=\"Aurora, Colorado\">Aurora, Colorado</a>, killing 12 and injuring 70 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/2012_Aurora,_Colorado_shooting\" class=\"mw-redirect\" title=\"2012 Aurora, Colorado shooting\">opened fire</a> at a movie theater in <a href=\"https://wikipedia.org/wiki/Aurora,_Colorado\" title=\"Aurora, Colorado\">Aurora, Colorado</a>, killing 12 and injuring 70 others.", "links": [{"title": "<PERSON> (mass murderer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)"}, {"title": "2012 Aurora, Colorado shooting", "link": "https://wikipedia.org/wiki/2012_Aurora,_Colorado_shooting"}, {"title": "Aurora, Colorado", "link": "https://wikipedia.org/wiki/Aurora,_Colorado"}]}, {"year": "2012", "text": "Syrian civil war: The People's Protection Units (YPG) capture the cities of Amuda and Efrîn without resistance.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the cities of <a href=\"https://wikipedia.org/wiki/Amuda\" title=\"Amuda\">Amuda</a> and <a href=\"https://wikipedia.org/wiki/Afrin,_Syria\" title=\"Afrin, Syria\">Efrîn</a> without resistance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the cities of <a href=\"https://wikipedia.org/wiki/Amuda\" title=\"Amuda\">Amuda</a> and <a href=\"https://wikipedia.org/wiki/Afrin,_Syria\" title=\"Afrin, Syria\">Efrîn</a> without resistance.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amuda"}, {"title": "Afrin, Syria", "link": "https://wikipedia.org/wiki/Afrin,_Syria"}]}, {"year": "2013", "text": "Seventeen government soldiers are killed in an attack by FARC revolutionaries in the Colombian department of Arauca.", "html": "2013 - Seventeen government soldiers are killed in <a href=\"https://wikipedia.org/wiki/2013_Colombian_clashes\" title=\"2013 Colombian clashes\">an attack</a> by <a href=\"https://wikipedia.org/wiki/FARC\" class=\"mw-redirect\" title=\"FARC\">FARC</a> revolutionaries in the Colombian department of <a href=\"https://wikipedia.org/wiki/Arauca_department\" class=\"mw-redirect\" title=\"Arauca department\">Arauca</a>.", "no_year_html": "Seventeen government soldiers are killed in <a href=\"https://wikipedia.org/wiki/2013_Colombian_clashes\" title=\"2013 Colombian clashes\">an attack</a> by <a href=\"https://wikipedia.org/wiki/FARC\" class=\"mw-redirect\" title=\"FARC\">FARC</a> revolutionaries in the Colombian department of <a href=\"https://wikipedia.org/wiki/Arauca_department\" class=\"mw-redirect\" title=\"Arauca department\">Arauca</a>.", "links": [{"title": "2013 Colombian clashes", "link": "https://wikipedia.org/wiki/2013_Colombian_clashes"}, {"title": "FARC", "link": "https://wikipedia.org/wiki/FARC"}, {"title": "Arauca department", "link": "https://wikipedia.org/wiki/Arauca_department"}]}, {"year": "2013", "text": "Syrian civil war: The Battle of Ras al-Ayn ends with the expulsion of Islamist forces from the city by the People's Protection Units (YPG).", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ras_al-Ayn_(2012%E2%80%9313)\" title=\"Battle of Ras al-Ayn (2012-13)\">Battle of Ras al-Ayn</a> ends with the expulsion of Islamist forces from the city by the <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ras_al-Ayn_(2012%E2%80%9313)\" title=\"Battle of Ras al-Ayn (2012-13)\">Battle of Ras al-Ayn</a> ends with the expulsion of Islamist forces from the city by the <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG).", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Battle of Ras al-Ayn (2012-13)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>_<PERSON>-<PERSON>yn_(2012%E2%80%9313)"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}]}, {"year": "2015", "text": "A huge explosion in the mostly Kurdish border town of Suruç, Turkey, targeting the Socialist Youth Associations Federation, kills at least 31 people and injures over 100.", "html": "2015 - A huge <a href=\"https://wikipedia.org/wiki/2015_Suru%C3%A7_bombing\" class=\"mw-redirect\" title=\"2015 Suruç bombing\">explosion</a> in the mostly <a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> border town of <a href=\"https://wikipedia.org/wiki/Suru%C3%A7\" title=\"Suruç\">Suruç</a>, Turkey, targeting the Socialist Youth Associations Federation, kills at least 31 people and injures over 100.", "no_year_html": "A huge <a href=\"https://wikipedia.org/wiki/2015_Suru%C3%A7_bombing\" class=\"mw-redirect\" title=\"2015 Suruç bombing\">explosion</a> in the mostly <a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> border town of <a href=\"https://wikipedia.org/wiki/Suru%C3%A7\" title=\"Suruç\">Suruç</a>, Turkey, targeting the Socialist Youth Associations Federation, kills at least 31 people and injures over 100.", "links": [{"title": "2015 Suruç bombing", "link": "https://wikipedia.org/wiki/2015_Suru%C3%A7_bombing"}, {"title": "Kurds", "link": "https://wikipedia.org/wiki/Kurds"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suru%C3%A7"}]}, {"year": "2015", "text": "The United States and Cuba resume full diplomatic relations after five decades.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> and <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> resume full <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">diplomatic relations</a> after five decades.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> and <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> resume full <a href=\"https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations\" title=\"Cuba-United States relations\">diplomatic relations</a> after five decades.", "links": [{"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Cuba-United States relations", "link": "https://wikipedia.org/wiki/Cuba%E2%80%93United_States_relations"}]}, {"year": "2017", "text": "<PERSON><PERSON> <PERSON><PERSON> is granted parole to be released from prison after serving nine years of a 33-year sentence after being convicted of armed robbery in Las Vegas.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is granted parole to be released from prison after serving nine years of a 33-year sentence after being convicted of armed robbery in <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is granted parole to be released from prison after serving nine years of a 33-year sentence after being convicted of armed robbery in <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Las Vegas", "link": "https://wikipedia.org/wiki/Las_Vegas"}]}, {"year": "2021", "text": "American businessman <PERSON> flies to space aboard New Shepard  NS-16 operated by his private spaceflight company Blue Origin.", "html": "2021 - American businessman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies to space aboard <a href=\"https://wikipedia.org/wiki/<PERSON>_Shepard\" title=\"New Shepard\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Blue_Origin_NS-16\" title=\"Blue Origin NS-16\"> NS-16</a> operated by his <a href=\"https://wikipedia.org/wiki/Private_spaceflight\" title=\"Private spaceflight\">private spaceflight</a> company <a href=\"https://wikipedia.org/wiki/Blue_Origin\" title=\"Blue Origin\">Blue Origin</a>.", "no_year_html": "American businessman <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies to space aboard <a href=\"https://wikipedia.org/wiki/<PERSON>_Shepard\" title=\"New Shepard\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Blue_Origin_NS-16\" title=\"Blue Origin NS-16\"> NS-16</a> operated by his <a href=\"https://wikipedia.org/wiki/Private_spaceflight\" title=\"Private spaceflight\">private spaceflight</a> company <a href=\"https://wikipedia.org/wiki/Blue_Origin\" title=\"Blue Origin\">Blue Origin</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Shepard", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Blue Origin NS-16", "link": "https://wikipedia.org/wiki/Blue_Origin_NS-16"}, {"title": "Private spaceflight", "link": "https://wikipedia.org/wiki/Private_spaceflight"}, {"title": "Blue Origin", "link": "https://wikipedia.org/wiki/Blue_Origin"}]}], "Births": [{"year": "682", "text": "<PERSON><PERSON><PERSON>, Japanese monk and scholar (d. 767)", "html": "682 - <a href=\"https://wikipedia.org/wiki/Taich%C5%8D\" title=\"Tai<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese monk and scholar (d. 767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taich%C5%8D\" title=\"Tai<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese monk and scholar (d. 767)", "links": [{"title": "Taich<PERSON>", "link": "https://wikipedia.org/wiki/Taich%C5%8D"}]}, {"year": "1304", "text": "<PERSON><PERSON>, Italian poet and scholar (d. 1374)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\"><PERSON><PERSON></a>, Italian poet and scholar (d. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\"><PERSON><PERSON></a>, Italian poet and scholar (d. 1374)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petrarch"}]}, {"year": "1313", "text": "<PERSON>, 2nd Baron <PERSON> (d. 1367)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (d. 1367)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a> (d. 1367)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1346", "text": "<PERSON>, Countess of Pembroke, daughter of King <PERSON> of England (d. 1361)", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Pembroke\" title=\"<PERSON>, Countess of Pembroke\"><PERSON>, Countess of Pembroke</a>, daughter of King <PERSON> of England (d. 1361)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Pembroke\" title=\"<PERSON>, Countess of Pembroke\"><PERSON>, Countess of Pembroke</a>, daughter of King <PERSON> of England (d. 1361)", "links": [{"title": "<PERSON>, Countess of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_of_Pembroke"}]}, {"year": "1470", "text": "<PERSON>, 1st Earl of Bath, English noble (d. 1539)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl of Bath</a>, English noble (d. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl <PERSON> Bath</a>, English noble (d. 1539)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Bath", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bath"}]}, {"year": "1519", "text": "<PERSON> (d. 1591)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_IX\" title=\"Pope Innocent IX\">Pope <PERSON> IX</a> (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_IX\" title=\"Pope Innocent IX\"><PERSON> IX</a> (d. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}]}, {"year": "1537", "text": "<PERSON><PERSON><PERSON>, French cardinal (d. 1604)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_d%27Ossat\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Ossat\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (d. 1604)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnaud_d%27Ossat"}]}, {"year": "1583", "text": "<PERSON><PERSON>, English Benedictine martyr (d. 1642)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/Alban_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English Benedictine martyr (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alban_Roe\" title=\"Alba<PERSON>\"><PERSON><PERSON></a>, English Benedictine martyr (d. 1642)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alban_Roe"}]}, {"year": "1591", "text": "<PERSON>, English Puritan preacher (d. 1643)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Puritan preacher (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Puritan preacher (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, governor of New Sweden (d. 1663)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B6<PERSON><PERSON>_Printz\" title=\"<PERSON>z\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/New_Sweden\" title=\"New Sweden\">New Sweden</a> (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B<PERSON><PERSON><PERSON>_Printz\" title=\"<PERSON>z\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/New_Sweden\" title=\"New Sweden\">New Sweden</a> (d. 1663)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6<PERSON><PERSON>_<PERSON>z"}, {"title": "New Sweden", "link": "https://wikipedia.org/wiki/New_Sweden"}]}, {"year": "1601", "text": "<PERSON>, English politician (d. 1667)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1620", "text": "<PERSON><PERSON> the Elder, Dutch poet and scholar (d. 1681)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Elder\" class=\"mw-redirect\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Dutch poet and scholar (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_the_Elder\" class=\"mw-redirect\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the <PERSON></a>, Dutch poet and scholar (d. 1681)", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_the_Elder"}]}, {"year": "1649", "text": "<PERSON>, 1st Earl of Portland (d. 1709)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Portland\" title=\"<PERSON>, 1st Earl of Portland\"><PERSON>, 1st Earl of Portland</a> (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Portland\" title=\"<PERSON>, 1st Earl of Portland\"><PERSON>, 1st Earl of Portland</a> (d. 1709)", "links": [{"title": "<PERSON>, 1st Earl of Portland", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Portland"}]}, {"year": "1754", "text": "<PERSON>, French philosopher and academic (d. 1836)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON><PERSON><PERSON><PERSON>, Georgian politician and diplomat (d. 1811)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian politician and diplomat (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian politician and diplomat (d. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Austrian tenor and composer (d. 1826)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tenor and composer (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tenor and composer (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, French general (d. 1852)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON><PERSON> <PERSON>, Ottoman sultan (d. 1839)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Mahmud II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Mahmud II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}]}, {"year": "1804", "text": "<PERSON>, English biologist, anatomist, and paleontologist (d. 1892)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and paleontologist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and paleontologist (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "Sir <PERSON>, English surgeon, histologist and anatomist. (d. 1892)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, English surgeon, histologist and anatomist. (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, English surgeon, histologist and anatomist. (d. 1892)", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, Austro-German monk, geneticist and botanist (d. 1884)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-German monk, geneticist and botanist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-German monk, geneticist and botanist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON>, American playwright and manager (d. 1899)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>\" title=\"August<PERSON> <PERSON>\">August<PERSON></a>, American playwright and manager (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON>\" title=\"August<PERSON> <PERSON>\">August<PERSON></a>, American playwright and manager (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Augustin_<PERSON>"}]}, {"year": "1830", "text": "<PERSON><PERSON>, English explorer (d. 1916)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English explorer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English explorer (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American lawyer and politician, 9th Governor of Oregon (d. 1911)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1838", "text": "Sir <PERSON>, 2nd Baronet, English civil servant and politician, Chancellor of the Duchy of Lancaster (d. 1928)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English civil servant and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English civil servant and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1928)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1847", "text": "<PERSON>, German painter and academic (d. 1935)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and academic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American lawyer and politician, 91st Mayor of New York City (d. 1918)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 91st <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 91st <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1852", "text": "<PERSON>, Dutch lawyer and politician, Prime Minister of the Netherlands (d. 1932)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Canadian artist (d. 1940)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Philom%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian artist (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philom%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian artist (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Philom%C3%A8ne_<PERSON>au"}]}, {"year": "1864", "text": "<PERSON>, Swedish poet, Nobel Prize laureate (d. 1931)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON><PERSON>,  Italian physiologist and anatomist (d. 1913)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian physiologist and anatomist (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ug<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian physiologist and anatomist (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rugger<PERSON>_<PERSON>i"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Romanian cleric and politician, 38th Prime Minister of Romania (d. 1939)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Miron_Cristea\" class=\"mw-redirect\" title=\"Miron Cristea\"><PERSON><PERSON></a>, Romanian cleric and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miron_Cristea\" class=\"mw-redirect\" title=\"Miron Cristea\"><PERSON><PERSON></a>, Romanian cleric and politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miron_Cristea"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1873", "text": "<PERSON>, Brazilian pilot (d. 1932)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pilot (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian pilot (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, German mathematician and academic (d. 1944)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Irish sailor and explorer (d. 1938)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish sailor and explorer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish sailor and explorer (d. 1938)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}]}, {"year": "1882", "text": "<PERSON>, Austrian mathematician and philosopher (d. 1937)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and philosopher (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and philosopher (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, 1st Baron <PERSON>, Scottish broadcaster, co-founded BBC (d. 1971)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish broadcaster, co-founded <a href=\"https://wikipedia.org/wiki/BBC\" title=\"BBC\">BBC</a> (d. 1971)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "BBC", "link": "https://wikipedia.org/wiki/BBC"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1966)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Verna_<PERSON>lton"}]}, {"year": "1890", "text": "<PERSON>, Danish-Swiss astronomer and academic (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swiss astronomer and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Swiss astronomer and academic (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Italian painter (d. 1964)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English soldier (d. 1915)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Hungarian painter, photographer, and sculptor (d. 1946)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Moholy-Nagy\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Moholy-Nagy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter, photographer, and sculptor (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Moholy-Nagy\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>holy-Nagy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter, photographer, and sculptor (d. 1946)", "links": [{"title": "László <PERSON>-Nagy", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Moholy-Nagy"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Polish-Swiss chemist and academic, Nobel Prize laureate (d. 1996)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stein"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1900", "text": "<PERSON>, English cricketer and coach (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Turkish businessman and philanthropist, founded Koç Holding (d. 1996)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Vehbi_Ko%C3%A7\" title=\"Vehbi Koç\"><PERSON><PERSON><PERSON></a>, Turkish businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Ko%C3%A7_Holding\" title=\"Koç Holding\">Koç Holding</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vehbi_Ko%C3%A7\" title=\"Vehbi Koç\"><PERSON><PERSON><PERSON></a>, Turkish businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Ko%C3%A7_Holding\" title=\"Koç Holding\">Koç Holding</a> (d. 1996)", "links": [{"title": "Vehbi <PERSON>ç", "link": "https://wikipedia.org/wiki/Vehbi_Ko%C3%A7"}, {"title": "Koç Holding", "link": "https://wikipedia.org/wiki/Ko%C3%A7_Holding"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Filipino businessman and founder of the Lopez Group of Companies (d. 1975)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino businessman and founder of the <a href=\"https://wikipedia.org/wiki/Lopez_Group_of_Companies\" class=\"mw-redirect\" title=\"Lopez Group of Companies\">Lopez Group of Companies</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>.</a>, Filipino businessman and founder of the <a href=\"https://wikipedia.org/wiki/Lopez_Group_of_Companies\" class=\"mw-redirect\" title=\"Lopez Group of Companies\">Lopez Group of Companies</a> (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Lopez Group of Companies", "link": "https://wikipedia.org/wiki/Lopez_Group_of_Companies"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (d. 1971)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Belarusian Jewish anarchist (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian Jewish anarchist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian Jewish anarchist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Mett"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American gastroenterologist (d. 1995)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gastroenterologist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gastroenterologist (d. 1995)", "links": [{"title": "Leon<PERSON> Berry", "link": "https://wikipedia.org/wiki/Leon<PERSON>_Berry"}]}, {"year": "1905", "text": "<PERSON>, American foil fencer (d. 2005)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American foil fencer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American foil fencer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, South African cricketer (d. 1993)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech-English conductor and composer (d. 2004)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD\" title=\"Vilé<PERSON>sk<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-English conductor and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD\" title=\"Vilé<PERSON> Tausk<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-English conductor and composer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Indian cricketer (d. 1941)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>-<PERSON>, Filipino author and illustrator (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino author and illustrator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino author and illustrator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Polish dancer and actress (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lo<PERSON>\"><PERSON><PERSON></a>, Polish dancer and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish dancer and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Loda_Halama"}]}, {"year": "1912", "text": "<PERSON>, Australian journalist and author (d. 1970)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Australian journalist and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Australian journalist and author (d. 1970)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Bulgarian philanthropist (d. 2018)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Do<PERSON>_Dobrev\" title=\"<PERSON><PERSON> Dobrev\"><PERSON><PERSON></a>, Bulgarian philanthropist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Do<PERSON>_Dobrev\" title=\"<PERSON><PERSON> Dobrev\"><PERSON><PERSON></a>, Bulgarian philanthropist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American singer-songwriter and dancer (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, New Zealand mountaineer and explorer (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mountaineer and explorer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand mountaineer and explorer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, English writer (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English writer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English writer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lieutenant and politician, 11th United States Secretary of Defense (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1921", "text": "<PERSON>, English-French journalist and author (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French journalist and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American lawyer and politician, 1st United States Secretary of Transportation (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Transportation", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Transportation"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Polish economist and journalist (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish economist and journalist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish economist and journalist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress and singer (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American author and playwright (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author and playwright (d. 2014)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Canadian-American songwriter and composer (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American songwriter and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>on\"><PERSON><PERSON></a>, Canadian-American songwriter and composer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French economist and politician, 8th President of the European Commission (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, French-Algerian psychiatrist and philosopher (d. 1961)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Algerian psychiatrist and philosopher (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Fanon\"><PERSON><PERSON><PERSON></a>, French-Algerian psychiatrist and philosopher (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>z_Fanon"}]}, {"year": "1927", "text": "<PERSON>, American economist and academic (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actress (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Austrian conductor and composer (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and composer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and composer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English-Canadian psychologist and academic (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian psychologist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian psychologist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Polish economist and politician, Polish Minister of Foreign Affairs (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON><PERSON>"}, {"title": "Ministry of Foreign Affairs (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Prime Minister of Algeria (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>aid_Abdessalam\" title=\"Belaid Abdessalam\"><PERSON><PERSON> Abdessalam</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Algeria\" title=\"Prime Minister of Algeria\">Prime Minister of Algeria</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Abdessalam\" title=\"Belaid Abdessalam\"><PERSON><PERSON> Abdessalam</a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Algeria\" title=\"Prime Minister of Algeria\">Prime Minister of Algeria</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belaid_Abdessalam"}, {"title": "Prime Minister of Algeria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Algeria"}]}, {"year": "1929", "text": "<PERSON>, Australian social worker and pianist, 23rd Spouse of the Prime Minister of Australia (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian social worker and pianist, 23rd <a href=\"https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Australia\" class=\"mw-redirect\" title=\"Spouse of the Prime Minister of Australia\">Spouse of the Prime Minister of Australia</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian social worker and pianist, 23rd <a href=\"https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Australia\" class=\"mw-redirect\" title=\"Spouse of the Prime Minister of Australia\">Spouse of the Prime Minister of Australia</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spouse of the Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Australia"}]}, {"year": "1929", "text": "<PERSON>, American businessman, founded Little Caesars (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Little_Caesars\" title=\"Little Caesars\">Little Caesars</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Little_Caesars\" title=\"Little Caesars\">Little Caesars</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Little Caesars", "link": "https://wikipedia.org/wiki/Little_Caesars"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Pakistani-Indian actor and producer (d. 1999)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian actor and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian actor and producer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian politician, 38th Premier of South Australia (d. 2000)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 38th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1930", "text": "<PERSON>, American basketball player and coach (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American historian and author (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English-American singer and actress (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer and actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian biographer, essayist and philanthropist (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian biographer, essayist and philanthropist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian biographer, essayist and philanthropist (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English race car driver (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2009)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1932", "text": "<PERSON>, American artist (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Nam_June_Paik\" title=\"Nam June <PERSON>\"><PERSON> <PERSON></a>, American artist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nam_June_Paik\" title=\"Nam June Paik\"><PERSON> <PERSON></a>, American artist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nam_June_Paik"}]}, {"year": "1932", "text": "<PERSON>, German lawyer and politician, German Minister of the Interior", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_German_interior_ministers\" title=\"List of German interior ministers\">German Minister of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_German_interior_ministers\" title=\"List of German interior ministers\">German Minister of the Interior</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of German interior ministers", "link": "https://wikipedia.org/wiki/List_of_German_interior_ministers"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1999)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Knox\"><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American novelist, playwright, and screenwriter (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, playwright, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist, playwright, and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English snooker player", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Baron <PERSON>, English businessman and art collector", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and art collector", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and art collector", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian novelist and short story writer (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian novelist and short story writer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian novelist and short story writer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American social worker and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Turkish lawyer and politician, Deputy Prime Minister of Turkey (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (d. 2023)", "links": [{"title": "Deniz <PERSON>", "link": "https://wikipedia.org/wiki/Deniz_Baykal"}, {"title": "Deputy Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey"}]}, {"year": "1938", "text": "<PERSON>, English footballer (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Cuban-American baseball player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English actress (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ig<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>igg\" title=\"<PERSON> Rigg\"><PERSON></a>, English actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>igg"}]}, {"year": "1938", "text": "<PERSON>, American actress (d. 1981)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American feminist artist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Judy_Chicago\" title=\"Judy Chicago\"><PERSON></a>, American feminist artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judy_Chicago\" title=\"Judy Chicago\"><PERSON></a>, American feminist artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Judy_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Greek author and journalist (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and journalist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and journalist (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German actor, screenwriter, and production designer (d. 1988)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, screenwriter, and production designer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor, screenwriter, and production designer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American race car driver (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, New Zealand race car driver (d. 2016)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1943", "text": "<PERSON>, English footballer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Romanian poet, journalist, and politician (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, journalist, and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, journalist, and politician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_P%C4%83unescu"}]}, {"year": "1943", "text": "<PERSON>, English actress (d. 2009)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American politician (d. 2010)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French sailor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON> <PERSON><PERSON>, American country music singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American country music singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American country music singer-songwriter", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American non-fiction author, journalist and essayist (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American non-fiction author, journalist and essayist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American non-fiction author, journalist and essayist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American soldier and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American football player and coach (d. 1980)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Re<PERSON>\" title=\"<PERSON> Rein\"><PERSON></a>, American football player and coach (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Re<PERSON>\" title=\"<PERSON> Rein\"><PERSON></a>, American football player and coach (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>in"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actor, director, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>iser\"><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>iser\"><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, German physicist and academic, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g\" title=\"<PERSON><PERSON> Binnig\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nig\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nig"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1947", "text": "<PERSON>, Mexican-American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Canadian archer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian archer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English actor and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Welsh-Australian singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh-Australian singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1953", "text": "<PERSON>, American journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American-Australian singer and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American guitarist and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jay <PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jay <PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Jamaican-English table tennis player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Canadian actor and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor and playwright", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9-<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, English banjo player and songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English banjo player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English banjo player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English drummer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Cameroonian footballer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Kono\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_N%27Kono\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_N%27Kono"}]}, {"year": "1956", "text": "<PERSON>, Canadian lawyer and politician, 16th Premier of Alberta (d. 2016)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1958", "text": "<PERSON>, Scottish keyboard player and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American salesman (d. 2009)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American salesman (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American salesman (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Italian race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian-Canadian poet and philosopher", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Pr<PERSON><PERSON>_<PERSON>uj%C4%8Di%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-Canadian poet and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pr<PERSON><PERSON>_<PERSON>uj%C4%8Di%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-Canadian poet and philosopher", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pr<PERSON><PERSON>_Vuj%C4%8Di%C4%87"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Berry\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sudes<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Cuban physician and activist, founded the Lawton Foundation", "html": "1961 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_El%C3%ADas_Biscet\" title=\"Óscar Elías <PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban physician and activist, founded the <a href=\"https://wikipedia.org/wiki/Lawton_Foundation\" title=\"Lawton Foundation\">Lawton Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_El%C3%ADas_Biscet\" title=\"Óscar <PERSON>ías <PERSON>cet\"><PERSON><PERSON><PERSON></a>, Cuban physician and activist, founded the <a href=\"https://wikipedia.org/wiki/Lawton_Foundation\" title=\"Lawton Foundation\">Lawton Foundation</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_El%C3%ADas_Biscet"}, {"title": "Lawton Foundation", "link": "https://wikipedia.org/wiki/Lawton_Foundation"}]}, {"year": "1962", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Italian race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English journalist, author, and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American-Australian zoologist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Australian zoologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Australian zoologist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Italian footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON><PERSON> (racing driver)\"><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON><PERSON> (racing driver)\"><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1965", "text": "<PERSON>, American journalist and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English dancer and presenter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer and presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dancer and presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gossard\" title=\"Stone Gossard\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gossard\" title=\"Stone Gossard\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Stone Gossard", "link": "https://wikipedia.org/wiki/Stone_Gossard"}]}, {"year": "1966", "text": "<PERSON>, Mexican lawyer and politician, 57th President of Mexico", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_Pe%C3%B1a_<PERSON>eto"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1967", "text": "<PERSON>-<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Hami_Mand%C4%B1ral%C4%B1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hami_Mand%C4%B1ral%C4%B1\" title=\"<PERSON><PERSON>d<PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hami_Mand%C4%B1ral%C4%B1"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American hip-hop artist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Kool_G_Rap\" title=\"Kool G Rap\"><PERSON><PERSON></a>, American hip-hop artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kool_G_Rap\" title=\"Kool G Rap\"><PERSON><PERSON> G <PERSON></a>, American hip-hop artist", "links": [{"title": "Kool G Rap", "link": "https://wikipedia.org/wiki/<PERSON>ol_G_Rap"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Croatian-German footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kreso_<PERSON>c"}]}, {"year": "1969", "text": "<PERSON>, Italian cyclist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)"}]}, {"year": "1969", "text": "<PERSON><PERSON>, South Korean-American singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Joon_Park\" title=\"Joon Park\"><PERSON><PERSON></a>, South Korean-American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joon_Park\" title=\"Joon Park\"><PERSON><PERSON></a>, South Korean-American singer", "links": [{"title": "Joon Park", "link": "https://wikipedia.org/wiki/Joon_Park"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vail\" title=\"Tobi Vail\"><PERSON><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ail\" title=\"Tobi Vail\"><PERSON><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tobi_Vail"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_(catcher)"}]}, {"year": "1971", "text": "<PERSON>, Canadian actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Slovak ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON>_St%C3%BCmpel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCmpel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jozef_St%C3%BCmpel"}]}, {"year": "1972", "text": "<PERSON>, Swedish jurist and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish jurist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish jurist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Crown Prince of Norway", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Crown_Prince_of_Norway\" title=\"<PERSON><PERSON><PERSON>, Crown Prince of Norway\"><PERSON><PERSON><PERSON>, Crown Prince of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Crown_Prince_of_Norway\" title=\"<PERSON><PERSON><PERSON>, Crown Prince of Norway\"><PERSON><PERSON><PERSON>, Crown Prince of Norway</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Crown Prince of Norway", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Crown_Prince_of_Norway"}]}, {"year": "1973", "text": "<PERSON>, Swedish ice hockey player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Caribbean cricketer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Mexican-American screenwriter and producer (d. 2025)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American screenwriter and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American screenwriter and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American soccer player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Norwegian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish journalist and politician, 5th Swedish Minister for European Union Affairs", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_for_EU_Affairs_(Sweden)\" title=\"Minister for EU Affairs (Sweden)\">Swedish Minister for European Union Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Minister_for_EU_Affairs_(Sweden)\" title=\"Minister for EU Affairs (Sweden)\">Swedish Minister for European Union Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for EU Affairs (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_EU_Affairs_(Sweden)"}]}, {"year": "1975", "text": "<PERSON>, American singer and actor (d. 2004)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Turkish footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Eim%C5%9Fek\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Eim%C5%9Fek\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%9Eim%C5%9Fek"}]}, {"year": "1976", "text": "<PERSON>, American journalist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Erica Hill\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Erica Hill\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Malaysian race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Congolese footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French shot putter (d. 2012)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French shot putter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French shot putter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Yves_Niar%C3%A9"}]}, {"year": "1977", "text": "<PERSON>, Brazilian-Japanese footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Japanese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Russian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yamin\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Latvian runner and hurdler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian runner and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian runner and hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer (d. 2004)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Feh%C3%A9r\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Feh%C3%A9r\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Feh%C3%A9r"}]}, {"year": "1979", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Spanish swimmer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Spanish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Spanish swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_(swimmer)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, English-Montserratian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Tes<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>s<PERSON><PERSON> Bram<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English-Montserratian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tes<PERSON><PERSON>_<PERSON>\" title=\"Tes<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English-Montserratian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Brazilian model, fashionista, and businesswoman", "html": "1980 - <a href=\"https://wikipedia.org/wiki/G<PERSON>le_B%C3%BCndchen\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian model, fashionista, and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>%C3%BCndchen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian model, fashionista, and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gisele_B%C3%BCndchen"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Estonian journalist and politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Viktoria_Lad%C3%B5nskaja\" title=\"<PERSON>ia <PERSON>d<PERSON>ns<PERSON>\"><PERSON><PERSON></a>, Estonian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viktoria_Lad%C3%B5nskaja\" title=\"<PERSON>ia <PERSON>d<PERSON>nska<PERSON>\"><PERSON><PERSON></a>, Estonian journalist and politician", "links": [{"title": "Viktoria Ladõnskaja", "link": "https://wikipedia.org/wiki/Viktoria_Lad%C3%B5nskaja"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lla\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lla"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and screenwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian actor and YouTube personality", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and YouTube personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Canadian actor, director, producer, and screenwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1987", "text": "<PERSON>, Scottish violinist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American singer-songwriter, actress, and dancer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actress, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actress, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Iranian volleyball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_Cort%C3%A9s"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian sumo wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Chiyosh%C5%8Dma_Fujio\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiyosh%C5%8Dma_Fuji<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiyosh%C5%8Dma_Fujio"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1991", "text": "<PERSON>, Miss America 2015", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Miss_America_2015\" title=\"Miss America 2015\">Miss America 2015</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Miss_America_2015\" title=\"Miss America 2015\">Miss America 2015</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America 2015", "link": "https://wikipedia.org/wiki/Miss_America_2015"}]}, {"year": "1991", "text": "<PERSON>, German mountaineer and runner", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer and runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer and runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Thai actor, host, and model", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vih<PERSON>\" title=\"Tawan Vihokratana\"><PERSON><PERSON></a>, Thai actor, host, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_V<PERSON>\" title=\"Tawan Vihokratana\"><PERSON><PERSON></a>, Thai actor, host, and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tawan_Vih<PERSON>na"}]}, {"year": "1993", "text": "<PERSON>, New Zealand basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American rapper and singer (d. 2020)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Pop_Smoke\" title=\"Pop Smoke\"><PERSON> Smoke</a>, American rapper and singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pop_Smoke\" title=\"Pop Smoke\"><PERSON> Smoke</a>, American rapper and singer (d. 2020)", "links": [{"title": "Pop Smoke", "link": "https://wikipedia.org/wiki/Pop_Smoke"}]}], "Deaths": [{"year": "518", "text": "<PERSON><PERSON><PERSON>, Byzantine grand chamberlain and Monophysite martyr", "html": "518 - <a href=\"https://wikipedia.org/wiki/<PERSON>anti<PERSON>_(praepositus)\" title=\"<PERSON><PERSON><PERSON> (praepositus)\"><PERSON><PERSON><PERSON></a>, Byzantine grand chamberlain and Monophysite martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(praepositus)\" title=\"<PERSON><PERSON><PERSON> (praepositus)\"><PERSON><PERSON><PERSON></a>, Byzantine grand chamberlain and Monophysite martyr", "links": [{"title": "<PERSON><PERSON><PERSON> (praepositus)", "link": "https://wikipedia.org/wiki/Amantius_(praepositus)"}]}, {"year": "833", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish abbot and saint", "html": "833 - <a href=\"https://wikipedia.org/wiki/Ansegis<PERSON>\" title=\"Ansegis<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish abbot and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ansegis<PERSON>\" title=\"Ansegis<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish abbot and saint", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ansegisus"}]}, {"year": "985", "text": "<PERSON><PERSON><PERSON>, antipope of Rome", "html": "985 - <a href=\"https://wikipedia.org/wiki/Antipope_Boniface_VII\" title=\"Antipope Boniface VII\">Boniface VII</a>, antipope of Rome", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Boniface_VII\" title=\"Antipope Boniface VII\">Boniface VII</a>, antipope of Rome", "links": [{"title": "<PERSON>pop<PERSON>", "link": "https://wikipedia.org/wiki/Antipope_Boniface_VII"}]}, {"year": "1031", "text": "<PERSON>, king of France (b. 972)", "html": "1031 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, king of France (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, king of France (b. 972)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1128", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vizier (b. c. 1086)", "html": "1128 - <a href=\"https://wikipedia.org/wiki/Al-Ma%27mun_al-Bata%27ihi\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>un al-Bata'ihi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> vizier (b. c. 1086)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Ma%27mun_al-Bata%27ihi\" title=\"<PERSON>-<PERSON><PERSON><PERSON>un al-Bata'ihi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> vizier (b. c. 1086)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Ma%27mun_al-Bata%27ihi"}]}, {"year": "1156", "text": "<PERSON><PERSON>, emperor of Japan (b. 1103)", "html": "1156 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 1103)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\"><PERSON><PERSON></a>, emperor of Japan (b. 1103)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1320", "text": "<PERSON><PERSON>, king of Armenia (b. 1282)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Armenia\" title=\"<PERSON><PERSON>, King of Armenia\"><PERSON><PERSON></a>, king of Armenia (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Armenia\" title=\"<PERSON><PERSON>, King of Armenia\"><PERSON><PERSON></a>, king of Armenia (b. 1282)", "links": [{"title": "<PERSON><PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Armenia"}]}, {"year": "1332", "text": "<PERSON>, 1st Earl of Moray, regent of Scotland", "html": "1332 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, regent of Scotland", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray\" title=\"<PERSON>, 1st Earl of Moray\"><PERSON>, 1st Earl of Moray</a>, regent of Scotland", "links": [{"title": "<PERSON>, 1st Earl of Moray", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Moray"}]}, {"year": "1387", "text": "<PERSON>, French nobleman (b. 1356)", "html": "1387 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Artois,_Count_of_Eu\" title=\"<PERSON> of Artois, Count of Eu\"><PERSON></a>, French nobleman (b. 1356)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Artois,_Count_of_Eu\" title=\"<PERSON> of Artois, Count of Eu\"><PERSON></a>, French nobleman (b. 1356)", "links": [{"title": "<PERSON> of Artois, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1398", "text": "<PERSON>, 4th Earl of March, Welsh nobleman (b. 1374)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_March\" title=\"<PERSON>, 4th Earl of March\"><PERSON>, 4th Earl of March</a>, Welsh nobleman (b. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_March\" title=\"<PERSON>, 4th Earl of <PERSON>\"><PERSON>, 4th Earl of March</a>, Welsh nobleman (b. 1374)", "links": [{"title": "<PERSON>, 4th Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_<PERSON>"}]}, {"year": "1405", "text": "<PERSON>, Earl of Buchan, fourth son of King <PERSON> of Scotland (approximate, b. 1343)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Buchan\" title=\"<PERSON>, Earl of Buchan\"><PERSON>, Earl of Buchan</a>, fourth son of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (approximate, b. 1343)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Buchan\" title=\"<PERSON>, Earl of Buchan\"><PERSON>, Earl of Buchan</a>, fourth son of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (approximate, b. 1343)", "links": [{"title": "<PERSON>, Earl of Buchan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON><PERSON>, French historian and author (b. 1400)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/En<PERSON><PERSON><PERSON>_<PERSON>_Monstrelet\" title=\"<PERSON><PERSON><PERSON><PERSON> de Monstrelet\"><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (b. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> de Monstrelet\"><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (b. 1400)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/En<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1454", "text": "<PERSON> <PERSON>, king of Castile and León (b. 1405)", "html": "1454 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> II</a>, king of Castile and León (b. 1405)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> II of Castile\"><PERSON> II</a>, king of Castile and León (b. 1405)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1514", "text": "<PERSON><PERSON><PERSON><PERSON>, Transylvanian peasant revolt leader (b. 1470)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_D%C3%B3zsa\" title=\"György Dózsa\"><PERSON><PERSON><PERSON><PERSON></a>, Transylvanian peasant revolt leader (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_D%C3%B3zsa\" title=\"György Dózsa\"><PERSON><PERSON><PERSON><PERSON></a>, Transylvanian peasant revolt leader (b. 1470)", "links": [{"title": "György Dózsa", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_D%C3%B3zsa"}]}, {"year": "1524", "text": "<PERSON>, queen consort of France (b. 1499)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON></a>, queen consort of France (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON></a>, queen consort of France (b. 1499)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France"}]}, {"year": "1526", "text": "<PERSON>, Spanish explorer (b. 1490)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Jo<PERSON><PERSON>_de_Loa%C3%ADsa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Jo<PERSON><PERSON>_de_Loa%C3%ADsa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Garc%C3%ADa_<PERSON><PERSON><PERSON>_de_Loa%C3%ADsa"}]}, {"year": "1600", "text": "<PERSON>, English courtier (b. 1520)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1600)\" title=\"<PERSON> (died 1600)\"><PERSON></a>, English courtier (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1600)\" title=\"<PERSON> (died 1600)\"><PERSON></a>, English courtier (b. 1520)", "links": [{"title": "<PERSON> (died 1600)", "link": "https://wikipedia.org/wiki/<PERSON>(died_1600)"}]}, {"year": "1616", "text": "<PERSON>, Earl of Tyrone, Irish nobleman and rebel soldier (b. 1550)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>,_Earl_of_Tyrone\" title=\"<PERSON>, Earl of Tyrone\"><PERSON>, Earl of Tyrone</a>, Irish nobleman and rebel soldier (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>,_Earl_of_Tyrone\" title=\"<PERSON>, Earl of Tyrone\"><PERSON>, Earl of Tyrone</a>, Irish nobleman and rebel soldier (b. 1550)", "links": [{"title": "<PERSON>, Earl of Tyrone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>,_<PERSON>_of_Tyrone"}]}, {"year": "1704", "text": "<PERSON><PERSON><PERSON>, English-American farmer and soldier (b. 1620)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_White\" title=\"<PERSON><PERSON><PERSON> White\"><PERSON><PERSON><PERSON></a>, English-American farmer and soldier (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_White\" title=\"<PERSON><PERSON><PERSON> White\"><PERSON><PERSON><PERSON></a>, English-American farmer and soldier (b. 1620)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_White"}]}, {"year": "1752", "text": "<PERSON>, German-English composer and theorist (b. 1667)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English composer and theorist (b. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English composer and theorist (b. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian poet and politician (b. 1743)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and politician (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian poet and politician (b. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1866", "text": "<PERSON>, German mathematician and academic (b. 1826)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English poet and author (b. 1820)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English poet and critic (b. 1840)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monk<PERSON>\" title=\"William <PERSON> Monkhouse\"><PERSON></a>, English poet and critic (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monk<PERSON>\" title=\"William <PERSON> Monkhouse\"><PERSON></a>, English poet and critic (b. 1840)", "links": [{"title": "William <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Monkhouse"}]}, {"year": "1903", "text": "<PERSON>, pope of the Catholic Church (b. 1810)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Greek businessman and author (b. 1835)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_V<PERSON>\" class=\"mw-redirect\" title=\"De<PERSON><PERSON> Vike<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman and author (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> V<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman and author (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrius_Vikelas"}]}, {"year": "1908", "text": "<PERSON>, German geophysicist and seismologist (b. 1881)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geophysicist and seismologist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geophysicist and seismologist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Australian politician, 14th Premier of Queensland (b. 1863)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Galician architect (b. 1858)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Galician architect (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Galician architect (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Russian mathematician and theorist (b. 1856)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Mexican general and politician, Governor of Chihuahua (b. 1878)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\"><PERSON>cho <PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Chihuahua\" title=\"Governor of Chihuahua\">Governor of Chihuahua</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\"><PERSON><PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Chihuahua\" title=\"Governor of Chihuahua\">Governor of Chihuahua</a> (b. 1878)", "links": [{"title": "Pancho <PERSON>", "link": "https://wikipedia.org/wiki/Pancho_Villa"}, {"title": "Governor of Chihuahua", "link": "https://wikipedia.org/wiki/Governor_of_Chihuahua"}]}, {"year": "1926", "text": "<PERSON>, Soviet educator and politician of Belarusian origin (b. 1877)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet educator and politician of Belarusian origin (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet educator and politician of Belarusian origin (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, king of Romania (b. 1865)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON></a>, king of Romania (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON></a>, king of Romania (b. 1865)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Greek poet and author (b. 1896)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, French author and academic (b. 1853)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Austrian mathematician and philosopher from the Vienna Circle (b. 1882)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and philosopher from the Vienna Circle (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and philosopher from the Vienna Circle (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian physicist and engineer, Nobel Prize laureate (b. 1874)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American actor and producer (b. 1867)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lew_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German general (b. 1880)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American actress (b. 1901) ", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1901) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1901) ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, French author and poet (b. 1871)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Val%C3%A9ry"}]}, {"year": "1951", "text": "<PERSON>, king of Jordan (b. 1882)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jordan\" title=\"<PERSON> of Jordan\"><PERSON></a>, king of Jordan (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON></a>, king of Jordan (b. 1882)", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Haitian lawyer and politician, 33rd President of Haiti (b. 1900)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Dumarsais_Estim%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> Estimé\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dumarsais_Estim%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> Estimé\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dumarsais_Estim%C3%A9"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1953", "text": "<PERSON>, English author and hymn-writer (b. 1901)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and hymn-writer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and hymn-writer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Armenian businessman and philanthropist (b. 1869)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian businessman and philanthropist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian businessman and philanthropist (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian educator and politician, Canadian Minister of Militia and Defence (b. 1868)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)\" class=\"mw-redirect\" title=\"Minister of Militia and Defence (Canada)\">Canadian Minister of Militia and Defence</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)\" class=\"mw-redirect\" title=\"Minister of Militia and Defence (Canada)\">Canadian Minister of Militia and Defence</a> (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Militia and Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)"}]}, {"year": "1959", "text": "<PERSON>, American admiral and diplomat, United States Ambassador to France (b. 1875)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist (b. 1910)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American historian and author (b. 1886)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English journalist and politician, Chancellor of the Exchequer (b. 1913)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian singer and actress (b. 1930)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and actress (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor and martial artist (b. 1940)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American photographer and sculptor (b. 1938)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and sculptor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and sculptor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor and singer (b. 1900)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Bengali music director, composer and folk artist. (b. 1912)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali music director, composer and folk artist. (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali music director, composer and folk artist. (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American captain and cryptanalyst (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and cryptanalyst (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and cryptanalyst (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American record producer, co-founded Record Plant (b. 1939)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Record_Plant\" title=\"Record Plant\">Record Plant</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Record_Plant\" title=\"Record Plant\">Record Plant</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Record Plant", "link": "https://wikipedia.org/wiki/Record_Plant"}]}, {"year": "1980", "text": "<PERSON>, San Ildefonso Pueblo (Native American) potter (b. 1887)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/San_Ildefonso_Pueblo\" class=\"mw-redirect\" title=\"San Ildefonso Pueblo\">San Ildefonso Pueblo</a> (Native American) potter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/San_Ildefonso_Pueblo\" class=\"mw-redirect\" title=\"San Ildefonso Pueblo\">San Ildefonso Pueblo</a> (Native American) potter (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "San Ildefonso Pueblo", "link": "https://wikipedia.org/wiki/San_Ildefonso_Pueblo"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek-Romanian footballer (b. 1913)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Romanian footballer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Romanian footballer (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American soldier and journalist (b. 1923)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and journalist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American soldier and actor (b. 1921)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American soldier and actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American soldier and actor (b. 1921)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1989", "text": "<PERSON>, American judge and politician, 17th Governor of Montana (b. 1913)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Montana", "link": "https://wikipedia.org/wiki/Governor_of_Montana"}]}, {"year": "1990", "text": "<PERSON>, American police officer (b. 1907)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American lawyer and political figure (b. 1945)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and political figure (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and political figure (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Belgian painter (b. 1897)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Sri Lankan politician (b. 1939)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan politician (b. 1939)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American wrestler  (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/June_Byers\" title=\"June Byers\">June Byers</a>, American wrestler (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Byers\" title=\"June Byers\">June Byers</a>, American wrestler (b. 1922)", "links": [{"title": "June Byers", "link": "https://wikipedia.org/wiki/June_Byers"}]}, {"year": "1999", "text": "<PERSON>, American actress (b. 1916)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Greek footballer (b. 1946)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English author (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Fijian politician (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian politician (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian footballer and coach (b. 1965)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Valdemaras_Martink%C4%97nas\" title=\"Valde<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian footballer and coach (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valdemaras_<PERSON>k%C4%97nas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian footballer and coach (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valdemaras_Martink%C4%97nas"}]}, {"year": "2005", "text": "<PERSON>, Canadian-American actor (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Norwegian journalist and politician (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian journalist and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian journalist and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American director and cinematographer (b. 1958)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and cinematographer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director and cinematographer (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, South African-English theorist and activist (b. 1913)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English theorist and activist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English theorist and activist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, French actor, director, and producer (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>y\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and producer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>y\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and producer (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_Oury"}]}, {"year": "2007", "text": "<PERSON>, American Christian evangelist and talk show host (b. 1942)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian evangelist and talk show host (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian evangelist and talk show host (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American guitarist, songwriter, and producer (b. 1943)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, songwriter, and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, songwriter, and producer (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>um"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Croatian concentration camp commander (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Din<PERSON>_%C5%A0aki%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian concentration camp commander (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0aki%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian concentration camp commander (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dinko_%C5%A0aki%C4%87"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Turkish footballer (b. 1945)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vedat_Okyar"}]}, {"year": "2009", "text": "<PERSON>, American psychologist and academic (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (b. 1922)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "2011", "text": "<PERSON>, German-English painter and illustrator (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Lucian_Freud\" title=\"Lucian Freud\"><PERSON></a>, German-English painter and illustrator (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucian_Freud\" title=\"Lucian Freud\"><PERSON></a>, German-English painter and illustrator (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, English journalist (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Alastair_Burnet\" title=\"Alastair Burnet\"><PERSON><PERSON><PERSON></a>, English journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alastair_Burnet\" title=\"Alastair Burnet\"><PERSON><PERSON><PERSON></a>, English journalist (b. 1928)", "links": [{"title": "Alast<PERSON>", "link": "https://wikipedia.org/wiki/Alastair_Burnet"}]}, {"year": "2012", "text": "<PERSON>, American hurdler (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler (b. 1930)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "2012", "text": "<PERSON>, Portuguese historian, jurist, and politician, Portuguese Minister of Education (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese historian, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_(Portugal)\" title=\"Ministry of Education (Portugal)\">Portuguese Minister of Education</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese historian, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_(Portugal)\" title=\"Ministry of Education (Portugal)\">Portuguese Minister of Education</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Education (Portugal)", "link": "https://wikipedia.org/wiki/Ministry_of_Education_(Portugal)"}]}, {"year": "2013", "text": "<PERSON>, French pharmacist and businessman, founded Laboratoires Pierre F<PERSON>re (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Laboratoires_<PERSON>\" title=\"Laboratoires <PERSON>\">Laboratoires <PERSON></a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, French pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Laboratoires_<PERSON>\" title=\"Laboratoires Pierre <PERSON>\">Laboratoires <PERSON></a> (b. 1926)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}, {"title": "Laboratoires Pierre Fabre", "link": "https://wikipedia.org/wiki/Laboratoires_Pierre_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian politician, 2nd Governor of Goa (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Goa\" class=\"mw-redirect\" title=\"Governor of Goa\">Governor of Goa</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Goa\" class=\"mw-redirect\" title=\"Governor of Goa\">Governor of Goa</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Goa", "link": "https://wikipedia.org/wiki/Governor_of_Goa"}]}, {"year": "2013", "text": "<PERSON>, Canadian physician and politician (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman and politician, 32nd Governor of Oregon (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Romanian sculptor and educator (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian sculptor and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian sculptor and educator (b. 1923)", "links": [{"title": "Con<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American football player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American football player (b. 1931)", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "2014", "text": "<PERSON>, German archaeologist and academic (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, German archaeologist and academic (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, German archaeologist and academic (b. 1953)", "links": [{"title": "<PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(archaeologist)"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter and producer (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English footballer and manager (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Swiss-German keyboard player and producer (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German keyboard player and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-German keyboard player and producer (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Romanian actor, director, and essayist (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor, director, and essayist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor, director, and essayist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer (b. 1976)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Chester_Bennington\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chester_Bennington\" title=\"Chester Bennington\"><PERSON></a>, American singer (b. 1976)", "links": [{"title": "Chester <PERSON>", "link": "https://wikipedia.org/wiki/Chester_Bennington"}]}, {"year": "2020", "text": "<PERSON>, political commentator (b. 1983)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, political commentator (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, political commentator (b. 1983)", "links": [{"title": "<PERSON> (political commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_(political_commentator)"}]}, {"year": "2024", "text": "<PERSON>, American songwriter, guitarist and vocalist (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, guitarist and vocalist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, guitarist and vocalist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American novelist (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}