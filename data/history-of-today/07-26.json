{"date": "July 26", "url": "https://wikipedia.org/wiki/July_26", "data": {"Events": [{"year": "657", "text": "First Fitna: In the Battle of Siffin, troops led by <PERSON> clash with those led by <PERSON><PERSON><PERSON><PERSON>.", "html": "657 - <a href=\"https://wikipedia.org/wiki/First_Fitna\" title=\"First Fitna\">First Fitna</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Siffin\" title=\"Battle of Siffin\">Battle of Siffin</a>, troops led by <a href=\"https://wikipedia.org/wiki/Ali\" title=\"<PERSON>\"><PERSON></a> clash with those led by <a href=\"https://wikipedia.org/wiki/Muawiyah_I\" class=\"mw-redirect\" title=\"Muawiyah I\">Muawiyah I</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Fitna\" title=\"First Fitna\">First Fitna</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Siffin\" title=\"Battle of Siffin\">Battle of Siffin</a>, troops led by <a href=\"https://wikipedia.org/wiki/Ali\" title=\"<PERSON>\"><PERSON> ibn <PERSON></a> clash with those led by <a href=\"https://wikipedia.org/wiki/Muawiyah_I\" class=\"mw-redirect\" title=\"Muawiyah I\">Muawiyah I</a>.", "links": [{"title": "First Fitna", "link": "https://wikipedia.org/wiki/First_Fitna"}, {"title": "Battle of Siffin", "link": "https://wikipedia.org/wiki/Battle_of_Siffin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ali"}, {"title": "Muawiyah I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}]}, {"year": "811", "text": "Battle of Pliska: Byzantine Emperor <PERSON><PERSON><PERSON><PERSON> is killed and his heir <PERSON><PERSON><PERSON><PERSON> is seriously wounded.", "html": "811 - <a href=\"https://wikipedia.org/wiki/Battle_of_Pliska\" title=\"Battle of Pliska\">Battle of Pliska</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a> is killed and his heir <a href=\"https://wikipedia.org/wiki/Staurakios\" title=\"Staurakios\"><PERSON><PERSON><PERSON><PERSON></a> is seriously wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Pliska\" title=\"Battle of Pliska\">Battle of Pliska</a>: <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a> is killed and his heir <a href=\"https://wikipedia.org/wiki/Staurakios\" title=\"Staurakios\">Stau<PERSON><PERSON></a> is seriously wounded.", "links": [{"title": "Battle of Pliska", "link": "https://wikipedia.org/wiki/Battle_of_Pliska"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "Staurak<PERSON>", "link": "https://wikipedia.org/wiki/Staurakios"}]}, {"year": "920", "text": "Rout of an alliance of Christian troops from Navarre and Léon against the Muslims at the Battle of Valdejunquera.", "html": "920 - Rout of an alliance of Christian troops from <a href=\"https://wikipedia.org/wiki/Navarre\" title=\"Navarre\">Navarre</a> and <a href=\"https://wikipedia.org/wiki/Le%C3%B3n,_Spain\" title=\"León, Spain\"><PERSON></a> against the Muslims at the <a href=\"https://wikipedia.org/wiki/Battle_of_Valdejunquera\" title=\"Battle of Valdejunquera\">Battle of Valdejunquera</a>.", "no_year_html": "Rout of an alliance of Christian troops from <a href=\"https://wikipedia.org/wiki/Navarre\" title=\"Navarre\">Navarre</a> and <a href=\"https://wikipedia.org/wiki/Le%C3%B3n,_Spain\" title=\"León, Spain\"><PERSON></a> against the Muslims at the <a href=\"https://wikipedia.org/wiki/Battle_of_Valdejunquera\" title=\"Battle of Valdejunquera\">Battle of Valdejunquera</a>.", "links": [{"title": "Navarre", "link": "https://wikipedia.org/wiki/Navarre"}, {"title": "León, Spain", "link": "https://wikipedia.org/wiki/Le%C3%B3n,_Spain"}, {"title": "Battle of Valdejunquera", "link": "https://wikipedia.org/wiki/Battle_of_Valdejunquera"}]}, {"year": "1309", "text": "The Holy Roman Emperor <PERSON> is recognized King of the Romans by Pope <PERSON>.", "html": "1309 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\">The Holy Roman Emperor <PERSON></a> is recognized <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a> by <a href=\"https://wikipedia.org/wiki/Pope_Clement_V\" title=\"Pope Clement V\">Pope Clement V</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\">The Holy Roman Emperor <PERSON></a> is recognized <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a> by <a href=\"https://wikipedia.org/wiki/Pope_Clement_V\" title=\"Pope Clement V\">Pope Clement V</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "King of the Romans", "link": "https://wikipedia.org/wiki/King_of_the_Romans"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1509", "text": "The Emperor <PERSON><PERSON><PERSON><PERSON> ascends to the throne, marking the beginning of the regeneration of the Vijayanagara Empire.", "html": "1509 - The Emperor <a href=\"https://wikipedia.org/wiki/Krishnadevaraya\" title=\"Krishnadevaraya\"><PERSON><PERSON><PERSON><PERSON></a> ascends to the throne, marking the beginning of the regeneration of the <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara Empire</a>.", "no_year_html": "The Emperor <a href=\"https://wikipedia.org/wiki/Krishnadevaraya\" title=\"Krishnadevaraya\"><PERSON><PERSON><PERSON><PERSON></a> ascends to the throne, marking the beginning of the regeneration of the <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krishnadevaraya"}, {"title": "Vijayanagara Empire", "link": "https://wikipedia.org/wiki/Vijayanagara_Empire"}]}, {"year": "1529", "text": "<PERSON>, Spanish conquistador, is appointed governor of Peru.", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>nz%C3%A1lez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish conquistador, is appointed governor of Peru.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>nz%C3%A1lez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish conquistador, is appointed governor of Peru.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Pizarro_Gonz%C3%A1lez"}]}, {"year": "1579", "text": "<PERSON>, the English explorer, discovers a \"fair and good\" bay on the coast of the Pacific Northwest (probably Oregon or Washington).", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the English explorer, discovers a \"fair and good\" bay on the coast of the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> (probably <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a> or <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the English explorer, discovers a \"fair and good\" bay on the coast of the <a href=\"https://wikipedia.org/wiki/Pacific_Northwest\" title=\"Pacific Northwest\">Pacific Northwest</a> (probably <a href=\"https://wikipedia.org/wiki/Oregon\" title=\"Oregon\">Oregon</a> or <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pacific Northwest", "link": "https://wikipedia.org/wiki/Pacific_Northwest"}, {"title": "Oregon", "link": "https://wikipedia.org/wiki/Oregon"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}]}, {"year": "1581", "text": "Plak<PERSON><PERSON> van Verlatinghe (Act of Abjuration): The northern Low Countries declare their independence from the Spanish king, <PERSON>.", "html": "1581 - <a href=\"https://wikipedia.org/wiki/Act_of_Abjuration\" title=\"Act of Abjuration\">Plak<PERSON><PERSON> van Verlatinghe (Act of Abjuration)</a>: The northern <a href=\"https://wikipedia.org/wiki/Low_Countries\" title=\"Low Countries\">Low Countries</a> declare their independence from the Spanish king, <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Act_of_Abjuration\" title=\"Act of Abjuration\">Plak<PERSON><PERSON> van Verlatinghe (Act of Abjuration)</a>: The northern <a href=\"https://wikipedia.org/wiki/Low_Countries\" title=\"Low Countries\">Low Countries</a> declare their independence from the Spanish king, <a href=\"https://wikipedia.org/wiki/Philip_II_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> II</a>.", "links": [{"title": "Act of Abjuration", "link": "https://wikipedia.org/wiki/Act_of_Abjuration"}, {"title": "Low Countries", "link": "https://wikipedia.org/wiki/Low_Countries"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1703", "text": "During the Bavarian Rummel the rural population of Tyrol drove the Bavarian Prince-Elector <PERSON> out of North Tyrol with a victory at the Pontlatzer Bridge and thus prevented the Bavarian Army, which was allied with France, from marching as planned on Vienna during the War of the Spanish Succession.", "html": "1703 - During the <i><a href=\"https://wikipedia.org/wiki/Bavarian_Rummel\" title=\"Bavarian Rummel\">Bavarian Rummel</a></i> the rural population of <a href=\"https://wikipedia.org/wiki/County_of_Tyrol\" title=\"County of Tyrol\">Tyrol</a> drove the <a href=\"https://wikipedia.org/wiki/History_of_Bavaria#Absolutism\" title=\"History of Bavaria\">Bavarian</a> Prince-Elector <a href=\"https://wikipedia.org/wiki/<PERSON>_II_<PERSON>_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON> of Bavaria\"><PERSON></a> out of North Tyrol with a victory at the Pontlatzer Bridge and thus prevented the <a href=\"https://wikipedia.org/wiki/Bavarian_Army\" title=\"Bavarian Army\">Bavarian Army</a>, which was allied with <a href=\"https://wikipedia.org/wiki/History_of_France#Louis_XIV_(1643-1715)\" title=\"History of France\">France</a>, from marching as planned on <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "no_year_html": "During the <i><a href=\"https://wikipedia.org/wiki/Bavarian_Rummel\" title=\"Bavarian Rummel\">Bavarian Rummel</a></i> the rural population of <a href=\"https://wikipedia.org/wiki/County_of_Tyrol\" title=\"County of Tyrol\">Tyrol</a> drove the <a href=\"https://wikipedia.org/wiki/History_of_Bavaria#Absolutism\" title=\"History of Bavaria\">Bavarian</a> Prince-Elector <a href=\"https://wikipedia.org/wiki/<PERSON>_II_<PERSON>_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON> of Bavaria\"><PERSON></a> out of North Tyrol with a victory at the Pontlatzer Bridge and thus prevented the <a href=\"https://wikipedia.org/wiki/Bavarian_Army\" title=\"Bavarian Army\">Bavarian Army</a>, which was allied with <a href=\"https://wikipedia.org/wiki/History_of_France#Louis_XIV_(1643-1715)\" title=\"History of France\">France</a>, from marching as planned on <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>.", "links": [{"title": "Bavarian Rummel", "link": "https://wikipedia.org/wiki/Bavarian_Rummel"}, {"title": "County of Tyrol", "link": "https://wikipedia.org/wiki/County_of_Tyrol"}, {"title": "History of Bavaria", "link": "https://wikipedia.org/wiki/History_of_Bavaria#Absolutism"}, {"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Bavaria"}, {"title": "Bavarian Army", "link": "https://wikipedia.org/wiki/Bavarian_Army"}, {"title": "History of France", "link": "https://wikipedia.org/wiki/History_of_France#Louis_XIV_(1643-1715)"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}, {"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}]}, {"year": "1745", "text": "The first recorded women's cricket match takes place near Guildford, England.", "html": "1745 - The first recorded <a href=\"https://wikipedia.org/wiki/History_of_women%27s_cricket\" title=\"History of women's cricket\">women's cricket</a> match takes place near <a href=\"https://wikipedia.org/wiki/Guildford\" title=\"Guildford\">Guildford</a>, England.", "no_year_html": "The first recorded <a href=\"https://wikipedia.org/wiki/History_of_women%27s_cricket\" title=\"History of women's cricket\">women's cricket</a> match takes place near <a href=\"https://wikipedia.org/wiki/Guildford\" title=\"Guildford\">Guildford</a>, England.", "links": [{"title": "History of women's cricket", "link": "https://wikipedia.org/wiki/History_of_women%27s_cricket"}, {"title": "Guildford", "link": "https://wikipedia.org/wiki/Guildford"}]}, {"year": "1758", "text": "French and Indian War: The Siege of Louisbourg ends with British forces defeating the French and taking control of the Gulf of Saint Lawrence.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)\" title=\"Siege of Louisbourg (1758)\">Siege of Louisbourg</a> ends with British forces defeating the French and taking control of the <a href=\"https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence\" class=\"mw-redirect\" title=\"Gulf of Saint Lawrence\">Gulf of Saint Lawrence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)\" title=\"Siege of Louisbourg (1758)\">Siege of Louisbourg</a> ends with British forces defeating the French and taking control of the <a href=\"https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence\" class=\"mw-redirect\" title=\"Gulf of Saint Lawrence\">Gulf of Saint Lawrence</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Siege of Louisbourg (1758)", "link": "https://wikipedia.org/wiki/Siege_of_Louisbourg_(1758)"}, {"title": "Gulf of Saint Lawrence", "link": "https://wikipedia.org/wiki/Gulf_of_Saint_Lawrence"}]}, {"year": "1775", "text": "The office that would later become the United States Post Office Department is established by the Second Continental Congress. <PERSON> of Pennsylvania takes office as Postmaster General.", "html": "1775 - The office that would later become the <a href=\"https://wikipedia.org/wiki/United_States_Post_Office_Department\" title=\"United States Post Office Department\">United States Post Office Department</a> is established by the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Pennsylvania takes office as Postmaster General.", "no_year_html": "The office that would later become the <a href=\"https://wikipedia.org/wiki/United_States_Post_Office_Department\" title=\"United States Post Office Department\">United States Post Office Department</a> is established by the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Pennsylvania takes office as Postmaster General.", "links": [{"title": "United States Post Office Department", "link": "https://wikipedia.org/wiki/United_States_Post_Office_Department"}, {"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "The Emigration of Christians from the Crimea in 1778 begins.", "html": "1778 - The <a href=\"https://wikipedia.org/wiki/Emigration_of_Christians_from_the_Crimea_(1778)\" class=\"mw-redirect\" title=\"Emigration of Christians from the Crimea (1778)\">Emigration of Christians from the Crimea in 1778</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Emigration_of_Christians_from_the_Crimea_(1778)\" class=\"mw-redirect\" title=\"Emigration of Christians from the Crimea (1778)\">Emigration of Christians from the Crimea in 1778</a> begins.", "links": [{"title": "Emigration of Christians from the Crimea (1778)", "link": "https://wikipedia.org/wiki/Emigration_of_Christians_from_the_Crimea_(1778)"}]}, {"year": "1788", "text": "New York ratifies the United States Constitution and becomes the 11th state of the United States.", "html": "1788 - New York ratifies the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> and becomes the 11th state of the United States.", "no_year_html": "New York ratifies the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> and becomes the 11th state of the United States.", "links": [{"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1803", "text": "The Surrey Iron Railway, arguably the world's first public railway, opens in south London, United Kingdom.", "html": "1803 - The <a href=\"https://wikipedia.org/wiki/Surrey_Iron_Railway\" title=\"Surrey Iron Railway\">Surrey Iron Railway</a>, arguably the world's first public <a href=\"https://wikipedia.org/wiki/Rail_transport\" title=\"Rail transport\">railway</a>, opens in south London, United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Surrey_Iron_Railway\" title=\"Surrey Iron Railway\">Surrey Iron Railway</a>, arguably the world's first public <a href=\"https://wikipedia.org/wiki/Rail_transport\" title=\"Rail transport\">railway</a>, opens in south London, United Kingdom.", "links": [{"title": "Surrey Iron Railway", "link": "https://wikipedia.org/wiki/Surrey_Iron_Railway"}, {"title": "Rail transport", "link": "https://wikipedia.org/wiki/Rail_transport"}]}, {"year": "1814", "text": "The Swedish-Norwegian War begins.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)\" class=\"mw-redirect\" title=\"Swedish-Norwegian War (1814)\">Swedish-Norwegian War</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)\" class=\"mw-redirect\" title=\"Swedish-Norwegian War (1814)\">Swedish-Norwegian War</a> begins.", "links": [{"title": "Swedish-Norwegian War (1814)", "link": "https://wikipedia.org/wiki/Swedish%E2%80%93Norwegian_War_(1814)"}]}, {"year": "1822", "text": "<PERSON> arrives in Guayaquil, Ecuador, to meet with <PERSON><PERSON><PERSON>.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, to <a href=\"https://wikipedia.org/wiki/Guayaquil_Conference\" title=\"Guayaquil Conference\">meet</a> with <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>í<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, to <a href=\"https://wikipedia.org/wiki/Guayaquil_Conference\" title=\"Guayaquil Conference\">meet</a> with <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON>ívar\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "Guayaquil", "link": "https://wikipedia.org/wiki/Guayaquil"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Guayaquil Conference", "link": "https://wikipedia.org/wiki/Guayaquil_Conference"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}]}, {"year": "1822", "text": "First day of the three-day Battle of Dervenakia, between the Ottoman Empire force led by <PERSON><PERSON><PERSON> and the Greek Revolutionary force led by <PERSON><PERSON>.", "html": "1822 - First day of the three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Dervenakia\" title=\"Battle of Dervenakia\">Battle of Dervenakia</a>, between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> force led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek Revolutionary</a> force led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ronis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "First day of the three-day <a href=\"https://wikipedia.org/wiki/Battle_of_Dervenakia\" title=\"Battle of Dervenakia\">Battle of Dervenakia</a>, between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> force led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek Revolutionary</a> force led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kotronis\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Battle of Dervenakia", "link": "https://wikipedia.org/wiki/Battle_of_Dervenakia"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "Liberia declares its independence from the United States. France and the United Kingdom are the first to recognize the new nation.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a> declares its independence from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>. <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/The_United_Kingdom\" class=\"mw-redirect\" title=\"The United Kingdom\">the United Kingdom</a> are the first to recognize the new nation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberia\" title=\"Liberia\">Liberia</a> declares its independence from the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>. <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/The_United_Kingdom\" class=\"mw-redirect\" title=\"The United Kingdom\">the United Kingdom</a> are the first to recognize the new nation.", "links": [{"title": "Liberia", "link": "https://wikipedia.org/wiki/Liberia"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "The United Kingdom", "link": "https://wikipedia.org/wiki/The_United_Kingdom"}]}, {"year": "1861", "text": "American Civil War: <PERSON> assumes command of the Army of the Potomac following a disastrous Union defeat at the First Battle of Bull Run.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> following a disastrous <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> defeat at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> following a disastrous <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union</a> defeat at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "First Battle of Bull Run", "link": "https://wikipedia.org/wiki/First_Battle_of_Bull_Run"}]}, {"year": "1863", "text": "American Civil War: <PERSON>'s Raid ends; At Salineville, Ohio, Confederate cavalry leader <PERSON> and 360 of his volunteers are captured by Union forces.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Morgan%27s_Raid\" title=\"<PERSON>'s Raid\"><PERSON>'s Raid</a> ends; At <a href=\"https://wikipedia.org/wiki/Salineville,_Ohio\" title=\"Salineville, Ohio\">Salineville, Ohio</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> cavalry leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 360 of his volunteers are captured by Union forces.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Raid\" title=\"<PERSON>'s Raid\"><PERSON>'s Raid</a> ends; At <a href=\"https://wikipedia.org/wiki/Salineville,_Ohio\" title=\"Salineville, Ohio\">Salineville, Ohio</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> cavalry leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 360 of his volunteers are captured by Union forces.", "links": [{"title": "Morgan's Raid", "link": "https://wikipedia.org/wiki/Morgan%27s_Raid"}, {"title": "Salineville, Ohio", "link": "https://wikipedia.org/wiki/Salineville,_Ohio"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "Premiere of <PERSON>'s opera Parsifal at Bayreuth.", "html": "1882 - Premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Parsifal\" title=\"Parsifal\">Parsifal</a></i> at <a href=\"https://wikipedia.org/wiki/Bayreuth\" title=\"Bayreuth\">Bayreuth</a>.", "no_year_html": "Premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s opera <i><a href=\"https://wikipedia.org/wiki/Parsifal\" title=\"Parsifal\">Parsifal</a></i> at <a href=\"https://wikipedia.org/wiki/Bayreuth\" title=\"Bayreuth\">Bayreuth</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Parsifal", "link": "https://wikipedia.org/wiki/Parsifal"}, {"title": "Bayreuth", "link": "https://wikipedia.org/wiki/Bayreuth"}]}, {"year": "1882", "text": "The Republic of Stellaland is founded in Southern Africa.", "html": "1882 - The Republic of <a href=\"https://wikipedia.org/wiki/Stellaland\" title=\"Stellaland\">Stellaland</a> is founded in Southern Africa.", "no_year_html": "The Republic of <a href=\"https://wikipedia.org/wiki/Stellaland\" title=\"Stellaland\">Stellaland</a> is founded in Southern Africa.", "links": [{"title": "Stellaland", "link": "https://wikipedia.org/wiki/Stellaland"}]}, {"year": "1887", "text": "Publication of the Unua Libro, founding the Esperanto movement.", "html": "1887 - Publication of the <i><a href=\"https://wikipedia.org/wiki/Unua_Libro\" title=\"Unua Libro\">Unua Libro</a></i>, founding the Esperanto movement.", "no_year_html": "Publication of the <i><a href=\"https://wikipedia.org/wiki/Unua_Libro\" title=\"Unua Libro\">Unua Libro</a></i>, founding the Esperanto movement.", "links": [{"title": "Unua Libro", "link": "https://wikipedia.org/wiki/Unua_Libro"}]}, {"year": "1890", "text": "In Buenos Aires, Argentina the Revolución del Parque takes place, forcing President <PERSON>'s resignation.", "html": "1890 - In <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires, Argentina</a> the <a href=\"https://wikipedia.org/wiki/Revolution_of_the_Park\" title=\"Revolution of the Park\">Revolución del Parque</a> takes place, forcing President <a href=\"https://wikipedia.org/wiki/Miguel_%C3%81ngel_Ju%C3%<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s resignation.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires, Argentina</a> the <a href=\"https://wikipedia.org/wiki/Revolution_of_the_Park\" title=\"Revolution of the Park\">Revolución del Parque</a> takes place, forcing President <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Ju%C3%<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s resignation.", "links": [{"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}, {"title": "Revolution of the Park", "link": "https://wikipedia.org/wiki/Revolution_of_the_Park"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Ju%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "France annexes Tahiti.", "html": "1891 - France annexes <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a>.", "no_year_html": "France annexes <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a>.", "links": [{"title": "Tahiti", "link": "https://wikipedia.org/wiki/Tahiti"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON> is elected as the first Indian Member of Parliament in Britain.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected as the first Indian <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">Member of Parliament</a> in Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is elected as the first Indian <a href=\"https://wikipedia.org/wiki/British_House_of_Commons\" class=\"mw-redirect\" title=\"British House of Commons\">Member of Parliament</a> in Britain.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "British House of Commons", "link": "https://wikipedia.org/wiki/British_House_of_Commons"}]}, {"year": "1897", "text": "Anglo-Afghan War: The Pashtun faki<PERSON> leads an army of more than 10,000 to begin a siege of the British garrison in the Malakand Agency of the North West Frontier Province of India.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Anglo-Afghan_War\" title=\"Anglo-Afghan War\">Anglo-Afghan War</a>: The <a href=\"https://wikipedia.org/wiki/Pashtuns\" title=\"Pashtuns\">Pashtun</a> <a href=\"https://wikipedia.org/wiki/Fakir\" title=\"Fakir\">fakir</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ah\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads an army of more than 10,000 to begin a <a href=\"https://wikipedia.org/wiki/Siege_of_Malakand\" title=\"Siege of Malakand\">siege of the British garrison</a> in the <a href=\"https://wikipedia.org/wiki/Malakand_Agency\" title=\"Malakand Agency\">Malakand Agency</a> of the <a href=\"https://wikipedia.org/wiki/Khyber_Pakhtunkhwa\" title=\"Khyber Pakhtunkhwa\">North West Frontier Province</a> of India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Afghan_War\" title=\"Anglo-Afghan War\">Anglo-Afghan War</a>: The <a href=\"https://wikipedia.org/wiki/Pashtuns\" title=\"Pashtuns\">Pashtun</a> <a href=\"https://wikipedia.org/wiki/Fakir\" title=\"Fakir\">fakir</a> <a href=\"https://wikipedia.org/wiki/<PERSON>ullah\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads an army of more than 10,000 to begin a <a href=\"https://wikipedia.org/wiki/Siege_of_Malakand\" title=\"Siege of Malakand\">siege of the British garrison</a> in the <a href=\"https://wikipedia.org/wiki/Malakand_Agency\" title=\"Malakand Agency\">Malakand Agency</a> of the <a href=\"https://wikipedia.org/wiki/Khyber_Pakhtunkhwa\" title=\"Khyber Pakhtunkhwa\">North West Frontier Province</a> of India.", "links": [{"title": "Anglo-Afghan War", "link": "https://wikipedia.org/wiki/Anglo-Afghan_War"}, {"title": "Pashtuns", "link": "https://wikipedia.org/wiki/Pashtuns"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>akir"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Siege of Malakand", "link": "https://wikipedia.org/wiki/Siege_of_Malakand"}, {"title": "Malakand Agency", "link": "https://wikipedia.org/wiki/Malakand_Agency"}, {"title": "Khyber Pakhtunkhwa", "link": "https://wikipedia.org/wiki/Khyber_Pakhtunkhwa"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, the 27th President of the Dominican Republic, is assassinated.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON></a>, the 27th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a>, is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON></a>, the 27th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a>, is assassinated.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aux"}, {"title": "President of the Dominican Republic", "link": "https://wikipedia.org/wiki/President_of_the_Dominican_Republic"}]}, {"year": "1908", "text": "United States Attorney General <PERSON> issues an order to immediately staff the Office of the Chief Examiner (later renamed the Federal Bureau of Investigation).", "html": "1908 - <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an order to immediately staff the Office of the Chief Examiner (later renamed the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an order to immediately staff the Office of the Chief Examiner (later renamed the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a>).", "links": [{"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}]}, {"year": "1918", "text": "<PERSON>'s paper, which became known as <PERSON><PERSON><PERSON>'s theorem was presented at Göttingen, Germany, from which  conservation laws are deduced for symmetries of angular momentum, linear momentum, and energy.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s paper, which became known as <a href=\"https://wikipedia.org/wiki/Noether%27s_theorem\" title=\"<PERSON><PERSON><PERSON>'s theorem\"><PERSON><PERSON><PERSON>'s theorem</a> was presented at <a href=\"https://wikipedia.org/wiki/G%C3%B6ttingen\" title=\"Göttingen\">Göttingen</a>, Germany, from which <a href=\"https://wikipedia.org/wiki/Conservation_law\" title=\"Conservation law\">conservation laws</a> are deduced for symmetries of <a href=\"https://wikipedia.org/wiki/Angular_momentum\" title=\"Angular momentum\">angular momentum</a>, <a href=\"https://wikipedia.org/wiki/Momentum\" title=\"Momentum\">linear momentum</a>, and <a href=\"https://wikipedia.org/wiki/Energy\" title=\"Energy\">energy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s paper, which became known as <a href=\"https://wikipedia.org/wiki/<PERSON>ether%27s_theorem\" title=\"<PERSON><PERSON><PERSON>'s theorem\"><PERSON><PERSON><PERSON>'s theorem</a> was presented at <a href=\"https://wikipedia.org/wiki/G%C3%B6ttingen\" title=\"Göttingen\">Göttingen</a>, Germany, from which <a href=\"https://wikipedia.org/wiki/Conservation_law\" title=\"Conservation law\">conservation laws</a> are deduced for symmetries of <a href=\"https://wikipedia.org/wiki/Angular_momentum\" title=\"Angular momentum\">angular momentum</a>, <a href=\"https://wikipedia.org/wiki/Momentum\" title=\"Momentum\">linear momentum</a>, and <a href=\"https://wikipedia.org/wiki/Energy\" title=\"Energy\">energy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}, {"title": "<PERSON><PERSON><PERSON>'s theorem", "link": "https://wikipedia.org/wiki/Noether%27s_theorem"}, {"title": "Göttingen", "link": "https://wikipedia.org/wiki/G%C3%B6ttingen"}, {"title": "Conservation law", "link": "https://wikipedia.org/wiki/Conservation_law"}, {"title": "Angular momentum", "link": "https://wikipedia.org/wiki/Angular_momentum"}, {"title": "Momentum", "link": "https://wikipedia.org/wiki/Momentum"}, {"title": "Energy", "link": "https://wikipedia.org/wiki/Energy"}]}, {"year": "1936", "text": "Spanish Civil War: Germany and Italy decide to intervene in the war in support for Francisco Franco and the Nationalist faction.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> decide to intervene in the war in support for <a href=\"https://wikipedia.org/wiki/Francisco_Franco\" title=\"Francisco Franco\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalist faction</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> decide to intervene in the war in support for <a href=\"https://wikipedia.org/wiki/Francisco_Franco\" title=\"Francisco Franco\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalist faction</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "Nationalist faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)"}]}, {"year": "1936", "text": "King <PERSON>, in one of his few official duties before he abdicates the throne, officially unveils the Canadian National Vimy Memorial.", "html": "1936 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VIII\"><PERSON></a>, in one of his few official duties before he <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_abdication_crisis\" class=\"mw-redirect\" title=\"<PERSON> VIII abdication crisis\">abdicates</a> the throne, officially unveils the <a href=\"https://wikipedia.org/wiki/Canadian_National_Vimy_Memorial\" title=\"Canadian National Vimy Memorial\">Canadian National Vimy Memorial</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in one of his few official duties before he <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_abdication_crisis\" class=\"mw-redirect\" title=\"<PERSON> VIII abdication crisis\">abdicates</a> the throne, officially unveils the <a href=\"https://wikipedia.org/wiki/Canadian_National_Vimy_Memorial\" title=\"Canadian National Vimy Memorial\">Canadian National Vimy Memorial</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Edward VIII abdication crisis", "link": "https://wikipedia.org/wiki/<PERSON>_VIII_abdication_crisis"}, {"title": "Canadian National Vimy Memorial", "link": "https://wikipedia.org/wiki/Canadian_National_V<PERSON>y_Memorial"}]}, {"year": "1937", "text": "Spanish Civil War: End of the Battle of Brunete with the Nationalist victory.", "html": "1937 - Spanish Civil War: End of the <a href=\"https://wikipedia.org/wiki/Battle_of_Brunete\" title=\"Battle of Brunete\">Battle of Brunete</a> with the Nationalist victory.", "no_year_html": "Spanish Civil War: End of the <a href=\"https://wikipedia.org/wiki/Battle_of_Brunete\" title=\"Battle of Brunete\">Battle of Brunete</a> with the Nationalist victory.", "links": [{"title": "Battle of Brunete", "link": "https://wikipedia.org/wiki/Battle_of_Brunete"}]}, {"year": "1941", "text": "World War II: Battle of Grand Harbour, British forces on Malta destroy an attack by the Italian Decima Flottiglia MAS. Fort St Elmo Bridge covering the harbour is demolished in the process.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Harbour\" class=\"mw-redirect\" title=\"Battle of Grand Harbour\">Battle of Grand Harbour</a>, British forces on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> destroy an attack by the Italian <a href=\"https://wikipedia.org/wiki/Decima_Flottiglia_MAS\" title=\"Decima Flottiglia MAS\">Decima Flottiglia MAS</a>. Fort <a href=\"https://wikipedia.org/wiki/St_Elmo_Bridge\" title=\"St Elmo Bridge\">St Elmo Bridge</a> covering the harbour is demolished in the process.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Harbour\" class=\"mw-redirect\" title=\"Battle of Grand Harbour\">Battle of Grand Harbour</a>, British forces on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> destroy an attack by the Italian <a href=\"https://wikipedia.org/wiki/Decima_Flottiglia_MAS\" title=\"Decima Flottiglia MAS\">Decima Flottiglia MAS</a>. Fort <a href=\"https://wikipedia.org/wiki/St_Elmo_Bridge\" title=\"St Elmo Bridge\">St Elmo Bridge</a> covering the harbour is demolished in the process.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Grand Harbour", "link": "https://wikipedia.org/wiki/Battle_of_Grand_Harbour"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Decima Flottiglia MAS", "link": "https://wikipedia.org/wiki/Decima_Flottiglia_MAS"}, {"title": "St Elmo Bridge", "link": "https://wikipedia.org/wiki/St_Elmo_Bridge"}]}, {"year": "1941", "text": "World War II: In response to the Japanese occupation of French Indochina, the United States, Britain and the Netherlands freeze all Japanese assets and cut off oil shipments.", "html": "1941 - World War II: In response to the Japanese occupation of <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, the United States, Britain and the Netherlands freeze all Japanese assets and <a href=\"https://wikipedia.org/wiki/ABCD_line\" title=\"ABCD line\">cut off oil shipments</a>.", "no_year_html": "World War II: In response to the Japanese occupation of <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, the United States, Britain and the Netherlands freeze all Japanese assets and <a href=\"https://wikipedia.org/wiki/ABCD_line\" title=\"ABCD line\">cut off oil shipments</a>.", "links": [{"title": "French Indochina", "link": "https://wikipedia.org/wiki/French_Indochina"}, {"title": "ABCD line", "link": "https://wikipedia.org/wiki/ABCD_line"}]}, {"year": "1944", "text": "World War II: The Red Army enters Lviv, a major city in western Ukraine, capturing it from the Nazis. Only 300 Jews survive out of 160,000 living in Lviv prior to occupation.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> enters <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>, a major city in western <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, capturing it from the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a>. Only 300 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> survive out of 160,000 living in Lviv prior to occupation.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> enters <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>, a major city in western <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, capturing it from the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a>. Only 300 <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jews</a> survive out of 160,000 living in Lviv prior to occupation.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Lviv", "link": "https://wikipedia.org/wiki/Lviv"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}]}, {"year": "1945", "text": "The Labour Party wins the United Kingdom general election of July 5 by a landslide, removing <PERSON> from power.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> wins the <a href=\"https://wikipedia.org/wiki/1945_United_Kingdom_general_election\" title=\"1945 United Kingdom general election\">United Kingdom general election of July 5</a> by a landslide, removing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> from power.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a> wins the <a href=\"https://wikipedia.org/wiki/1945_United_Kingdom_general_election\" title=\"1945 United Kingdom general election\">United Kingdom general election of July 5</a> by a landslide, removing <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> from power.", "links": [{"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "1945 United Kingdom general election", "link": "https://wikipedia.org/wiki/1945_United_Kingdom_general_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1945", "text": "World War II: The Potsdam Declaration is signed in Potsdam, Germany.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Potsdam_Declaration\" title=\"Potsdam Declaration\">Potsdam Declaration</a> is signed in <a href=\"https://wikipedia.org/wiki/Potsdam\" title=\"Potsdam\">Potsdam</a>, Germany.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Potsdam_Declaration\" title=\"Potsdam Declaration\">Potsdam Declaration</a> is signed in <a href=\"https://wikipedia.org/wiki/Potsdam\" title=\"Potsdam\">Potsdam</a>, Germany.", "links": [{"title": "Potsdam Declaration", "link": "https://wikipedia.org/wiki/Potsdam_Declaration"}, {"title": "Potsdam", "link": "https://wikipedia.org/wiki/Potsdam"}]}, {"year": "1945", "text": "World War II: HMS Vestal is the last British Royal Navy ship to be sunk in the war.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/HMS_Vestal_(J215)\" title=\"HMS Vestal (J215)\">HMS <i>Vestal</i></a> is the last British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> ship to be sunk in the war.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/HMS_Vestal_(J215)\" title=\"HMS Vestal (J215)\">HMS <i>Vestal</i></a> is the last British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> ship to be sunk in the war.", "links": [{"title": "HMS Vestal (J215)", "link": "https://wikipedia.org/wiki/HMS_Vestal_(J215)"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1945", "text": "World War II: The USS Indianapolis arrives at Tinian with components and enriched uranium for the Little Boy nuclear bomb.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a> arrives at <a href=\"https://wikipedia.org/wiki/<PERSON>ian\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> with components and <a href=\"https://wikipedia.org/wiki/Enriched_uranium\" title=\"Enriched uranium\">enriched uranium</a> for the <a href=\"https://wikipedia.org/wiki/<PERSON>_Boy\" title=\"Little Boy\">Little Boy</a> nuclear bomb.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)\" title=\"USS Indianapolis (CA-35)\">USS <i>Indianapolis</i></a> arrives at <a href=\"https://wikipedia.org/wiki/<PERSON>ian\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> with components and <a href=\"https://wikipedia.org/wiki/Enriched_uranium\" title=\"Enriched uranium\">enriched uranium</a> for the <a href=\"https://wikipedia.org/wiki/<PERSON>_Boy\" title=\"Little Boy\">Little Boy</a> nuclear bomb.", "links": [{"title": "USS Indianapolis (CA-35)", "link": "https://wikipedia.org/wiki/USS_Indianapolis_(CA-35)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "Enriched uranium", "link": "https://wikipedia.org/wiki/Enriched_uranium"}, {"title": "Little Boy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "Aloha Airlines begins service from Honolulu International Airport.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Aloha_Airlines\" title=\"Aloha Airlines\">Aloha Airlines</a> begins service from <a href=\"https://wikipedia.org/wiki/Honolulu_International_Airport\" class=\"mw-redirect\" title=\"Honolulu International Airport\">Honolulu International Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aloha_Airlines\" title=\"Aloha Airlines\">Aloha Airlines</a> begins service from <a href=\"https://wikipedia.org/wiki/Honolulu_International_Airport\" class=\"mw-redirect\" title=\"Honolulu International Airport\">Honolulu International Airport</a>.", "links": [{"title": "Aloha Airlines", "link": "https://wikipedia.org/wiki/Aloha_Airlines"}, {"title": "Honolulu International Airport", "link": "https://wikipedia.org/wiki/Honolulu_International_Airport"}]}, {"year": "1947", "text": "Cold War: U.S. President <PERSON> signs the National Security Act of 1947 into United States law creating the Central Intelligence Agency, United States Department of Defense, United States Air Force, Joint Chiefs of Staff, and the United States National Security Council.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act of 1947</a> into <a href=\"https://wikipedia.org/wiki/Law_of_the_United_States\" title=\"Law of the United States\">United States law</a> creating the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a>, <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">United States Department of Defense</a>, <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>, <a href=\"https://wikipedia.org/wiki/Joint_Chiefs_of_Staff\" title=\"Joint Chiefs of Staff\">Joint Chiefs of Staff</a>, and the <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Council\" title=\"United States National Security Council\">United States National Security Council</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act of 1947</a> into <a href=\"https://wikipedia.org/wiki/Law_of_the_United_States\" title=\"Law of the United States\">United States law</a> creating the <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">Central Intelligence Agency</a>, <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">United States Department of Defense</a>, <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a>, <a href=\"https://wikipedia.org/wiki/Joint_Chiefs_of_Staff\" title=\"Joint Chiefs of Staff\">Joint Chiefs of Staff</a>, and the <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Council\" title=\"United States National Security Council\">United States National Security Council</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Security Act of 1947", "link": "https://wikipedia.org/wiki/National_Security_Act_of_1947"}, {"title": "Law of the United States", "link": "https://wikipedia.org/wiki/Law_of_the_United_States"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "United States Department of Defense", "link": "https://wikipedia.org/wiki/United_States_Department_of_Defense"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Joint Chiefs of Staff", "link": "https://wikipedia.org/wiki/Joint_Chiefs_of_Staff"}, {"title": "United States National Security Council", "link": "https://wikipedia.org/wiki/United_States_National_Security_Council"}]}, {"year": "1948", "text": "U.S. President <PERSON> signs Executive Order 9981, desegregating the military of the United States.", "html": "1948 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/Executive_Order_9981\" title=\"Executive Order 9981\">Executive Order 9981</a>, desegregating the military of the United States.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs <a href=\"https://wikipedia.org/wiki/Executive_Order_9981\" title=\"Executive Order 9981\">Executive Order 9981</a>, desegregating the military of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Executive Order 9981", "link": "https://wikipedia.org/wiki/Executive_Order_9981"}]}, {"year": "1951", "text": "<PERSON>'s 13th animated film, Alice in Wonderland, premieres in London, England, United Kingdom.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON> Disney</a>'s 13th <a href=\"https://wikipedia.org/wiki/Animation\" title=\"Animation\">animated film</a>, <i><a href=\"https://wikipedia.org/wiki/Alice_in_Wonderland_(1951_film)\" title=\"Alice in Wonderland (1951 film)\">Alice in Wonderland</a></i>, premieres in London, England, United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON> Disney</a>'s 13th <a href=\"https://wikipedia.org/wiki/Animation\" title=\"Animation\">animated film</a>, <i><a href=\"https://wikipedia.org/wiki/Alice_in_Wonderland_(1951_film)\" title=\"Alice in Wonderland (1951 film)\">Alice in Wonderland</a></i>, premieres in London, England, United Kingdom.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Animation", "link": "https://wikipedia.org/wiki/Animation"}, {"title": "<PERSON> in Wonderland (1951 film)", "link": "https://wikipedia.org/wiki/<PERSON>_in_Wonderland_(1951_film)"}]}, {"year": "1952", "text": "King <PERSON><PERSON><PERSON> of Egypt abdicates in favor of his son <PERSON><PERSON>.", "html": "1952 - King <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\"><PERSON><PERSON><PERSON> of Egypt</a> abdicates in favor of his son <a href=\"https://wikipedia.org/wiki/Fuad_II_of_Egypt\" title=\"Fuad II of Egypt\">Fuad</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Farouk_of_Egypt\" title=\"Farouk of Egypt\"><PERSON><PERSON><PERSON> of Egypt</a> abdicates in favor of his son <a href=\"https://wikipedia.org/wiki/Fuad_II_of_Egypt\" title=\"Fuad II of Egypt\">Fuad</a>.", "links": [{"title": "Far<PERSON>k of Egypt", "link": "https://wikipedia.org/wiki/Farouk_of_Egypt"}, {"title": "Fuad II of Egypt", "link": "https://wikipedia.org/wiki/Fuad_II_of_Egypt"}]}, {"year": "1953", "text": "Cold War: <PERSON><PERSON> leads an unsuccessful attack on the Moncada Barracks, thus beginning the Cuban Revolution. The movement took the name of the date: 26th of July Movement", "html": "1953 - Cold War: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a> leads an unsuccessful attack on the <a href=\"https://wikipedia.org/wiki/Moncada_Barracks\" class=\"mw-redirect\" title=\"Moncada Barracks\">Moncada Barracks</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>. The movement took the name of the date: <a href=\"https://wikipedia.org/wiki/26th_of_July_Movement\" title=\"26th of July Movement\">26th of July Movement</a>", "no_year_html": "Cold War: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a> leads an unsuccessful attack on the <a href=\"https://wikipedia.org/wiki/Moncada_Barracks\" class=\"mw-redirect\" title=\"Moncada Barracks\">Moncada Barracks</a>, thus beginning the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>. The movement took the name of the date: <a href=\"https://wikipedia.org/wiki/26th_of_July_Movement\" title=\"26th of July Movement\">26th of July Movement</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Moncada Barracks", "link": "https://wikipedia.org/wiki/Moncada_Barracks"}, {"title": "Cuban Revolution", "link": "https://wikipedia.org/wiki/Cuban_Revolution"}, {"title": "26th of July Movement", "link": "https://wikipedia.org/wiki/26th_of_July_Movement"}]}, {"year": "1953", "text": "Arizona Governor <PERSON> orders an anti-polygamy law enforcement crackdown on residents of Short Creek, Arizona, which becomes known as the Short Creek raid.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> orders an anti-<a href=\"https://wikipedia.org/wiki/Polygamy\" title=\"Polygamy\">polygamy</a> law enforcement crackdown on residents of <a href=\"https://wikipedia.org/wiki/Colorado_City,_Arizona\" title=\"Colorado City, Arizona\">Short Creek, Arizona</a>, which becomes known as the <a href=\"https://wikipedia.org/wiki/Short_Creek_raid\" title=\"Short Creek raid\">Short Creek raid</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> orders an anti-<a href=\"https://wikipedia.org/wiki/Polygamy\" title=\"Polygamy\">polygamy</a> law enforcement crackdown on residents of <a href=\"https://wikipedia.org/wiki/Colorado_City,_Arizona\" title=\"Colorado City, Arizona\">Short Creek, Arizona</a>, which becomes known as the <a href=\"https://wikipedia.org/wiki/Short_Creek_raid\" title=\"Short Creek raid\">Short Creek raid</a>.", "links": [{"title": "Arizona", "link": "https://wikipedia.org/wiki/Arizona"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Polygamy", "link": "https://wikipedia.org/wiki/Polygamy"}, {"title": "Colorado City, Arizona", "link": "https://wikipedia.org/wiki/Colorado_City,_Arizona"}, {"title": "Short Creek raid", "link": "https://wikipedia.org/wiki/Short_Creek_raid"}]}, {"year": "1953", "text": "Soldiers from the 2nd Battalion, Royal Australian Regiment repel a number of Chinese assaults against a key position known as The Hook during the Battle of the Samichon River, just hours before the Armistice Agreement is signed, ending the Korean War.", "html": "1953 - Soldiers from the <a href=\"https://wikipedia.org/wiki/2nd_Battalion,_Royal_Australian_Regiment\" title=\"2nd Battalion, Royal Australian Regiment\">2nd Battalion, Royal Australian Regiment</a> repel a number of Chinese assaults against a key position known as The Hook during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Samichon_River\" title=\"Battle of the Samichon River\">Battle of the Samichon River</a>, just hours before the Armistice Agreement is signed, ending the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>.", "no_year_html": "Soldiers from the <a href=\"https://wikipedia.org/wiki/2nd_Battalion,_Royal_Australian_Regiment\" title=\"2nd Battalion, Royal Australian Regiment\">2nd Battalion, Royal Australian Regiment</a> repel a number of Chinese assaults against a key position known as The Hook during the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Samichon_River\" title=\"Battle of the Samichon River\">Battle of the Samichon River</a>, just hours before the Armistice Agreement is signed, ending the <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>.", "links": [{"title": "2nd Battalion, Royal Australian Regiment", "link": "https://wikipedia.org/wiki/2nd_Battalion,_Royal_Australian_Regiment"}, {"title": "Battle of the Samichon River", "link": "https://wikipedia.org/wiki/Battle_of_the_Samichon_River"}, {"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}]}, {"year": "1956", "text": "Following the World Bank's refusal to fund building the Aswan Dam, Egyptian leader <PERSON><PERSON><PERSON> nationalizes the Suez Canal, sparking international condemnation.", "html": "1956 - Following the <a href=\"https://wikipedia.org/wiki/World_Bank\" title=\"World Bank\">World Bank</a>'s refusal to fund building the <a href=\"https://wikipedia.org/wiki/Aswan_Dam\" title=\"Aswan Dam\">Aswan Dam</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> leader <a href=\"https://wikipedia.org/wiki/Gamal_Abdel_Nasser\" title=\"Gamal Abdel Nasser\"><PERSON><PERSON><PERSON><PERSON></a> nationalizes the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, <a href=\"https://wikipedia.org/wiki/Suez_crisis\" class=\"mw-redirect\" title=\"Suez crisis\">sparking international condemnation</a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/World_Bank\" title=\"World Bank\">World Bank</a>'s refusal to fund building the <a href=\"https://wikipedia.org/wiki/Aswan_Dam\" title=\"Aswan Dam\">Aswan Dam</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> leader <a href=\"https://wikipedia.org/wiki/Gamal_Abdel_Nasser\" title=\"Gamal Abdel Nasser\"><PERSON><PERSON><PERSON><PERSON></a> nationalizes the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, <a href=\"https://wikipedia.org/wiki/Suez_crisis\" class=\"mw-redirect\" title=\"Suez crisis\">sparking international condemnation</a>.", "links": [{"title": "World Bank", "link": "https://wikipedia.org/wiki/World_Bank"}, {"title": "Aswan Dam", "link": "https://wikipedia.org/wiki/Aswan_Dam"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Suez crisis", "link": "https://wikipedia.org/wiki/Suez_crisis"}]}, {"year": "1957", "text": "<PERSON>, dictator of Guatemala, is assassinated.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, dictator of <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, is assassinated.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}]}, {"year": "1958", "text": "Explorer program: Explorer 4 is launched.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_4\" title=\"Explorer 4\">Explorer 4</a></i> is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_4\" title=\"Explorer 4\">Explorer 4</a></i> is launched.", "links": [{"title": "Explorer program", "link": "https://wikipedia.org/wiki/Explorer_program"}, {"title": "Explorer 4", "link": "https://wikipedia.org/wiki/Explorer_4"}]}, {"year": "1963", "text": "Syncom 2, the world's first geosynchronous satellite, is launched from Cape Canaveral on a Delta B booster.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Syncom#Syncom_2\" title=\"Syncom\">Syncom 2</a>, the world's first <a href=\"https://wikipedia.org/wiki/Geosynchronous_satellite\" title=\"Geosynchronous satellite\">geosynchronous satellite</a>, is launched from Cape Canaveral on a Delta B booster.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syncom#Syncom_2\" title=\"Syncom\">Syncom 2</a>, the world's first <a href=\"https://wikipedia.org/wiki/Geosynchronous_satellite\" title=\"Geosynchronous satellite\">geosynchronous satellite</a>, is launched from Cape Canaveral on a Delta B booster.", "links": [{"title": "Syncom", "link": "https://wikipedia.org/wiki/Syncom#Syncom_2"}, {"title": "Geosynchronous satellite", "link": "https://wikipedia.org/wiki/Geosynchronous_satellite"}]}, {"year": "1963", "text": "An earthquake in Skopje, Yugoslavia (present-day North Macedonia) leaves 1,100 dead.", "html": "1963 - An <a href=\"https://wikipedia.org/wiki/1963_Skopje_earthquake\" title=\"1963 Skopje earthquake\">earthquake</a> in <a href=\"https://wikipedia.org/wiki/Skopje\" title=\"Skopje\">Skopje</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> (present-day <a href=\"https://wikipedia.org/wiki/North_Macedonia\" title=\"North Macedonia\">North Macedonia</a>) leaves 1,100 dead.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1963_Skopje_earthquake\" title=\"1963 Skopje earthquake\">earthquake</a> in <a href=\"https://wikipedia.org/wiki/Skopje\" title=\"Skopje\">Skopje</a>, <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> (present-day <a href=\"https://wikipedia.org/wiki/North_Macedonia\" title=\"North Macedonia\">North Macedonia</a>) leaves 1,100 dead.", "links": [{"title": "1963 Skopje earthquake", "link": "https://wikipedia.org/wiki/1963_Skopje_earthquake"}, {"title": "Skopje", "link": "https://wikipedia.org/wiki/Skopje"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "North Macedonia", "link": "https://wikipedia.org/wiki/North_Macedonia"}]}, {"year": "1963", "text": "The Organisation for Economic Co-operation and Development votes to admit Japan.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Organisation_for_Economic_Co-operation_and_Development\" class=\"mw-redirect\" title=\"Organisation for Economic Co-operation and Development\">Organisation for Economic Co-operation and Development</a> votes to admit <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organisation_for_Economic_Co-operation_and_Development\" class=\"mw-redirect\" title=\"Organisation for Economic Co-operation and Development\">Organisation for Economic Co-operation and Development</a> votes to admit <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>.", "links": [{"title": "Organisation for Economic Co-operation and Development", "link": "https://wikipedia.org/wiki/Organisation_for_Economic_Co-operation_and_Development"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1968", "text": "Vietnam War: South Vietnamese opposition leader <PERSON><PERSON><PERSON><PERSON><PERSON> is sentenced to five years hard labor for advocating the formation of a coalition government as a way to move toward an end to the war.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> opposition leader <a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%C3%ACnh_Dzu\" title=\"Trương Đình Dzu\">Trương Đình Dzu</a> is sentenced to five years <a href=\"https://wikipedia.org/wiki/Penal_labour#Prison_labour\" title=\"Penal labour\">hard labor</a> for advocating the formation of a <a href=\"https://wikipedia.org/wiki/Coalition_government\" title=\"Coalition government\">coalition government</a> as a way to move toward an end to the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> opposition leader <a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%C3%ACnh_Dzu\" title=\"Trương Đình Dzu\">Trương Đình Dzu</a> is sentenced to five years <a href=\"https://wikipedia.org/wiki/Penal_labour#Prison_labour\" title=\"Penal labour\">hard labor</a> for advocating the formation of a <a href=\"https://wikipedia.org/wiki/Coalition_government\" title=\"Coalition government\">coalition government</a> as a way to move toward an end to the war.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%C3%ACnh_Dzu"}, {"title": "Penal labour", "link": "https://wikipedia.org/wiki/Penal_labour#Prison_labour"}, {"title": "Coalition government", "link": "https://wikipedia.org/wiki/Coalition_government"}]}, {"year": "1971", "text": "Apollo program: Launch of Apollo 15 on the first Apollo \"J-Mission\", and first use of a Lunar Roving Vehicle.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: Launch of <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a> on the first Apollo \"<a href=\"https://wikipedia.org/wiki/List_of_Apollo_missions#Alphabetical_mission_types\" title=\"List of Apollo missions\">J-Mission</a>\", and first use of a <a href=\"https://wikipedia.org/wiki/Lunar_Roving_Vehicle\" title=\"Lunar Roving Vehicle\">Lunar Roving Vehicle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: Launch of <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a> on the first Apollo \"<a href=\"https://wikipedia.org/wiki/List_of_Apollo_missions#Alphabetical_mission_types\" title=\"List of Apollo missions\">J-Mission</a>\", and first use of a <a href=\"https://wikipedia.org/wiki/Lunar_Roving_Vehicle\" title=\"Lunar Roving Vehicle\">Lunar Roving Vehicle</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 15", "link": "https://wikipedia.org/wiki/Apollo_15"}, {"title": "List of Apollo missions", "link": "https://wikipedia.org/wiki/List_of_Apollo_missions#Alphabetical_mission_types"}, {"title": "Lunar Roving Vehicle", "link": "https://wikipedia.org/wiki/Lunar_Roving_Vehicle"}]}, {"year": "1974", "text": "Greek Prime Minister <PERSON><PERSON> forms the country's first civil government after seven years of military rule.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Greek Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> forms the country's first civil government after <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">seven years of military rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Greek Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> forms the country's first civil government after <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">seven years of military rule</a>.", "links": [{"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}]}, {"year": "1977", "text": "The National Assembly of Quebec imposes the use of French as the official language of the provincial government.", "html": "1977 - The National Assembly of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> imposes the use of French as the official language of the provincial government.", "no_year_html": "The National Assembly of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> imposes the use of French as the official language of the provincial government.", "links": [{"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1989", "text": "A federal grand jury indicts Cornell University student <PERSON>, Jr<PERSON> for releasing the Morris worm, thus becoming the first person to be prosecuted under the 1986 Computer Fraud and Abuse Act.", "html": "1989 - A federal <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> indicts <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> student <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>, Jr.</a> for releasing the <a href=\"https://wikipedia.org/wiki/Morris_worm\" title=\"Morris worm\">Morris worm</a>, thus becoming the first person to be prosecuted under the 1986 <a href=\"https://wikipedia.org/wiki/Computer_Fraud_and_Abuse_Act\" title=\"Computer Fraud and Abuse Act\">Computer Fraud and Abuse Act</a>.", "no_year_html": "A federal <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> indicts <a href=\"https://wikipedia.org/wiki/Cornell_University\" title=\"Cornell University\">Cornell University</a> student <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>, Jr.</a> for releasing the <a href=\"https://wikipedia.org/wiki/Morris_worm\" title=\"Morris worm\">Morris worm</a>, thus becoming the first person to be prosecuted under the 1986 <a href=\"https://wikipedia.org/wiki/Computer_Fraud_and_Abuse_Act\" title=\"Computer Fraud and Abuse Act\">Computer Fraud and Abuse Act</a>.", "links": [{"title": "Grand jury", "link": "https://wikipedia.org/wiki/Grand_jury"}, {"title": "Cornell University", "link": "https://wikipedia.org/wiki/Cornell_University"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Morris worm", "link": "https://wikipedia.org/wiki/<PERSON>_worm"}, {"title": "Computer Fraud and Abuse Act", "link": "https://wikipedia.org/wiki/Computer_Fraud_and_Abuse_Act"}]}, {"year": "1990", "text": "The Americans with Disabilities Act of 1990 is signed into law by President <PERSON>.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Americans_with_Disabilities_Act_of_1990\" title=\"Americans with Disabilities Act of 1990\">Americans with Disabilities Act of 1990</a> is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Americans_with_Disabilities_Act_of_1990\" title=\"Americans with Disabilities Act of 1990\">Americans with Disabilities Act of 1990</a> is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Americans with Disabilities Act of 1990", "link": "https://wikipedia.org/wiki/Americans_with_Disabilities_Act_of_1990"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "Asiana Airlines Flight 733 crashes into a ridge on Mt. Ungeo on its third attempt to land at Mokpo Airport, South Korea. Sixty-eight of the 116 people on board are killed.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_733\" title=\"Asiana Airlines Flight 733\">Asiana Airlines Flight 733</a> crashes into a ridge on Mt. Ungeo on its third attempt to land at <a href=\"https://wikipedia.org/wiki/Mokpo_Airport\" class=\"mw-redirect\" title=\"Mokpo Airport\">Mokpo Airport</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>. Sixty-eight of the 116 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_733\" title=\"Asiana Airlines Flight 733\">Asiana Airlines Flight 733</a> crashes into a ridge on Mt. Ungeo on its third attempt to land at <a href=\"https://wikipedia.org/wiki/Mokpo_Airport\" class=\"mw-redirect\" title=\"Mokpo Airport\">Mokpo Airport</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>. Sixty-eight of the 116 people on board are killed.", "links": [{"title": "Asiana Airlines Flight 733", "link": "https://wikipedia.org/wiki/Asiana_Airlines_Flight_733"}, {"title": "Mokpo Airport", "link": "https://wikipedia.org/wiki/Mokpo_Airport"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}]}, {"year": "1999", "text": "Kargil conflict officially comes to an end. The Indian Army announces the complete eviction of Pakistani intruders.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kargil_War\" title=\"Kargil War\">Kargil conflict</a> officially comes to an end. The Indian Army announces the complete eviction of Pakistani intruders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kargil_War\" title=\"Kargil War\">Kargil conflict</a> officially comes to an end. The Indian Army announces the complete eviction of Pakistani intruders.", "links": [{"title": "Kargil War", "link": "https://wikipedia.org/wiki/Kargil_War"}]}, {"year": "2005", "text": "Space Shuttle program: STS-114 Mission: Launch of Discovery, NASA's first scheduled flight mission after the Columbia Disaster in 2003.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-114\" title=\"STS-114\">STS-114</a> Mission: Launch of <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\"><i>Discovery</i></a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s first scheduled flight mission after the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\"><i>Columbia</i> Disaster</a> in <a href=\"https://wikipedia.org/wiki/2003\" title=\"2003\">2003</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-114\" title=\"STS-114\">STS-114</a> Mission: Launch of <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\"><i>Discovery</i></a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s first scheduled flight mission after the <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster\" title=\"Space Shuttle Columbia disaster\"><i>Columbia</i> Disaster</a> in <a href=\"https://wikipedia.org/wiki/2003\" title=\"2003\">2003</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-114", "link": "https://wikipedia.org/wiki/STS-114"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle Columbia disaster", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia_disaster"}, {"title": "2003", "link": "https://wikipedia.org/wiki/2003"}]}, {"year": "2005", "text": "Mumbai, India receives 99.5cm of rain (39.17 inches) within 24 hours, resulting in floods killing over 5,000 people.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India receives <a href=\"https://wikipedia.org/wiki/Maharashtra_floods_of_2005\" title=\"Maharashtra floods of 2005\">99.5cm of rain</a> (39.17 inches) within 24 hours, resulting in floods killing over 5,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Mumbai</a>, India receives <a href=\"https://wikipedia.org/wiki/Maharashtra_floods_of_2005\" title=\"Maharashtra floods of 2005\">99.5cm of rain</a> (39.17 inches) within 24 hours, resulting in floods killing over 5,000 people.", "links": [{"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}, {"title": "Maharashtra floods of 2005", "link": "https://wikipedia.org/wiki/Maharashtra_floods_of_2005"}]}, {"year": "2008", "text": "Fifty-six people are killed and over 200 people are injured, in the Ahmedabad bombings in India.", "html": "2008 - Fifty-six people are killed and over 200 people are injured, in the <a href=\"https://wikipedia.org/wiki/2008_Ahmedabad_bombings\" title=\"2008 Ahmedabad bombings\">Ahmedabad bombings in India</a>.", "no_year_html": "Fifty-six people are killed and over 200 people are injured, in the <a href=\"https://wikipedia.org/wiki/2008_Ahmedabad_bombings\" title=\"2008 Ahmedabad bombings\">Ahmedabad bombings in India</a>.", "links": [{"title": "2008 Ahmedabad bombings", "link": "https://wikipedia.org/wiki/2008_Ahmedabad_bombings"}]}, {"year": "2009", "text": "The militant Nigerian Islamist group Boko Haram attacks a police station in Bauchi, leading to reprisals by the Nigeria Police Force and four days of violence across multiple cities.", "html": "2009 - The militant <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> group <a href=\"https://wikipedia.org/wiki/Boko_Haram\" title=\"Boko Haram\"><PERSON><PERSON> Haram</a> attacks a police station in <a href=\"https://wikipedia.org/wiki/Bauchi_(city)\" title=\"Bauchi (city)\">Bauchi</a>, leading to reprisals by the <a href=\"https://wikipedia.org/wiki/Nigeria_Police_Force\" title=\"Nigeria Police Force\">Nigeria Police Force</a> and <a href=\"https://wikipedia.org/wiki/2009_Boko_Haram_Uprising\" class=\"mw-redirect\" title=\"2009 Boko Haram Uprising\">four days of violence</a> across multiple cities.", "no_year_html": "The militant <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigerian</a> <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> group <a href=\"https://wikipedia.org/wiki/Bo<PERSON>_Haram\" title=\"Boko Haram\"><PERSON><PERSON> Haram</a> attacks a police station in <a href=\"https://wikipedia.org/wiki/Bauchi_(city)\" title=\"Bauchi (city)\">Bauchi</a>, leading to reprisals by the <a href=\"https://wikipedia.org/wiki/Nigeria_Police_Force\" title=\"Nigeria Police Force\">Nigeria Police Force</a> and <a href=\"https://wikipedia.org/wiki/2009_Boko_Haram_Uprising\" class=\"mw-redirect\" title=\"2009 Boko Haram Uprising\">four days of violence</a> across multiple cities.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Islamism", "link": "https://wikipedia.org/wiki/Islamism"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}, {"title": "Bauchi (city)", "link": "https://wikipedia.org/wiki/Bauchi_(city)"}, {"title": "Nigeria Police Force", "link": "https://wikipedia.org/wiki/Nigeria_Police_Force"}, {"title": "2009 Boko Haram Uprising", "link": "https://wikipedia.org/wiki/2009_Boko_Haram_Uprising"}]}, {"year": "2011", "text": "A Royal Moroccan Air Force Lockheed C-130 Hercules crashes near Guelmim Airport in Guelmim, Morocco. All 80 people on board are killed.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/Royal_Moroccan_Air_Force\" title=\"Royal Moroccan Air Force\">Royal Moroccan Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Lockheed C-130 Hercules</a> <a href=\"https://wikipedia.org/wiki/2011_Royal_Moroccan_Air_Force_C-130_crash\" title=\"2011 Royal Moroccan Air Force C-130 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Guelmim_Airport\" title=\"Guelmim Airport\">Guelmim Airport</a> in <a href=\"https://wikipedia.org/wiki/Guelmim\" title=\"Guelmim\">Guelmim</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>. All 80 people on board are killed.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Royal_Moroccan_Air_Force\" title=\"Royal Moroccan Air Force\">Royal Moroccan Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">Lockheed C-130 Hercules</a> <a href=\"https://wikipedia.org/wiki/2011_Royal_Moroccan_Air_Force_C-130_crash\" title=\"2011 Royal Moroccan Air Force C-130 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Guelmim_Airport\" title=\"Guelmim Airport\">Guelmim Airport</a> in <a href=\"https://wikipedia.org/wiki/Guelmim\" title=\"Guelmim\">Guelmim</a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>. All 80 people on board are killed.", "links": [{"title": "Royal Moroccan Air Force", "link": "https://wikipedia.org/wiki/Royal_Moroccan_Air_Force"}, {"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}, {"title": "2011 Royal Moroccan Air Force C-130 crash", "link": "https://wikipedia.org/wiki/2011_Royal_Moroccan_Air_Force_C-130_crash"}, {"title": "Guelmim Airport", "link": "https://wikipedia.org/wiki/Guelmim_Airport"}, {"title": "G<PERSON>mi<PERSON>", "link": "https://wikipedia.org/wiki/Guelmim"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "2016", "text": "The Sagamihara stabbings occur in Kanagawa Prefecture in Japan. Nineteen people are killed.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/Sagamihara_stabbings\" title=\"Sagamihara stabbings\">Sagamihara stabbings</a> occur in <a href=\"https://wikipedia.org/wiki/Kanagawa_Prefecture\" title=\"Kanagawa Prefecture\">Kanagawa Prefecture</a> in Japan. Nineteen people are killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sagamihara_stabbings\" title=\"Sagamihara stabbings\">Sagamihara stabbings</a> occur in <a href=\"https://wikipedia.org/wiki/Kanagawa_Prefecture\" title=\"Kanagawa Prefecture\">Kanagawa Prefecture</a> in Japan. Nineteen people are killed.", "links": [{"title": "<PERSON><PERSON><PERSON> stabbings", "link": "https://wikipedia.org/wiki/Sagamihara_stabbings"}, {"title": "Kanagawa Prefecture", "link": "https://wikipedia.org/wiki/Kanagawa_Prefecture"}]}, {"year": "2016", "text": "<PERSON> becomes the first female nominee for President of the United States by a major political party at the Democratic National Convention in Philadelphia.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female nominee for <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> by a major political party at the <a href=\"https://wikipedia.org/wiki/2016_Democratic_National_Convention\" title=\"2016 Democratic National Convention\">Democratic National Convention</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female nominee for <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> by a major political party at the <a href=\"https://wikipedia.org/wiki/2016_Democratic_National_Convention\" title=\"2016 Democratic National Convention\">Democratic National Convention</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "2016 Democratic National Convention", "link": "https://wikipedia.org/wiki/2016_Democratic_National_Convention"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "2016", "text": "Solar Impulse 2 becomes the first solar-powered aircraft to circumnavigate the Earth.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Solar_Impulse_2\" class=\"mw-redirect\" title=\"Solar Impulse 2\">Solar Impulse 2</a> becomes the first solar-powered aircraft to circumnavigate the <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solar_Impulse_2\" class=\"mw-redirect\" title=\"Solar Impulse 2\">Solar Impulse 2</a> becomes the first solar-powered aircraft to circumnavigate the <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>.", "links": [{"title": "Solar Impulse 2", "link": "https://wikipedia.org/wiki/Solar_Impulse_2"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "2024", "text": "Olympic swimmer <PERSON> has been kicked out of the 2024 Summer Olympics in Paris due to sneaking out of the Olympic Village.", "html": "2024 - Olympic swimmer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a> has been kicked out of the <a href=\"https://wikipedia.org/wiki/2024_Summer_Olympics\" title=\"2024 Summer Olympics\">2024 Summer Olympics</a> in Paris due to sneaking out of the Olympic Village.", "no_year_html": "Olympic swimmer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a> has been kicked out of the <a href=\"https://wikipedia.org/wiki/2024_Summer_Olympics\" title=\"2024 Summer Olympics\">2024 Summer Olympics</a> in Paris due to sneaking out of the Olympic Village.", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/Ana_<PERSON>_<PERSON>_(swimmer)"}, {"title": "2024 Summer Olympics", "link": "https://wikipedia.org/wiki/2024_Summer_Olympics"}]}], "Births": [{"year": "1030", "text": "<PERSON><PERSON><PERSON> of Szczepanów, Polish bishop and saint (d. 1079)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, Polish bishop and saint (d. 1079)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, Polish bishop and saint (d. 1079)", "links": [{"title": "<PERSON><PERSON><PERSON> of Szczepanów", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w"}]}, {"year": "1400", "text": "<PERSON>, Countess of Worcester, English noble (d. 1439)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Worcester\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Worcester\"><PERSON>, Countess of Worcester</a>, English noble (d. 1439)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Worcester\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Worcester\"><PERSON>, Countess of Worcester</a>, English noble (d. 1439)", "links": [{"title": "<PERSON>, Countess of Worcester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Worcester"}]}, {"year": "1502", "text": "<PERSON>, German printer (d. 1555)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German printer (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German printer (d. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Egenolff"}]}, {"year": "1678", "text": "<PERSON>, Holy Roman Emperor (d. 1711)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1711)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON>, German physician, mathematician, and historian (d. 1778)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician, mathematician, and historian (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician, mathematician, and historian (d. 1778)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON>, American general and politician, 4th Vice President of the United States (d. 1812)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(vice_president)\" title=\"<PERSON> (vice president)\"><PERSON></a>, American general and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(vice_president)\" title=\"<PERSON> (vice president)\"><PERSON></a>, American general and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1812)", "links": [{"title": "<PERSON> (vice president)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(vice_president)"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1782", "text": "<PERSON>, Irish pianist and composer (d. 1837)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Irish pianist and composer (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Irish pianist and composer (d. 1837)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1791", "text": "<PERSON>, Austrian pianist, composer, and conductor (d. 1844)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Wolfgang_Mozart\" title=\"Franz Xaver Wolfgang Mozart\">Franz <PERSON><PERSON></a>, Austrian pianist, composer, and conductor (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Franz Xaver Wolfgang Mozart\"><PERSON></a>, Austrian pianist, composer, and conductor (d. 1844)", "links": [{"title": "Franz <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American painter, author, and traveler (d. 1872)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, author, and traveler (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, author, and traveler (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, Mexican general and politician, 42nd President of Mexico (d. 1855)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1819", "text": "<PERSON>, American guitarist and educator (d. 1887)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and educator (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Belgian politician, 14th Prime Minister of Belgium, Nobel Prize laureate (d. 1912)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1838", "text": "<PERSON>, American soldier and whistleblower of the Sand Creek Massacre (d. 1865)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and whistleblower of the <a href=\"https://wikipedia.org/wiki/Sand_Creek_Massacre\" class=\"mw-redirect\" title=\"Sand Creek Massacre\">Sand Creek Massacre</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and whistleblower of the <a href=\"https://wikipedia.org/wiki/Sand_Creek_Massacre\" class=\"mw-redirect\" title=\"Sand Creek Massacre\">Sand Creek Massacre</a> (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sand Creek Massacre", "link": "https://wikipedia.org/wiki/Sand_Creek_Massacre"}]}, {"year": "1841", "text": "<PERSON>, Estonian journalist and politician (d. 1882)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and politician (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, English economist and academic (d. 1924)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Ukrainian-Polish engineer and journalist (d. 1938)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Polish engineer and journalist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Polish engineer and journalist (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_<PERSON>zewiecki"}]}, {"year": "1854", "text": "<PERSON>, French dermatologist and academic (d. 1918)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dermatologist and academic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dermatologist and academic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, German sociologist and philosopher (d. 1936)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%B6nnies\" title=\"Ferdinand Tönnies\"><PERSON></a>, German sociologist and philosopher (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T%C3%B6nnies\" title=\"Ferdinand T<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_T%C3%B6nnies"}]}, {"year": "1856", "text": "<PERSON>, Irish playwright and critic, Nobel Prize laureate (d. 1950)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1858", "text": "<PERSON>, Australian cricketer and lawyer (d. 1943)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and lawyer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and lawyer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian composer (d. 1948)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols\" title=\"<PERSON><PERSON><PERSON><PERSON> Vīto<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols\" title=\"<PERSON><PERSON><PERSON><PERSON> Vītols\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian composer (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%81zeps_V%C4%ABtols"}]}, {"year": "1865", "text": "<PERSON>, German journalist and politician, 10th Chancellor of Germany (d. 1939)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and composer (d. 1910)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and composer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and composer (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Sen"}]}, {"year": "1874", "text": "<PERSON>, Russian-American bassist, composer, and conductor (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American bassist, composer, and conductor (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American bassist, composer, and conductor (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Swiss psychiatrist and psychotherapist (d. 1961)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychotherapist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychotherapist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Italian botanist and explorer (d. 1943)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian botanist and explorer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian botanist and explorer (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Spanish poet and academic (d. 1939)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and academic (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American investor and security analyst, \"Great Bear of Wall Street\" (d. 1940)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Livermore\" class=\"mw-redirect\" title=\"<PERSON> Livermore\"><PERSON></a>, American investor and security analyst, \"Great Bear of Wall Street\" (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Livermore\" class=\"mw-redirect\" title=\"<PERSON>n Livermore\"><PERSON></a>, American investor and security analyst, \"Great Bear of Wall Street\" (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Livermore"}]}, {"year": "1878", "text": "<PERSON>, German swimmer and water polo player (d. 1937)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer and water polo player (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer and water polo player (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese field marshal and politician, 48th Japanese Minister of War (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Shun<PERSON><PERSON>_Hata\" title=\"Shunrok<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 48th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>nrok<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 48th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "Ministry of War of Japan", "link": "https://wikipedia.org/wiki/Ministry_of_War_of_Japan"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian playwright and politician, 1st Prime Minister of Ukrainian People's Republic (d. 1951)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian playwright and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Ukraine\" class=\"mw-redirect\" title=\"List of Prime Ministers of Ukraine\">Prime Minister of Ukrainian People's Republic</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian playwright and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Ukraine\" class=\"mw-redirect\" title=\"List of Prime Ministers of Ukraine\">Prime Minister of Ukrainian People's Republic</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "List of Prime Ministers of Ukraine", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Ukraine"}]}, {"year": "1882", "text": "<PERSON>, Australian politician, 33rd Premier of Victoria (d. 1950)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1885", "text": "<PERSON>, American baseball player (d. 1967)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, French soldier and author (d. 1967)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Swedish actor (d. 1965)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, South African cricketer and rugby player (d. 1918)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American admiral, Medal of Honor recipient (d. 1942)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1892", "text": "<PERSON>, American baseball player and manager (d. 1966)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German painter and illustrator (d. 1959)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, English novelist and philosopher (d. 1963)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and philosopher (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English novelist and philosopher (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American actress and comedian (d. 1964)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American actress and comedian (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American actress and comedian (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English soldier and race car driver (d. 1933)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and race car driver (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and race car driver (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American lawyer and politician (d. 1974)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American journalist and author (d. 1976)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Israeli politician and teacher (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli politician and teacher (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli politician and teacher (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician (d. 1963)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uver"}]}, {"year": "1904", "text": "<PERSON>, Canadian astronomer and academic (d. 1951)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astronomer and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astronomer and academic (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American industrialist and entrepreneur, invented the flight simulator (d. 1981)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American industrialist and entrepreneur, invented the <a href=\"https://wikipedia.org/wiki/Flight_simulator\" title=\"Flight simulator\">flight simulator</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American industrialist and entrepreneur, invented the <a href=\"https://wikipedia.org/wiki/Flight_simulator\" title=\"Flight simulator\">flight simulator</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Flight simulator", "link": "https://wikipedia.org/wiki/Flight_simulator"}]}, {"year": "1905", "text": "<PERSON>, Australian rugby league player (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1906", "text": "<PERSON><PERSON>, German-Polish lieutenant (d. 1943)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I%C5%82%C5%82akowicz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Polish lieutenant (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I%C5%82%C5%82akowicz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Polish lieutenant (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irena_I%C5%82%C5%82akowicz"}]}, {"year": "1908", "text": "<PERSON>, Luxembourger sculptor (d. 2002)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger sculptor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger sculptor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Baron <PERSON>, English lawyer and politician, Chancellor of the Exchequer (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1994)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1909", "text": "<PERSON>, American actress and singer (d. 1979)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Hong Kong banker, lawyer, and politician (d. 2012)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Kan_<PERSON>-keung\" title=\"Kan <PERSON>-keung\"><PERSON><PERSON>-keu<PERSON></a>, Hong Kong banker, lawyer, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>-keung\" title=\"Kan <PERSON>et-keung\"><PERSON><PERSON>-keu<PERSON></a>, Hong Kong banker, lawyer, and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>ng", "link": "https://wikipedia.org/wiki/Kan_<PERSON><PERSON>-keung"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American soldier, lawyer, and politician, 34th Governor of Florida (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Florida\" class=\"mw-redirect\" title=\"List of Governors of Florida\">34th Governor of Florida</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Florida\" class=\"mw-redirect\" title=\"List of Governors of Florida\">34th Governor of Florida</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of Governors of Florida", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Florida"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, American trumpet player and bandleader (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpet player and bandleader (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpet player and bandleader (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American baseball player (d. 1968)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American physician and actor (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Brazilian archbishop (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian archbishop (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian archbishop (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 1986)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Virginia_Gilmore\" title=\"<PERSON> Gilmore\"><PERSON></a>, American actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gilmore\" title=\"<PERSON> Gilmore\"><PERSON></a>, American actress (d. 1986)", "links": [{"title": "<PERSON> Gilmore", "link": "https://wikipedia.org/wiki/Virginia_Gilmore"}]}, {"year": "1919", "text": "<PERSON>, English biologist and chemist (d. 2022)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and chemist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American football player and coach (d. 1983)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player and manager (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American radio host, actor, and screenwriter (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, actor, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, actor, and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American director, producer, and screenwriter (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American record producer (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author and illustrator (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Welsh author (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh author (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American baseball player and coach (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Polish-Swedish physician and politician (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swedish physician and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swedish physician and politician (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American physicist and engineer (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Polish-American film producer (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American film producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American film producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Spanish author and academic (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Matute\" title=\"<PERSON>\"><PERSON></a>, Spanish author and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Matute\" title=\"<PERSON>\"><PERSON></a>, Spanish author and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Mar%C3%AD<PERSON>_Matute"}]}, {"year": "1926", "text": "<PERSON>, American actor, director, and screenwriter (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"James Best\"><PERSON></a>, American actor, director, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"James Best\"><PERSON></a>, American actor, director, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ramchand"}]}, {"year": "1928", "text": "<PERSON>, English race car driver (d. 1955)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Italian academic and politician, 8th President of Italy (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian academic and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian academic and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>ga"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1928", "text": "<PERSON>, French-American photographer and director (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American photographer and director (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American photographer and director (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani author and poet (d. 1980)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani author and poet (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani author and poet (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American talent manager, father of <PERSON> (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>, American talent manager, father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)\" class=\"mw-redirect\" title=\"<PERSON> (manager)\"><PERSON></a>, American talent manager, father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2018)", "links": [{"title": "<PERSON> (manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(manager)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American director, producer, screenwriter, and cinematographer (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and cinematographer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and cinematographer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian lawyer and politician, 10th Premier of Alberta (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1928", "text": "<PERSON>-<PERSON>, Baroness <PERSON>-<PERSON>, Irish-born English politician", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>, Baroness <PERSON>-<PERSON></a>, Irish-born English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>, Baroness <PERSON>-<PERSON></a>, Irish-born English politician", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian lawyer and politician, 34th Canadian Minister of Justice (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1929", "text": "<PERSON>, Bulgarian-French pianist and educator (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French pianist and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian-French pianist and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian lawyer and politician (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio\" title=\"Plínio de Arruda Sampaio\"><PERSON><PERSON><PERSON><PERSON> de Arruda Sampaio</a>, Brazilian lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio\" title=\"Plínio de Arruda Sampaio\"><PERSON><PERSON><PERSON><PERSON> de Arruda Sampaio</a>, Brazilian lawyer and politician (d. 2014)", "links": [{"title": "Plínio de Arruda Sampaio", "link": "https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio"}]}, {"year": "1930", "text": "<PERSON>, English actress (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Brazilian footballer and manager (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Tel%C3%<PERSON>_Santana\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tel%C3%AA_Santana\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tel%C3%AA_Santana"}]}, {"year": "1934", "text": "<PERSON>, American football player (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2018)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese volleyball player and coach (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese volleyball player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese volleyball player and coach (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, English footballer and manager", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON>enemy\" title=\"<PERSON><PERSON> McMenemy\"><PERSON><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON>enemy\" title=\"<PERSON><PERSON> McMenemy\"><PERSON><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>my"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Italian automotive designer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Ercole_Spada\" title=\"Ercole Spada\"><PERSON><PERSON><PERSON></a>, Italian automotive designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ercole_Spada\" title=\"Ercole Spada\"><PERSON><PERSON><PERSON></a>, Italian automotive designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_Spada"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Welsh physician and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Welsh physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Welsh physician and academic", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1939", "text": "<PERSON>, Japanese author and poet (d. 2011)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author and poet (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author and poet (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Henmi"}]}, {"year": "1939", "text": "<PERSON>, Australian lawyer and politician, 25th Prime Minister of Australia", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1939", "text": "<PERSON>, American football player and photographer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English organist and conductor (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and conductor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter and producer (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON>, Northern Irish-British academic and politician, Secretary of State for Transport (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Northern Irish-British academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Northern Irish-British academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a> (d. 2019)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1940", "text": "<PERSON>, Canadian ice hockey player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, French historian and sociologist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rot\" title=\"<PERSON>\"><PERSON></a>, French historian and sociologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rot\" title=\"<PERSON>\"><PERSON></a>, French historian and sociologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rot"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American singer and actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American R&B singer-songwriter and keyboard player (d. 2025)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Brenton_Wood\" title=\"Brenton Wood\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and keyboard player (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brenton_Wood\" title=\"Brenton Wood\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and keyboard player (d. 2025)", "links": [{"title": "Brenton Wood", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wood"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak politician, 1st Prime Minister of Slovakia", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Me%C4%8Diar\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovakia\" title=\"Prime Minister of Slovakia\">Prime Minister of Slovakia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Me%C4%8Diar\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovakia\" title=\"Prime Minister of Slovakia\">Prime Minister of Slovakia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Me%C4%8Diar"}, {"title": "Prime Minister of Slovakia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovakia"}]}, {"year": "1942", "text": "<PERSON>, Belgian race car driver", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pilette\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pilette\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American director, screenwriter, and cinematographer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter, producer, and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Spanish race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Czech guitarist and songwriter (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Lubo%C5%A1_Andr%C5%A1t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech guitarist and songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lubo%C5%A1_Andr%C5%A1t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech guitarist and songwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lubo%C5%A1_Andr%C5%A1t"}]}, {"year": "1948", "text": "<PERSON>, German figure skater", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Thai businessman and politician, 23rd Prime Minister of Thailand", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Shin<PERSON>\" title=\"Thaksin Shinawatra\"><PERSON><PERSON><PERSON></a>, Thai businessman and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Thaksin Shinawa<PERSON>\"><PERSON><PERSON><PERSON></a>, Thai businessman and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, drummer, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queen_drummer)\" title=\"<PERSON> (Queen drummer)\"><PERSON></a>, English singer-songwriter, drummer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Queen_drummer)\" title=\"<PERSON> (Queen drummer)\"><PERSON></a>, English singer-songwriter, drummer, and producer", "links": [{"title": "<PERSON> (Queen drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Queen_drummer)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English journalist, screenwriter, and producer (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, screenwriter, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, screenwriter, and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English actress and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1950", "text": "<PERSON>, English lawyer and judge", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American race car driver (d. 1990)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian-American ice hockey player (d. 2011)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, English psychologist and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English psychologist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Dutch politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American professional basketball player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American tennis player and coach (d. 1994)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gerulaitis\" title=\"<PERSON><PERSON> Gerulaitis\"><PERSON><PERSON></a>, American tennis player and coach (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>itis\" title=\"<PERSON><PERSON> Gerulaitis\"><PERSON><PERSON></a>, American tennis player and coach (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>ulaitis"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Latvian footballer and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Pakistani businessman and politician, 11th President of Pakistan", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1955", "text": "<PERSON>, American serial killer (d. 1993)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English screenwriter and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American figure skater", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American wrestler", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English cricketer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Scottish politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Visitor\" title=\"Nana Visitor\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Visitor\" title=\"Nana Visitor\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Visitor"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American basketball player (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian-English pianist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American author and journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor and director", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English keyboard player and songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Caribbean-English comedian and actor (d. 2013)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-English comedian and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-English comedian and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American boxer and Catholic priest (d. 2014)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and Catholic priest (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and Catholic priest (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian curler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German painter and designer (d. 2020)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter and designer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter and designer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Belgian author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English organist and conductor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and conductor", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)"}]}, {"year": "1967", "text": "<PERSON>, American video game designer, founded Double Fine Productions", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, founded <a href=\"https://wikipedia.org/wiki/Double_Fine_Productions\" class=\"mw-redirect\" title=\"Double Fine Productions\">Double Fine Productions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, founded <a href=\"https://wikipedia.org/wiki/Double_Fine_Productions\" class=\"mw-redirect\" title=\"Double Fine Productions\">Double Fine Productions</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Double Fine Productions", "link": "https://wikipedia.org/wiki/Double_Fine_Productions"}]}, {"year": "1967", "text": "<PERSON>, English actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French actor and director", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French actor and director", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish biologist and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Scottish biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Scottish biologist and academic", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)"}]}, {"year": "1968", "text": "<PERSON>, English actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Welsh baroness and wheelchair racer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh baroness and wheelchair racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh baroness and wheelchair racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American television personality", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian director and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Iron & Wine, American singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Iron_%26_Wine\" title=\"Iron &amp; Wine\">Iron &amp; Wine</a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iron_%26_Wine\" title=\"Iron &amp; Wine\">Iron &amp; Wine</a>, American singer-songwriter", "links": [{"title": "Iron & Wine", "link": "https://wikipedia.org/wiki/Iron_%26_Wine"}]}, {"year": "1974", "text": "<PERSON><PERSON>, New Zealand rugby player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>euws"}]}, {"year": "1974", "text": "<PERSON>, English footballer and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, German sprinter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1975", "text": "<PERSON>, former Prime Minister of the United Kingdom", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1976", "text": "<PERSON>, Russian ice dancer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Benoit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Benoit\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Benoit"}]}, {"year": "1977", "text": "<PERSON>, Danish footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, German figure skater", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English pianist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, 40th Prime Minister of New Zealand", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1980", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Robert_Gallery\" title=\"Robert Gallery\">Robert <PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_Gallery\" title=\"Robert Gallery\"><PERSON></a>, American football player", "links": [{"title": "Robert <PERSON>", "link": "https://wikipedia.org/wiki/Robert_Gallery"}]}, {"year": "1981", "text": "<PERSON>, Australian actor, director, and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Israeli composer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Scottish fashion designer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American snowboarder", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Nigerian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Strong\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Strong\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Dutch field hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian kayaker", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Australian kayaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Australian kayaker", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Delonte_West\" title=\"Delonte West\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delonte_West\" title=\"Delonte West\"><PERSON><PERSON> West</a>, American basketball player", "links": [{"title": "Delonte West", "link": "https://wikipedia.org/wiki/Delonte_West"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Kyriakos_Ioannou\" title=\"Kyriakos Ioannou\"><PERSON><PERSON><PERSON><PERSON></a>, Cypriot high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kyriakos_Ioannou\" title=\"Kyriakos Ioannou\"><PERSON><PERSON><PERSON><PERSON> I<PERSON></a>, Cypriot high jumper", "links": [{"title": "Kyriakos <PERSON>", "link": "https://wikipedia.org/wiki/Kyriakos_Ioannou"}]}, {"year": "1984", "text": "<PERSON>, French rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sabri_Sar%C4%B1o%C4%9Flu\" title=\"Sabri Sarıoğlu\"><PERSON><PERSON> Sarıoğlu</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabri_Sar%C4%B1o%C4%9Flu\" title=\"Sabri Sarıoğlu\"><PERSON><PERSON>rı<PERSON>ğlu</a>, Turkish footballer", "links": [{"title": "Sabri Sarıoğlu", "link": "https://wikipedia.org/wiki/Sabri_Sar%C4%B1o%C4%9Flu"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Clichy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Clichy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%ABl_Clichy"}]}, {"year": "1985", "text": "<PERSON>, Canadian singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Argentinian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1986)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>\" title=\"Panagiot<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>\" title=\"Panagiot<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiot<PERSON>_<PERSON>ne"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Colombian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese announcer and news anchor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese announcer and news anchor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese announcer and news anchor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Filipino-Japanese actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino-Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino-Japanese actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barrie\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barrie\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese curler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Yoshida\"><PERSON><PERSON></a>, Japanese curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Yoshida\"><PERSON><PERSON></a>, Japanese curler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Fijian rugby player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari<PERSON>_<PERSON>e"}]}, {"year": "1993", "text": "<PERSON>-<PERSON><PERSON>, New Zealand rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>-Marine<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter, model, and actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, model, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Finnish tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Le<PERSON>\"><PERSON></a>, Finnish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ella_Leivo"}]}, {"year": "1996", "text": "<PERSON>, British Paralympic athlete", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Paralympic athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Paralympic athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2000", "text": "<PERSON><PERSON>, New Zealand actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "342", "text": "<PERSON> Jin, emperor of the Jin Dynasty (b. 321)", "html": "342 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Jin\" title=\"Emperor <PERSON> of Jin\"><PERSON> of Jin</a>, emperor of the Jin Dynasty (b. 321)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Jin\" title=\"Emperor <PERSON> of Jin\"><PERSON> of Jin</a>, emperor of the Jin Dynasty (b. 321)", "links": [{"title": "Emperor <PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "811", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine emperor", "html": "811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I\" title=\"<PERSON><PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a>, Byzantine emperor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I"}]}, {"year": "899", "text": "<PERSON>, Chinese warlord (b. 842)", "html": "899 - <a href=\"https://wikipedia.org/wiki/Li_<PERSON>zhi\" title=\"Li Hanzhi\"><PERSON></a>, Chinese warlord (b. 842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>zhi\" title=\"Li Hanzhi\"><PERSON></a>, Chinese warlord (b. 842)", "links": [{"title": "Li <PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>zhi"}]}, {"year": "943", "text": "<PERSON><PERSON><PERSON>, Japanese nobleman and poet (b. 890)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese nobleman and poet (b. 890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON>\" title=\"Prince <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese nobleman and poet (b. 890)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "990", "text": "<PERSON><PERSON>, Japanese statesman (b. 929)", "html": "990 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kaneie\" title=\"Fujiwara no Kaneie\"><PERSON><PERSON> no <PERSON></a>, Japanese statesman (b. 929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kaneie\" title=\"Fujiwara no Kaneie\"><PERSON><PERSON> no <PERSON></a>, Japanese statesman (b. 929)", "links": [{"title": "<PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/Fuji<PERSON>_<PERSON>_<PERSON>ie"}]}, {"year": "1380", "text": "<PERSON><PERSON><PERSON><PERSON>, emperor of Japan (b. 1322)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1322)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1322)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dmy%C5%8D"}]}, {"year": "1450", "text": "<PERSON><PERSON>, duchess of Warwick (b. 1424)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Warwick\" title=\"<PERSON><PERSON>, Duchess of Warwick\"><PERSON><PERSON></a>, duchess of Warwick (b. 1424)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Warwick\" title=\"<PERSON><PERSON>, Duchess of Warwick\"><PERSON><PERSON></a>, duchess of Warwick (b. 1424)", "links": [{"title": "<PERSON><PERSON>, Duchess of Warwick", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duchess_of_Warwick"}]}, {"year": "1471", "text": "<PERSON>, pope of the Catholic Church (b. 1417)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul II\"><PERSON></a>, pope of the Catholic Church (b. 1417)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON><PERSON>, Inca emperor abducted and murdered by <PERSON> (b. ca. 1500)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a>, Inca emperor abducted and murdered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. ca. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahualpa</a>, Inca emperor abducted and murdered by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. ca. 1500)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atahualpa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, French marshal (b. 1524)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_baron_de_Biron\" class=\"mw-redirect\" title=\"<PERSON>, baron de Biron\"><PERSON></a>, French marshal (b. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, baron de Biron\"><PERSON></a>, French marshal (b. 1524)", "links": [{"title": "<PERSON>, baron de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_baron_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, Spanish archbishop and sinologist (b. 1552)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish archbishop and sinologist (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish archbishop and sinologist (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1542)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1542)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1630", "text": "<PERSON>, duke of Savoy (b. 1562)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a>, duke of Savoy (b. 1562)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1659", "text": "<PERSON>, English criminal (b. 1584)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1584)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, 2nd Earl of Rochester, English poet and courtier (b. 1647)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Rochester\" title=\"<PERSON>, 2nd Earl of Rochester\"><PERSON>, 2nd Earl of Rochester</a>, English poet and courtier (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Rochester\" title=\"<PERSON>, 2nd Earl of Rochester\"><PERSON>, 2nd Earl of Rochester</a>, English poet and courtier (b. 1647)", "links": [{"title": "<PERSON>, 2nd Earl of Rochester", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Rochester"}]}, {"year": "1684", "text": "<PERSON>, Italian mathematician and philosopher (b. 1646)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Piscopia"}]}, {"year": "1693", "text": "<PERSON><PERSON><PERSON> of Denmark, queen of Sweden (b. 1656)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Eleonora_of_Denmark\" title=\"<PERSON><PERSON><PERSON> Eleonora of Denmark\"><PERSON><PERSON><PERSON> of Denmark</a>, queen of Sweden (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON><PERSON><PERSON>eon<PERSON> of Denmark\"><PERSON><PERSON><PERSON> of Denmark</a>, queen of Sweden (b. 1656)", "links": [{"title": "<PERSON><PERSON><PERSON> of Denmark", "link": "https://wikipedia.org/wiki/U<PERSON>rika_Eleonora_of_Denmark"}]}, {"year": "1712", "text": "<PERSON>, 1st Duke of Leeds, English politician, Lord High Treasurer (b. 1631)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leeds\" title=\"<PERSON>, 1st Duke of Leeds\"><PERSON>, 1st Duke of Leeds</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leeds\" title=\"<PERSON>, 1st Duke of Leeds\"><PERSON>, 1st Duke of Leeds</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1631)", "links": [{"title": "<PERSON>, 1st Duke of Leeds", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leeds"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1723", "text": "<PERSON>, 1st Duke of Ancaster and Kesteven, English politician, Chancellor of the Duchy of Lancaster (b. 1660)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_Kesteven\" title=\"<PERSON>, 1st Duke of Ancaster and Kesteven\"><PERSON>, 1st Duke of Ancaster and Kesteven</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_Kesteven\" title=\"<PERSON>, 1st Duke of Ancaster and Kesteven\"><PERSON>, 1st Duke of Ancaster and Kesteven</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1660)", "links": [{"title": "<PERSON>, 1st Duke of Ancaster and Kesteven", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ancaster_and_<PERSON><PERSON><PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1801", "text": "<PERSON>, archduke of Austria (b. 1756)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\"><PERSON></a>, archduke of Austria (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\"><PERSON></a>, archduke of Austria (b. 1756)", "links": [{"title": "Archduke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1863", "text": "<PERSON>, American general and politician, 7th Governor of Texas, and 6th Governor of Tennessee (b. 1793)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Sam_<PERSON>\" title=\"Sam <PERSON>\"><PERSON></a>, American general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a>, and 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a>, American general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a>, and 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Houston"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1867", "text": "<PERSON>, king of Greece (b. 1815)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Greece\" class=\"mw-redirect\" title=\"<PERSON>, King of Greece\"><PERSON></a>, king of Greece (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Greece\" class=\"mw-redirect\" title=\"<PERSON>, King of Greece\"><PERSON></a>, king of Greece (b. 1815)", "links": [{"title": "<PERSON>, King of Greece", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Greece"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, 22nd, 26th, and 27th President of the Dominican Republic (b. 1845)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_He<PERSON>\" title=\"<PERSON><PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON></a>, 22nd, 26th, and 27th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON></a>, 22nd, 26th, and 27th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aux"}, {"title": "President of the Dominican Republic", "link": "https://wikipedia.org/wiki/President_of_the_Dominican_Republic"}]}, {"year": "1915", "text": "<PERSON>, Scottish lexicographer and philologist (b. 1837)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, Scottish lexicographer and philologist (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, Scottish lexicographer and philologist (b. 1837)", "links": [{"title": "<PERSON> (lexicographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)"}]}, {"year": "1919", "text": "<PERSON>, English painter and illustrator (b. 1836)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian actor (b. 1848)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor (b. 1848)", "links": [{"title": "<PERSON> (Australian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_actor)"}]}, {"year": "1925", "text": "<PERSON>, Italian race car driver (b. 1888)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, German mathematician and philosopher (b. 1848)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ege\" title=\"<PERSON><PERSON><PERSON>ege\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ege\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American lawyer and politician, 41st United States Secretary of State (b. 1860)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician, 35th United States Secretary of War, son of <PERSON> (b. 1843)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abraham <PERSON>\"><PERSON></a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Greek historian and academic (b. 1849)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and academic (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek historian and academic (b. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German-American businessman, co-founded the Duesenberg Company (b. 1876)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Duesenberg\">Duesenberg Company</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Duesenberg\">Duesenberg Company</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Duesenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>berg"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American cartoonist, animator, producer, and screenwriter (b. 1871)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Winsor_McCay\" title=\"<PERSON>sor McCay\"><PERSON><PERSON></a>, American cartoonist, animator, producer, and screenwriter (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winsor_McCay\" title=\"<PERSON>sor McCay\"><PERSON><PERSON></a>, American cartoonist, animator, producer, and screenwriter (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Winsor_Mc<PERSON>ay"}]}, {"year": "1941", "text": "<PERSON>, French mathematician and academic (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian author and playwright (b. 1900)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author and playwright (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author and playwright (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1951", "text": "<PERSON>, Australian politician, 13th Premier of Western Australia (b. 1866)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1866)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1952", "text": "<PERSON>, Argentinian politician, 25th First Lady of Argentina (b. 1919)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Eva_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian politician, 25th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Argentina\" class=\"mw-redirect\" title=\"First Lady of Argentina\">First Lady of Argentina</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian politician, 25th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Argentina\" class=\"mw-redirect\" title=\"First Lady of Argentina\">First Lady of Argentina</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Per%C3%B3n"}, {"title": "First Lady of Argentina", "link": "https://wikipedia.org/wiki/First_Lady_of_Argentina"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Greek general and politician, 135th Prime Minister of Greece (b. 1883)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 135th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 135th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>last<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1957", "text": "<PERSON>, Authoritarian ruler of Guatemala (1954-1957)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Authoritarian ruler of Guatemala (1954-1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Authoritarian ruler of Guatemala (1954-1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, British art director and production designer (b. 1893)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British art director and production designer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British art director and production designer (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, 5th <PERSON>, English race car driver and politician (b. 1884)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th <PERSON>\"><PERSON>, 5th <PERSON></a>, English race car driver and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th <PERSON>\"><PERSON>, 5th <PERSON></a>, English race car driver and politician (b. 1884)", "links": [{"title": "<PERSON>, 5th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish lieutenant and painter (b. 1899)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll<PERSON>\" title=\"Ce<PERSON>ll<PERSON>\"><PERSON><PERSON></a>, Turkish lieutenant and painter (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll<PERSON>\" title=\"Ce<PERSON> Toll<PERSON>\"><PERSON><PERSON></a>, Turkish lieutenant and painter (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>llu"}]}, {"year": "1970", "text": "<PERSON>, Canadian lawyer and jurist, 11th Chief Justice of Canada (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1971", "text": "<PERSON>, American photographer and academic (b. 1923)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American mathematician and statistician, founded the Gallup Company (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and statistician, founded the <a href=\"https://wikipedia.org/wiki/Gallup_(company)\" class=\"mw-redirect\" title=\"Gallup (company)\">Gallup Company</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and statistician, founded the <a href=\"https://wikipedia.org/wiki/Gallup_(company)\" class=\"mw-redirect\" title=\"Gallup (company)\">Gallup Company</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gallup (company)", "link": "https://wikipedia.org/wiki/Gallup_(company)"}]}, {"year": "1984", "text": "<PERSON>, American serial killer (b. 1906)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American politician and diplomat, 11th United States Secretary of Commerce (b. 1891)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician and diplomat, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician and diplomat, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani philosopher, scholar, and academic (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani philosopher, scholar, and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani philosopher, scholar, and academic (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter (b. 1943)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American general (b. 1895)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American theologian and academic (b. 1901)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and academic (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and academic (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Brazilian-American guitarist and composer (b. 1917)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Lau<PERSON>do_Almeida\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-American guitarist and composer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Al<PERSON>da\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-American guitarist and composer (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laurindo_Almeida"}]}, {"year": "1995", "text": "<PERSON>, Canadian lawyer and politician (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American businessman and politician, 43rd Governor of Michigan (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1996", "text": "<PERSON>, American businessman and sports executive (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Max_Winter\" title=\"Max Winter\"><PERSON></a>, American businessman and sports executive (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_Winter\" title=\"Max Winter\"><PERSON></a>, American businessman and sports executive (b. 1903)", "links": [{"title": "Max Winter", "link": "https://wikipedia.org/wiki/Max_Winter"}]}, {"year": "1999", "text": "<PERSON>, American author and critic (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Greek general and politician, President of Greece (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Phaedon_Gizikis\" title=\"Phaedon Gizikis\">Phaedon Gizikis</a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phaedon_Gizikis\" title=\"Phaedon Gizikis\">Phaedon Gizikis</a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">President of Greece</a> (b. 1917)", "links": [{"title": "Phaedon <PERSON>", "link": "https://wikipedia.org/wiki/Phaedon_Gizikis"}, {"title": "List of heads of state of Greece", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece"}]}, {"year": "2000", "text": "<PERSON>, American mathematician and academic (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American colonel and pilot (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, German journalist and author (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American chemist, created Pop Rocks and Cool Whip (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, created <a href=\"https://wikipedia.org/wiki/Pop_Rocks\" title=\"Pop Rocks\">Pop Rocks</a> and <a href=\"https://wikipedia.org/wiki/Cool_Whip\" title=\"Cool Whip\">Cool Whip</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist, created <a href=\"https://wikipedia.org/wiki/Pop_Rocks\" title=\"Pop Rocks\">Pop Rocks</a> and <a href=\"https://wikipedia.org/wiki/Cool_Whip\" title=\"Cool Whip\">Cool Whip</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Pop Rocks", "link": "https://wikipedia.org/wiki/Pop_Rocks"}, {"title": "Cool Whip", "link": "https://wikipedia.org/wiki/Cool_Whip"}]}, {"year": "2005", "text": "<PERSON>, Russian-born American production designer and art director (b. 1908)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American production designer and art director (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-born American production designer and art director (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American economist and academic (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Swedish author, poet, and playwright (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author, poet, and playwright (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish author, poet, and playwright (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American basketball player and coach (b. 1950)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>sser\"><PERSON><PERSON></a>, American basketball player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>sser\"><PERSON><PERSON></a>, American basketball player and coach (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>sser"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, American dancer and choreographer (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-Singaporean politician (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>vakan<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Singaporean politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>va<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Singaporean politician (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vakant_Tiwari"}]}, {"year": "2011", "text": "<PERSON>, Colombian singer-songwriter and composer (b. 1955)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian singer-songwriter and composer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian singer-songwriter and composer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American jazz saxophonist, songwriter and composer (b. 1956)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz saxophonist, songwriter and composer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz saxophonist, songwriter and composer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American-Canadian football player and coach (b. 1948)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American-Canadian football player and coach (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American-Canadian football player and coach (b. 1948)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Japanese author and screenwriter (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_Komatsu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and screenwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sakyo_Ko<PERSON>su"}]}, {"year": "2011", "text": "<PERSON>, Australian painter and philanthropist (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and philanthropist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and philanthropist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American bassist and composer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American painter and educator (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian-Israeli lawyer and jurist (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli lawyer and jurist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli lawyer and jurist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American actress (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ontiveros"}]}, {"year": "2012", "text": "<PERSON>, American admiral and politician, 6th United States Secretary of Energy (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Energy\" title=\"United States Secretary of Energy\">United States Secretary of Energy</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Energy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Energy"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American mathematician and academic (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harley Flanders\"><PERSON></a>, American mathematician and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harley_<PERSON>\" title=\"Harley Flanders\"><PERSON></a>, American mathematician and academic (b. 1925)", "links": [{"title": "Harley <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Flanders"}]}, {"year": "2013", "text": "<PERSON>, South Korean philosopher and activist (b. 1967)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" title=\"<PERSON> J<PERSON>-gi\"><PERSON></a>, South Korean philosopher and activist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi\" title=\"<PERSON> J<PERSON>-gi\"><PERSON></a>, South Korean philosopher and activist (b. 1967)", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-gi"}]}, {"year": "2013", "text": "<PERSON>, American businessman and philanthropist (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Ukrainian businessman and politician (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian businessman and politician (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian businessman and politician (b. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American admiral (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English architect, founded MJP Architects (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, founded <a href=\"https://wikipedia.org/wiki/MJP_Architects\" title=\"MJP Architects\">MJP Architects</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, founded <a href=\"https://wikipedia.org/wiki/MJP_Architects\" title=\"MJP Architects\">MJP Architects</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MJP Architects", "link": "https://wikipedia.org/wiki/MJP_Architects"}]}, {"year": "2014", "text": "<PERSON>, Russian anthropologist and author (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian anthropologist and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian anthropologist and author (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Belgian director, producer, and screenwriter (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian director, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian director, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Mines (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Mines_(India)\" title=\"Ministry of Mines (India)\">Indian Minister of Mines</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Mines_(India)\" title=\"Ministry of Mines (India)\">Indian Minister of Mines</a> (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Krishna_Handique"}, {"title": "Ministry of Mines (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Mines_(India)"}]}, {"year": "2015", "text": "<PERSON>, Canadian banker and politician, 10th Canadian Minister of Communications (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian banker and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Communications_(Canada)\" title=\"Minister of Communications (Canada)\">Canadian Minister of Communications</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian banker and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_of_Communications_(Canada)\" title=\"Minister of Communications (Canada)\">Canadian Minister of Communications</a> (b. 1926)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Minister of Communications (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Communications_(Canada)"}]}, {"year": "2015", "text": "<PERSON>, Jr., Canadian ice hockey player (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Canadian ice hockey player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Canadian ice hockey player (b. 1922)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2015", "text": "<PERSON>, American police officer and author (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ann_Rule\" title=\"Ann Rule\"><PERSON></a>, American police officer and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ann_Rule\" title=\"Ann Rule\"><PERSON></a>, American police officer and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rule"}]}, {"year": "2017", "text": "<PERSON>, American voice actress (b. 1917)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/June_Foray\" title=\"June Foray\">June Foray</a>, American voice actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Foray\" title=\"June Foray\">June Foray</a>, American voice actress (b. 1917)", "links": [{"title": "June Foray", "link": "https://wikipedia.org/wiki/June_Foray"}]}, {"year": "2017", "text": "<PERSON>, American voice artist and comedic actress (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice artist and comedic actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice artist and comedic actress (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American criminal (b. 1973)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(murderer)\" title=\"<PERSON> (murderer)\"><PERSON></a>, American criminal (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(murderer)\" title=\"<PERSON> (murderer)\"><PERSON></a>, American criminal (b. 1973)", "links": [{"title": "<PERSON> (murderer)", "link": "https://wikipedia.org/wiki/<PERSON>_(murderer)"}]}, {"year": "2016", "text": "<PERSON>, American philosopher and mathematician (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and mathematician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and mathematician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Kosovo Albanian politician and writer (b. 1936)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kosovo_Albanian\" class=\"mw-redirect\" title=\"Kosovo Albanian\">Kosovo Albanian</a> politician and writer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kosovo_Albanian\" class=\"mw-redirect\" title=\"Kosovo Albanian\">Kosovo Albanian</a> politician and writer (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adem_Dema%C3%A7i"}, {"title": "Kosovo Albanian", "link": "https://wikipedia.org/wiki/Kosovo_Albanian"}]}, {"year": "2018", "text": "<PERSON>, American basketball player (b. 1931)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1931)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American voice actress (b. 1944)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Cuban Roman Catholic prelate (b. 1936)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_y_<PERSON>amino\" title=\"<PERSON> Alami<PERSON>\"><PERSON> y <PERSON></a>, Cuban Roman Catholic prelate (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> y <PERSON>ami<PERSON>\"><PERSON> y <PERSON></a>, Cuban Roman Catholic prelate (b. 1936)", "links": [{"title": "<PERSON> y Alamino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American actress (b. 1916)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American musician (b. 1975)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Irish singer and musician (b. 1966)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer and musician (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer and musician (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor"}]}]}}