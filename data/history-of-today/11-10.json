{"date": "November 10", "url": "https://wikipedia.org/wiki/November_10", "data": {"Events": [{"year": "474", "text": "Emperor <PERSON> dies after a reign of ten months. He is succeeded by his father <PERSON><PERSON>, who becomes sole ruler of the Byzantine Empire.", "html": "474 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a> dies after a reign of ten months. He is succeeded by his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, who becomes sole ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a> dies after a reign of ten months. He is succeeded by his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, who becomes sole ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "937", "text": "Ten Kingdoms: <PERSON> usurps the throne and deposes Emperor <PERSON>. The Wu State is replaced by <PERSON> (now called \"<PERSON>\"), who becomes the first ruler of Southern Tang.", "html": "937 - <a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Bian\" title=\"Li Bian\"><PERSON></a> usurps the throne and deposes Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pu\"><PERSON></a>. The <a href=\"https://wikipedia.org/wiki/Wu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"Wu (Ten Kingdoms)\">Wu State</a> is replaced by <PERSON> (now called \"<PERSON>\"), who becomes the first ruler of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>: <a href=\"https://wikipedia.org/wiki/Li_Bian\" title=\"Li Bian\"><PERSON></a> usurps the throne and deposes Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pu\"><PERSON></a>. The <a href=\"https://wikipedia.org/wiki/Wu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"Wu (Ten Kingdoms)\">Wu State</a> is replaced by <PERSON> (now called \"<PERSON>\"), who becomes the first ruler of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a>.", "links": [{"title": "Ten Kingdoms", "link": "https://wikipedia.org/wiki/Ten_Kingdoms"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Bian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wu (Ten Kingdoms)", "link": "https://wikipedia.org/wiki/Wu_(Ten_Kingdoms)"}, {"title": "Southern Tang", "link": "https://wikipedia.org/wiki/Southern_Tang"}]}, {"year": "1202", "text": "Fourth Crusade: Despite letters from <PERSON> forbidding it and threatening excommunication, Catholic crusaders begin a siege of Zara (now Zadar, Croatia).", "html": "1202 - <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a>: Despite letters from <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope <PERSON> III</a> forbidding it and threatening <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a>, Catholic crusaders <a href=\"https://wikipedia.org/wiki/Siege_of_Zara\" title=\"Siege of Zara\">begin a siege</a> of Zara (now <a href=\"https://wikipedia.org/wiki/Zadar\" title=\"Zadar\">Zadar, Croatia</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a>: Despite letters from <a href=\"https://wikipedia.org/wiki/Pope_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> forbidding it and threatening <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a>, Catholic crusaders <a href=\"https://wikipedia.org/wiki/Siege_of_Zara\" title=\"Siege of Zara\">begin a siege</a> of Zara (now <a href=\"https://wikipedia.org/wiki/Zadar\" title=\"Zadar\">Zadar, Croatia</a>).", "links": [{"title": "Fourth Crusade", "link": "https://wikipedia.org/wiki/Fourth_Crusade"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "Siege of Zara", "link": "https://wikipedia.org/wiki/Siege_of_Zara"}, {"title": "Zadar", "link": "https://wikipedia.org/wiki/Zadar"}]}, {"year": "1293", "text": "<PERSON><PERSON> is crowned as the first monarch of Majapahit kingdom of Java, taking the throne name <PERSON><PERSON><PERSON><PERSON>.", "html": "1293 - <a href=\"https://wikipedia.org/wiki/Raden_Wijaya\" title=\"Raden Wijaya\"><PERSON><PERSON></a> is crowned as the first monarch of <a href=\"https://wikipedia.org/wiki/Majapahit\" title=\"Majapahit\">Majapahit</a> kingdom of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, taking the throne name <PERSON><PERSON><PERSON><PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raden_Wijaya\" title=\"Raden Wijaya\"><PERSON><PERSON></a> is crowned as the first monarch of <a href=\"https://wikipedia.org/wiki/Majapahit\" title=\"Majapahit\">Majapahit</a> kingdom of <a href=\"https://wikipedia.org/wiki/Java\" title=\"Java\">Java</a>, taking the throne name <PERSON><PERSON><PERSON><PERSON>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>den_Wijaya"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Majapa<PERSON>"}, {"title": "Java", "link": "https://wikipedia.org/wiki/Java"}]}, {"year": "1444", "text": "Battle of Varna: The crusading forces of King <PERSON><PERSON><PERSON><PERSON> of Poland (aka Ulaszlo I of Hungary and <PERSON><PERSON><PERSON><PERSON> of Varna) are defeated by the Turks under Sultan <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> is killed.", "html": "1444 - <a href=\"https://wikipedia.org/wiki/Battle_of_Varna\" title=\"Battle of Varna\">Battle of Varna</a>: The crusading forces of King <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Poland\"><PERSON><PERSON><PERSON><PERSON> of Poland</a> (aka <i>Ulaszlo I of Hungary</i> and <i><PERSON><PERSON><PERSON><PERSON> of Varna</i>) are defeated by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turks</a> under <PERSON> <a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\">Murad II</a> and <PERSON><PERSON><PERSON><PERSON> is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Varna\" title=\"Battle of Varna\">Battle of Varna</a>: The crusading forces of King <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> III of Poland\"><PERSON><PERSON><PERSON><PERSON> of Poland</a> (aka <i>Ulaszlo I of Hungary</i> and <i><PERSON><PERSON><PERSON><PERSON> of Varna</i>) are defeated by the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turks</a> under <PERSON> <a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\">Murad II</a> and <PERSON><PERSON><PERSON><PERSON> is killed.", "links": [{"title": "Battle of Varna", "link": "https://wikipedia.org/wiki/Battle_of_Varna"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Poland", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Murad II", "link": "https://wikipedia.org/wiki/Murad_II"}]}, {"year": "1599", "text": "<PERSON><PERSON> Bloodbath: Fourteen noblemen who opposed Duke <PERSON> are decapitated in the Old Great Square of Turku (Swedish: Åbo) for their involvement in the War against <PERSON><PERSON><PERSON>.", "html": "1599 - <a href=\"https://wikipedia.org/wiki/%C3%85bo_Bloodbath\" title=\"Åbo Bloodbath\">Åbo Bloodbath</a>: Fourteen noblemen who opposed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\">Duke <PERSON></a> are decapitated in the <a href=\"https://wikipedia.org/wiki/Old_Great_Square_(Turku)\" title=\"Old Great Square (Turku)\">Old Great Square</a> of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON></i>) for their involvement in the <a href=\"https://wikipedia.org/wiki/War_against_Sigismund\" title=\"War against Sigismund\">War against Sigismund</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85bo_Bloodbath\" title=\"Åbo Bloodbath\">Åbo Bloodbath</a>: Fourteen noblemen who opposed <a href=\"https://wikipedia.org/wiki/<PERSON>_IX_of_Sweden\" title=\"Charles IX of Sweden\">Duke <PERSON></a> are decapitated in the <a href=\"https://wikipedia.org/wiki/Old_Great_Square_(Turku)\" title=\"Old Great Square (Turku)\">Old Great Square</a> of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON></i>) for their involvement in the <a href=\"https://wikipedia.org/wiki/War_against_Sigismund\" title=\"War against Sigismund\">War against Sigismund</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%85bo_Bloodbath"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_IX_of_Sweden"}, {"title": "Old Great Square (Turku)", "link": "https://wikipedia.org/wiki/Old_Great_Square_(Turku)"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}, {"title": "War against Si<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/War_against_<PERSON><PERSON><PERSON>"}]}, {"year": "1659", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> kills <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> in the battle popularly known as Battle of Pratapgarh.", "html": "1659 - <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\">Chatt<PERSON><PERSON>ji <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\"><PERSON><PERSON> King</a> kills <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Adilshahi\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ha<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in the battle popularly known as <a href=\"https://wikipedia.org/wiki/Battle_of_Pratapgarh\" title=\"Battle of Pratapgarh\">Battle of Pratapgarh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\">Chattrapati <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha King</a> kills <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Adilshahi\" class=\"mw-redirect\" title=\"Adilsha<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in the battle popularly known as <a href=\"https://wikipedia.org/wiki/Battle_of_Pratapgarh\" title=\"Battle of Pratapgarh\">Battle of Pratapgarh</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "<PERSON><PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ilshahi"}, {"title": "Battle of Pratapgarh", "link": "https://wikipedia.org/wiki/Battle_of_Pratapgarh"}]}, {"year": "1674", "text": "Third Anglo-Dutch War: As provided in the Treaty of Westminster, Netherlands cedes New Netherland to England.", "html": "1674 - <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>: As provided in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)\" title=\"Treaty of Westminster (1674)\">Treaty of Westminster</a>, Netherlands cedes <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> to <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>: As provided in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)\" title=\"Treaty of Westminster (1674)\">Treaty of Westminster</a>, Netherlands cedes <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> to <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "links": [{"title": "Third Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Third_Anglo-Dutch_War"}, {"title": "Treaty of Westminster (1674)", "link": "https://wikipedia.org/wiki/Treaty_of_Westminster_(1674)"}, {"title": "New Netherland", "link": "https://wikipedia.org/wiki/New_Netherland"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}, {"year": "1702", "text": "English colonists under the command of <PERSON> besiege Spanish St. Augustine during Queen Anne's War.", "html": "1702 - English colonists under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Siege_of_St._Augustine_(1702)\" title=\"Siege of St. Augustine (1702)\">besiege</a> Spanish <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> during <a href=\"https://wikipedia.org/wiki/Queen_Anne%27s_War\" title=\"Queen <PERSON>'s War\">Queen <PERSON>'s War</a>.", "no_year_html": "English colonists under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Siege_of_St._Augustine_(1702)\" title=\"Siege of St. Augustine (1702)\">besiege</a> Spanish <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> during <a href=\"https://wikipedia.org/wiki/Queen_Anne%27s_War\" title=\"Queen Anne's War\">Queen <PERSON>'s War</a>.", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Siege of St. Augustine (1702)", "link": "https://wikipedia.org/wiki/Siege_of_St._Augustine_(1702)"}, {"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}, {"title": "Queen Anne's War", "link": "https://wikipedia.org/wiki/Queen_<PERSON>%27s_War"}]}, {"year": "1766", "text": "The last colonial governor of New Jersey, <PERSON>, signs the charter of Queen's College (later renamed Rutgers University).", "html": "1766 - The last colonial governor of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, signs the charter of Queen's College (later renamed <a href=\"https://wikipedia.org/wiki/Rutgers_University\" title=\"Rutgers University\">Rutgers University</a>).", "no_year_html": "The last colonial governor of <a href=\"https://wikipedia.org/wiki/New_Jersey\" title=\"New Jersey\">New Jersey</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, signs the charter of Queen's College (later renamed <a href=\"https://wikipedia.org/wiki/Rutgers_University\" title=\"Rutgers University\">Rutgers University</a>).", "links": [{"title": "New Jersey", "link": "https://wikipedia.org/wiki/New_Jersey"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rutgers University", "link": "https://wikipedia.org/wiki/Rutgers_University"}]}, {"year": "1775", "text": "The United States Marine Corps is founded at Tun Tavern in Philadelphia by <PERSON>.", "html": "1775 - The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> is <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps_birthday_ball\" class=\"mw-redirect\" title=\"United States Marine Corps birthday ball\">founded</a> at <a href=\"https://wikipedia.org/wiki/Tun_Tavern\" title=\"Tun Tavern\">Tun Tavern</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> is <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps_birthday_ball\" class=\"mw-redirect\" title=\"United States Marine Corps birthday ball\">founded</a> at <a href=\"https://wikipedia.org/wiki/Tun_Tavern\" title=\"Tun Tavern\">Tun Tavern</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "United States Marine Corps birthday ball", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps_birthday_ball"}, {"title": "Tun Tavern", "link": "https://wikipedia.org/wiki/Tun_Tavern"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "A Goddess of Reason is proclaimed by the French Convention at the suggestion of <PERSON>.", "html": "1793 - A <a href=\"https://wikipedia.org/wiki/Goddess_of_Reason\" class=\"mw-redirect\" title=\"Goddess of Reason\">Goddess of Reason</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/French_Convention\" class=\"mw-redirect\" title=\"French Convention\">French Convention</a> at the suggestion of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Goddess_of_Reason\" class=\"mw-redirect\" title=\"Goddess of Reason\">Goddess of Reason</a> is proclaimed by the <a href=\"https://wikipedia.org/wiki/French_Convention\" class=\"mw-redirect\" title=\"French Convention\">French Convention</a> at the suggestion of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Goddess of Reason", "link": "https://wikipedia.org/wiki/Goddess_of_Reason"}, {"title": "French Convention", "link": "https://wikipedia.org/wiki/French_Convention"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "Cry of Independence by <PERSON><PERSON><PERSON> at La Villa de Los Santos, Panama setting into motion a revolt which led to Panama's independence from Spain and to it immediately becoming part of Colombia.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/The_Gesture_of_Rufina_Alfaro\" class=\"mw-redirect\" title=\"The Gesture of Rufina Alfaro\">Cry of Independence</a> by <PERSON><PERSON><PERSON> at <a href=\"https://wikipedia.org/wiki/Los_Santos_District\" title=\"Los Santos District\">La Villa de Los Santos</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> setting into motion a revolt which led to <a href=\"https://wikipedia.org/wiki/History_of_Panama#Independence\" title=\"History of Panama\">Panama's independence from Spain</a> and to it immediately becoming part of <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Colombia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Gesture_of_Rufina_Alfaro\" class=\"mw-redirect\" title=\"The Gesture of Rufina Alfaro\">Cry of Independence</a> by <PERSON><PERSON><PERSON> at <a href=\"https://wikipedia.org/wiki/Los_Santos_District\" title=\"Los Santos District\">La Villa de Los Santos</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> setting into motion a revolt which led to <a href=\"https://wikipedia.org/wiki/History_of_Panama#Independence\" title=\"History of Panama\">Panama's independence from Spain</a> and to it immediately becoming part of <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Colombia</a>.", "links": [{"title": "The Gesture of R<PERSON>ina <PERSON>", "link": "https://wikipedia.org/wiki/The_Gesture_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Los Santos District", "link": "https://wikipedia.org/wiki/Los_Santos_District"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "History of Panama", "link": "https://wikipedia.org/wiki/History_of_Panama#Independence"}, {"title": "Gran Colombia", "link": "https://wikipedia.org/wiki/Gran_Colombia"}]}, {"year": "1847", "text": "The passenger ship <PERSON> is wrecked in thick fog off the southern coast of Ireland, killing 92 of the 110 on board. The disaster results in the construction of the Fastnet Rock lighthouse.", "html": "1847 - The passenger ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> is wrecked in thick fog off the southern coast of Ireland, killing 92 of the 110 on board. The disaster results in the construction of the <a href=\"https://wikipedia.org/wiki/Fastnet_Rock\" class=\"mw-redirect\" title=\"Fastnet Rock\">Fastnet Rock lighthouse</a>.", "no_year_html": "The passenger ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> is wrecked in thick fog off the southern coast of Ireland, killing 92 of the 110 on board. The disaster results in the construction of the <a href=\"https://wikipedia.org/wiki/Fastnet_Rock\" class=\"mw-redirect\" title=\"Fastnet Rock\">Fastnet Rock lighthouse</a>.", "links": [{"title": "<PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(ship)"}, {"title": "Fastnet Rock", "link": "https://wikipedia.org/wiki/Fastnet_Rock"}]}, {"year": "1865", "text": "Major <PERSON>, the superintendent of a prison camp in Andersonville, Georgia, is hanged, becoming one of only three American Civil War soldiers executed for war crimes.", "html": "1865 - Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the superintendent of <a href=\"https://wikipedia.org/wiki/Andersonville,_Georgia#History\" title=\"Andersonville, Georgia\">a prison camp in Andersonville, Georgia</a>, is <a href=\"https://wikipedia.org/wiki/Hanging\" title=\"Hanging\">hanged</a>, becoming one of only three <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> soldiers <a href=\"https://wikipedia.org/wiki/Execution_(legal)\" class=\"mw-redirect\" title=\"Execution (legal)\">executed</a> for <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>.", "no_year_html": "Major <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the superintendent of <a href=\"https://wikipedia.org/wiki/Andersonville,_Georgia#History\" title=\"Andersonville, Georgia\">a prison camp in Andersonville, Georgia</a>, is <a href=\"https://wikipedia.org/wiki/Hanging\" title=\"Hanging\">hanged</a>, becoming one of only three <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> soldiers <a href=\"https://wikipedia.org/wiki/Execution_(legal)\" class=\"mw-redirect\" title=\"Execution (legal)\">executed</a> for <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "Andersonville, Georgia", "link": "https://wikipedia.org/wiki/Andersonville,_Georgia#History"}, {"title": "Hanging", "link": "https://wikipedia.org/wiki/Hanging"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Execution (legal)", "link": "https://wikipedia.org/wiki/Execution_(legal)"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}]}, {"year": "1871", "text": "<PERSON> locates missing explorer and missionary, <PERSON> in Ujiji, near Lake Tanganyika, famously greeting him with the words, \"Dr. <PERSON>, I presume?\"", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> locates missing explorer and missionary, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Ujiji\" title=\"Ujiji\">Ujiji</a>, near <a href=\"https://wikipedia.org/wiki/Lake_Tanganyika\" title=\"Lake Tanganyika\">Lake Tanganyika</a>, famously greeting him with the words, \"<PERSON><PERSON>, I presume?\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> locates missing explorer and missionary, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Ujiji\" title=\"Ujiji\">Ujiji</a>, near <a href=\"https://wikipedia.org/wiki/Lake_Tanganyika\" title=\"Lake Tanganyika\">Lake Tanganyika</a>, famously greeting him with the words, \"<PERSON><PERSON>, I presume?\"", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>jiji"}, {"title": "Lake Tanganyika", "link": "https://wikipedia.org/wiki/Lake_Tanganyika"}]}, {"year": "1898", "text": "Beginning of the Wilmington insurrection of 1898, the only instance of a municipal government being overthrown in United States history.", "html": "1898 - Beginning of the <a href=\"https://wikipedia.org/wiki/Wilmington_insurrection_of_1898\" class=\"mw-redirect\" title=\"Wilmington insurrection of 1898\">Wilmington insurrection of 1898</a>, the only instance of a municipal government being overthrown in United States history.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/Wilmington_insurrection_of_1898\" class=\"mw-redirect\" title=\"Wilmington insurrection of 1898\">Wilmington insurrection of 1898</a>, the only instance of a municipal government being overthrown in United States history.", "links": [{"title": "Wilmington insurrection of 1898", "link": "https://wikipedia.org/wiki/Wilmington_insurrection_of_1898"}]}, {"year": "1910", "text": "The date of <PERSON>' opening of the San Diego Army and Navy Academy, although the official founding date is November 23, 1910.", "html": "1910 - The date of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>' opening of the <a href=\"https://wikipedia.org/wiki/San_Diego_Army_and_Navy_Academy\" class=\"mw-redirect\" title=\"San Diego Army and Navy Academy\">San Diego Army and Navy Academy</a>, although the official founding date is November 23, 1910.", "no_year_html": "The date of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>' opening of the <a href=\"https://wikipedia.org/wiki/San_Diego_Army_and_Navy_Academy\" class=\"mw-redirect\" title=\"San Diego Army and Navy Academy\">San Diego Army and Navy Academy</a>, although the official founding date is November 23, 1910.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "San Diego Army and Navy Academy", "link": "https://wikipedia.org/wiki/San_Diego_Army_and_Navy_Academy"}]}, {"year": "1918", "text": "The Western Union Cable Office in North Sydney, Nova Scotia, receives a top-secret coded message from Europe (that would be sent to Ottawa and Washington, D.C.) that said on November 11, 1918, all fighting would cease on land, sea and in the air.", "html": "1918 - The Western Union Cable Office in <a href=\"https://wikipedia.org/wiki/North_Sydney,_Nova_Scotia\" title=\"North Sydney, Nova Scotia\">North Sydney, Nova Scotia</a>, receives a top-secret coded message from Europe (that would be sent to <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> and Washington, D.C.) that said on <a href=\"https://wikipedia.org/wiki/November_11,_1918\" class=\"mw-redirect\" title=\"November 11, 1918\">November 11, 1918</a>, all fighting would cease on land, sea and in the air.", "no_year_html": "The Western Union Cable Office in <a href=\"https://wikipedia.org/wiki/North_Sydney,_Nova_Scotia\" title=\"North Sydney, Nova Scotia\">North Sydney, Nova Scotia</a>, receives a top-secret coded message from Europe (that would be sent to <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> and Washington, D.C.) that said on <a href=\"https://wikipedia.org/wiki/November_11,_1918\" class=\"mw-redirect\" title=\"November 11, 1918\">November 11, 1918</a>, all fighting would cease on land, sea and in the air.", "links": [{"title": "North Sydney, Nova Scotia", "link": "https://wikipedia.org/wiki/North_Sydney,_Nova_Scotia"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "November 11, 1918", "link": "https://wikipedia.org/wiki/November_11,_1918"}]}, {"year": "1939", "text": "Finnish author <PERSON><PERSON> <PERSON><PERSON> is awarded the Nobel Prize in Literature.", "html": "1939 - Finnish author <a href=\"https://wikipedia.org/wiki/Frans_E<PERSON>il_Sillanp%C3%A4%C3%A4\" title=\"Frans Eemil Sillanpää\"><PERSON><PERSON> <PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>.", "no_year_html": "Finnish author <a href=\"https://wikipedia.org/wiki/Frans_Eemil_Sillanp%C3%A4%C3%A4\" title=\"Frans Eemil Sillanpää\"><PERSON><PERSON> <PERSON><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize in Literature</a>.", "links": [{"title": "Frans E<PERSON>il <PERSON>", "link": "https://wikipedia.org/wiki/Frans_Eemil_Sillanp%C3%A4%C3%A4"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1940", "text": "The 1940 Vrancea earthquake strikes Romania killing an estimated 1,000 and injuring approximately 4,000 more.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/1940_Vrancea_earthquake\" title=\"1940 Vrancea earthquake\">1940 Vrancea earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> killing an estimated 1,000 and injuring approximately 4,000 more.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1940_Vrancea_earthquake\" title=\"1940 Vrancea earthquake\">1940 Vrancea earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a> killing an estimated 1,000 and injuring approximately 4,000 more.", "links": [{"title": "1940 Vrancea earthquake", "link": "https://wikipedia.org/wiki/1940_Vrancea_earthquake"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1942", "text": "World War II: Germany invades Vichy France following French Admiral <PERSON>'s agreement to an armistice with the Allies in North Africa.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Anton\" title=\"<PERSON> Anton\">invades Vichy France</a> following French Admiral <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s agreement to an armistice with the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies</a> in North Africa.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Anton\" title=\"<PERSON> Anton\">invades Vichy France</a> following French Admiral <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s agreement to an armistice with the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allies</a> in North Africa.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>lan"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1944", "text": "The ammunition ship USS Mount Hood explodes at Seeadler Harbour, Manus, Admiralty Islands, killing at least 432 and wounding 371.", "html": "1944 - The ammunition ship <a href=\"https://wikipedia.org/wiki/USS_Mount_Hood_(AE-11)\" title=\"USS Mount Hood (AE-11)\">USS <i>Mount Hood</i></a> explodes at Seeadler Harbour, <a href=\"https://wikipedia.org/wiki/Manus_Island\" title=\"Manus Island\">Manus</a>, <a href=\"https://wikipedia.org/wiki/Admiralty_Islands\" title=\"Admiralty Islands\">Admiralty Islands</a>, killing at least 432 and wounding 371.", "no_year_html": "The ammunition ship <a href=\"https://wikipedia.org/wiki/USS_Mount_Hood_(AE-11)\" title=\"USS Mount Hood (AE-11)\">USS <i>Mount Hood</i></a> explodes at Seeadler Harbour, <a href=\"https://wikipedia.org/wiki/Manus_Island\" title=\"Manus Island\">Manus</a>, <a href=\"https://wikipedia.org/wiki/Admiralty_Islands\" title=\"Admiralty Islands\">Admiralty Islands</a>, killing at least 432 and wounding 371.", "links": [{"title": "USS Mount Hood (AE-11)", "link": "https://wikipedia.org/wiki/USS_Mount_Hood_(AE-11)"}, {"title": "Manus Island", "link": "https://wikipedia.org/wiki/Manus_Island"}, {"title": "Admiralty Islands", "link": "https://wikipedia.org/wiki/Admiralty_Islands"}]}, {"year": "1945", "text": "Heavy fighting in Surabaya between Indonesian nationalists and returning colonialists after World War II, today celebrated as Heroes' Day (Hari <PERSON>).", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Battle_of_Surabaya\" title=\"Battle of Surabaya\">Heavy fighting</a> in <a href=\"https://wikipedia.org/wiki/Surabaya\" title=\"Surabaya\">Surabaya</a> between <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> nationalists and returning colonialists after World War II, today celebrated as <a href=\"https://wikipedia.org/wiki/Heroes%27_Day#Indonesia\" title=\"Heroes' Day\">Heroes' Day (Hari <PERSON>)</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Surabaya\" title=\"Battle of Surabaya\">Heavy fighting</a> in <a href=\"https://wikipedia.org/wiki/Surabaya\" title=\"Surabaya\">Surabaya</a> between <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> nationalists and returning colonialists after World War II, today celebrated as <a href=\"https://wikipedia.org/wiki/Heroes%27_Day#Indonesia\" title=\"Heroes' Day\">Heroes' Day (Hari Pahl<PERSON>n)</a>.", "links": [{"title": "Battle of Surabaya", "link": "https://wikipedia.org/wiki/Battle_of_Surabaya"}, {"title": "Surabaya", "link": "https://wikipedia.org/wiki/Surabaya"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Heroes' Day", "link": "https://wikipedia.org/wiki/Heroes%27_Day#Indonesia"}]}, {"year": "1946", "text": "A magnitude 6.9 earthquake in the Peruvian Andes mountains kills at least 1,400 people.", "html": "1946 - A <a href=\"https://wikipedia.org/wiki/1946_Ancash_earthquake\" title=\"1946 Ancash earthquake\">magnitude 6.9 earthquake</a> in the Peruvian Andes mountains kills at least 1,400 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1946_Ancash_earthquake\" title=\"1946 Ancash earthquake\">magnitude 6.9 earthquake</a> in the Peruvian Andes mountains kills at least 1,400 people.", "links": [{"title": "1946 Ancash earthquake", "link": "https://wikipedia.org/wiki/1946_Ancash_earthquake"}]}, {"year": "1951", "text": "With the rollout of the North American Numbering Plan, direct-dial coast-to-coast telephone service begins in the United States.", "html": "1951 - With the rollout of the <a href=\"https://wikipedia.org/wiki/North_American_Numbering_Plan\" title=\"North American Numbering Plan\">North American Numbering Plan</a>, direct-dial coast-to-coast <a href=\"https://wikipedia.org/wiki/Telephone\" title=\"Telephone\">telephone</a> service begins in the United States.", "no_year_html": "With the rollout of the <a href=\"https://wikipedia.org/wiki/North_American_Numbering_Plan\" title=\"North American Numbering Plan\">North American Numbering Plan</a>, direct-dial coast-to-coast <a href=\"https://wikipedia.org/wiki/Telephone\" title=\"Telephone\">telephone</a> service begins in the United States.", "links": [{"title": "North American Numbering Plan", "link": "https://wikipedia.org/wiki/North_American_Numbering_Plan"}, {"title": "Telephone", "link": "https://wikipedia.org/wiki/Telephone"}]}, {"year": "1954", "text": "U.S. President <PERSON> dedicates the USMC War Memorial (Iwo Jima memorial) in Arlington Ridge Park in Arlington County, Virginia.", "html": "1954 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Marine_Corps_War_Memorial\" title=\"Marine Corps War Memorial\">USMC War Memorial</a> (Iwo Jima memorial) in Arlington Ridge Park in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates the <a href=\"https://wikipedia.org/wiki/Marine_Corps_War_Memorial\" title=\"Marine Corps War Memorial\">USMC War Memorial</a> (Iwo Jima memorial) in Arlington Ridge Park in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Marine Corps War Memorial", "link": "https://wikipedia.org/wiki/Marine_Corps_War_Memorial"}, {"title": "Arlington County, Virginia", "link": "https://wikipedia.org/wiki/Arlington_County,_Virginia"}]}, {"year": "1958", "text": "The Hope Diamond is donated to the Smithsonian Institution by New York diamond merchant <PERSON>.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Hope_Diamond\" title=\"Hope Diamond\">Hope Diamond</a> is donated to the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> by New York diamond merchant <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hope_Diamond\" title=\"Hope Diamond\">Hope Diamond</a> is donated to the <a href=\"https://wikipedia.org/wiki/Smithsonian_Institution\" title=\"Smithsonian Institution\">Smithsonian Institution</a> by New York diamond merchant <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Hope Diamond", "link": "https://wikipedia.org/wiki/Hope_Diamond"}, {"title": "Smithsonian Institution", "link": "https://wikipedia.org/wiki/Smithsonian_Institution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "The Nauru Independence Act 1967 passed the Parliament of Australia, giving independence to the UN Trust Territory of Nauru with effect from 31 January 1968.", "html": "1967 - The <i><a href=\"https://wikipedia.org/wiki/Nauru_Independence_Act_1967\" title=\"Nauru Independence Act 1967\">Nauru Independence Act 1967</a></i> passed the <a href=\"https://wikipedia.org/wiki/Parliament_of_Australia\" title=\"Parliament of Australia\">Parliament of Australia</a>, giving independence to the UN Trust Territory of <a href=\"https://wikipedia.org/wiki/Nauru\" title=\"Nauru\">Nauru</a> with effect from 31 January 1968.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Nauru_Independence_Act_1967\" title=\"Nauru Independence Act 1967\">Nauru Independence Act 1967</a></i> passed the <a href=\"https://wikipedia.org/wiki/Parliament_of_Australia\" title=\"Parliament of Australia\">Parliament of Australia</a>, giving independence to the UN Trust Territory of <a href=\"https://wikipedia.org/wiki/Nauru\" title=\"Nauru\">Nauru</a> with effect from 31 January 1968.", "links": [{"title": "Nauru Independence Act 1967", "link": "https://wikipedia.org/wiki/Nauru_Independence_Act_1967"}, {"title": "Parliament of Australia", "link": "https://wikipedia.org/wiki/Parliament_of_Australia"}, {"title": "Nauru", "link": "https://wikipedia.org/wiki/Nauru"}]}, {"year": "1969", "text": "National Educational Television (the predecessor to the Public Broadcasting Service) in the United States debuts Sesame Street.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/National_Educational_Television\" title=\"National Educational Television\">National Educational Television</a> (the predecessor to the <a href=\"https://wikipedia.org/wiki/PBS\" title=\"PBS\">Public Broadcasting Service</a>) in the United States debuts <i><a href=\"https://wikipedia.org/wiki/Sesame_Street\" title=\"Sesame Street\">Sesame Street</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Educational_Television\" title=\"National Educational Television\">National Educational Television</a> (the predecessor to the <a href=\"https://wikipedia.org/wiki/PBS\" title=\"PBS\">Public Broadcasting Service</a>) in the United States debuts <i><a href=\"https://wikipedia.org/wiki/Sesame_Street\" title=\"Sesame Street\">Sesame Street</a></i>.", "links": [{"title": "National Educational Television", "link": "https://wikipedia.org/wiki/National_Educational_Television"}, {"title": "PBS", "link": "https://wikipedia.org/wiki/PBS"}, {"title": "Sesame Street", "link": "https://wikipedia.org/wiki/Sesame_Street"}]}, {"year": "1970", "text": "Vietnam War: Vietnamization: For the first time in five years, an entire week ends with no reports of American combat fatalities in Southeast Asia.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>: For the first time in five years, an entire week ends with no reports of American combat fatalities in Southeast Asia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Vietnamization\" title=\"Vietnamization\">Vietnamization</a>: For the first time in five years, an entire week ends with no reports of American combat fatalities in Southeast Asia.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Vietnamization", "link": "https://wikipedia.org/wiki/Vietnamization"}]}, {"year": "1970", "text": "Luna 17: uncrewed space mission launched by the Soviet Union.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Luna_17\" title=\"Luna 17\">Luna 17</a>: uncrewed space mission launched by the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luna_17\" title=\"Luna 17\">Luna 17</a>: uncrewed space mission launched by the Soviet Union.", "links": [{"title": "Luna 17", "link": "https://wikipedia.org/wiki/Luna_17"}]}, {"year": "1971", "text": "In Cambodia, Khmer Rouge forces attack the city of Phnom Penh and its airport, killing 44, wounding at least 30 and damaging nine aircraft.", "html": "1971 - In <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> forces attack the city of <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> and its airport, killing 44, wounding at least 30 and damaging nine <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">aircraft</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> forces attack the city of <a href=\"https://wikipedia.org/wiki/Phnom_Penh\" title=\"Phnom Penh\">Phnom Penh</a> and its airport, killing 44, wounding at least 30 and damaging nine <a href=\"https://wikipedia.org/wiki/Fixed-wing_aircraft\" title=\"Fixed-wing aircraft\">aircraft</a>.", "links": [{"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Phnom Penh", "link": "https://wikipedia.org/wiki/Phnom_Penh"}, {"title": "Fixed-wing aircraft", "link": "https://wikipedia.org/wiki/Fixed-wing_aircraft"}]}, {"year": "1971", "text": "A Merpati Nusantara Airlines Vickers Viscount crashes into the Indian Ocean near Padang, West Sumatra, Indonesia, killing all 69 people on board.", "html": "1971 - A <a href=\"https://wikipedia.org/wiki/Merpati_Nusantara_Airlines\" title=\"Merpati Nusantara Airlines\">Merpati Nusantara Airlines</a> <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> <a href=\"https://wikipedia.org/wiki/1971_Indian_Ocean_Vickers_Viscount_crash\" class=\"mw-redirect\" title=\"1971 Indian Ocean Vickers Viscount crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Indian_Ocean\" title=\"Indian Ocean\">Indian Ocean</a> near <a href=\"https://wikipedia.org/wiki/Padang\" title=\"Padang\">Padang</a>, <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing all 69 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Merpati_Nusantara_Airlines\" title=\"Merpati Nusantara Airlines\">Merpati Nusantara Airlines</a> <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> <a href=\"https://wikipedia.org/wiki/1971_Indian_Ocean_Vickers_Viscount_crash\" class=\"mw-redirect\" title=\"1971 Indian Ocean Vickers Viscount crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Indian_Ocean\" title=\"Indian Ocean\">Indian Ocean</a> near <a href=\"https://wikipedia.org/wiki/Padang\" title=\"Padang\">Padang</a>, <a href=\"https://wikipedia.org/wiki/West_Sumatra\" title=\"West Sumatra\">West Sumatra</a>, <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, killing all 69 people on board.", "links": [{"title": "Merpati Nusantara Airlines", "link": "https://wikipedia.org/wiki/Merpati_Nusantara_Airlines"}, {"title": "Vickers Viscount", "link": "https://wikipedia.org/wiki/Vickers_Viscount"}, {"title": "1971 Indian Ocean Vickers Viscount crash", "link": "https://wikipedia.org/wiki/1971_Indian_Ocean_Vickers_Viscount_crash"}, {"title": "Indian Ocean", "link": "https://wikipedia.org/wiki/Indian_Ocean"}, {"title": "Padang", "link": "https://wikipedia.org/wiki/Padang"}, {"title": "West Sumatra", "link": "https://wikipedia.org/wiki/West_Sumatra"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}]}, {"year": "1972", "text": "Southern Airways Flight 49 from Birmingham, Alabama is hijacked and, at one point, is threatened with crashing into the nuclear installation at the Oak Ridge National Laboratory. After two days, the plane lands in Havana, Cuba, where the hijackers are jailed by <PERSON><PERSON>.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Southern_Airways\" title=\"Southern Airways\">Southern Airways</a> <a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_49\" title=\"Southern Airways Flight 49\">Flight 49</a> from <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a> is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> and, at one point, is threatened with crashing into the nuclear installation at the <a href=\"https://wikipedia.org/wiki/Oak_Ridge_National_Laboratory\" title=\"Oak Ridge National Laboratory\">Oak Ridge National Laboratory</a>. After two days, the plane lands in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, where the hijackers are jailed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southern_Airways\" title=\"Southern Airways\">Southern Airways</a> <a href=\"https://wikipedia.org/wiki/Southern_Airways_Flight_49\" title=\"Southern Airways Flight 49\">Flight 49</a> from <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a> is <a href=\"https://wikipedia.org/wiki/Aircraft_hijacking\" title=\"Aircraft hijacking\">hijacked</a> and, at one point, is threatened with crashing into the nuclear installation at the <a href=\"https://wikipedia.org/wiki/Oak_Ridge_National_Laboratory\" title=\"Oak Ridge National Laboratory\">Oak Ridge National Laboratory</a>. After two days, the plane lands in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, where the hijackers are jailed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Southern Airways", "link": "https://wikipedia.org/wiki/Southern_Airways"}, {"title": "Southern Airways Flight 49", "link": "https://wikipedia.org/wiki/Southern_Airways_Flight_49"}, {"title": "Birmingham, Alabama", "link": "https://wikipedia.org/wiki/Birmingham,_Alabama"}, {"title": "Aircraft hijacking", "link": "https://wikipedia.org/wiki/Aircraft_hijacking"}, {"title": "Oak Ridge National Laboratory", "link": "https://wikipedia.org/wiki/Oak_Ridge_National_Laboratory"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "The 729-foot-long freighter SS <PERSON> sinks during a storm on Lake Superior, killing all 29 crew on board.", "html": "1975 - The 729-foot-long freighter <a href=\"https://wikipedia.org/wiki/SS_<PERSON>_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> sinks during a storm on <a href=\"https://wikipedia.org/wiki/Lake_Superior\" title=\"Lake Superior\">Lake Superior</a>, killing all 29 crew on board.", "no_year_html": "The 729-foot-long freighter <a href=\"https://wikipedia.org/wiki/SS_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> sinks during a storm on <a href=\"https://wikipedia.org/wiki/Lake_Superior\" title=\"Lake Superior\">Lake Superior</a>, killing all 29 crew on board.", "links": [{"title": "SS <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lake Superior", "link": "https://wikipedia.org/wiki/Lake_Superior"}]}, {"year": "1975", "text": "Treaty of Osimo between Yugoslavia and Italy", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Osimo\" title=\"Treaty of Osimo\">Treaty of Osimo</a> between <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> and <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Osimo\" title=\"Treaty of Osimo\">Treaty of Osimo</a> between <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> and <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>", "links": [{"title": "Treaty of Osimo", "link": "https://wikipedia.org/wiki/Treaty_of_Osimo"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1975", "text": "Israeli-Palestinian conflict: the United Nations General Assembly passes Resolution 3379, determining that Zionism is a form of racism.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> passes <a href=\"https://wikipedia.org/wiki/Resolution_3379\" class=\"mw-redirect\" title=\"Resolution 3379\">Resolution 3379</a>, determining that <a href=\"https://wikipedia.org/wiki/Zionism\" title=\"Zionism\">Zionism</a> is a form of <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a> passes <a href=\"https://wikipedia.org/wiki/Resolution_3379\" class=\"mw-redirect\" title=\"Resolution 3379\">Resolution 3379</a>, determining that <a href=\"https://wikipedia.org/wiki/Zionism\" title=\"Zionism\">Zionism</a> is a form of <a href=\"https://wikipedia.org/wiki/Racism\" title=\"Racism\">racism</a>.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "United Nations General Assembly", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly"}, {"title": "Resolution 3379", "link": "https://wikipedia.org/wiki/Resolution_3379"}, {"title": "Zionism", "link": "https://wikipedia.org/wiki/Zionism"}, {"title": "Racism", "link": "https://wikipedia.org/wiki/Racism"}]}, {"year": "1979", "text": "A 106-car Canadian Pacific freight train carrying explosive and poisonous chemicals from Windsor, Ontario, Canada derails in Mississauga, Ontario.", "html": "1979 - A 106-car <a href=\"https://wikipedia.org/wiki/Canadian_Pacific\" class=\"mw-redirect\" title=\"Canadian Pacific\">Canadian Pacific</a> freight train carrying explosive and poisonous chemicals from <a href=\"https://wikipedia.org/wiki/Windsor,_Ontario\" title=\"Windsor, Ontario\">Windsor, Ontario</a>, Canada <a href=\"https://wikipedia.org/wiki/1979_Mississauga_train_derailment\" title=\"1979 Mississauga train derailment\">derails</a> in <a href=\"https://wikipedia.org/wiki/Mississauga\" title=\"Mississauga\">Mississauga</a>, Ontario.", "no_year_html": "A 106-car <a href=\"https://wikipedia.org/wiki/Canadian_Pacific\" class=\"mw-redirect\" title=\"Canadian Pacific\">Canadian Pacific</a> freight train carrying explosive and poisonous chemicals from <a href=\"https://wikipedia.org/wiki/Windsor,_Ontario\" title=\"Windsor, Ontario\">Windsor, Ontario</a>, Canada <a href=\"https://wikipedia.org/wiki/1979_Mississauga_train_derailment\" title=\"1979 Mississauga train derailment\">derails</a> in <a href=\"https://wikipedia.org/wiki/Mississauga\" title=\"Mississauga\">Mississauga</a>, Ontario.", "links": [{"title": "Canadian Pacific", "link": "https://wikipedia.org/wiki/Canadian_Pacific"}, {"title": "Windsor, Ontario", "link": "https://wikipedia.org/wiki/Windsor,_Ontario"}, {"title": "1979 Mississauga train derailment", "link": "https://wikipedia.org/wiki/1979_Mississauga_train_derailment"}, {"title": "Mississauga", "link": "https://wikipedia.org/wiki/Mississauga"}]}, {"year": "1983", "text": "<PERSON> introduces Windows 1.0.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces <a href=\"https://wikipedia.org/wiki/Windows_1.0\" title=\"Windows 1.0\">Windows 1.0</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> introduces <a href=\"https://wikipedia.org/wiki/Windows_1.0\" title=\"Windows 1.0\">Windows 1.0</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Windows 1.0", "link": "https://wikipedia.org/wiki/Windows_1.0"}]}, {"year": "1985", "text": "A Dassault Falcon 50 and a Piper PA-28 Cherokee collide in mid-air over Fairview, New Jersey, killing six people and injuring eight.", "html": "1985 - A <a href=\"https://wikipedia.org/wiki/Dassault_Falcon_50\" title=\"Dassault Falcon 50\">Dassault Falcon 50</a> and a <a href=\"https://wikipedia.org/wiki/Piper_PA-28_Cherokee\" title=\"Piper PA-28 Cherokee\">Piper PA-28 Cherokee</a> <a href=\"https://wikipedia.org/wiki/1985_Teterboro_mid-air_collision\" title=\"1985 Teterboro mid-air collision\">collide</a> in mid-air over <a href=\"https://wikipedia.org/wiki/Fairview,_New_Jersey\" title=\"Fairview, New Jersey\">Fairview, New Jersey</a>, killing six people and injuring eight.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Dassault_Falcon_50\" title=\"Dassault Falcon 50\">Dassault Falcon 50</a> and a <a href=\"https://wikipedia.org/wiki/Piper_PA-28_Cherokee\" title=\"Piper PA-28 Cherokee\">Piper PA-28 Cherokee</a> <a href=\"https://wikipedia.org/wiki/1985_Teterboro_mid-air_collision\" title=\"1985 Teterboro mid-air collision\">collide</a> in mid-air over <a href=\"https://wikipedia.org/wiki/Fairview,_New_Jersey\" title=\"Fairview, New Jersey\">Fairview, New Jersey</a>, killing six people and injuring eight.", "links": [{"title": "Dassault Falcon 50", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ult_Falcon_50"}, {"title": "Piper PA-28 Cherokee", "link": "https://wikipedia.org/wiki/Piper_PA-28_Cherokee"}, {"title": "1985 Teterboro mid-air collision", "link": "https://wikipedia.org/wiki/1985_Teterboro_mid-air_collision"}, {"title": "Fairview, New Jersey", "link": "https://wikipedia.org/wiki/Fairview,_New_Jersey"}]}, {"year": "1989", "text": "Longtime Bulgarian leader <PERSON><PERSON> is removed from office and replaced by <PERSON><PERSON>.", "html": "1989 - Longtime <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria\" title=\"People's Republic of Bulgaria\">Bulgarian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is removed from office and replaced by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Longtime <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria\" title=\"People's Republic of Bulgaria\">Bulgarian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is removed from office and replaced by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "People's Republic of Bulgaria", "link": "https://wikipedia.org/wiki/People%27s_Republic_of_Bulgaria"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Germans begin to tear down the Berlin Wall.", "html": "1989 - Germans begin to <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">tear down</a> the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "no_year_html": "Germans begin to <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">tear down</a> the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "links": [{"title": "Fall of the Berlin Wall", "link": "https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "1995", "text": "In Nigeria, playwright and environmental activist <PERSON>, along with eight others from the Movement for the Survival of the Ogoni People (Mosop), are hanged by government forces.", "html": "1995 - In <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, playwright and environmental activist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, along with eight others from the <a href=\"https://wikipedia.org/wiki/Movement_for_the_Survival_of_the_Ogoni_People\" title=\"Movement for the Survival of the Ogoni People\">Movement for the Survival of the Ogoni People</a> (Mosop), are hanged by government forces.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, playwright and environmental activist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, along with eight others from the <a href=\"https://wikipedia.org/wiki/Movement_for_the_Survival_of_the_Ogoni_People\" title=\"Movement for the Survival of the Ogoni People\">Movement for the Survival of the Ogoni People</a> (Mosop), are hanged by government forces.", "links": [{"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wa"}, {"title": "Movement for the Survival of the Ogoni People", "link": "https://wikipedia.org/wiki/Movement_for_the_Survival_of_the_Ogoni_People"}]}, {"year": "1997", "text": "WorldCom and MCI Communications announce a $37 billion merger (the largest merger in US history at the time).", "html": "1997 - <a href=\"https://wikipedia.org/wiki/MCI_Inc.\" title=\"MCI Inc.\">WorldCom</a> and <a href=\"https://wikipedia.org/wiki/MCI_Communications\" title=\"MCI Communications\">MCI Communications</a> announce a $37 billion merger (the largest merger in US history at the time).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MCI_Inc.\" title=\"MCI Inc.\">WorldCom</a> and <a href=\"https://wikipedia.org/wiki/MCI_Communications\" title=\"MCI Communications\">MCI Communications</a> announce a $37 billion merger (the largest merger in US history at the time).", "links": [{"title": "MCI Inc.", "link": "https://wikipedia.org/wiki/MCI_Inc."}, {"title": "MCI Communications", "link": "https://wikipedia.org/wiki/MCI_Communications"}]}, {"year": "1999", "text": "World Anti-Doping Agency is formed in Lausanne.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/World_Anti-Doping_Agency\" title=\"World Anti-Doping Agency\">World Anti-Doping Agency</a> is formed in <a href=\"https://wikipedia.org/wiki/Lausanne\" title=\"Lausanne\">Lausanne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_Anti-Doping_Agency\" title=\"World Anti-Doping Agency\">World Anti-Doping Agency</a> is formed in <a href=\"https://wikipedia.org/wiki/Lausanne\" title=\"Lausanne\">Lausanne</a>.", "links": [{"title": "World Anti-Doping Agency", "link": "https://wikipedia.org/wiki/World_Anti-Doping_Agency"}, {"title": "Lausanne", "link": "https://wikipedia.org/wiki/Lausanne"}]}, {"year": "2002", "text": "Veteran's Day Weekend Tornado Outbreak: A tornado outbreak stretching from Northern Ohio to the Gulf Coast, one of the largest outbreaks recorded in November.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2002_Veterans_Day_weekend_tornado_outbreak\" title=\"2002 Veterans Day weekend tornado outbreak\">V<PERSON><PERSON>'s Day Weekend Tornado Outbreak</a>: A tornado outbreak stretching from Northern <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> to the Gulf Coast, one of the largest outbreaks recorded in November.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2002_Veterans_Day_weekend_tornado_outbreak\" title=\"2002 Veterans Day weekend tornado outbreak\">Veter<PERSON>'s Day Weekend Tornado Outbreak</a>: A tornado outbreak stretching from Northern <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> to the Gulf Coast, one of the largest outbreaks recorded in November.", "links": [{"title": "2002 Veterans Day weekend tornado outbreak", "link": "https://wikipedia.org/wiki/2002_Veterans_Day_weekend_tornado_outbreak"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}]}, {"year": "2006", "text": "Sri Lankan Tamil politician <PERSON><PERSON><PERSON><PERSON> is assassinated in Colombo.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil\" class=\"mw-redirect\" title=\"Sri Lankan Tamil\">Sri Lankan Tamil</a> politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil\" class=\"mw-redirect\" title=\"Sri Lankan Tamil\">Sri Lankan Tamil</a> politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>.", "links": [{"title": "Sri Lankan Tamil", "link": "https://wikipedia.org/wiki/Sri_Lankan_Tamil"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}]}, {"year": "2006", "text": "The National Museum of the Marine Corps in Quantico, Virginia is opened and dedicated by U.S. President <PERSON>, who announces that Marine Corporal <PERSON> will posthumously receive the Medal of Honor.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/National_Museum_of_the_Marine_Corps\" title=\"National Museum of the Marine Corps\">National Museum of the Marine Corps</a> in <a href=\"https://wikipedia.org/wiki/Quantico,_Virginia\" title=\"Quantico, Virginia\">Quantico, Virginia</a> is opened and dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/George_<PERSON>._Bush\" title=\"George <PERSON> Bush\"><PERSON></a>, who announces that Marine Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> will posthumously receive the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Museum_of_the_Marine_Corps\" title=\"National Museum of the Marine Corps\">National Museum of the Marine Corps</a> in <a href=\"https://wikipedia.org/wiki/Quantico,_Virginia\" title=\"Quantico, Virginia\">Quantico, Virginia</a> is opened and dedicated by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>\" title=\"George <PERSON> Bush\"><PERSON></a>, who announces that Marine Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> will posthumously receive the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "links": [{"title": "National Museum of the Marine Corps", "link": "https://wikipedia.org/wiki/National_Museum_of_the_Marine_Corps"}, {"title": "Quantico, Virginia", "link": "https://wikipedia.org/wiki/Quantico,_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2008", "text": "Over five months after landing on Mars, NASA declares the Phoenix mission concluded after communications with the lander were lost.", "html": "2008 - Over five months after landing on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> declares the <i><a href=\"https://wikipedia.org/wiki/Phoenix_(spacecraft)\" title=\"Phoenix (spacecraft)\">Phoenix</a></i> mission concluded after communications with the <a href=\"https://wikipedia.org/wiki/Lander_(spacecraft)\" title=\"Lander (spacecraft)\">lander</a> were lost.", "no_year_html": "Over five months after landing on <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> declares the <i><a href=\"https://wikipedia.org/wiki/Phoenix_(spacecraft)\" title=\"Phoenix (spacecraft)\">Phoenix</a></i> mission concluded after communications with the <a href=\"https://wikipedia.org/wiki/Lander_(spacecraft)\" title=\"Lander (spacecraft)\">lander</a> were lost.", "links": [{"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Phoenix (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "<PERSON>er (spacecraft)", "link": "https://wikipedia.org/wiki/Lander_(spacecraft)"}]}, {"year": "2009", "text": "Ships of the South and North Korean navies skirmish off Daecheong Island in the Yellow Sea.", "html": "2009 - Ships of the South and North Korean navies <a href=\"https://wikipedia.org/wiki/Battle_of_Daecheong\" class=\"mw-redirect\" title=\"Battle of Daecheong\">skirmish</a> off <a href=\"https://wikipedia.org/wiki/Daecheong_Island\" class=\"mw-redirect\" title=\"Daecheong Island\">Daecheong Island</a> in the <a href=\"https://wikipedia.org/wiki/Yellow_Sea\" title=\"Yellow Sea\">Yellow Sea</a>.", "no_year_html": "Ships of the South and North Korean navies <a href=\"https://wikipedia.org/wiki/Battle_of_Daecheong\" class=\"mw-redirect\" title=\"Battle of Daecheong\">skirmish</a> off <a href=\"https://wikipedia.org/wiki/Daecheong_Island\" class=\"mw-redirect\" title=\"Daecheong Island\">Daecheong Island</a> in the <a href=\"https://wikipedia.org/wiki/Yellow_Sea\" title=\"Yellow Sea\">Yellow Sea</a>.", "links": [{"title": "Battle of Daecheong", "link": "https://wikipedia.org/wiki/Battle_of_Daecheong"}, {"title": "Daecheong Island", "link": "https://wikipedia.org/wiki/Daecheong_Island"}, {"title": "Yellow Sea", "link": "https://wikipedia.org/wiki/Yellow_Sea"}]}, {"year": "2019", "text": "President of Bolivia <PERSON><PERSON> and several of his government resign after 19 days of civil protests and a recommendation from the military.", "html": "2019 - President of Bolivia <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and several of his government <a href=\"https://wikipedia.org/wiki/2019_Bolivian_political_crisis\" title=\"2019 Bolivian political crisis\">resign</a> after <a href=\"https://wikipedia.org/wiki/2019_Bolivian_protests\" title=\"2019 Bolivian protests\">19 days of civil protests</a> and a recommendation from the military.", "no_year_html": "President of Bolivia <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and several of his government <a href=\"https://wikipedia.org/wiki/2019_Bolivian_political_crisis\" title=\"2019 Bolivian political crisis\">resign</a> after <a href=\"https://wikipedia.org/wiki/2019_Bolivian_protests\" title=\"2019 Bolivian protests\">19 days of civil protests</a> and a recommendation from the military.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "2019 Bolivian political crisis", "link": "https://wikipedia.org/wiki/2019_Bolivian_political_crisis"}, {"title": "2019 Bolivian protests", "link": "https://wikipedia.org/wiki/2019_Bolivian_protests"}]}, {"year": "2020", "text": "Armenia and Azerbaijan sign a ceasefire agreement, ending the Second Nagorno-Karabakh War, and prompting protests in Armenia.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> sign a <a href=\"https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement\" title=\"2020 Nagorno-Karabakh ceasefire agreement\">ceasefire agreement</a>, ending the <a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>, and <a href=\"https://wikipedia.org/wiki/2020%E2%80%932021_Armenian_protests\" title=\"2020-2021 Armenian protests\">prompting protests in Armenia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a> and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> sign a <a href=\"https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement\" title=\"2020 Nagorno-Karabakh ceasefire agreement\">ceasefire agreement</a>, ending the <a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>, and <a href=\"https://wikipedia.org/wiki/2020%E2%80%932021_Armenian_protests\" title=\"2020-2021 Armenian protests\">prompting protests in Armenia</a>.", "links": [{"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "2020 Nagorno-Karabakh ceasefire agreement", "link": "https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement"}, {"title": "Second Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War"}, {"title": "2020-2021 Armenian protests", "link": "https://wikipedia.org/wiki/2020%E2%80%932021_Armenian_protests"}]}], "Births": [{"year": "745", "text": "<PERSON> the seventh Shia Imam (d.  799)", "html": "745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Kadhi<PERSON>\"><PERSON></a> the seventh <a href=\"https://wikipedia.org/wiki/Shia\" class=\"mw-redirect\" title=\"Shia\">Shia</a> <a href=\"https://wikipedia.org/wiki/Imam\" title=\"Imam\">Imam</a> (d. 799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Kadhim\"><PERSON></a> the seventh <a href=\"https://wikipedia.org/wiki/Shia\" class=\"mw-redirect\" title=\"Shia\">Shia</a> <a href=\"https://wikipedia.org/wiki/Imam\" title=\"Imam\">Imam</a> (d. 799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shia", "link": "https://wikipedia.org/wiki/Shia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Imam"}]}, {"year": "1278", "text": "<PERSON>, Prince of Taranto (d. 1332)", "html": "1278 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Taranto\"><PERSON>, Prince of Taranto</a> (d. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Taranto\"><PERSON>, Prince of Taranto</a> (d. 1332)", "links": [{"title": "<PERSON>, Prince of Taranto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto"}]}, {"year": "1341", "text": "<PERSON>, 1st Earl of Northumberland, English politician (d. 1408)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northumberland\" title=\"<PERSON>, 1st Earl of Northumberland\"><PERSON>, 1st Earl of Northumberland</a>, English politician (d. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northumberland\" title=\"<PERSON>, 1st Earl of Northumberland\"><PERSON>, 1st Earl of Northumberland</a>, English politician (d. 1408)", "links": [{"title": "<PERSON>, 1st Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northumberland"}]}, {"year": "1433", "text": "<PERSON> the <PERSON>, Duke of Burgundy (d. 1477)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (d. 1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (d. 1477)", "links": [{"title": "<PERSON> the Bold", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duke of Burgundy", "link": "https://wikipedia.org/wiki/Duke_of_Burgundy"}]}, {"year": "1480", "text": "<PERSON> York, English nun (d. 1517)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York\" title=\"<PERSON> of York\"><PERSON> York</a>, English nun (d. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_York\" title=\"<PERSON> York\"><PERSON> of York</a>, English nun (d. 1517)", "links": [{"title": "Bridget of York", "link": "https://wikipedia.org/wiki/Bridget_<PERSON>_York"}]}, {"year": "1483", "text": "<PERSON>, German monk and priest, leader of the Protestant Reformation (d. 1546)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German monk and priest, leader of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a> (d. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German monk and priest, leader of the <a href=\"https://wikipedia.org/wiki/Protestant_Reformation\" class=\"mw-redirect\" title=\"Protestant Reformation\">Protestant Reformation</a> (d. 1546)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Protestant Reformation", "link": "https://wikipedia.org/wiki/Protestant_Reformation"}]}, {"year": "1489", "text": "<PERSON>, Duke of Brunswick-Lüneburg and Prince of Wolfenbüttel (d. 1568)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> and Prince of Wolfenbüttel (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> and Prince of Wolfenbüttel (d. 1568)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1490", "text": "<PERSON>, Duke of Cleves (d. 1539)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1539)", "links": [{"title": "<PERSON>, Duke of Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_C<PERSON>"}]}, {"year": "1520", "text": "<PERSON> of Denmark, <PERSON><PERSON>tress <PERSON>, Princess of Denmark, Sweden and Norway (d. 1580)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_Electress_<PERSON><PERSON>\" title=\"<PERSON> of Denmark, Electress <PERSON>\"><PERSON> of Denmark, <PERSON><PERSON>tress <PERSON></a>, Princess of Denmark, Sweden and Norway (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Denmark,_<PERSON>ectress_<PERSON><PERSON>\" title=\"<PERSON> of Denmark, Electress <PERSON>latine\"><PERSON> of Denmark, <PERSON><PERSON>tress <PERSON></a>, Princess of Denmark, Sweden and Norway (d. 1580)", "links": [{"title": "<PERSON> of Denmark, Electress <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denmark,_<PERSON><PERSON>tress_Palatine"}]}, {"year": "1547", "text": "<PERSON><PERSON><PERSON> von <PERSON>, Archbishop of Cologne (d. 1601)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> von Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a>, Archbishop of Cologne (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_von_Waldburg\" title=\"<PERSON><PERSON><PERSON> von Waldburg\"><PERSON><PERSON><PERSON> von Waldburg</a>, Archbishop of Cologne (d. 1601)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, 2nd Earl of Essex, English general and politician, Lord Lieutenant of Ireland (d. 1601)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex\" title=\"<PERSON>, 2nd Earl of Essex\"><PERSON>, 2nd Earl of Essex</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1601)", "links": [{"title": "<PERSON>, 2nd Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Essex"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1565", "text": "<PERSON><PERSON>, Swedish astronomer and theologian (d. 1646)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish astronomer and theologian (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish astronomer and theologian (d. 1646)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1577", "text": "<PERSON>, Dutch poet, jurist, and politician (d. 1660)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jacob Cats\"><PERSON></a>, Dutch poet, jurist, and politician (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacob_Cats\" title=\"Jacob Cats\"><PERSON></a>, Dutch poet, jurist, and politician (d. 1660)", "links": [{"title": "Jacob Cats", "link": "https://wikipedia.org/wiki/Jacob_Cats"}]}, {"year": "1584", "text": "<PERSON> of Sweden, Countess <PERSON><PERSON> of Kleeburg (d. 1638)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/Catherine_of_Sweden,_Countess_<PERSON><PERSON>_of_Kleeburg\" title=\"Catherine of Sweden, Countess <PERSON><PERSON> of Kleeburg\"><PERSON> of Sweden, Countess <PERSON><PERSON> of Kleeburg</a> (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catherine_of_Sweden,_Countess_<PERSON><PERSON>_of_Kleeburg\" title=\"Catherine of Sweden, Countess <PERSON><PERSON> of Kleeburg\"><PERSON> of Sweden, Countess <PERSON><PERSON> of Kleeburg</a> (d. 1638)", "links": [{"title": "<PERSON> of Sweden, Countess <PERSON><PERSON> of Kleeburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sweden,_Countess_<PERSON><PERSON>_of_Kleeburg"}]}, {"year": "1620", "text": "<PERSON><PERSON>, French courtier and author (d. 1705)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_l%27Enclos\" title=\"<PERSON><PERSON>'En<PERSON>los\"><PERSON><PERSON></a>, French courtier and author (d. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_l%27Enclos\" title=\"<PERSON><PERSON> de <PERSON>'En<PERSON>\"><PERSON><PERSON></a>, French courtier and author (d. 1705)", "links": [{"title": "<PERSON><PERSON>'Enclos", "link": "https://wikipedia.org/wiki/Ni<PERSON>_de_l%27Enclos"}]}, {"year": "1668", "text": "<PERSON>, French organist and composer (d. 1733)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1668", "text": "<PERSON>, Prince of Condé (d. 1710)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1668%E2%80%931710)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1668-1710)\"><PERSON>, Prince of Condé</a> (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1668%E2%80%931710)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1668-1710)\"><PERSON>, Prince of Condé</a> (d. 1710)", "links": [{"title": "<PERSON>, Prince of Condé (1668-1710)", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1668%E2%80%931710)"}]}, {"year": "1695", "text": "<PERSON>, English physician and astronomer (d. 1771)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and astronomer (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and astronomer (d. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON>, English painter, illustrator, and critic (d. 1764)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, illustrator, and critic (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, illustrator, and critic (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Danish courtier, politician, and diplomat (d. 1792)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish courtier, politician, and diplomat (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish courtier, politician, and diplomat (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, Irish-English author, poet, and playwright (d. 1774)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English author, poet, and playwright (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English author, poet, and playwright (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>smith"}]}, {"year": "1735", "text": "<PERSON><PERSON>, English activist and scholar, co-founded the Sierra Leone Company (d. 1813)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/Granville_Sharp\" title=\"Granville Sharp\">Granville Sharp</a>, English activist and scholar, co-founded the <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Company\" title=\"Sierra Leone Company\">Sierra Leone Company</a> (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Granville_Sharp\" title=\"Granville Sharp\">Granville Sharp</a>, English activist and scholar, co-founded the <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Company\" title=\"Sierra Leone Company\">Sierra Leone Company</a> (d. 1813)", "links": [{"title": "Granville Sharp", "link": "https://wikipedia.org/wiki/Granville_Sharp"}, {"title": "Sierra Leone Company", "link": "https://wikipedia.org/wiki/Sierra_Leone_Company"}]}, {"year": "1755", "text": "<PERSON>, German violinist and educator (d. 1846)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and educator (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and educator (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, German poet and playwright (d. 1805)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Spanish-Mexican scientist and discoverer of vanadium (d. 1849)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>_del_Rio\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican scientist and discoverer of vanadium (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_<PERSON>_del_Rio\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish-Mexican scientist and discoverer of vanadium (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9<PERSON>_<PERSON>_del_Rio"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, French nun, founder of the Sisters of St Joseph of Cluny (d. 1851)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nun, founder of the Sisters of St Joseph of Cluny (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nun, founder of the Sisters of St Joseph of Cluny (d. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Russian lexicographer and author (d. 1872)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lexicographer and author (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lexicographer and author (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, American physician and activist (d. 1876)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, English plumber and engineer, invented the flush toilet (d. 1882)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English plumber and engineer, invented the <a href=\"https://wikipedia.org/wiki/Flush_toilet\" title=\"Flush toilet\">flush toilet</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English plumber and engineer, invented the <a href=\"https://wikipedia.org/wiki/Flush_toilet\" title=\"Flush toilet\">flush toilet</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flush toilet", "link": "https://wikipedia.org/wiki/Flush_toilet"}]}, {"year": "1826", "text": "<PERSON>, German rabbi and author (d. 1911)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and author (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rabbi and author (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, Argentinian journalist, poet, and politician (d. 1886)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Hern%C3%<PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Argentinian journalist, poet, and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Hern%C3%<PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Argentinian journalist, poet, and politician (d. 1886)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Hern%C3%A1<PERSON>z_(writer)"}]}, {"year": "1836", "text": "<PERSON>, Peruvian general, President of Peru (d. 1923)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Peruvian general, <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1844", "text": "<PERSON>, American educator and theologian (d. 1932)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and theologian (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and theologian (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Canadian lawyer, judge, and politician, 4th Prime Minister of Canada (d. 1894)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, Indian academic and politician (d. 1925)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and politician (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian academic and politician (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German philologist, historian, and educator (d. 1931)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, historian, and educator (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, historian, and educator (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, <PERSON> (d. 1928)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>XVI<PERSON>,_Prince_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> XXVII, <PERSON>\"><PERSON>, <PERSON></a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>X<PERSON>,_Prince_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Heinrich XXVII, <PERSON>\"><PERSON>, <PERSON></a> (d. 1928)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>X<PERSON>,_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON> (d. 1889) First Jewish student at Cambridge University", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1889) First Jewish student at Cambridge University", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1889) First Jewish student at Cambridge University", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist and educator, founded Shotokan (d. 1957)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Funakoshi\" title=\"<PERSON><PERSON><PERSON>oshi\"><PERSON><PERSON><PERSON></a>, Japanese martial artist and educator, founded <a href=\"https://wikipedia.org/wiki/Shotokan\" title=\"Shotokan\">Shotokan</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Fun<PERSON>oshi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist and educator, founded <a href=\"https://wikipedia.org/wiki/Shotokan\" title=\"Shotokan\">Shotokan</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Shotokan", "link": "https://wikipedia.org/wiki/Shotokan"}]}, {"year": "1871", "text": "<PERSON>, American author and painter (d. 1947)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author and painter (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American author and painter (d. 1947)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "1873", "text": "<PERSON>, French conductor and composer (d. 1949)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, American composer and songwriter (d. 1954)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>stone\" title=\"<PERSON><PERSON><PERSON> Firestone\"><PERSON><PERSON><PERSON></a>, American composer and songwriter (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>stone\" title=\"<PERSON><PERSON><PERSON> Firestone\"><PERSON><PERSON><PERSON></a>, American composer and songwriter (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON> Smith Firestone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Firestone"}]}, {"year": "1878", "text": "<PERSON>, American baseball player (d. 1962)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, American poet and educator (d. 1931)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and educator (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and educator (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Irish lawyer, poet, teacher, and insurrectionist; executed for his role in the Easter Rising (d. 1916)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer, poet, teacher, and insurrectionist; executed for his role in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer, poet, teacher, and insurrectionist; executed for his role in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a> (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}]}, {"year": "1880", "text": "<PERSON>, American-English sculptor (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English sculptor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English sculptor (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Polish author and playwright (d. 1954)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Zofia_Na%C5%82kowska\" title=\"<PERSON>ofia Nałkowska\"><PERSON><PERSON><PERSON></a>, Polish author and playwright (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zofia_Na%C5%82kowska\" title=\"<PERSON><PERSON><PERSON> Nałkows<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and playwright (d. 1954)", "links": [{"title": "Zofia Nałkowska", "link": "https://wikipedia.org/wiki/Zofia_Na%C5%82kowska"}]}, {"year": "1886", "text": "<PERSON>, American pianist, composer, and conductor (d. 1951)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Romanian engineer and academic (d. 1973)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian engineer and academic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian engineer and academic (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German author and activist (d. 1968)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Russian engineer and designer, founded the Tupolev Design Bureau (d. 1972)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and designer, founded the <a href=\"https://wikipedia.org/wiki/Tupolev_Design_Bureau\" class=\"mw-redirect\" title=\"Tupolev Design Bureau\">Tupolev Design Bureau</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and designer, founded the <a href=\"https://wikipedia.org/wiki/Tupolev_Design_Bureau\" class=\"mw-redirect\" title=\"Tupolev Design Bureau\">Tupolev Design Bureau</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tupolev Design Bureau", "link": "https://wikipedia.org/wiki/Tupolev_Design_Bureau"}]}, {"year": "1889", "text": "<PERSON>, English-American actor (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, German engineer, founder of Borgward Group", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer, founder of Borgward Group", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer, founder of Borgward Group", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American pianist and composer (d. 1972)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American author (d. 1960)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Slovenian lawyer, jurist, and politician (d. 1957)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian lawyer, jurist, and politician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian lawyer, jurist, and politician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian physician and academic (d. 1979)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zsef_M%C3%A1ty%C3%A1s_Bal%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physician and academic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3zsef_M%C3%A1ty%C3%A1s_Bal%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian physician and academic (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zsef_M%C3%A1ty%C3%A1s_Bal%C3%B3"}]}, {"year": "1895", "text": "<PERSON>, American businessman, founded the Northrop Corporation (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Northrop_Corporation\" title=\"Northrop Corporation\">Northrop Corporation</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack <PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Northrop_Corporation\" title=\"Northrop Corporation\">Northrop Corporation</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Northrop Corporation", "link": "https://wikipedia.org/wiki/Northrop_Corporation"}]}, {"year": "1896", "text": "<PERSON>, American baseball player and manager (d. 1976)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Hungarian-American author and illustrator (d. 1975)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American author and illustrator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American author and illustrator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1907", "text": "<PERSON>, American actress and singer (d. 1980)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English activist and author (d. 1967)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_author)\" class=\"mw-redirect\" title=\"<PERSON> (British author)\"><PERSON></a>, English activist and author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_author)\" class=\"mw-redirect\" title=\"<PERSON> (British author)\"><PERSON></a>, English activist and author (d. 1967)", "links": [{"title": "<PERSON> (British author)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_author)"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Argentinian sculptor and illustrator (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Noem%C3%AD_G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian sculptor and illustrator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noem%C3%AD_G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian sculptor and illustrator (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noem%C3%<PERSON>_<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Canadian colonel and politician, Victoria Cross recipient (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Russian-Polish soldier, journalist, and historian (d. 1970)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Jasieni<PERSON>\" title=\"Paweł J<PERSON>eni<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Polish soldier, journalist, and historian (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Jasienica\" title=\"<PERSON><PERSON>ł <PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Polish soldier, journalist, and historian (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON>asi<PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American composer and songwriter (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Italian sculptor (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1999)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American poet and academic (d. 2000)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Irish painter and illustrator (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>uy\"><PERSON></a>, Irish painter and illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American trumpet player and composer (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1918", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, American radio and television announcer (d. 1997)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television announcer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Russian general and engineer, designed the AK-47 (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and engineer, designed the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and engineer, designed the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "AK-47", "link": "https://wikipedia.org/wiki/AK-47"}]}, {"year": "1919", "text": "<PERSON>, American sergeant and flag raiser at the Battle of Iwo Jima (d. 1945)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and flag raiser at the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and flag raiser at the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Congolese accountant and politician, Prime Minister of the Democratic Republic of the Congo (d. 1969)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>omb<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Tshombe\"><PERSON><PERSON></a>, Congolese accountant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Tshombe\"><PERSON><PERSON></a>, Congolese accountant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo\" title=\"Prime Minister of the Democratic Republic of the Congo\">Prime Minister of the Democratic Republic of the Congo</a> (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}, {"title": "Prime Minister of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Democratic_Republic_of_the_Congo"}]}, {"year": "1920", "text": "<PERSON><PERSON>, English actress (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Spanish businessman, founded the Ferrovial Company (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Spanish businessman, founded the <a href=\"https://wikipedia.org/wiki/Ferrovial\" title=\"Ferrovial\">Ferrovial Company</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Spanish businessman, founded the <a href=\"https://wikipedia.org/wiki/Ferrovial\" title=\"Ferrovial\">Ferrovial Company</a> (d. 2008)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}, {"title": "Ferrovial", "link": "https://wikipedia.org/wiki/Ferrovial"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese dog famous for his loyalty to his owner (d. 1935)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Hachik%C5%8D\" title=\"Ha<PERSON>k<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese dog famous for his loyalty to his owner (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hachik%C5%8D\" title=\"Ha<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese dog famous for his loyalty to his owner (d. 1935)", "links": [{"title": "Hachikō", "link": "https://wikipedia.org/wiki/Hachik%C5%8D"}]}, {"year": "1924", "text": "<PERSON>, Australian comedian, actor, and bandleader (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, actor, and bandleader (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, actor, and bandleader (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Welsh actor and singer (d. 1984)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian hymnodist (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Hymnodist\" class=\"mw-redirect\" title=\"Hymnodist\">hymnodist</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Hymnodist\" class=\"mw-redirect\" title=\"Hymnodist\">hymnodist</a> (d. 2022)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}, {"title": "Hymnodist", "link": "https://wikipedia.org/wiki/Hymnodist"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Turkish architect and a former mayor of Ankara (d. 1991)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Vedat_Dalokay\" title=\"Vedat Dalokay\"><PERSON><PERSON><PERSON></a>, Turkish architect and a former mayor of Ankara (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vedat_Dalokay\" title=\"Vedat Dalokay\"><PERSON><PERSON><PERSON></a>, Turkish architect and a former mayor of Ankara (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vedat_Dalokay"}]}, {"year": "1927", "text": "<PERSON>, American general (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Japanese politician, Japanese Minister of Defense (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Minister of Defense (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Defense_(Japan)"}]}, {"year": "1927", "text": "Sabah, Lebanese singer and actress (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Sabah_(singer)\" title=\"Sabah (singer)\">Sabah</a>, Lebanese singer and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabah_(singer)\" title=\"<PERSON> (singer)\">Sabah</a>, Lebanese singer and actress (d. 2014)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1927", "text": "<PERSON>, Argentine basketball player (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine basketball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Italian trumpet player, composer, and conductor (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian trumpet player, composer, and conductor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian trumpet player, composer, and conductor (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ennio_Morricone"}]}, {"year": "1929", "text": "<PERSON>, American composer and songwriter (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American soldier and author (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. E<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American soldier and author (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Cuban-Mexican actress and dancer (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Nin%C3%B3n_Sevilla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-Mexican actress and dancer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nin%C3%B3n_Sevilla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-Mexican actress and dancer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nin%C3%B3n_Sevilla"}]}, {"year": "1929", "text": "<PERSON>, English footballer (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2024)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1930", "text": "<PERSON>, American baseball and basketball player (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and basketball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball and basketball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American fashion designer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lilly Pulitzer\"><PERSON></a>, American fashion designer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lilly_<PERSON>\" title=\"Lilly Pulitzer\"><PERSON></a>, American fashion designer (d. 2013)", "links": [{"title": "Lilly Pulitzer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian-American pianist and composer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist and composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish linguist, author, and academic (d. 1996)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu\" title=\"Nec<PERSON>in <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish linguist, author, and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu\" title=\"Necmettin <PERSON>lu\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish linguist, author, and academic (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American lawyer and politician (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American captain, engineer, and astronaut (d. 1990)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, engineer, and astronaut (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, engineer, and astronaut (d. 1990)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_(astronaut)"}]}, {"year": "1933", "text": "<PERSON>, Barbadian cricketer (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Italian-Belgian race car driver (d. 1969)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Belgian race car driver (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Belgian race car driver (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, 3rd Viscount <PERSON> of Doxford, English sociologist and academic (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>_of_Doxford\" title=\"<PERSON>, 3rd Viscount <PERSON> of Doxford\"><PERSON>, 3rd Viscount <PERSON> of Doxford</a>, English sociologist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>_of_Doxford\" title=\"<PERSON>, 3rd Viscount <PERSON> of Doxford\"><PERSON>, 3rd Viscount <PERSON> of Doxford</a>, English sociologist and academic (d. 2020)", "links": [{"title": "<PERSON>, 3rd Viscount <PERSON> of Doxford", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Viscount_<PERSON>_of_Doxford"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Sri Lankan engineer and academic (d. 1994)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Sri Lankan engineer and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Sri Lankan engineer and academic (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Italian lawyer (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clio_<PERSON>_<PERSON>\" title=\"Clio <PERSON>\"><PERSON><PERSON></a>, Italian lawyer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clio_Maria_Bittoni"}]}, {"year": "1935", "text": "<PERSON>, American physician and biochemist (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biochemist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biochemist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Russian astronomer, astrophysicist, and cosmologist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer, astrophysicist, and cosmologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian astronomer, astrophysicist, and cosmologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Nigerian Supreme Court judge (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian Supreme Court judge (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian Supreme Court judge (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1938", "text": "<PERSON>, American college basketball coach (Texas Southern Tigers) (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American college basketball coach (<a href=\"https://wikipedia.org/wiki/Texas_Southern_Tigers_basketball\" title=\"Texas Southern Tigers basketball\">Texas Southern Tigers</a>) (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American college basketball coach (<a href=\"https://wikipedia.org/wiki/Texas_Southern_Tigers_basketball\" title=\"Texas Southern Tigers basketball\">Texas Southern Tigers</a>) (d. 2024)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}, {"title": "Texas Southern Tigers basketball", "link": "https://wikipedia.org/wiki/Texas_Southern_Tigers_basketball"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Filipino monk and theologian (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ansca<PERSON>_<PERSON>pungco\" title=\"Anscar Chupungco\"><PERSON><PERSON><PERSON></a>, Filipino monk and theologian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ansca<PERSON>_<PERSON>pungco\" title=\"Anscar Chupungco\"><PERSON><PERSON><PERSON></a>, Filipino monk and theologian (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anscar_Chu<PERSON>ngco"}]}, {"year": "1939", "text": "<PERSON>, American rock & roll singer and guitarist (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock &amp; roll singer and guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock &amp; roll singer and guitarist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American activist, actor, and musician (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Means\"><PERSON></a>, American activist, actor, and musician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Means\"><PERSON></a>, American activist, actor, and musician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Means"}]}, {"year": "1939", "text": "<PERSON>, Canadian-Australian race car driver", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian geneticist and academic (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)\" title=\"<PERSON> (geneticist)\"><PERSON></a>, Australian geneticist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)\" title=\"<PERSON> (geneticist)\"><PERSON></a>, Australian geneticist and academic (d. 2015)", "links": [{"title": "<PERSON> (geneticist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)"}]}, {"year": "1940", "text": "Screaming <PERSON>, English singer-songwriter and politician (d. 1999)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Screaming_Lord_Sutch\" title=\"Screaming Lord Sutch\">Screaming Lord <PERSON></a>, English singer-songwriter and politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Screaming_Lord_Sutch\" title=\"Screaming Lord Sutch\">Screaming Lord Sutch</a>, English singer-songwriter and politician (d. 1999)", "links": [{"title": "Screaming Lord <PERSON>", "link": "https://wikipedia.org/wiki/Screaming_Lord_<PERSON>tch"}]}, {"year": "1942", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>gle"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1942", "text": "<PERSON>, American activist (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Swiss lawyer and politician, 92nd President of the Swiss Confederation", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 92nd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 92nd <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Saxby_Chambliss\" title=\"Saxby Chambliss\"><PERSON>x<PERSON>mb<PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saxby_Chambliss\" title=\"Saxby Chambliss\"><PERSON>x<PERSON>mb<PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sax<PERSON>_<PERSON>liss"}]}, {"year": "1943", "text": "<PERSON>, Australian rugby league player (d. 2020)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 2020)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Kyrgyzstani economist and politician, 1st President of Kyrgyzstan", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kyrgyzstani economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kyrgyzstani economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Kyrgyzstan", "link": "https://wikipedia.org/wiki/President_of_Kyrgyzstan"}]}, {"year": "1944", "text": "<PERSON>, Jr., American historian, author, and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American historian, author, and academic", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American sergeant and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American sergeant and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English lyricist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lyricist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lyricist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English actor, director, and screenwriter  (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Donna Fargo\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Donna_<PERSON>\" title=\"Donna Fargo\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American guitarist and songwriter (d. 1997)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Glen_Buxton\" title=\"Glen Buxton\"><PERSON></a>, American guitarist and songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_Buxton\" title=\"Glen Buxton\"><PERSON></a>, American guitarist and songwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Buxton"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Lebanese commander and politician (d. 1982)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese commander and politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese commander and politician (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American journalist and academic (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic (d. 2024)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1948", "text": "<PERSON>, Croatian Olympic runner and politician (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1anj\" title=\"<PERSON>\"><PERSON></a>, Croatian Olympic runner and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1anj\" title=\"<PERSON>\"><PERSON></a>, Croatian Olympic runner and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luciano_Su%C5%A1anj"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese video game designer and voice actor, created EarthBound", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Itoi\" title=\"Shigesato Itoi\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and voice actor, created <i><a href=\"https://wikipedia.org/wiki/EarthBound_(series)\" class=\"mw-redirect\" title=\"EarthBound (series)\">EarthBound</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Itoi\" title=\"Shigesato Itoi\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and voice actor, created <i><a href=\"https://wikipedia.org/wiki/EarthBound_(series)\" class=\"mw-redirect\" title=\"EarthBound (series)\">EarthBound</a></i>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>oi"}, {"title": "EarthBound (series)", "link": "https://wikipedia.org/wiki/EarthBound_(series)"}]}, {"year": "1948", "text": "<PERSON>, American author and poet (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actress, dancer, and choreographer (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ink<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian ice hockey player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Turkish footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American screenwriter and producer (d. 2005)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_Hill\" title=\"Debra Hill\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>\" title=\"Debra Hill\"><PERSON><PERSON></a>, American screenwriter and producer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Debra_Hill"}]}, {"year": "1950", "text": "<PERSON>, American actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player (d. 1996)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1951)\" title=\"<PERSON> (basketball, born 1951)\"><PERSON></a>, American basketball player (d. 1996)", "links": [{"title": "<PERSON> (basketball, born 1951)", "link": "https://wikipedia.org/wiki/<PERSON>(basketball,_born_1951)"}]}, {"year": "1953", "text": "<PERSON>, American football player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian chess player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1955", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1955", "text": "<PERSON>, German director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Egyptian businessman and activist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>aw<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian businessman and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian businessman and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American comedian and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Sin<PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin<PERSON>_(comedian)\" title=\"<PERSON><PERSON> (comedian)\"><PERSON><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON><PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(comedian)"}]}, {"year": "1957", "text": "<PERSON>, Welsh politician, Shadow Secretary of State for Wales", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales\" title=\"Shadow Secretary of State for Wales\">Shadow Secretary of State for Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Wales"}]}, {"year": "1958", "text": "<PERSON>, English linguist, anthropologist, and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, English linguist, anthropologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, English linguist, anthropologist, and academic", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(linguist)"}]}, {"year": "1958", "text": "<PERSON>, American director and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Italian singer-songwriter and activist (d. 2001)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and activist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter and activist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6der\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hr%C3%B6der"}]}, {"year": "1960", "text": "<PERSON>, English author, illustrator, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, illustrator, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, illustrator, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American football player, coach, and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1960", "text": "<PERSON>, Japanese actress and singer (d. 2015)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, English politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, German-Austrian physicist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English darts player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, English darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(darts_player)\" title=\"<PERSON> (darts player)\"><PERSON></a>, English darts player", "links": [{"title": "<PERSON> (darts player)", "link": "https://wikipedia.org/wiki/<PERSON>_(darts_player)"}]}, {"year": "1962", "text": "<PERSON>, Australian rugby league player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American director and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON> (screenwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(screenwriter)"}]}, {"year": "1963", "text": "<PERSON>, English actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor and comedian", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American long jumper", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(long_jumper)\" title=\"<PERSON> (long jumper)\"><PERSON></a>, American long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(long_jumper)\" title=\"<PERSON> (long jumper)\"><PERSON></a>, American long jumper", "links": [{"title": "<PERSON> (long jumper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(long_jumper)"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Northern Irish race car driver", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Welsh rugby player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1965)\" title=\"<PERSON> (rugby union, born 1965)\"><PERSON></a>, Welsh rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1965)\" title=\"<PERSON> (rugby union, born 1965)\"><PERSON></a>, Welsh rugby player and coach", "links": [{"title": "<PERSON> (rugby union, born 1965)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1965)"}]}, {"year": "1967", "text": "<PERSON>, Australian runner and coach (d. 2014)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American comedian and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morgan\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American comedian, actor, television host", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Papa\"><PERSON></a>, American comedian, actor, television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Papa\"><PERSON></a>, American comedian, actor, television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Colombian footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Faustino Asprilla\"><PERSON><PERSON></a>, Colombian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Faustino Asprilla\"><PERSON><PERSON></a>, Colombian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_As<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German footballer and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American rapper and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Warren G\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G\" title=\"Warren G\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Warren_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/U-God\" title=\"U-God\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U-God\" title=\"U-God\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "U-God", "link": "https://wikipedia.org/wiki/U-God"}]}, {"year": "1970", "text": "<PERSON>, Belgian race car driver", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Russian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer_born_1970)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1970)\"><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer_born_1970)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1970)\"><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON> (footballer born 1970)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(footballer_born_1970)"}]}, {"year": "1971", "text": "<PERSON>, American rapper (d. 2000)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Big_Pun\" title=\"Big Pun\"><PERSON></a>, American rapper (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Pun\" title=\"Big Pun\"><PERSON></a>, American rapper (d. 2000)", "links": [{"title": "<PERSON> Pun", "link": "https://wikipedia.org/wiki/<PERSON>_Pun"}]}, {"year": "1971", "text": "<PERSON>, American actor and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swedish footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON> (footballer, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1971)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Iranian actress, director, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vir%C3%A1g_Csurg%C3%B3\" title=\"Vir<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vir%C3%A1g_Csurg%C3%B3\" title=\"Vir<PERSON><PERSON> Csurgó\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vir%C3%A1g_Csurg%C3%B3"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Czech footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Mexican footballer and referee", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rodr%C3%ADguez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADguez"}]}, {"year": "1974", "text": "<PERSON>, Australian comedian and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Australian comedian and producer", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mark<PERSON>_M%C3%A4rtin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M%C3%A4rtin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Markko_M%C3%A4rtin"}]}, {"year": "1976", "text": "<PERSON>, Swedish footballer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%85slund\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%85slund\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%85slund"}]}, {"year": "1976", "text": "<PERSON>, Spanish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1976)\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1976)\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1976)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1976)"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Steffen_<PERSON>n\" title=\"Steffen Iversen\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stef<PERSON>_<PERSON>n\" title=\"Steffen Iversen\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Finnish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American mixed martial artist and wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress and singer (d. 2009)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Norwegian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American DJ, songwriter, and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American DJ, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American DJ, songwriter, and producer", "links": [{"title": "Diplo", "link": "https://wikipedia.org/wiki/Diplo"}]}, {"year": "1978", "text": "<PERSON>, American rapper and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian bass player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9veill%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9veill%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anthony_R%C3%A9veill%C3%A8re"}]}, {"year": "1979", "text": "<PERSON>, Brazilian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Serbian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Beli%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Beli%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danilo_Beli%C4%87"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> Bell", "link": "https://wikipedia.org/wiki/Troy_Bell"}]}, {"year": "1981", "text": "<PERSON>, Dominican baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American soldier, Medal of Honor recipient (d. 2004)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1981", "text": "<PERSON>, Kenyan runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>\" title=\"Ryback\"><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>\" title=\"R<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>back"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Mir<PERSON><PERSON>_Slepi%C4%8Dka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Slepi%C4%8Dka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Slepi%C4%8Dka"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Lithuanian footballer (d. 2020)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Marius_%C5%BDali%C5%ABkas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marius_%C5%BDali%C5%ABkas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marius_%C5%BDali%C5%ABkas"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Egyptian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, New Zealand singer-songwriter and dancer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, New Zealand singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, New Zealand singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Gambian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Cherno_Samba\" title=\"Cherno Samba\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherno_Samba\" title=\"Cherno Samba\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "links": [{"title": "Cherno Samba", "link": "https://wikipedia.org/wiki/Cherno_Samba"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Goran_Jerkovi%C4%87_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goran_Jerkovi%C4%87_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/Goran_Jerkovi%C4%87_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Moldovan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99co\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moldovan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99co\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moldovan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99co"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Kenyan runner (d. 2011)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_Augustin\" title=\"D<PERSON> J. Augustin\"><PERSON><PERSON> <PERSON><PERSON> Augustin</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_Augustin\" title=\"D. J. Augustin\"><PERSON><PERSON> <PERSON><PERSON> Augustin</a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Augustin"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>som"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>imo_Coda\" title=\"Massimo Coda\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>imo_Coda\" title=\"Massimo Coda\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_Coda"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Filipino actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ghanaian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Welsh actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ton"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, New Zealand race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Swiss footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Nik%C3%A7i\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Nik%C3%A7i\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_Nik%C3%A7i"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American boxer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, South Korean singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1990", "text": "<PERSON>, Trinidadian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, German track cyclist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German track cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Macedonian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Marko_Bla%C5%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mark<PERSON>_Bla%C5%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Bla%C5%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Rafa%C5%82_Wolski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rafa%C5%82_Wolski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rafa%C5%82_W<PERSON>ki"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Daieish%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daieish%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Daieish%C5%8D_<PERSON>ato"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian sprinter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Ralfs_Gr%C4%ABnbergs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ralfs_Gr%C4%ABnbergs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ralfs_Gr%C4%ABnbergs"}]}, {"year": "1995", "text": "<PERSON>, British tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Italian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Cypriot gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(gymnast)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (gymnast)\"><PERSON><PERSON></a>, Cypriot gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(gymnast)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (gymnast)\"><PERSON><PERSON></a>, Cypriot gymnast", "links": [{"title": "<PERSON><PERSON> (gymnast)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(gymnast)"}]}, {"year": "1997", "text": "<PERSON>, Italian-Senegalese footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Senegalese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Welsh footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, German judoka", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German judoka", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German judoka", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Ukrainian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Mexican rhythmic gymnast", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican rhythmic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Swedish-American pole vaulter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1999", "text": "<PERSON>, Portuguese footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_F%C3%A9lix"}]}, {"year": "1999", "text": "<PERSON>, Austrian musician and filmmaker", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musician and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian musician and filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress and model", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2002", "text": "<PERSON>, French footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian ice hockey player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bel_Belluz\" title=\"<PERSON> Bel Belluz\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bel_Belluz\" title=\"Luca Del Bel Belluz\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Luca Del <PERSON> Belluz", "link": "https://wikipedia.org/wiki/Luca_Del_Bel_Belluz"}]}, {"year": "2009", "text": "<PERSON>, Canadian actor", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Christian_Convery\" title=\"Christian Convery\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Convery\" title=\"Christian Convery\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Convery"}]}], "Deaths": [{"year": "461", "text": "<PERSON>", "html": "461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\">Pope <PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\">Pope <PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "474", "text": "<PERSON>, Byzantine emperor (b. 467)", "html": "474 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a>, Byzantine emperor (b. 467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_(emperor)\" title=\"<PERSON> II (emperor)\"><PERSON> II</a>, Byzantine emperor (b. 467)", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(emperor)"}]}, {"year": "901", "text": "<PERSON> of Paris (b. 850)", "html": "901 - <a href=\"https://wikipedia.org/wiki/Adelaide_of_Paris\" title=\"Adelaide of Paris\">Adelaide of Paris</a> (b. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_of_Paris\" title=\"Adelaide of Paris\">Adelaide of Paris</a> (b. 850)", "links": [{"title": "Adelaide of Paris", "link": "https://wikipedia.org/wiki/Adelaide_of_Paris"}]}, {"year": "948", "text": "<PERSON>, Chinese general and governor", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and governor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1066", "text": "<PERSON>, bishop of Mecklenburg", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Mecklenburg)\" title=\"<PERSON> (bishop of Mecklenburg)\"><PERSON></a>, bishop of Mecklenburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Mecklenburg)\" title=\"<PERSON> (bishop of Mecklenburg)\"><PERSON></a>, bishop of Mecklenburg", "links": [{"title": "<PERSON> (bishop of Mecklenburg)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Mecklenburg)"}]}, {"year": "1068", "text": "<PERSON> of Burgundy, Duchess of Aquitaine, regent of Aquitaine", "html": "1068 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Burgundy,_Duchess_of_Aquitaine\" title=\"<PERSON> of Burgundy, Duchess of Aquitaine\"><PERSON> of Burgundy, Duchess of Aquitaine</a>, regent of Aquitaine", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Burgundy,_Duchess_of_Aquitaine\" title=\"<PERSON> of Burgundy, Duchess of Aquitaine\"><PERSON> of Burgundy, Duchess of Aquitaine</a>, regent of Aquitaine", "links": [{"title": "<PERSON> of Burgundy, Duchess of Aquitaine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Burgundy,_Duchess_of_Aquitaine"}]}, {"year": "1187", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, King of the Isles", "html": "1187 - <a href=\"https://wikipedia.org/wiki/Gu%C3%B0r%C3%B8%C3%B0r_%C3%93l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of the Isles", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gu%C3%B0r%C3%B8%C3%B0r_%C3%93l%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>u<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of the Isles", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gu%C3%B0r%C3%B8%C3%B0r_%C3%93l%C3%<PERSON><PERSON>son"}]}, {"year": "1241", "text": "<PERSON> <PERSON><PERSON><PERSON> IV", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_IV\" title=\"Pope Ce<PERSON>tine IV\">Pope <PERSON><PERSON><PERSON> IV</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_IV\" title=\"Pope Celestine IV\"><PERSON> <PERSON><PERSON><PERSON> IV</a>", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1258", "text": "<PERSON>, Bishop of Glasgow", "html": "1258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Glasgow", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Glasgow", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1290", "text": "<PERSON><PERSON><PERSON><PERSON>, Sultan of Egypt (b. c. 1222)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/Al-Mansur_Qalawun\" class=\"mw-redirect\" title=\"Al-<PERSON>ur Qalawun\"><PERSON><PERSON><PERSON><PERSON></a>, Sultan of Egypt (b. c. 1222)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Mansur_Qalawun\" class=\"mw-redirect\" title=\"Al-Mansur Qalawun\"><PERSON><PERSON><PERSON><PERSON></a>, Sultan of Egypt (b. c. 1222)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-<PERSON>_<PERSON>"}]}, {"year": "1293", "text": "<PERSON>, Countess of Devon (b. 1237)", "html": "1293 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_suo_jure_8th_Countess_of_Devon\" class=\"mw-redirect\" title=\"<PERSON>, suo jure 8th Countess of Devon\"><PERSON>, Countess of Devon</a> (b. 1237)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_suo_jure_8th_Countess_of_Devon\" class=\"mw-redirect\" title=\"<PERSON>, suo jure 8th Countess of Devon\"><PERSON>, Countess of Devon</a> (b. 1237)", "links": [{"title": "<PERSON>, suo jure 8th Countess of Devon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_suo_jure_8th_Countess_of_Devon"}]}, {"year": "1299", "text": "<PERSON>, Count of Holland (b. 1284)", "html": "1299 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 1284)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON>, Count of Holland</a> (b. 1284)", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}]}, {"year": "1444", "text": "<PERSON><PERSON><PERSON><PERSON> of Poland (b. 1424)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> of Poland\"><PERSON><PERSON><PERSON><PERSON> of Poland</a> (b. 1424)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland\" title=\"<PERSON><PERSON><PERSON><PERSON> of Poland\"><PERSON><PERSON><PERSON><PERSON> of Poland</a> (b. 1424)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Poland", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_III_of_Poland"}]}, {"year": "1549", "text": "<PERSON> (b. 1468)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (b. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1468)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1556", "text": "<PERSON>, English explorer(b. c. 1521)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chancellor\"><PERSON></a>, English explorer(b. c. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer(b. c. 1521)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON>, English soldier and author (b. 1540)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rich\" title=\"Barnabe Rich\"><PERSON><PERSON><PERSON></a>, English soldier and author (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>be_Rich\" title=\"Barnabe Rich\"><PERSON><PERSON><PERSON></a>, English soldier and author (b. 1540)", "links": [{"title": "Barnabe Rich", "link": "https://wikipedia.org/wiki/<PERSON>nabe_Rich"}]}, {"year": "1624", "text": "<PERSON>, 3rd Earl of Southampton, English politician, Lord Lieutenant of Hampshire (b. 1573)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton\" title=\"<PERSON>, 3rd Earl of Southampton\"><PERSON>, 3rd Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton\" title=\"<PERSON>, 3rd Earl of Southampton\"><PERSON>, 3rd Earl of Southampton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire\" title=\"Lord Lieutenant of Hampshire\">Lord Lieutenant of Hampshire</a> (b. 1573)", "links": [{"title": "<PERSON>, 3rd Earl of Southampton", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Southampton"}, {"title": "Lord Lieutenant of Hampshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Hampshire"}]}, {"year": "1644", "text": "<PERSON>, Spanish author and playwright (b. 1579)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Luis_V%C3%A9lez_de_Guevara\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_V%C3%A9<PERSON>z_de_Guevara\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (b. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_V%C3%A9le<PERSON>_<PERSON>_<PERSON>ra"}]}, {"year": "1659", "text": "<PERSON><PERSON><PERSON>, Indian commander", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, Indian commander", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON><PERSON> (general)\"><PERSON><PERSON><PERSON></a>, Indian commander", "links": [{"title": "<PERSON><PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(general)"}]}, {"year": "1673", "text": "<PERSON><PERSON><PERSON>, King of Poland (b. 1640)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Korybut_Wi%C5%9Bniowiecki\" title=\"<PERSON><PERSON><PERSON>iśniowiecki\"><PERSON><PERSON><PERSON></a>, King of Poland (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>%C5%82_Korybut_Wi%C5%9Bniowiecki\" title=\"<PERSON><PERSON><PERSON> Wiśniowiecki\"><PERSON><PERSON><PERSON>wi<PERSON></a>, King of Poland (b. 1640)", "links": [{"title": "<PERSON><PERSON><PERSON>śniowieck<PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_Korybut_Wi%C5%9Bniowiecki"}]}, {"year": "1727", "text": "<PERSON><PERSON><PERSON>, French-American sailor and explorer (b. 1659)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American sailor and explorer (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American sailor and explorer (b. 1659)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON><PERSON><PERSON>, Russian admiral (b. 1661)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian admiral (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian admiral (b. 1661)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1772", "text": "<PERSON>, Portuguese poet and author (b. 1724)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Pedro_Co<PERSON>_Gar%C3%A7%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Portuguese poet and author (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_Gar%C3%A7%C3%A3o\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, Portuguese poet and author (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Correia_Gar%C3%A7%C3%A3o"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON><PERSON>, American tribal chief (b. 1720)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>tal<PERSON>_(Shawnee_leader)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Shawnee leader)\"><PERSON><PERSON><PERSON><PERSON></a>, American tribal chief (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(Shawnee_leader)\" title=\"<PERSON><PERSON><PERSON><PERSON> (Shawnee leader)\"><PERSON><PERSON><PERSON><PERSON></a>, American tribal chief (b. 1720)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (Shawnee leader)", "link": "https://wikipedia.org/wiki/<PERSON>rnstalk_(<PERSON><PERSON>_leader)"}]}, {"year": "1808", "text": "<PERSON>, 1st Baron <PERSON>, Irish-born English general and politician, 21st Governor General of Canada (b. 1724)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_Dorchester\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-born English general and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_Do<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-born English general and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1724)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1852", "text": "<PERSON>, English scientist (b. 1790)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scientist (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Swiss-American captain in Confederate army, commandant of Andersonville Prison (b. 1823)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American captain in Confederate army, commandant of <a href=\"https://wikipedia.org/wiki/Andersonville_National_Historic_Site\" class=\"mw-redirect\" title=\"Andersonville National Historic Site\">Andersonville Prison</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American captain in Confederate army, commandant of <a href=\"https://wikipedia.org/wiki/Andersonville_National_Historic_Site\" class=\"mw-redirect\" title=\"Andersonville National Historic Site\">Andersonville Prison</a> (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}, {"title": "Andersonville National Historic Site", "link": "https://wikipedia.org/wiki/Andersonville_National_Historic_Site"}]}, {"year": "1869", "text": "<PERSON>, American general (b. 1784)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wool\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wool\" title=\"<PERSON>ool\"><PERSON></a>, American general (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Wool"}]}, {"year": "1873", "text": "<PERSON>, Welsh musician and folklorist (b. circa 1794)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh musician and folklorist (b. circa 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh musician and folklorist (b. circa 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German-American carpenter and activist (b. 1864)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American carpenter and activist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American carpenter and activist (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, French poet and educator (b. 1854)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and educator (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian poet and educator (b. 1863)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and educator (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and educator (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Evans"}]}, {"year": "1928", "text": "<PERSON>, German dancer (b. 1899)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German dancer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German dancer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, French general and explorer (b. 1856)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and explorer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and explorer (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, founder of the Republic of Turkey, Turkish field marshal and statesman, 1st President of Turkey (b. 1881)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Republic of Turkey</a>, <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turkish</a> field marshal and statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Republic of Turkey</a>, <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turkish</a> field marshal and statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Atat%C3%BCrk"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Turkish people", "link": "https://wikipedia.org/wiki/Turkish_people"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1941", "text": "<PERSON>, Canadian botanist and geneticist (b. 1862)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian botanist and geneticist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian botanist and geneticist (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, physicist (b. 1903) ", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, physicist (b. 1903) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, physicist (b. 1903) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Swiss gymnast (b. 1856)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss gymnast (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss gymnast (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American author and journalist (b. 1900)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Austrian gymnast and engineer (b. 1875)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian gymnast and engineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian gymnast and engineer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American computer scientist (b. 1911)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Kl%C3%A1ra_D%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American computer scientist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kl%C3%A1ra_D%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-American computer scientist (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kl%C3%A1ra_D%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American author and academic (b. 1909)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian-American captain (b. 1912)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American captain (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American captain (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Ukrainian-Russian general and politician, 4th Head of State of the Soviet Union (b. 1906)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian general and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of the Soviet Union</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Russian general and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of the Soviet Union</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}]}, {"year": "1984", "text": "<PERSON>, Australian author (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Filipino actor and politician (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>_<PERSON>_Rosa\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>_<PERSON>_Rosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor and politician (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rogel<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English jockey and manager (b. 1904)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey and manager (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey and manager (b. 1904)", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Bangladeshi activist (b. 1961)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi activist (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi activist (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Cuban baseball player and manager (b. 1943)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Aurelio_Monteagudo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban baseball player and manager (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rel<PERSON>_Monteagudo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban baseball player and manager (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_Monteagudo"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Brazilian physicist and academic (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physicist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physicist and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_<PERSON>berg"}]}, {"year": "1991", "text": "<PERSON>, English toy-maker and businesswoman (b. 1899)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English toy-maker and businesswoman (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English toy-maker and businesswoman (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American football player and wrestler (b. 1929)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bruiser\" title=\"<PERSON> the Bruiser\"><PERSON></a>, American football player and wrestler (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bru<PERSON>\" title=\"<PERSON> the Bruiser\"><PERSON></a>, American football player and wrestler (b. 1929)", "links": [{"title": "<PERSON> the Bruiser", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and poet (b. 1904)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Kuvempu\" title=\"Kuvempu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuvempu\" title=\"Kuvempu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and poet (b. 1904)", "links": [{"title": "Kuvempu", "link": "https://wikipedia.org/wiki/Kuvempu"}]}, {"year": "1994", "text": "<PERSON>, American singer, pianist, and actress (b. 1920)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, pianist, and actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, pianist, and actress (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Nigerian author and activist (b. 1941)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author and activist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian author and activist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wa"}]}, {"year": "1998", "text": "<PERSON>, English actress (b. 1936)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician, 171st Prime Minister of Greece (b. 1919)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>os_And<PERSON>ulos\" title=\"<PERSON><PERSON><PERSON> Androut<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 171st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_And<PERSON>\" title=\"<PERSON><PERSON><PERSON> Androut<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, 171st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adamantios_And<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "2000", "text": "<PERSON>, French general and politician, 153rd Prime Minister of France (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 153rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 153rd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Prime Ministers of France\">Prime Minister of France</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_France"}]}, {"year": "2001", "text": "<PERSON>, American novelist, essayist, and poet (b. 1935)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, French actor, director, and screenwriter (b. 1921)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Zimbabwean minister and politician, 1st President of Zimbabwe (b. 1936)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Canaan_Banana\" title=\"Canaan Banana\"><PERSON><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canaan_Banana\" title=\"Canaan Banana\"><PERSON><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Canaan_Banana"}, {"title": "President of Zimbabwe", "link": "https://wikipedia.org/wiki/President_of_Zimbabwe"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, American journalist and talk show host (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\">Ir<PERSON></a>, American journalist and talk show host (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\">Ir<PERSON></a>, American journalist and talk show host (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Filipino-American singer and actress (b. 1907)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American singer and actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American singer and actress (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and manager (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/%C5%9Eeref_G%C3%B6rkey\" title=\"<PERSON>eref Görkey\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9Eeref_G%C3%B6rkey\" title=\"<PERSON>eref Görkey\"><PERSON><PERSON><PERSON></a>, Turkish footballer and manager (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%9Eeref_G%C3%B6rkey"}]}, {"year": "2006", "text": "<PERSON>, English actress and singer (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Dutch mathematician and computer scientist (b. 1954)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Fokko du Cloux\"><PERSON><PERSON><PERSON></a>, Dutch mathematician and computer scientist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Fokko du Cloux\"><PERSON><PERSON><PERSON></a>, Dutch mathematician and computer scientist (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and producer (b. 1966)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American boxer and actor (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Palance\"><PERSON></a>, American boxer and actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Palance\"><PERSON></a>, American boxer and actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician (b. 1962)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American author, critic, and academic (b. 1908)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, critic, and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American actress (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Day\" title=\"Laraine Day\"><PERSON><PERSON></a>, American actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ine_Day\" title=\"Laraine Day\"><PERSON><PERSON></a>, American actress (b. 1920)", "links": [{"title": "Laraine Day", "link": "https://wikipedia.org/wiki/Laraine_Day"}]}, {"year": "2007", "text": "<PERSON>, American engineer and politician (b. 1907)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American novelist and essayist (b. 1923)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Mailer\"><PERSON></a>, American novelist and essayist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Mailer\"><PERSON></a>, American novelist and essayist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Belgian singer and poet (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer and poet (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer and poet (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Japanese mathematician and academic (b. 1915)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_It%C3%B4\" title=\"<PERSON><PERSON><PERSON> Itô\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_It%C3%B4\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and academic (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kiyosi_It%C3%B4"}]}, {"year": "2009", "text": "<PERSON>, German footballer (b. 1977)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American spree killer (b. 1960)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American spree killer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American spree killer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Italian-American actor, producer, and production manager (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor, producer, and production manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor, producer, and production manager (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American soldier and politician (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Czech poet (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech poet (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech poet (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and judge (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Japanese actress (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch lawyer and politician, Dutch Minister of Economic Affairs (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economic_Affairs_(Netherlands)\" title=\"Ministry of Economic Affairs (Netherlands)\">Dutch Minister of Economic Affairs</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Economic_Affairs_(Netherlands)\" title=\"Ministry of Economic Affairs (Netherlands)\">Dutch Minister of Economic Affairs</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of Economic Affairs (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Economic_Affairs_(Netherlands)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian author (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Vijaydan_<PERSON>\" title=\"Vijaydan <PERSON>\"><PERSON><PERSON></a>, Indian author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vijaydan_<PERSON>\" title=\"Vijaydan <PERSON>\"><PERSON><PERSON></a>, Indian author (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vijaydan_Detha"}]}, {"year": "2013", "text": "<PERSON>, Australian neurosurgeon (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(neurosurgeon)\" title=\"<PERSON> (neurosurgeon)\"><PERSON></a>, Australian neurosurgeon (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(neurosurgeon)\" title=\"<PERSON> (neurosurgeon)\"><PERSON></a>, Australian neurosurgeon (b. 1922)", "links": [{"title": "<PERSON> (neurosurgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(neurosurgeon)"}]}, {"year": "2013", "text": "<PERSON>, American ice hockey player and coach (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Swiss poet and translator (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and translator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss poet and translator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Croatian soldier and politician, 1st Croatia Minister of the Interior (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)\" title=\"Ministry of the Interior (Croatia)\">Croatia Minister of the Interior</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)\" title=\"Ministry of the Interior (Croatia)\">Croatia Minister of the Interior</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of the Interior (Croatia)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_(Croatia)"}]}, {"year": "2014", "text": "<PERSON>, Australian lawyer and politician, 34th Premier of Queensland (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON> \"<PERSON>\" <PERSON>, American surfer and physician (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Dorian_%22Doc%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American surfer and physician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Doc%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American surfer and physician (b. 1921)", "links": [{"title": "<PERSON> \"Doc\" <PERSON>", "link": "https://wikipedia.org/wiki/Dorian_%22Doc%22_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American ice hockey player and coach (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>frew\" title=\"<PERSON> Renfrew\"><PERSON></a>, American ice hockey player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Renfrew\" title=\"<PERSON>frew\"><PERSON></a>, American ice hockey player and coach (b. 1924)", "links": [{"title": "Al Renfrew", "link": "https://wikipedia.org/wiki/Al_Renfrew"}]}, {"year": "2015", "text": "<PERSON>, American computer scientist, physicist, and engineer, founded the Amdahl Corporation (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, physicist, and engineer, founded the <a href=\"https://wikipedia.org/wiki/Amdahl_Corporation\" title=\"Amdahl Corporation\">Amdahl Corporation</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, physicist, and engineer, founded the <a href=\"https://wikipedia.org/wiki/Amdahl_Corporation\" title=\"Amdahl Corporation\">Amdahl Corporation</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amdahl Corporation", "link": "https://wikipedia.org/wiki/Amdahl_Corporation"}]}, {"year": "2015", "text": "<PERSON>, Irish jockey and trainer (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey and trainer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey and trainer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "2015", "text": "<PERSON>, French philosopher and author (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Gluck<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_G<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>n"}]}, {"year": "2015", "text": "<PERSON>, German soldier, economist, and politician, 5th Chancellor of Germany (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, economist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, economist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter, pianist, and producer (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Chief Palestinian negotiator (b. 1955)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>eb <PERSON>\"><PERSON><PERSON></a>, Chief Palestinian negotiator (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chief Palestinian negotiator (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eb_<PERSON>at"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Slovak singer, songwriter and guitarist (b. 1952)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Miroslav_%C5%BDbirka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak singer, songwriter and guitarist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miroslav_%C5%BDbirka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak singer, songwriter and guitarist (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_%C5%BDbirka"}]}, {"year": "2022", "text": "<PERSON>, American actor and voice actor, longtime voice of <PERSON> (b. 1955)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice actor, longtime voice of <a href=\"https://wikipedia.org/wiki/Batman:_The_Animated_Series\" title=\"Batman: The Animated Series\"><PERSON></a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice actor, longtime voice of <a href=\"https://wikipedia.org/wiki/Batman:_The_Animated_Series\" title=\"Batman: The Animated Series\"><PERSON></a> (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Batman: The Animated Series", "link": "https://wikipedia.org/wiki/Batman:_The_Animated_Series"}]}, {"year": "2024", "text": "<PERSON>, American novelist (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist (b. 1948)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}]}}