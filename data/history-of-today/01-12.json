{"date": "January 12", "url": "https://wikipedia.org/wiki/January_12", "data": {"Events": [{"year": "475", "text": "Byzantine Emperor <PERSON><PERSON> is forced to flee his capital at Constantinople, and his general, <PERSON><PERSON><PERSON> gains control of the empire.", "html": "475 - <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> is forced to flee his capital at <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, and his general, <a href=\"https://wikipedia.org/wiki/Basiliscus\" title=\"Basiliscus\">Basiliscus</a> gains control of the empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a> is forced to flee his capital at <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, and his general, <a href=\"https://wikipedia.org/wiki/Basiliscus\" title=\"Basiliscus\">Basiliscus</a> gains control of the empire.", "links": [{"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Basiliscus", "link": "https://wikipedia.org/wiki/Basiliscus"}]}, {"year": "1528", "text": "<PERSON> of Sweden is crowned King of Sweden, having already reigned since his election in June 1523.", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> is crowned King of Sweden, having already reigned since his election in June 1523.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> is crowned King of Sweden, having already reigned since his election in June 1523.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1554", "text": "<PERSON><PERSON><PERSON><PERSON>, who would go on to assemble the largest empire in the history of Southeast Asia, is crowned King of Burma.", "html": "1554 - <a href=\"https://wikipedia.org/wiki/Bayinnaung\" title=\"Bayinnaung\">Bayinnaung</a>, who would go on to assemble the <a href=\"https://wikipedia.org/wiki/Toungoo_dynasty\" title=\"Toungoo dynasty\">largest empire</a> in the history of Southeast Asia, is crowned <a href=\"https://wikipedia.org/wiki/List_of_Burmese_monarchs\" title=\"List of Burmese monarchs\">King of Burma</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bayinnaung\" title=\"Bayinnaung\">Bayinnaung</a>, who would go on to assemble the <a href=\"https://wikipedia.org/wiki/Toungoo_dynasty\" title=\"Toungoo dynasty\">largest empire</a> in the history of Southeast Asia, is crowned <a href=\"https://wikipedia.org/wiki/List_of_Burmese_monarchs\" title=\"List of Burmese monarchs\">King of Burma</a>.", "links": [{"title": "Bayinnaung", "link": "https://wikipedia.org/wiki/Bayinnaung"}, {"title": "Toungoo dynasty", "link": "https://wikipedia.org/wiki/Toungoo_dynasty"}, {"title": "List of Burmese monarchs", "link": "https://wikipedia.org/wiki/List_of_Burmese_monarchs"}]}, {"year": "1616", "text": "The city of Belém, Brazil is founded on the Amazon River delta by Portuguese captain <PERSON>.", "html": "1616 - The city of <a href=\"https://wikipedia.org/wiki/Bel%C3%A9m\" title=\"Belém\">Belém</a>, Brazil is founded on the <a href=\"https://wikipedia.org/wiki/Amazon_River\" title=\"Amazon River\">Amazon River</a> delta by Portuguese captain <a href=\"https://wikipedia.org/wiki/Francisco_Caldeira_Castelo_Branco\" title=\"Francisco <PERSON> Castelo Branco\"><PERSON></a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Bel%C3%A9m\" title=\"Belém\">Belém</a>, Brazil is founded on the <a href=\"https://wikipedia.org/wiki/Amazon_River\" title=\"Amazon River\">Amazon River</a> delta by Portuguese captain <a href=\"https://wikipedia.org/wiki/Francisco_Caldeira_Castelo_Branco\" title=\"Francisco Caldeira Castelo Branco\"><PERSON></a>.", "links": [{"title": "Belém", "link": "https://wikipedia.org/wiki/Bel%C3%A9m"}, {"title": "Amazon River", "link": "https://wikipedia.org/wiki/Amazon_River"}, {"title": "<PERSON> Bran<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Caldeira_Castelo_Branco"}]}, {"year": "1792", "text": "Federalist <PERSON> appointed first U.S. minister to Britain.", "html": "1792 - Federalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> appointed first U.S. minister to Britain.", "no_year_html": "Federalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> appointed first U.S. minister to Britain.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>'s scheme to defend St Mary's Church, Reculver, founded in 669, from coastal erosion is abandoned in favour of demolition, despite the church being an exemplar of Anglo-Saxon architecture and sculpture.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON></a>'s scheme to defend <a href=\"https://wikipedia.org/wiki/St_Mary%27s_Church,_Reculver\" title=\"St Mary's Church, Reculver\">St Mary's Church, Reculver</a>, founded in 669, from <a href=\"https://wikipedia.org/wiki/Coastal_erosion\" title=\"Coastal erosion\">coastal erosion</a> is abandoned in favour of demolition, despite the church being an exemplar of <a href=\"https://wikipedia.org/wiki/Anglo-Saxon_architecture\" title=\"Anglo-Saxon architecture\">Anglo-Saxon architecture</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Saxon_art\" title=\"Anglo-Saxon art\">sculpture</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON></a>'s scheme to defend <a href=\"https://wikipedia.org/wiki/St_Mary%27s_Church,_Reculver\" title=\"St Mary's Church, Reculver\">St Mary's Church, Reculver</a>, founded in 669, from <a href=\"https://wikipedia.org/wiki/Coastal_erosion\" title=\"Coastal erosion\">coastal erosion</a> is abandoned in favour of demolition, despite the church being an exemplar of <a href=\"https://wikipedia.org/wiki/Anglo-Saxon_architecture\" title=\"Anglo-Saxon architecture\">Anglo-Saxon architecture</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Saxon_art\" title=\"Anglo-Saxon art\">sculpture</a>.", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_the_Elder"}, {"title": "St Mary's Church, Reculver", "link": "https://wikipedia.org/wiki/St_Mary%27s_Church,_Reculver"}, {"title": "Coastal erosion", "link": "https://wikipedia.org/wiki/Coastal_erosion"}, {"title": "Anglo-Saxon architecture", "link": "https://wikipedia.org/wiki/Anglo-Saxon_architecture"}, {"title": "Anglo-Saxon art", "link": "https://wikipedia.org/wiki/Anglo-Saxon_art"}]}, {"year": "1808", "text": "The organizational meeting leading to the creation of the Wernerian Natural History Society, a former Scottish learned society, is held in Edinburgh.", "html": "1808 - The organizational meeting leading to the creation of the <a href=\"https://wikipedia.org/wiki/Wernerian_Natural_History_Society\" title=\"Wernerian Natural History Society\">Wernerian Natural History Society</a>, a former Scottish learned society, is held in Edinburgh.", "no_year_html": "The organizational meeting leading to the creation of the <a href=\"https://wikipedia.org/wiki/Wernerian_Natural_History_Society\" title=\"Wernerian Natural History Society\">Wernerian Natural History Society</a>, a former Scottish learned society, is held in Edinburgh.", "links": [{"title": "Wernerian Natural History Society", "link": "https://wikipedia.org/wiki/Wernerian_Natural_History_Society"}]}, {"year": "1848", "text": "The Palermo rising takes place in Sicily against the Bourbon Kingdom of the Two Sicilies.", "html": "1848 - The <a href=\"https://wikipedia.org/wiki/Sicilian_revolution_of_1848\" title=\"Sicilian revolution of 1848\">Palermo rising</a> takes place in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a> against the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">Bourbon</a> <a href=\"https://wikipedia.org/wiki/Kingdom_of_the_Two_Sicilies\" title=\"Kingdom of the Two Sicilies\">Kingdom of the Two Sicilies</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sicilian_revolution_of_1848\" title=\"Sicilian revolution of 1848\">Palermo rising</a> takes place in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a> against the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">Bourbon</a> <a href=\"https://wikipedia.org/wiki/Kingdom_of_the_Two_Sicilies\" title=\"Kingdom of the Two Sicilies\">Kingdom of the Two Sicilies</a>.", "links": [{"title": "Sicilian revolution of 1848", "link": "https://wikipedia.org/wiki/Sicilian_revolution_of_1848"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}, {"title": "House of Bourbon", "link": "https://wikipedia.org/wiki/House_of_Bourbon"}, {"title": "Kingdom of the Two Sicilies", "link": "https://wikipedia.org/wiki/Kingdom_of_the_Two_Sicilies"}]}, {"year": "1866", "text": "The Royal Aeronautical Society is formed in London.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/Royal_Aeronautical_Society\" title=\"Royal Aeronautical Society\">Royal Aeronautical Society</a> is formed in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Aeronautical_Society\" title=\"Royal Aeronautical Society\">Royal Aeronautical Society</a> is formed in London.", "links": [{"title": "Royal Aeronautical Society", "link": "https://wikipedia.org/wiki/Royal_Aeronautical_Society"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON> <PERSON> is crowned Emperor of Ethiopia in Axum, the first imperial coronation in that city in over 200 years.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"<PERSON><PERSON><PERSON> IV\"><PERSON><PERSON><PERSON> IV</a> is crowned Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> in <a href=\"https://wikipedia.org/wiki/Axum\" title=\"Axum\">Axum</a>, the first imperial coronation in that city in over 200 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"<PERSON><PERSON><PERSON> IV\"><PERSON><PERSON><PERSON> IV</a> is crowned Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a> in <a href=\"https://wikipedia.org/wiki/Axum\" title=\"Axum\">Axum</a>, the first imperial coronation in that city in over 200 years.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Axum", "link": "https://wikipedia.org/wiki/Axum"}]}, {"year": "1895", "text": "The National Trust is founded in the United Kingdom.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/National_Trust_for_Places_of_Historic_Interest_or_Natural_Beauty\" class=\"mw-redirect\" title=\"National Trust for Places of Historic Interest or Natural Beauty\">The National Trust</a> is founded in the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Trust_for_Places_of_Historic_Interest_or_Natural_Beauty\" class=\"mw-redirect\" title=\"National Trust for Places of Historic Interest or Natural Beauty\">The National Trust</a> is founded in the United Kingdom.", "links": [{"title": "National Trust for Places of Historic Interest or Natural Beauty", "link": "https://wikipedia.org/wiki/National_Trust_for_Places_of_Historic_Interest_or_Natural_Beauty"}]}, {"year": "1911", "text": "The University of the Philippines College of Law is established.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/University_of_the_Philippines_College_of_Law\" title=\"University of the Philippines College of Law\">University of the Philippines College of Law</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/University_of_the_Philippines_College_of_Law\" title=\"University of the Philippines College of Law\">University of the Philippines College of Law</a> is established.", "links": [{"title": "University of the Philippines College of Law", "link": "https://wikipedia.org/wiki/University_of_the_Philippines_College_of_Law"}]}, {"year": "1915", "text": "The United States House of Representatives rejects a proposed constitutional amendment to require states to give women the right to vote.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> rejects a proposed constitutional amendment to require states to give <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">women the right to vote</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> rejects a proposed constitutional amendment to require states to give <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">women the right to vote</a>.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Women's suffrage", "link": "https://wikipedia.org/wiki/Women%27s_suffrage"}]}, {"year": "1916", "text": "<PERSON> and <PERSON> become the first German aviators to earn the Pour le Mérite, receive the German Empire's highest military award, for achieving eight aerial victories each over Allied aircraft.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first German aviators to earn the <a href=\"https://wikipedia.org/wiki/Pour_le_M%C3%A9rite\" title=\"Pour le Mérite\"><PERSON><PERSON> le Mérite</a>, receive the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>'s highest military award, for achieving eight aerial victories each over <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first German aviators to earn the <a href=\"https://wikipedia.org/wiki/Pour_le_M%C3%A9rite\" title=\"Pour le Mérite\"><PERSON><PERSON> le Mérite</a>, receive the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>'s highest military award, for achieving eight aerial victories each over <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> aircraft.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pour le Mérite", "link": "https://wikipedia.org/wiki/Pour_le_M%C3%A9rite"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1918", "text": "The Minnie Pit Disaster coal mining accident occurs in Halmer End, Staffordshire, in which 155 men and boys die.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Minnie_Pit_Disaster\" title=\"Minnie Pit Disaster\">Minnie Pit Disaster</a> coal mining accident occurs in Halmer End, Staffordshire, in which 155 men and boys die.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Minnie_Pit_Disaster\" title=\"Minnie Pit Disaster\">Minnie Pit Disaster</a> coal mining accident occurs in Halmer End, Staffordshire, in which 155 men and boys die.", "links": [{"title": "Minnie Pit Disaster", "link": "https://wikipedia.org/wiki/Minnie_Pit_Disaster"}]}, {"year": "1932", "text": "<PERSON><PERSON> becomes the first woman elected to the United States Senate.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Hattie_Caraway\" title=\"Hattie Caraway\"><PERSON><PERSON></a> becomes the first woman elected to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hattie_Caraway\" title=\"Hattie Caraway\"><PERSON><PERSON></a> becomes the first woman elected to the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>.", "links": [{"title": "<PERSON><PERSON>way", "link": "https://wikipedia.org/wiki/Hattie_Caraway"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1933", "text": "Casas Viejas incident: 22 peasants killed by the Security and Assault Corps in Casas Viejas, Spain.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Casas_Viejas_incident\" title=\"Casas Viejas incident\">Casas Viejas incident</a>: 22 peasants killed by the <a href=\"https://wikipedia.org/wiki/Security_and_Assault_Corps\" title=\"Security and Assault Corps\">Security and Assault Corps</a> in <a href=\"https://wikipedia.org/wiki/Casas_Viejas\" class=\"mw-redirect\" title=\"Casas Viejas\">Casas Viejas</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Casas_Viejas_incident\" title=\"Casas Viejas incident\">Casas Viejas incident</a>: 22 peasants killed by the <a href=\"https://wikipedia.org/wiki/Security_and_Assault_Corps\" title=\"Security and Assault Corps\">Security and Assault Corps</a> in <a href=\"https://wikipedia.org/wiki/Casas_Viejas\" class=\"mw-redirect\" title=\"Casas Viejas\">Casas Viejas</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "links": [{"title": "Casas Viejas incident", "link": "https://wikipedia.org/wiki/Casas_Viejas_incident"}, {"title": "Security and Assault Corps", "link": "https://wikipedia.org/wiki/Security_and_Assault_Corps"}, {"title": "Casas Viejas", "link": "https://wikipedia.org/wiki/Casas_Viejas"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1942", "text": "World War II: United States President <PERSON> creates the National War Labor Board.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> creates the <a href=\"https://wikipedia.org/wiki/National_War_Labor_Board_(1942%E2%80%931945)\" title=\"National War Labor Board (1942-1945)\">National War Labor Board</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> creates the <a href=\"https://wikipedia.org/wiki/National_War_Labor_Board_(1942%E2%80%931945)\" title=\"National War Labor Board (1942-1945)\">National War Labor Board</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National War Labor Board (1942-1945)", "link": "https://wikipedia.org/wiki/National_War_Labor_Board_(1942%E2%80%931945)"}]}, {"year": "1945", "text": "World War II: The Red Army begins the Vistula-Oder Offensive.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive\" class=\"mw-redirect\" title=\"Vistula-Oder Offensive\">Vistula-Oder Offensive</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> begins the <a href=\"https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive\" class=\"mw-redirect\" title=\"Vistula-Oder Offensive\">Vistula-Oder Offensive</a>.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Vistula-Oder Offensive", "link": "https://wikipedia.org/wiki/Vistula%E2%80%93Oder_Offensive"}]}, {"year": "1955", "text": "A <PERSON> 2-0-2 and <PERSON>-3 collide over Boone County, Kentucky, killing 15 people.", "html": "1955 - A <a href=\"https://wikipedia.org/wiki/<PERSON>_2-0-2\" title=\"<PERSON> 2-0-2\"><PERSON> 2-0-2</a> and <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> <a href=\"https://wikipedia.org/wiki/1955_Cincinnati_mid-air_collision\" title=\"1955 Cincinnati mid-air collision\">collide</a> over <a href=\"https://wikipedia.org/wiki/Boone_County,_Kentucky\" title=\"Boone County, Kentucky\">Boone County, Kentucky</a>, killing 15 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON>_2-0-2\" title=\"<PERSON> 2-0-2\"><PERSON> 2-0-2</a> and <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> <a href=\"https://wikipedia.org/wiki/1955_Cincinnati_mid-air_collision\" title=\"1955 Cincinnati mid-air collision\">collide</a> over <a href=\"https://wikipedia.org/wiki/Boone_County,_Kentucky\" title=\"Boone County, Kentucky\">Boone County, Kentucky</a>, killing 15 people.", "links": [{"title": "<PERSON> 2-0-2", "link": "https://wikipedia.org/wiki/<PERSON>_2-0-2"}, {"title": "Douglas DC-3", "link": "https://wikipedia.org/wiki/Douglas_DC-3"}, {"title": "1955 Cincinnati mid-air collision", "link": "https://wikipedia.org/wiki/1955_Cincinnati_mid-air_collision"}, {"title": "Boone County, Kentucky", "link": "https://wikipedia.org/wiki/Boone_County,_Kentucky"}]}, {"year": "1962", "text": "Vietnam War: Operation Chopper, the first American combat mission and first American helicopter assault in the war, takes place.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Chopper_(Vietnam)\" title=\"Operation Chopper (Vietnam)\">Operation Chopper</a>, the first American combat mission and first American helicopter assault in the war, takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Operation_Chopper_(Vietnam)\" title=\"Operation Chopper (Vietnam)\">Operation Chopper</a>, the first American combat mission and first American helicopter assault in the war, takes place.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Chopper (Vietnam)", "link": "https://wikipedia.org/wiki/Operation_Chopper_(Vietnam)"}]}, {"year": "1964", "text": "Rebels in Zanzibar begin a revolt known as the Zanzibar Revolution and proclaim a republic.", "html": "1964 - Rebels in <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> begin a revolt known as the <a href=\"https://wikipedia.org/wiki/Zanzibar_Revolution\" title=\"Zanzibar Revolution\">Zanzibar Revolution</a> and proclaim a republic.", "no_year_html": "Rebels in <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a> begin a revolt known as the <a href=\"https://wikipedia.org/wiki/Zanzibar_Revolution\" title=\"Zanzibar Revolution\">Zanzibar Revolution</a> and proclaim a republic.", "links": [{"title": "Zanzibar", "link": "https://wikipedia.org/wiki/Zanzibar"}, {"title": "Zanzibar Revolution", "link": "https://wikipedia.org/wiki/Zanzibar_Revolution"}]}, {"year": "1966", "text": "<PERSON> states that the United States should stay in South Vietnam until Communist aggression there is ended.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> states that the United States should stay in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> until <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> aggression there is ended.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> states that the United States should stay in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> until <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> aggression there is ended.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}]}, {"year": "1967", "text": "Dr. <PERSON> becomes the first person to be cryonically preserved with intent of future resuscitation.", "html": "1967 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a> becomes the first person to be <a href=\"https://wikipedia.org/wiki/Cryonics\" title=\"Cryonics\">cryonically preserved</a> with intent of future resuscitation.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a> becomes the first person to be <a href=\"https://wikipedia.org/wiki/Cryonics\" title=\"Cryonics\">cryonically preserved</a> with intent of future resuscitation.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cryonics", "link": "https://wikipedia.org/wiki/Cryonics"}]}, {"year": "1969", "text": "The New York Jets of the American Football League defeat the Baltimore Colts of the National Football League to win Super Bowl III in what is considered to be one of the greatest upsets in sports history.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/New_York_Jets\" title=\"New York Jets\">New York Jets</a> of the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a> defeat the <a href=\"https://wikipedia.org/wiki/History_of_the_Baltimore_Colts\" class=\"mw-redirect\" title=\"History of the Baltimore Colts\">Baltimore Colts</a> of the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> to win <a href=\"https://wikipedia.org/wiki/Super_Bowl_III\" title=\"Super Bowl III\">Super Bowl III</a> in what is considered to be one of the greatest upsets in sports history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Jets\" title=\"New York Jets\">New York Jets</a> of the <a href=\"https://wikipedia.org/wiki/American_Football_League\" title=\"American Football League\">American Football League</a> defeat the <a href=\"https://wikipedia.org/wiki/History_of_the_Baltimore_Colts\" class=\"mw-redirect\" title=\"History of the Baltimore Colts\">Baltimore Colts</a> of the <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> to win <a href=\"https://wikipedia.org/wiki/Super_Bowl_III\" title=\"Super Bowl III\">Super Bowl III</a> in what is considered to be one of the greatest upsets in sports history.", "links": [{"title": "New York Jets", "link": "https://wikipedia.org/wiki/New_York_Jets"}, {"title": "American Football League", "link": "https://wikipedia.org/wiki/American_Football_League"}, {"title": "History of the Baltimore Colts", "link": "https://wikipedia.org/wiki/History_of_the_Baltimore_Colts"}, {"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}, {"title": "Super Bowl III", "link": "https://wikipedia.org/wiki/Super_Bowl_III"}]}, {"year": "1970", "text": "Biafra capitulates, ending the Nigerian Civil War.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Biafra</a> capitulates, ending the <a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">Nigerian Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Biafra\" title=\"Biafra\">Biafra</a> capitulates, ending the <a href=\"https://wikipedia.org/wiki/Nigerian_Civil_War\" title=\"Nigerian Civil War\">Nigerian Civil War</a>.", "links": [{"title": "Biafra", "link": "https://wikipedia.org/wiki/Biafra"}, {"title": "Nigerian Civil War", "link": "https://wikipedia.org/wiki/Nigerian_Civil_War"}]}, {"year": "1971", "text": "The Harrisburg Seven: Rev. <PERSON> and five other activists are indicted on charges of conspiring to kidnap <PERSON> and of plotting to blow up the heating tunnels of federal buildings in Washington, D.C.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Harrisburg_Seven\" title=\"Harrisburg Seven\">Harrisburg Seven</a>: Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five other activists are indicted on charges of conspiring to kidnap <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and of plotting to blow up the heating tunnels of federal buildings in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Harrisburg_Seven\" title=\"Harrisburg Seven\">Harrisburg Seven</a>: Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and five other activists are indicted on charges of conspiring to kidnap <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and of plotting to blow up the heating tunnels of federal buildings in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "links": [{"title": "Harrisburg Seven", "link": "https://wikipedia.org/wiki/Harrisburg_Seven"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1976", "text": "The United Nations Security Council votes 11-1 to allow the Palestine Liberation Organization to participate in a Security Council debate (without voting rights).", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> votes 11-1 to allow the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> to participate in a Security Council debate (without voting rights).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> votes 11-1 to allow the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> to participate in a Security Council debate (without voting rights).", "links": [{"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}]}, {"year": "1986", "text": "Space Shuttle program: Congressman and future NASA Administrator <PERSON> lifts off from Kennedy Space Center aboard Columbia on mission STS-61-C as a payload specialist.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Congressman and future NASA Administrator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a> lifts off from <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> aboard <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\"><i>Columbia</i></a> on mission <a href=\"https://wikipedia.org/wiki/STS-61-C\" title=\"STS-61-C\">STS-61-C</a> as a <a href=\"https://wikipedia.org/wiki/Payload_specialist\" title=\"Payload specialist\">payload specialist</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: Congressman and future NASA Administrator <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a> lifts off from <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> aboard <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\"><i>Columbia</i></a> on mission <a href=\"https://wikipedia.org/wiki/STS-61-C\" title=\"STS-61-C\">STS-61-C</a> as a <a href=\"https://wikipedia.org/wiki/Payload_specialist\" title=\"Payload specialist\">payload specialist</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-61-C", "link": "https://wikipedia.org/wiki/STS-61-C"}, {"title": "Payload specialist", "link": "https://wikipedia.org/wiki/Payload_specialist"}]}, {"year": "1990", "text": "A seven-day pogrom breaks out against the Armenian civilian population of Baku, Azerbaijan, during which Armenians were beaten, tortured, murdered, and expelled from the city.", "html": "1990 - A <a href=\"https://wikipedia.org/wiki/Baku_pogrom\" title=\"Baku pogrom\">seven-day pogrom</a> breaks out against <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">the Armenian civilian population of Baku, Azerbaijan</a>, during which Armenians were beaten, tortured, murdered, and expelled from the city.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Baku_pogrom\" title=\"Baku pogrom\">seven-day pogrom</a> breaks out against <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">the Armenian civilian population of Baku, Azerbaijan</a>, during which Armenians were beaten, tortured, murdered, and expelled from the city.", "links": [{"title": "Baku pogrom", "link": "https://wikipedia.org/wiki/Baku_pogrom"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}]}, {"year": "1991", "text": "Persian Gulf War: An act of the U.S. Congress authorizes the use of American military force to drive Iraq out of Kuwait.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Persian Gulf War</a>: An act of the U.S. Congress authorizes the use of American military force to drive <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> out of <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Persian Gulf War</a>: An act of the U.S. Congress authorizes the use of American military force to drive <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> out of <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}, {"title": "Ba'athist Iraq", "link": "https://wikipedia.org/wiki/Ba%27athist_Iraq"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}]}, {"year": "1997", "text": "Space Shuttle program: Atlantis launches from the Kennedy Space Center on mission STS-81 to the Russian space station Mir, carrying astronaut <PERSON> for a four-month stay on board the station, replacing astronaut <PERSON>.", "html": "1997 - Space Shuttle program: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i> launches from the Kennedy Space Center on mission <a href=\"https://wikipedia.org/wiki/STS-81\" title=\"STS-81\">STS-81</a> to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>, carrying astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> for a four-month stay on board the station, replacing astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Space Shuttle program: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i> launches from the Kennedy Space Center on mission <a href=\"https://wikipedia.org/wiki/STS-81\" title=\"STS-81\">STS-81</a> to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>, carrying astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> for a four-month stay on board the station, replacing astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-81", "link": "https://wikipedia.org/wiki/STS-81"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "Nineteen European nations agree to forbid human cloning.", "html": "1998 - Nineteen European nations agree to forbid <a href=\"https://wikipedia.org/wiki/Human_cloning\" title=\"Human cloning\">human cloning</a>.", "no_year_html": "Nineteen European nations agree to forbid <a href=\"https://wikipedia.org/wiki/Human_cloning\" title=\"Human cloning\">human cloning</a>.", "links": [{"title": "Human cloning", "link": "https://wikipedia.org/wiki/Human_cloning"}]}, {"year": "2001", "text": "Downtown Disney opens to the public as part of the Disneyland Resort in Anaheim, California.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Downtown_Disney_(Disneyland_Resort)\" class=\"mw-redirect\" title=\"Downtown Disney (Disneyland Resort)\">Downtown Disney</a> opens to the public as part of the <a href=\"https://wikipedia.org/wiki/Disneyland_Resort\" title=\"Disneyland Resort\">Disneyland Resort</a> in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Downtown_Disney_(Disneyland_Resort)\" class=\"mw-redirect\" title=\"Downtown Disney (Disneyland Resort)\">Downtown Disney</a> opens to the public as part of the <a href=\"https://wikipedia.org/wiki/Disneyland_Resort\" title=\"Disneyland Resort\">Disneyland Resort</a> in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "links": [{"title": "Downtown Disney (Disneyland Resort)", "link": "https://wikipedia.org/wiki/Downtown_Disney_(Disneyland_Resort)"}, {"title": "Disneyland Resort", "link": "https://wikipedia.org/wiki/Disneyland_Resort"}, {"title": "Anaheim, California", "link": "https://wikipedia.org/wiki/Anaheim,_California"}]}, {"year": "2004", "text": "The world's largest ocean liner, RMS Queen Mary 2, makes its maiden voyage.", "html": "2004 - The world's largest <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a>, <a href=\"https://wikipedia.org/wiki/Queen_Mary_2\" title=\"Queen Mary 2\">RMS <i>Queen Mary 2</i></a>, makes its maiden voyage.", "no_year_html": "The world's largest <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a>, <a href=\"https://wikipedia.org/wiki/Queen_Mary_2\" title=\"Queen Mary 2\">RMS <i>Queen Mary 2</i></a>, makes its maiden voyage.", "links": [{"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "Queen Mary 2", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_2"}]}, {"year": "2005", "text": "Deep Impact launches from Cape Canaveral on a Delta II rocket.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Deep_Impact_(spacecraft)\" title=\"Deep Impact (spacecraft)\">Deep Impact</a> launches from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Canaveral</a> on a <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> rocket.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deep_Impact_(spacecraft)\" title=\"Deep Impact (spacecraft)\">Deep Impact</a> launches from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Canaveral</a> on a <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> rocket.", "links": [{"title": "Deep Impact (spacecraft)", "link": "https://wikipedia.org/wiki/Deep_Impact_(spacecraft)"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral"}, {"title": "Delta II", "link": "https://wikipedia.org/wiki/Delta_II"}]}, {"year": "2006", "text": "A stampede during the Stoning of the Devil ritual on the last day at the Hajj in Mina, Saudi Arabia, kills at least 362 Muslim pilgrims.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Incidents_during_the_Hajj\" title=\"Incidents during the Hajj\">A stampede</a> during the <a href=\"https://wikipedia.org/wiki/Stoning_of_the_Devil\" title=\"Stoning of the Devil\">Stoning of the Devil</a> ritual on the last day at the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a> in <a href=\"https://wikipedia.org/wiki/Mina,_Saudi_Arabia\" title=\"Mina, Saudi Arabia\">Mina, Saudi Arabia</a>, kills at least 362 Muslim <a href=\"https://wikipedia.org/wiki/Pilgrim\" class=\"mw-redirect\" title=\"Pilgrim\">pilgrims</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Incidents_during_the_Hajj\" title=\"Incidents during the Hajj\">A stampede</a> during the <a href=\"https://wikipedia.org/wiki/Stoning_of_the_Devil\" title=\"Stoning of the Devil\">Stoning of the Devil</a> ritual on the last day at the <a href=\"https://wikipedia.org/wiki/Hajj\" title=\"Hajj\">Hajj</a> in <a href=\"https://wikipedia.org/wiki/Mina,_Saudi_Arabia\" title=\"Mina, Saudi Arabia\">Mina, Saudi Arabia</a>, kills at least 362 Muslim <a href=\"https://wikipedia.org/wiki/Pilgrim\" class=\"mw-redirect\" title=\"Pilgrim\">pilgrims</a>.", "links": [{"title": "Incidents during the Hajj", "link": "https://wikipedia.org/wiki/Incidents_during_the_Hajj"}, {"title": "Stoning of the Devil", "link": "https://wikipedia.org/wiki/Stoning_of_the_Devil"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hajj"}, {"title": "Mina, Saudi Arabia", "link": "https://wikipedia.org/wiki/Mina,_Saudi_Arabia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pilgrim"}]}, {"year": "2007", "text": "Comet C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>), one of the brightest comets ever observed is at its zenith visible during the day.", "html": "2007 - Comet <a href=\"https://wikipedia.org/wiki/C/2006_P1_(<PERSON><PERSON><PERSON><PERSON><PERSON>)\" class=\"mw-redirect\" title=\"C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>)\">C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>)</a>, one of the brightest comets ever observed is at its zenith visible during the day.", "no_year_html": "Comet <a href=\"https://wikipedia.org/wiki/C/2006_P1_(<PERSON><PERSON><PERSON><PERSON><PERSON>)\" class=\"mw-redirect\" title=\"C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>)\">C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>)</a>, one of the brightest comets ever observed is at its zenith visible during the day.", "links": [{"title": "C/2006 P1 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/C/2006_P1_(<PERSON><PERSON><PERSON><PERSON><PERSON>)"}]}, {"year": "2010", "text": "An earthquake in Haiti occurs, killing between 220,000 and 300,000 people and destroying much of the capital Port-au-Prince.", "html": "2010 - An <a href=\"https://wikipedia.org/wiki/2010_Haiti_earthquake\" title=\"2010 Haiti earthquake\">earthquake in Haiti</a> occurs, killing between 220,000 and 300,000 people and destroying much of the capital <a href=\"https://wikipedia.org/wiki/Port-au-Prince\" title=\"Port-au-Prince\">Port-au-Prince</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2010_Haiti_earthquake\" title=\"2010 Haiti earthquake\">earthquake in Haiti</a> occurs, killing between 220,000 and 300,000 people and destroying much of the capital <a href=\"https://wikipedia.org/wiki/Port-au-Prince\" title=\"Port-au-Prince\">Port-au-Prince</a>.", "links": [{"title": "2010 Haiti earthquake", "link": "https://wikipedia.org/wiki/2010_Haiti_earthquake"}, {"title": "Port-au-Prince", "link": "https://wikipedia.org/wiki/Port-au-Prince"}]}, {"year": "2012", "text": "Violent protests occur in Bucharest, Romania, as two-day-old demonstrations continue against President <PERSON><PERSON><PERSON>'s economic austerity measures. Clashes are reported in numerous Romanian cities between protesters and law enforcement officers.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/2012_Romanian_protests\" title=\"2012 Romanian protests\">Violent protests</a> occur in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania, as two-day-old demonstrations continue against President <a href=\"https://wikipedia.org/wiki/Traian_B%C4%83sescu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s economic <a href=\"https://wikipedia.org/wiki/Austerity\" title=\"Austerity\">austerity</a> measures. Clashes are reported in numerous Romanian cities between protesters and law enforcement officers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2012_Romanian_protests\" title=\"2012 Romanian protests\">Violent protests</a> occur in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a>, Romania, as two-day-old demonstrations continue against President <a href=\"https://wikipedia.org/wiki/Traian_B%C4%83sescu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s economic <a href=\"https://wikipedia.org/wiki/Austerity\" title=\"Austerity\">austerity</a> measures. Clashes are reported in numerous Romanian cities between protesters and law enforcement officers.", "links": [{"title": "2012 Romanian protests", "link": "https://wikipedia.org/wiki/2012_Romanian_protests"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Traian_B%C4%83sescu"}, {"title": "Austerity", "link": "https://wikipedia.org/wiki/Austerity"}]}, {"year": "2015", "text": "Government raids kill 143 Boko Haram fighters in Kolofata, Cameroon.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/January_2015_raid_on_Kolofata\" title=\"January 2015 raid on Kolofata\">Government raids</a> kill 143 <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Haram\" title=\"<PERSON><PERSON> Haram\"><PERSON><PERSON></a> fighters in <a href=\"https://wikipedia.org/wiki/Kolofata\" title=\"Kolofata\">Kolofata, Cameroon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/January_2015_raid_on_Kolofata\" title=\"January 2015 raid on Kolofata\">Government raids</a> kill 143 <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hara<PERSON>\" title=\"<PERSON><PERSON> Haram\"><PERSON><PERSON></a> fighters in <a href=\"https://wikipedia.org/wiki/Kolofata\" title=\"Kolofa<PERSON>\">Kolofata, Cameroon</a>.", "links": [{"title": "January 2015 raid on Kolofata", "link": "https://wikipedia.org/wiki/January_2015_raid_on_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2016", "text": "Ten people are killed and 15 wounded in a bombing near the Blue Mosque in Istanbul.", "html": "2016 - Ten people are killed and 15 wounded in a <a href=\"https://wikipedia.org/wiki/January_2016_Istanbul_bombing\" title=\"January 2016 Istanbul bombing\">bombing</a> near the <a href=\"https://wikipedia.org/wiki/Sultan_Ahmed_Mosque\" class=\"mw-redirect\" title=\"Sultan Ahmed Mosque\">Blue Mosque in Istanbul</a>.", "no_year_html": "Ten people are killed and 15 wounded in a <a href=\"https://wikipedia.org/wiki/January_2016_Istanbul_bombing\" title=\"January 2016 Istanbul bombing\">bombing</a> near the <a href=\"https://wikipedia.org/wiki/Sultan_Ahmed_Mosque\" class=\"mw-redirect\" title=\"Sultan Ahmed Mosque\">Blue Mosque in Istanbul</a>.", "links": [{"title": "January 2016 Istanbul bombing", "link": "https://wikipedia.org/wiki/January_2016_Istanbul_bombing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan_Ahmed_Mosque"}]}, {"year": "2020", "text": "Taal Volcano in the Philippines erupts and kills 39 people.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Taal_Volcano\" title=\"Taal Volcano\">Taal Volcano</a> in the Philippines <a href=\"https://wikipedia.org/wiki/2020_Taal_Volcano_eruption\" class=\"mw-redirect\" title=\"2020 Taal Volcano eruption\">erupts</a> and kills 39 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taal_Volcano\" title=\"Taal Volcano\">Taal Volcano</a> in the Philippines <a href=\"https://wikipedia.org/wiki/2020_Taal_Volcano_eruption\" class=\"mw-redirect\" title=\"2020 Taal Volcano eruption\">erupts</a> and kills 39 people.", "links": [{"title": "Taal Volcano", "link": "https://wikipedia.org/wiki/Taal_Volcano"}, {"title": "2020 Taal Volcano eruption", "link": "https://wikipedia.org/wiki/2020_Taal_Volcano_eruption"}]}], "Births": [{"year": "1483", "text": "<PERSON> of Nassau-Breda (d. 1538)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Breda\" title=\"<PERSON> of Nassau-Breda\"><PERSON> of Nassau-Breda</a> (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Breda\" title=\"<PERSON> of Nassau-Breda\"><PERSON> of Nassau-Breda</a> (d. 1538)", "links": [{"title": "<PERSON> of Nassau-Breda", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Breda"}]}, {"year": "1562", "text": "<PERSON>, Duke of Savoy (d. 1630)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (d. 1630)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Savoy"}]}, {"year": "1576", "text": "<PERSON><PERSON>, Dutch historian and scholar (d. 1660)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and scholar (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and scholar (d. 1660)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Scriverius"}]}, {"year": "1577", "text": "<PERSON>, Flemish chemist and physician (d. 1644)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish chemist and physician (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, Flemish chemist and physician (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, English lawyer and politician, 2nd Governor of the Massachusetts Bay Colony (d. 1649)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (d. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of the Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON>, Spanish painter (d. 1652)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> R<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish painter (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Riber<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish painter (d. 1652)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON>, Flemish sculptor and educator (d. 1643)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish sculptor and educator (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish sculptor and educator (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1598", "text": "<PERSON><PERSON><PERSON><PERSON>, mother of Indian king <PERSON><PERSON> (d. 1674)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, mother of Indian king <a href=\"https://wikipedia.org/wiki/<PERSON>ji\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, mother of Indian king <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1674)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1628", "text": "<PERSON>, French author and academic (d. 1703)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and academic (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1673", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1757)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/Rosalba_Carriera\" title=\"Rosalba Carriera\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosa<PERSON><PERSON>_Carriera\" title=\"Rosalba Carriera\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1757)", "links": [{"title": "Rosalba Carriera", "link": "https://wikipedia.org/wiki/Rosalba_Carriera"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON> Paleologue, possibly last member of the Palaiologos dynasty (d. ????)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/Godscall_Paleologue\" title=\"Godscall Paleologue\">Godscall Paleologue</a>, possibly last member of the <a href=\"https://wikipedia.org/wiki/Palaiologos\" title=\"Palaiologos\">Palaiologos</a> dynasty (d. ????)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Godscall_Paleologue\" title=\"Godscall Paleologue\">Godscall Paleologue</a>, possibly last member of the <a href=\"https://wikipedia.org/wiki/Palaiologos\" title=\"Palaiologos\">Palaiologos</a> dynasty (d. ????)", "links": [{"title": "Godscall Paleologue", "link": "https://wikipedia.org/wiki/Godscall_Paleologue"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pa<PERSON>ologos"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON>, Italian composer (d. 1788)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French organist and composer (d. 1789)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, Spanish general and politician, 1st Spanish Governor of Louisiana (d. 1795)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Spanish Governor of Louisiana</a> (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Spanish Governor of Louisiana</a> (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana"}]}, {"year": "1721", "text": "<PERSON> of Brunswick-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Prussian field marshal (d. 1792)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel\" title=\"Duke <PERSON> of Brunswick-Wolfenbüttel\">Duke <PERSON> of Brunswick-Wolfe<PERSON></a>, Prussian field marshal (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel\" title=\"Duke <PERSON> of Brunswick-Wolfenbüttel\">Duke <PERSON> of Brunswick-Wolfe<PERSON></a>, Prussian field marshal (d. 1792)", "links": [{"title": "<PERSON> of Brunswick-Wolfenbüttel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brunswick-Wolfenb%C3%BCttel"}]}, {"year": "1723", "text": "<PERSON>, American minister, theologian, and academic (d. 1797)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, theologian, and academic (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, theologian, and academic (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, English author and playwright (d. 1789)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, Irish philosopher, academic, and politician (d. 1797)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish philosopher, academic, and politician (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish philosopher, academic, and politician (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, Swiss philosopher and educator (d. 1827)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philosopher and educator (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss philosopher and educator (d. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON> of the Two Sicilies (d. 1825)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1825)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_I_of_the_Two_Sicilies"}]}, {"year": "1772", "text": "<PERSON>, Russian academic and politician (d. 1839)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian academic and politician (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian academic and politician (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "Sir <PERSON>, 2nd Baronet, English politician (d. 1855)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English politician (d. 1855)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet"}]}, {"year": "1792", "text": "<PERSON>, Swedish chemist and academic (d. 1841)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and academic (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and academic (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON><PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Austrian physician and author (d. 1873)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and author (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and author (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, British botanist (d. 1872)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British botanist (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British botanist (d. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, Belgian engineer, designed the internal combustion engine (d. 1900)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian engineer, designed the <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">internal combustion engine</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian engineer, designed the <a href=\"https://wikipedia.org/wiki/Internal_combustion_engine\" title=\"Internal combustion engine\">internal combustion engine</a> (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Lenoir"}, {"title": "Internal combustion engine", "link": "https://wikipedia.org/wiki/Internal_combustion_engine"}]}, {"year": "1837", "text": "<PERSON>, German pianist and composer (d. 1879)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Russian-French painter and academic (d. 1935)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9raud\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and academic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ra<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and academic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jean_<PERSON>%C3%A9raud"}]}, {"year": "1853", "text": "<PERSON><PERSON>, Italian mathematician (d. 1925)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American painter and academic (d. 1925)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Indian monk and philosopher (d. 1902)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON><PERSON><PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and philosopher (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON><PERSON><PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and philosopher (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian philosopher, academic, and politician (d. 1958)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher, academic, and politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher, academic, and politician (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Greek runner (d. 1940)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek runner (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek runner (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rid<PERSON>_Louis"}]}, {"year": "1874", "text": "<PERSON>, American author and photographer (d. 1963)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Turkish field marshal and politician, Prime Minister of the Turkish Provisional Government (d. 1950)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Fevzi_%C3%87akmak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of the Turkish Provisional Government</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fe<PERSON><PERSON>_%C3%87akmak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish field marshal and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of the Turkish Provisional Government</a> (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fevzi_%C3%87akmak"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1876", "text": "<PERSON>, American novelist and journalist (d. 1916)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Jack_London\" title=\"Jack London\"><PERSON></a>, American novelist and journalist (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_London\" title=\"Jack London\"><PERSON></a>, American novelist and journalist (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jack_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Italian composer and educator (d. 1948)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wolf-Ferrari\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-Ferrari\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON> Wolf-Ferrari", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American lawyer and politician, 45th Mayor of Chicago (d. 1934)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Hungarian-American author and playwright (d. 1952)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Ferenc_Moln%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American author and playwright (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_<PERSON>ln%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American author and playwright (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Moln%C3%A1r"}]}, {"year": "1879", "text": "<PERSON>, American race car driver and engineer (d. 1968)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Estonian engineer and politician, 17th Mayor of Tallinn (d. 1942)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian engineer and politician, 17th <a href=\"https://wikipedia.org/wiki/Mayor_of_Tallinn\" class=\"mw-redirect\" title=\"Mayor of Tallinn\">Mayor of Tallinn</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian engineer and politician, 17th <a href=\"https://wikipedia.org/wiki/Mayor_of_Tallinn\" class=\"mw-redirect\" title=\"Mayor of Tallinn\">Mayor of Tallinn</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Tallinn", "link": "https://wikipedia.org/wiki/Mayor_of_Tallinn"}]}, {"year": "1882", "text": "<PERSON>, American actor and screenwriter (d. 1930)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON> G<PERSON>, American entertainer and bootlegger (d. 1933)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Texas_Guinan\" title=\"Texas Guinan\"><PERSON> Guinan</a>, American entertainer and bootlegger (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Guinan\" title=\"Texas Guinan\"><PERSON> Guinan</a>, American entertainer and bootlegger (d. 1933)", "links": [{"title": "Texas Guinan", "link": "https://wikipedia.org/wiki/Texas_Guinan"}]}, {"year": "1885", "text": "<PERSON>, Irish Republican died while on Hunger Strike (d. 1917)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died while on Hunger Strike (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Republican died while on Hunger Strike (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani spiritual leader (d. 1965)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-ud-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>-u<PERSON>-<PERSON>\"><PERSON>-<PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani spiritual leader (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-u<PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>-ud-<PERSON>\"><PERSON>-<PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani spiritual leader (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>-<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Estonian poet, physician, and politician (d. 1946)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet, physician, and politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian poet, physician, and politician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Russian engineer and businessman, co-founded the Mikoyan-Gurevich Design Bureau (d. 1976)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON> (aircraft designer)\"><PERSON></a>, Russian engineer and businessman, co-founded the Mi<PERSON><PERSON>-<PERSON><PERSON>vich Design Bureau (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aircraft_designer)\" title=\"<PERSON> (aircraft designer)\"><PERSON></a>, Russian engineer and businessman, co-founded the Mi<PERSON><PERSON>-<PERSON><PERSON>vich Design Bureau (d. 1976)", "links": [{"title": "<PERSON> (aircraft designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aircraft_designer)"}]}, {"year": "1893", "text": "<PERSON>, German commander, pilot, and politician, Minister President of Prussia (d. 1946)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>, German commander, pilot, and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ring\" title=\"<PERSON>\"><PERSON></a>, German commander, pilot, and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hermann_G%C3%B6ring"}, {"title": "Minister President of Prussia", "link": "https://wikipedia.org/wiki/Minister_President_of_Prussia"}]}, {"year": "1893", "text": "<PERSON>, Estonian-German architect and politician, Reich Minister for the Occupied Eastern Territories (d. 1946)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_for_the_Occupied_Eastern_Territories\" title=\"Reich Ministry for the Occupied Eastern Territories\">Reich Minister for the Occupied Eastern Territories</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect and politician, <a href=\"https://wikipedia.org/wiki/Reich_Ministry_for_the_Occupied_Eastern_Territories\" title=\"Reich Ministry for the Occupied Eastern Territories\">Reich Minister for the Occupied Eastern Territories</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reich Ministry for the Occupied Eastern Territories", "link": "https://wikipedia.org/wiki/Reich_Ministry_for_the_Occupied_Eastern_Territories"}]}, {"year": "1894", "text": "<PERSON>, French boxer and actor (d. 1975)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer and actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer and actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Polish-Israeli scholar and academic (d. 1959)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli scholar and academic (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli scholar and academic (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Italian tennis player (d. 1961)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Romanian-American psychologist and author (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American psychologist and author (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American psychologist and author (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French opera singer and educator (d. 1979)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French opera singer and educator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French opera singer and educator (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Swiss chemist and academic, Nobel Prize laureate (d. 1965)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1901", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_K%C3%BCnstler"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1903", "text": "<PERSON>, Russian physicist and academic (d. 1960)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American politician and attorney (Morissette v. United States) (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney (<i><a href=\"https://wikipedia.org/wiki/Morissette_v._United_States\" title=\"Morissette v. United States\">Morissette v. United States</a></i>) (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney (<i><a href=\"https://wikipedia.org/wiki/Morissette_v._United_States\" title=\"Morissette v. United States\">Morissette v. United States</a></i>) (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ue"}, {"title": "Morissette v. United States", "link": "https://wikipedia.org/wiki/Morissette_v._United_States"}]}, {"year": "1904", "text": "Mississippi <PERSON>, American singer-songwriter and guitarist (d. 1972)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Mississippi_<PERSON>\" title=\"Mississippi <PERSON>\">Mississippi <PERSON></a>, American singer-songwriter and guitarist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mississippi_<PERSON>_<PERSON>\" title=\"Mississippi <PERSON>\">Mississippi <PERSON></a>, American singer-songwriter and guitarist (d. 1972)", "links": [{"title": "Mississippi <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Turkish author, poet, and philosopher (d. 1975)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Nihal_Ats%C4%B1z\" title=\"<PERSON><PERSON> Atsız\"><PERSON><PERSON></a>, Turkish author, poet, and philosopher (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nihal_Ats%C4%B1z\" title=\"<PERSON><PERSON> Atsız\"><PERSON><PERSON></a>, Turkish author, poet, and philosopher (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nihal_Ats%C4%B1z"}]}, {"year": "1905", "text": "<PERSON>, American archaeologist and academic (d. 1997)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor and singer (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ritter\"><PERSON></a>, American actor and singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ritter\" title=\"<PERSON> Ritter\"><PERSON></a>, American actor and singer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ritter"}]}, {"year": "1906", "text": "<PERSON>, Lithuanian-French historian, philosopher, and academic (d. 1995)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-French historian, philosopher, and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-French historian, philosopher, and academic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Russian colonel and engineer (d. 1966)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and engineer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and engineer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French actor, director, and screenwriter (d. 2008)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American illustrator (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American actress and comedian (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, German-English actress (d. 2014)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English actress (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Estonian footballer (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Japanese psychiatrist and psychologist (d. 1979)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese psychiatrist and psychologist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese psychiatrist and psychologist (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American screenwriter and producer (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian archbishop and academic (d. 2013)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A8le_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American chemist and inventor (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African politician, 8th Prime Minister of South Africa (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Both<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Both<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Both<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Both<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1916", "text": "<PERSON>, <PERSON> of Rievaulx, British poet and Spouse of the Prime Minister of the United Kingdom (d. 2018)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Rievaulx\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Rievaulx\"><PERSON>, Baroness <PERSON> of Rievaulx</a>, British poet and Spouse of the Prime Minister of the United Kingdom (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Rievaulx\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Rievaulx\"><PERSON>, Baroness <PERSON> of Rievaulx</a>, British poet and Spouse of the Prime Minister of the United Kingdom (d. 2018)", "links": [{"title": "<PERSON>, <PERSON> of Rievaulx", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>_Rie<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American pianist, composer, and conductor (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American activist and politician, co-founded Congress of Racial Equality (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician, co-founded <a href=\"https://wikipedia.org/wiki/Congress_of_Racial_Equality\" title=\"Congress of Racial Equality\">Congress of Racial Equality</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician, co-founded <a href=\"https://wikipedia.org/wiki/Congress_of_Racial_Equality\" title=\"Congress of Racial Equality\">Congress of Racial Equality</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Congress of Racial Equality", "link": "https://wikipedia.org/wiki/Congress_of_Racial_Equality"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Polish-Australian sociologist and academic (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Australian sociologist and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Australian sociologist and academic (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Polish journalist and historian (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and historian (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%B<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and historian (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tad<PERSON><PERSON>_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American marine who raised the U.S. flag on Iwo Jima (d. 1955)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American marine who <a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima\" title=\"Raising the Flag on Iwo Jima\">raised the U.S. flag on Iwo Jima</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American marine who <a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima\" title=\"Raising the Flag on Iwo Jima\">raised the U.S. flag on Iwo Jima</a> (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Raising the Flag on Iwo Jima", "link": "https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima"}]}, {"year": "1924", "text": "<PERSON>, Belgian racing driver and businessman (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and businessman (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and businessman (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American television host, producer, and actor (d. 1990)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host, producer, and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host, producer, and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American composer and academic (d. 1987)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1928", "text": "<PERSON>, American R&B singer-songwriter and actress (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish philosopher and logician (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish philosopher and logician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish philosopher and logician (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Scottish-American philosopher and academic", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Scottish-American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Scottish-American philosopher and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian ice hockey player and businessman, founded <PERSON> (d. 1974)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Irish author and playwright", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Irish author and playwright", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(novelist)"}]}, {"year": "1930", "text": "<PERSON>, American singer and actor (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English entertainer, singer and TV presenter (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Des_O%27Connor\" title=\"<PERSON>\"><PERSON></a>, English entertainer, singer and TV presenter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Des_O%27Connor\" title=\"<PERSON>\"><PERSON></a>, English entertainer, singer and TV presenter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_O%27Connor"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Greek author and playwright (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and playwright (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Scottish-American author and screenwriter (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American author and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American author and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English rugby player and coach (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Mexican historian and critic (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican historian and critic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican historian and critic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, American mentalist (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mentalist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mentalist (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, <PERSON> of Eggardon, English police officer and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Eggardon\" title=\"<PERSON>, Baroness <PERSON> of Eggardon\"><PERSON>, Baroness <PERSON> of Eggardon</a>, English police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Eggardon\" title=\"<PERSON>, Baroness <PERSON> of Eggardon\"><PERSON>, Baroness <PERSON> of Eggardon</a>, English police officer and politician", "links": [{"title": "<PERSON>, <PERSON> of Eggardon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Eggardon"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Latvian pianist and composer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and activist (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, Indian Minister of Home Affairs (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)\" title=\"Minister of Home Affairs (India)\">Indian Minister of Home Affairs</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)\" title=\"Minister of Home Affairs (India)\">Indian Minister of Home Affairs</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Home Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_Home_Affairs_(India)"}]}, {"year": "1937", "text": "<PERSON>, English actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Pakistani scholar and politician (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani scholar and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani scholar and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, British racing driver (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver (d. 2024)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1940", "text": "<PERSON>, Australian-South African tennis player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-South African tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-South African tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American drummer and composer (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, New Zealand cricketer (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English-Canadian singer-songwriter and voice actor (d. 2005)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Long John Baldry\"><PERSON></a>, English-Canadian singer-songwriter and voice actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Long John Baldry\"><PERSON></a>, English-Canadian singer-songwriter and voice actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English psychiatrist and psychotherapist (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychiatrist and psychotherapist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychiatrist and psychotherapist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American swimmer and physician (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet <PERSON>\"><PERSON><PERSON></a>, American swimmer and physician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>et_<PERSON>\" title=\"Chet <PERSON>\"><PERSON><PERSON></a>, American swimmer and physician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>ski"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American domestic terrorist, political activist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American domestic terrorist, political activist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American domestic terrorist, political activist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1944", "text": "<PERSON>, German author and theorist (d. 2018)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and theorist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and theorist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American boxer (d. 2011)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American R&B trumpet player and singer (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B trumpet player and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B trumpet player and singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, <PERSON> Co<PERSON>grove, Scottish lawyer and judge", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lady_Cosgrove\" title=\"<PERSON>, Lady Cosgrove\"><PERSON>, <PERSON> Co<PERSON>grove</a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lady_Cosgrove\" title=\"<PERSON>, Lady Cosgrove\"><PERSON>, Lady Cosgrove</a>, Scottish lawyer and judge", "links": [{"title": "Hazel Cosgrove, Lady Cosgrove", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lady_Cosgrove"}]}, {"year": "1946", "text": "<PERSON>, American keyboard player, composer, and educator (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, composer, and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, composer, and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English historian and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American football player and educator (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and educator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Baroness <PERSON>, English politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1948", "text": "<PERSON>, English actor and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian educator and politician, 34th Premier of British Columbia", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian educator and politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian educator and politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1948", "text": "<PERSON>, English runner and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English author and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Japanese pianist and composer (d. 2007)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON>eda"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Tunisian engineer, journalist, and politician, 19th Prime Minister of Tunisia", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian engineer, journalist, and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tunisia\" title=\"Prime Minister of Tunisia\">Prime Minister of Tunisia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tunisian engineer, journalist, and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tunisia\" title=\"Prime Minister of Tunisia\">Prime Minister of Tunisia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}, {"title": "Prime Minister of Tunisia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tunisia"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Japanese novelist, short-story writer, and essayist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist, short-story writer, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist, short-story writer, and essayist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1950", "text": "<PERSON>, American lawyer, judge, and politician (d. 2024)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Swedish dentist and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Swedish dentist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Swedish dentist and politician", "links": [{"title": "<PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1950", "text": "<PERSON>, American businessman and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Israeli-Icelandic jewelry designer and businesswoman, 5th First Lady of Iceland", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-Icelandic jewelry designer and businesswoman, 5th <a href=\"https://wikipedia.org/wiki/List_of_spouses_and_partners_of_Icelandic_presidents\" title=\"List of spouses and partners of Icelandic presidents\">First Lady of Iceland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-Icelandic jewelry designer and businesswoman, 5th <a href=\"https://wikipedia.org/wiki/List_of_spouses_and_partners_of_Icelandic_presidents\" title=\"List of spouses and partners of Icelandic presidents\">First Lady of Iceland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of spouses and partners of Icelandic presidents", "link": "https://wikipedia.org/wiki/List_of_spouses_and_partners_of_Icelandic_presidents"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American actress and producer (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ie_<PERSON>\" title=\"Kirstie Alley\"><PERSON><PERSON><PERSON></a>, American actress and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kirstie Alley\"><PERSON><PERSON><PERSON></a>, American actress and producer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1978)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1978)", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)"}]}, {"year": "1951", "text": "<PERSON>, American talk show host and author (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1952", "text": "<PERSON>, Salvadoran footballer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Fagoaga\" title=\"<PERSON>\"><PERSON></a>, Salvadoran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Fagoaga\" title=\"<PERSON>\"><PERSON></a>, Salvadoran footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Fagoaga"}]}, {"year": "1952", "text": "<PERSON>, American novelist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American basketball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American country singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand runner and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, New Zealand runner and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, New Zealand runner and politician", "links": [{"title": "<PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON>_(runner)"}]}, {"year": "1953", "text": "<PERSON>, Canadian director and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American radio host, actor, and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American rock drummer (d. 2012)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Azerbaijani author, historian, and human rights activist.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani author, historian, and human rights activist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani author, historian, and human rights activist.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Russian rock singer and singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian rock singer and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian rock singer and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American animator, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English director, playwright, and composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, playwright, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, playwright, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, English-Iranian journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Iranian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Iranian journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American-Canadian ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON> <PERSON>, American wrestler and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American wrestler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American wrestler and politician", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Swedish singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Russian economist and politician (d. 2024)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian economist and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian economist and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian-American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French-American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Malaysia-born English actor and historian", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysia-born English actor and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysia-born English actor and historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author and illustrator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Antiguan cricketer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American-Canadian wrestler and manager (d. 2010)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Vach<PERSON>\" title=\"Luna Vachon\"><PERSON></a>, American-Canadian wrestler and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Vachon\" title=\"Luna Vachon\"><PERSON></a>, American-Canadian wrestler and manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vachon"}]}, {"year": "1963", "text": "<PERSON>, Canadian director and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>rd"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Brazilian singer-songwriter, guitarist, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}, {"year": "1964", "text": "<PERSON>, American computer scientist and businessman", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter, producer, actor, and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Rob_Zombie\" title=\"Rob Zombie\"><PERSON></a>, American singer-songwriter, producer, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rob_Zombie\" title=\"Rob Zombie\"><PERSON></a>, American singer-songwriter, producer, actor, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Zombie"}]}, {"year": "1966", "text": "<PERSON>, French actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Norwegian-Swedish model and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian-Swedish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian-Swedish model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vend<PERSON>_<PERSON>bom"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American actress (d. 2022)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Farrah_Forke\" title=\"Farrah Forke\"><PERSON><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Farrah_Forke\" title=\"Farrah Forke\"><PERSON><PERSON></a>, American actress (d. 2022)", "links": [{"title": "Farrah <PERSON>e", "link": "https://wikipedia.org/wiki/Farrah_Forke"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American actress and comedian", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese director, producer, and composer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director, producer, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director, producer, and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English businesswoman, activist and model", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, activist and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, activist and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English novelist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English novelist", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1969", "text": "<PERSON>, American screenwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> la Roch<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Iranian-Finnish tailor and television presenter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-Finnish tailor and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-Finnish tailor and television presenter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Danish engineer, entrepreneur, and convicted murderer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish engineer, entrepreneur, and convicted murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish engineer, entrepreneur, and convicted murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Indian politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Norwegian ice hockey player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian cricketer and umpire", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and umpire", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1972", "text": "<PERSON><PERSON> Wolff, Austrian investor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON>\"><PERSON><PERSON></a>, Austrian investor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON>\"><PERSON><PERSON></a>, Austrian investor", "links": [{"title": "<PERSON><PERSON> Wolff", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American pianist and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Turkish singer-songwriter, producer, and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ye<PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ner"}]}, {"year": "1974", "text": "<PERSON>, English singer-songwriter and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C\"><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_C\" title=\"<PERSON> C\"><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON> <PERSON>, Norwegian skier", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Tor A<PERSON>\"><PERSON> <PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Tor <PERSON>\"><PERSON> <PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American saxophonist, songwriter, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Cuban baseball player (d. 2023)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Yoandy_Garlobo\" title=\"Yoandy Garlobo\"><PERSON><PERSON><PERSON></a>, Cuban baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoandy_Garlobo\" title=\"Yoandy Garlobo\"><PERSON><PERSON><PERSON></a>, Cuban baseball player (d. 2023)", "links": [{"title": "Yoandy Garlobo", "link": "https://wikipedia.org/wiki/Yoandy_Garlobo"}]}, {"year": "1978", "text": "<PERSON>, Mexican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Mexican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camp\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camp\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Italian rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>-<PERSON>, South Korean actress and model", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Slovak ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Mari%C3%A1n_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari%C3%A1n_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mari%C3%A1n_Hossa"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and writer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Amerie\" title=\"Amerie\"><PERSON><PERSON></a>, American singer-songwriter, producer, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amerie\" title=\"Amerie\"><PERSON><PERSON></a>, American singer-songwriter, producer, and writer", "links": [{"title": "Amerie", "link": "https://wikipedia.org/wiki/Amerie"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wall\" title=\"<PERSON><PERSON>wall\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wall\" title=\"<PERSON><PERSON>wall\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>las_Kronwall"}]}, {"year": "1981", "text": "<PERSON>, New Zealand rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1981", "text": "<PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON>_(footballer,_born_January_1981)\" title=\"<PERSON> (footballer, born January 1981)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON>_(footballer,_born_January_1981)\" title=\"<PERSON> (footballer, born January 1981)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born January 1981)", "link": "https://wikipedia.org/wiki/Jo%C3%<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_January_1981)"}]}, {"year": "1981", "text": "<PERSON>, Mexican footballer and manager", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rez"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Belgian decathlete", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English-American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Ukrainian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arte<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mile<PERSON>\"><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress, writer, director, producer and web series creator", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, writer, director, producer and web series creator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, writer, director, producer and web series creator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, German rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Brenner\"><PERSON><PERSON><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Brenner\"><PERSON><PERSON><PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Spanish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>eto"}]}, {"year": "1986", "text": "<PERSON>, Italian-Argentinian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Nova\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Nova\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Nova"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American actress and singer (d. 2020)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South Korean baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-soo_<PERSON>\" title=\"<PERSON><PERSON>-so<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-soo_<PERSON>\" title=\"<PERSON><PERSON>-so<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-so<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Thiemo-J%C3%A9r%C3%B4me_Kialka\" title=\"Thiemo-<PERSON><PERSON><PERSON><PERSON>\">Thiem<PERSON>-<PERSON><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiemo-J%C3%A9r%C3%B4me_Ki<PERSON>a\" title=\"Thiemo-<PERSON><PERSON><PERSON><PERSON>\">Thiemo-<PERSON><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "Thiemo<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiemo-J%C3%A9r%C3%B4me_Kialka"}]}, {"year": "1989", "text": "<PERSON>, Belgian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter, dancer, and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>t"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Algerian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>fo<PERSON>l\"><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>fo<PERSON>l\"><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hak_Belfodil"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Artis\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Artis\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (entertainer)\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (entertainer)\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_(entertainer)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, English singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, German footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Em<PERSON>_<PERSON>\" title=\"Emre Can\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em<PERSON>_<PERSON>\" title=\"Emre <PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emre_Can"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ish<PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ish<PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ish<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)\" title=\"<PERSON> (offensive lineman)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)\" title=\"<PERSON> (offensive lineman)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (offensive lineman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)"}]}, {"year": "1996", "text": "<PERSON>, English singer and songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Argentinian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Welsh footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Dutch footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, German tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lys\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eva Lys\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Lys"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}], "Deaths": [{"year": "690", "text": "<PERSON>, English scholar and saint, founded the Monkwearmouth-Jarrow Abbey (b. 628)", "html": "690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and saint, founded the <a href=\"https://wikipedia.org/wiki/Monkwearmouth%E2%80%93Jarrow_Abbey\" title=\"Monkwearmouth-Jarrow Abbey\">Monkwearmouth-Jarrow Abbey</a> (b. 628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and saint, founded the <a href=\"https://wikipedia.org/wiki/Monkwearmouth%E2%80%93Jarrow_Abbey\" title=\"Monkwearmouth-Jarrow Abbey\">Monkwearmouth-Jarrow Abbey</a> (b. 628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Monkwearmouth-Jarrow Abbey", "link": "https://wikipedia.org/wiki/Monkwearmouth%E2%80%93<PERSON><PERSON><PERSON>_Abbey"}]}, {"year": "914", "text": "<PERSON>, Samanid emir", "html": "914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samanid emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samanid emir", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "947", "text": "<PERSON>, Chinese chief of staff (b. 898)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chief of staff (b. 898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chief of staff (b. 898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1140", "text": "<PERSON>, Landgrave of Thuringia", "html": "1140 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a>", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1167", "text": "<PERSON><PERSON><PERSON> of Rievaulx, English monk and saint (b. 1110)", "html": "1167 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Rievaulx\" title=\"<PERSON><PERSON><PERSON> of Rievaulx\"><PERSON><PERSON><PERSON> of Rievaulx</a>, English monk and saint (b. 1110)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Rievaulx\" title=\"<PERSON><PERSON><PERSON> of Rievaulx\"><PERSON><PERSON><PERSON> of Rievaulx</a>, English monk and saint (b. 1110)", "links": [{"title": "<PERSON><PERSON><PERSON> of Rievaulx", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rievaulx"}]}, {"year": "1320", "text": "<PERSON>, bishop of Lincoln", "html": "1320 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Lincoln", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Lincoln", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1322", "text": "<PERSON>, Queen of France (b. 1254)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>ant, Queen of France\"><PERSON>, Queen of France</a> (b. 1254)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON>ant, Queen of France</a> (b. 1254)", "links": [{"title": "<PERSON> Brabant, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1405", "text": "<PERSON>, English noblewoman (b. 1345)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1345)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (b. 1345)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, Holy Roman Emperor (b. 1459)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1459)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1665", "text": "<PERSON>, French mathematician and lawyer (b. 1601)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and lawyer (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and lawyer (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON>, Italian priest and composer (b. 1605)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>imi"}]}, {"year": "1700", "text": "<PERSON>, French-Canadian nun and saint, founded the Congregation of Notre Dame of Montreal (b. 1620)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal\" title=\"Congregation of Notre Dame of Montreal\">Congregation of Notre Dame of Montreal</a> (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal\" title=\"Congregation of Notre Dame of Montreal\">Congregation of Notre Dame of Montreal</a> (b. 1620)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Congregation of Notre Dame of Montreal", "link": "https://wikipedia.org/wiki/Congregation_of_Notre_Dame_of_Montreal"}]}, {"year": "1720", "text": "<PERSON>, English banker and politician, Lord Mayor of London (b. 1647)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> (b. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord Mayor of London", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_London"}]}, {"year": "1732", "text": "<PERSON>, English-Scottish historian and author (b. 1685)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)\" title=\"<PERSON> (antiquarian)\"><PERSON></a>, English-Scottish historian and author (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)\" title=\"<PERSON> (antiquarian)\"><PERSON></a>, English-Scottish historian and author (b. 1685)", "links": [{"title": "<PERSON> (antiquarian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquarian)"}]}, {"year": "1735", "text": "<PERSON>, English composer (b. 1668)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer (b. 1668)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1759", "text": "<PERSON>, Princess <PERSON> and Princess of Orange (b. 1709)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange\" title=\"<PERSON>, Princess <PERSON> and Princess of Orange\"><PERSON>, Princess <PERSON> and Princess of Orange</a> (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange\" title=\"<PERSON>, Princess <PERSON> and Princess of Orange\"><PERSON>, Princess <PERSON> and Princess of Orange</a> (b. 1709)", "links": [{"title": "<PERSON>, Princess <PERSON> and Princess of Orange", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_Royal_and_Princess_of_Orange"}]}, {"year": "1765", "text": "<PERSON>, German violinist and composer (b. 1696)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Scottish-American general and physician (b. 1726)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American general and physician (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American general and physician (b. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, French politician (b. 1703)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>ot"}]}, {"year": "1781", "text": "<PERSON>, English bishop (b. 1691)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, German philosopher, poet, and critic (b. 1772)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher, poet, and critic (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher, poet, and critic (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, French chef (b. 1784)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AAme\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chef (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%AAme\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chef (b. 1784)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%AAme"}]}, {"year": "1834", "text": "<PERSON>, 1st Baron <PERSON>, English academic and politician, Prime Minister of the United Kingdom (b. 1759)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1759)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak philologist and politician (b. 1815)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr\" title=\"<PERSON>udo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak philologist and politician (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr\" title=\"<PERSON>udo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak philologist and politician (b. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDudov%C3%ADt_%C5%A0t%C3%BAr"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech philologist and author (b. 1791)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech philologist and author (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech philologist and author (b. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1892", "text": "<PERSON>, 3rd Earl of Charlemont, Irish politician, Lord Lieutenant of Tyrone (b. 1820)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont\" title=\"<PERSON>, 3rd Earl of Charlemont\"><PERSON>, 3rd Earl of Charlemont</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone\" title=\"Lord Lieutenant of Tyrone\">Lord Lieutenant of Tyrone</a> (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont\" title=\"<PERSON>, 3rd Earl of Charlemont\"><PERSON>, 3rd Earl of Charlemont</a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone\" title=\"Lord Lieutenant of Tyrone\">Lord Lieutenant of Tyrone</a> (b. 1820)", "links": [{"title": "<PERSON>, 3rd Earl of Charlemont", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Charlemont"}, {"title": "Lord Lieutenant of Tyrone", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Tyrone"}]}, {"year": "1892", "text": "<PERSON>, Irish bishop and historian (b. 1815)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish bishop and historian (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish bishop and historian (b. 1815)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American businessman, founded Canadian Club (b. 1816)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Canadian_Club\" title=\"Canadian Club\">Canadian Club</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Canadian_Club\" title=\"Canadian Club\">Canadian Club</a> (b. 1816)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Canadian Club", "link": "https://wikipedia.org/wiki/Canadian_Club"}]}, {"year": "1909", "text": "<PERSON>, Lithuanian-German mathematician and academic (b. 1864)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-German mathematician and academic (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-German mathematician and academic (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Greek journalist, judge, and politician (b. 1845)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek journalist, judge, and politician (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek journalist, judge, and politician (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Greek lawyer and politician, 80th Prime Minister of Greece (b. 1844)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 80th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 80th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_Theotokis"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, English tenor and actor (b. 1866)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English tenor and actor (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English tenor and actor (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian businessman and politician, 4th Australian Minister for Defence (b. 1864)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Chapman"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1934", "text": "<PERSON>, Polish violinist and composer (b. 1887)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish violinist and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish violinist and composer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German-American painter and illustrator (b. 1867)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Oscar <PERSON>\"><PERSON></a>, German-American painter and illustrator (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Austrian-American hotelier (b. 1891)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American hotelier (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American hotelier (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English lieutenant, Victoria Cross recipient (b. 1898)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC)\" title=\"<PERSON> (VC)\"><PERSON></a>, English lieutenant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\"><PERSON> Cross</a> recipient (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(VC)\" title=\"<PERSON> (VC)\"><PERSON></a>, English lieutenant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1898)", "links": [{"title": "<PERSON> (VC)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC)"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1943", "text": "<PERSON>, Dutch journalist and critic (b. 1902)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and critic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and critic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON> <PERSON>, American commander and pilot (b. 1915)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, American commander and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and pilot (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American meteorologist (b. 1875)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, English engineer and author (b. 1899)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Nevil_<PERSON>e\" title=\"Nevil Shute\"><PERSON><PERSON><PERSON></a>, English engineer and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nevil_<PERSON>e\" title=\"Nevil Shute\"><PERSON><PERSON><PERSON></a>, English engineer and author (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nevil_<PERSON>e"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Russian journalist and activist (b. 1869)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian journalist and activist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Russian journalist and activist (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American author, playwright, and director (b. 1936)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and director (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Turkish diplomat (b. 1887)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>af_Belge\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Asaf Belge\"><PERSON><PERSON><PERSON></a>, Turkish diplomat (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Belge\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Asaf Belge\"><PERSON><PERSON><PERSON></a>, Turkish diplomat (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ge"}]}, {"year": "1971", "text": "<PERSON>, 1st Baron <PERSON>, English admiral (b. 1885)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral (b. 1885)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American historian and academic (b. 1896)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Princess <PERSON> of Connaught (b. 1886)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Connaught\" title=\"Princess Patricia of Connaught\">Princess <PERSON> of Connaught</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Connaught\" title=\"Princess Patricia of Connaught\">Princess <PERSON> of Connaught</a> (b. 1886)", "links": [{"title": "Princess <PERSON> of Connaught", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Con<PERSON>ught"}]}, {"year": "1976", "text": "<PERSON>, English crime novelist, short story writer, and playwright (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English crime novelist, short story writer, and playwright (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English crime novelist, short story writer, and playwright (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French director and screenwriter (b. 1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director and screenwriter (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director and screenwriter (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Ukrainian engineer and politician (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian engineer and politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikolai_Podgorny"}]}, {"year": "1988", "text": "<PERSON>, South African politician (b. 1925)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Italian racing driver and motorcycle racer (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver and motorcycle racer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian racing driver and motorcycle racer (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian-American author and educator (b. 1919)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and educator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian public servant and diplomat (b. 1911)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)\" title=\"<PERSON> (UN administrator)\"><PERSON></a>, Australian public servant and diplomat (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)\" title=\"<PERSON> (UN administrator)\"><PERSON></a>, Australian public servant and diplomat (b. 1911)", "links": [{"title": "<PERSON> (UN administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(UN_administrator)"}]}, {"year": "1992", "text": "<PERSON>, a Hindustani classical singer (b. 1924)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Hindustani classical singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Hindustani classical singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Estonian physicist and philosopher (b. 1919)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and philosopher (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and philosopher (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German mathematician and academic (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, French author (b. 1936)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian-American physician and physiologist, Nobel Prize laureate (b. 1901)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, English racing driver (b. 1939)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, English racing driver (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rally_driver)\" title=\"<PERSON> (rally driver)\"><PERSON></a>, English racing driver (b. 1939)", "links": [{"title": "<PERSON> (rally driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(rally_driver)"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American ice hockey player (b. 1961)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American animator and screenwriter (b. 1913)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator and screenwriter (b. 1913)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)"}]}, {"year": "2000", "text": "<PERSON>, American basketball player (b. 1969)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Brazilian guitarist and composer (b. 1922)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>f%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian guitarist and composer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luiz_Bonf%C3%A1"}]}, {"year": "2001", "text": "<PERSON>, American engineer and businessman, co-founded Hewlett-Packard (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hewlett\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"Hewlett-Packard\">He<PERSON>ett-<PERSON></a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hewlett\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"He<PERSON><PERSON>-Packard\"><PERSON><PERSON><PERSON>-<PERSON></a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hewlett-Packard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ett-<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American lawyer and politician, 57th U.S. Secretary of State (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/U.S._Secretary_of_State\" class=\"mw-redirect\" title=\"U.S. Secretary of State\">U.S. Secretary of State</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 57th <a href=\"https://wikipedia.org/wiki/U.S._Secretary_of_State\" class=\"mw-redirect\" title=\"U.S. Secretary of State\">U.S. Secretary of State</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "U.S. Secretary of State", "link": "https://wikipedia.org/wiki/U.S._Secretary_of_State"}]}, {"year": "2003", "text": "<PERSON>, American ornithologist and author (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Japanese actor, director, and screenwriter (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Ki<PERSON>_<PERSON>\" title=\"Ki<PERSON>\"><PERSON><PERSON></a>, Japanese actor, director, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ki<PERSON>\"><PERSON><PERSON></a>, Japanese actor, director, and screenwriter (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ki<PERSON>_Fu<PERSON>aku"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Argentine general and politician, 44th President of Argentina (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine general and politician, 44th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine general and politician, 44th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "2003", "text": "<PERSON>, Manx-Australian singer-songwriter, guitarist, and producer (b. 1949)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-Australian singer-songwriter, guitarist, and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-Australian singer-songwriter, guitarist, and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English physicist and spy (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and spy (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> May\"><PERSON></a>, English physicist and spy (b. 1911)", "links": [{"title": "<PERSON> May", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_May"}]}, {"year": "2004", "text": "<PERSON>, Russian mathematician and academic (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Indian actor (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Santa Clara Pueblo Native American painter (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Pablita_Velarde\" title=\"Pablita Velarde\"><PERSON><PERSON><PERSON><PERSON> Vela<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> Native American painter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pablita_Velarde\" title=\"Pablita Velarde\"><PERSON><PERSON><PERSON><PERSON> Vela<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Santa_Clara_Pueblo\" class=\"mw-redirect\" title=\"Santa Clara Pueblo\">Santa Clara Pueblo</a> Native American painter (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>lit<PERSON>_Velarde"}, {"title": "Santa Clara Pueblo", "link": "https://wikipedia.org/wiki/Santa_Clara_Pueblo"}]}, {"year": "2007", "text": "<PERSON>, American pianist and composer (b. 1937)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian soldier, lawyer, and politician, 38th Australian Minister for Defence (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier, lawyer, and politician, 38th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "2008", "text": "<PERSON>, American intersex advocate (b. 1966)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Intersex\" title=\"Intersex\">intersex</a> advocate (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Beck\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Intersex\" title=\"Intersex\">intersex</a> advocate (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Intersex", "link": "https://wikipedia.org/wiki/Intersex"}]}, {"year": "2009", "text": "<PERSON>, French actor, director, and screenwriter (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, French philosopher and author (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AFd\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AFd\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AFd"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Palestinian businessman and philanthropist, co-founded Consolidated Contractors Company (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Consolidated_Contractors_Company\" title=\"Consolidated Contractors Company\">Consolidated Contractors Company</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Consolidated_Contractors_Company\" title=\"Consolidated Contractors Company\">Consolidated Contractors Company</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Consolidated Contractors Company", "link": "https://wikipedia.org/wiki/Consolidated_Contractors_Company"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian geologist and academic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Norwegian geologist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Norwegian geologist and academic (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rn_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American director, choreographer, and educator (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, choreographer, and educator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, choreographer, and educator (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician, 27th Governor of South Dakota (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Dakota", "link": "https://wikipedia.org/wiki/Governor_of_South_Dakota"}]}, {"year": "2012", "text": "<PERSON>, American businessman and diplomat, United States Ambassador to the United Kingdom (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>, American businessman and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Price_II\" title=\"<PERSON> II\"><PERSON> II</a>, American businessman and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Price_II"}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "links": [{"title": "Precious Bryant", "link": "https://wikipedia.org/wiki/Precious_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON>, First Lady of Venezuela (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Flor_Mar%C3%ADa_Cha<PERSON>ud\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, First Lady of Venezuela (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flor_Mar%C3%ADa_Cha<PERSON>ud\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, First Lady of Venezuela (b. 1921)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Flor_Mar%C3%AD<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and activist (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actress (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American educator and politician, 58th Lieutenant Governor of Michigan (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 58th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Michigan\" title=\"Lieutenant Governor of Michigan\">Lieutenant Governor of Michigan</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 58th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Michigan\" title=\"Lieutenant Governor of Michigan\">Lieutenant Governor of Michigan</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Michigan", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Michigan"}]}, {"year": "2014", "text": "<PERSON>, American soldier, businessman, and politician (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, businessman, and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American historian and academic (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American journalist and author (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1935)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2015", "text": "<PERSON>, Russian soprano and actress (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano and actress (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano and actress (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Brazilian-Dutch field hockey player (b. 1985)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Inge_Vermeulen\" title=\"Inge Vermeulen\"><PERSON><PERSON> Ver<PERSON></a>, Brazilian-Dutch field hockey player (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_Vermeulen\" title=\"Inge Vermeulen\"><PERSON><PERSON> Ver<PERSON></a>, Brazilian-Dutch field hockey player (b. 1985)", "links": [{"title": "Inge Vermeulen", "link": "https://wikipedia.org/wiki/Inge_Vermeulen"}]}, {"year": "2017", "text": "<PERSON>, American writer and filmmaker (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and filmmaker (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and filmmaker (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English football player and manager (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American sports commentator and journalist (b. 1928)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports commentator and journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports commentator and journalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "Sir <PERSON>, English philosopher and writer (b. 1944)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, English philosopher and writer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, English philosopher and writer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American singer (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American singer-songwriter (b. 1968)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Indian politician, 30th Minister of Civil Aviation, 29th Labour Minister (b. 1947)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>hara<PERSON>_Yadav\" title=\"<PERSON><PERSON><PERSON> Yadav\"><PERSON><PERSON><PERSON>da<PERSON></a>, Indian politician, 30th <a href=\"https://wikipedia.org/wiki/Ministry_of_Civil_Aviation_(India)#List_of_ministers\" title=\"Ministry of Civil Aviation (India)\">Minister of Civil Aviation</a>, 29th <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour_and_Employment_(India)#Labour_Ministers_of_India\" title=\"Ministry of Labour and Employment (India)\">Labour Minister</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hara<PERSON>_Yadav\" title=\"<PERSON><PERSON><PERSON> Yadav\"><PERSON><PERSON><PERSON>dav</a>, Indian politician, 30th <a href=\"https://wikipedia.org/wiki/Ministry_of_Civil_Aviation_(India)#List_of_ministers\" title=\"Ministry of Civil Aviation (India)\">Minister of Civil Aviation</a>, 29th <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour_and_Employment_(India)#Labour_Ministers_of_India\" title=\"Ministry of Labour and Employment (India)\">Labour Minister</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hara<PERSON>_Yadav"}, {"title": "Ministry of Civil Aviation (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Civil_Aviation_(India)#List_of_ministers"}, {"title": "Ministry of Labour and Employment (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Labour_and_Employment_(India)#Labour_Ministers_of_India"}]}, {"year": "2025", "text": "<PERSON>, American actress (b. 1945)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American actor and producer (b. 1934)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor and producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor and producer (b. 1934)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}]}}