{"date": "September 7", "url": "https://wikipedia.org/wiki/September_7", "data": {"Events": [{"year": "878", "text": "<PERSON> Stammerer is crowned as king of West Francia by Pope <PERSON>.", "html": "878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the Stammerer</a> is crowned as king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John VIII\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the St<PERSON>merer</a> is crowned as king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_John_<PERSON>\" title=\"Pope John VIII\"><PERSON></a>.", "links": [{"title": "<PERSON> Stammerer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1159", "text": "Cardinal <PERSON><PERSON> is elected <PERSON> <PERSON>, prompting the election of Cardinal <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON> the same day.", "html": "1159 - Cardinal <PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"<PERSON> Alexander III\">Pope <PERSON> III</a>, prompting the election of Cardinal <PERSON><PERSON><PERSON> as <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">Antipope</a> <a href=\"https://wikipedia.org/wiki/Antipope_Victor_IV_(1159%E2%80%931164)\" title=\"Antipope Victor IV (1159-1164)\"><PERSON> IV</a> the same day.", "no_year_html": "Cardinal <PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Alexander III\">Pope <PERSON> III</a>, prompting the election of Cardinal <PERSON><PERSON><PERSON> as <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">Antipope</a> <a href=\"https://wikipedia.org/wiki/Antipope_Victor_IV_(1159%E2%80%931164)\" title=\"Antipope Victor IV (1159-1164)\"><PERSON> IV</a> the same day.", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Antipope", "link": "https://wikipedia.org/wiki/Antipope"}, {"title": "<PERSON><PERSON><PERSON> (1159-1164)", "link": "https://wikipedia.org/wiki/Antipop<PERSON>_<PERSON>_IV_(1159%E2%80%931164)"}]}, {"year": "1191", "text": "Third Crusade: Battle of Arsuf: <PERSON> of England defeats <PERSON><PERSON><PERSON> at Arsuf.", "html": "1191 - <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Arsuf\" title=\"Battle of Arsuf\">Battle of Arsuf</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Richard I of England\"><PERSON> of England</a> defeats <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladin\">Saladin</a> at <a href=\"https://wikipedia.org/wiki/Arsuf\" class=\"mw-redirect\" title=\"Arsuf\">Arsuf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Arsuf\" title=\"Battle of Arsuf\">Battle of Arsuf</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_England\" title=\"Richard I of England\"><PERSON> of England</a> defeats <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladin\">Saladin</a> at <a href=\"https://wikipedia.org/wiki/Arsuf\" class=\"mw-redirect\" title=\"Arsuf\">Arsuf</a>.", "links": [{"title": "Third Crusade", "link": "https://wikipedia.org/wiki/Third_Crusade"}, {"title": "Battle of Arsuf", "link": "https://wikipedia.org/wiki/Battle_of_A<PERSON>uf"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arsuf"}]}, {"year": "1228", "text": "Holy Roman Emperor <PERSON> lands in Acre, Israel, and starts the Sixth Crusade, which results in a peaceful restoration of the Kingdom of Jerusalem.", "html": "1228 - Holy Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> lands in <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre, Israel</a>, and starts the <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>, which results in a peaceful restoration of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a>.", "no_year_html": "Holy Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> lands in <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre, Israel</a>, and starts the <a href=\"https://wikipedia.org/wiki/Sixth_Crusade\" title=\"Sixth Crusade\">Sixth Crusade</a>, which results in a peaceful restoration of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Acre, Israel", "link": "https://wikipedia.org/wiki/Acre,_Israel"}, {"title": "Sixth Crusade", "link": "https://wikipedia.org/wiki/Sixth_Crusade"}, {"title": "Kingdom of Jerusalem", "link": "https://wikipedia.org/wiki/Kingdom_of_Jerusalem"}]}, {"year": "1303", "text": "<PERSON> takes <PERSON> <PERSON><PERSON><PERSON> VIII prisoner on behalf of <PERSON> of France.", "html": "1303 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ace_VIII\" title=\"<PERSON> VIII\"><PERSON> VIII</a> prisoner on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON> VIII</a> prisoner on behalf of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>if<PERSON>_VIII"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1571", "text": "<PERSON>, 4th Duke of Norfolk, is arrested for his role in the <PERSON><PERSON><PERSON><PERSON> plot to assassinate Queen <PERSON> of England and replace her with <PERSON>, Queen of Scots.", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a>, is arrested for his role in the <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_plot\" title=\"<PERSON><PERSON><PERSON><PERSON> plot\"><PERSON><PERSON><PERSON><PERSON> plot</a> to assassinate Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> of England and replace her with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a>, is arrested for his role in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_plot\" title=\"<PERSON><PERSON><PERSON><PERSON> plot\"><PERSON><PERSON><PERSON><PERSON> plot</a> to assassinate Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> of England and replace her with <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>.", "links": [{"title": "<PERSON>, 4th Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk"}, {"title": "<PERSON><PERSON><PERSON><PERSON> plot", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_plot"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}]}, {"year": "1620", "text": "The town of Kokkola (Swedish: Karleby) is founded by King <PERSON><PERSON> of Sweden.", "html": "1620 - The town of <a href=\"https://wikipedia.org/wiki/Kokkola\" title=\"Kokkola\">Kokkola</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON><PERSON></i>) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Adolphus\" title=\"<PERSON><PERSON> Adolphus\"><PERSON><PERSON> of Sweden</a>.", "no_year_html": "The town of <a href=\"https://wikipedia.org/wiki/Kokkola\" title=\"Kokkola\">Kokkola</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON><PERSON></i>) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Adolphus\" title=\"<PERSON><PERSON> Adolphus\"><PERSON><PERSON> of Sweden</a>.", "links": [{"title": "Kokkola", "link": "https://wikipedia.org/wiki/Kok<PERSON>la"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1630", "text": "The city of Boston, Massachusetts, is founded in North America.", "html": "1630 - The city of <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts, is founded in North America.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts, is founded in North America.", "links": [{"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1652", "text": "Around 15,000 Han farmers and militia rebel against Dutch rule on Taiwan.", "html": "1652 - Around 15,000 <a href=\"https://wikipedia.org/wiki/Han_Chinese\" title=\"Han Chinese\">Han</a> farmers and militia <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rebellion\" class=\"mw-redirect\" title=\"Guo Hu<PERSON>yi Rebellion\">rebel</a> against <a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">Dutch rule</a> on <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "no_year_html": "Around 15,000 <a href=\"https://wikipedia.org/wiki/Han_Chinese\" title=\"Han Chinese\">Han</a> farmers and militia <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rebellion\" class=\"mw-redirect\" title=\"Guo Hu<PERSON>yi Rebellion\">rebel</a> against <a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">Dutch rule</a> on <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "links": [{"title": "Han Chinese", "link": "https://wikipedia.org/wiki/Han_Chinese"}, {"title": "Guo <PERSON> Rebellion", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dutch Formosa", "link": "https://wikipedia.org/wiki/Dutch_Formosa"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1695", "text": "<PERSON> perpetrates one of the most profitable pirate raids in history with the capture of the Grand Mughal ship G<PERSON>j-<PERSON>-Saw<PERSON>. In response, Emperor <PERSON><PERSON><PERSON><PERSON> threatens to end all English trading in India.", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Every\"><PERSON> Every</a> perpetrates one of the most profitable pirate raids in history with the capture of the Grand Mughal ship <i><a href=\"https://wikipedia.org/wiki/Ganj-<PERSON>-<PERSON>ai\" title=\"Ganj-<PERSON>-Sawai\">Gan<PERSON>-<PERSON>-<PERSON>ai</a></i>. In response, Emperor <a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangze<PERSON>\">Aurangzeb</a> threatens to end all English trading in India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Every\"><PERSON> Every</a> perpetrates one of the most profitable pirate raids in history with the capture of the Grand Mughal ship <i><a href=\"https://wikipedia.org/wiki/Gan<PERSON>-<PERSON>-<PERSON>ai\" title=\"Ganj-<PERSON>-Sawai\">Gan<PERSON>-<PERSON>-<PERSON>ai</a></i>. In response, Emperor <a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangze<PERSON>\">Aurangzeb</a> threatens to end all English trading in India.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ganj-i-Sawai", "link": "https://wikipedia.org/wiki/Ganj-<PERSON>-Sawai"}, {"title": "Aurangzeb", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>zeb"}]}, {"year": "1706", "text": "War of the Spanish Succession: Siege of Turin ends, leading to the withdrawal of French forces from North Italy.", "html": "1706 - <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Turin\" title=\"Siege of Turin\">Siege of Turin</a> ends, leading to the withdrawal of French forces from North Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Turin\" title=\"Siege of Turin\">Siege of Turin</a> ends, leading to the withdrawal of French forces from North Italy.", "links": [{"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}, {"title": "Siege of Turin", "link": "https://wikipedia.org/wiki/Siege_of_Turin"}]}, {"year": "1764", "text": "Election of <PERSON><PERSON> as the last ruler of the Polish-Lithuanian Commonwealth.", "html": "1764 - Election of <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the last ruler of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "no_year_html": "Election of <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the last ruler of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}]}, {"year": "1776", "text": "According to American colonial reports, <PERSON> makes the world's first submarine attack in the Turtle, attempting to attach a time bomb to the hull of HMS Eagle in New York Harbor (no British records of this attack exist).", "html": "1776 - According to American colonial reports, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the world's first <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> attack in the <i><a href=\"https://wikipedia.org/wiki/Turtle_(submersible)\" title=\"<PERSON> (submersible)\">Turtle</a></i>, attempting to attach a <a href=\"https://wikipedia.org/wiki/Time_bomb_(explosive)\" class=\"mw-redirect\" title=\"Time bomb (explosive)\">time bomb</a> to the hull of <a href=\"https://wikipedia.org/wiki/HMS_Eagle_(1774)\" title=\"HMS Eagle (1774)\">HMS <i>Eagle</i></a> in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a> (no British records of this attack exist).", "no_year_html": "According to American colonial reports, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the world's first <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> attack in the <i><a href=\"https://wikipedia.org/wiki/Turtle_(submersible)\" title=\"<PERSON> (submersible)\">Turtle</a></i>, attempting to attach a <a href=\"https://wikipedia.org/wiki/Time_bomb_(explosive)\" class=\"mw-redirect\" title=\"Time bomb (explosive)\">time bomb</a> to the hull of <a href=\"https://wikipedia.org/wiki/HMS_Eagle_(1774)\" title=\"HMS Eagle (1774)\">HMS <i>Eagle</i></a> in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a> (no British records of this attack exist).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "Turtle (submersible)", "link": "https://wikipedia.org/wiki/Turtle_(submersible)"}, {"title": "Time bomb (explosive)", "link": "https://wikipedia.org/wiki/Time_bomb_(explosive)"}, {"title": "HMS Eagle (1774)", "link": "https://wikipedia.org/wiki/HMS_Eagle_(1774)"}, {"title": "New York Harbor", "link": "https://wikipedia.org/wiki/New_York_Harbor"}]}, {"year": "1778", "text": "American Revolutionary War: France invades Dominica in the British West Indies, before Britain is even aware of France's involvement in the war.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: France <a href=\"https://wikipedia.org/wiki/Invasion_of_Dominica_(1778)\" title=\"Invasion of Dominica (1778)\">invades Dominica</a> in the <a href=\"https://wikipedia.org/wiki/British_West_Indies\" title=\"British West Indies\">British West Indies</a>, before Britain is even aware of France's involvement in the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: France <a href=\"https://wikipedia.org/wiki/Invasion_of_Dominica_(1778)\" title=\"Invasion of Dominica (1778)\">invades Dominica</a> in the <a href=\"https://wikipedia.org/wiki/British_West_Indies\" title=\"British West Indies\">British West Indies</a>, before Britain is even aware of France's involvement in the war.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Invasion of Dominica (1778)", "link": "https://wikipedia.org/wiki/Invasion_of_Dominica_(1778)"}, {"title": "British West Indies", "link": "https://wikipedia.org/wiki/British_West_Indies"}]}, {"year": "1812", "text": "French invasion of Russia: The Battle of Borodino, the bloodiest battle of the Napoleonic Wars, is fought near Moscow and results in a French victory.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">French invasion of Russia</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Borodino\" title=\"Battle of Borodino\">Battle of Borodino</a>, the bloodiest battle of the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>, is fought near Moscow and results in a French victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\">French invasion of Russia</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Borodino\" title=\"Battle of Borodino\">Battle of Borodino</a>, the bloodiest battle of the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>, is fought near Moscow and results in a French victory.", "links": [{"title": "French invasion of Russia", "link": "https://wikipedia.org/wiki/French_invasion_of_Russia"}, {"title": "Battle of Borodino", "link": "https://wikipedia.org/wiki/Battle_of_Borodino"}, {"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}]}, {"year": "1818", "text": "<PERSON> of Sweden-Norway is crowned king of Norway, in Trondheim.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Sweden%E2%80%93Norway\" class=\"mw-redirect\" title=\"Sweden-Norway\">Sweden-Norway</a> is crowned king of Norway, in <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XIV_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Charles XIV John of Sweden\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Sweden%E2%80%93Norway\" class=\"mw-redirect\" title=\"Sweden-Norway\">Sweden-Norway</a> is crowned king of Norway, in <a href=\"https://wikipedia.org/wiki/Trondheim\" title=\"Trondheim\">Trondheim</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}, {"title": "Sweden-Norway", "link": "https://wikipedia.org/wiki/Sweden%E2%80%93Norway"}, {"title": "Trondheim", "link": "https://wikipedia.org/wiki/Trondheim"}]}, {"year": "1822", "text": "<PERSON> declares Brazil independent from Portugal on the shores of the Ipiranga Brook in São Paulo.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Pedro_I_of_Brazil\" title=\"Pedro I of Brazil\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Independence_of_Brazil\" title=\"Independence of Brazil\">Brazil independent</a> from Portugal on the shores of the <a href=\"https://wikipedia.org/wiki/Ipiranga_Brook\" title=\"Ipiranga Brook\">Ipiranga Brook</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_I_of_Brazil\" title=\"Pedro I of Brazil\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Independence_of_Brazil\" title=\"Independence of Brazil\">Brazil independent</a> from Portugal on the shores of the <a href=\"https://wikipedia.org/wiki/Ipiranga_Brook\" title=\"Ipiranga Brook\">Ipiranga Brook</a> in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a>.", "links": [{"title": "<PERSON> of Brazil", "link": "https://wikipedia.org/wiki/Pedro_I_of_Brazil"}, {"title": "Independence of Brazil", "link": "https://wikipedia.org/wiki/Independence_of_Brazil"}, {"title": "Ipiranga Brook", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rang<PERSON>_Brook"}, {"title": "São Paulo", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo"}]}, {"year": "1856", "text": "The Saimaa Canal is inaugurated.", "html": "1856 - The <a href=\"https://wikipedia.org/wiki/Saimaa_Canal\" title=\"Saimaa Canal\">Saimaa Canal</a> is inaugurated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Saimaa_Canal\" title=\"Saimaa Canal\">Saimaa Canal</a> is inaugurated.", "links": [{"title": "Saimaa Canal", "link": "https://wikipedia.org/wiki/Saimaa_Canal"}]}, {"year": "1857", "text": "Mountain Meadows massacre: Mormon settlers slaughter most members of peaceful, emigrant wagon train.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Mountain_Meadows_massacre\" class=\"mw-redirect\" title=\"Mountain Meadows massacre\">Mountain Meadows massacre</a>: Mormon settlers slaughter most members of peaceful, emigrant wagon train.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mountain_Meadows_massacre\" class=\"mw-redirect\" title=\"Mountain Meadows massacre\">Mountain Meadows massacre</a>: Mormon settlers slaughter most members of peaceful, emigrant wagon train.", "links": [{"title": "Mountain Meadows massacre", "link": "https://wikipedia.org/wiki/Mountain_Meadows_massacre"}]}, {"year": "1860", "text": "Unification of Italy: <PERSON> enters Naples.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Unification_of_Italy\" title=\"Unification of Italy\">Unification of Italy</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters Naples.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unification_of_Italy\" title=\"Unification of Italy\">Unification of Italy</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters Naples.", "links": [{"title": "Unification of Italy", "link": "https://wikipedia.org/wiki/Unification_of_Italy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "American Civil War: Union troops under <PERSON> capture Fort Wagner in Morris Island after a seven-week siege.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Quincy Adams <PERSON>more\"><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Fort Wagner</a> in <a href=\"https://wikipedia.org/wiki/Morris_Island\" title=\"Morris Island\">Morris Island</a> after a seven-week siege.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Union troops under <a href=\"https://wikipedia.org/wiki/Quincy_<PERSON>_<PERSON>\" title=\"Quincy Adams <PERSON>more\"><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Fort Wagner</a> in <a href=\"https://wikipedia.org/wiki/Morris_Island\" title=\"Morris Island\">Morris Island</a> after a seven-week siege.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Quincy_Adams_<PERSON>"}, {"title": "Fort Wagner", "link": "https://wikipedia.org/wiki/Fort_Wagner"}, {"title": "Morris Island", "link": "https://wikipedia.org/wiki/Morris_Island"}]}, {"year": "1864", "text": "American Civil War: Atlanta is evacuated on orders of Union General <PERSON>.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a> is evacuated on orders of <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a> is evacuated on orders of <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/William_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "In Northfield, Minnesota, <PERSON> and the James-Younger Gang attempt to rob the town's bank but are driven off by armed citizens.", "html": "1876 - In <a href=\"https://wikipedia.org/wiki/Northfield,_Minnesota\" title=\"Northfield, Minnesota\">Northfield, Minnesota</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/James%E2%80%93Younger_Gang\" title=\"<PERSON>-Younger Gang\"><PERSON>-<PERSON> Gang</a> attempt to rob the town's bank but are driven off by armed citizens.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Northfield,_Minnesota\" title=\"Northfield, Minnesota\">Northfield, Minnesota</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/James%E2%80%93Younger_Gang\" title=\"James-Younger Gang\"><PERSON>-<PERSON> Gang</a> attempt to rob the town's bank but are driven off by armed citizens.", "links": [{"title": "Northfield, Minnesota", "link": "https://wikipedia.org/wiki/Northfield,_Minnesota"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>-Younger Gang", "link": "https://wikipedia.org/wiki/James%E2%80%93You<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "The Boxer Rebellion in Qing dynasty (modern-day China) officially ends with the signing of the Boxer Protocol.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> (modern-day <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>) officially ends with the signing of the <a href=\"https://wikipedia.org/wiki/Boxer_Protocol\" title=\"Boxer Protocol\">Boxer Protocol</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a> in <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> (modern-day <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>) officially ends with the signing of the <a href=\"https://wikipedia.org/wiki/Boxer_Protocol\" title=\"Boxer Protocol\">Boxer Protocol</a>.", "links": [{"title": "Boxer Rebellion", "link": "https://wikipedia.org/wiki/Boxer_Rebellion"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Boxer Protocol", "link": "https://wikipedia.org/wiki/Boxer_Protocol"}]}, {"year": "1903", "text": "The Ottoman Empire launches a counter-offensive against the Strandzha Commune, which dissolves.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> launches a counter-offensive against the <a href=\"https://wikipedia.org/wiki/Strandzha_Commune\" title=\"Strandzha Commune\">Strandzha Commune</a>, which dissolves.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> launches a counter-offensive against the <a href=\"https://wikipedia.org/wiki/Strandzha_Commune\" title=\"Strandzha Commune\">Strandzha Commune</a>, which dissolves.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Strandzha_Commune"}]}, {"year": "1906", "text": "<PERSON> flies his 14-bis aircraft at Bagatelle, France successfully for the first time.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies his <a href=\"https://wikipedia.org/wiki/14-bis\" class=\"mw-redirect\" title=\"14-bis\">14-bis</a> aircraft at <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_de_Bagatelle\" title=\"Château de Bagatelle\">Bagate<PERSON></a>, France successfully for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies his <a href=\"https://wikipedia.org/wiki/14-bis\" class=\"mw-redirect\" title=\"14-bis\">14-bis</a> aircraft at <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau_de_Bagatelle\" title=\"Château de Bagatelle\">Ba<PERSON><PERSON></a>, France successfully for the first time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "14-bis", "link": "https://wikipedia.org/wiki/14-bis"}, {"title": "Château de Bagatelle", "link": "https://wikipedia.org/wiki/Ch%C3%A2teau_de_Bagatelle"}]}, {"year": "1907", "text": "Cunard Line's RMS Lusitania sets sail on her maiden voyage from Liverpool, England, to New York City.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a>'s <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> sets sail on her maiden voyage from <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, England, to New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a>'s <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> sets sail on her maiden voyage from <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>, England, to New York City.", "links": [{"title": "Cunard Line", "link": "https://wikipedia.org/wiki/Cunard_Line"}, {"title": "RMS Lusitania", "link": "https://wikipedia.org/wiki/RMS_Lusitania"}, {"title": "Liverpool", "link": "https://wikipedia.org/wiki/Liverpool"}]}, {"year": "1909", "text": "<PERSON> crashes a new French-built Wright biplane during a test flight at Juvisy, south of Paris, becoming the first aviator in the world to lose his life piloting a powered heavier-than-air craft.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crashes a new French-built <a href=\"https://wikipedia.org/wiki/<PERSON>_Model_A\" title=\"Wright Model A\"><PERSON></a> biplane during a test flight at <a href=\"https://wikipedia.org/wiki/Juvisy\" class=\"mw-redirect\" title=\"Juvisy\">Juvisy</a>, south of Paris, becoming the first aviator in the world to lose his life piloting a powered heavier-than-air craft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crashes a new French-built <a href=\"https://wikipedia.org/wiki/<PERSON>_Model_A\" title=\"Wright Model A\"><PERSON></a> biplane during a test flight at <a href=\"https://wikipedia.org/wiki/Juvisy\" class=\"mw-redirect\" title=\"Juvisy\">Juvisy</a>, south of Paris, becoming the first aviator in the world to lose his life piloting a powered heavier-than-air craft.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>vre"}, {"title": "Wright Model A", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_A"}, {"title": "Juvisy", "link": "https://wikipedia.org/wiki/Juvisy"}]}, {"year": "1911", "text": "French poet <PERSON> is arrested and put in jail on suspicion of stealing the Mona Lisa from the Louvre museum.", "html": "1911 - French poet <a href=\"https://wikipedia.org/wiki/Guillaume_Apollinaire\" title=\"Guillaume Apollinaire\">Guillaume Apollinaire</a> is arrested and put in jail on suspicion of stealing the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> from the <a href=\"https://wikipedia.org/wiki/Louvre\" title=\"Louvre\">Louvre</a> museum.", "no_year_html": "French poet <a href=\"https://wikipedia.org/wiki/<PERSON>_Apollinaire\" title=\"Guillaume Apollinaire\"><PERSON>ire</a> is arrested and put in jail on suspicion of stealing the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> from the <a href=\"https://wikipedia.org/wiki/Louvre\" title=\"Louvre\">Louvre</a> museum.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillaume_Apollinaire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vre"}]}, {"year": "1916", "text": "US federal employees win the right to Workers' compensation by Federal Employers Liability Act (39 Stat. 742; 5 U.S.C. 751)", "html": "1916 - US federal employees win the right to <a href=\"https://wikipedia.org/wiki/Workers%27_compensation\" title=\"Workers' compensation\">Workers' compensation</a> by <a href=\"https://wikipedia.org/wiki/Federal_Employers_Liability_Act\" title=\"Federal Employers Liability Act\">Federal Employers Liability Act</a> (39 Stat. 742; 5 U.S.C. 751)", "no_year_html": "US federal employees win the right to <a href=\"https://wikipedia.org/wiki/Workers%27_compensation\" title=\"Workers' compensation\">Workers' compensation</a> by <a href=\"https://wikipedia.org/wiki/Federal_Employers_Liability_Act\" title=\"Federal Employers Liability Act\">Federal Employers Liability Act</a> (39 Stat. 742; 5 U.S.C. 751)", "links": [{"title": "Workers' compensation", "link": "https://wikipedia.org/wiki/Workers%27_compensation"}, {"title": "Federal Employers Liability Act", "link": "https://wikipedia.org/wiki/Federal_Employers_Liability_Act"}]}, {"year": "1920", "text": "Two newly purchased Savoia flying boats crash in the Swiss Alps en route to Finland where they were to serve with the Finnish Air Force, killing both crews.", "html": "1920 - Two newly purchased <a href=\"https://wikipedia.org/wiki/Savoia-Marchetti\" class=\"mw-redirect\" title=\"Savoia-Marchetti\">Savoia flying boats</a> crash in the <a href=\"https://wikipedia.org/wiki/Swiss_Alps\" title=\"Swiss Alps\">Swiss Alps</a> en route to Finland where they were to serve with the <a href=\"https://wikipedia.org/wiki/Finnish_Air_Force\" title=\"Finnish Air Force\">Finnish Air Force</a>, killing both crews.", "no_year_html": "Two newly purchased <a href=\"https://wikipedia.org/wiki/Savoia-Marchetti\" class=\"mw-redirect\" title=\"Savoia-Marchetti\">Savoia flying boats</a> crash in the <a href=\"https://wikipedia.org/wiki/Swiss_Alps\" title=\"Swiss Alps\">Swiss Alps</a> en route to Finland where they were to serve with the <a href=\"https://wikipedia.org/wiki/Finnish_Air_Force\" title=\"Finnish Air Force\">Finnish Air Force</a>, killing both crews.", "links": [{"title": "Savoia-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Savoia-Marchetti"}, {"title": "Swiss Alps", "link": "https://wikipedia.org/wiki/Swiss_Alps"}, {"title": "Finnish Air Force", "link": "https://wikipedia.org/wiki/Finnish_Air_Force"}]}, {"year": "1921", "text": "In Atlantic City, New Jersey, the first Miss America Pageant, a two-day event, is held.", "html": "1921 - In <a href=\"https://wikipedia.org/wiki/Atlantic_City,_New_Jersey\" title=\"Atlantic City, New Jersey\">Atlantic City, New Jersey</a>, the first <a href=\"https://wikipedia.org/wiki/Miss_America_Pageant\" class=\"mw-redirect\" title=\"Miss America Pageant\">Miss America Pageant</a>, a two-day event, is held.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Atlantic_City,_New_Jersey\" title=\"Atlantic City, New Jersey\">Atlantic City, New Jersey</a>, the first <a href=\"https://wikipedia.org/wiki/Miss_America_Pageant\" class=\"mw-redirect\" title=\"Miss America Pageant\">Miss America Pageant</a>, a two-day event, is held.", "links": [{"title": "Atlantic City, New Jersey", "link": "https://wikipedia.org/wiki/Atlantic_City,_New_Jersey"}, {"title": "Miss America Pageant", "link": "https://wikipedia.org/wiki/Miss_America_Pageant"}]}, {"year": "1921", "text": "The Legion of Mary, the largest apostolic organization of lay people in the Catholic Church, is founded in Dublin, Ireland.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Legion_of_Mary\" title=\"Legion of Mary\">Legion of Mary</a>, the largest <a href=\"https://wikipedia.org/wiki/Consecrated_life#Apostolic_congregations\" title=\"Consecrated life\">apostolic</a> organization of lay people in the Catholic Church, is founded in Dublin, Ireland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Legion_of_Mary\" title=\"Legion of Mary\">Legion of Mary</a>, the largest <a href=\"https://wikipedia.org/wiki/Consecrated_life#Apostolic_congregations\" title=\"Consecrated life\">apostolic</a> organization of lay people in the Catholic Church, is founded in Dublin, Ireland.", "links": [{"title": "Legion of Mary", "link": "https://wikipedia.org/wiki/Legion_of_Mary"}, {"title": "Consecrated life", "link": "https://wikipedia.org/wiki/Consecrated_life#Apostolic_congregations"}]}, {"year": "1923", "text": "The International Criminal Police Organization (INTERPOL) is formed.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/Interpol\" title=\"Interpol\">International Criminal Police Organization (INTERPOL)</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Interpol\" title=\"Interpol\">International Criminal Police Organization (INTERPOL)</a> is formed.", "links": [{"title": "Interpol", "link": "https://wikipedia.org/wiki/Interpol"}]}, {"year": "1927", "text": "The first fully electronic television system is achieved by <PERSON><PERSON>.", "html": "1927 - The first fully electronic television system is achieved by <a href=\"https://wikipedia.org/wiki/Philo_Farnsworth\" title=\"Phil<PERSON> Farnsworth\"><PERSON><PERSON></a>.", "no_year_html": "The first fully electronic television system is achieved by <a href=\"https://wikipedia.org/wiki/Philo_Farnsworth\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Philo_Farnsworth"}]}, {"year": "1929", "text": "Steamer Kuru capsizes and sinks on Lake Näsijärvi near Tampere in Finland. One hundred thirty-six lives are lost.", "html": "1929 - Steamer <a href=\"https://wikipedia.org/wiki/SS_Kuru\" title=\"SS Kuru\"><i><PERSON><PERSON></i></a> capsizes and sinks on Lake <a href=\"https://wikipedia.org/wiki/N%C3%A4sij%C3%A4rvi\" title=\"Näsijärvi\">Näsij<PERSON>rvi</a> near <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tam<PERSON><PERSON></a> in Finland. One hundred thirty-six lives are lost.", "no_year_html": "Steamer <a href=\"https://wikipedia.org/wiki/SS_Kuru\" title=\"SS Kuru\"><i><PERSON><PERSON></i></a> capsizes and sinks on Lake <a href=\"https://wikipedia.org/wiki/N%C3%A4sij%C3%A4rvi\" title=\"Näsijärvi\">Näsij<PERSON>rvi</a> near <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\"><PERSON><PERSON><PERSON></a> in Finland. One hundred thirty-six lives are lost.", "links": [{"title": "SS Kuru", "link": "https://wikipedia.org/wiki/SS_Kuru"}, {"title": "<PERSON>äsi<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A4sij%C3%A4rvi"}, {"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}]}, {"year": "1932", "text": "The Battle of Boquerón, the first major battle of the Chaco War, commences.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Boquer%C3%B3n_(1932)\" title=\"Battle of Boquerón (1932)\">Battle of Boquerón</a>, the first major battle of the <a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a>, commences.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Boquer%C3%B3n_(1932)\" title=\"Battle of Boquerón (1932)\">Battle of Boquerón</a>, the first major battle of the <a href=\"https://wikipedia.org/wiki/Chaco_War\" title=\"Chaco War\">Chaco War</a>, commences.", "links": [{"title": "Battle of Boquerón (1932)", "link": "https://wikipedia.org/wiki/Battle_of_Boquer%C3%B3n_(1932)"}, {"title": "Chaco War", "link": "https://wikipedia.org/wiki/Chaco_War"}]}, {"year": "1936", "text": "The last thylacine, a carnivorous marsupial, dies alone in its cage at the Hobart Zoo in Tasmania.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Thylacine#Last_of_the_species\" title=\"Thylacine\">The last thylacine</a>, a carnivorous marsupial, dies alone in its cage at the Hobart Zoo in Tasmania.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thylacine#Last_of_the_species\" title=\"Thylacine\">The last thylacine</a>, a carnivorous marsupial, dies alone in its cage at the Hobart Zoo in Tasmania.", "links": [{"title": "Thylacine", "link": "https://wikipedia.org/wiki/Thylacine#Last_of_the_species"}]}, {"year": "1940", "text": "Romania returns Southern Dobruja to Bulgaria under the Treaty of Craiova.", "html": "1940 - Romania returns <a href=\"https://wikipedia.org/wiki/Southern_Dobruja\" title=\"Southern Dobruja\">Southern Dobruja</a> to Bulgaria under the <a href=\"https://wikipedia.org/wiki/Treaty_of_Craiova\" title=\"Treaty of Craiova\">Treaty of Craiova</a>.", "no_year_html": "Romania returns <a href=\"https://wikipedia.org/wiki/Southern_Dobruja\" title=\"Southern Dobruja\">Southern Dobruja</a> to Bulgaria under the <a href=\"https://wikipedia.org/wiki/Treaty_of_Craiova\" title=\"Treaty of Craiova\">Treaty of Craiova</a>.", "links": [{"title": "Southern Dobruja", "link": "https://wikipedia.org/wiki/Southern_Dobruja"}, {"title": "Treaty of Craiova", "link": "https://wikipedia.org/wiki/Treaty_of_Craiova"}]}, {"year": "1940", "text": "World War II: The German Luftwaffe begins the Blitz, bombing London and other British cities for over 50 consecutive nights.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German Luftwaffe begins <a href=\"https://wikipedia.org/wiki/The_Blitz\" title=\"The Blitz\">the Blitz</a>, bombing London and other British cities for over 50 consecutive nights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German Luftwaffe begins <a href=\"https://wikipedia.org/wiki/The_Blitz\" title=\"The Blitz\">the Blitz</a>, bombing London and other British cities for over 50 consecutive nights.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "The Blitz", "link": "https://wikipedia.org/wiki/The_Blitz"}]}, {"year": "1942", "text": "World War II: Japanese marines are forced to withdraw during the Battle of Milne Bay.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Special_Naval_Landing_Forces\" title=\"Special Naval Landing Forces\">Japanese marines</a> are forced to withdraw during the <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Special_Naval_Landing_Forces\" title=\"Special Naval Landing Forces\">Japanese marines</a> are forced to withdraw during the <a href=\"https://wikipedia.org/wiki/Battle_of_Milne_Bay\" title=\"Battle of Milne Bay\">Battle of Milne Bay</a>.", "links": [{"title": "Special Naval Landing Forces", "link": "https://wikipedia.org/wiki/Special_Naval_Landing_Forces"}, {"title": "Battle of Milne Bay", "link": "https://wikipedia.org/wiki/Battle_of_Milne_Bay"}]}, {"year": "1943", "text": "A fire at the Gulf Hotel in Houston kills 55 people.", "html": "1943 - A <a href=\"https://wikipedia.org/wiki/Gulf_Hotel_fire\" title=\"Gulf Hotel fire\">fire at the Gulf Hotel</a> in Houston kills 55 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Gulf_Hotel_fire\" title=\"Gulf Hotel fire\">fire at the Gulf Hotel</a> in Houston kills 55 people.", "links": [{"title": "Gulf Hotel fire", "link": "https://wikipedia.org/wiki/Gulf_Hotel_fire"}]}, {"year": "1943", "text": "World War II: The German 17th Army begins its evacuation of the Kuban bridgehead (Taman Peninsula) in southern Russia and moves across the Strait of Kerch to the Crimea.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/17th_Army_(Wehrmacht)\" title=\"17th Army (Wehrmacht)\">German 17th Army</a> begins its evacuation of the <a href=\"https://wikipedia.org/wiki/Kuban_bridgehead\" title=\"Kuban bridgehead\">Kuban bridgehead</a> (Taman Peninsula) in southern Russia and moves across the Strait of Kerch to the Crimea.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/17th_Army_(Wehrmacht)\" title=\"17th Army (Wehrmacht)\">German 17th Army</a> begins its evacuation of the <a href=\"https://wikipedia.org/wiki/Kuban_bridgehead\" title=\"Kuban bridgehead\">Kuban bridgehead</a> (Taman Peninsula) in southern Russia and moves across the Strait of Kerch to the Crimea.", "links": [{"title": "17th Army (Wehrmacht)", "link": "https://wikipedia.org/wiki/17th_Army_(Wehrmacht)"}, {"title": "Kuban bridgehead", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bridgehead"}]}, {"year": "1945", "text": "World War II: Japanese forces on Wake Island, which they had held since December 1941, surrender to U.S. Marines.", "html": "1945 - World War II: Japanese forces on <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a>, which they had held since December 1941, surrender to U.S. Marines.", "no_year_html": "World War II: Japanese forces on <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a>, which they had held since December 1941, surrender to U.S. Marines.", "links": [{"title": "Wake Island", "link": "https://wikipedia.org/wiki/Wake_Island"}]}, {"year": "1945", "text": "The Berlin Victory Parade of 1945 is held.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Berlin_Victory_Parade_of_1945\" title=\"Berlin Victory Parade of 1945\">Berlin Victory Parade of 1945</a> is held.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Berlin_Victory_Parade_of_1945\" title=\"Berlin Victory Parade of 1945\">Berlin Victory Parade of 1945</a> is held.", "links": [{"title": "Berlin Victory Parade of 1945", "link": "https://wikipedia.org/wiki/Berlin_Victory_Parade_of_1945"}]}, {"year": "1953", "text": "<PERSON><PERSON> is elected first secretary of the Communist Party of the Soviet Union.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">first secretary</a> of the Communist Party of the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">first secretary</a> of the Communist Party of the Soviet Union.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1963", "text": "The Pro Football Hall of Fame opens in Canton, Ohio with 17 charter members.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Pro Football Hall of Fame</a> opens in Canton, Ohio with 17 charter members.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame\" title=\"Pro Football Hall of Fame\">Pro Football Hall of Fame</a> opens in Canton, Ohio with 17 charter members.", "links": [{"title": "Pro Football Hall of Fame", "link": "https://wikipedia.org/wiki/Pro_Football_Hall_of_Fame"}]}, {"year": "1965", "text": "During an Indo-Pakistani War, China announces that it will reinforce its troops on the Indian border.", "html": "1965 - During an <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War</a>, China announces that it will reinforce its troops on the Indian border.", "no_year_html": "During an <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1965\">Indo-Pakistani War</a>, China announces that it will reinforce its troops on the Indian border.", "links": [{"title": "Indo-Pakistani War of 1965", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1965"}]}, {"year": "1965", "text": "Vietnam War: In a follow-up to August's Operation Starlite, United States Marines and South Vietnamese forces initiate Operation Piranha on the Batangan Peninsula.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In a follow-up to August's <a href=\"https://wikipedia.org/wiki/Operation_Starlite\" title=\"Operation Starlite\">Operation Starlite</a>, United States Marines and South Vietnamese forces initiate <a href=\"https://wikipedia.org/wiki/Operation_Piranha\" title=\"Operation Piranha\">Operation Piranha</a> on the Batangan Peninsula.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: In a follow-up to August's <a href=\"https://wikipedia.org/wiki/Operation_Starlite\" title=\"Operation Starlite\">Operation Starlite</a>, United States Marines and South Vietnamese forces initiate <a href=\"https://wikipedia.org/wiki/Operation_Piranha\" title=\"Operation Piranha\">Operation Piranha</a> on the Batangan Peninsula.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Starlite", "link": "https://wikipedia.org/wiki/Operation_Starlite"}, {"title": "Operation Piranha", "link": "https://wikipedia.org/wiki/Operation_Piranha"}]}, {"year": "1970", "text": "Fighting begins between Arab guerrillas and government forces in Jordan.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Black_September\" title=\"Black September\">Fighting</a> begins between Arab guerrillas and government forces in Jordan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_September\" title=\"Black September\">Fighting</a> begins between Arab guerrillas and government forces in Jordan.", "links": [{"title": "Black September", "link": "https://wikipedia.org/wiki/Black_September"}]}, {"year": "1970", "text": "Vietnam Television was established.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_Television\" title=\"Vietnam Television\">Vietnam Television</a> was established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_Television\" title=\"Vietnam Television\">Vietnam Television</a> was established.", "links": [{"title": "Vietnam Television", "link": "https://wikipedia.org/wiki/Vietnam_Television"}]}, {"year": "1977", "text": "The Torrijos-Carter Treaties between Panama and the United States on the status of the Panama Canal are signed. The United States agrees to transfer control of the canal to Panama at the end of the 20th century.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Treaties\"><PERSON><PERSON><PERSON>-<PERSON> Treaties</a> between <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> and the United States on the status of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> are signed. The United States agrees to transfer control of the canal to Panama at the end of the 20th century.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties\" title=\"<PERSON><PERSON><PERSON><PERSON>Carter Treaties\"><PERSON><PERSON><PERSON>-<PERSON> Treaties</a> between <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> and the United States on the status of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> are signed. The United States agrees to transfer control of the canal to Panama at the end of the 20th century.", "links": [{"title": "Torrijos<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Torrijos%E2%80%93Carter_Treaties"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}]}, {"year": "1977", "text": "The 300-metre-tall CKVR-DT transmission tower in Barrie, Ontario, Canada, is hit by a light aircraft in a fog, causing it to collapse. All aboard the aircraft are killed.", "html": "1977 - The 300-metre-tall <a href=\"https://wikipedia.org/wiki/CKVR-DT\" title=\"CKVR-DT\">CKVR-DT</a> transmission tower in Barrie, Ontario, Canada, is hit by a light aircraft in a fog, causing it to collapse. All aboard the aircraft are killed.", "no_year_html": "The 300-metre-tall <a href=\"https://wikipedia.org/wiki/CKVR-DT\" title=\"CKVR-DT\">CKVR-DT</a> transmission tower in Barrie, Ontario, Canada, is hit by a light aircraft in a fog, causing it to collapse. All aboard the aircraft are killed.", "links": [{"title": "CKVR-DT", "link": "https://wikipedia.org/wiki/CKVR-DT"}]}, {"year": "1978", "text": "While walking across Waterloo Bridge in London, Bulgarian dissident <PERSON><PERSON> is assassinated by Bulgarian secret police agent <PERSON> by means of a ricin pellet fired from a specially-designed umbrella.", "html": "1978 - While walking across Waterloo Bridge in London, Bulgarian dissident <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is assassinated by Bulgarian secret police agent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by means of a <a href=\"https://wikipedia.org/wiki/Ricin\" title=\"Ricin\">ricin</a> pellet fired from a specially-designed umbrella.", "no_year_html": "While walking across Waterloo Bridge in London, Bulgarian dissident <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is assassinated by Bulgarian secret police agent <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by means of a <a href=\"https://wikipedia.org/wiki/Ricin\" title=\"Ricin\">ricin</a> pellet fired from a specially-designed umbrella.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ricin", "link": "https://wikipedia.org/wiki/Ricin"}]}, {"year": "1979", "text": "The Chrysler Corporation asks the United States government for US$1.5 billion to avoid bankruptcy.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/Chrysler_Corporation\" class=\"mw-redirect\" title=\"Chrysler Corporation\">Chrysler Corporation</a> asks the United States government for US$1.5 billion to avoid bankruptcy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chrysler_Corporation\" class=\"mw-redirect\" title=\"Chrysler Corporation\">Chrysler Corporation</a> asks the United States government for US$1.5 billion to avoid bankruptcy.", "links": [{"title": "Chrysler Corporation", "link": "https://wikipedia.org/wiki/Chrysler_Corporation"}]}, {"year": "1984", "text": "An explosion on board a Maltese patrol boat disposing of illegal fireworks at sea off Gozo kills seven soldiers and policemen.", "html": "1984 - An <a href=\"https://wikipedia.org/wiki/C23_tragedy\" title=\"C23 tragedy\">explosion</a> on board a Maltese patrol boat disposing of illegal fireworks at sea off Gozo kills seven soldiers and policemen.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/C23_tragedy\" title=\"C23 tragedy\">explosion</a> on board a Maltese patrol boat disposing of illegal fireworks at sea off Gozo kills seven soldiers and policemen.", "links": [{"title": "C23 tragedy", "link": "https://wikipedia.org/wiki/C23_tragedy"}]}, {"year": "1986", "text": "<PERSON> becomes the first black man to lead the Anglican Diocese of Cape Town.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black man to lead the <a href=\"https://wikipedia.org/wiki/Anglican_Diocese_of_Cape_Town\" title=\"Anglican Diocese of Cape Town\">Anglican Diocese of Cape Town</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black man to lead the <a href=\"https://wikipedia.org/wiki/Anglican_Diocese_of_Cape_Town\" title=\"Anglican Diocese of Cape Town\">Anglican Diocese of Cape Town</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Anglican Diocese of Cape Town", "link": "https://wikipedia.org/wiki/Anglican_Diocese_of_Cape_Town"}]}, {"year": "1986", "text": "Chilean dictator <PERSON><PERSON> survives an assassination attempt by the FPMR; five of <PERSON><PERSON><PERSON>'s bodyguards are killed.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> survives <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON><PERSON>\">an assassination attempt</a> by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Patriotic_Front\" title=\"<PERSON> Patriotic Front\">FPMR</a>; five of <PERSON><PERSON><PERSON>'s bodyguards are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> dictator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> survives <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON><PERSON>\">an assassination attempt</a> by the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Patriotic_Front\" title=\"<PERSON> Patriotic Front\">FPMR</a>; five of <PERSON><PERSON><PERSON>'s bodyguards are killed.", "links": [{"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Attempted assassination of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> Patriotic Front", "link": "https://wikipedia.org/wiki/<PERSON>_Rod<PERSON>%C3%ADguez_Patriotic_Front"}]}, {"year": "1995", "text": "Space Shuttle Endeavour is launched on STS-69, the second flight of the Wake Shield Facility.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-69\" title=\"STS-69\">STS-69</a>, the second flight of the <a href=\"https://wikipedia.org/wiki/Wake_Shield_Facility\" title=\"Wake Shield Facility\">Wake Shield Facility</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-69\" title=\"STS-69\">STS-69</a>, the second flight of the <a href=\"https://wikipedia.org/wiki/Wake_Shield_Facility\" title=\"Wake Shield Facility\">Wake Shield Facility</a>.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-69", "link": "https://wikipedia.org/wiki/STS-69"}, {"title": "Wake Shield Facility", "link": "https://wikipedia.org/wiki/Wake_Shield_Facility"}]}, {"year": "1997", "text": "Maiden flight of the Lockheed Martin F-22 Raptor.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Maiden_flight\" title=\"Maiden flight\">Maiden flight</a> of the <a href=\"https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor\" title=\"Lockheed Martin F-22 Raptor\">Lockheed Martin F-22 Raptor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maiden_flight\" title=\"Maiden flight\">Maiden flight</a> of the <a href=\"https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor\" title=\"Lockheed Martin F-22 Raptor\">Lockheed Martin F-22 Raptor</a>.", "links": [{"title": "Maiden flight", "link": "https://wikipedia.org/wiki/Maiden_flight"}, {"title": "Lockheed Martin F-22 Raptor", "link": "https://wikipedia.org/wiki/Lockheed_Martin_F-22_Raptor"}]}, {"year": "1999", "text": "The 6.0 Mw  Athens earthquake affected the area with a maximum Mercalli intensity of IX (Violent), killing 143, injuring 800-1,600, and leaving 50,000 homeless.", "html": "1999 - The 6.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_Athens_earthquake\" title=\"1999 Athens earthquake\">Athens earthquake</a> affected the area with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing 143, injuring 800-1,600, and leaving 50,000 homeless.", "no_year_html": "The 6.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_Athens_earthquake\" title=\"1999 Athens earthquake\">Athens earthquake</a> affected the area with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing 143, injuring 800-1,600, and leaving 50,000 homeless.", "links": [{"title": "1999 Athens earthquake", "link": "https://wikipedia.org/wiki/1999_Athens_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2005", "text": "Egypt holds its first-ever multi-party presidential election.", "html": "2005 - Egypt holds its first-ever <a href=\"https://wikipedia.org/wiki/2005_Egyptian_presidential_election\" title=\"2005 Egyptian presidential election\">multi-party presidential election</a>.", "no_year_html": "Egypt holds its first-ever <a href=\"https://wikipedia.org/wiki/2005_Egyptian_presidential_election\" title=\"2005 Egyptian presidential election\">multi-party presidential election</a>.", "links": [{"title": "2005 Egyptian presidential election", "link": "https://wikipedia.org/wiki/2005_Egyptian_presidential_election"}]}, {"year": "2008", "text": "The United States government takes control of the two largest mortgage financing companies in the US, Fannie Mae and Freddie Mac.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Federal_takeover_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Federal takeover of <PERSON><PERSON> Mae and <PERSON>\">United States government takes control of</a> the two largest mortgage financing companies in the US, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mae\" title=\"Fan<PERSON> Mae\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_Mac\" title=\"<PERSON> Mac\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_takeover_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Federal takeover of <PERSON><PERSON> and <PERSON>\">United States government takes control of</a> the two largest mortgage financing companies in the US, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mae\" title=\"Fannie Mae\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mac\"><PERSON></a>.", "links": [{"title": "Federal takeover of <PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Federal_takeover_of_<PERSON><PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "A Chinese fishing trawler collides with two Japanese Coast Guard patrol boats in disputed waters near the Senkaku Islands.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/2010_Senkaku_boat_collision_incident\" title=\"2010 Senkaku boat collision incident\">A Chinese fishing trawler</a> collides with two Japanese Coast Guard patrol boats in disputed waters near the <a href=\"https://wikipedia.org/wiki/Senkaku_Islands\" title=\"Senkaku Islands\">Senkaku Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2010_Senkaku_boat_collision_incident\" title=\"2010 Senkaku boat collision incident\">A Chinese fishing trawler</a> collides with two Japanese Coast Guard patrol boats in disputed waters near the <a href=\"https://wikipedia.org/wiki/Senkaku_Islands\" title=\"Senkaku Islands\">Senkaku Islands</a>.", "links": [{"title": "2010 Senkaku boat collision incident", "link": "https://wikipedia.org/wiki/2010_<PERSON><PERSON><PERSON>_boat_collision_incident"}, {"title": "Senkaku Islands", "link": "https://wikipedia.org/wiki/Senkaku_Islands"}]}, {"year": "2011", "text": "The Lokomotiv Yaroslavl plane crash in Russia kills 43 people, including nearly the entire roster of the Lokomotiv Yaroslavl Kontinental Hockey League team.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Lokomotiv_Yaroslavl_plane_crash\" title=\"Lokomotiv Yaroslavl plane crash\">Lokomotiv Yaroslavl plane crash</a> in Russia kills 43 people, including nearly the entire roster of the <a href=\"https://wikipedia.org/wiki/Lokomotiv_Yaroslavl\" title=\"Lokomotiv Yaroslavl\">Lokomotiv Yaroslavl</a> <a href=\"https://wikipedia.org/wiki/Kontinental_Hockey_League\" title=\"Kontinental Hockey League\">Kontinental Hockey League</a> team.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lokomotiv_Yaroslavl_plane_crash\" title=\"Lokomotiv Yaroslavl plane crash\">Lokomotiv Yaroslavl plane crash</a> in Russia kills 43 people, including nearly the entire roster of the <a href=\"https://wikipedia.org/wiki/Lokomotiv_Yaroslavl\" title=\"Lokomotiv Yaroslavl\">Lokomotiv Yaroslavl</a> <a href=\"https://wikipedia.org/wiki/Kontinental_Hockey_League\" title=\"Kontinental Hockey League\">Kontinental Hockey League</a> team.", "links": [{"title": "Lokomotiv Yaroslavl plane crash", "link": "https://wikipedia.org/wiki/Lokomotiv_Yaroslavl_plane_crash"}, {"title": "Lokomotiv Yaroslavl", "link": "https://wikipedia.org/wiki/Lokomotiv_Yaroslavl"}, {"title": "Kontinental Hockey League", "link": "https://wikipedia.org/wiki/Kontinental_Hockey_League"}]}, {"year": "2012", "text": "Canada officially cuts diplomatic ties with Iran by closing its embassy in Tehran and orders the expulsion of Iranian diplomats from Ottawa, over nuclear plans and purported human rights abuses.", "html": "2012 - Canada officially <a href=\"https://wikipedia.org/wiki/Canada%E2%80%93Iran_relations\" title=\"Canada-Iran relations\">cuts diplomatic ties</a> with <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> by closing its embassy in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> and orders the expulsion of Iranian diplomats from <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, over nuclear plans and purported human rights abuses.", "no_year_html": "Canada officially <a href=\"https://wikipedia.org/wiki/Canada%E2%80%93Iran_relations\" title=\"Canada-Iran relations\">cuts diplomatic ties</a> with <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> by closing its embassy in <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a> and orders the expulsion of Iranian diplomats from <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, over nuclear plans and purported human rights abuses.", "links": [{"title": "Canada-Iran relations", "link": "https://wikipedia.org/wiki/Canada%E2%80%93Iran_relations"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}]}, {"year": "2017", "text": "The 8.2 Mw  2017 Chiapas earthquake strikes southern Mexico, killing at least 60 people.", "html": "2017 - The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2017_Chiapas_earthquake\" title=\"2017 Chiapas earthquake\">2017 Chiapas earthquake</a> strikes southern <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>, killing at least 60 people.", "no_year_html": "The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2017_Chiapas_earthquake\" title=\"2017 Chiapas earthquake\">2017 Chiapas earthquake</a> strikes southern <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>, killing at least 60 people.", "links": [{"title": "2017 Chiapas earthquake", "link": "https://wikipedia.org/wiki/2017_Chiapas_earthquake"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}, {"year": "2019", "text": "Ukrainian filmmaker <PERSON><PERSON> and 66 others are released in a prisoner exchange between Ukraine and Russia.", "html": "2019 - Ukrainian filmmaker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and 66 others are released in a prisoner exchange between <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> and <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "no_year_html": "Ukrainian filmmaker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and 66 others are released in a prisoner exchange between <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> and <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "2021", "text": "Bitcoin becomes legal tender in El Salvador.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Bitcoin\" title=\"Bitcoin\">Bitcoin</a> becomes legal tender in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bitcoin\" title=\"Bitcoin\">Bitcoin</a> becomes legal tender in <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>.", "links": [{"title": "Bitcoin", "link": "https://wikipedia.org/wiki/Bitcoin"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}]}, {"year": "2021", "text": "The National Unity Government of Myanmar declares a people's defensive war against the military junta during the Myanmar civil war.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/National_Unity_Government_of_Myanmar\" title=\"National Unity Government of Myanmar\">National Unity Government of Myanmar</a> declares a <a href=\"https://wikipedia.org/wiki/People%27s_war\" title=\"People's war\">people's</a> <a href=\"https://wikipedia.org/wiki/Defensive_war\" title=\"Defensive war\">defensive war</a> against the military junta during the <a href=\"https://wikipedia.org/wiki/Myanmar_civil_war_(2021%E2%80%93present)\" title=\"Myanmar civil war (2021-present)\">Myanmar civil war</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Unity_Government_of_Myanmar\" title=\"National Unity Government of Myanmar\">National Unity Government of Myanmar</a> declares a <a href=\"https://wikipedia.org/wiki/People%27s_war\" title=\"People's war\">people's</a> <a href=\"https://wikipedia.org/wiki/Defensive_war\" title=\"Defensive war\">defensive war</a> against the military junta during the <a href=\"https://wikipedia.org/wiki/Myanmar_civil_war_(2021%E2%80%93present)\" title=\"Myanmar civil war (2021-present)\">Myanmar civil war</a>.", "links": [{"title": "National Unity Government of Myanmar", "link": "https://wikipedia.org/wiki/National_Unity_Government_of_Myanmar"}, {"title": "People's war", "link": "https://wikipedia.org/wiki/People%27s_war"}, {"title": "Defensive war", "link": "https://wikipedia.org/wiki/Defensive_war"}, {"title": "Myanmar civil war (2021-present)", "link": "https://wikipedia.org/wiki/Myanmar_civil_war_(2021%E2%80%93present)"}]}], "Births": [{"year": "923", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (d. 952)", "html": "923 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 952)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}]}, {"year": "1395", "text": "<PERSON>, 6th Baron <PERSON>, English politician (d. 1427)", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician (d. 1427)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician (d. 1427)", "links": [{"title": "<PERSON>, 6th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1438", "text": "<PERSON>, Landgrave of Lower Hesse (d. 1471)", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Lower_Hesse\" title=\"<PERSON>, Landgrave of Lower Hesse\"><PERSON>, Landgrave of Lower Hesse</a> (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Lower_Hesse\" title=\"<PERSON>, Landgrave of Lower Hesse\"><PERSON>, Landgrave of Lower Hesse</a> (d. 1471)", "links": [{"title": "<PERSON>, Landgrave of Lower Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Lower_Hesse"}]}, {"year": "1448", "text": "<PERSON>, Count of Württemberg-<PERSON><PERSON><PERSON><PERSON><PERSON> (1473-1482) (d. 1519)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_W%C3%BCrttemberg\" class=\"mw-redirect\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a>-<PERSON><PERSON><PERSON><PERSON><PERSON> (1473-1482) (d. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_W%C3%BCrttemberg\" class=\"mw-redirect\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a>-<PERSON><PERSON><PERSON><PERSON><PERSON> (1473-1482) (d. 1519)", "links": [{"title": "<PERSON>, Count of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_W%C3%BCrt<PERSON><PERSON>"}]}, {"year": "1500", "text": "<PERSON>, Carthusian monk and martyr (d. 1535)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sebastian <PERSON>\"><PERSON></a>, Carthusian monk and martyr (d. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sebastian <PERSON>\"><PERSON></a>, Carthusian monk and martyr (d. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Newdigate"}]}, {"year": "1524", "text": "<PERSON>, Swiss physician and theologian (d. 1583)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and theologian (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physician and theologian (d. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON> of England (d. 1603)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a> of England (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth I\"><PERSON></a> of England (d. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "Sir <PERSON>, 1st Baronet, Irish nobleman (d. 1665)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Irish nobleman (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Irish nobleman (d. 1665)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1635", "text": "<PERSON>, Prince <PERSON>, Hungarian prince (d. 1713)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON><PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a>, Hungarian prince (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince <PERSON></a>, Hungarian prince (d. 1713)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>%C3%A1zy"}]}, {"year": "1641", "text": "<PERSON>, Japanese shōgun (d. 1680)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ietsuna\" title=\"Tokugawa Ietsuna\">Tokugawa I<PERSON></a>, Japanese shōgun (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Ietsuna\" title=\"Tokugawa Ietsuna\">Tokugawa Ietsuna</a>, Japanese shō<PERSON> (d. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tokugawa_Ietsuna"}]}, {"year": "1650", "text": "<PERSON>, 8th duke of Escalona (d. 1725)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_de_la_Aurora,_8th_duke_of_Escalona\" class=\"mw-redirect\" title=\"<PERSON>, 8th duke of Escalona\"><PERSON>, 8th duke of Escalona</a> (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_de_la_Aurora,_8th_duke_of_Escalona\" class=\"mw-redirect\" title=\"<PERSON>, 8th duke of Escalona\"><PERSON>, 8th duke of Escalona</a> (d. 1725)", "links": [{"title": "<PERSON>, 8th duke of Escalona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_de_la_Aurora,_8th_duke_of_Escalona"}]}, {"year": "1683", "text": "<PERSON> of Austria (d. 1754)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1754)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish Minister of State (d. 1763)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Ledreborg\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>Ledreborg</a>, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Denmark\" class=\"mw-redirect\" title=\"List of Prime Ministers of Denmark\">Danish Minister of State</a> (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Ledreborg\" class=\"mw-redirect\" title=\"<PERSON>Ledre<PERSON>\"><PERSON>Ledreborg</a>, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Denmark\" class=\"mw-redirect\" title=\"List of Prime Ministers of Denmark\">Danish Minister of State</a> (d. 1763)", "links": [{"title": "<PERSON>Ledreborg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Ledreborg"}, {"title": "List of Prime Ministers of Denmark", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Denmark"}]}, {"year": "1705", "text": "<PERSON><PERSON><PERSON><PERSON>, German painter (d. 1788)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/Matth%C3%A4us_G%C3%BCnther\" title=\"<PERSON><PERSON><PERSON><PERSON> Günther\"><PERSON><PERSON><PERSON><PERSON></a>, German painter (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matth%C3%A4us_G%C3%BCnther\" title=\"<PERSON><PERSON><PERSON><PERSON> Günther\"><PERSON><PERSON><PERSON><PERSON></a>, German painter (d. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matth%C3%A4us_G%C3%BCnther"}]}, {"year": "1707", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, French mathematician, cosmologist, and author (d. 1788)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> de B<PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French mathematician, cosmologist, and author (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> de B<PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French mathematician, cosmologist, and author (d. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON><PERSON><PERSON>, French chess player and composer (d. 1795)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Andr%C3%A9_Danican_Philidor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chess player and composer (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Andr%C3%A9_Danican_Philidor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chess player and composer (d. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois-Andr%C3%A9_Danican_Philidor"}]}, {"year": "1740", "text": "<PERSON>, Swedish sculptor and illustrator (d. 1814)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish sculptor and illustrator (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish sculptor and illustrator (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, German horn player and composer (d. 1844)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lzel\" title=\"<PERSON>\"><PERSON></a>, German horn player and composer (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lzel\" title=\"<PERSON>\"><PERSON></a>, German horn player and composer (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_St%C3%B6lzel"}]}, {"year": "1791", "text": "<PERSON>, Italian poet and author (d. 1863)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1795", "text": "<PERSON>, English physician and author (d. 1821)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON><PERSON>, South African preacher and activist (d. 1871)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African preacher and activist (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African preacher and activist (d. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, English Baptist minister and Jamaican missionary (d. 1845)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Baptist minister and Jamaican missionary (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Baptist minister and Jamaican missionary (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, English lawyer and politician, 1st Prime Minister of New Zealand (d. 1879)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1810", "text": "<PERSON>, Prussian economist and academic (d. 1858)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian economist and academic (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian economist and academic (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, Polish activist and translator (d. 1839)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish activist and translator (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish activist and translator (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Scottish explorer and surveyor (d. 1866)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish explorer and surveyor (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish explorer and surveyor (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American businessman and politician, 31st Governor of Massachusetts (d. 1886)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Massachusetts_politician)\" title=\"<PERSON> (Massachusetts politician)\"><PERSON></a>, American businessman and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Massachusetts_politician)\" title=\"<PERSON> (Massachusetts politician)\"><PERSON></a>, American businessman and politician, 31st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1886)", "links": [{"title": "<PERSON> (Massachusetts politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Massachusetts_politician)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1819", "text": "<PERSON>, American lawyer and politician, 21st Vice President of the United States (d. 1885)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1829", "text": "<PERSON>, German chemist and academic (d. 1896)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/August_Kekul%C3%A9\" title=\"August <PERSON>\">August <PERSON></a>, German chemist and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Kekul%C3%A9\" title=\"August Ke<PERSON>\">August <PERSON></a>, German chemist and academic (d. 1896)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_Kekul%C3%A9"}]}, {"year": "1831", "text": "<PERSON>, French sculptor and painter (d. 1900)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French sculptor and painter (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alexandre_Falgui%C3%A8re"}]}, {"year": "1836", "text": "<PERSON>-Bannerman, Scottish merchant and politician, Prime Minister of the United Kingdom (d. 1908)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish merchant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish merchant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1908)", "links": [{"title": "<PERSON>-Bannerman", "link": "https://wikipedia.org/wiki/<PERSON>man"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1836", "text": "<PERSON>, German physicist and academic (d. 1912)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German physicist and academic (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German physicist and academic (d. 1912)", "links": [{"title": "August <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Polish-English chess player, linguist, and journalist (d. 1888)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English chess player, linguist, and journalist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English chess player, linguist, and journalist (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, American zoologist and academic (d. 1950)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and academic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist and academic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>-<PERSON>, English photographer, director, and cinematographer (d. 1921)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer, director, and cinematographer (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer, director, and cinematographer (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American painter (d. 1961)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Grandma_<PERSON>\" title=\"Grandma <PERSON>\"><PERSON></a>, American painter (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grandma_<PERSON>\" title=\"Grandma <PERSON>\"><PERSON></a>, American painter (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Grandma_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American-English financier and philanthropist (d. 1932)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English financier and philanthropist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English financier and philanthropist (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, French author and playwright (d. 1947)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, German-Swiss actor (d. 1952)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss actor (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss actor (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON> <PERSON><PERSON> Jr., American banker and philanthropist (d. 1943)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Jr<PERSON>\"><PERSON><PERSON> <PERSON><PERSON> Jr.</a>, American banker and philanthropist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON><PERSON> Morgan Jr.\"><PERSON><PERSON> <PERSON><PERSON> Jr.</a>, American banker and philanthropist (d. 1943)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1869", "text": "<PERSON>, South African general (d. 1917)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African general (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African general (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Russian pilot, explorer, and author (d. 1938)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, explorer, and author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, explorer, and author (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, English cricketer and coach (d. 1954)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Australian politician who served as an independent member of the Legislative Assembly of Western Australia (d. 1963)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician who served as an <a href=\"https://wikipedia.org/wiki/Independent_politician\" title=\"Independent politician\">independent</a> member of the <a href=\"https://wikipedia.org/wiki/Western_Australian_Legislative_Assembly\" title=\"Western Australian Legislative Assembly\">Legislative Assembly</a> of <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician who served as an <a href=\"https://wikipedia.org/wiki/Independent_politician\" title=\"Independent politician\">independent</a> member of the <a href=\"https://wikipedia.org/wiki/Western_Australian_Legislative_Assembly\" title=\"Western Australian Legislative Assembly\">Legislative Assembly</a> of <a href=\"https://wikipedia.org/wiki/Western_Australia\" title=\"Western Australia\">Western Australia</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Independent politician", "link": "https://wikipedia.org/wiki/Independent_politician"}, {"title": "Western Australian Legislative Assembly", "link": "https://wikipedia.org/wiki/Western_Australian_Legislative_Assembly"}, {"title": "Western Australia", "link": "https://wikipedia.org/wiki/Western_Australia"}]}, {"year": "1875", "text": "<PERSON>, American businessman and financier, co-founded E. F. Hutton & Co. (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, co-founded <a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co.\" class=\"mw-redirect\" title=\"E. F. Hutton &amp; Co.\">E. F. Hutton &amp; Co.</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co.\" class=\"mw-redirect\" title=\"E. F. Hutton &amp; Co.\">E. F. Hutton &amp; Co.</a> (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "E. F. Hutton & Co.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_%26_Co."}]}, {"year": "1876", "text": "<PERSON>, Maltese politician, 2nd Prime Minister of Malta (d. 1934)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1876", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian poet and author (d. 1938)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian poet and author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian poet and author (d. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek composer and conductor (d. 1950)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek composer and conductor (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek composer and conductor (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, American author and poet (d. 1928)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Eli<PERSON>_Wylie\" title=\"<PERSON><PERSON> Wylie\"><PERSON><PERSON></a>, American author and poet (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wylie\" title=\"<PERSON><PERSON> Wylie\"><PERSON><PERSON></a>, American author and poet (d. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wylie"}]}, {"year": "1887", "text": "<PERSON>, English poet and critic (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Australian soldier and politician, 27th Australian Minister for Defence (d. 1974)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1892", "text": "<PERSON>, Canadian priest, pianist, and composer (d. 1958)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Oscar_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, Canadian priest, pianist, and composer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, Canadian priest, pianist, and composer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oscar_O%27Brien"}]}, {"year": "1893", "text": "<PERSON>, English politician, Secretary of State for War (d. 1957)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}]}, {"year": "1894", "text": "<PERSON>, Australian cricketer, footballer, and sportscaster (d. 1969)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and sportscaster (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and sportscaster (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actor, director, and producer (d. 1984)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French author and poet (d. 1919)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ach%C3%A9"}]}, {"year": "1897", "text": "<PERSON>, Tin Pan Alley era songwriter (d. 1973)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tin Pan Alley era songwriter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tin Pan Alley era songwriter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sherman"}]}, {"year": "1900", "text": "<PERSON>, English-American author (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Italian-American assassin of <PERSON> (d. 1933)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American missionary and author (d. 1993)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American poet and author (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. B. Colby\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. B<PERSON> Colby\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 1977)", "links": [{"title": "C. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Turkish composer and musicologist (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish composer and musicologist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish composer and musicologist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American football player and coach (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American surgeon and educator (d. 2008)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ake<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and educator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American trumpet player and bandleader (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and bandleader (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and bandleader (d. 1994)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Greek-American actor, director, producer, and screenwriter (d. 2003)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Elia_Kazan\" title=\"Elia Kazan\"><PERSON><PERSON></a>, Greek-American actor, director, producer, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elia_Kazan\" title=\"Elia Kazan\"><PERSON><PERSON></a>, Greek-American actor, director, producer, and screenwriter (d. 2003)", "links": [{"title": "Elia Kazan", "link": "https://wikipedia.org/wiki/Elia_Kazan"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Bulgarian police officer and politician, Head of State of Bulgaria (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian police officer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">Head of State of Bulgaria</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian police officer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria\" title=\"List of heads of state of Bulgaria\">Head of State of Bulgaria</a> (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Bulgaria"}]}, {"year": "1912", "text": "<PERSON>, American engineer and businessman, co-founded Hewlett-Packard (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"He<PERSON>ett-Packard\"><PERSON><PERSON><PERSON>-<PERSON></a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Packard\" title=\"He<PERSON>ett-Packard\">Hewlett-Packard</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hewlett-Packard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ett-<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Baron <PERSON> of Amisfield, English soldier and courtier (d. 1999)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Amisfield\" title=\"<PERSON>, Baron <PERSON> of Amisfield\"><PERSON>, Baron <PERSON> of Amisfield</a>, English soldier and courtier (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Amisfield\" title=\"<PERSON>, Baron <PERSON> of Amisfield\"><PERSON>, Baron <PERSON> of Amisfield</a>, English soldier and courtier (d. 1999)", "links": [{"title": "<PERSON>, Baron <PERSON> of Amisfield", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Amisfield"}]}, {"year": "1913", "text": "<PERSON>, English actor (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Czech-Austrian actress (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/L%C3%ADda_Baarov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Austrian actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%ADda_Baarov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Austrian actress (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%ADda_Baarov%C3%A1"}]}, {"year": "1914", "text": "<PERSON>, Australian pianist and composer (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American physicist and philosopher (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and philosopher (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and philosopher (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Argentine bishop (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine bishop (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine bishop (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Japanese mathematician and academic (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_It%C3%B4\" title=\"<PERSON><PERSON><PERSON> Itô\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_It%C3%B4\" title=\"<PERSON><PERSON><PERSON>ô\"><PERSON><PERSON><PERSON></a>, Japanese mathematician and academic (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kiyosi_It%C3%B4"}]}, {"year": "1917", "text": "<PERSON>, English captain, pilot, and humanitarian (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Leonard_<PERSON>\" title=\"Leonard <PERSON>\"><PERSON></a>, English captain, pilot, and humanitarian (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leonard_<PERSON>\" title=\"Leonard <PERSON>\"><PERSON></a>, English captain, pilot, and humanitarian (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian-English chemist and academic, Nobel Prize laureate (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1917", "text": "<PERSON>, American painter and educator (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American microbiologist and academic (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist and coach (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and coach (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and coach (d. 2004)", "links": [{"title": "Briek Schotte", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>k_Schotte"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Welsh journalist and poet (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh journalist and poet (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh journalist and poet (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American soldier and politician (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, French-Canadian journalist and radio host (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian journalist and radio host (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian journalist and radio host (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian author and poet (d. 1993)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English-American actor (d. 1984)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American golfer, co-founded LPGA (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, co-founded <a href=\"https://wikipedia.org/wiki/LPGA\" title=\"LPGA\">LPGA</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, co-founded <a href=\"https://wikipedia.org/wiki/LPGA\" title=\"LPGA\">LPGA</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "LPGA", "link": "https://wikipedia.org/wiki/LPGA"}]}, {"year": "1924", "text": "<PERSON>, American captain and politician, Medal of Honor recipient (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1924", "text": "<PERSON>, American composer and conductor (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Welsh-English fashion designer, founded Laura Ashley plc (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_plc\" class=\"mw-redirect\" title=\"Laura Ashley plc\">Laura Ashley plc</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_plc\" class=\"mw-redirect\" title=\"Laura Ashley plc\">Laura Ashley plc</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Laura Ashley plc", "link": "https://wikipedia.org/wiki/Laura_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian lawyer and politician, 10th Premier of Saskatchewan (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Saskatchewan\" title=\"Premier of Saskatchewan\">Premier of Saskatchewan</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Saskatchewan", "link": "https://wikipedia.org/wiki/Premier_of_Saskatchewan"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress, singer, director, and producer (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, singer, director, and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, singer, director, and producer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American director and producer (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American director and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American director and producer (d. 2015)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician, 32nd Mayor of Norwalk (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Norwalk,_Connecticut\" title=\"List of mayors of Norwalk, Connecticut\">Mayor of Norwalk</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Norwalk,_Connecticut\" title=\"List of mayors of Norwalk, Connecticut\">Mayor of Norwalk</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of mayors of Norwalk, Connecticut", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Norwalk,_Connecticut"}]}, {"year": "1926", "text": "<PERSON>, Baron <PERSON> of Roding, English lawyer and politician, Secretary of State for the Environment (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Roding\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Roding\"><PERSON>, Baron <PERSON> of Roding</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment\" title=\"Secretary of State for the Environment\">Secretary of State for the Environment</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Roding\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Roding\"><PERSON>, Baron <PERSON> of Roding</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment\" title=\"Secretary of State for the Environment\">Secretary of State for the Environment</a> (d. 2016)", "links": [{"title": "<PERSON>, Baron <PERSON> of Roding", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Rod<PERSON>"}, {"title": "Secretary of State for the Environment", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment"}]}, {"year": "1926", "text": "<PERSON>, German footballer (d. 1983)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American voice actor (d. 1997)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American paranormal investigator and author (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American paranormal investigator and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American paranormal investigator and author (d. 2006)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English-American author and illustrator (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and illustrator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and jurist", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Heureux-Dub%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Heureux-Dub%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Claire_L%27Heureux-Dub%C3%A9"}]}, {"year": "1928", "text": "<PERSON>, Australian ballerina (d. 1983)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ballerina (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ballerina (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player, coach, and commentator (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and commentator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and commentator (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American basketball player (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON> of Belgium (d. 1993)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON> of Belgium</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON> of Belgium</a> (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Baudouin_of_Belgium"}]}, {"year": "1930", "text": "<PERSON>, American saxophonist and composer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Sri Lankan journalist and author (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/S._Si<PERSON>gam\" title=\"S. Sivana<PERSON>gam\"><PERSON><PERSON></a>, Sri Lankan journalist and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Sivana<PERSON>gam\" title=\"S. <PERSON>\"><PERSON><PERSON></a>, Sri Lankan journalist and author (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON>yagam"}]}, {"year": "1930", "text": "<PERSON>, Academician of the Chinese Academy of Engineering and an expert in hybrid rice (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Longping\"><PERSON></a>, Academician of the <a href=\"https://wikipedia.org/wiki/Chinese_Academy_of_Engineering\" title=\"Chinese Academy of Engineering\">Chinese Academy of Engineering</a> and an expert in <a href=\"https://wikipedia.org/wiki/Hybrid_rice\" title=\"Hybrid rice\">hybrid rice</a> (d. <a href=\"https://wikipedia.org/wiki/2021\" title=\"2021\">2021</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Longping\"><PERSON></a>, Academician of the <a href=\"https://wikipedia.org/wiki/Chinese_Academy_of_Engineering\" title=\"Chinese Academy of Engineering\">Chinese Academy of Engineering</a> and an expert in <a href=\"https://wikipedia.org/wiki/Hybrid_rice\" title=\"Hybrid rice\">hybrid rice</a> (d. <a href=\"https://wikipedia.org/wiki/2021\" title=\"2021\">2021</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Longping"}, {"title": "Chinese Academy of Engineering", "link": "https://wikipedia.org/wiki/Chinese_Academy_of_Engineering"}, {"title": "Hybrid rice", "link": "https://wikipedia.org/wiki/Hybrid_rice"}, {"title": "2021", "link": "https://wikipedia.org/wiki/2021"}]}, {"year": "1931", "text": "<PERSON>, Maltese composer and conductor (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese composer and conductor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese composer and conductor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English author and academic (d. 2000)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American-English philanthropist and book collector (d. 2003)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American-English philanthropist and book collector (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American-English philanthropist and book collector (d. 2003)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1934", "text": "<PERSON>, German painter and illustrator (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Argentine composer and conductor (d. 1977)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Waldo_de_los_R%C3%ADos\" title=\"Waldo de los Ríos\"><PERSON><PERSON> los Ríos</a>, Argentine composer and conductor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waldo_de_los_R%C3%ADos\" title=\"Waldo de los Ríos\"><PERSON><PERSON> de los Ríos</a>, Argentine composer and conductor (d. 1977)", "links": [{"title": "<PERSON><PERSON> de los Ríos", "link": "https://wikipedia.org/wiki/Waldo_de_los_R%C3%ADos"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian author and poet (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and poet (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and poet (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1934", "text": "<PERSON>, Lebanese lawyer and politician, 58th Prime Minister of Lebanon (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 58th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}, {"year": "1934", "text": "<PERSON>, American singer and guitarist (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Milton\" title=\"Little Milton\"><PERSON></a>, American singer and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Little_Milton\" title=\"Little Milton\"><PERSON></a>, American singer and guitarist (d. 2005)", "links": [{"title": "Little Milton", "link": "https://wikipedia.org/wiki/<PERSON>_Milton"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player and coach (d. 2025)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sul<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sulin\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player and coach (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sul<PERSON>\" title=\"Hilpas Sulin\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player and coach (d. 2025)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sulin"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Senegalese lawyer and politician, 2nd President of Senegal", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Senegal", "link": "https://wikipedia.org/wiki/President_of_Senegal"}]}, {"year": "1935", "text": "<PERSON>, American basketball player and dentist (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American basketball player and dentist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American basketball player and dentist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal"}]}, {"year": "1936", "text": "<PERSON>, English race car driver and engineer, founded Brian Hart Ltd. (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"Brian Hart Ltd.\">Brian Hart Ltd.</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/Brian_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"Brian Hart Ltd.\">Brian Hart Ltd.</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Brian Hart Ltd.", "link": "https://wikipedia.org/wiki/Brian_Hart_Ltd."}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1959)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holly\"><PERSON></a>, American singer-songwriter and guitarist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holly\"><PERSON></a>, American singer-songwriter and guitarist (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Justice", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights (Greece)\">Greek Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights (Greece)\">Greek Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apost<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Ministry of Justice, Transparency and Human Rights (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights_(Greece)"}]}, {"year": "1937", "text": "<PERSON>, American actor (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Russian politician, Premier of the Russian SFSR (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Oleg_Lobov\" title=\"Oleg Lobov\"><PERSON><PERSON></a>, Russian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Premier of the Russian SFSR</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g_Lobov\" title=\"Oleg Lobov\"><PERSON><PERSON></a>, Russian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia\" title=\"List of heads of government of Russia\">Premier of the Russian SFSR</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oleg_Lobov"}, {"title": "List of heads of government of Russia", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Russia"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1939", "text": "<PERSON>, Welsh actor, director, and playwright", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, Welsh actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, Welsh actor, director, and playwright", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>(playwright)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Italian director, producer, and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dario_Argento"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Indonesian journalist and politician, 4th President of Indonesia (d. 2009)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1942", "text": "<PERSON>, Scottish footballer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Best\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Best\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English footballer and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON> of Blackheath, English businessman and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Blackheath\" title=\"<PERSON>, Baron <PERSON> of Blackheath\"><PERSON>, Baron <PERSON> of Blackheath</a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Blackheath\" title=\"<PERSON>, Baron Stone of Blackheath\"><PERSON>, Baron <PERSON> of Blackheath</a>, English businessman and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Blackheath", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Blackheath"}]}, {"year": "1942", "text": "<PERSON>, American sociologist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and jurist, 17th Chief Justice of Canada", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Bever<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and jurist, 17th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bever<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> M<PERSON>L<PERSON>lin\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and jurist, 17th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a>", "links": [{"title": "Be<PERSON><PERSON> McLachlin", "link": "https://wikipedia.org/wiki/Bever<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1944", "text": "<PERSON>, American football player (d. 2011)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blue\"><PERSON></a>, American football player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blue\"><PERSON></a>, American football player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Danish lawyer and politician, Education Minister of Denmark", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Education_Minister_of_Denmark\" class=\"mw-redirect\" title=\"Education Minister of Denmark\">Education Minister of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Education_Minister_of_Denmark\" class=\"mw-redirect\" title=\"Education Minister of Denmark\">Education Minister of Denmark</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Education Minister of Denmark", "link": "https://wikipedia.org/wiki/Education_Minister_of_Denmark"}]}, {"year": "1944", "text": "<PERSON>, English rugby player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player and coach (d. 1998)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Serbian footballer and manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bora_Milutinovi%C4%87"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Iranian author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ushang <PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Houshang <PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1945", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English-New Zealand rugby player and footballer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ard\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand rugby player and footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ard\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand rugby player and footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Pollard"}]}, {"year": "1945", "text": "<PERSON>, American musicologist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musicologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musicologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English footballer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American baseball player (d. 2004)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American sportscaster", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aldman\" title=\"<PERSON><PERSON><PERSON> Waldman\"><PERSON><PERSON><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Waldman\"><PERSON><PERSON><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>man"}]}, {"year": "1947", "text": "<PERSON>, Israeli demographer and statistician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli demographer and statistician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli demographer and statistician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German-English politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English historian and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, German-Australian engineer (d. 1991)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fraudster)\" title=\"<PERSON> (fraudster)\"><PERSON></a>, German-Australian engineer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fraudster)\" title=\"<PERSON> (fraudster)\"><PERSON></a>, German-Australian engineer (d. 1991)", "links": [{"title": "<PERSON> (fraudster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(fraudster)"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author, journalist, speechwriter, and pundit", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, speechwriter, and pundit", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, speechwriter, and pundit", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Brazilian singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Albert\"><PERSON></a>, Brazilian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Morris Albert\"><PERSON></a>, Brazilian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American trumpet player and composer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Mammootty\" title=\"Mammootty\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mammootty\" title=\"Mammootty\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mammootty"}]}, {"year": "1952", "text": "<PERSON>, Spanish motorcycle racer (d. 1998)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, New Zealand-Australian singer-songwriter (d. 1998)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American keyboardist and songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Benmont_Tench\" title=\"Benmont Tench\"><PERSON><PERSON></a>, American keyboardist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benmont_Tench\" title=\"Benmont Tench\"><PERSON><PERSON></a>, American keyboardist and songwriter", "links": [{"title": "Benmont Tench", "link": "https://wikipedia.org/wiki/Benmont_Tench"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American software architect and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American software architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American software architect and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Croatian-American actress (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer and pianist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Welsh footballer (d. 2007)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and dancer (d. 1997)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and dancer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and dancer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American bass player (d. 2023)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Turkish Cypriot politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish Cypriot politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish Cypriot politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>in_Tatar"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American saxophonist and songwriter (d. 2008)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and songwriter (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, French pianist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American novelist and short story writer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American wrestler", "html": "1962 - <a href=\"https://wikipedia.org/wiki/George_South\" title=\"George South\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_South\" title=\"George South\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_South"}]}, {"year": "1962", "text": "<PERSON>, Turkish footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American rapper and producer (d. 1995)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American rapper and producer (d. 1995)", "links": [{"title": "Eazy-E", "link": "https://wikipedia.org/wiki/Eazy-E"}]}, {"year": "1965", "text": "<PERSON>, Romanian soprano", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Macedonian footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Darko_Pan%C4%8Dev\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Darko_Pan%C4%8Dev\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Darko_Pan%C4%8Dev"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German runner", "html": "1965 - <a href=\"https://wikipedia.org/wiki/U<PERSON>_<PERSON>\" title=\"U<PERSON> Pippig\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON>_<PERSON>\" title=\"U<PERSON> Pippig\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uta_<PERSON>ig"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Skuhrav%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Skuhrav%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Skuhrav%C3%BD"}]}, {"year": "1965", "text": "<PERSON>, German footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian race walker", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/<PERSON>_(racewalker)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, German politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mann"}]}, {"year": "1966", "text": "<PERSON>, English actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, German speed skater", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, German speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German speed skater", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian sportscaster and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sportscaster and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sportscaster and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American comedian and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actress", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Indian-English accountant and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English accountant and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Ghanaian-French footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Russian figure skater and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian figure skater and coach", "links": [{"title": "Gennadi <PERSON>", "link": "https://wikipedia.org/wiki/Gennadi_Krasnitski"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress and model", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American figure skater", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Canadian ice hockey player (d. 2023)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Od<PERSON>ck\" title=\"<PERSON><PERSON> Od<PERSON>ck\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Od<PERSON>ck\" title=\"<PERSON><PERSON> Od<PERSON>ck\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American boxer and trainer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American model and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swiss-Liechtensteiner footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss-Liechtensteiner footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Swiss-Liechtensteiner footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese motorcycle racer (d. 2007)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Abe\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Costa Rican footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Jamaican cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>ll_Hinds\" title=\"<PERSON>ll Hinds\"><PERSON><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ll_Hinds\" title=\"Wavell Hinds\"><PERSON><PERSON>nds</a>, Jamaican cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hinds"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ves\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>leaves"}]}, {"year": "1977", "text": "<PERSON>, American wrestler and trainer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English-Irish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ersin_G%C3%BCreler\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ersin_G%C3%BCreler\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ersin_G%C3%BCreler"}]}, {"year": "1978", "text": "<PERSON>, Canadian actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Devon_Sawa\" title=\"Devon Sawa\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devon_Sawa\" title=\"Devon Sawa\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Devon_Sawa"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian singer-songwriter and keyboard player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Emre_Bel%C3%B6zo%C4%9Flu\" title=\"<PERSON>re Belözoğlu\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emre_Bel%C3%B6zo%C4%9Flu\" title=\"<PERSON>re Belözoğlu\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emre_Bel%C3%B6zo%C4%9Flu"}]}, {"year": "1980", "text": "<PERSON>, Australian cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentine footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Iranian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Javad_<PERSON>nam\" title=\"Javad <PERSON>\"><PERSON><PERSON></a>, Iranian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javad_<PERSON>\" title=\"Javad <PERSON>ek<PERSON>\"><PERSON><PERSON></a>, Iranian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Javad_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. D. Pa<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prior\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>kha<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6kha<PERSON>_<PERSON>\" title=\"<PERSON>ökha<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6kha<PERSON>_<PERSON>an"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Mexican wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(wrestler)"}]}, {"year": "1982", "text": "<PERSON>, American boxer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1982)\" title=\"<PERSON> (cricketer, born 1982)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1982)\" title=\"<PERSON> (cricketer, born 1982)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Hungarian fencer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Emese_Sz%C3%A1sz\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emese_Sz%C3%A1sz\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian fencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emese_Sz%C3%A1sz"}]}, {"year": "1983", "text": "<PERSON>, Irish cyclist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German figure skater", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English-American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Pops_Mensah-<PERSON>su\" title=\"Pops Mensah-Bonsu\"><PERSON>s Mensah-<PERSON></a>, English-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pops_<PERSON><PERSON>h-<PERSON>su\" title=\"Pops Mensah-Bonsu\"><PERSON><PERSON> Men<PERSON><PERSON>-<PERSON></a>, English-American basketball player", "links": [{"title": "Pops Men<PERSON>h-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pops_<PERSON><PERSON>h-<PERSON>su"}]}, {"year": "1983", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>epu\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>epu\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1984", "text": "<PERSON>, Canadian actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Brazilian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1984)"}]}, {"year": "1984", "text": "<PERSON>, Russian tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Radhi<PERSON>_Apte\" title=\"Radhika Apte\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radhi<PERSON>_Apte\" title=\"Radhika Apte\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON> A<PERSON>e", "link": "https://wikipedia.org/wiki/Radhi<PERSON>_Apte"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(footballer,_born_1985)"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON>, American wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Danny North\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Danny_<PERSON>\" title=\"Danny North\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (cornerback)", "link": "https://wikipedia.org/wiki/<PERSON>_(cornerback)"}]}, {"year": "1987", "text": "<PERSON>, American actress and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Canadian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian skier", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)\" title=\"<PERSON> (skier)\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON> (skier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(skier)"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jonathan Majors\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jonathan Majors\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Slovakian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Libor_Hud%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovakian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libor_Hud%C3%A1%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovakian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Libor_Hud%C3%A1%C4%8Dek"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Russian figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Serbian skier (d. 2010)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian skier (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian skier (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Amar_Garibovi%C4%87"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Welsh track cyclist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh track cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ese\" title=\"<PERSON>es<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ese\" title=\"<PERSON>es<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ese"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "Dean-<PERSON>, English actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian cyclist", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}], "Deaths": [{"year": "251", "text": "<PERSON><PERSON>, Chinese general and politician (b. 179)", "html": "251 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general and politician (b. 179)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general and politician (b. 179)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "355", "text": "<PERSON><PERSON><PERSON>, Roman general", "html": "355 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "859", "text": "Emperor <PERSON><PERSON><PERSON> of Tang, Chinese emperor (b. 810)", "html": "859 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>%C4%81<PERSON><PERSON>_of_Tang\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a>, Chinese emperor (b. 810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>%C4%81nzong_of_Tang\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON><PERSON> of Tang</a>, Chinese emperor (b. 810)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>%C4%81nzong_of_Tang"}]}, {"year": "934", "text": "<PERSON><PERSON>, Chinese general (b. 874)", "html": "934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general (b. 874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general (b. 874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng"}]}, {"year": "1134", "text": "<PERSON> the Battler, Spanish emperor (b. 1073)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Battler\" title=\"<PERSON> the Battler\"><PERSON> the Battler</a>, Spanish emperor (b. 1073)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Battler\" title=\"<PERSON> the Battler\"><PERSON> the Battler</a>, Spanish emperor (b. 1073)", "links": [{"title": "<PERSON> the Battler", "link": "https://wikipedia.org/wiki/<PERSON>_the_Battler"}]}, {"year": "1151", "text": "<PERSON>, Count of Anjou (b. 1113)", "html": "1151 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a> (b. 1113)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a> (b. 1113)", "links": [{"title": "<PERSON>, Count of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_Plantagenet,_Count_of_Anjou"}]}, {"year": "1202", "text": "<PERSON> of the White Hands, French cardinal (b. 1135)", "html": "1202 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_White_Hands\" title=\"<PERSON> of the White Hands\"><PERSON> of the White Hands</a>, French cardinal (b. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_White_Hands\" title=\"<PERSON> of the White Hands\"><PERSON> of the White Hands</a>, French cardinal (b. 1135)", "links": [{"title": "<PERSON> of the White Hands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_White_Hands"}]}, {"year": "1251", "text": "<PERSON>, Duchess of Opole", "html": "1251 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Opole\" title=\"<PERSON>, Duchess of Opole\"><PERSON>, Duchess of Opole</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Opole\" title=\"<PERSON>, Duchess of Opole\"><PERSON>, Duchess of Opole</a>", "links": [{"title": "<PERSON>, Duchess of Opole", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_<PERSON>_Opole"}]}, {"year": "1303", "text": "<PERSON>, archbishop of Esztergom", "html": "1303 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Esztergom", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, archbishop of Esztergom", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gregory_<PERSON>"}]}, {"year": "1312", "text": "<PERSON> of Castile (b. 1285)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a> (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a> (b. 1285)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_Castile"}]}, {"year": "1354", "text": "<PERSON>, doge of Venice (b. 1306)", "html": "1354 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 1306)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of Venice (b. 1306)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1362", "text": "<PERSON> of the Tower (b. 1321)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/Joan_<PERSON>_the_Tower\" title=\"Joan of the Tower\">Joan of the Tower</a> (b. 1321)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Tower\" title=\"Joan of the Tower\">Joan of the Tower</a> (b. 1321)", "links": [{"title": "Joan of the Tower", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Tower"}]}, {"year": "1464", "text": "<PERSON>, Elector of Saxony (b. 1412)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1412)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1496", "text": "<PERSON> of Naples (b. 1469)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (b. 1469)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Naples"}]}, {"year": "1559", "text": "<PERSON>, English-French printer and scholar (b. 1503)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French printer and scholar (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French printer and scholar (b. 1503)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Croatian general (b. 1506)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/Nikola_%C5%A0ubi%C4%87_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian general (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikola_%C5%A0ubi%C4%87_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian general (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_%C5%A0ubi%C4%87_<PERSON><PERSON><PERSON>"}]}, {"year": "1573", "text": "<PERSON> of Austria, Princess of Portugal (b. 1535)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Princess_of_Portugal\" title=\"<PERSON> of Austria, Princess of Portugal\"><PERSON> of Austria, Princess of Portugal</a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joanna_of_Austria,_Princess_of_Portugal\" title=\"<PERSON> of Austria, Princess of Portugal\"><PERSON> of Austria, Princess of Portugal</a> (b. 1535)", "links": [{"title": "<PERSON> of Austria, Princess of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_of_Austria,_Princess_of_Portugal"}]}, {"year": "1601", "text": "<PERSON>, father of <PERSON> (b. 1529)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shakespeare\"><PERSON></a>, father of <a href=\"https://wikipedia.org/wiki/<PERSON>_Shakespeare\" title=\"<PERSON> Shakespeare\"><PERSON></a> (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shakespeare\"><PERSON></a>, father of <a href=\"https://wikipedia.org/wiki/<PERSON>_Shakespeare\" title=\"William Shakespeare\"><PERSON></a> (b. 1529)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON>, Polish priest and saint (b. 1582)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1582)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON><PERSON>, Croatian priest, missionary, and saint (b. 1589)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian priest, missionary, and saint (b. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian priest, missionary, and saint (b. 1589)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, French lawyer and jurist (b. 1549)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1549)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, English noble and politician (b. c. 1585)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Master_of_the_Mint)\" title=\"<PERSON> (Master of the Mint)\"><PERSON></a>, English noble and politician (b. c. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Master_of_the_Mint)\" title=\"<PERSON> (Master of the Mint)\"><PERSON></a>, English noble and politician (b. c. 1585)", "links": [{"title": "<PERSON> (Master of the Mint)", "link": "https://wikipedia.org/wiki/<PERSON>_(Master_of_the_Mint)"}]}, {"year": "1644", "text": "<PERSON>, Italian cardinal and historian (b. 1579)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and historian (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and historian (b. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1655", "text": "<PERSON>, French author and playwright (b. 1601)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Tristan_l%27Hermite\" title=\"<PERSON>Hermite\"><PERSON></a>, French author and playwright (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Tristan_l%27Hermite\" title=\"<PERSON>Hermite\"><PERSON></a>, French author and playwright (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Tristan_l%27Hermite"}]}, {"year": "1657", "text": "<PERSON><PERSON><PERSON>, Swedish field marshal (b. 1606)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish field marshal (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish field marshal (b. 1606)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, English-American settler, co-founded Rhode Island and Providence Plantations (b. 1605)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_colonist)\" title=\"<PERSON> (Rhode Island colonist)\"><PERSON></a>, English-American settler, co-founded <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island and Providence Plantations</a> (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_colonist)\" title=\"<PERSON> (Rhode Island colonist)\"><PERSON></a>, English-American settler, co-founded <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island and Providence Plantations</a> (b. 1605)", "links": [{"title": "<PERSON> (Rhode Island colonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_colonist)"}, {"title": "Rhode Island", "link": "https://wikipedia.org/wiki/Rhode_Island"}]}, {"year": "1729", "text": "<PERSON>, Dutch-American civil servant and politician, 21st Governor of the province of New York (b. 1688)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, Dutch-American civil servant and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Governor of the province of New York</a> (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, Dutch-American civil servant and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York\" title=\"List of colonial governors of New York\">Governor of the province of New York</a> (b. 1688)", "links": [{"title": "<PERSON> (colonial administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)"}, {"title": "List of colonial governors of New York", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_New_York"}]}, {"year": "1741", "text": "<PERSON><PERSON>, Spanish admiral (b. 1689)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Spanish admiral (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Spanish admiral (b. 1689)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Danish-Norwegian historian and author (b. 1728)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian historian and author (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Norwegian historian and author (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, French botanist and physicist (b. 1717)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French botanist and physicist (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French botanist and physicist (b. 1717)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, Thai king (b. 1737)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Buddha_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Buddha Yo<PERSON><PERSON>\">Buddha <PERSON><PERSON><PERSON></a>, Thai king (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddha_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Buddha Yo<PERSON><PERSON>\">Buddha <PERSON><PERSON><PERSON></a>, Thai king (b. 1737)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Chulaloke"}]}, {"year": "1833", "text": "<PERSON>, English poet, playwright, and philanthropist (b. 1745)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and philanthropist (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> More\"><PERSON></a>, English poet, playwright, and philanthropist (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, French general (b. 1765)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 13th <PERSON><PERSON><PERSON><PERSON> (b. 1826)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 13th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 13th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>zan_Tanigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Ottoman politician, 217th Grand Vizier of the Ottoman Empire (b. 1815)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_%C3%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, 217th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_%C3%82<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman politician, 217th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emin_%C3%82li_Pasha"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1881", "text": "<PERSON>, American poet and academic (b. 1842)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American lawyer and judge (b. 1820)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American poet and activist (b. 1807)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>hittier"}]}, {"year": "1893", "text": "<PERSON>, American lawyer and politician, 26th United States Secretary of State (b. 1808)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fish\" title=\"Hamilton Fish\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamilton_Fish\" title=\"Hamilton Fish\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1808)", "links": [{"title": "Hamilton Fish", "link": "https://wikipedia.org/wiki/<PERSON>_Fish"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Romanian philologist, journalist, and playwright (b. 1838)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian philologist, journalist, and playwright (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian philologist, journalist, and playwright (b. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English painter and soldier (b. 1827)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and soldier (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and soldier (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 12th Premier of Quebec (b. 1855)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9on_Parent\" title=\"Simon-Na<PERSON>éon Parent\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9on_Parent\" title=\"Simon<PERSON>Na<PERSON>on Parent\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simon-Napol%C3%A9on_Parent"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1921", "text": "<PERSON>, English author and painter (b. 1856)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and painter (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and painter (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English lawyer, author, and songwriter (b. 1848)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, author, and songwriter (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, author, and songwriter (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, 1st Viscount <PERSON> of Fallodon, English ornithologist and politician, Secretary of State for Foreign and Commonwealth Affairs (b. 1862)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Fallodon\" title=\"<PERSON>, 1st Viscount <PERSON> of Fallodon\"><PERSON>, 1st Viscount <PERSON> of Fallodon</a>, English ornithologist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Fallodon\" title=\"<PERSON>, 1st Viscount <PERSON> of Fallodon\"><PERSON>, 1st Viscount <PERSON> of Fallodon</a>, English ornithologist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1862)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Fallodon", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_Grey_of_Fallodon"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Japanese author, poet, and playwright (b. 1873)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, and playwright (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, poet, and playwright (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ky%C5%8Dka_<PERSON><PERSON>mi"}]}, {"year": "1940", "text": "<PERSON>, Paraguayan soldier and politician, President of Paraguay (b. 1888)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Estigarribia\" title=\"<PERSON>\"><PERSON></a>, Paraguayan soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Estigarribia\" title=\"<PERSON>\"><PERSON></a>, Paraguayan soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_F%C3%A9lix_Estigarribia"}, {"title": "President of Paraguay", "link": "https://wikipedia.org/wiki/President_of_Paraguay"}]}, {"year": "1941", "text": "<PERSON>, Cuban lawyer and politician, President of Cuba (b. 1866)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Menocal\" title=\"<PERSON>\"><PERSON></a>, Cuban lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Menocal\" title=\"<PERSON>\"><PERSON></a>, Cuban lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_Garc%C3%ADa_Menocal"}, {"title": "President of Cuba", "link": "https://wikipedia.org/wiki/President_of_Cuba"}]}, {"year": "1942", "text": "<PERSON>, American painter and academic (b. 1855)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Mexican painter and illustrator (b. 1883)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Clemente_Orozco\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and illustrator (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>zco\" title=\"<PERSON>\"><PERSON></a>, Mexican painter and illustrator (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_Orozco"}]}, {"year": "1951", "text": "<PERSON>, Dominican-French actress (b. 1912)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-French actress (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-French actress (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_<PERSON>z"}]}, {"year": "1951", "text": "<PERSON>, American painter and etcher (b. 1871)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American painter and etcher (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American painter and etcher (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American cartoonist (b. 1885)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fisher"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer, academic, and politician (b. 1872)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Fry\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer, academic, and politician (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Fry\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer, academic, and politician (b. 1872)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian lawyer and politician, 16th Premier of Quebec (b. 1890)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1960", "text": "<PERSON>, German carpenter and politician, President of East Germany (b. 1873)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and politician, <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">President of East Germany</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and politician, <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">President of East Germany</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leadership of East Germany", "link": "https://wikipedia.org/wiki/Leadership_of_East_Germany"}]}, {"year": "1961", "text": "<PERSON>, Dutch lawyer, jurist, and politician, 34th Prime Minister of the Netherlands (b. 1885)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer, jurist, and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer, jurist, and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1962", "text": "<PERSON>, Danish memoirist and short story writer (b. 1885)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish memoirist and short story writer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish memoirist and short story writer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English motorcycle racer and journalist (b. 1897)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer and journalist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer and journalist (b. 1897)", "links": [{"title": "<PERSON> (motorcycle racer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)"}]}, {"year": "1964", "text": "<PERSON>, American businessman (b. 1905)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American lieutenant and politician (b. 1896)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Polish-Israeli journalist and politician, 1st Internal Affairs Minister of Israel (b. 1879)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Israeli journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel\" class=\"mw-redirect\" title=\"Internal Affairs Minister of Israel\">Internal Affairs Minister of Israel</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Israeli journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel\" class=\"mw-redirect\" title=\"Internal Affairs Minister of Israel\">Internal Affairs Minister of Israel</a> (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Internal Affairs Minister of Israel", "link": "https://wikipedia.org/wiki/Internal_Affairs_Minister_of_Israel"}]}, {"year": "1971", "text": "<PERSON>, American actress (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Spring_Byington\" title=\"Spring Byington\"><PERSON></a>, American actress (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spring_Byington\" title=\"Spring Byington\"><PERSON></a>, American actress (b. 1886)", "links": [{"title": "Spring Byington", "link": "https://wikipedia.org/wiki/Spring_Byington"}]}, {"year": "1971", "text": "<PERSON>, German tenor (b. 1906)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Greek painter and illustrator (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and illustrator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and illustrator (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator (b. 1900)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>lling_<PERSON>_<PERSON>lling\" title=\"<PERSON>lling <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lling_<PERSON>_<PERSON>lling\" title=\"Holling <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (b. 1900)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>lling"}]}, {"year": "1973", "text": "<PERSON>, Kazakhstani-Russian admiral (b. 1903)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani-Russian admiral (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani-Russian admiral (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON> <PERSON><PERSON>, Ceylon politician (b. 1913)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Ceylon politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Ceylon politician (b. 1913)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, South African-English viola player (b. 1916)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English viola player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English viola player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>itz"}]}, {"year": "1978", "text": "<PERSON>, English drummer (The Who) (b. 1946)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (<a href=\"https://wikipedia.org/wiki/The_Who\" title=\"The Who\">The Who</a>) (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (<a href=\"https://wikipedia.org/wiki/The_Who\" title=\"The Who\">The Who</a>) (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Who", "link": "https://wikipedia.org/wiki/The_Who"}]}, {"year": "1978", "text": "<PERSON>, English composer and conductor (b. 1893)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English composer and conductor (b. 1893)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON><PERSON>, English literary critic and rhetorician (b. 1893)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English literary critic and rhetorician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English literary critic and rhetorician (b. 1893)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish author, poet, and painter (b. 1932)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and painter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, poet, and painter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player, coach, and manager (b. 1931)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Tatar author and prisoner of war (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Tamur<PERSON>_Dawl<PERSON>chin\" title=\"Tamur<PERSON> Dawletschin\"><PERSON><PERSON><PERSON></a>, Tatar author and prisoner of war (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dawl<PERSON>chin\" title=\"<PERSON><PERSON><PERSON> Dawletschin\"><PERSON><PERSON><PERSON></a>, Tatar author and prisoner of war (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON> Dawletschin", "link": "https://wikipedia.org/wiki/Tamur<PERSON>_Dawletschin"}]}, {"year": "1984", "text": "<PERSON>, American baseball player and manager (b. 1906)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Ukrainian cardinal (b. 1892)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lipyj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cardinal (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Slipyj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian cardinal (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>f_Slipyj"}]}, {"year": "1984", "text": "<PERSON>, Australian cricketer (b. 1916)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Dutch author (b. 1903)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Filipino cartoonist (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cartoonist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cartoonist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English-Australian public servant and politician, 26th Australian Minister for Foreign Affairs (b. 1913)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian public servant and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Les_Bury\" title=\"Les Bury\"><PERSON></a>, English-Australian public servant and politician, 26th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_Bury"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Turkish architect (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Sedad_Hakk%C4%B1_Eldem\" title=\"<PERSON>dad Hakkı Eldem\"><PERSON><PERSON></a>, Turkish architect (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sedad_Hakk%C4%B1_Eldem\" title=\"<PERSON><PERSON> Hakkı Eldem\"><PERSON><PERSON> Ha<PERSON>k<PERSON> Elde<PERSON></a>, Turkish architect (b. 1908)", "links": [{"title": "Sedad Hakkı Eldem", "link": "https://wikipedia.org/wiki/Sedad_Hakk%C4%B1_Eldem"}]}, {"year": "1989", "text": "<PERSON>, Ukrainian violinist and composer (b. 1917)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist and composer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist and composer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American general and pilot (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English historian and journalist (b. 1906)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian and journalist (b. 1906)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American physicist and chemist, Nobel Prize laureate (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1994", "text": "<PERSON>, English director and playwright (b. 1914)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and playwright (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Chinese-English director and screenwriter (b. 1915)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Chinese-English director and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Chinese-English director and screenwriter (b. 1915)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1995", "text": "<PERSON>, American cartoonist (b. 1893)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist (b. 1893)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Austrian-American actress (b. 1942)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Congolese soldier and politician, President of Zaire (b. 1930)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Zaire\" class=\"mw-redirect\" title=\"President of Zaire\">President of Zaire</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese soldier and politician, <a href=\"https://wikipedia.org/wiki/President_of_Zaire\" class=\"mw-redirect\" title=\"President of Zaire\">President of Zaire</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Zaire", "link": "https://wikipedia.org/wiki/President_of_Zaire"}]}, {"year": "2000", "text": "<PERSON>, Australian-English broadcaster (b. 1929)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English broadcaster (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English broadcaster (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American conductor and educator (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Finnish film director and producer, comedian, and inventor (b. 1930)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Spede_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and producer, comedian, and inventor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sped<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and producer, comedian, and inventor (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Spede_<PERSON><PERSON>en"}]}, {"year": "2001", "text": "<PERSON>, American actress and voice artist (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, German-Israeli colonel and gun designer, designed the <PERSON><PERSON> (b. 1923)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Uziel_Gal\" title=\"Uziel Gal\"><PERSON><PERSON><PERSON></a>, German-Israeli colonel and gun designer, designed the <a href=\"https://wikipedia.org/wiki/Uzi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uziel_Gal\" title=\"Uziel Gal\"><PERSON><PERSON><PERSON></a>, German-Israeli colonel and gun designer, designed the <a href=\"https://wikipedia.org/wiki/Uzi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (b. 1923)", "links": [{"title": "Uziel Gal", "link": "https://wikipedia.org/wiki/U<PERSON>l_Gal"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uzi"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter (b. 1947)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Dutch minister and theologian (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Biezeveld\" title=\"<PERSON><PERSON> Biezeveld\"><PERSON><PERSON></a>, Dutch minister and theologian (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zeveld\" title=\"<PERSON><PERSON> Biezeveld\"><PERSON><PERSON></a>, Dutch minister and theologian (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kune_Biezeveld"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Romanian rugby player and actor (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Ilarion_Ciobanu\" title=\"Ilarion Ciobanu\"><PERSON><PERSON><PERSON></a>, Romanian rugby player and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilarion_C<PERSON>u\" title=\"Ilarion Ciobanu\"><PERSON><PERSON><PERSON></a>, Romanian rugby player and actor (b. 1931)", "links": [{"title": "Ilarion Ciobanu", "link": "https://wikipedia.org/wiki/Ilarion_Ciobanu"}]}, {"year": "2008", "text": "<PERSON>, American basketball player and coach (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American author (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Japanese director and producer (b. 1973)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Nagi_<PERSON>da\" title=\"Nagi Noda\"><PERSON><PERSON></a>, Japanese director and producer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Nagi Noda\"><PERSON><PERSON></a>, Japanese director and producer (b. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nagi_<PERSON>da"}]}, {"year": "2010", "text": "<PERSON>, Serbian skier (b. 1991)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian skier (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian skier (b. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Amar_Garibovi%C4%87"}]}, {"year": "2010", "text": "<PERSON>, American historian and author (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American author (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_Holland"}]}, {"year": "2010", "text": "<PERSON>, German-American businessman (b.  1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1952)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "Victims of the 2011 Lokomotiv Yaroslavl plane crash:\n<PERSON><PERSON><PERSON>, Slovakian ice hockey player (b. 1974)\n<PERSON>, Russian ice hockey player and coach (b. 1970)\n<PERSON>, Russian ice hockey player and coach (b. 1970)\n<PERSON>, Polish-Swedish ice hockey player (b. 1980)\n<PERSON>, Czech ice hockey player (b. 1979)\n<PERSON>, Canadian ice hockey player and coach (b. 1959)\n<PERSON><PERSON>, Czech ice hockey player (b. 1979)\n<PERSON><PERSON><PERSON><PERSON>, Latvian ice hockey player (b. 1974)\n<PERSON><PERSON><PERSON>, Belarusian ice hockey player (b. 1974)\n<PERSON>, Czech ice hockey player (b. 1980)", "html": "2011 - Victims of the <a href=\"https://wikipedia.org/wiki/2011_Lokomotiv_Yaroslavl_plane_crash\" class=\"mw-redirect\" title=\"2011 Lokomotiv Yaroslavl plane crash\">2011 Lokomotiv Yaroslavl plane crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Pavol_Demitra\" title=\"Pavol Demitra\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swedish ice hockey player (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1979)\" title=\"<PERSON> <PERSON>k (ice hockey, born 1979)\">Jan <PERSON></a>, Czech ice hockey player (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Brad_McCrimmon\" title=\"Brad McCrimmon\">Brad McCrimmon</a>, Canadian ice hockey player and coach (b. 1959)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Karel_Rach%C5%AFnek\" title=\"Karel Rachůnek\">Karel Rachůnek</a>, Czech ice hockey player (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"Kārlis Skrastiņš\">Kārlis Skrastiņš</a>, Latvian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ruslan_Salei\" title=\"Ruslan Salei\">Ruslan Salei</a>, Belarusian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Josef_Va%C5%A1%C3%AD%C4%8Dek\" title=\"Josef Vašíček\">Josef Vašíček</a>, Czech ice hockey player (b. 1980)</li>\n</ul>", "no_year_html": "Victims of the <a href=\"https://wikipedia.org/wiki/2011_Lokomotiv_Yaroslavl_plane_crash\" class=\"mw-redirect\" title=\"2011 Lokomotiv Yaroslavl plane crash\">2011 Lokomotiv Yaroslavl plane crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Pavol_Demitra\" title=\"Pavol Demitra\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swedish ice hockey player (b. 1980)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1979)\" title=\"<PERSON> (ice hockey, born 1979)\"><PERSON></a>, Czech ice hockey player (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Brad_McCrimmon\" title=\"Brad McCrimmon\">Brad McCrimmon</a>, Canadian ice hockey player and coach (b. 1959)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Karel_Rach%C5%AFnek\" title=\"Karel Rachůnek\">Karel Rachůnek</a>, Czech ice hockey player (b. 1979)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"Kārlis Skrastiņš\">Kārlis Skrastiņš</a>, Latvian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Ruslan_Salei\" title=\"Ruslan Salei\">Ruslan Salei</a>, Belarusian ice hockey player (b. 1974)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Josef_Va%C5%A1%C3%AD%C4%8Dek\" title=\"Josef Vašíček\">Josef Vašíček</a>, Czech ice hockey player (b. 1980)</li>\n</ul>", "links": [{"title": "2011 Lokomotiv Yaroslavl plane crash", "link": "https://wikipedia.org/wiki/2011_Lokomotiv_Yaroslavl_plane_crash"}, {"title": "Pavol Demitra", "link": "https://wikipedia.org/wiki/Pa<PERSON>l_Demitra"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (ice hockey, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1979)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%AFnek"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_Va%C5%A1%C3%AD%C4%8Dek"}]}, {"year": "<PERSON><PERSON><PERSON>, Slovakian ice hockey player (b. 1974)", "text": null, "html": "<PERSON><PERSON><PERSON>, Slovakian ice hockey player (b. 1974) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Pa<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Demit<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian ice hockey player (b. 1974)", "links": [{"title": "Pavol Demitra", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>l_Demitra"}]}, {"year": "<PERSON>, Russian ice hockey player and coach (b. 1970)", "text": null, "html": "<PERSON>, Russian ice hockey player and coach (b. 1970) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>"}]}, {"year": "<PERSON>, Russian ice hockey player and coach (b. 1970)", "text": null, "html": "<PERSON>, Russian ice hockey player and coach (b. 1970) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, Polish-Swedish ice hockey player (b. 1980)", "text": null, "html": "<PERSON>, Polish-Swedish ice hockey player (b. 1980) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swedish ice hockey player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Swedish ice hockey player (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, Czech ice hockey player (b. 1979)", "text": null, "html": "<PERSON>, Czech ice hockey player (b. 1979) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1979)\" title=\"<PERSON> (ice hockey, born 1979)\"><PERSON></a>, Czech ice hockey player (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1979)\" title=\"<PERSON> (ice hockey, born 1979)\"><PERSON></a>, Czech ice hockey player (b. 1979)", "links": [{"title": "<PERSON> (ice hockey, born 1979)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1979)"}]}, {"year": "<PERSON>, Canadian ice hockey player and coach (b. 1959)", "text": null, "html": "<PERSON>, Canadian ice hockey player and coach (b. 1959) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, Czech ice hockey player (b. 1979)", "text": null, "html": "<PERSON><PERSON>, Czech ice hockey player (b. 1979) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%AFnek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%AFnek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player (b. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ch%C5%AFnek"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON>, Latvian ice hockey player (b. 1974)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON>, Latvian ice hockey player (b. 1974) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/K%C4%81rlis_Skrasti%C5%86%C5%A1"}]}, {"year": "<PERSON><PERSON><PERSON>, Belarusian ice hockey player (b. 1974)", "text": null, "html": "<PERSON><PERSON><PERSON>, Belarusian ice hockey player (b. 1974) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian ice hockey player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian ice hockey player (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "<PERSON>, Czech ice hockey player (b. 1980)", "text": null, "html": "<PERSON>, Czech ice hockey player (b. 1980) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>a%C5%A1%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>a%C5%A1%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Josef_Va%C5%A1%C3%AD%C4%8Dek"}]}, {"year": "2012", "text": "<PERSON>, Spanish director and screenwriter (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Fern%C3%A1ndez_Ardav%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Fern%C3%A1ndez_Ardav%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Fern%C3%A1ndez_Ardav%C3%ADn"}]}, {"year": "2012", "text": "<PERSON>, Russian footballer and manager (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American computer scientist and programmer (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Pakistani-Indian politician and diplomat, 13th Foreign Secretary of India (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian politician and diplomat, 13th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian politician and diplomat, 13th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ari"}, {"title": "Foreign Secretary (India)", "link": "https://wikipedia.org/wiki/Foreign_Secretary_(India)"}]}, {"year": "2013", "text": "<PERSON>, English-Australian politician, 7th Deputy Premier of South Australia (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_South_Australia\" title=\"Deputy Premier of South Australia\">Deputy Premier of South Australia</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_South_Australia\" title=\"Deputy Premier of South Australia\">Deputy Premier of South Australia</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Premier of South Australia", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_South_Australia"}]}, {"year": "2013", "text": "<PERSON>, American cartoonist (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Czech playwright and composer (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hu<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech playwright and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech playwright and composer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilja_Hurn%C3%ADk"}]}, {"year": "2013", "text": "<PERSON>, American cellist and composer (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cellist)\" title=\"<PERSON> (cellist)\"><PERSON></a>, American cellist and composer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cellist)\" title=\"<PERSON> (cellist)\"><PERSON></a>, American cellist and composer (b. 1919)", "links": [{"title": "<PERSON> (cellist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cellist)"}]}, {"year": "2014", "text": "<PERSON><PERSON>, South Korean singer (b. 1991)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ri-se\" title=\"Kwon Ri-se\"><PERSON><PERSON>-<PERSON></a>, South Korean singer (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_Ri-se\" title=\"Kwon Ri-se\"><PERSON><PERSON>-<PERSON></a>, South Korean singer (b. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-se"}]}, {"year": "2014", "text": "<PERSON>, American sportscaster and radio host (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and radio host (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and radio host (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Filipino lawyer and politician, 42nd Filipino Secretary of Justice (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)\" title=\"Secretary of Justice (Philippines)\">Filipino Secretary of Justice</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)\" title=\"Secretary of Justice (Philippines)\">Filipino Secretary of Justice</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of Justice (Philippines)", "link": "https://wikipedia.org/wiki/Secretary_of_Justice_(Philippines)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Chinese-Japanese actress, singer, and politician (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%8Ctaka\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese actress, singer, and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%8Ctaka\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese actress, singer, and politician (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON><PERSON>_%C5%8Ctaka"}]}, {"year": "2014", "text": "<PERSON>, Canadian businessman and philanthropist (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American actor (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, American actor (b. 1925)", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American porn actress, director, and producer (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>di<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>di<PERSON>\"><PERSON><PERSON><PERSON></a>, American porn actress, director, and producer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>di<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American porn actress, director, and producer (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Candida_<PERSON>le"}]}, {"year": "2015", "text": "<PERSON>, Cuban pianist, composer, and bandleader (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist, composer, and bandleader (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist, composer, and bandleader (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillermo_Rubalcaba"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Greek actress (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actress (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Nicaraguan footballer (b. 1939)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan footballer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan footballer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n"}]}, {"year": "2018", "text": "<PERSON>, American rapper (b. 1992)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Polish architect, participant in the Warsaw Uprising (b. 1923)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish architect, participant in the Warsaw Uprising (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish architect, participant in the Warsaw Uprising (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German-American jazz writer and editor (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jazz writer and editor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American jazz writer and editor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}]}}