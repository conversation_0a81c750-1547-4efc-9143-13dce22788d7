{"date": "June 22", "url": "https://wikipedia.org/wiki/June_22", "data": {"Events": [{"year": "217 BC", "text": "Battle of Raphia: <PERSON> IV <PERSON>ator of Egypt defeats <PERSON><PERSON> the Great of the Seleucid kingdom.", "html": "217 BC - 217 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Raphia\" title=\"Battle of Raphia\">Battle of Raphia</a>: <a href=\"https://wikipedia.org/wiki/Ptolemy_IV_Philopator\" title=\"Ptolemy IV Philopator\">Ptolemy IV Philopator</a> of Egypt defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_the_Great\" title=\"<PERSON><PERSON> III the Great\"><PERSON><PERSON> III the Great</a> of the <a href=\"https://wikipedia.org/wiki/Seleucid_kingdom\" class=\"mw-redirect\" title=\"Seleucid kingdom\">Seleucid kingdom</a>.", "no_year_html": "217 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Raphia\" title=\"Battle of Raphia\">Battle of Raphia</a>: <a href=\"https://wikipedia.org/wiki/Ptolemy_IV_Philopator\" title=\"Ptolemy IV Philopator\">Ptolemy IV Philopator</a> of Egypt defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_the_Great\" title=\"<PERSON><PERSON> III the Great\"><PERSON><PERSON> III the Great</a> of the <a href=\"https://wikipedia.org/wiki/Seleucid_kingdom\" class=\"mw-redirect\" title=\"Seleucid kingdom\">Seleucid kingdom</a>.", "links": [{"title": "Battle of Raphia", "link": "https://wikipedia.org/wiki/Battle_of_Raphia"}, {"title": "Ptolemy IV Philopator", "link": "https://wikipedia.org/wiki/Ptolemy_IV_Philopator"}, {"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_III_the_Great"}, {"title": "Seleucid kingdom", "link": "https://wikipedia.org/wiki/Seleucid_kingdom"}]}, {"year": "168 BC", "text": "Battle of Pydna: Romans under <PERSON><PERSON><PERSON> defeat Macedonian King <PERSON><PERSON><PERSON> who surrenders after the battle, ending the Third Macedonian War.", "html": "168 BC - 168 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Pydna\" title=\"Battle of Pydna\">Battle of Pydna</a>: <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Romans</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>ilius_<PERSON>_<PERSON>icus\" title=\"Lucius <PERSON>ilius <PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Macedonia_(ancient_kingdom)\" title=\"Macedonia (ancient kingdom)\">Macedonian</a> King <a href=\"https://wikipedia.org/wiki/Perseus_of_Macedon\" title=\"Perseus of Macedon\">Per<PERSON>us</a> who surrenders after the battle, ending the <a href=\"https://wikipedia.org/wiki/Third_Macedonian_War\" title=\"Third Macedonian War\">Third Macedonian War</a>.", "no_year_html": "168 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Pydna\" title=\"Battle of Pydna\">Battle of Pydna</a>: <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Romans</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>emilius_<PERSON>_<PERSON>icus\" title=\"Lucius <PERSON>us <PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Macedonia_(ancient_kingdom)\" title=\"Macedonia (ancient kingdom)\">Macedonian</a> King <a href=\"https://wikipedia.org/wiki/Perseus_of_Macedon\" title=\"Perseus of Macedon\"><PERSON><PERSON><PERSON></a> who surrenders after the battle, ending the <a href=\"https://wikipedia.org/wiki/Third_Macedonian_War\" title=\"Third Macedonian War\">Third Macedonian War</a>.", "links": [{"title": "Battle of Pydna", "link": "https://wikipedia.org/wiki/Battle_of_Pydna"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>icus"}, {"title": "Macedonia (ancient kingdom)", "link": "https://wikipedia.org/wiki/Macedonia_(ancient_kingdom)"}, {"title": "<PERSON><PERSON><PERSON> of Macedon", "link": "https://wikipedia.org/wiki/Perseus_of_Macedon"}, {"title": "Third Macedonian War", "link": "https://wikipedia.org/wiki/Third_Macedonian_War"}]}, {"year": "331", "text": "The Council of Ephesus, the third ecumenical council, begins, dealing with Nestorianism.", "html": "331 - The <a href=\"https://wikipedia.org/wiki/Council_of_Ephesus\" title=\"Council of Ephesus\">Council of Ephesus</a>, the third <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a>, begins, dealing with <a href=\"https://wikipedia.org/wiki/Nestorianism\" title=\"Nestorianism\">Nestorianism</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Ephesus\" title=\"Council of Ephesus\">Council of Ephesus</a>, the third <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a>, begins, dealing with <a href=\"https://wikipedia.org/wiki/Nestorianism\" title=\"Nestorianism\">Nestorianism</a>.", "links": [{"title": "Council of Ephesus", "link": "https://wikipedia.org/wiki/Council_of_Ephesus"}, {"title": "Ecumenical council", "link": "https://wikipedia.org/wiki/Ecumenical_council"}, {"title": "Nestorianism", "link": "https://wikipedia.org/wiki/Nestorianism"}]}, {"year": "813", "text": "Battle of Versinikia: The Bulgars led by K<PERSON> defeat the Byzantine army near Edirne. Emperor <PERSON> is forced to abdicate in favor of <PERSON> the Armenian.", "html": "813 - <a href=\"https://wikipedia.org/wiki/Battle_of_Versinikia\" title=\"Battle of Versinikia\">Battle of Versinikia</a>: The <a href=\"https://wikipedia.org/wiki/Bulgars\" title=\"Bulgars\">Bulgars</a> led by <a href=\"https://wikipedia.org/wiki/Krum\" title=\"Krum\">Krum</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army near <a href=\"https://wikipedia.org/wiki/Edirne\" title=\"Edirne\"><PERSON>irn<PERSON></a>. Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a> is forced to abdicate in favor of <a href=\"https://wikipedia.org/wiki/Leo_V_the_Armenian\" title=\"<PERSON> V the Armenian\"><PERSON> V the Armenian</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Versinikia\" title=\"Battle of Versinikia\">Battle of Versinikia</a>: The <a href=\"https://wikipedia.org/wiki/Bulgars\" title=\"Bulgars\">Bulgars</a> led by <a href=\"https://wikipedia.org/wiki/Krum\" title=\"Krum\">Krum</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army near <a href=\"https://wikipedia.org/wiki/Edirne\" title=\"Edirne\"><PERSON>irn<PERSON></a>. Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is forced to abdicate in favor of <a href=\"https://wikipedia.org/wiki/Leo_V_the_Armenian\" title=\"<PERSON> V the Armenian\"><PERSON> V the Armenian</a>.", "links": [{"title": "Battle of Versinikia", "link": "https://wikipedia.org/wiki/Battle_of_Versinikia"}, {"title": "Bulgars", "link": "https://wikipedia.org/wiki/Bulgars"}, {"title": "Krum", "link": "https://wikipedia.org/wiki/Krum"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Ed<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edirne"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> the Armenian", "link": "https://wikipedia.org/wiki/Leo_V_the_Armenian"}]}, {"year": "910", "text": "The Hungarians defeat the East Frankish army near the Rednitz River, killing its leader <PERSON><PERSON><PERSON>, Duke of Lotharingia (Lorraine).", "html": "910 - The <a href=\"https://wikipedia.org/wiki/Hungarians\" title=\"Hungarians\">Hungarians</a> defeat the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish</a> army near the <a href=\"https://wikipedia.org/wiki/Rednitz\" title=\"Rednitz\">Rednitz River</a>, killing its leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON>_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON></a>, Duke of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> (Lorraine).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hungarians\" title=\"Hungarians\">Hungarians</a> defeat the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish</a> army near the <a href=\"https://wikipedia.org/wiki/Rednitz\" title=\"Rednitz\">Rednitz River</a>, killing its leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON></a>, Duke of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lotharingia</a> (Lorraine).", "links": [{"title": "Hungarians", "link": "https://wikipedia.org/wiki/Hungarians"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}, {"title": "Rednitz", "link": "https://wikipedia.org/wiki/Rednitz"}, {"title": "<PERSON><PERSON><PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON>_of_Lorraine"}, {"title": "Lotharingia", "link": "https://wikipedia.org/wiki/Lotharingia"}]}, {"year": "1527", "text": "<PERSON><PERSON><PERSON> expels Portuguese forces from Sunda Kelapa, now regarded as the foundation of Jakarta.", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON>ahi<PERSON>\"><PERSON><PERSON><PERSON></a> expels <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> forces from <a href=\"https://wikipedia.org/wiki/Sunda_Kelapa\" title=\"Sunda Kelapa\"><PERSON><PERSON> Kelapa</a>, now regarded as the foundation of <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> expels <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese</a> forces from <a href=\"https://wikipedia.org/wiki/Sunda_Kelapa\" title=\"Sunda Kelapa\"><PERSON><PERSON> Kelapa</a>, now regarded as the foundation of <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sun<PERSON>_<PERSON>a"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}]}, {"year": "1593", "text": "Battle of Sisak: Allied Christian troops defeat the Ottomans.", "html": "1593 - <a href=\"https://wikipedia.org/wiki/Battle_of_Sisak\" title=\"Battle of Sisak\">Battle of Sisak</a>: Allied Christian troops defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Sisak\" title=\"Battle of Sisak\">Battle of Sisak</a>: Allied Christian troops defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a>.", "links": [{"title": "Battle of Sisak", "link": "https://wikipedia.org/wiki/Battle_of_Sisak"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1633", "text": "The Holy Office in Rome forces <PERSON> to recant his view that the Sun, not the Earth, is the center of the Universe in the form he presented it in, after heated controversy.", "html": "1633 - The <a href=\"https://wikipedia.org/wiki/Congregation_for_the_Doctrine_of_the_Faith\" class=\"mw-redirect\" title=\"Congregation for the Doctrine of the Faith\">Holy Office</a> in Rome forces <a href=\"https://wikipedia.org/wiki/Galileo_Galilei\" title=\"Galileo Galilei\">Galileo Galilei</a> to <a href=\"https://wikipedia.org/wiki/Galileo_affair\" title=\"Galileo affair\">recant</a> his view that the Sun, not the Earth, is the center of the Universe in the form he presented it in, after heated controversy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congregation_for_the_Doctrine_of_the_Faith\" class=\"mw-redirect\" title=\"Congregation for the Doctrine of the Faith\">Holy Office</a> in Rome forces <a href=\"https://wikipedia.org/wiki/Galileo_Galilei\" title=\"Galileo Galilei\">Galileo Galilei</a> to <a href=\"https://wikipedia.org/wiki/Galileo_affair\" title=\"Galileo affair\">recant</a> his view that the Sun, not the Earth, is the center of the Universe in the form he presented it in, after heated controversy.", "links": [{"title": "Congregation for the Doctrine of the Faith", "link": "https://wikipedia.org/wiki/Congregation_for_the_Doctrine_of_the_Faith"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gali<PERSON>i"}, {"title": "Galileo affair", "link": "https://wikipedia.org/wiki/Galileo_affair"}]}, {"year": "1774", "text": "The British pass the Quebec Act, setting out rules of governance for the colony of Quebec in British North America.", "html": "1774 - The British pass the <a href=\"https://wikipedia.org/wiki/Quebec_Act\" title=\"Quebec Act\">Quebec Act</a>, setting out rules of governance for the colony of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> in British North America.", "no_year_html": "The British pass the <a href=\"https://wikipedia.org/wiki/Quebec_Act\" title=\"Quebec Act\">Quebec Act</a>, setting out rules of governance for the colony of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> in British North America.", "links": [{"title": "Quebec Act", "link": "https://wikipedia.org/wiki/Quebec_Act"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}]}, {"year": "1783", "text": "A poisonous cloud caused by the eruption of the Laki volcano in Iceland reaches Le Havre in France.", "html": "1783 - A poisonous cloud caused by the <a href=\"https://wikipedia.org/wiki/Types_of_volcanic_eruptions\" class=\"mw-redirect\" title=\"Types of volcanic eruptions\">eruption</a> of the <a href=\"https://wikipedia.org/wiki/Laki\" title=\"Laki\">Laki</a> volcano in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> reaches <a href=\"https://wikipedia.org/wiki/Le_Havre\" title=\"Le Havre\">Le Havre</a> in France.", "no_year_html": "A poisonous cloud caused by the <a href=\"https://wikipedia.org/wiki/Types_of_volcanic_eruptions\" class=\"mw-redirect\" title=\"Types of volcanic eruptions\">eruption</a> of the <a href=\"https://wikipedia.org/wiki/Laki\" title=\"Laki\"><PERSON>ki</a> volcano in <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> reaches <a href=\"https://wikipedia.org/wiki/Le_Havre\" title=\"Le Havre\">Le Havre</a> in France.", "links": [{"title": "Types of volcanic eruptions", "link": "https://wikipedia.org/wiki/Types_of_volcanic_eruptions"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ki"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Le Havre", "link": "https://wikipedia.org/wiki/Le_<PERSON>vre"}]}, {"year": "1793", "text": "Haitian Revolution: The Battle of Cap-Français ends with French Republican troops and black slave insurgents capturing the city.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cap-Fran%C3%A7ais_(1793)\" title=\"Battle of Cap-Français (1793)\">Battle of Cap-Français</a> ends with French Republican troops and black slave insurgents capturing the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cap-Fran%C3%A7ais_(1793)\" title=\"Battle of Cap-Français (1793)\">Battle of Cap-Français</a> ends with French Republican troops and black slave insurgents capturing the city.", "links": [{"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}, {"title": "Battle of Cap-Français (1793)", "link": "https://wikipedia.org/wiki/Battle_of_Cap-Fran%C3%A7ais_(1793)"}]}, {"year": "1807", "text": "In the Chesapeake-Leopard affair, the British warship HMS Leopard attacks and boards the American frigate USS Chesapeake.", "html": "1807 - In the <a href=\"https://wikipedia.org/wiki/Chesapeake%E2%80%93Leopard_affair\" title=\"Chesapeake-Leopard affair\"><i>Chesapeake</i>-<i><PERSON><PERSON></i> affair</a>, the British warship <a href=\"https://wikipedia.org/wiki/HMS_Leopard_(1790)\" title=\"HMS Leopard (1790)\">HMS <i><PERSON><PERSON></i></a> attacks and boards the American frigate <a href=\"https://wikipedia.org/wiki/USS_Chesapeake_(1799)\" title=\"USS Chesapeake (1799)\">USS <i>Chesapeake</i></a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Chesapeake%E2%80%93Leopard_affair\" title=\"Chesapeake-Leopard affair\"><i>Chesapeake</i>-<i><PERSON><PERSON></i> affair</a>, the British warship <a href=\"https://wikipedia.org/wiki/HMS_Leopard_(1790)\" title=\"HMS Leopard (1790)\">HMS <i><PERSON><PERSON></i></a> attacks and boards the American frigate <a href=\"https://wikipedia.org/wiki/USS_Chesapeake_(1799)\" title=\"USS Chesapeake (1799)\">USS <i>Chesapeake</i></a>.", "links": [{"title": "Chesapeake-Leopard affair", "link": "https://wikipedia.org/wiki/Chesapeake%E2%80%93Leopard_affair"}, {"title": "HMS Leopard (1790)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1790)"}, {"title": "USS Chesapeake (1799)", "link": "https://wikipedia.org/wiki/USS_Chesapeake_(1799)"}]}, {"year": "1812", "text": "France declares war on Russia, starting <PERSON>'s invasion.", "html": "1812 - France <a href=\"https://wikipedia.org/wiki/1812_French_declaration_of_war_on_Russia\" title=\"1812 French declaration of war on Russia\">declares war on Russia</a>, starting <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\"><PERSON>'s invasion</a>.", "no_year_html": "France <a href=\"https://wikipedia.org/wiki/1812_French_declaration_of_war_on_Russia\" title=\"1812 French declaration of war on Russia\">declares war on Russia</a>, starting <a href=\"https://wikipedia.org/wiki/French_invasion_of_Russia\" title=\"French invasion of Russia\"><PERSON>'s invasion</a>.", "links": [{"title": "1812 French declaration of war on Russia", "link": "https://wikipedia.org/wiki/1812_French_declaration_of_war_on_Russia"}, {"title": "French invasion of Russia", "link": "https://wikipedia.org/wiki/French_invasion_of_Russia"}]}, {"year": "1813", "text": "War of 1812: After learning of American plans for a surprise attack on Beaver Dams in Ontario, <PERSON> sets out on a thirty kilometres (19 mi) journey on foot to warn Lieutenant <PERSON>.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: After learning of American plans for a surprise attack on Beaver Dams in <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets out on a thirty kilometres (19 mi) journey on foot to warn Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: After learning of American plans for a surprise attack on Beaver Dams in <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets out on a thirty kilometres (19 mi) journey on foot to warn Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_Secord"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1839", "text": "Cherokee leaders <PERSON>, <PERSON>, and <PERSON> are assassinated for signing the Treaty of New Echota, which had resulted in the Trail of Tears.", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Cherokee\" title=\"Cherokee\">Cherokee</a> leaders <a href=\"https://wikipedia.org/wiki/Major_Ridge\" title=\"Major Ridge\">Major Ridge</a>, <a href=\"https://wikipedia.org/wiki/John_Ridge\" title=\"John Ridge\">John Ridge</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>(Cherokee)\" title=\"<PERSON> (Cherokee)\"><PERSON></a> are assassinated for signing the <a href=\"https://wikipedia.org/wiki/Treaty_of_New_Echota\" title=\"Treaty of New Echota\">Treaty of New Echota</a>, which had resulted in the <a href=\"https://wikipedia.org/wiki/Cherokee_removal\" title=\"Cherokee removal\">Trail of Tears</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherokee\" title=\"Cherokee\">Cherokee</a> leaders <a href=\"https://wikipedia.org/wiki/Major_Ridge\" title=\"Major Ridge\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/John_Ridge\" title=\"John Ridge\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>(Cherokee)\" title=\"<PERSON> (Cherokee)\"><PERSON></a> are assassinated for signing the <a href=\"https://wikipedia.org/wiki/Treaty_of_New_Echota\" title=\"Treaty of New Echota\">Treaty of New Echota</a>, which had resulted in the <a href=\"https://wikipedia.org/wiki/Cherokee_removal\" title=\"Cherokee removal\">Trail of Tears</a>.", "links": [{"title": "Cherokee", "link": "https://wikipedia.org/wiki/Cherokee"}, {"title": "Major <PERSON>", "link": "https://wikipedia.org/wiki/Major_Ridge"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Cherokee)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cherokee)"}, {"title": "Treaty of New Echota", "link": "https://wikipedia.org/wiki/Treaty_of_New_Echota"}, {"title": "Cherokee removal", "link": "https://wikipedia.org/wiki/Cherokee_removal"}]}, {"year": "1870", "text": "The United States Department of Justice is created by the U.S. Congress.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> is created by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a> is created by the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "links": [{"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1893", "text": "The Royal Navy battleship HMS Camperdown accidentally rams the British Mediterranean Fleet flagship HMS Victoria which sinks taking 358 crew with her, including the fleet's commander, Vice-Admiral Sir <PERSON>.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a> <a href=\"https://wikipedia.org/wiki/HMS_Camperdown_(1885)\" title=\"HMS Camperdown (1885)\">HMS <i>Camperdown</i></a> accidentally rams the <a href=\"https://wikipedia.org/wiki/British_Mediterranean_Fleet\" class=\"mw-redirect\" title=\"British Mediterranean Fleet\">British Mediterranean Fleet</a> <a href=\"https://wikipedia.org/wiki/Flagship\" title=\"Flagship\">flagship</a> <a href=\"https://wikipedia.org/wiki/HMS_Victoria_(1887)\" title=\"HMS Victoria (1887)\">HMS <i>Victoria</i></a> which sinks taking 358 crew with her, including the fleet's commander, Vice-Admiral Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battleship\" title=\"Battleship\">battleship</a> <a href=\"https://wikipedia.org/wiki/HMS_Camperdown_(1885)\" title=\"HMS Camperdown (1885)\">HMS <i>Camperdown</i></a> accidentally rams the <a href=\"https://wikipedia.org/wiki/British_Mediterranean_Fleet\" class=\"mw-redirect\" title=\"British Mediterranean Fleet\">British Mediterranean Fleet</a> <a href=\"https://wikipedia.org/wiki/Flagship\" title=\"Flagship\">flagship</a> <a href=\"https://wikipedia.org/wiki/HMS_Victoria_(1887)\" title=\"HMS Victoria (1887)\">HMS <i>Victoria</i></a> which sinks taking 358 crew with her, including the fleet's commander, Vice-Admiral Sir <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battleship", "link": "https://wikipedia.org/wiki/Battleship"}, {"title": "HMS Camperdown (1885)", "link": "https://wikipedia.org/wiki/HMS_Camperdown_(1885)"}, {"title": "British Mediterranean Fleet", "link": "https://wikipedia.org/wiki/British_Mediterranean_Fleet"}, {"title": "Flagship", "link": "https://wikipedia.org/wiki/Flagship"}, {"title": "HMS Victoria (1887)", "link": "https://wikipedia.org/wiki/HMS_Victoria_(1887)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "British colonial officers <PERSON> and Lt. <PERSON> are assassinated in Pune, Maharashtra, India by the <PERSON><PERSON> brothers and <PERSON><PERSON><PERSON>, who are later caught and hanged.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/British_Colony\" class=\"mw-redirect\" title=\"British Colony\">British colonial officers</a> <PERSON> and Lt. <PERSON> are assassinated in <a href=\"https://wikipedia.org/wiki/Pune\" title=\"Pune\">Pune</a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, India by the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_brothers\" title=\"<PERSON><PERSON><PERSON> brothers\"><PERSON><PERSON><PERSON> brothers</a> and <PERSON><PERSON><PERSON>, who are later caught and hanged.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_Colony\" class=\"mw-redirect\" title=\"British Colony\">British colonial officers</a> <PERSON> and Lt. <PERSON> are assassinated in <a href=\"https://wikipedia.org/wiki/Pune\" title=\"Pune\">Pune</a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, India by the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_brothers\" title=\"<PERSON><PERSON><PERSON> brothers\"><PERSON><PERSON><PERSON> brothers</a> and <PERSON><PERSON><PERSON>, who are later caught and hanged.", "links": [{"title": "British Colony", "link": "https://wikipedia.org/wiki/British_Colony"}, {"title": "Pune", "link": "https://wikipedia.org/wiki/Pune"}, {"title": "Maharashtra", "link": "https://wikipedia.org/wiki/Maharashtra"}, {"title": "<PERSON><PERSON><PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_brothers"}]}, {"year": "1898", "text": "Spanish-American War: In a chaotic operation, 6,000 men of the U.S. Fifth Army Corps begins landing at Daiquirí, Cuba, about 16 miles (26 km) east of Santiago de Cuba. Lt. Gen. <PERSON><PERSON><PERSON> of the Spanish Army outnumbers them two-to-one, but does not oppose the landings.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: In a chaotic operation, 6,000 men of the <a href=\"https://wikipedia.org/wiki/Fifth_Army_Corps_(Spanish%E2%80%93American_War)\" title=\"Fifth Army Corps (Spanish-American War)\">U.S. Fifth Army Corps</a> begins landing at <a href=\"https://wikipedia.org/wiki/Daiquir%C3%AD\" title=\"Daiquirí\">Daiquirí</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, about 16 miles (26 km) east of <a href=\"https://wikipedia.org/wiki/Santiago_de_Cuba\" title=\"Santiago de Cuba\">Santiago de Cuba</a>. Lt. Gen. <a href=\"https://wikipedia.org/wiki/Arsenio_Linares_y_Pombo\" title=\"Arsenio Lin<PERSON> y Pombo\">Arsen<PERSON> y <PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Spanish_Army\" title=\"Spanish Army\">Spanish Army</a> outnumbers them two-to-one, but does not oppose the landings.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: In a chaotic operation, 6,000 men of the <a href=\"https://wikipedia.org/wiki/Fifth_Army_Corps_(Spanish%E2%80%93American_War)\" title=\"Fifth Army Corps (Spanish-American War)\">U.S. Fifth Army Corps</a> begins landing at <a href=\"https://wikipedia.org/wiki/Daiquir%C3%AD\" title=\"Daiquirí\">Daiquirí</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, about 16 miles (26 km) east of <a href=\"https://wikipedia.org/wiki/Santiago_de_Cuba\" title=\"Santiago de Cuba\">Santiago de Cuba</a>. Lt. Gen. <a href=\"https://wikipedia.org/wiki/Arsenio_Linares_y_Pombo\" title=\"Arsenio Linares y Pombo\">Arsen<PERSON> y <PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Spanish_Army\" title=\"Spanish Army\">Spanish Army</a> outnumbers them two-to-one, but does not oppose the landings.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Fifth Army Corps (Spanish-American War)", "link": "https://wikipedia.org/wiki/Fifth_Army_Corps_(Spanish%E2%80%93American_War)"}, {"title": "Daiquirí", "link": "https://wikipedia.org/wiki/Daiquir%C3%AD"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Santiago de Cuba", "link": "https://wikipedia.org/wiki/Santiago_de_Cuba"}, {"title": "<PERSON><PERSON><PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/Arsenio_Linares_y_Pombo"}, {"title": "Spanish Army", "link": "https://wikipedia.org/wiki/Spanish_Army"}]}, {"year": "1907", "text": "The London Underground's Charing Cross, Euston and Hampstead Railway opens.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>'s <a href=\"https://wikipedia.org/wiki/Charing_Cross,_Euston_and_Hampstead_Railway\" title=\"Charing Cross, Euston and Hampstead Railway\">Charing Cross, Euston and Hampstead Railway</a> opens.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">London Underground</a>'s <a href=\"https://wikipedia.org/wiki/Charing_Cross,_Euston_and_Hampstead_Railway\" title=\"Charing Cross, Euston and Hampstead Railway\">Charing Cross, Euston and Hampstead Railway</a> opens.", "links": [{"title": "London Underground", "link": "https://wikipedia.org/wiki/London_Underground"}, {"title": "Charing Cross, Euston and Hampstead Railway", "link": "https://wikipedia.org/wiki/<PERSON>ring_Cross,_Euston_and_Hampstead_Railway"}]}, {"year": "1911", "text": "<PERSON> and <PERSON> of Teck are crowned King and Queen of the United Kingdom of Great Britain and Ireland.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of <PERSON>ck</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_V_and_<PERSON>\" title=\"Coronation of <PERSON> V and Mary\">crowned King and Queen</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Teck\"><PERSON> of Teck</a> are <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_V_and_<PERSON>\" title=\"Coronation of <PERSON> V and Mary\">crowned King and Queen</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Teck", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "Coronation of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON>"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1911", "text": "Mexican Revolution: Government forces bring an end to the Magonista rebellion of 1911 in the Second Battle of Tijuana.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: Government forces bring an end to the <a href=\"https://wikipedia.org/wiki/Magonista_rebellion_of_1911\" title=\"Magonista rebellion of 1911\">Magonista rebellion of 1911</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Tijuana\" title=\"Second Battle of Tijuana\">Second Battle of Tijuana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: Government forces bring an end to the <a href=\"https://wikipedia.org/wiki/Magonista_rebellion_of_1911\" title=\"Magonista rebellion of 1911\">Magonista rebellion of 1911</a> in the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Tijuana\" title=\"Second Battle of Tijuana\">Second Battle of Tijuana</a>.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "Magonista rebellion of 1911", "link": "https://wikipedia.org/wiki/Magonista_rebellion_of_1911"}, {"title": "Second Battle of Tijuana", "link": "https://wikipedia.org/wiki/Second_Battle_of_Tijuana"}]}, {"year": "1918", "text": "The Hammond Circus Train Wreck kills 86 and injures 127 near Hammond, Indiana.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Hammond_Circus_Train_Wreck\" class=\"mw-redirect\" title=\"Hammond Circus Train Wreck\">Hammond Circus Train Wreck</a> kills 86 and injures 127 near <a href=\"https://wikipedia.org/wiki/Hammond,_Indiana\" title=\"Hammond, Indiana\">Hammond, Indiana</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hammond_Circus_Train_Wreck\" class=\"mw-redirect\" title=\"Hammond Circus Train Wreck\">Hammond Circus Train Wreck</a> kills 86 and injures 127 near <a href=\"https://wikipedia.org/wiki/Hammond,_Indiana\" title=\"Hammond, Indiana\">Hammond, Indiana</a>.", "links": [{"title": "Hammond Circus Train Wreck", "link": "https://wikipedia.org/wiki/Hammond_Circus_Train_Wreck"}, {"title": "Hammond, Indiana", "link": "https://wikipedia.org/wiki/Hammond,_Indiana"}]}, {"year": "1922", "text": "British Army Field Marshal Sir <PERSON> is killed by the Irish Republican Army helping to spark the Irish Civil War.", "html": "1922 - British Army <a href=\"https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)\" title=\"Field marshal (United Kingdom)\">Field Marshal</a> <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a> is killed by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> helping to spark the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>.", "no_year_html": "British Army <a href=\"https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)\" title=\"Field marshal (United Kingdom)\">Field Marshal</a> <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a> is killed by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> helping to spark the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>.", "links": [{"title": "Field marshal (United Kingdom)", "link": "https://wikipedia.org/wiki/Field_marshal_(United_Kingdom)"}, {"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}, {"title": "Irish Republican Army (1919-1922)", "link": "https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)"}, {"title": "Irish Civil War", "link": "https://wikipedia.org/wiki/Irish_Civil_War"}]}, {"year": "1940", "text": "World War II: France is forced to sign the Second Compiègne armistice with Germany, in the same railroad car in which the Germans signed the Armistice in 1918.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: France is forced to sign the <a href=\"https://wikipedia.org/wiki/Armistice_of_22_June_1940\" title=\"Armistice of 22 June 1940\">Second Compiègne</a> <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a> with <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a>, in the same railroad car in which the Germans signed the Armistice in 1918.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: France is forced to sign the <a href=\"https://wikipedia.org/wiki/Armistice_of_22_June_1940\" title=\"Armistice of 22 June 1940\">Second Compiègne</a> <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a> with <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a>, in the same railroad car in which the Germans signed the Armistice in 1918.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Armistice of 22 June 1940", "link": "https://wikipedia.org/wiki/Armistice_of_22_June_1940"}, {"title": "Armistice", "link": "https://wikipedia.org/wiki/Armistice"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1941", "text": "World War II: Nazi Germany invades the Soviet Union in Operation Barbarossa.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> invades the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in <a href=\"https://wikipedia.org/wiki/Operation_Barbarossa\" title=\"Operation Barbarossa\">Operation Barbarossa</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> invades the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in <a href=\"https://wikipedia.org/wiki/Operation_Barbarossa\" title=\"Operation Barbarossa\">Operation Barbarossa</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Operation Barbarossa", "link": "https://wikipedia.org/wiki/Operation_Barbarossa"}]}, {"year": "1942", "text": "World War II: <PERSON> is promoted to Field Marshal after the Axis capture of Tobruk.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Field_Marshal_(Germany)\" class=\"mw-redirect\" title=\"Field Marshal (Germany)\">Field Marshal</a> after the <a href=\"https://wikipedia.org/wiki/Axis_capture_of_Tobruk\" title=\"Axis capture of Tobruk\">Axis capture of Tobruk</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is promoted to <a href=\"https://wikipedia.org/wiki/Field_Marshal_(Germany)\" class=\"mw-redirect\" title=\"Field Marshal (Germany)\">Field Marshal</a> after the <a href=\"https://wikipedia.org/wiki/Axis_capture_of_Tobruk\" title=\"Axis capture of Tobruk\">Axis capture of Tobruk</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Field Marshal (Germany)", "link": "https://wikipedia.org/wiki/Field_Marshal_(Germany)"}, {"title": "Axis capture of Tobruk", "link": "https://wikipedia.org/wiki/Axis_capture_of_Tobruk"}]}, {"year": "1942", "text": "The Pledge of Allegiance is formally adopted by U.S. Congress.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is formally adopted by U.S. Congress.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a> is formally adopted by U.S. Congress.", "links": [{"title": "Pledge of Allegiance (United States)", "link": "https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)"}]}, {"year": "1944", "text": "World War II: Opening day of the Soviet Union's Operation Bagration against the Army Group Centre.", "html": "1944 - World War II: Opening day of the Soviet Union's <a href=\"https://wikipedia.org/wiki/Operation_Bagration\" title=\"Operation Bagration\">Operation Bagration</a> against the <a href=\"https://wikipedia.org/wiki/Army_Group_Centre\" title=\"Army Group Centre\">Army Group Centre</a>.", "no_year_html": "World War II: Opening day of the Soviet Union's <a href=\"https://wikipedia.org/wiki/Operation_Bagration\" title=\"Operation Bagration\">Operation Bagration</a> against the <a href=\"https://wikipedia.org/wiki/Army_Group_Centre\" title=\"Army Group Centre\">Army Group Centre</a>.", "links": [{"title": "Operation Bagration", "link": "https://wikipedia.org/wiki/Operation_Bagration"}, {"title": "Army Group Centre", "link": "https://wikipedia.org/wiki/Army_Group_Centre"}]}, {"year": "1944", "text": "U.S. President <PERSON> signs into law the Servicemen's Readjustment Act of 1944, commonly known as the G.I. Bill.", "html": "1944 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law the Servicemen's Readjustment Act of 1944, commonly known as the <a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs into law the Servicemen's Readjustment Act of 1944, commonly known as the <a href=\"https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "World War II: The Battle of Okinawa comes to an end with an American flag-raising ceremony.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">Battle of Okinawa</a> comes to an end with an American flag-raising ceremony.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">Battle of Okinawa</a> comes to an end with an American flag-raising ceremony.", "links": [{"title": "Battle of Okinawa", "link": "https://wikipedia.org/wiki/Battle_of_Okinawa"}]}, {"year": "1948", "text": "The ship HMT Empire Windrush brought the first group of 802 West Indian immigrants to Tilbury, marking the start of modern immigration to the United Kingdom.", "html": "1948 - The ship <a href=\"https://wikipedia.org/wiki/HMT_Empire_Windrush\" title=\"HMT Empire Windrush\">HMT <i>Empire Windrush</i></a> brought the first group of 802 <a href=\"https://wikipedia.org/wiki/West_Indies\" title=\"West Indies\">West Indian</a> immigrants to <a href=\"https://wikipedia.org/wiki/Tilbury\" title=\"Tilbury\">Tilbury</a>, marking the start of <a href=\"https://wikipedia.org/wiki/Modern_immigration_to_the_United_Kingdom\" title=\"Modern immigration to the United Kingdom\">modern immigration to the United Kingdom</a>.", "no_year_html": "The ship <a href=\"https://wikipedia.org/wiki/HMT_Empire_Windrush\" title=\"HMT Empire Windrush\">HMT <i>Empire Windrush</i></a> brought the first group of 802 <a href=\"https://wikipedia.org/wiki/West_Indies\" title=\"West Indies\">West Indian</a> immigrants to <a href=\"https://wikipedia.org/wiki/Tilbury\" title=\"Tilbury\">Tilbury</a>, marking the start of <a href=\"https://wikipedia.org/wiki/Modern_immigration_to_the_United_Kingdom\" title=\"Modern immigration to the United Kingdom\">modern immigration to the United Kingdom</a>.", "links": [{"title": "HMT Empire Windrush", "link": "https://wikipedia.org/wiki/HMT_Empire_Windrush"}, {"title": "West Indies", "link": "https://wikipedia.org/wiki/West_Indies"}, {"title": "Tilbury", "link": "https://wikipedia.org/wiki/Tilbury"}, {"title": "Modern immigration to the United Kingdom", "link": "https://wikipedia.org/wiki/Modern_immigration_to_the_United_Kingdom"}]}, {"year": "1948", "text": "King <PERSON> formally gives up the title \"Emperor of India\", half a year after Britain actually gave up its rule of India.", "html": "1948 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\"><PERSON> VI</a> formally gives up the title \"<a href=\"https://wikipedia.org/wiki/Emperor_of_India\" title=\"Emperor of India\">Emperor of India</a>\", half a year after Britain actually gave up its rule of India.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\"><PERSON> VI</a> formally gives up the title \"<a href=\"https://wikipedia.org/wiki/Emperor_of_India\" title=\"Emperor of India\">Emperor of India</a>\", half a year after Britain actually gave up its rule of India.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emperor of India", "link": "https://wikipedia.org/wiki/Emperor_of_India"}]}, {"year": "1962", "text": "Air France Flight 117 crashes on approach to Pointe-à-Pitre International Airport in Guadeloupe, killing 112 people.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_117\" title=\"Air France Flight 117\">Air France Flight 117</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Pointe-%C3%A0-Pitre_International_Airport\" title=\"Pointe-à-Pitre International Airport\">Pointe-à-Pitre International Airport</a> in <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, killing 112 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_117\" title=\"Air France Flight 117\">Air France Flight 117</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Pointe-%C3%A0-Pitre_International_Airport\" title=\"Pointe-à-Pitre International Airport\">Pointe-à-Pitre International Airport</a> in <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, killing 112 people.", "links": [{"title": "Air France Flight 117", "link": "https://wikipedia.org/wiki/Air_France_Flight_117"}, {"title": "Pointe-à-Pitre International Airport", "link": "https://wikipedia.org/wiki/Pointe-%C3%A0-Pitre_International_Airport"}, {"title": "Guadeloupe", "link": "https://wikipedia.org/wiki/Guadeloupe"}]}, {"year": "1965", "text": "The Treaty on Basic Relations between Japan and the Republic of Korea is signed.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Treaty_on_Basic_Relations_Between_Japan_and_the_Republic_of_Korea\" title=\"Treaty on Basic Relations Between Japan and the Republic of Korea\">Treaty on Basic Relations between Japan and the Republic of Korea</a> is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_on_Basic_Relations_Between_Japan_and_the_Republic_of_Korea\" title=\"Treaty on Basic Relations Between Japan and the Republic of Korea\">Treaty on Basic Relations between Japan and the Republic of Korea</a> is signed.", "links": [{"title": "Treaty on Basic Relations Between Japan and the Republic of Korea", "link": "https://wikipedia.org/wiki/Treaty_on_Basic_Relations_Between_Japan_and_the_Republic_of_Korea"}]}, {"year": "1966", "text": "Vietnamese Buddhist activist leader <PERSON><PERSON><PERSON><PERSON> was arrested as the military junta of <PERSON><PERSON><PERSON> crushed the Buddhist Uprising.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Buddhism_in_Vietnam\" title=\"Buddhism in Vietnam\">Vietnamese Buddhist</a> activist leader <a href=\"https://wikipedia.org/wiki/Th%C3%ADch_Tr%C3%<PERSON>_<PERSON><PERSON>\" title=\"Th<PERSON><PERSON> <PERSON><PERSON><PERSON>\">Th<PERSON><PERSON><PERSON><PERSON></a> was arrested as the military junta of <a href=\"https://wikipedia.org/wiki/Nguy<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> crushed the <a href=\"https://wikipedia.org/wiki/Buddhist_Uprising\" title=\"Buddhist Uprising\">Buddhist Uprising</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddhism_in_Vietnam\" title=\"Buddhism in Vietnam\">Vietnamese Buddhist</a> activist leader <a href=\"https://wikipedia.org/wiki/Th%C3%ADch_Tr%C3%<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>h<PERSON><PERSON> <PERSON><PERSON><PERSON>\">Th<PERSON><PERSON><PERSON><PERSON></a> was arrested as the military junta of <a href=\"https://wikipedia.org/wiki/Nguy<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Nguy<PERSON>\"><PERSON><PERSON><PERSON></a> crushed the <a href=\"https://wikipedia.org/wiki/Buddhist_Uprising\" title=\"Buddhist Uprising\">Buddhist Uprising</a>.", "links": [{"title": "Buddhism in Vietnam", "link": "https://wikipedia.org/wiki/Buddhism_in_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%ADch_Tr%C3%AD_Quang"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Buddhist Uprising", "link": "https://wikipedia.org/wiki/Buddhist_Uprising"}]}, {"year": "1969", "text": "The Cuyahoga River catches fire in Cleveland, Ohio, drawing national attention to water pollution, and spurring the passing of the Clean Water Act and the creation of the Environmental Protection Agency.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Cuyahoga_River\" title=\"Cuyahoga River\">Cuyahoga River</a> catches fire in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, drawing national attention to water pollution, and spurring the passing of the <a href=\"https://wikipedia.org/wiki/Clean_Water_Act\" title=\"Clean Water Act\">Clean Water Act</a> and the creation of the <a href=\"https://wikipedia.org/wiki/Environmental_Protection_Agency\" class=\"mw-redirect\" title=\"Environmental Protection Agency\">Environmental Protection Agency</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cuyahoga_River\" title=\"Cuyahoga River\">Cuyahoga River</a> catches fire in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, drawing national attention to water pollution, and spurring the passing of the <a href=\"https://wikipedia.org/wiki/Clean_Water_Act\" title=\"Clean Water Act\">Clean Water Act</a> and the creation of the <a href=\"https://wikipedia.org/wiki/Environmental_Protection_Agency\" class=\"mw-redirect\" title=\"Environmental Protection Agency\">Environmental Protection Agency</a>.", "links": [{"title": "Cuyahoga River", "link": "https://wikipedia.org/wiki/Cuyahoga_River"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}, {"title": "Clean Water Act", "link": "https://wikipedia.org/wiki/Clean_Water_Act"}, {"title": "Environmental Protection Agency", "link": "https://wikipedia.org/wiki/Environmental_Protection_Agency"}]}, {"year": "1978", "text": "<PERSON><PERSON>, the first of Pluto's satellites to be discovered, was first seen at the United States Naval Observatory by <PERSON>.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)\" title=\"<PERSON><PERSON> (moon)\"><PERSON><PERSON></a>, the first of Pluto's satellites to be discovered, was first seen at the <a href=\"https://wikipedia.org/wiki/United_States_Naval_Observatory\" title=\"United States Naval Observatory\">United States Naval Observatory</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)\" title=\"<PERSON><PERSON> (moon)\"><PERSON><PERSON></a>, the first of Pluto's satellites to be discovered, was first seen at the <a href=\"https://wikipedia.org/wiki/United_States_Naval_Observatory\" title=\"United States Naval Observatory\">United States Naval Observatory</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(moon)"}, {"title": "United States Naval Observatory", "link": "https://wikipedia.org/wiki/United_States_Naval_Observatory"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "Former Liberal Party leader <PERSON> was acquitted of conspiracy to murder <PERSON>, who had accused <PERSON> of having a relationship with him.", "html": "1979 - Former <a href=\"https://wikipedia.org/wiki/Liberal_Party_(UK)\" title=\"Liberal Party (UK)\">Liberal Party</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> was acquitted of <a href=\"https://wikipedia.org/wiki/Thorpe_affair\" title=\"Thorpe affair\">conspiracy to murder</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had accused <PERSON> of having a relationship with him.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Liberal_Party_(UK)\" title=\"Liberal Party (UK)\">Liberal Party</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> was acquitted of <a href=\"https://wikipedia.org/wiki/Thorpe_affair\" title=\"Thorpe affair\">conspiracy to murder</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had accused <PERSON> of having a relationship with him.", "links": [{"title": "Liberal Party (UK)", "link": "https://wikipedia.org/wiki/Liberal_Party_(UK)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> affair", "link": "https://wikipedia.org/wiki/Thorpe_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "Virgin Atlantic launches with its first flight from London to Newark.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Virgin_Atlantic\" title=\"Virgin Atlantic\">Virgin Atlantic</a> launches with its first flight from <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">London</a> to <a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virgin_Atlantic\" title=\"Virgin Atlantic\">Virgin Atlantic</a> launches with its first flight from <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">London</a> to <a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark</a>.", "links": [{"title": "Virgin Atlantic", "link": "https://wikipedia.org/wiki/Virgin_Atlantic"}, {"title": "Gatwick Airport", "link": "https://wikipedia.org/wiki/Gatwick_Airport"}, {"title": "Newark Liberty International Airport", "link": "https://wikipedia.org/wiki/Newark_Liberty_International_Airport"}]}, {"year": "1986", "text": "The famous <PERSON> of God goal, scored by <PERSON> in the quarter-finals of the 1986 FIFA World Cup match between Argentina and England, ignites controversy. This was later followed by the Goal of the Century. Argentina wins 2-1 and later goes on to win the World Cup.", "html": "1986 - The famous <a href=\"https://wikipedia.org/wiki/Hand_of_God_goal\" class=\"mw-redirect\" title=\"Hand of God goal\">Hand of God goal</a>, scored by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the quarter-finals of the <a href=\"https://wikipedia.org/wiki/1986_FIFA_World_Cup\" title=\"1986 FIFA World Cup\">1986 FIFA World Cup</a> match between <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> and England, ignites controversy. This was later followed by the <a href=\"https://wikipedia.org/wiki/Goal_of_the_Century\" class=\"mw-redirect\" title=\"Goal of the Century\">Goal of the Century</a>. Argentina wins 2-1 and later goes on to win the World Cup.", "no_year_html": "The famous <a href=\"https://wikipedia.org/wiki/Hand_of_God_goal\" class=\"mw-redirect\" title=\"Hand of God goal\">Hand of God goal</a>, scored by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the quarter-finals of the <a href=\"https://wikipedia.org/wiki/1986_FIFA_World_Cup\" title=\"1986 FIFA World Cup\">1986 FIFA World Cup</a> match between <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a> and England, ignites controversy. This was later followed by the <a href=\"https://wikipedia.org/wiki/Goal_of_the_Century\" class=\"mw-redirect\" title=\"Goal of the Century\">Goal of the Century</a>. Argentina wins 2-1 and later goes on to win the World Cup.", "links": [{"title": "Hand of God goal", "link": "https://wikipedia.org/wiki/Hand_of_God_goal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}, {"title": "1986 FIFA World Cup", "link": "https://wikipedia.org/wiki/1986_FIFA_World_Cup"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Goal of the Century", "link": "https://wikipedia.org/wiki/Goal_of_the_Century"}]}, {"year": "1990", "text": "Cold War: Checkpoint Charlie is dismantled in Berlin.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Checkpoint_<PERSON>\" title=\"Checkpoint Charlie\">Checkpoint Charlie</a> is dismantled in Berlin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Checkpoint_Charlie\" title=\"Checkpoint Charlie\">Checkpoint <PERSON></a> is dismantled in Berlin.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Checkpoint Charlie", "link": "https://wikipedia.org/wiki/Checkpoint_<PERSON>"}]}, {"year": "2000", "text": "Wuhan Airlines Flight 343 is struck by lightning and crashes into Wuhan's Hanyang District, killing 49 people.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Wuhan_Airlines_Flight_343\" title=\"Wuhan Airlines Flight 343\">Wuhan Airlines Flight 343</a> is struck by lightning and crashes into <a href=\"https://wikipedia.org/wiki/Wuhan\" title=\"Wu<PERSON>\"><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Hanyang,_Wuhan\" title=\"Hanyang, Wuhan\">Hanyang District</a>, killing 49 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wuhan_Airlines_Flight_343\" title=\"Wuhan Airlines Flight 343\">Wuhan Airlines Flight 343</a> is struck by lightning and crashes into <a href=\"https://wikipedia.org/wiki/Wuhan\" title=\"Wu<PERSON>\"><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Hanyang,_Wuhan\" title=\"Hanyang, Wuhan\">Hanyang District</a>, killing 49 people.", "links": [{"title": "Wuhan Airlines Flight 343", "link": "https://wikipedia.org/wiki/Wuhan_Airlines_Flight_343"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>han"}, {"title": "Hanyang, Wuhan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Wuhan"}]}, {"year": "2002", "text": "An earthquake measuring 6.5 Mw strikes a region of northwestern Iran killing at least 261 people and injuring 1,300 others and eventually causing widespread public anger due to the slow official response.", "html": "2002 - An earthquake measuring 6.5 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">M<sub>w</sub></a> <a href=\"https://wikipedia.org/wiki/2002_Bou%27in-Zahra_earthquake\" title=\"2002 Bou'in-Zahra earthquake\">strikes</a> a region of northwestern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> killing at least 261 people and injuring 1,300 others and eventually causing widespread public anger due to the slow official response.", "no_year_html": "An earthquake measuring 6.5 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">M<sub>w</sub></a> <a href=\"https://wikipedia.org/wiki/2002_Bou%27in-Zahra_earthquake\" title=\"2002 Bou'in-Zahra earthquake\">strikes</a> a region of northwestern <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> killing at least 261 people and injuring 1,300 others and eventually causing widespread public anger due to the slow official response.", "links": [{"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "2002 Bou'in-Zahra earthquake", "link": "https://wikipedia.org/wiki/2002_Bou%27in-Zahra_earthquake"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "2009", "text": "A Washington D.C Metro train traveling southbound near Fort Totten station collides into another train waiting to enter the station. Nine people are killed in the collision (eight passengers and the train operator) and at least 80 others are injured.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/Washington_Metro\" title=\"Washington Metro\">Washington D.C Metro train</a> traveling southbound near <a href=\"https://wikipedia.org/wiki/Fort_Totten_(WMATA_station)\" class=\"mw-redirect\" title=\"Fort Totten (WMATA station)\">Fort Totten station</a> <a href=\"https://wikipedia.org/wiki/June_2009_Washington_Metro_train_collision\" title=\"June 2009 Washington Metro train collision\">collides into another train</a> waiting to enter the station. Nine people are killed in the collision (eight passengers and the train operator) and at least 80 others are injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Washington_Metro\" title=\"Washington Metro\">Washington D.C Metro train</a> traveling southbound near <a href=\"https://wikipedia.org/wiki/Fort_Totten_(WMATA_station)\" class=\"mw-redirect\" title=\"Fort Totten (WMATA station)\">Fort Totten station</a> <a href=\"https://wikipedia.org/wiki/June_2009_Washington_Metro_train_collision\" title=\"June 2009 Washington Metro train collision\">collides into another train</a> waiting to enter the station. Nine people are killed in the collision (eight passengers and the train operator) and at least 80 others are injured.", "links": [{"title": "Washington Metro", "link": "https://wikipedia.org/wiki/Washington_Metro"}, {"title": "Fort Totten (WMATA station)", "link": "https://wikipedia.org/wiki/Fort_Totten_(WMATA_station)"}, {"title": "June 2009 Washington Metro train collision", "link": "https://wikipedia.org/wiki/June_2009_Washington_Metro_train_collision"}]}, {"year": "2012", "text": "Paraguayan President <PERSON> is removed from office by impeachment and succeeded by <PERSON>.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguayan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of Fernando <PERSON>\">removed from office</a> by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> and succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguayan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>\" title=\"Impeachment of Fernando <PERSON>\">removed from office</a> by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> and succeeded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_<PERSON>"}, {"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "A Turkish Air Force McDonnell Douglas F-4 Phantom II fighter plane is shot down by the Syrian Armed Forces, killing both of the plane's pilots and worsening already-strained relations between Turkey and Syria.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Turkish_Air_Force\" title=\"Turkish Air Force\">Turkish Air Force</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a> fighter plane is <a href=\"https://wikipedia.org/wiki/2012_Turkish_F-4_Phantom_shootdown\" title=\"2012 Turkish F-4 Phantom shootdown\">shot down</a> by the <a href=\"https://wikipedia.org/wiki/Syrian_Armed_Forces\" title=\"Syrian Armed Forces\">Syrian Armed Forces</a>, killing both of the plane's pilots and worsening already-strained relations between Turkey and Syria.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Turkish_Air_Force\" title=\"Turkish Air Force\">Turkish Air Force</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a> fighter plane is <a href=\"https://wikipedia.org/wiki/2012_Turkish_F-4_Phantom_shootdown\" title=\"2012 Turkish F-4 Phantom shootdown\">shot down</a> by the <a href=\"https://wikipedia.org/wiki/Syrian_Armed_Forces\" title=\"Syrian Armed Forces\">Syrian Armed Forces</a>, killing both of the plane's pilots and worsening already-strained relations between Turkey and Syria.", "links": [{"title": "Turkish Air Force", "link": "https://wikipedia.org/wiki/Turkish_Air_Force"}, {"title": "McDonnell Douglas F-4 Phantom II", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F-4_Phantom_II"}, {"title": "2012 Turkish F-4 Phantom shootdown", "link": "https://wikipedia.org/wiki/2012_Turkish_F-4_Phantom_shootdown"}, {"title": "Syrian Armed Forces", "link": "https://wikipedia.org/wiki/Syrian_Armed_Forces"}]}, {"year": "2015", "text": "The Afghan National Assembly building is attacked by gunmen after a suicide bombing. All six of the gunmen are killed and 18 people are injured.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Afghanistan)\" title=\"National Assembly (Afghanistan)\">Afghan National Assembly</a> building is <a href=\"https://wikipedia.org/wiki/2015_Kabul_Parliament_attack\" title=\"2015 Kabul Parliament attack\">attacked by gunmen</a> after a suicide bombing. All six of the gunmen are killed and 18 people are injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Afghanistan)\" title=\"National Assembly (Afghanistan)\">Afghan National Assembly</a> building is <a href=\"https://wikipedia.org/wiki/2015_Kabul_Parliament_attack\" title=\"2015 Kabul Parliament attack\">attacked by gunmen</a> after a suicide bombing. All six of the gunmen are killed and 18 people are injured.", "links": [{"title": "National Assembly (Afghanistan)", "link": "https://wikipedia.org/wiki/National_Assembly_(Afghanistan)"}, {"title": "2015 Kabul Parliament attack", "link": "https://wikipedia.org/wiki/2015_Kabul_Parliament_attack"}]}, {"year": "2022", "text": "An earthquake occurs in eastern Afghanistan resulting in over 1,000 deaths.", "html": "2022 - An <a href=\"https://wikipedia.org/wiki/June_2022_Afghanistan_earthquake\" title=\"June 2022 Afghanistan earthquake\">earthquake</a> occurs in eastern Afghanistan resulting in over 1,000 deaths.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/June_2022_Afghanistan_earthquake\" title=\"June 2022 Afghanistan earthquake\">earthquake</a> occurs in eastern Afghanistan resulting in over 1,000 deaths.", "links": [{"title": "June 2022 Afghanistan earthquake", "link": "https://wikipedia.org/wiki/June_2022_Afghanistan_earthquake"}]}], "Births": [{"year": "662", "text": "<PERSON><PERSON>, emperor of the Tang Dynasty (d. 716)", "html": "662 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON><PERSON></a>, emperor of the Tang Dynasty (d. 716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON><PERSON></a>, emperor of the Tang Dynasty (d. 716)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_<PERSON>_Tang"}]}, {"year": "916", "text": "<PERSON><PERSON>, founder of the Emirate of Aleppo (d. 967)", "html": "916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the Emirate of Aleppo (d. 967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, founder of the Emirate of Aleppo (d. 967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1000", "text": "<PERSON>, duke of Normandy (d. 1035)", "html": "1000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Normandy\" title=\"<PERSON>, Duke of Normandy\"><PERSON></a>, duke of Normandy (d. 1035)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Normandy\" title=\"<PERSON>, Duke of Normandy\"><PERSON></a>, duke of Normandy (d. 1035)", "links": [{"title": "<PERSON>, Duke of Normandy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Normandy"}]}, {"year": "1373", "text": "<PERSON>, heiress of Poland (d. 1399)", "html": "1373 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_of_Poland#Pregnancy_and_death_(1399)\" title=\"<PERSON><PERSON><PERSON><PERSON> of Poland\"><PERSON></a>, heiress of Poland (d. 1399)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_of_Poland#Pregnancy_and_death_(1399)\" title=\"<PERSON><PERSON><PERSON><PERSON> of Poland\"><PERSON></a>, heiress of Poland (d. 1399)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Poland", "link": "https://wikipedia.org/wiki/Jadwiga_of_Poland#Pregnancy_and_death_(1399)"}]}, {"year": "1427", "text": "<PERSON><PERSON><PERSON>, Italian writer and wife of <PERSON><PERSON> (d. 1482)", "html": "1427 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer and wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Cosimo_de%27_<PERSON>\" title=\"<PERSON><PERSON> di Cosimo de' Medici\"><PERSON><PERSON> Cosimo de<PERSON> Medici</a> (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer and wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Cosimo_de%27_<PERSON>\" title=\"<PERSON><PERSON> di Cosimo de' Medici\"><PERSON><PERSON> Cosimo de<PERSON> Medici</a> (d. 1482)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> Cosimo de' Medici", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Co<PERSON><PERSON>_de%27_<PERSON>"}]}, {"year": "1450", "text": "<PERSON> of Naples, duchess of Ferrara (d. 1493)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples,_Duchess_of_Ferrara\" title=\"<PERSON> of Naples, Duchess of Ferrara\"><PERSON> of Naples</a>, duchess of Ferrara (d. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Naples,_Duchess_of_Ferrara\" title=\"<PERSON> of Naples, Duchess of Ferrara\"><PERSON> of Naples</a>, duchess of Ferrara (d. 1493)", "links": [{"title": "<PERSON> of Naples, Duchess of Ferrara", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Naples,_Duchess_of_Ferrara"}]}, {"year": "1477", "text": "<PERSON>, 2nd Marquess of Dorset, English nobleman (d. 1530)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Dorset\" title=\"<PERSON>, 2nd Marquess of Dorset\"><PERSON>, 2nd Marquess of Dorset</a>, English nobleman (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Marquess_of_Dorset\" title=\"<PERSON>, 2nd Marquess of Dorset\"><PERSON>, 2nd Marquess of Dorset</a>, English nobleman (d. 1530)", "links": [{"title": "<PERSON>, 2nd Marquess of Dorset", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Marquess_of_Dorset"}]}, {"year": "1593", "text": "Sir <PERSON>, 1st Baronet, English landowner and Parliamentarian commander (d. 1671)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English landowner and Parliamentarian commander (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English landowner and Parliamentarian commander (d. 1671)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1680", "text": "<PERSON><PERSON><PERSON>, Scottish minister and theologian (d. 1754).", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish minister and theologian (d. 1754).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish minister and theologian (d. 1754).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1684", "text": "<PERSON>, Italian violinist and composer (d. 1762)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, English author and scholar (d. 1766)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)\" title=\"<PERSON> (classical scholar)\"><PERSON></a>, English author and scholar (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)\" title=\"<PERSON> (classical scholar)\"><PERSON></a>, English author and scholar (d. 1766)", "links": [{"title": "<PERSON> (classical scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classical_scholar)"}]}, {"year": "1713", "text": "<PERSON>, English cricketer and politician (d. 1765)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\"><PERSON></a>, English cricketer and politician (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\"><PERSON></a>, English cricketer and politician (d. 1765)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, French poet and translator (d. 1813).", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and translator (d. 1813).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and translator (d. 1813).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, English lieutenant and explorer (d. 1798).", "html": "1757 - <a href=\"https://wikipedia.org/wiki/George_Vancouver\" title=\"George Vancouver\"><PERSON></a>, English lieutenant and explorer (d. 1798).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Vancouver\" title=\"George Vancouver\"><PERSON></a>, English lieutenant and explorer (d. 1798).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, French pianist and composer (d. 1817).", "html": "1763 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_M%C3%A9hul\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1817).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_M%C3%A9hul\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1817).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_M%C3%A9hul"}]}, {"year": "1767", "text": "<PERSON>, German philosopher, academic, and politician, Interior Minister of Prussia (d. 1835).", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, academic, and politician, <a href=\"https://wikipedia.org/wiki/Interior_Minister_of_Prussia\" class=\"mw-redirect\" title=\"Interior Minister of Prussia\">Interior Minister of Prussia</a> (d. 1835).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, academic, and politician, <a href=\"https://wikipedia.org/wiki/Interior_Minister_of_Prussia\" class=\"mw-redirect\" title=\"Interior Minister of Prussia\">Interior Minister of Prussia</a> (d. 1835).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Interior Minister of Prussia", "link": "https://wikipedia.org/wiki/Interior_Minister_of_Prussia"}]}, {"year": "1792", "text": "<PERSON>, Scottish engineer and businessman (d. 1865)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and businessman (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer and businessman (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Italian journalist and politician (d. 1872).", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (d. 1872).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (d. 1872).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Scottish physician and philosopher (d. 1909).", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and philosopher (d. 1909).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and philosopher (d. 1909).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, American surgeon and linguist (d. 1920)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and linguist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_Minor\" title=\"William <PERSON>\"><PERSON></a>, American surgeon and linguist (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American chess player (d. 1884)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, German-Greek architect, designed the Presidential Mansion (d. 1923)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Greek architect, designed the <a href=\"https://wikipedia.org/wiki/Presidential_Mansion,_Athens\" title=\"Presidential Mansion, Athens\">Presidential Mansion</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Greek architect, designed the <a href=\"https://wikipedia.org/wiki/Presidential_Mansion,_Athens\" title=\"Presidential Mansion, Athens\">Presidential Mansion</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Presidential Mansion, Athens", "link": "https://wikipedia.org/wiki/Presidential_Mansion,_Athens"}]}, {"year": "1844", "text": "<PERSON>, German theologian and academic (d. 1906)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, American soldier (d. 1868)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, English-New Zealand politician, 15th Prime Minister of New Zealand (d. 1906)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1850", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian scholar of Islam (d. 1921)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Ign%C3%A1c_Gold<PERSON>her\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian scholar of <a href=\"https://wikipedia.org/wiki/Islam\" title=\"Islam\">Islam</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ign%C3%A1c_Gold<PERSON>her\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian scholar of <a href=\"https://wikipedia.org/wiki/Islam\" title=\"Islam\">Islam</a> (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ign%C3%A1c_<PERSON>"}, {"title": "Islam", "link": "https://wikipedia.org/wiki/Islam"}]}, {"year": "1855", "text": "<PERSON>, Australian cricketer (d. 1931)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1931)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1856", "text": "<PERSON>, English novelist (d. 1925).", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rider_Haggard\" title=\"H. Rider Haggard\"><PERSON>ggard</a>, English novelist (d. 1925).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rider_Haggard\" title=\"H. Rider Haggard\"><PERSON>ggard</a>, English novelist (d. 1925).", "links": [{"title": "<PERSON><PERSON> Haggard", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Haggard"}]}, {"year": "1861", "text": "<PERSON>, Danish-German admiral (d. 1914)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German admiral (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German admiral (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German mathematician and academic (d. 1909)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch Politician and Prime Minister of the Netherlands (d. 1944)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Hend<PERSON><PERSON> Col<PERSON>jn\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch Politician and Prime Minister of the Netherlands (d. 1944)<a rel=\"nofollow\" class=\"external autonumber\" href=\"https://wikipedia.orghttps://www.uni-muenster.de/NiederlandeNet/nl-wissen/personen/colijn.html\">[1]</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch Politician and Prime Minister of the Netherlands (d. 1944)<a rel=\"nofollow\" class=\"external autonumber\" href=\"https://wikipedia.orghttps://www.uni-muenster.de/NiederlandeNet/nl-wissen/personen/colijn.html\">[1]</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>jn"}]}, {"year": "1871", "text": "<PERSON>, English psychologist and polymath (d. 1938)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, English psychologist and polymath (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, English psychologist and polymath (d. 1938)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Italian entomologist and academic (d. 1949)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian entomologist and academic (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian entomologist and academic (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, German philologist and scholar (d. 1958)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican archbishop (d. 1936)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Pascual_D%C3%ADaz_y_Barreto\" title=\"<PERSON><PERSON><PERSON><PERSON> y <PERSON>eto\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican archbishop (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pascual_D%C3%ADaz_y_Barreto\" title=\"<PERSON><PERSON><PERSON><PERSON> y Barreto\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican archbishop (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> y <PERSON>eto", "link": "https://wikipedia.org/wiki/Pascual_D%C3%ADaz_y_Barreto"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and jurist, 9th Chief Justice of Canada (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Thibaudeau_Rinfret\" title=\"Thibaudeau Rinfret\"><PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret</a>, Canadian lawyer and jurist, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thi<PERSON><PERSON>au_Rinfret\" title=\"Thibaudeau Rinfret\"><PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret</a>, Canadian lawyer and jurist, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Rinfret", "link": "https://wikipedia.org/wiki/Thi<PERSON><PERSON><PERSON>_Rinfret"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1880", "text": "<PERSON>, Dutch swimmer (d. 1954)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American sprinter and lawyer (d. 1949)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and lawyer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and lawyer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Slovenian engineer and chess player (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Milan_Vidmar\" title=\"Milan Vidmar\"><PERSON></a>, Slovenian engineer and chess player (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Vidmar\" title=\"Milan Vidmar\"><PERSON>id<PERSON></a>, Slovenian engineer and chess player (d. 1962)", "links": [{"title": "Milan Vidmar", "link": "https://wikipedia.org/wiki/Milan_Vidmar"}]}, {"year": "1887", "text": "<PERSON>, English biologist and academic (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American lawyer and politician, 45th Mayor of Cleveland (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cleveland\" class=\"mw-redirect\" title=\"List of mayors of Cleveland\">Mayor of Cleveland</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cleveland\" class=\"mw-redirect\" title=\"List of mayors of Cleveland\">Mayor of Cleveland</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of mayors of Cleveland", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Cleveland"}]}, {"year": "1889", "text": "<PERSON>, British solicitor, property developer, cinema magnate and Jewish community leader (d. 1980)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>(solicitor)\" title=\"<PERSON> (solicitor)\"><PERSON></a>, British solicitor, property developer, cinema magnate and Jewish community leader (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(solicitor)\" title=\"<PERSON> (solicitor)\"><PERSON></a>, British solicitor, property developer, cinema magnate and Jewish community leader (d. 1980)", "links": [{"title": "<PERSON> (solicitor)", "link": "https://wikipedia.org/wiki/<PERSON>(solicitor)"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian commander and politician, 4th Prime Minister of Estonia in exile (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian commander and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian commander and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (d. 1970)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Warma"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1891", "text": "<PERSON>, Hungarian psychoanalyst and physician (d. 1964)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian psychoanalyst and physician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian psychoanalyst and physician (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German general and pilot (d. 1945)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and pilot (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and pilot (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English archaeologist and art historian (d. 1988)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and art historian (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and art historian (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Canadian admiral (d. 1971)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian admiral (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian admiral (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American journalist and broadcaster (d. 1973)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and broadcaster (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and broadcaster (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, German-Dutch sociologist and philosopher (d. 1990)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch sociologist and philosopher (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch sociologist and philosopher (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German-Swiss soldier and author (d. 1970)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss soldier and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss soldier and author (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American engineer, invented Masking tape (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Masking_tape\" title=\"Masking tape\">Masking tape</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Masking_tape\" title=\"Masking tape\">Masking tape</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Masking tape", "link": "https://wikipedia.org/wiki/Masking_tape"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Polish economist and academic (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C5%82_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and academic (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_<PERSON><PERSON><PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, German-American abstract artist, filmmaker, and painter (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American abstract artist, filmmaker, and painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American abstract artist, filmmaker, and painter (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Finnish runner and coach (d. 1947)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish runner and coach (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish runner and coach (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actress (d. 1950)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American criminal (d. 1934)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English logician and philosopher (d. 1990)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English logician and philosopher (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English logician and philosopher (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American pilot and author (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Austrian-born American director, producer, and screenwriter (d. 2002)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born American director, producer, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born American director, producer, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Latvian writer, poet, and novelist (d. 1946)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Erik<PERSON>_%C4%80damsons\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian writer, poet, and novelist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%80damsons\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian writer, poet, and novelist (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%80damsons"}]}, {"year": "1909", "text": "<PERSON>, American dancer and choreographer (d. 2006)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON> of Spain, Spanish princess and aristocrat (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Infanta_Beatriz_of_Spain\" title=\"Infanta Beatriz of Spain\"><PERSON><PERSON><PERSON> of Spain</a>, Spanish princess and aristocrat (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Infanta_Beatriz_of_Spain\" title=\"Infanta Beatriz of Spain\"><PERSON><PERSON><PERSON> of Spain</a>, Spanish princess and aristocrat (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON> of Spain", "link": "https://wikipedia.org/wiki/Infanta_<PERSON>riz_of_Spain"}]}, {"year": "1909", "text": "<PERSON>, American producer and manager (d. 1958)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, <PERSON>, Indian-English lieutenant and mountaineer (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Indian-English lieutenant and mountaineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Indian-English lieutenant and mountaineer (d. 1998)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English singer (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German computer scientist and engineer, invented the Z3 computer (d. 1995)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3 computer</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3 computer</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Z3 (computer)", "link": "https://wikipedia.org/wiki/Z3_(computer)"}]}, {"year": "1911", "text": "<PERSON>, South African tennis player (d. 1994)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African tennis player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "Princess <PERSON> of Saxe-Coburg and Gotha (d. 1983)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Saxe-Coburg_and_Gotha\" class=\"mw-redirect\" title=\"Princess <PERSON> of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Saxe-Coburg_and_Gotha\" class=\"mw-redirect\" title=\"Princess <PERSON> of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1983)", "links": [{"title": "Princess <PERSON> of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON><PERSON>_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1912", "text": "<PERSON><PERSON>, French model and actress (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French model and actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French model and actress (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ain"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Hungarian poet and author (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_We%C3%B6res\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_We%C3%B6res\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and author (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_We%C3%B6res"}]}, {"year": "1914", "text": "<PERSON>, Chinese author and essayist (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and essayist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and essayist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Dutch conductor and composer (d. 1999)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch conductor and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch conductor and composer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American pole vaulter and coach (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter and coach (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American pianist (d. 2018)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American writer, and film and theatre critic (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, and film and theatre critic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, and film and theatre critic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American television announcer (d. 1982)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actor (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, German Jewish philosopher and Reform rabbi (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish philosopher and Reform rabbi (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish philosopher and Reform rabbi (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, English nurse, social worker, physician and writer (d. 2005)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nurse, social worker, physician and writer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English nurse, social worker, physician and writer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Singaporean politician, acting President of Singapore (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Singaporean politician, acting <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Singaporean politician, acting <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>g"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1919", "text": "<PERSON><PERSON> Champion, American dancer and choreographer (d. 1980)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Gower_Champion\" title=\"Gower Champion\">Gower Champion</a>, American dancer and choreographer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gower_Champion\" title=\"Gower Champion\">Gower Champion</a>, American dancer and choreographer (d. 1980)", "links": [{"title": "Gower Champion", "link": "https://wikipedia.org/wiki/Gower_Champion"}]}, {"year": "1919", "text": "<PERSON>, Polish social psychologist (d. 1982)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish social psychologist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish social psychologist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American basketball player and coach (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American computer scientist and engineer (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer and politician, 14th President of the Senate of the Philippines (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "1921", "text": "<PERSON>, American director and producer (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American lawyer and politician (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Croatian writer (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Radovan_Iv%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian writer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radovan_Iv%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian writer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radovan_Iv%C5%A1i%C4%87"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2019)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1922", "text": "<PERSON>, American fashion designer, founded Bill Blass Group (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Bill Blass Group\">Bill Blass Group</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Bill Blass Group\">Bill Blass Group</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group"}]}, {"year": "1922", "text": "<PERSON>, American scientist (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French-Swiss director and screenwriter (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss director and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss director and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English clinician and historian (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clinician and historian (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clinician and historian (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Canadian physicist and academic (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kerwin\"><PERSON><PERSON></a>, Canadian physicist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kerwin\"><PERSON><PERSON></a>, Canadian physicist and academic (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>win"}]}, {"year": "1926", "text": "<PERSON>, American film editor, director, producer and actor (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film editor, director, producer and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film editor, director, producer and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Lebanese politician, 48th Prime Minister of Lebanon (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese politician, 48th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}, {"year": "1927", "text": "<PERSON>, Indian-English historian and academic (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English historian and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English historian and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor and director (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English activist and laicised Roman Catholic priest (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and laicised Roman Catholic priest (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and laicised Roman Catholic priest (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Russian colonel, engineer, and astronaut (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, engineer, and astronaut (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, engineer, and astronaut (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Italian journalist and mountaineer (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and mountaineer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and mountaineer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American educator and cultural historian (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American educator and cultural historian (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Garrard Woodson\"><PERSON></a>, American educator and cultural historian (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Princess of Iran (d. 2001)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON>-Ba<PERSON>tiari\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Princess of Iran (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Princess of Iran (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>-Bakh<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Russian orientalist, historian, and academic (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian orientalist, historian, and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian orientalist, historian, and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Indian actor (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Australian actress (d. 2001)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>er\">June <PERSON></a>, Australian actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>er\"><PERSON></a>, Australian actress (d. 2001)", "links": [{"title": "June <PERSON>er", "link": "https://wikipedia.org/wiki/June_<PERSON>er"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Prunella_Scales\" title=\"Prunella Scales\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prunella_Scales\" title=\"Prunella Scales\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "Prunella Scales", "link": "https://wikipedia.org/wiki/Prunella_Scales"}]}, {"year": "1932", "text": "<PERSON>, Baron <PERSON>, English businessman and politician, Leader of the House of Lords", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Leader of the House of Lords", "link": "https://wikipedia.org/wiki/Leader_of_the_House_of_Lords"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American politician (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American physicist, author, and academic", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Spanish footballer (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Brazilian accordion player and composer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON><PERSON> Pascoal\"><PERSON><PERSON><PERSON></a>, Brazilian accordion player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pascoal\"><PERSON><PERSON><PERSON></a>, Brazilian accordion player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hermeto_Pascoal"}]}, {"year": "1937", "text": "<PERSON>, English record producer, co-founded Island Records", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English record producer, co-founded <a href=\"https://wikipedia.org/wiki/Island_Records\" title=\"Island Records\">Island Records</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English record producer, co-founded <a href=\"https://wikipedia.org/wiki/Island_Records\" title=\"Island Records\">Island Records</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Island Records", "link": "https://wikipedia.org/wiki/Island_Records"}]}, {"year": "1937", "text": "<PERSON>, Australian saxophonist and composer (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American-Canadian football player and coach (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Polish-American painter and academic (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English sociologist, psychologist, and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, psychologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, psychologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English historian and author (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Iranian director, producer, and screenwriter (d. 2016)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian director, producer, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian director, producer, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English journalist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American journalist (d. 2006)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Finnish journalist and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Terttu_<PERSON>vola\" title=\"<PERSON>rt<PERSON> Savola\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terttu_<PERSON>vola\" title=\"Terttu Savola\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician", "links": [{"title": "<PERSON>rt<PERSON>", "link": "https://wikipedia.org/wiki/Terttu_Savola"}]}, {"year": "1943", "text": "<PERSON>, Austrian actor and director", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON> <PERSON>, British-American physicist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American physicist", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer, guitarist, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German director, producer, and screenwriter (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German economist and politician, German Minister of Economics and Technology", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%BCderle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">German Minister of Economics and Technology</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%BCderle\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German economist and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)\" class=\"mw-redirect\" title=\"Federal Ministry for Economic Affairs and Energy (Germany)\">German Minister of Economics and Technology</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rainer_Br%C3%BCderle"}, {"title": "Federal Ministry for Economic Affairs and Energy (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_for_Economic_Affairs_and_Energy_(Germany)"}]}, {"year": "1946", "text": "<PERSON>, Canadian 19th General of The Salvation Army", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian 19th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian 19th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1946", "text": "<PERSON>, <PERSON>, English psychiatrist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, English psychiatrist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baroness <PERSON></a>, English psychiatrist and academic", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Cuban singer-songwriter, guitarist, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Eliades_Ochoa\" title=\"<PERSON>ades Ochoa\"><PERSON><PERSON></a>, Cuban singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ochoa\" title=\"Eliades Ochoa\"><PERSON><PERSON></a>, Cuban singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eliades_Ochoa"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Polish economist and politician, 7th Prime Minister of Poland (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish economist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1946", "text": "<PERSON>-<PERSON>, English journalist and businessman", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and businessman", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American author (d. 2006)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Octavia_<PERSON><PERSON>_<PERSON>\" title=\"Oct<PERSON>\"><PERSON><PERSON></a>, American author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oct<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Octavia <PERSON>\"><PERSON><PERSON></a>, American author (d. 2006)", "links": [{"title": "Octavia E<PERSON>", "link": "https://wikipedia.org/wiki/Octavia_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American pop-rock singer-songwriter and musician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop-rock singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop-rock singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French philosopher, anthropologist and sociologist (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, anthropologist and sociologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, anthropologist and sociologist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American basketball player (d. 1988)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Ghanaian lieutenant and politician, President of Ghana (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ghana", "link": "https://wikipedia.org/wiki/President_of_Ghana"}]}, {"year": "1948", "text": "<PERSON>, 13th Earl of Wemyss, Scottish businessman", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Wemyss\" title=\"<PERSON>, 13th Earl of Wemyss\"><PERSON>, 13th Earl of Wemyss</a>, Scottish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Wemyss\" title=\"<PERSON>, 13th Earl of Wemyss\"><PERSON>, 13th Earl of Wemyss</a>, Scottish businessman", "links": [{"title": "<PERSON>, 13th Earl of Wemyss", "link": "https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Wemyss"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American bass player (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English lawyer and judge", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>p"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Portuguese businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Filip<PERSON>_Vieira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Filip<PERSON>_V<PERSON>ira\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American academic and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Romanian lawyer and politician, 59th Prime Minister of Romania", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_N%C4%83stase\" title=\"<PERSON>\"><PERSON></a>, Romanian lawyer and politician, 59th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83stase\" title=\"<PERSON>\"><PERSON></a>, Romanian lawyer and politician, 59th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_N%C4%83stase"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1950", "text": "<PERSON>, Australian rugby league player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, former West Virginia State Treasurer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/West_Virginia_State_Treasurer\" title=\"West Virginia State Treasurer\">West Virginia State Treasurer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/West_Virginia_State_Treasurer\" title=\"West Virginia State Treasurer\">West Virginia State Treasurer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "West Virginia State Treasurer", "link": "https://wikipedia.org/wiki/West_Virginia_State_Treasurer"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Lithuanian lawyer and politician (d. 2009)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian lawyer and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian lawyer and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zen<PERSON><PERSON>_<PERSON>as"}]}, {"year": "1950", "text": "<PERSON>, Indian actor (d. 2017)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, British cyclist and sports administrator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cyclist and sports administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cyclist and sports administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American bass player (d. 2015)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English painter and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Humphrey_<PERSON>\" title=\"Humphrey Ocean\"><PERSON></a>, English painter and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Humphrey_<PERSON>\" title=\"Humphrey Ocean\"><PERSON></a>, English painter and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Humphrey_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Malaysian football player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Dutch cardinal", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON><PERSON><PERSON>\" title=\"Wim <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>i<PERSON>\" title=\"Wim <PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON>jk"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Italian mathematician and academic (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mathematician and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>per\" title=\"<PERSON><PERSON> Lauper\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>per\" title=\"<PERSON><PERSON> Lauper\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ndi_Lauper"}]}, {"year": "1953", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American comedian and actor (d. 1977)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Welsh singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Green_Gartside\" title=\"Green Gartside\"><PERSON></a>, Welsh singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Green_Gartside\" title=\"Green Gartside\"><PERSON></a>, Welsh singer-songwriter and guitarist", "links": [{"title": "Green Gartside", "link": "https://wikipedia.org/wiki/Green_Gartside"}]}, {"year": "1955", "text": "<PERSON>, British academic and educator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Alfons <PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Pakistani agriculturist and politician, 25th Pakistani Minister of Foreign Affairs", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani agriculturist and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Pakistan)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Pakistan)\">Pakistani Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani agriculturist and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Pakistan)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Pakistan)\">Pakistani Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Pakistan)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Pakistan)"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German footballer, manager, and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer, manager, and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer, manager, and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish bass player and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bass player and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English journalist and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian bass player, songwriter, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(English_footballer)"}]}, {"year": "1957", "text": "<PERSON>, English geneticist and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geneticist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Mexican pop singer and actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Banquells\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican pop singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Banquells\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican pop singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roc%C3%ADo_Banquells"}]}, {"year": "1958", "text": "<PERSON>, American actor, director, producer and writer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Irish jockey", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American lawyer and environmentalist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and environmentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and environmentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"Marg<PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>grit <PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Hong Kong actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish musician and singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 61st <PERSON><PERSON><PERSON><PERSON>", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 61st <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 61st <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1963", "text": "<PERSON>, Canadian-American wrestler (d. 2006)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Anderson\" title=\"<PERSON> Anderson\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Anderson\" title=\"<PERSON> Anderson\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> Anderson", "link": "https://wikipedia.org/wiki/<PERSON>_Anderson"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American author and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, German director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Morav%C4%8D%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Morav%C4%8D%C3%ADk\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%BDubom%C3%ADr_Morav%C4%8D%C3%ADk"}]}, {"year": "1966", "text": "<PERSON>, English racing driver (d. 2005)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON> (co-driver)\"><PERSON></a>, English racing driver (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)\" title=\"<PERSON> (co-driver)\"><PERSON></a>, English racing driver (d. 2005)", "links": [{"title": "<PERSON> (co-driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(co-driver)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, French actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian cyclist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Korean novelist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean novelist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ain<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English rugby player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress and comedian", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian jockey", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Icelandic politician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Eyd%C3%ADs_%C3%81sbj%C3%B6rnsd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eyd%C3%ADs_%C3%81sbj%C3%B6rnsd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eyd%C3%ADs_%C3%81sbj%C3%B6rnsd%C3%B3ttir"}]}, {"year": "1973", "text": "<PERSON>, American radio and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, British politician (d. 2016)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Indian actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Indian actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Estonian academic and politician, 28th Estonian Minister of Defence", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Reinsalu\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>al<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian academic and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian academic and politician, 28th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)\" title=\"Minister of Defence (Estonia)\">Estonian Minister of Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Urmas_Reinsalu"}, {"title": "Minister of Defence (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Estonia)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English racing driver (d. 2011)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American speed skater", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cheek\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cheek\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eek"}]}, {"year": "1979", "text": "<PERSON>, French cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Hong Kong-Australian actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, New Zealand rugby player (d. 2017)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby player (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Colombian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Aquivaldo_Mosquera\" title=\"Aquivaldo Mosquera\"><PERSON><PERSON><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aquivaldo_Mosquera\" title=\"Aquivaldo Mosquera\"><PERSON><PERSON><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aquivaldo_Mosquera"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ola"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Portuguese actress and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actress and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Estonian rower", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Raja"}]}, {"year": "1984", "text": "<PERSON>, American golfer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Iv%C3%A1n_Mart%C3%ADnez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Iv%C3%A1n_Mart%C3%ADnez\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Iv%C3%A1n_Mart%C3%ADnez"}]}, {"year": "1984", "text": "<PERSON>, Jamaican cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Serbian tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tipsarevi%C4%87"}]}, {"year": "1985", "text": "<PERSON>, New Zealand rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1987", "text": "<PERSON>, South Korean actor, singer, model, creative director and businessman", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor, singer, model, creative director and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor, singer, model, creative director and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Ukrainian-Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Australian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Israeli basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Congolese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South Korean singer-songwriter and actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wa"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese sumo wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, British actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, German footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Brazilian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian-English actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "207 BC", "text": "<PERSON><PERSON><PERSON><PERSON>, Carthaginian general in the Second Punic War (b. 245 BC)", "html": "207 BC - 207 BC - <a href=\"https://wikipedia.org/wiki/Hasdrubal_Barca\" title=\"Hasdrubal Barca\"><PERSON><PERSON><PERSON>l Barca</a>, <a href=\"https://wikipedia.org/wiki/Ancient_Carthage\" title=\"Ancient Carthage\">Carthaginian</a> general in the <a href=\"https://wikipedia.org/wiki/Second_Punic_War\" title=\"Second Punic War\">Second Punic War</a> (b. 245 BC)", "no_year_html": "207 BC - <a href=\"https://wikipedia.org/wiki/Hasdrubal_Barca\" title=\"Hasdrubal Barca\"><PERSON><PERSON><PERSON><PERSON> Barca</a>, <a href=\"https://wikipedia.org/wiki/Ancient_Carthage\" title=\"Ancient Carthage\">Carthaginian</a> general in the <a href=\"https://wikipedia.org/wiki/Second_Punic_War\" title=\"Second Punic War\">Second Punic War</a> (b. 245 BC)", "links": [{"title": "Hasdrubal Barca", "link": "https://wikipedia.org/wiki/Hasdrubal_Barca"}, {"title": "Ancient Carthage", "link": "https://wikipedia.org/wiki/Ancient_Carthage"}, {"title": "Second Punic War", "link": "https://wikipedia.org/wiki/Second_Punic_War"}]}, {"year": "431", "text": "<PERSON><PERSON> of Nola, Christian bishop and poet (b. 354)", "html": "431 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Nola\" title=\"<PERSON><PERSON> of Nola\"><PERSON><PERSON> of Nola</a>, Christian bishop and poet (b. 354)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Nola\" title=\"<PERSON><PERSON> of Nola\"><PERSON><PERSON> of Nola</a>, Christian bishop and poet (b. 354)", "links": [{"title": "<PERSON><PERSON> of Nola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Nola"}]}, {"year": "910", "text": "<PERSON><PERSON><PERSON>, Frankish nobleman", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON></a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON></a>, Frankish nobleman", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON>_of_Lorraine"}]}, {"year": "910", "text": "<PERSON>, Frankish nobleman", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Metz\" title=\"<PERSON> of Metz\"><PERSON></a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Metz\" title=\"<PERSON> of Metz\"><PERSON></a>, Frankish nobleman", "links": [{"title": "<PERSON> of Metz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Metz"}]}, {"year": "947", "text": "<PERSON><PERSON>, king of Wuyue (b. 928)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON>yue\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yue"}]}, {"year": "1017", "text": "<PERSON>, Byzantine general", "html": "1017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1101", "text": "<PERSON> of Sicily, Norman nobleman (b. 1031)", "html": "1101 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>, Norman nobleman (b. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a>, Norman nobleman (b. 1031)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1276", "text": "<PERSON>, pope of the Catholic Church (b. 1225)", "html": "1276 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_V\" title=\"Pope Innocent V\"><PERSON> V</a>, pope of the Catholic Church (b. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_V\" title=\"Pope Innocent V\"><PERSON></a>, pope of the Catholic Church (b. 1225)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Innocent_V"}]}, {"year": "1343", "text": "<PERSON><PERSON><PERSON>, Count of Savoy (b. 1291)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Savoy\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Savoy\"><PERSON><PERSON><PERSON>, Count of Savoy</a> (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Savoy\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Count of Savoy\"><PERSON><PERSON><PERSON>, Count of Savoy</a> (b. 1291)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Savoy"}]}, {"year": "1429", "text": "<PERSON><PERSON><PERSON><PERSON>, Persian astronomer and mathematician (b. 1380)", "html": "1429 - <a href=\"https://wikipedia.org/wiki/Jamsh%C4%ABd_al-K%C4%81sh%C4%AB\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Persian astronomer and mathematician (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jamsh%C4%ABd_al-K%C4%81sh%C4%AB\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Persian astronomer and mathematician (b. 1380)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jamsh%C4%ABd_al-K%C4%81sh%C4%AB"}]}, {"year": "1521", "text": "<PERSON>, Italian politician, 76th <PERSON><PERSON> of Venice (b. 1436)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 76th <a href=\"https://wikipedia.org/wiki/Doge_of_Venice\" title=\"<PERSON><PERSON> of Venice\"><PERSON><PERSON> of Venice</a> (b. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, 76th <a href=\"https://wikipedia.org/wiki/<PERSON>e_of_Venice\" title=\"<PERSON><PERSON> of Venice\"><PERSON><PERSON> of Venice</a> (b. 1436)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> of Venice", "link": "https://wikipedia.org/wiki/Doge_of_Venice"}]}, {"year": "1535", "text": "<PERSON>, English bishop and saint (b. 1469)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and saint (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and saint (b. 1469)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON>, English judge and politician, Chief Justice of Chester (b. 1570)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Chester\" class=\"mw-redirect\" title=\"Chief Justice of Chester\">Chief Justice of Chester</a> (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English judge and politician, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Chester\" class=\"mw-redirect\" title=\"Chief Justice of Chester\">Chief Justice of Chester</a> (b. 1570)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Chester", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Chester"}]}, {"year": "1634", "text": "<PERSON>, Austrian field marshal (b. 1588)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, Anglo-Welsh poet (b. 1631)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Welsh poet (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Welsh poet (b. 1631)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1699", "text": "<PERSON>, English merchant, economist, and politician (b. 1630)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Child\"><PERSON></a>, English merchant, economist, and politician (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Child\"><PERSON></a>, English merchant, economist, and politician (b. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON>, Welsh minister and author (b. 1662)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh minister and author (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh minister and author (b. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Maltese priest and painter (b. 1696)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese priest and painter (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese priest and painter (b. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Norwegian road manager, land owner, and mill owner (b. 1760)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian road manager, land owner, and mill owner (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian road manager, land owner, and mill owner (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON> <PERSON>, American religious leader (b. 1801)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Heber_<PERSON>_Kimball\" title=\"Heber C<PERSON> Kimball\"><PERSON><PERSON> <PERSON></a>, American religious leader (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heber_<PERSON>_Kimball\" title=\"Heber C. Kimball\"><PERSON><PERSON> <PERSON></a>, American religious leader (b. 1801)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heber_C._Kimball"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian general (b. 1792)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>ado\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian general (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian general (b. 1792)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, English chess player (b. 1810)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, French mathematician and academic (b. 1819)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian archbishop and missionary (b. 1823)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and missionary (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian archbishop and missionary (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1905", "text": "<PERSON>, American colonel and politician, 9th Governor of Texas (b. 1815)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Texas\" class=\"mw-redirect\" title=\"List of Governors of Texas\">Governor of Texas</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Texas\" class=\"mw-redirect\" title=\"List of Governors of Texas\">Governor of Texas</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Governors of Texas", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Texas"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Romanian poet and translator (b. 1875)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/%C8%98tefan_Octavian_Iosif\" title=\"<PERSON><PERSON><PERSON> Octavian I<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian poet and translator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C8%98te<PERSON>_Octavian_Iosif\" title=\"Ș<PERSON>fan Octavian Iosif\"><PERSON><PERSON><PERSON></a>, Romanian poet and translator (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C8%98te<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German mathematician and academic (b. 1849)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON> <PERSON><PERSON>, American illustrator and painter (b. 1851)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Frost\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and painter (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Frost\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and painter (b. 1851)", "links": [{"title": "A<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French politician, 9th President of France (b. 1841)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, French politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Armand_Falli%C3%A8res"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1933", "text": "<PERSON>, English racing driver and lieutenant (b. 1896)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and lieutenant (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and lieutenant (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Polish historian and diplomat (b. 1866)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and diplomat (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish historian and diplomat (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, German-Austrian physicist and philosopher (b. 1882)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian physicist and philosopher (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian physicist and philosopher (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian poet and author (b. 1876)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian poet and author (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian poet and author (b. 1876)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian cricketer and sportscaster (b. 1873)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, German priest and activist (b. 1891)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German priest and activist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German priest and activist (b. 1891)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Japanese general (b. 1895)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ch%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ch%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isamu_Ch%C5%8D"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English poet, short story writer and novelist (b. 1873)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, short story writer and novelist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, short story writer and novelist (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German educator and politician, 8th Minister-President of Thuringia (b. 1895)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Thuringia\" class=\"mw-redirect\" title=\"List of Ministers-President of Thuringia\">Minister-President of Thuringia</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Thuringia\" class=\"mw-redirect\" title=\"List of Ministers-President of Thuringia\">Minister-President of Thuringia</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ministers-President of Thuringia", "link": "https://wikipedia.org/wiki/List_of_Ministers-President_of_Thuringia"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Dutch journalist and author (b. 1904)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>vank\" title=\"<PERSON>van<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vank\" title=\"<PERSON>van<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch journalist and author (b. 1904)", "links": [{"title": "Havank", "link": "https://wikipedia.org/wiki/Havank"}]}, {"year": "1965", "text": "<PERSON>, American screenwriter and producer (b. 1902)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, American hurdler (b. 1883)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>er\" title=\"Thad<PERSON><PERSON>deler\"><PERSON><PERSON><PERSON><PERSON></a>, American hurdler (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>er\" title=\"Thad<PERSON><PERSON>deler\"><PERSON><PERSON><PERSON><PERSON></a>, American hurdler (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1969", "text": "<PERSON>, American actress and singer (b. 1922)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Đặng <PERSON><PERSON><PERSON><PERSON>, Vietnamese surgeon and author (b. 1942)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m\" title=\"Đặng Thùy Trâm\">Đặng Thù<PERSON> Trâm</a>, Vietnamese surgeon and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m\" title=\"Đặng Thùy Trâm\">Đặng Thùy Trâm</a>, Vietnamese surgeon and author (b. 1942)", "links": [{"title": "Đặng <PERSON><PERSON><PERSON><PERSON>âm", "link": "https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m"}]}, {"year": "1974", "text": "<PERSON>, French composer and educator (b. 1892)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French director and screenwriter (b. 1908)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1952)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Monégasque race car driver (b. 1899)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Monégasque race car driver (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Monégasque race car driver (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, British solicitor, property developer, cinema magnate and Jewish community leader (b. 1889)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>(solicitor)\" title=\"<PERSON> (solicitor)\"><PERSON></a>, British solicitor, property developer, cinema magnate and Jewish community leader (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(solicitor)\" title=\"<PERSON> (solicitor)\"><PERSON></a>, British solicitor, property developer, cinema magnate and Jewish community leader (b. 1889)", "links": [{"title": "<PERSON> (solicitor)", "link": "https://wikipedia.org/wiki/<PERSON>(solicitor)"}]}, {"year": "1984", "text": "<PERSON>, American director, producer, and screenwriter (b. 1909)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and dancer (b. 1899)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer and actor (b. 1916)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dennis Day\"><PERSON></a>, American singer and actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dennis Day\"><PERSON></a>, American singer and actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1993", "text": "<PERSON>, American educator, 37th First Lady of the United States (b. 1912)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, 37th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, 37th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Russian poet and songwriter (b. 1931)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and songwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American sculptor and author (b. 1927)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>Hansen"}]}, {"year": "1997", "text": "<PERSON>, Swedish singer-songwriter (b. 1956)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ted_G%C3%A4<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Canadian journalist and politician (b. 1919)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Belarusian war novelist (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Vasil_Byka%C5%AD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian war novelist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasil_Byka%C5%AD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian war novelist (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasil_Byka%C5%AD"}]}, {"year": "2004", "text": "<PERSON>, American computer scientist and engineer (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American poet and author (b. 1990)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (b. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Dutch field hockey player (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Russian neuroscientist and psychologist (b. 1924)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian neuroscientist and psychologist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian neuroscientist and psychologist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American comedian, actor, and author (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American actress and dancer (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer and coach (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Co%C5%9Fkun_%C3%96zar%C4%B1\" title=\"Coşkun Özarı\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Co%C5%9Fkun_%C3%96zar%C4%B1\" title=\"Coşkun Özarı\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach (b. 1931)", "links": [{"title": "Coşkun Özarı", "link": "https://wikipedia.org/wiki/Co%C5%9Fkun_%C3%96zar%C4%B1"}]}, {"year": "2012", "text": "<PERSON>, Spanish actor and producer (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Danish architect, designed the Copenhagen Opera House (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Copenhagen_Opera_House\" title=\"Copenhagen Opera House\">Copenhagen Opera House</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish architect, designed the <a href=\"https://wikipedia.org/wiki/Copenhagen_Opera_House\" title=\"Copenhagen Opera House\">Copenhagen Opera House</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Copenhagen Opera House", "link": "https://wikipedia.org/wiki/Copenhagen_Opera_House"}]}, {"year": "2013", "text": "<PERSON>, Danish race car driver (b. 1978)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish race car driver (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish race car driver (b. 1978)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Lebanese-American author and academic (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American author and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American author and academic (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>mi"}]}, {"year": "2014", "text": "<PERSON>, Indian director and producer (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian director and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American composer and conductor (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Japanese newscaster and actress (b. 1982)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Japanese newscaster and actress (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Japanese newscaster and actress (b. 1982)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Botswanan politician (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Botswanan politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Botswanan politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "2018", "text": "<PERSON>, American musician (b. 1964)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American racetrack promoter (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American racetrack promoter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American racetrack promoter (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, French anthropologist (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French anthropologist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American Nobel economist (b. 1927)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Nobel economist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Nobel economist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}