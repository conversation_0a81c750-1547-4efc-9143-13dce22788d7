{"date": "March 14", "url": "https://wikipedia.org/wiki/March_14", "data": {"Events": [{"year": "1074", "text": "Battle of Mogyoród: Dukes <PERSON><PERSON> and <PERSON><PERSON><PERSON> defeat their cousin <PERSON>, King of Hungary, forcing him to flee to Hungary's western borderland.", "html": "1074 - <a href=\"https://wikipedia.org/wiki/Battle_of_Mogyor%C3%B3d\" title=\"Battle of Mogyoród\">Battle of Mogyoród</a>: Dukes <a href=\"https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary\" title=\"Géza I of Hungary\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ladislaus_I_of_Hungary\" title=\"Ladislaus I of Hungary\"><PERSON><PERSON><PERSON></a> defeat their cousin <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Hungary\" title=\"<PERSON>, King of Hungary\"><PERSON>, King of Hungary</a>, forcing him to flee to Hungary's western borderland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Mogyor%C3%B3d\" title=\"Battle of Mogyoród\">Battle of Mogyoród</a>: Dukes <a href=\"https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary\" title=\"Géza I of Hungary\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ladislaus_I_of_Hungary\" title=\"Ladislaus I of Hungary\"><PERSON><PERSON><PERSON></a> defeat their cousin <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Hungary\" title=\"<PERSON>, King of Hungary\"><PERSON>, King of Hungary</a>, forcing him to flee to Hungary's western borderland.", "links": [{"title": "Battle of Mogyoród", "link": "https://wikipedia.org/wiki/Battle_of_Mogyor%C3%B3d"}, {"title": "Géza I of Hungary", "link": "https://wikipedia.org/wiki/G%C3%A9za_I_of_Hungary"}, {"title": "<PERSON><PERSON><PERSON> I of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Hungary"}, {"title": "<PERSON>, King of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Hungary"}]}, {"year": "1590", "text": "Battle of Ivry: <PERSON> of Navarre and the Huguenots defeat the forces of the Catholic League under <PERSON>, Duke of Mayenne, during the French Wars of Religion.", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Battle_of_Ivry\" title=\"Battle of Ivry\">Battle of Ivry</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of Navarre</a> and the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> defeat the forces of the <a href=\"https://wikipedia.org/wiki/Catholic_League_(French)\" title=\"Catholic League (French)\">Catholic League</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mayenne\" title=\"<PERSON>, Duke of Mayenne\"><PERSON>, Duke of Mayenne</a>, during the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Ivry\" title=\"Battle of Ivry\">Battle of Ivry</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> of Navarre</a> and the <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Huguenot\">Huguenots</a> defeat the forces of the <a href=\"https://wikipedia.org/wiki/Catholic_League_(French)\" title=\"Catholic League (French)\">Catholic League</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Mayenne\" title=\"<PERSON>, Duke of Mayenne\"><PERSON>, Duke of Mayenne</a>, during the <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>.", "links": [{"title": "Battle of Ivry", "link": "https://wikipedia.org/wiki/Battle_of_Ivry"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "Catholic League (French)", "link": "https://wikipedia.org/wiki/Catholic_League_(French)"}, {"title": "<PERSON>, Duke of Mayenne", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_May<PERSON>"}, {"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}]}, {"year": "1647", "text": "Thirty Years' War: Bavaria, Cologne, France and Sweden sign the Truce of Ulm.", "html": "1647 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>, <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> sign the <a href=\"https://wikipedia.org/wiki/Truce_of_Ulm_(1647)\" title=\"Truce of Ulm (1647)\">Truce of Ulm</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavaria</a>, <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> and <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> sign the <a href=\"https://wikipedia.org/wiki/Truce_of_Ulm_(1647)\" title=\"Truce of Ulm (1647)\">Truce of Ulm</a>.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "Bavaria", "link": "https://wikipedia.org/wiki/Bavaria"}, {"title": "Cologne", "link": "https://wikipedia.org/wiki/Cologne"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}, {"title": "<PERSON><PERSON><PERSON> of Ulm (1647)", "link": "https://wikipedia.org/wiki/Truce_of_Ulm_(1647)"}]}, {"year": "1663", "text": "According to his own account, <PERSON> completes his book Experimenta Nova (ut vocantur) Magdeburgica de Vacuo Spatio, detailing his experiments on vacuum and his discovery of electrostatic repulsion.", "html": "1663 - According to his own account, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes his book <i>Experimenta Nova (ut vocantur) Magdeburgica de Vacuo <PERSON></i>, detailing his experiments on vacuum and his discovery of <a href=\"https://wikipedia.org/wiki/Electrostatic_repulsion\" class=\"mw-redirect\" title=\"Electrostatic repulsion\">electrostatic repulsion</a>.", "no_year_html": "According to his own account, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes his book <i>Experimenta Nova (ut vocantur) Magdeburgica de Vacuo <PERSON>o</i>, detailing his experiments on vacuum and his discovery of <a href=\"https://wikipedia.org/wiki/Electrostatic_repulsion\" class=\"mw-redirect\" title=\"Electrostatic repulsion\">electrostatic repulsion</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Electrostatic repulsion", "link": "https://wikipedia.org/wiki/Electrostatic_repulsion"}]}, {"year": "1674", "text": "The Third Anglo-Dutch War: The Battle of Ronas Voe results in the Dutch East India Company ship Wapen van Rotterdam being captured with a death toll of up to 300 Dutch crew and soldiers.", "html": "1674 - The <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ronas_Voe\" title=\"Battle of Ronas Voe\">Battle of Ronas Voe</a> results in the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> ship <i><PERSON>apen van Rotterdam</i> being captured with a death toll of up to 300 Dutch crew and soldiers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Ronas_Voe\" title=\"Battle of Ronas Voe\">Battle of Ronas Voe</a> results in the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> ship <i><PERSON><PERSON><PERSON> van Rotterdam</i> being captured with a death toll of up to 300 Dutch crew and soldiers.", "links": [{"title": "Third Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Third_Anglo-Dutch_War"}, {"title": "Battle of Ronas Voe", "link": "https://wikipedia.org/wiki/Battle_of_Ronas_Voe"}, {"title": "Dutch East India Company", "link": "https://wikipedia.org/wiki/Dutch_East_India_Company"}]}, {"year": "1757", "text": "Admiral Sir <PERSON> is executed by firing squad aboard HMS Monarch for breach of the Articles of War.", "html": "1757 - Admiral Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Executed_by_firing_squad\" class=\"mw-redirect\" title=\"Executed by firing squad\">executed by firing squad</a> aboard <a href=\"https://wikipedia.org/wiki/HMS_Monarch_(1747)\" title=\"HMS Monarch (1747)\">HMS <i>Monarch</i></a> for breach of the <a href=\"https://wikipedia.org/wiki/Articles_of_War\" title=\"Articles of War\">Articles of War</a>.", "no_year_html": "Admiral Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Executed_by_firing_squad\" class=\"mw-redirect\" title=\"Executed by firing squad\">executed by firing squad</a> aboard <a href=\"https://wikipedia.org/wiki/HMS_Monarch_(1747)\" title=\"HMS Monarch (1747)\">HMS <i>Monarch</i></a> for breach of the <a href=\"https://wikipedia.org/wiki/Articles_of_War\" title=\"Articles of War\">Articles of War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Executed by firing squad", "link": "https://wikipedia.org/wiki/Executed_by_firing_squad"}, {"title": "HMS Monarch (1747)", "link": "https://wikipedia.org/wiki/HMS_Monarch_(1747)"}, {"title": "Articles of War", "link": "https://wikipedia.org/wiki/Articles_of_War"}]}, {"year": "1780", "text": "American Revolutionary War: Spanish forces capture Fort Charlotte in Mobile, Alabama, the last British frontier post capable of threatening New Orleans.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Charlotte\" title=\"Battle of Fort Charlotte\">capture Fort Charlotte</a> in <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, the last British frontier post capable of threatening <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Charlotte\" title=\"Battle of Fort Charlotte\">capture Fort Charlotte</a> in <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, the last British frontier post capable of threatening <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Battle of Fort Charlotte", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Charlotte"}, {"title": "Mobile, Alabama", "link": "https://wikipedia.org/wiki/Mobile,_Alabama"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1794", "text": "<PERSON> is granted a patent for the cotton gin.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for the <a href=\"https://wikipedia.org/wiki/Cotton_gin\" title=\"Cotton gin\">cotton gin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for the <a href=\"https://wikipedia.org/wiki/Cotton_gin\" title=\"Cotton gin\">cotton gin</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Cotton gin", "link": "https://wikipedia.org/wiki/Cotton_gin"}]}, {"year": "1864", "text": "<PERSON><PERSON>'s Petite messe solennelle is first performed, by twelve singers, two pianists and a harmonium player in a mansion in Paris.", "html": "1864 - <PERSON><PERSON>'s <i><a href=\"https://wikipedia.org/wiki/Petite_messe_solennelle\" title=\"Petite messe solennelle\">Petite messe solennelle</a></i> is first performed, by twelve singers, two pianists and a <a href=\"https://wikipedia.org/wiki/Pump_organ\" title=\"Pump organ\">harmonium</a> player in a mansion in Paris.", "no_year_html": "<PERSON><PERSON>'s <i><a href=\"https://wikipedia.org/wiki/Petite_messe_solennelle\" title=\"Petite messe solennelle\">Petite messe solennelle</a></i> is first performed, by twelve singers, two pianists and a <a href=\"https://wikipedia.org/wiki/Pump_organ\" title=\"Pump organ\">harmonium</a> player in a mansion in Paris.", "links": [{"title": "Petite messe solennelle", "link": "https://wikipedia.org/wiki/Petite_messe_solennelle"}, {"title": "Pump organ", "link": "https://wikipedia.org/wiki/Pump_organ"}]}, {"year": "1885", "text": "The Mikado, a light opera by <PERSON><PERSON> <PERSON><PERSON> and <PERSON>, receives its first public performance at the Savoy Theatre in London.", "html": "1885 - <i><a href=\"https://wikipedia.org/wiki/The_Mikado\" title=\"The Mikado\">The Mikado</a></i>, a light opera by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, receives its first public performance at the Savoy Theatre in London.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Mikado\" title=\"The Mikado\">The Mikado</a></i>, a light opera by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, receives its first public performance at the Savoy Theatre in London.", "links": [{"title": "The Mikado", "link": "https://wikipedia.org/wiki/The_Mikado"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "The Gold Standard Act is ratified, placing the United States currency on the gold standard.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Gold_Standard_Act\" title=\"Gold Standard Act\">Gold Standard Act</a> is ratified, placing the <a href=\"https://wikipedia.org/wiki/United_States_currency\" class=\"mw-redirect\" title=\"United States currency\">United States currency</a> on the <a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">gold standard</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gold_Standard_Act\" title=\"Gold Standard Act\">Gold Standard Act</a> is ratified, placing the <a href=\"https://wikipedia.org/wiki/United_States_currency\" class=\"mw-redirect\" title=\"United States currency\">United States currency</a> on the <a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">gold standard</a>.", "links": [{"title": "Gold Standard Act", "link": "https://wikipedia.org/wiki/Gold_Standard_Act"}, {"title": "United States currency", "link": "https://wikipedia.org/wiki/United_States_currency"}, {"title": "Gold standard", "link": "https://wikipedia.org/wiki/Gold_standard"}]}, {"year": "1901", "text": "Utah governor <PERSON><PERSON> vetoes a bill that would have eased restriction on polygamy.", "html": "1901 - Utah governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> vetoes a bill that would have eased restriction on polygamy.", "no_year_html": "Utah governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> vetoes a bill that would have eased restriction on polygamy.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "Pelican Island National Wildlife Refuge, the first national wildlife refuge in the US, is established by President <PERSON>.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Pelican_Island_National_Wildlife_Refuge\" title=\"Pelican Island National Wildlife Refuge\">Pelican Island National Wildlife Refuge</a>, the first national wildlife refuge in the US, is established by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pelican_Island_National_Wildlife_Refuge\" title=\"Pelican Island National Wildlife Refuge\">Pelican Island National Wildlife Refuge</a>, the first national wildlife refuge in the US, is established by President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Pelican Island National Wildlife Refuge", "link": "https://wikipedia.org/wiki/Pelican_Island_National_Wildlife_Refuge"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "In the second of the 1920 Schleswig plebiscites, about 80% of the population in Zone II votes to remain part of Weimar Germany.", "html": "1920 - In the second of the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>, about 80% of the population in Zone II votes to remain part of <a href=\"https://wikipedia.org/wiki/Weimar_Germany\" class=\"mw-redirect\" title=\"Weimar Germany\">Weimar Germany</a>.", "no_year_html": "In the second of the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>, about 80% of the population in Zone II votes to remain part of <a href=\"https://wikipedia.org/wiki/Weimar_Germany\" class=\"mw-redirect\" title=\"Weimar Germany\">Weimar Germany</a>.", "links": [{"title": "1920 Schleswig plebiscites", "link": "https://wikipedia.org/wiki/1920_Schleswig_plebiscites"}, {"title": "Weimar Germany", "link": "https://wikipedia.org/wiki/Weimar_Germany"}]}, {"year": "1921", "text": "Six members of a group of Irish Republican Army activists known as the Forgotten Ten, are hanged in Dublin's Mountjoy Prison.", "html": "1921 - Six members of a group of <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> activists known as the <a href=\"https://wikipedia.org/wiki/Forgotten_Ten\" title=\"Forgotten Ten\">Forgotten Ten</a>, are hanged in Dublin's <a href=\"https://wikipedia.org/wiki/Mountjoy_Prison\" title=\"Mountjoy Prison\">Mountjoy Prison</a>.", "no_year_html": "Six members of a group of <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> activists known as the <a href=\"https://wikipedia.org/wiki/Forgotten_Ten\" title=\"Forgotten Ten\">Forgotten Ten</a>, are hanged in Dublin's <a href=\"https://wikipedia.org/wiki/Mountjoy_Prison\" title=\"Mountjoy Prison\">Mountjoy Prison</a>.", "links": [{"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "Forgotten Ten", "link": "https://wikipedia.org/wiki/Forgotten_Ten"}, {"title": "Mountjoy Prison", "link": "https://wikipedia.org/wiki/Mountjoy_Prison"}]}, {"year": "1923", "text": "<PERSON> and three other members of the Irish Republican Army are executed by Irish Free State forces.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other members of the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> are executed by <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other members of the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> are executed by <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> forces.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Army (1919-1922)", "link": "https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}]}, {"year": "1926", "text": "The El Virilla train accident, Costa Rica, kills 248 people and wounds another 93 when a train falls off a bridge over the Río Virilla between Heredia and Tibás.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/El_Virilla_train_accident\" title=\"El Virilla train accident\">El Virilla train accident</a>, <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, kills 248 people and wounds another 93 when a train falls off a bridge over the Río Virilla between <a href=\"https://wikipedia.org/wiki/Heredia,_Costa_Rica\" title=\"Heredia, Costa Rica\">Heredia</a> and <a href=\"https://wikipedia.org/wiki/Tib%C3%A1s\" title=\"Tibás\">Tibás</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/El_Virilla_train_accident\" title=\"El Virilla train accident\">El Virilla train accident</a>, <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, kills 248 people and wounds another 93 when a train falls off a bridge over the Río Virilla between <a href=\"https://wikipedia.org/wiki/Heredia,_Costa_Rica\" title=\"Heredia, Costa Rica\">Heredia</a> and <a href=\"https://wikipedia.org/wiki/Tib%C3%A1s\" title=\"Tibás\">Tibás</a>.", "links": [{"title": "El Virilla train accident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_train_accident"}, {"title": "Costa Rica", "link": "https://wikipedia.org/wiki/Costa_Rica"}, {"title": "Heredia, Costa Rica", "link": "https://wikipedia.org/wiki/Heredia,_Costa_Rica"}, {"title": "Tibás", "link": "https://wikipedia.org/wiki/Tib%C3%A1s"}]}, {"year": "1931", "text": "<PERSON><PERSON>, India's first talking film, is released.", "html": "1931 - <i><a href=\"https://wikipedia.org/wiki/Alam_Ara\" title=\"Alam Ara\"><PERSON><PERSON></a></i>, India's first talking film, is released.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Alam_Ara\" title=\"Alam Ara\"><PERSON><PERSON></a></i>, India's first talking film, is released.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "Slovakia declares independence under German pressure.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Republic (1939-45)\">Slovakia</a> declares independence under <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> pressure.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Republic (1939-45)\">Slovakia</a> declares independence under <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> pressure.", "links": [{"title": "Slovak Republic (1939-45)", "link": "https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1942", "text": "<PERSON> becomes the first American patient to be treated with penicillin, under the care of <PERSON><PERSON> and <PERSON>.", "html": "1942 - <PERSON> becomes the first American patient to be treated with <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a>, under the care of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <PERSON>.", "no_year_html": "<PERSON> becomes the first American patient to be treated with <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a>, under the care of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <PERSON>.", "links": [{"title": "Penicillin", "link": "https://wikipedia.org/wiki/Penicillin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "The Holocaust: The liquidation of the Kraków Ghetto is completed.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The liquidation of the <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto\" title=\"Kraków Ghetto\">Kraków Ghetto</a> is completed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: The liquidation of the <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto\" title=\"Kraków Ghetto\">Kraków Ghetto</a> is completed.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Kraków Ghetto", "link": "https://wikipedia.org/wiki/Krak%C3%B3w_Ghetto"}]}, {"year": "1945", "text": "The R.A.F. drop the Grand Slam bomb in action for the first time, on a railway viaduct near Bielefeld, Germany.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/R.A.F.\" class=\"mw-redirect\" title=\"R.A.F.\">R.A.F.</a> drop the <a href=\"https://wikipedia.org/wiki/Grand_Slam_bomb\" class=\"mw-redirect\" title=\"Grand Slam bomb\">Grand Slam bomb</a> in action for the first time, on a railway viaduct near <a href=\"https://wikipedia.org/wiki/Bielefeld\" title=\"Bielefeld\">Bielefeld</a>, Germany.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/R.A.F.\" class=\"mw-redirect\" title=\"R.A.F.\">R.A.F.</a> drop the <a href=\"https://wikipedia.org/wiki/Grand_Slam_bomb\" class=\"mw-redirect\" title=\"Grand Slam bomb\">Grand Slam bomb</a> in action for the first time, on a railway viaduct near <a href=\"https://wikipedia.org/wiki/Bielefeld\" title=\"Bielefeld\">Bielefeld</a>, Germany.", "links": [{"title": "R.A.F.", "link": "https://wikipedia.org/wiki/R.A.F."}, {"title": "Grand Slam bomb", "link": "https://wikipedia.org/wiki/Grand_Slam_bomb"}, {"title": "Bielefeld", "link": "https://wikipedia.org/wiki/Bielefeld"}]}, {"year": "1951", "text": "Korean War: United Nations troops recapture Seoul for the second time.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> troops recapture <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a> for the second time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> troops recapture <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a> for the second time.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}]}, {"year": "1961", "text": "A USAF B-52 bomber carrying nuclear weapons crashes near Yuba City, California.", "html": "1961 - A <a href=\"https://wikipedia.org/wiki/USAF\" class=\"mw-redirect\" title=\"USAF\">USAF</a> B-52 bomber carrying nuclear weapons <a href=\"https://wikipedia.org/wiki/1961_Yuba_City_B-52_crash\" title=\"1961 Yuba City B-52 crash\">crashes near Yuba City, California</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/USAF\" class=\"mw-redirect\" title=\"USAF\">USAF</a> B-52 bomber carrying nuclear weapons <a href=\"https://wikipedia.org/wiki/1961_Yuba_City_B-52_crash\" title=\"1961 Yuba City B-52 crash\">crashes near Yuba City, California</a>.", "links": [{"title": "USAF", "link": "https://wikipedia.org/wiki/USAF"}, {"title": "1961 Yuba City B-52 crash", "link": "https://wikipedia.org/wiki/1961_Yuba_City_B-52_crash"}]}, {"year": "1964", "text": "<PERSON> is convicted of killing <PERSON>, the assumed assassin of <PERSON>.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of killing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the assumed assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of killing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the assumed assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "The body of U.S. President <PERSON> is moved to a permanent burial place at Arlington National Cemetery.", "html": "1967 - The body of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is moved to a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Eternal_Flame\" title=\"John <PERSON> Eternal Flame\">permanent burial place</a> at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "no_year_html": "The body of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is moved to a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Eternal_Flame\" title=\"John <PERSON> Eternal Flame\">permanent burial place</a> at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> Eternal Flame", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Eternal_Flame"}, {"title": "Arlington National Cemetery", "link": "https://wikipedia.org/wiki/Arlington_National_Cemetery"}]}, {"year": "1972", "text": "Sterling Airways Flight 296 crashes near Kalba, United Arab Emirates while on approach to Dubai International Airport, killing 112 people.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sterling_Airways_Flight_296\" title=\"Sterling Airways Flight 296\">Sterling Airways Flight 296</a> crashes near <a href=\"https://wikipedia.org/wiki/Kalba\" title=\"Kalba\"><PERSON><PERSON><PERSON></a>, United Arab Emirates while on approach to <a href=\"https://wikipedia.org/wiki/Dubai_International_Airport\" title=\"Dubai International Airport\">Dubai International Airport</a>, killing 112 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sterling_Airways_Flight_296\" title=\"Sterling Airways Flight 296\">Sterling Airways Flight 296</a> crashes near <a href=\"https://wikipedia.org/wiki/Kal<PERSON>\" title=\"Ka<PERSON><PERSON>\">Kal<PERSON></a>, United Arab Emirates while on approach to <a href=\"https://wikipedia.org/wiki/Dubai_International_Airport\" title=\"Dubai International Airport\">Dubai International Airport</a>, killing 112 people.", "links": [{"title": "Sterling Airways Flight 296", "link": "https://wikipedia.org/wiki/Sterling_Airways_Flight_296"}, {"title": "Kalba", "link": "https://wikipedia.org/wiki/Kalba"}, {"title": "Dubai International Airport", "link": "https://wikipedia.org/wiki/Dubai_International_Airport"}]}, {"year": "1978", "text": "The Israel Defense Forces launch Operation Litani, a seven-day campaign to invade and occupy southern Lebanon.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> launch <a href=\"https://wikipedia.org/wiki/Operation_Litani\" class=\"mw-redirect\" title=\"Operation Litani\">Operation Litani</a>, a seven-day campaign to invade and occupy southern <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> launch <a href=\"https://wikipedia.org/wiki/Operation_Litani\" class=\"mw-redirect\" title=\"Operation Litani\">Operation Litani</a>, a seven-day campaign to invade and occupy southern <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>.", "links": [{"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Operation Litani", "link": "https://wikipedia.org/wiki/Operation_Litani"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}]}, {"year": "1979", "text": "Alia Royal Jordanian Flight 600 crashes at Doha International Airport, killing 45 people.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>a_Royal_Jordanian_Flight_600\" title=\"Alia Royal Jordanian Flight 600\">Alia Royal Jordanian Flight 600</a> crashes at <a href=\"https://wikipedia.org/wiki/Doha_International_Airport\" title=\"Doha International Airport\">Doha International Airport</a>, killing 45 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>a_Royal_Jordanian_Flight_600\" title=\"Alia Royal Jordanian Flight 600\">Alia Royal Jordanian Flight 600</a> crashes at <a href=\"https://wikipedia.org/wiki/Doha_International_Airport\" title=\"Doha International Airport\">Doha International Airport</a>, killing 45 people.", "links": [{"title": "Alia Royal Jordanian Flight 600", "link": "https://wikipedia.org/wiki/<PERSON>a_Royal_Jordanian_Flight_600"}, {"title": "Doha International Airport", "link": "https://wikipedia.org/wiki/Doha_International_Airport"}]}, {"year": "1980", "text": "LOT Polish Airlines Flight 007 crashes during final approach near Warsaw, Poland, killing 87 people, including a 14-man American boxing team.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_007\" title=\"LOT Polish Airlines Flight 007\">LOT Polish Airlines Flight 007</a> crashes during <a href=\"https://wikipedia.org/wiki/Final_approach\" title=\"Final approach\">final approach</a> near <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland, killing 87 people, including a 14-man American <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> team.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_007\" title=\"LOT Polish Airlines Flight 007\">LOT Polish Airlines Flight 007</a> crashes during <a href=\"https://wikipedia.org/wiki/Final_approach\" title=\"Final approach\">final approach</a> near <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland, killing 87 people, including a 14-man American <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a> team.", "links": [{"title": "LOT Polish Airlines Flight 007", "link": "https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_007"}, {"title": "Final approach", "link": "https://wikipedia.org/wiki/Final_approach"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}, {"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}]}, {"year": "1982", "text": "The South African government bombs the headquarters of the African National Congress in London.", "html": "1982 - The South African government <a href=\"https://wikipedia.org/wiki/1982_bombing_of_the_African_National_Congress_headquarters_in_London\" title=\"1982 bombing of the African National Congress headquarters in London\">bombs</a> the headquarters of the <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a> in London.", "no_year_html": "The South African government <a href=\"https://wikipedia.org/wiki/1982_bombing_of_the_African_National_Congress_headquarters_in_London\" title=\"1982 bombing of the African National Congress headquarters in London\">bombs</a> the headquarters of the <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a> in London.", "links": [{"title": "1982 bombing of the African National Congress headquarters in London", "link": "https://wikipedia.org/wiki/1982_bombing_of_the_African_National_Congress_headquarters_in_London"}, {"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}]}, {"year": "1988", "text": "In the Johnson South Reef Skirmish Chinese forces defeat Vietnamese forces in an altercation over control of one of the Spratly Islands.", "html": "1988 - In the <a href=\"https://wikipedia.org/wiki/Johnson_South_Reef_Skirmish\" class=\"mw-redirect\" title=\"Johnson South Reef Skirmish\">Johnson South Reef Skirmish</a> Chinese forces defeat Vietnamese forces in an altercation over control of one of the <a href=\"https://wikipedia.org/wiki/Spratly_Islands\" title=\"Spratly Islands\">Spratly Islands</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Johnson_South_Reef_Skirmish\" class=\"mw-redirect\" title=\"Johnson South Reef Skirmish\">Johnson South Reef Skirmish</a> Chinese forces defeat Vietnamese forces in an altercation over control of one of the <a href=\"https://wikipedia.org/wiki/Spratly_Islands\" title=\"Spratly Islands\">Spratly Islands</a>.", "links": [{"title": "Johnson South Reef Skirmish", "link": "https://wikipedia.org/wiki/Johnson_South_Reef_Skirmish"}, {"title": "Spratly Islands", "link": "https://wikipedia.org/wiki/Spratly_Islands"}]}, {"year": "1995", "text": "<PERSON> becomes the first American astronaut to ride to space on board a Russian launch vehicle.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a> to ride to space on board a Russian launch vehicle.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a> to ride to space on board a Russian launch vehicle.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}]}, {"year": "2006", "text": "The 2006 Chadian coup d'état attempt ends in failure.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/2006_Chadian_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2006 Chadian coup d'état attempt\">2006 Chadian coup d'état attempt</a> ends in failure.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2006_Chadian_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2006 Chadian coup d'état attempt\">2006 Chadian coup d'état attempt</a> ends in failure.", "links": [{"title": "2006 Chadian coup d'état attempt", "link": "https://wikipedia.org/wiki/2006_Chadian_coup_d%27%C3%A9tat_attempt"}]}, {"year": "2006", "text": "Operation Bringing Home the Goods: Israeli troops raid an American-supervised Palestinian prison in Jericho to capture six Palestinian prisoners, including PFLP chief <PERSON>.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Operation_Bringing_Home_the_Goods\" title=\"Operation Bringing Home the Goods\">Operation Bringing Home the Goods</a>: Israeli troops raid an American-supervised <a href=\"https://wikipedia.org/wiki/Palestinian_Authority\" title=\"Palestinian Authority\">Palestinian</a> prison in <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a> to capture six Palestinian prisoners, including <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">PFLP</a> chief <a href=\"https://wikipedia.org/wiki/Ahmad_<PERSON>27adat\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Bringing_Home_the_Goods\" title=\"Operation Bringing Home the Goods\">Operation Bringing Home the Goods</a>: Israeli troops raid an American-supervised <a href=\"https://wikipedia.org/wiki/Palestinian_Authority\" title=\"Palestinian Authority\">Palestinian</a> prison in <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a> to capture six Palestinian prisoners, including <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">PFLP</a> chief <a href=\"https://wikipedia.org/wiki/Ahmad_<PERSON>27adat\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Operation Bringing Home the Goods", "link": "https://wikipedia.org/wiki/Operation_Bringing_Home_the_Goods"}, {"title": "Palestinian Authority", "link": "https://wikipedia.org/wiki/Palestinian_Authority"}, {"title": "Jericho", "link": "https://wikipedia.org/wiki/Jericho"}, {"title": "Popular Front for the Liberation of Palestine", "link": "https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ahmad_Sa%27adat"}]}, {"year": "2007", "text": "The Nandigram violence in Nandigram, West Bengal, results in the deaths of at least 14 people.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Nandigram_violence\" title=\"Nandigram violence\">Nandigram violence</a> in <a href=\"https://wikipedia.org/wiki/Nandigram\" title=\"Nandigram\">Nandigram</a>, <a href=\"https://wikipedia.org/wiki/West_Bengal\" title=\"West Bengal\">West Bengal</a>, results in the deaths of at least 14 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nandigram_violence\" title=\"Nandigram violence\">Nandigram violence</a> in <a href=\"https://wikipedia.org/wiki/Nandigram\" title=\"Nandigram\">Nandigram</a>, <a href=\"https://wikipedia.org/wiki/West_Bengal\" title=\"West Bengal\">West Bengal</a>, results in the deaths of at least 14 people.", "links": [{"title": "Nandigram violence", "link": "https://wikipedia.org/wiki/Nandigram_violence"}, {"title": "Nandigram", "link": "https://wikipedia.org/wiki/Nandigram"}, {"title": "West Bengal", "link": "https://wikipedia.org/wiki/West_Bengal"}]}, {"year": "2008", "text": "A series of riots, protests, and demonstrations erupt in Lhasa and subsequently spread elsewhere in Tibet.", "html": "2008 - A series of <a href=\"https://wikipedia.org/wiki/2008_Tibetan_unrest\" title=\"2008 Tibetan unrest\">riots, protests, and demonstrations</a> erupt in <a href=\"https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)\" class=\"mw-redirect\" title=\"Lhasa (prefecture-level city)\">Lhasa</a> and subsequently spread elsewhere in <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibet</a>.", "no_year_html": "A series of <a href=\"https://wikipedia.org/wiki/2008_Tibetan_unrest\" title=\"2008 Tibetan unrest\">riots, protests, and demonstrations</a> erupt in <a href=\"https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)\" class=\"mw-redirect\" title=\"Lhasa (prefecture-level city)\">Lhasa</a> and subsequently spread elsewhere in <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibet</a>.", "links": [{"title": "2008 Tibetan unrest", "link": "https://wikipedia.org/wiki/2008_Tibetan_unrest"}, {"title": "Lhasa (prefecture-level city)", "link": "https://wikipedia.org/wiki/Lhasa_(prefecture-level_city)"}, {"title": "Tibet", "link": "https://wikipedia.org/wiki/Tibet"}]}, {"year": "2017", "text": "A naming ceremony for the chemical element nihonium takes place in Tokyo, with then Crown Prince <PERSON> in attendance.", "html": "2017 - A naming ceremony for the chemical element <a href=\"https://wikipedia.org/wiki/Nihonium\" title=\"Nihonium\">nihonium</a> takes place in Tokyo, with then <a href=\"https://wikipedia.org/wiki/Naruhito\" title=\"Naruhito\">Crown Prince <PERSON></a> in attendance.", "no_year_html": "A naming ceremony for the chemical element <a href=\"https://wikipedia.org/wiki/Nihonium\" title=\"Nihonium\">nihonium</a> takes place in Tokyo, with then <a href=\"https://wikipedia.org/wiki/Naruhito\" title=\"Naruhito\">Crown Prince <PERSON></a> in attendance.", "links": [{"title": "Nihonium", "link": "https://wikipedia.org/wiki/Nihonium"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naru<PERSON>o"}]}, {"year": "2019", "text": "Cyclone <PERSON><PERSON> makes landfall near Beira, Mozambique, causing devastating floods and over 1,000 deaths.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Cyclone_Idai\" title=\"Cyclone Idai\">Cyclone <PERSON><PERSON></a> makes landfall near <a href=\"https://wikipedia.org/wiki/Beira,_Mozambique\" title=\"Beira, Mozambique\">Beira, Mozambique</a>, causing devastating floods and over 1,000 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyclone_Idai\" title=\"Cyclone Ida<PERSON>\">Cyclone <PERSON><PERSON></a> makes landfall near <a href=\"https://wikipedia.org/wiki/Beira,_Mozambique\" title=\"Beira, Mozambique\">Beira, Mozambique</a>, causing devastating floods and over 1,000 deaths.", "links": [{"title": "Cyclone <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cyclone_Idai"}, {"title": "Beira, Mozambique", "link": "https://wikipedia.org/wiki/Beira,_Mozambique"}]}, {"year": "2021", "text": "Burmese security forces kill at least 65 civilians in the Hlaingthaya massacre.", "html": "2021 - Burmese security forces kill at least 65 civilians in the <a href=\"https://wikipedia.org/wiki/Hlaingthaya_massacre\" title=\"Hlaingthaya massacre\">Hlaingthaya massacre</a>.", "no_year_html": "Burmese security forces kill at least 65 civilians in the <a href=\"https://wikipedia.org/wiki/Hlaingthaya_massacre\" title=\"Hlaingthaya massacre\">Hlaingthaya massacre</a>.", "links": [{"title": "Hlaingthaya massacre", "link": "https://wikipedia.org/wiki/Hlaingth<PERSON>_massacre"}]}], "Births": [{"year": "1638", "text": "<PERSON>, German mystic (d. 1710)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic (d. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mystic (d. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON><PERSON> of Pindus, Aromanian physician and noble (d. 1828)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus\" title=\"<PERSON><PERSON> of Pindus\"><PERSON><PERSON> of Pindus</a>, Aromanian physician and noble (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus\" title=\"<PERSON><PERSON> of Pindus\"><PERSON><PERSON> of Pindus</a>, Aromanian physician and noble (d. 1828)", "links": [{"title": "<PERSON><PERSON> of Pindus", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_of_Pindus"}]}, {"year": "1772", "text": "<PERSON>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (d. 1846)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>, Dominican politician and writer. He was the leader of the Independence movement of the Dominican Republic against Spain in 1821 (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres"}]}, {"year": "1790", "text": "<PERSON>, German painter and engraver (d. 1863)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and engraver (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and engraver (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, American inventor and architect (d. 1874)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and architect (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and architect (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON><PERSON><PERSON>, Estonian poet (d. 1822)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian poet (d. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Austrian composer and conductor (d. 1849)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a>, Austrian composer and conductor (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Johann Strauss I\"><PERSON></a>, Austrian composer and conductor (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American lawyer and jurist (d. 1892)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON> of Italy (d. 1878)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy\" class=\"mw-redirect\" title=\"<PERSON> of Italy\"><PERSON> of Italy</a> (d. 1878)", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Italy"}]}, {"year": "1822", "text": "<PERSON> of the Two Sicilies (d. 1889)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> of the Two Sicilies\"><PERSON> of the Two Sicilies</a> (d. 1889)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French poet and critic (d. 1891)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_de_Banville\" title=\"Th<PERSON>od<PERSON> Banville\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French poet and critic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_de_Banville\" title=\"Théod<PERSON> de Banville\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French poet and critic (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, English painter and illustrator (d. 1911)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American dentist and educator (d. 1910)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and educator (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dentist and educator (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, Italian astronomer and historian (d. 1910)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and historian (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and historian (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, English author of Mrs <PERSON><PERSON>'s Book of Household Management (d. 1865)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author of <i><a href=\"https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management\" class=\"mw-redirect\" title=\"Mrs <PERSON>'s Book of Household Management\">Mrs <PERSON>'s Book of Household Management</a></i> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author of <i><a href=\"https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management\" class=\"mw-redirect\" title=\"Mrs <PERSON>'s Book of Household Management\">Mrs <PERSON><PERSON>'s Book of Household Management</a></i> (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mrs <PERSON><PERSON>'s Book of Household Management", "link": "https://wikipedia.org/wiki/Mrs_<PERSON><PERSON>%27s_Book_of_Household_Management"}]}, {"year": "1837", "text": "<PERSON>, American librarian (d. 1903)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON> of Italy (d. 1900)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (d. 1900)", "links": [{"title": "<PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/Umberto_I_of_Italy"}]}, {"year": "1844", "text": "<PERSON>, English poet and herpetologist (d. 1881)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, English poet and herpetologist (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, English poet and herpetologist (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_O%27Shaughnessy"}]}, {"year": "1847", "text": "<PERSON>, Brazilian poet and playwright (d. 1871)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet and playwright (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Alves\"><PERSON></a>, Brazilian poet and playwright (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Alves"}]}, {"year": "1853", "text": "<PERSON>, Swiss painter (d. 1918)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, German physician and biologist, Nobel Prize laureate (d. 1915)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1854", "text": "<PERSON>, English publisher, co-founded The Bodley Head (d. 1925)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English publisher, co-founded <a href=\"https://wikipedia.org/wiki/The_Bodley_Head\" title=\"The Bodley Head\">The Bodley Head</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English publisher, co-founded <a href=\"https://wikipedia.org/wiki/The_Bodley_Head\" title=\"The Bodley Head\">The Bodley Head</a> (d. 1925)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)"}, {"title": "The Bodley Head", "link": "https://wikipedia.org/wiki/<PERSON>_Bo<PERSON>_Head"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Romanian author and poet (d. 1920)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian author and poet (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian author and poet (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, American lawyer and politician, 28th Vice President of the United States of America (d. 1925)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Vice President of the United States of America\">Vice President of the United States of America</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America\" class=\"mw-redirect\" title=\"Vice President of the United States of America\">Vice President of the United States of America</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States of America", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States_of_America"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian physicist and meteorologist (d. 1951)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Vilhel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian physicist and meteorologist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>hel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian physicist and meteorologist (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilhel<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American engineer (d. 1900)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Canadian jurist, author, and activist (d. 1933)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jurist, author, and activist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian jurist, author, and activist (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, English author and playwright (d. 1951)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Blackwood\"><PERSON><PERSON><PERSON></a>, English author and playwright (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Blackwood\"><PERSON><PERSON><PERSON></a>, English author and playwright (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wood"}]}, {"year": "1874", "text": "<PERSON>, Dutch businessman, co-founded Philips Electronics (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Philips\" title=\"<PERSON>\">Philips Electronics</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, co-founded <a href=\"https://wikipedia.org/wiki/Philips\" title=\"Philips\">Philips Electronics</a> (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philips", "link": "https://wikipedia.org/wiki/Philips"}]}, {"year": "1879", "text": "<PERSON>, German-American physicist, academic and Nobel Prize laureate (d. 1955)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a>, German-American physicist, academic and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert Einstein\"><PERSON></a>, German-American physicist, academic and <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Polish mathematician and academic (d. 1969)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mathematician and academic (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wac%C5%82aw_Sierpi%C5%84ski"}]}, {"year": "1885", "text": "<PERSON>, French-American soldier and pilot (d. 1918)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American soldier and pilot (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American soldier and pilot (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (d. 1964)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>\" title=\"<PERSON>rm<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rm<PERSON>_<PERSON>\" title=\"<PERSON>rm<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Firm<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American-French bookseller and publisher, who founded Shakespeare and Company (d. 1962)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Sylvia_Beach\" title=\"Sylvia Beach\">Sylvia Beach</a>, American-French bookseller and publisher, who founded <a href=\"https://wikipedia.org/wiki/Shakespeare_and_Company_(1919%E2%80%931941)\" title=\"Shakespeare and Company (1919-1941)\">Shakespeare and Company</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sylvia_Beach\" title=\"Sylvia Beach\">Sylvia Beach</a>, American-French bookseller and publisher, who founded <a href=\"https://wikipedia.org/wiki/Shakespeare_and_Company_(1919%E2%80%931941)\" title=\"Shakespeare and Company (1919-1941)\">Shakespeare and Company</a> (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sylvia_Beach"}, {"title": "Shakespeare and Company (1919-1941)", "link": "https://wikipedia.org/wiki/Shakespeare_and_Company_(1919%E2%80%931941)"}]}, {"year": "1898", "text": "<PERSON>, French-American painter and illustrator (d. 1954)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French-American painter and illustrator (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, French-American painter and illustrator (d. 1954)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1899", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian businessman, founded Irving Oil (d. 1992)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"K. C. Irving\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Irving_Oil\" title=\"Irving Oil\">Irving Oil</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON>\" title=\"K. C. Irving\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Irving_Oil\" title=\"Irving Oil\">Irving Oil</a> (d. 1992)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Irving Oil", "link": "https://wikipedia.org/wiki/Irving_Oil"}]}, {"year": "1901", "text": "<PERSON>, South African hurdler and long jumper (d. 1977)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African hurdler and long jumper (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African hurdler and long jumper (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, American painter and sculptor (d. 1974)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter and sculptor (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actress and dancer (d. 2010)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French journalist, sociologist, and philosopher (d. 1983)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, sociologist, and philosopher (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, sociologist, and philosopher (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Turkish composer and educator (d. 1972)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish composer and educator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish composer and educator (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ul<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American designer of military aircraft (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American designer of military aircraft (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American designer of military aircraft (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, French philosopher and academic (d. 1961)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English engineer and businessman, founded Vincent Motorcycles (d. 1979)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Vincent_Motorcycles\" title=\"Vincent Motorcycles\">Vincent Motorcycles</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Vincent_Motorcycles\" title=\"Vincent Motorcycles\">Vincent Motorcycles</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vincent Motorcycles", "link": "https://wikipedia.org/wiki/Vincent_Motorcycles"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Yugoslav politician and Divisional Commander of the First Proletarian Division of the Yugoslav Partisans (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Ko%C4%8Da_Popovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav politician and Divisional Commander of the <a href=\"https://wikipedia.org/wiki/1st_Division_(Yugoslav_Partisans)\" title=\"1st Division (Yugoslav Partisans)\">First Proletarian Division</a> of the <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ko%C4%8Da_Popovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav politician and Divisional Commander of the <a href=\"https://wikipedia.org/wiki/1st_Division_(Yugoslav_Partisans)\" title=\"1st Division (Yugoslav Partisans)\">First Proletarian Division</a> of the <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko%C4%8Da_Popovi%C4%87"}, {"title": "1st Division (Yugoslav Partisans)", "link": "https://wikipedia.org/wiki/1st_Division_(Yugoslav_Partisans)"}, {"title": "Yugoslav Partisans", "link": "https://wikipedia.org/wiki/Yugoslav_Partisans"}]}, {"year": "1911", "text": "<PERSON>, Japanese origamist (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese origamist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese origamist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English footballer (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ba<PERSON>in\" title=\"<PERSON> Bastin\"><PERSON></a>, English footballer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bastin\" title=\"<PERSON> Bastin\"><PERSON></a>, English footballer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American saxophonist, composer, and bandleader (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, American saxophonist, composer, and bandleader (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, American saxophonist, composer, and bandleader (d. 2001)", "links": [{"title": "<PERSON> (bandleader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)"}]}, {"year": "1912", "text": "<PERSON><PERSON> Jr. American lieutenant, lawyer, and politician, 15th United States Secretary of the Navy (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON> <PERSON> Jr.</a> American lieutenant, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Jr.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON> Jr.</a> American lieutenant, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 1994)", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American lawyer and politician, 10th United States Secretary of Labor (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>z"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Slovak writer (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>inik_Tatarka"}]}, {"year": "1914", "text": "<PERSON>, American singer-songwriter (d. 1981)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English actor and songwriter (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and songwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and songwriter (d. 1999)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1914", "text": "<PERSON>, American race car driver and businessman, founded Petty Enterprises (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Petty\" title=\"Lee Petty\"><PERSON></a>, American race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Petty_Enterprises\" title=\"Petty Enterprises\">Petty Enterprises</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lee_Petty\" title=\"Lee Petty\"><PERSON></a>, American race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Petty_Enterprises\" title=\"Petty Enterprises\">Petty Enterprises</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Petty Enterprises", "link": "https://wikipedia.org/wiki/Petty_Enterprises"}]}, {"year": "1915", "text": "<PERSON>, Canadian violinist, composer, and conductor (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, and conductor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American author, playwright, and screenwriter (d. 2009)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English lieutenant and pilot (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English lieutenant and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English lieutenant and pilot (d. 2013)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(RAF_officer)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American librarian (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American librarian (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American librarian (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American author and screenwriter (d. 1988)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American author and cartoonist, created <PERSON> (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Menace_(U.S._comics)\" title=\"<PERSON> the <PERSON> (U.S. comics)\"><PERSON> the Menace</a></i> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Menace_(U.S._comics)\" title=\"<PERSON> (U.S. comics)\"><PERSON> the Men<PERSON></a></i> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the <PERSON>ace (U.S. comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Menace_(U.S._comics)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, English high jumper (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American businessman, founded Chick-fil-A (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chick-fil-A\" title=\"Chick-fil-A\">Chick-fil-A</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chick-fil-A\" title=\"Chick-fil-A\">Chick-fil-A</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chick-fil-A", "link": "https://wikipedia.org/wiki/Chick-fil-A"}]}, {"year": "1921", "text": "<PERSON>, American author and critic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American pianist and composer (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American photographer (d. 1971)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON> Sr., American businessman (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Ford Sr.\"><PERSON>.</a>, American businessman (d. 2014)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ford_Sr."}]}, {"year": "1925", "text": "<PERSON>, American sergeant and businessman (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and businessman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian pianist, composer, conductor, and educator (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian pianist, composer, conductor, and educator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian pianist, composer, conductor, and educator (d. 2018)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1927", "text": "<PERSON>, American basketball player (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hare\" title=\"<PERSON> Share\"><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hare\" title=\"Chuck Share\"><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hare"}]}, {"year": "1928", "text": "<PERSON>, American astronaut (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Spanish environmentalist (d. 1980)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADgue<PERSON>_de_la_Fuente\" title=\"<PERSON>\"><PERSON></a>, Spanish environmentalist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADgue<PERSON>_de_la_Fuente\" title=\"<PERSON>\"><PERSON></a>, Spanish environmentalist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADguez_de_la_Fuente"}]}, {"year": "1929", "text": "<PERSON>, American golfer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>by\"><PERSON></a>, American golfer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goalby\"><PERSON></a>, American golfer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and actor (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and actor (d. 2015)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Russian wife of <PERSON>, First Lady of Russia", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Russia\" title=\"First Lady of Russia\">First Lady of Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Russia\" title=\"First Lady of Russia\">First Lady of Russia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "First Lady of Russia", "link": "https://wikipedia.org/wiki/First_Lady_of_Russia"}]}, {"year": "1933", "text": "<PERSON>, English actor", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American producer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jones\"><PERSON></a>, American producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American captain, pilot, and astronaut (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American 15th General of The Salvation Army", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American 15th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American 15th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1936", "text": "<PERSON>, New Zealand golfer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1937", "text": "<PERSON>, South African cricketer and referee (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and referee (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and referee (d. 2013)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1938", "text": "<PERSON>, English actress and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American televangelist, co-founder of the Trinity Broadcasting Network (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American televangelist, co-founder of the <a href=\"https://wikipedia.org/wiki/Trinity_Broadcasting_Network\" title=\"Trinity Broadcasting Network\">Trinity Broadcasting Network</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American televangelist, co-founder of the <a href=\"https://wikipedia.org/wiki/Trinity_Broadcasting_Network\" title=\"Trinity Broadcasting Network\">Trinity Broadcasting Network</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trinity Broadcasting Network", "link": "https://wikipedia.org/wiki/Trinity_Broadcasting_Network"}]}, {"year": "1938", "text": "<PERSON>, Australian cricketer (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2016)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_Orb%C3%A1n"}]}, {"year": "1939", "text": "<PERSON>, American actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French director and screenwriter (d. 2025)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bertrand_Blier"}]}, {"year": "1939", "text": "<PERSON>, French director and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German-American director, producer, and screenwriter (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actress and singer (d. 1994)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American actress and singer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian composer and conductor (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and conductor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player and manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Nedomansk%C3%BD\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Nedomansk%C3%BD\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A1clav_Nedomansk%C3%BD"}]}, {"year": "1944", "text": "<PERSON>, English footballer and manager", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1944)\" title=\"<PERSON> (footballer, born 1944)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1944)\" title=\"<PERSON> (footballer, born 1944)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1944)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1944)"}]}, {"year": "1944", "text": "<PERSON>, Australian historian and academic (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English comedian, actor, and game show host", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American saxophonist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American basketball player, coach, and manager (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>seld"}]}, {"year": "1947", "text": "<PERSON>, English pianist and composer (d. 1993)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English singer-songwriter and keyboard player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American physician and politician (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor, comedian, director, producer, and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Crystal\"><PERSON></a>, American actor, comedian, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Dutch sculptor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and radio host", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American businessman and philanthropist, co-founded Ben & Jerry's", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_%26_<PERSON>%27s\" title=\"<PERSON> &amp; <PERSON>'s\"><PERSON> &amp; <PERSON>'s</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_%26_<PERSON>%27s\" title=\"<PERSON> &amp; <PERSON>'s\"><PERSON> &amp; <PERSON>'s</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ben & Jerry's", "link": "https://wikipedia.org/wiki/<PERSON>_%26_<PERSON>%27s"}]}, {"year": "1953", "text": "<PERSON>, Scottish singer-songwriter (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian rugby league player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1954)\" title=\"<PERSON> (rugby league, born 1954)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1954)\" title=\"<PERSON> (rugby league, born 1954)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league, born 1954)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1954)"}]}, {"year": "1955", "text": "<PERSON>, American director and screenwriter (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Judge of the Supreme Court of India", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Indu_Malhotra\" title=\"Indu Malhotra\"><PERSON><PERSON> Malhotra</a>, Judge of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indu_Malhotra\" title=\"Indu Malhotra\"><PERSON><PERSON> Malhotra</a>, Judge of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_India\" title=\"Supreme Court of India\">Supreme Court of India</a>", "links": [{"title": "<PERSON><PERSON> Malhotra", "link": "https://wikipedia.org/wiki/Indu_Malhotra"}, {"title": "Supreme Court of India", "link": "https://wikipedia.org/wiki/Supreme_Court_of_India"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Prince of Monaco", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a>", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American astronomer and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player (d. 2006)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Greek-Canadian businessman and philanthropist, founded BlackBerry Limited", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/BlackBerry_Limited\" title=\"BlackBerry Limited\">BlackBerry Limited</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/BlackBerry_Limited\" title=\"BlackBerry Limited\">BlackBerry Limited</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BlackBerry Limited", "link": "https://wikipedia.org/wiki/BlackBerry_Limited"}]}, {"year": "1963", "text": "<PERSON>, Australian cricketer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player and administrator", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and administrator", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (right-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right-handed_pitcher)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Indian film actor, producer, and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor, producer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor, producer, and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON> (screenwriter)", "link": "https://wikipedia.org/wiki/<PERSON>(screenwriter)"}]}, {"year": "1966", "text": "<PERSON>, Danish actor, director, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Danish actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Danish actor, director, and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1966", "text": "<PERSON>, American actress and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian-American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Follows\" title=\"<PERSON> Follows\"><PERSON></a>, Canadian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Follows\" title=\"<PERSON> Follows\"><PERSON> Follow<PERSON></a>, Canadian-American actress", "links": [{"title": "<PERSON> Follows", "link": "https://wikipedia.org/wiki/<PERSON>_Follows"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Icelandic politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Magn%C3%BAs_%C3%81rni_Magn%C3%BAsson\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magn%C3%BAs_%C3%81rni_Magn%C3%BAsson\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Magn%C3%BAs_%C3%81rni_Magn%C3%BAsson"}]}, {"year": "1969", "text": "<PERSON>, American basketball player and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1969)\" title=\"<PERSON> (basketball, born 1969)\"><PERSON></a>, American basketball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1969)\" title=\"<PERSON> (basketball, born 1969)\"><PERSON></a>, American basketball player and actor", "links": [{"title": "<PERSON> (basketball, born 1969)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1969)"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian poet and activist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>mila"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Indian film director and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film director and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>\" title=\"<PERSON> Traverse\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Traverse\" title=\"Patrick Traverse\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Patrick Traverse", "link": "https://wikipedia.org/wiki/Patrick_Traverse"}]}, {"year": "1975", "text": "<PERSON>, English footballer and referee", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Belarusian-Australian pole vaulter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Australian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Australian pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American improvisational comedian and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American improvisational comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American improvisational comedian and actor", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "1976", "text": "<PERSON>, English rugby player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and sportscaster", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Latvian footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese footballer (d. 2011)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, New Zealand-Australian rugby player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand-Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand-Australian rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_union)"}]}, {"year": "1978", "text": "<PERSON>, Dutch swimmer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French footballer and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American musician and internet personality", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, German-Bosnian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sead_Ramovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sead_Ramovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Bosnian footballer", "links": [{"title": "<PERSON>d <PERSON>", "link": "https://wikipedia.org/wiki/Sead_Ramovi%C4%87"}]}, {"year": "1980", "text": "<PERSON>, English footballer and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1980)\" title=\"<PERSON> (footballer, born 1980)\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1980)"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby player and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)\" title=\"<PERSON> (safety)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)\" title=\"<PERSON> (safety)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (safety)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(safety)"}]}, {"year": "1982", "text": "<PERSON>, Argentine footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Belgian footballer (d. 2008)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Sterchele\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Sterchele\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Sterchele"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, Kazakh boxer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>ev\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakh boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Kazakh boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Zimbabwean cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian skier and cyclist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian skier and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian skier and cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German decathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rico_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Gonz%C3%A1lez"}]}, {"year": "1989", "text": "<PERSON>, Canadian race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1990", "text": "<PERSON>, Welsh footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Hungarian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tam%C3%A1s_K%C3%A1d%C3%A1r"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Japanese actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Kolbeinn_Sig%C3%BE%C3%B3<PERSON>son\" title=\"<PERSON><PERSON><PERSON>nn Sigþ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kolbeinn_Sig%C3%BE%C3%B3rsson\" title=\"<PERSON><PERSON>beinn Sigþ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko<PERSON>beinn_Sig%C3%BE%C3%B3rsson"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Serbian hurdler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Em<PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emir_Bekri%C4%87"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Sz%C5%B1<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Sz%C5%B1<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Sz%C5%B1cs_(footballer)"}]}, {"year": "1991", "text": "<PERSON>, German footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Blackheart\" title=\"Shotzi Blackheart\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Blackheart\" title=\"<PERSON>zi Blackheart\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Blackheart"}]}, {"year": "1992", "text": "<PERSON>, Swedish ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1992)\" title=\"<PERSON> (ice hockey, born 1992)\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1992)\" title=\"<PERSON> (ice hockey, born 1992)\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1992)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1992)"}]}, {"year": "1993", "text": "<PERSON>, Canadian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON>, American ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, German footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor and DJ", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and DJ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American multi-sport athlete", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American multi-sport athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American multi-sport athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON> 30, Canadian live streamer and professional gamer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_30\" title=\"Nick Eh 30\"><PERSON> 30</a>, Canadian live streamer and professional gamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_30\" title=\"Nick Eh 30\"><PERSON> 30</a>, Canadian live streamer and professional gamer", "links": [{"title": "<PERSON> 30", "link": "https://wikipedia.org/wiki/<PERSON>_Eh_30"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>an_Alt%C4%B1nta%C5%9F_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%B1nta%C5%9F_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%B1nta%C5%9F_(footballer)"}]}, {"year": "1997", "text": "<PERSON>, American gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jo<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jo<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American rapper and reality television personality", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Chrisean_Rock\" title=\"Chrisean Rock\"><PERSON><PERSON></a>, American rapper and reality television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chrisean_Rock\" title=\"Chrisean Rock\"><PERSON><PERSON></a>, American rapper and reality television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Jihoon\" title=\"Jiho<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jihoon\" title=\"<PERSON>ho<PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "Jihoon", "link": "https://wikipedia.org/wiki/Jihoon"}]}, {"year": "2001", "text": "<PERSON>, Italian-American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nico Mannion\"><PERSON></a>, Italian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nico Mannion\"><PERSON></a>, Italian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nico_Mannion"}]}, {"year": "2008", "text": "<PERSON>, American actress", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "840", "text": "<PERSON><PERSON>, Frankish scholar", "html": "840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Frankish scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Frankish scholar", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "968", "text": "<PERSON> of Ringelheim, Saxon queen (b. c. 896)", "html": "968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Ringelheim\" title=\"Matilda of Ringelheim\"><PERSON> of Ringelheim</a>, Saxon queen (b. c. 896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Ringelheim\" title=\"Matilda of Ringelheim\"><PERSON> of Ringelheim</a>, Saxon queen (b. c. 896)", "links": [{"title": "<PERSON> of Ringelheim", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ringelheim"}]}, {"year": "1555", "text": "<PERSON>, 1st Earl of Bedford (b. 1485)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Bedford\" title=\"<PERSON>, 1st Earl of Bedford\"><PERSON>, 1st Earl of Bedford</a> (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bedford\" title=\"<PERSON>, 1st Earl of Bedford\"><PERSON>, 1st Earl of Bedford</a> (b. 1485)", "links": [{"title": "<PERSON>, 1st Earl of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bedford"}]}, {"year": "1647", "text": "<PERSON>, Prince of Orange (b. 1584)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (b. 1584)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1648", "text": "<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron, English general and politician (b. 1584)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron\" title=\"<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron\"><PERSON><PERSON>, 2nd Lord <PERSON> of Cameron</a>, English general and politician (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron\" title=\"<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron\"><PERSON><PERSON>, 2nd Lord <PERSON> of Cameron</a>, English general and politician (b. 1584)", "links": [{"title": "<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron"}]}, {"year": "1696", "text": "<PERSON>, French lawyer and jurist (b. 1625)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1625)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1698", "text": "<PERSON><PERSON><PERSON>, Swedish statesman (b. 1622)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/C<PERSON>es_R%C3%A5lamb\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish statesman (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_R%C3%A5lamb\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish statesman (b. 1622)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Claes_R%C3%A5lamb"}]}, {"year": "1748", "text": "<PERSON>, Irish field marshal and politician (b. 1673)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish field marshal and politician (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish field marshal and politician (b. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, British admiral and politician, 11th Commodore Governor of Newfoundland (b. 1704)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British admiral and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British admiral and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Commodore Governor of Newfoundland</a> (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1791", "text": "<PERSON>, German historian and critic (b. 1725)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and critic (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and critic (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, German poet (b. 1724)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, 3rd Duke of Grafton, English politician, Prime Minister of Great Britain (b. 1735)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton\" title=\"<PERSON>, 3rd Duke of Grafton\"><PERSON>, 3rd Duke of Grafton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton\" title=\"<PERSON>, 3rd Duke of Grafton\"><PERSON>, 3rd Duke of Grafton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1735)", "links": [{"title": "<PERSON>, 3rd Duke of Grafton", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1823", "text": "<PERSON>, French general and politician, French Minister of War (b. 1739)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1860", "text": "<PERSON>, Italian engineer, designed the Semmering railway (b. 1802)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian engineer, designed the <a href=\"https://wikipedia.org/wiki/Semmering_railway\" title=\"Semmering railway\">Semmering railway</a> (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian engineer, designed the <a href=\"https://wikipedia.org/wiki/Semmering_railway\" title=\"Semmering railway\">Semmering railway</a> (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Semmering railway", "link": "https://wikipedia.org/wiki/Semmering_railway"}]}, {"year": "1877", "text": "<PERSON>, Argentinian general and politician, 17th Governor of Buenos Aires Province (b. 1793)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Buenos Aires Province", "link": "https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province"}]}, {"year": "1883", "text": "<PERSON>, German philosopher and theorist (b. 1818)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theorist (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theorist (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Italian economist and politician, Italian Minister of Finances (b. 1827)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances\" class=\"mw-redirect\" title=\"List of Italian Ministers of Economy and Finances\">Italian Minister of Finances</a> (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances\" class=\"mw-redirect\" title=\"List of Italian Ministers of Economy and Finances\">Italian Minister of Finances</a> (b. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "List of Italian Ministers of Economy and Finances", "link": "https://wikipedia.org/wiki/List_of_Italian_Ministers_of_Economy_and_Finances"}]}, {"year": "1921", "text": "<PERSON> executed Irish republican (b. 1901)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a> executed <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a> executed <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> (b. 1901)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}, {"title": "Irish republican", "link": "https://wikipedia.org/wiki/Irish_republican"}]}, {"year": "1923", "text": "<PERSON> and three other Irish Republicans are executed by Irish Free State forces(b. 1896)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish Republicans</a> are executed by <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> forces(b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish Republicans</a> are executed by <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> forces(b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republican", "link": "https://wikipedia.org/wiki/Irish_republican"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON><PERSON>, Finnish politician (b. 1876)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>nn<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Finnish politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>nn<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>nn<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Finnish politician (b. 1876)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American inventor and businessman, founded <PERSON> Kodak (b. 1854)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Eastman_Kodak\" class=\"mw-redirect\" title=\"Eastman Kodak\">Eastman Kodak</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Eastman_Kodak\" class=\"mw-redirect\" title=\"Eastman Kodak\">Eastman Kodak</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eastman Kodak", "link": "https://wikipedia.org/wiki/Eastman_Kodak"}]}, {"year": "1932", "text": "<PERSON>, American historian (b. 1861)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English historian (b. 1887)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/C._R._M._F._<PERSON>well\" title=\"C. R. M. F. <PERSON>well\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._R._M._F._<PERSON>well\" title=\"C. R. M. F. Cru<PERSON>well\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian (b. 1887)", "links": [{"title": "C. R. M. F. <PERSON>", "link": "https://wikipedia.org/wiki/C._R._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Czechoslovak Communist politician and 14th President of Czechoslovakia (b. 1896)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czechoslovak Communist politician and 14th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czechoslovak Communist politician and 14th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Czechoslovakia", "link": "https://wikipedia.org/wiki/President_of_Czechoslovakia"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Cypriot activist (b. 1938)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot activist (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American tennis player (b. 1879)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German historian and academic (b. 1892)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Lithuanian-American painter, illustrator, and educator (b. 1898)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American painter, illustrator, and educator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American painter, illustrator, and educator (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American computer scientist and engineer (b. 1900)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American cartoonist (b. 1901)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American cartoonist (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress (b. 1917)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American director and choreographer (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Busby_Berkeley\" title=\"Busby Berkeley\"><PERSON><PERSON></a>, American director and choreographer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Busby_Berkeley\" title=\"Busby Berkeley\"><PERSON><PERSON> Berkeley</a>, American director and choreographer (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Busby_Berkeley"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American activist and philanthropist (b. 1917)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and philanthropist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and philanthropist (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian businessman (b. 1908)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Indonesian politician, 3rd Prime Minister of Indonesia (b. 1902)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Indonesia\" title=\"Prime Minister of Indonesia\">Prime Minister of Indonesia</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Indonesia\" title=\"Prime Minister of Indonesia\">Prime Minister of Indonesia</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Indonesia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Indonesia"}]}, {"year": "1980", "text": "<PERSON>, Spanish environmentalist (b. 1928)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADgue<PERSON>_de_la_Fuente\" title=\"<PERSON>\"><PERSON></a>, Spanish environmentalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADgue<PERSON>_de_la_Fuente\" title=\"<PERSON>\"><PERSON></a>, Spanish environmentalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Rodr%C3%ADguez_de_la_Fuente"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian poet (b. 1915)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian poet (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON> of Bourbon-Parma, Empress of Austria and Queen of Hungary (b. 1892)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma\" title=\"<PERSON><PERSON> of Bourbon-Parma\"><PERSON><PERSON> of Bourbon-Parma</a>, <a href=\"https://wikipedia.org/wiki/Empress_of_Austria\" class=\"mw-redirect\" title=\"Empress of Austria\">Empress of Austria</a> and Queen of Hungary (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma\" title=\"<PERSON><PERSON> of Bourbon-Parma\"><PERSON><PERSON> of Bourbon-Parma</a>, <a href=\"https://wikipedia.org/wiki/Empress_of_Austria\" class=\"mw-redirect\" title=\"Empress of Austria\">Empress of Austria</a> and Queen of Hungary (b. 1892)", "links": [{"title": "<PERSON><PERSON> of Bourbon-Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Bourbon-Parma"}, {"title": "Empress of Austria", "link": "https://wikipedia.org/wiki/Empress_of_Austria"}]}, {"year": "1991", "text": "<PERSON>, American playwright and composer (b. 1950)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and composer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and composer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American physicist and astronomer, Nobel Prize laureate (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1997", "text": "<PERSON>, Austrian-American director and producer (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director and producer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American director and producer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actor (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American author (b. 1913)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author (b. 1913)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2003", "text": "<PERSON>, Canadian-American painter (b. 1945)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, French engineer and businessman (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer and businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer and businessman (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Estonian director and politician, 2nd President of Estonia (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri"}, {"title": "President of Estonia", "link": "https://wikipedia.org/wiki/President_of_Estonia"}]}, {"year": "2007", "text": "<PERSON><PERSON>, French educator and activist (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French educator and activist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French educator and activist (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Italian activist, co-founded the Focolare Movement (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian activist, co-founded the <a href=\"https://wikipedia.org/wiki/Focolare_Movement\" title=\"Focolare Movement\">Focolare Movement</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian activist, co-founded the <a href=\"https://wikipedia.org/wiki/Focolare_Movement\" title=\"Focolare Movement\">Focolare Movement</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}, {"title": "Focolare Movement", "link": "https://wikipedia.org/wiki/Focolare_Movement"}]}, {"year": "2010", "text": "<PERSON>, American actor (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French director and screenwriter (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Maltese general and politician, 4th President of Malta (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/%C4%8Aensu_Tabone\" title=\"Ċensu Tabone\">Ċensu <PERSON><PERSON></a>, Maltese general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%8Aensu_Tabone\" title=\"Ċensu Tabone\">Ċensu <PERSON><PERSON></a>, Maltese general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a> (b. 1913)", "links": [{"title": "Ċensu Tabone", "link": "https://wikipedia.org/wiki/%C4%8Aensu_Tabone"}, {"title": "President of Malta", "link": "https://wikipedia.org/wiki/President_of_Malta"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Armenian poet and author (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian poet and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian poet and author (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Vietnamese-Cambodian politician, Cambodian Minister for Foreign Affairs (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese-Cambodian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Cooperation_(Cambodia)\" title=\"Ministry of Foreign Affairs and International Cooperation (Cambodia)\">Cambodian Minister for Foreign Affairs</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese-Cambodian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Cooperation_(Cambodia)\" title=\"Ministry of Foreign Affairs and International Cooperation (Cambodia)\">Cambodian Minister for Foreign Affairs</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs and International Cooperation (Cambodia)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Cooperation_(Cambodia)"}]}, {"year": "2014", "text": "<PERSON>, English politician, Postmaster General of the United Kingdom (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Postmaster General of the United Kingdom", "link": "https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Israeli commander (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli commander (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli commander (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, German-American metallurgist and academic (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American metallurgist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American metallurgist and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English composer and conductor (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician (b. 1949)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>paksha\" title=\"<PERSON><PERSON><PERSON><PERSON> Rajapaksha\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ha\" title=\"<PERSON><PERSON><PERSON><PERSON> Rajapaksha\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Rajapaksha"}]}, {"year": "2018", "text": "<PERSON>, English stand-up comedian and TV personality (b. 1937)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English stand-up comedian and TV personality (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English stand-up comedian and TV personality (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Brazilian politician and human rights activist (b. 1979)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician and human rights activist (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian politician and human rights activist (b. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, English physicist and author (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Irish uileann piper (b. 1945)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flynn\" title=\"<PERSON>\"><PERSON></a>, Irish uileann piper (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flynn\" title=\"<PERSON>\"><PERSON></a>, Irish uileann piper (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Flynn"}]}, {"year": "2019", "text": "<PERSON>, American skateboarder and T<PERSON>sher editor-in-chief (b. 1962)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and Thrasher editor-in-chief (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and Thrasher editor-in-chief (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, British motorsport director (b. 1952)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British motorsport director (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British motorsport director (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Canadian politician (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American wrestler (b. 1958)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Scott Hall\"><PERSON></a>, American wrestler (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scott_<PERSON>\" title=\"Scott Hall\"><PERSON></a>, American wrestler (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_<PERSON>"}]}]}}