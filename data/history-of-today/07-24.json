{"date": "July 24", "url": "https://wikipedia.org/wiki/July_24", "data": {"Events": [{"year": "1132", "text": "Battle of Nocera between <PERSON><PERSON><PERSON> II of Alife and <PERSON> of Sicily.", "html": "1132 - <a href=\"https://wikipedia.org/wiki/Battle_of_Nocera\" title=\"Battle of Nocera\">Battle of Nocera</a> between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Alife\" title=\"<PERSON><PERSON><PERSON> II of Alife\"><PERSON><PERSON><PERSON> of Alife</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Nocera\" title=\"Battle of Nocera\">Battle of Nocera</a> between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Alife\" title=\"<PERSON><PERSON><PERSON> II of Alife\"><PERSON><PERSON><PERSON> of Alife</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a>.", "links": [{"title": "Battle of Nocera", "link": "https://wikipedia.org/wiki/Battle_of_Nocera"}, {"title": "<PERSON><PERSON><PERSON> of Alife", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Alife"}, {"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1148", "text": "<PERSON> of France lays siege to Damascus during the Second Crusade.", "html": "1148 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" title=\"Louis VII of France\">Louis VII of France</a> lays <a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(1148)\" title=\"Siege of Damascus (1148)\">siege to Damascus</a> during the <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" title=\"Louis VII of France\"><PERSON> of France</a> lays <a href=\"https://wikipedia.org/wiki/Siege_of_Damascus_(1148)\" title=\"Siege of Damascus (1148)\">siege to Damascus</a> during the <a href=\"https://wikipedia.org/wiki/Second_Crusade\" title=\"Second Crusade\">Second Crusade</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VII_of_France"}, {"title": "Siege of Damascus (1148)", "link": "https://wikipedia.org/wiki/Siege_of_Damascus_(1148)"}, {"title": "Second Crusade", "link": "https://wikipedia.org/wiki/Second_Crusade"}]}, {"year": "1304", "text": "Wars of Scottish Independence: Fall of Stirling Castle: King <PERSON> of England takes the stronghold using the War Wolf.", "html": "1304 - <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Fall_of_Stirling_Castle\" class=\"mw-redirect\" title=\"Fall of Stirling Castle\">Fall of Stirling Castle</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> takes the stronghold using the <a href=\"https://wikipedia.org/wiki/Warwolf\" title=\"Warwolf\">War Wolf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Fall_of_Stirling_Castle\" class=\"mw-redirect\" title=\"Fall of Stirling Castle\">Fall of Stirling Castle</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> takes the stronghold using the <a href=\"https://wikipedia.org/wiki/Warwolf\" title=\"Warwolf\"><PERSON> Wolf</a>.", "links": [{"title": "Wars of Scottish Independence", "link": "https://wikipedia.org/wiki/Wars_of_Scottish_Independence"}, {"title": "Fall of Stirling Castle", "link": "https://wikipedia.org/wiki/Fall_of_Stirling_Castle"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}, {"title": "Warwolf", "link": "https://wikipedia.org/wiki/Warwolf"}]}, {"year": "1411", "text": "Battle of Harlaw, one of the bloodiest battles in Scotland, takes place.", "html": "1411 - <a href=\"https://wikipedia.org/wiki/Battle_of_Harlaw\" title=\"Battle of Harlaw\">Battle of Harlaw</a>, one of the bloodiest battles in Scotland, takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Harlaw\" title=\"Battle of Harlaw\">Battle of Harlaw</a>, one of the bloodiest battles in Scotland, takes place.", "links": [{"title": "Battle of Harlaw", "link": "https://wikipedia.org/wiki/Battle_of_Harlaw"}]}, {"year": "1412", "text": "<PERSON><PERSON><PERSON> becomes Syriac Orthodox Patriarch of Mardin.", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox</a> Patriarch of Mardin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox</a> Patriarch of Mardin.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Syriac Orthodox Church", "link": "https://wikipedia.org/wiki/Syriac_Orthodox_Church"}]}, {"year": "1487", "text": "Citizens of Leeuwarden, Netherlands, strike against a ban on foreign beer.", "html": "1487 - Citizens of <a href=\"https://wikipedia.org/wiki/Leeuwarden\" title=\"Leeuwarden\">Leeuwarden</a>, Netherlands, strike against a ban on foreign <a href=\"https://wikipedia.org/wiki/Beer\" title=\"Beer\">beer</a>.", "no_year_html": "Citizens of <a href=\"https://wikipedia.org/wiki/Leeuwarden\" title=\"Leeuwarden\">Leeuwarden</a>, Netherlands, strike against a ban on foreign <a href=\"https://wikipedia.org/wiki/Beer\" title=\"Beer\">beer</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leeuwarden"}, {"title": "Beer", "link": "https://wikipedia.org/wiki/Beer"}]}, {"year": "1534", "text": "French explorer <PERSON> plants a cross on the Gaspé Peninsula and takes possession of the territory in the name of <PERSON> of France.", "html": "1534 - French explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> plants a cross on the <a href=\"https://wikipedia.org/wiki/Gasp%C3%A9_Peninsula\" title=\"Gaspé Peninsula\">Gaspé Peninsula</a> and takes possession of the territory in the name of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>.", "no_year_html": "French explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> plants a cross on the <a href=\"https://wikipedia.org/wiki/Gasp%C3%A9_Peninsula\" title=\"Gaspé Peninsula\">Gaspé Peninsula</a> and takes possession of the territory in the name of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Gaspé Peninsula", "link": "https://wikipedia.org/wiki/Gasp%C3%A9_Peninsula"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1567", "text": "<PERSON>, Queen of Scots, is forced to abdicate and be replaced by her one-year-old son <PERSON>.", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is <a href=\"https://wikipedia.org/wiki/Accession_and_Coronation_Act_1567\" title=\"Accession and Coronation Act 1567\">forced to abdicate</a> and be replaced by her one-year-old son <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, is <a href=\"https://wikipedia.org/wiki/Accession_and_Coronation_Act_1567\" title=\"Accession and Coronation Act 1567\">forced to abdicate</a> and be replaced by her one-year-old son <a href=\"https://wikipedia.org/wiki/James_VI_and_I\" title=\"<PERSON> VI and I\"><PERSON> VI</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Accession and Coronation Act 1567", "link": "https://wikipedia.org/wiki/Accession_and_Coronation_Act_1567"}, {"title": "James <PERSON> and I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_I"}]}, {"year": "1701", "text": "<PERSON> la Mothe Cadillac founds the trading post at Fort Pontchartrain, which later becomes the city of Detroit.", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Mothe_Cadillac\" title=\"Antoine de la Mothe Cadillac\"><PERSON> la <PERSON> Cadillac</a> founds the trading post at <a href=\"https://wikipedia.org/wiki/Fort_Detroit\" class=\"mw-redirect\" title=\"Fort Detroit\">Fort Pontchartrain</a>, which later becomes the city of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>the_Cadillac\" title=\"Antoine de la Mothe Cadillac\"><PERSON> Cadillac</a> founds the trading post at <a href=\"https://wikipedia.org/wiki/Fort_Detroit\" class=\"mw-redirect\" title=\"Fort Detroit\">Fort Pontchartrain</a>, which later becomes the city of <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "links": [{"title": "<PERSON> Cadillac", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Fort Detroit", "link": "https://wikipedia.org/wiki/Fort_Detroit"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1712", "text": "War of the Spanish Succession: The French under Marshal <PERSON> win a decisive victory over <PERSON> of Savoy at Denain.", "html": "1712 - <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: The French under <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\">Marshal <PERSON></a> win a <a href=\"https://wikipedia.org/wiki/Battle_of_Denain\" title=\"Battle of Denain\">decisive victory</a> over <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy\" class=\"mw-redirect\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> at Denain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a>: The French under <a href=\"https://wikipedia.org/wiki/Marshal_<PERSON>\" class=\"mw-redirect\" title=\"Marshal <PERSON>\">Marshal <PERSON></a> win a <a href=\"https://wikipedia.org/wiki/Battle_of_Denain\" title=\"Battle of Denain\">decisive victory</a> over <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy\" class=\"mw-redirect\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> at Denain.", "links": [{"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Denain", "link": "https://wikipedia.org/wiki/Battle_of_Denain"}, {"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Savoy"}]}, {"year": "1847", "text": "After 17 months of travel, <PERSON> leads 148 Mormon pioneers into Salt Lake Valley, resulting in the establishment of Salt Lake City.", "html": "1847 - After 17 months of travel, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a> leads 148 <a href=\"https://wikipedia.org/wiki/Mormons\" title=\"Mormons\">Mormon</a> pioneers into <a href=\"https://wikipedia.org/wiki/Salt_Lake_Valley\" title=\"Salt Lake Valley\">Salt Lake Valley</a>, resulting in the establishment of <a href=\"https://wikipedia.org/wiki/Salt_Lake_City\" title=\"Salt Lake City\">Salt Lake City</a>.", "no_year_html": "After 17 months of travel, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads 148 <a href=\"https://wikipedia.org/wiki/Mormons\" title=\"Mormons\">Mormon</a> pioneers into <a href=\"https://wikipedia.org/wiki/Salt_Lake_Valley\" title=\"Salt Lake Valley\">Salt Lake Valley</a>, resulting in the establishment of <a href=\"https://wikipedia.org/wiki/Salt_Lake_City\" title=\"Salt Lake City\">Salt Lake City</a>.", "links": [{"title": "<PERSON> Young", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}, {"title": "Mormons", "link": "https://wikipedia.org/wiki/Mormons"}, {"title": "Salt Lake Valley", "link": "https://wikipedia.org/wiki/Salt_Lake_Valley"}, {"title": "Salt Lake City", "link": "https://wikipedia.org/wiki/Salt_Lake_City"}]}, {"year": "1847", "text": "<PERSON>,  American inventor, patented the rotary-type printing press.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, patented the rotary-type printing press.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, patented the rotary-type printing press.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "American Civil War: Battle of Kernstown: Confederate General <PERSON><PERSON> defeats Union troops led by General <PERSON> in an effort to keep them out of the Shenandoah Valley.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Kernstown\" title=\"Second Battle of Kernstown\">Battle of Kernstown</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/General_(CSA)\" class=\"mw-redirect\" title=\"General (CSA)\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON> Early\"><PERSON><PERSON> Early</a> defeats <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in an effort to keep them out of the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Kernstown\" title=\"Second Battle of Kernstown\">Battle of Kernstown</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/General_(CSA)\" class=\"mw-redirect\" title=\"General (CSA)\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> Early</a> defeats <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in an effort to keep them out of the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Second Battle of Kernstown", "link": "https://wikipedia.org/wiki/Second_Battle_of_Kernstown"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "General (CSA)", "link": "https://wikipedia.org/wiki/General_(CSA)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shenandoah Valley", "link": "https://wikipedia.org/wiki/Shenandoah_Valley"}]}, {"year": "1866", "text": "Reconstruction: Tennessee becomes the first U.S. state to be readmitted to Congress following the American Civil War.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction</a>: <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> becomes the first <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> to be readmitted to Congress following the American Civil War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction</a>: <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> becomes the first <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> to be readmitted to Congress following the American Civil War.", "links": [{"title": "Reconstruction Era", "link": "https://wikipedia.org/wiki/Reconstruction_Era"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1901", "text": "<PERSON><PERSON> is released from prison in Columbus, Ohio, after serving three years for embezzlement from a bank.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is released from prison in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, after serving three years for <a href=\"https://wikipedia.org/wiki/Embezzlement\" title=\"Embezzlement\">embezzlement</a> from a bank.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is released from prison in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, after serving three years for <a href=\"https://wikipedia.org/wiki/Embezzlement\" title=\"Embezzlement\">embezzlement</a> from a bank.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Columbus, Ohio", "link": "https://wikipedia.org/wiki/Columbus,_Ohio"}, {"title": "Embezzlement", "link": "https://wikipedia.org/wiki/Embezzlement"}]}, {"year": "1910", "text": "The Ottoman Empire captures the city of Shkodër, putting down the Albanian Revolt of 1910.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> captures the city of <a href=\"https://wikipedia.org/wiki/Shkod%C3%ABr\" title=\"Shkodër\">Shko<PERSON><PERSON><PERSON></a>, putting down the <a href=\"https://wikipedia.org/wiki/Albanian_Revolt_of_1910\" class=\"mw-redirect\" title=\"Albanian Revolt of 1910\">Albanian Revolt of 1910</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> captures the city of <a href=\"https://wikipedia.org/wiki/Shkod%C3%ABr\" title=\"Shkodër\">Shkodër</a>, putting down the <a href=\"https://wikipedia.org/wiki/Albanian_Revolt_of_1910\" class=\"mw-redirect\" title=\"Albanian Revolt of 1910\">Albanian Revolt of 1910</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Shkodër", "link": "https://wikipedia.org/wiki/Shkod%C3%ABr"}, {"title": "Albanian Revolt of 1910", "link": "https://wikipedia.org/wiki/Albanian_Revolt_of_1910"}]}, {"year": "1911", "text": "<PERSON><PERSON> re-discovers <PERSON><PERSON>, \"the Lost City of the Incas\".", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> III\"><PERSON><PERSON></a> re-discovers <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON>hu\" title=\"<PERSON><PERSON> Pi<PERSON>hu\"><PERSON><PERSON></a>, \"the Lost City of the <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Incas</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> III\"><PERSON><PERSON></a> re-discovers <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pi<PERSON>hu\" title=\"<PERSON><PERSON> Pi<PERSON>hu\"><PERSON><PERSON></a>, \"the Lost City of the <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Incas</a>\".", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Machu_Pi<PERSON>hu"}, {"title": "Inca Empire", "link": "https://wikipedia.org/wiki/Inca_Empire"}]}, {"year": "1915", "text": "The passenger ship SS Eastland capsizes while tied to a dock in the Chicago River. A total of 844 passengers and crew are killed in the largest loss of life disaster from a single shipwreck on the Great Lakes.", "html": "1915 - The passenger ship <a href=\"https://wikipedia.org/wiki/SS_Eastland\" title=\"SS Eastland\">SS <i>Eastland</i></a> capsizes while tied to a dock in the Chicago River. A total of 844 passengers and crew are killed in the largest loss of life disaster from a single shipwreck on the <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a>.", "no_year_html": "The passenger ship <a href=\"https://wikipedia.org/wiki/SS_Eastland\" title=\"SS Eastland\">SS <i>Eastland</i></a> capsizes while tied to a dock in the Chicago River. A total of 844 passengers and crew are killed in the largest loss of life disaster from a single shipwreck on the <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a>.", "links": [{"title": "SS Eastland", "link": "https://wikipedia.org/wiki/SS_Eastland"}, {"title": "Great Lakes", "link": "https://wikipedia.org/wiki/Great_Lakes"}]}, {"year": "1922", "text": "The draft of the British Mandate of Palestine was formally confirmed by the Council of the League of Nations; it came into effect on 26 September 1923.", "html": "1922 - The draft of the <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">British Mandate of Palestine</a> was formally confirmed by the <a href=\"https://wikipedia.org/wiki/League_of_Nations#Council\" title=\"League of Nations\">Council of the League of Nations</a>; it came into effect on 26 September 1923.", "no_year_html": "The draft of the <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">British Mandate of Palestine</a> was formally confirmed by the <a href=\"https://wikipedia.org/wiki/League_of_Nations#Council\" title=\"League of Nations\">Council of the League of Nations</a>; it came into effect on 26 September 1923.", "links": [{"title": "Mandatory Palestine", "link": "https://wikipedia.org/wiki/Mandatory_Palestine"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations#Council"}]}, {"year": "1923", "text": "The Treaty of Lausanne, settling the boundaries of modern Turkey, is signed in Switzerland by Greece, Bulgaria and other countries that fought in World War I.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lausanne_(1923)\" class=\"mw-redirect\" title=\"Treaty of Lausanne (1923)\">Treaty of Lausanne</a>, settling the boundaries of modern Turkey, is signed in Switzerland by Greece, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and other countries that fought in <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lausanne_(1923)\" class=\"mw-redirect\" title=\"Treaty of Lausanne (1923)\">Treaty of Lausanne</a>, settling the boundaries of modern Turkey, is signed in Switzerland by Greece, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> and other countries that fought in <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "links": [{"title": "Treaty of Lausanne (1923)", "link": "https://wikipedia.org/wiki/Treaty_of_Lausanne_(1923)"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON> becomes Prime Minister of Greece.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Themistoklis_Sofoulis\" title=\"Themistoklis Sofoulis\">Themistok<PERSON> Sofoulis</a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Themistoklis_Sofoulis\" title=\"Themistoklis Sofoulis\">Themistoklis Sofoulis</a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Themistok<PERSON>_So<PERSON>ulis"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1927", "text": "The Menin Gate war memorial is unveiled at Ypres.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Menin_Gate\" title=\"Menin Gate\">Menin Gate</a> war memorial is unveiled at <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Menin_Gate\" title=\"Menin Gate\">Menin Gate</a> war memorial is unveiled at <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a>.", "links": [{"title": "Menin Gate", "link": "https://wikipedia.org/wiki/Menin_Gate"}, {"title": "Ypres", "link": "https://wikipedia.org/wiki/Ypres"}]}, {"year": "1929", "text": "The Kellogg-Briand Pact, renouncing war as an instrument of foreign policy, goes into effect (it is first signed in Paris on August 27, 1928, by most leading world powers).", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact\" title=\"Kellogg-Briand Pact\">Kellogg-Briand Pact</a>, renouncing war as an instrument of <a href=\"https://wikipedia.org/wiki/Foreign_policy\" title=\"Foreign policy\">foreign policy</a>, goes into effect (it is first signed in Paris on August 27, 1928, by most leading world powers).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact\" title=\"Kellogg-Briand Pact\">Kellogg-Briand Pact</a>, renouncing war as an instrument of <a href=\"https://wikipedia.org/wiki/Foreign_policy\" title=\"Foreign policy\">foreign policy</a>, goes into effect (it is first signed in Paris on August 27, 1928, by most leading world powers).", "links": [{"title": "Kellogg-Briand Pact", "link": "https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact"}, {"title": "Foreign policy", "link": "https://wikipedia.org/wiki/Foreign_policy"}]}, {"year": "1935", "text": "The Dust Bowl heat wave reaches its peak, sending temperatures to 109 °F (43 °C) in Chicago and 104 °F (40 °C) in Milwaukee.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Dust_Bowl\" title=\"Dust Bowl\">Dust Bowl</a> heat wave reaches its peak, sending temperatures to 109 °F (43 °C) in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> and 104 °F (40 °C) in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dust_Bowl\" title=\"Dust Bowl\">Dust Bowl</a> heat wave reaches its peak, sending temperatures to 109 °F (43 °C) in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> and 104 °F (40 °C) in <a href=\"https://wikipedia.org/wiki/Milwaukee\" title=\"Milwaukee\">Milwaukee</a>.", "links": [{"title": "Dust Bowl", "link": "https://wikipedia.org/wiki/Dust_Bowl"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "Milwaukee", "link": "https://wikipedia.org/wiki/Milwaukee"}]}, {"year": "1943", "text": "World War II: Operation Gomorrah begins: British and Canadian aeroplanes bomb Hamburg by night, and American planes bomb the city by day. By the end of the operation in November, 9,000 tons of explosives will have killed more than 30,000 people and destroyed 280,000 buildings.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">Operation Gomorrah</a> begins: British and Canadian aeroplanes bomb <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a> by night, and American planes bomb the city by day. By the end of the operation in <a href=\"https://wikipedia.org/wiki/November\" title=\"November\">November</a>, 9,000 tons of explosives will have killed more than 30,000 people and destroyed 280,000 buildings.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">Operation Gomorrah</a> begins: British and Canadian aeroplanes bomb <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a> by night, and American planes bomb the city by day. By the end of the operation in <a href=\"https://wikipedia.org/wiki/November\" title=\"November\">November</a>, 9,000 tons of explosives will have killed more than 30,000 people and destroyed 280,000 buildings.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bombing of Hamburg in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}, {"title": "November", "link": "https://wikipedia.org/wiki/November"}]}, {"year": "1950", "text": "Cape Canaveral Air Force Station begins operations with the launch of a Bumper rocket.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral Air Force Station</a> begins operations with the launch of a <a href=\"https://wikipedia.org/wiki/RTV-G-4_Bumper\" title=\"RTV-G-4 Bumper\">Bumper</a> rocket.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral Air Force Station</a> begins operations with the launch of a <a href=\"https://wikipedia.org/wiki/RTV-G-4_Bumper\" title=\"RTV-G-4 Bumper\">Bumper</a> rocket.", "links": [{"title": "Cape Canaveral Air Force Station", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station"}, {"title": "RTV-G-4 <PERSON><PERSON>er", "link": "https://wikipedia.org/wiki/RTV-G-4_Bumper"}]}, {"year": "1959", "text": "At the opening of the American National Exhibition in Moscow, U.S. Vice President <PERSON> and Soviet Premier <PERSON><PERSON> have a \"Kitchen Debate\".", "html": "1959 - At the opening of the <a href=\"https://wikipedia.org/wiki/American_National_Exhibition\" title=\"American National Exhibition\">American National Exhibition</a> in Moscow, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">U.S. Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet <a href=\"https://wikipedia.org/wiki/Premier\" title=\"Premier\">Premier</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> have a \"<a href=\"https://wikipedia.org/wiki/Kitchen_Debate\" title=\"Kitchen Debate\">Kitchen Debate</a>\".", "no_year_html": "At the opening of the <a href=\"https://wikipedia.org/wiki/American_National_Exhibition\" title=\"American National Exhibition\">American National Exhibition</a> in Moscow, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">U.S. Vice President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet <a href=\"https://wikipedia.org/wiki/Premier\" title=\"Premier\">Premier</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> have a \"<a href=\"https://wikipedia.org/wiki/Kitchen_Debate\" title=\"Kitchen Debate\">Kitchen Debate</a>\".", "links": [{"title": "American National Exhibition", "link": "https://wikipedia.org/wiki/American_National_Exhibition"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier", "link": "https://wikipedia.org/wiki/Premier"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Kitchen Debate", "link": "https://wikipedia.org/wiki/Kitchen_Debate"}]}, {"year": "1963", "text": "The ship Bluenose II was launched in Lunenburg, Nova Scotia. The schooner is a major Canadian symbol.", "html": "1963 - The ship <i><a href=\"https://wikipedia.org/wiki/Bluenose_II\" title=\"Bluenose II\">Bluenose II</a></i> was launched in <a href=\"https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia\" title=\"Lunenburg, Nova Scotia\">Lunenburg, Nova Scotia</a>. The schooner is a major Canadian symbol.", "no_year_html": "The ship <i><a href=\"https://wikipedia.org/wiki/Bluenose_II\" title=\"Bluenose II\">Bluenose II</a></i> was launched in <a href=\"https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia\" title=\"Lunenburg, Nova Scotia\">Lunenburg, Nova Scotia</a>. The schooner is a major Canadian symbol.", "links": [{"title": "Bluenose II", "link": "https://wikipedia.org/wiki/Bluenose_II"}, {"title": "Lunenburg, Nova Scotia", "link": "https://wikipedia.org/wiki/Lunenburg,_Nova_Scotia"}]}, {"year": "1966", "text": "<PERSON> makes the first BASE jump from El Capitan along with <PERSON>. Both came out with broken bones. BASE jumping has now been banned from El Cap.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE jump</a> from <a href=\"https://wikipedia.org/wiki/El_Capitan\" title=\"El Capitan\">El Capitan</a> along with <PERSON>. Both came out with broken bones. BASE jumping has now been banned from El Cap.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes the first <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE jump</a> from <a href=\"https://wikipedia.org/wiki/El_Capitan\" title=\"El Capitan\">El Capitan</a> along with <PERSON>. Both came out with broken bones. BASE jumping has now been banned from El Cap.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BASE jumping", "link": "https://wikipedia.org/wiki/BASE_jumping"}, {"title": "El Capitan", "link": "https://wikipedia.org/wiki/El_Capitan"}]}, {"year": "1967", "text": "During an official state visit to Canada, French President <PERSON> declares to a crowd of over 100,000 in Montreal: Vive le Québec libre! (\"Long live free Quebec!\"); the statement angered the Canadian government and many Anglophone Canadians.", "html": "1967 - During an official state visit to Canada, French President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares to a crowd of over 100,000 in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>: <i><a href=\"https://wikipedia.org/wiki/Vive_le_Qu%C3%A9bec_libre\" title=\"Vive le Québec libre\">Vive le Québec libre</a>!</i> (\"Long live free Quebec!\"); the statement angered the Canadian government and many <a href=\"https://wikipedia.org/wiki/Anglophone_Canadians\" class=\"mw-redirect\" title=\"Anglophone Canadians\">Anglophone Canadians</a>.", "no_year_html": "During an official state visit to Canada, French President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares to a crowd of over 100,000 in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>: <i><a href=\"https://wikipedia.org/wiki/Vive_le_Qu%C3%A9bec_libre\" title=\"Vive le Québec libre\">Vive le Québec libre</a>!</i> (\"Long live free Quebec!\"); the statement angered the Canadian government and many <a href=\"https://wikipedia.org/wiki/Anglophone_Canadians\" class=\"mw-redirect\" title=\"Anglophone Canadians\">Anglophone Canadians</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "Vive le Québec libre", "link": "https://wikipedia.org/wiki/Vive_le_Qu%C3%A9bec_libre"}, {"title": "Anglophone Canadians", "link": "https://wikipedia.org/wiki/Anglophone_Canadians"}]}, {"year": "1969", "text": "Apollo program: Apollo 11 splashes down safely in the Pacific Ocean.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> splashes down safely in the Pacific Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> splashes down safely in the Pacific Ocean.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}]}, {"year": "1974", "text": "Watergate scandal: The United States Supreme Court unanimously ruled that President <PERSON> did not have the authority to withhold subpoenaed White House tapes and they order him to surrender the tapes to the Watergate special prosecutor.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> unanimously ruled that <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> did not have the authority to withhold subpoenaed <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> tapes and they <a href=\"https://wikipedia.org/wiki/United_States_v<PERSON>_<PERSON>\" title=\"United States v<PERSON>\">order him</a> to surrender the tapes to the Watergate special prosecutor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> unanimously ruled that <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> did not have the authority to withhold subpoenaed <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> tapes and they <a href=\"https://wikipedia.org/wiki/United_States_v<PERSON>_<PERSON>\" title=\"United States v<PERSON> <PERSON>\">order him</a> to surrender the tapes to the Watergate special prosecutor.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "United States v. <PERSON>", "link": "https://wikipedia.org/wiki/United_States_v._<PERSON>"}]}, {"year": "1977", "text": "End of a four-day-long Libyan-Egyptian War.", "html": "1977 - End of a four-day-long <a href=\"https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War\" class=\"mw-redirect\" title=\"Libyan-Egyptian War\">Libyan-Egyptian War</a>.", "no_year_html": "End of a four-day-long <a href=\"https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War\" class=\"mw-redirect\" title=\"Libyan-Egyptian War\">Libyan-Egyptian War</a>.", "links": [{"title": "Libyan-Egyptian War", "link": "https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War"}]}, {"year": "1980", "text": "The Quietly Confident Quartet of Australia wins the men's 4 x 100 metre medley relay at the Moscow Olympics, the only time the United States has not won the event at Olympic level.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/Quietly_Confident_Quartet\" title=\"Quietly Confident Quartet\">Quietly Confident Quartet</a> of <a href=\"https://wikipedia.org/wiki/Australia_at_the_1980_Summer_Olympics\" title=\"Australia at the 1980 Summer Olympics\">Australia</a> wins the <a href=\"https://wikipedia.org/wiki/Swimming_at_the_1980_Summer_Olympics_%E2%80%93_Men%27s_4_%C3%97_100_metre_medley_relay\" title=\"Swimming at the 1980 Summer Olympics - Men's 4 × 100 metre medley relay\">men's 4 x 100 metre medley relay</a> at the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">Moscow Olympics</a>, the only time the United States has not won the event at Olympic level.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Quietly_Confident_Quartet\" title=\"Quietly Confident Quartet\">Quietly Confident Quartet</a> of <a href=\"https://wikipedia.org/wiki/Australia_at_the_1980_Summer_Olympics\" title=\"Australia at the 1980 Summer Olympics\">Australia</a> wins the <a href=\"https://wikipedia.org/wiki/Swimming_at_the_1980_Summer_Olympics_%E2%80%93_Men%27s_4_%C3%97_100_metre_medley_relay\" title=\"Swimming at the 1980 Summer Olympics - Men's 4 × 100 metre medley relay\">men's 4 x 100 metre medley relay</a> at the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">Moscow Olympics</a>, the only time the United States has not won the event at Olympic level.", "links": [{"title": "Quietly Confident Quartet", "link": "https://wikipedia.org/wiki/Quietly_Confident_Quartet"}, {"title": "Australia at the 1980 Summer Olympics", "link": "https://wikipedia.org/wiki/Australia_at_the_1980_Summer_Olympics"}, {"title": "Swimming at the 1980 Summer Olympics - Men's 4 × 100 metre medley relay", "link": "https://wikipedia.org/wiki/Swimming_at_the_1980_Summer_Olympics_%E2%80%93_Men%27s_4_%C3%97_100_metre_medley_relay"}, {"title": "1980 Summer Olympics", "link": "https://wikipedia.org/wiki/1980_Summer_Olympics"}]}, {"year": "1982", "text": "Heavy rain causes a mudslide that destroys a bridge at Nagasaki, Japan, killing 299.", "html": "1982 - Heavy rain causes a mudslide that destroys a bridge at <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a>, Japan, killing 299.", "no_year_html": "Heavy rain causes a mudslide that destroys a bridge at <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a>, Japan, killing 299.", "links": [{"title": "Nagasaki", "link": "https://wikipedia.org/wiki/Nagasaki"}]}, {"year": "1983", "text": "The Black July anti-Tamil riots begin in Sri Lanka, killing between 400 and 3,000. Black July is generally regarded as the beginning of the Sri Lankan Civil War.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a> anti-Tamil riots begin in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing between 400 and 3,000. Black July is generally regarded as the beginning of the <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Black_July\" title=\"Black July\">Black July</a> anti-Tamil riots begin in <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing between 400 and 3,000. Black July is generally regarded as the beginning of the <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>.", "links": [{"title": "Black July", "link": "https://wikipedia.org/wiki/Black_July"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}]}, {"year": "1983", "text": "<PERSON> playing for the Kansas City Royals against the New York Yankees, has a game-winning home run nullified in the \"Pine Tar Incident\".", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> playing for the <a href=\"https://wikipedia.org/wiki/Kansas_City_Royals\" title=\"Kansas City Royals\">Kansas City Royals</a> against the <a href=\"https://wikipedia.org/wiki/New_York_Yankees\" title=\"New York Yankees\">New York Yankees</a>, has a game-winning home run nullified in the \"<a href=\"https://wikipedia.org/wiki/Pine_Tar_Incident\" title=\"Pine Tar Incident\">Pine Tar Incident</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> playing for the <a href=\"https://wikipedia.org/wiki/Kansas_City_Royals\" title=\"Kansas City Royals\">Kansas City Royals</a> against the <a href=\"https://wikipedia.org/wiki/New_York_Yankees\" title=\"New York Yankees\">New York Yankees</a>, has a game-winning home run nullified in the \"<a href=\"https://wikipedia.org/wiki/Pine_Tar_Incident\" title=\"Pine Tar Incident\">Pine Tar Incident</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kansas City Royals", "link": "https://wikipedia.org/wiki/Kansas_City_Royals"}, {"title": "New York Yankees", "link": "https://wikipedia.org/wiki/New_York_Yankees"}, {"title": "Pine Tar Incident", "link": "https://wikipedia.org/wiki/Pine_Tar_Incident"}]}, {"year": "1987", "text": "US supertanker SS Bridgeton collides with mines laid by IRGC causing a 43-square-meter dent in the body of the oil tanker.", "html": "1987 - US supertanker <a href=\"https://wikipedia.org/wiki/SS_Bridgeton\" title=\"SS Bridgeton\">SS <i>Bridgeton</i></a> <a href=\"https://wikipedia.org/wiki/Bridgeton_incident\" title=\"Bridgeton incident\">collides</a> with mines laid by <a href=\"https://wikipedia.org/wiki/IRGC\" class=\"mw-redirect\" title=\"IRGC\">IRGC</a> causing a 43-square-meter dent in the body of the oil tanker.", "no_year_html": "US supertanker <a href=\"https://wikipedia.org/wiki/SS_Bridgeton\" title=\"SS Bridgeton\">SS <i>Bridgeton</i></a> <a href=\"https://wikipedia.org/wiki/Bridgeton_incident\" title=\"Bridgeton incident\">collides</a> with mines laid by <a href=\"https://wikipedia.org/wiki/IRGC\" class=\"mw-redirect\" title=\"IRGC\">IRGC</a> causing a 43-square-meter dent in the body of the oil tanker.", "links": [{"title": "SS <PERSON>", "link": "https://wikipedia.org/wiki/SS_Bridgeton"}, {"title": "Bridgeton incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_incident"}, {"title": "IRGC", "link": "https://wikipedia.org/wiki/IRGC"}]}, {"year": "1987", "text": "<PERSON><PERSON>, at 91 years of age, climbed Mt. Fuji. <PERSON><PERSON><PERSON> became the oldest person to climb Japan's highest peak.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rooks\" title=\"<PERSON><PERSON> Crooks\"><PERSON><PERSON></a>, at 91 years of age, climbed Mt. Fuji. <PERSON><PERSON><PERSON> became the oldest person to climb Japan's highest peak.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rook<PERSON>\" title=\"<PERSON><PERSON> Crooks\"><PERSON><PERSON></a>, at 91 years of age, climbed Mt. Fuji. <PERSON><PERSON><PERSON> became the oldest person to climb Japan's highest peak.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1998", "text": "<PERSON> bursts into the United States Capitol and opens fire killing two police officers. He is later ruled to be incompetent to stand trial.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/1998_United_States_Capitol_shooting_incident\" class=\"mw-redirect\" title=\"1998 United States Capitol shooting incident\"><PERSON>.</a> bursts into the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> and opens fire killing two police officers. He is later ruled to be incompetent to stand trial.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1998_United_States_Capitol_shooting_incident\" class=\"mw-redirect\" title=\"1998 United States Capitol shooting incident\"><PERSON>.</a> bursts into the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> and opens fire killing two police officers. He is later ruled to be incompetent to stand trial.", "links": [{"title": "1998 United States Capitol shooting incident", "link": "https://wikipedia.org/wiki/1998_United_States_Capitol_shooting_incident"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1999", "text": "Air Fiji flight 121 crashes while en route to Nadi, Fiji, killing all 17 people on board.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Air_Fiji_Flight_121\" title=\"Air Fiji Flight 121\">Air Fiji flight 121</a> crashes while en route to <a href=\"https://wikipedia.org/wiki/Nadi\" title=\"Nadi\">Nadi</a>, Fiji, killing all 17 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Fiji_Flight_121\" title=\"Air Fiji Flight 121\">Air Fiji flight 121</a> crashes while en route to <a href=\"https://wikipedia.org/wiki/Nadi\" title=\"Nadi\">Nadi</a>, Fiji, killing all 17 people on board.", "links": [{"title": "Air Fiji Flight 121", "link": "https://wikipedia.org/wiki/Air_Fiji_Flight_121"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadi"}]}, {"year": "2001", "text": "The Bandaranaike Airport attack is carried out by 14 Tamil Tiger commandos. Eleven civilian and military aircraft are destroyed and 15 are damaged. All 14 commandos are shot dead, while seven soldiers from the Sri Lanka Air Force are killed. In addition, three civilians and an engineer die. This incident slowed the Sri Lankan economy.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/Bandaranaike_Airport_attack\" title=\"Bandaranaike Airport attack\">Bandaranaike Airport attack</a> is carried out by 14 <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Tamil Tiger</a> commandos. Eleven civilian and military aircraft are destroyed and 15 are damaged. All 14 commandos are shot dead, while seven soldiers from the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_Force\" title=\"Sri Lanka Air Force\">Sri Lanka Air Force</a> are killed. In addition, three civilians and an engineer die. This incident slowed the <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lankan</a> economy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bandaranaike_Airport_attack\" title=\"Bandaranaike Airport attack\">Bandaranaike Airport attack</a> is carried out by 14 <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Tamil Tiger</a> commandos. Eleven civilian and military aircraft are destroyed and 15 are damaged. All 14 commandos are shot dead, while seven soldiers from the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Air_Force\" title=\"Sri Lanka Air Force\">Sri Lanka Air Force</a> are killed. In addition, three civilians and an engineer die. This incident slowed the <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lankan</a> economy.", "links": [{"title": "Bandaranaike Airport attack", "link": "https://wikipedia.org/wiki/Bandaranaike_Airport_attack"}, {"title": "Liberation Tigers of Tamil Eelam", "link": "https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam"}, {"title": "Sri Lanka Air Force", "link": "https://wikipedia.org/wiki/Sri_Lanka_Air_Force"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "2009", "text": "Aria Air Flight 1525 crashes at Mashhad International Airport, killing 16.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Aria_Air_Flight_1525\" title=\"Aria Air Flight 1525\">Aria Air Flight 1525</a> crashes at <a href=\"https://wikipedia.org/wiki/Mashhad_International_Airport\" class=\"mw-redirect\" title=\"Mashhad International Airport\">Mashhad International Airport</a>, killing 16.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aria_Air_Flight_1525\" title=\"Aria Air Flight 1525\">Aria Air Flight 1525</a> crashes at <a href=\"https://wikipedia.org/wiki/Mashhad_International_Airport\" class=\"mw-redirect\" title=\"Mashhad International Airport\">Mashhad International Airport</a>, killing 16.", "links": [{"title": "Aria Air Flight 1525", "link": "https://wikipedia.org/wiki/Aria_Air_Flight_1525"}, {"title": "Mashhad International Airport", "link": "https://wikipedia.org/wiki/Mashhad_International_Airport"}]}, {"year": "2012", "text": "Syrian civil war: The People's Protection Units (YPG) capture the city of Girkê Legê.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the city of <a href=\"https://wikipedia.org/wiki/Al-Ma%27bada\" class=\"mw-redirect\" title=\"Al<PERSON><PERSON>'bada\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the city of <a href=\"https://wikipedia.org/wiki/Al-Ma%27bada\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'bada\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Ma%27bada"}]}, {"year": "2013", "text": "Santiago de Compostela derailment: A high-speed train derails in Spain rounding a curve with an 80 km/h (50 mph) speed limit at 190 km/h (120 mph), killing 78 passengers.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Santiago_de_Compostela_derailment\" title=\"Santiago de Compostela derailment\">Santiago de Compostela derailment</a>: A high-speed train derails in Spain rounding a curve with an 80 km/h (50 mph) speed limit at 190 km/h (120 mph), killing 78 passengers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_de_Compostela_derailment\" title=\"Santiago de Compostela derailment\">Santiago de Compostela derailment</a>: A high-speed train derails in Spain rounding a curve with an 80 km/h (50 mph) speed limit at 190 km/h (120 mph), killing 78 passengers.", "links": [{"title": "Santiago de Compostela derailment", "link": "https://wikipedia.org/wiki/Santiago_de_Compostela_derailment"}]}, {"year": "2014", "text": "Air Algérie Flight 5017 loses contact with air traffic controllers 50 minutes after takeoff. It was travelling between Ouagadougou, Burkina Faso and Algiers. The wreckage is later found in Mali. All 116 people on board are killed.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Air_Alg%C3%A9rie_Flight_5017\" title=\"Air Algérie Flight 5017\">Air Algérie Flight 5017</a> loses contact with air traffic controllers 50 minutes after takeoff. It was travelling between <a href=\"https://wikipedia.org/wiki/Ouagadougou\" title=\"Ouagadougou\">Ouagadougou</a>, <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a> and <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>. The wreckage is later found in <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>. All 116 people on board are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Alg%C3%A9rie_Flight_5017\" title=\"Air Algérie Flight 5017\">Air Algérie Flight 5017</a> loses contact with air traffic controllers 50 minutes after takeoff. It was travelling between <a href=\"https://wikipedia.org/wiki/Ouagadougou\" title=\"Ouagadougou\">Ouagadougou</a>, <a href=\"https://wikipedia.org/wiki/Burkina_Faso\" title=\"Burkina Faso\">Burkina Faso</a> and <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a>. The wreckage is later found in <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>. All 116 people on board are killed.", "links": [{"title": "Air Algérie Flight 5017", "link": "https://wikipedia.org/wiki/Air_Alg%C3%A9rie_Flight_5017"}, {"title": "Ouagadougou", "link": "https://wikipedia.org/wiki/Ouagadougou"}, {"title": "Burkina Faso", "link": "https://wikipedia.org/wiki/Burkina_Faso"}, {"title": "Algiers", "link": "https://wikipedia.org/wiki/Algiers"}, {"title": "Mali", "link": "https://wikipedia.org/wiki/Mali"}]}, {"year": "2019", "text": "<PERSON> becomes Prime Minister of the United Kingdom after defeating <PERSON> in a leadership contest, succeeding <PERSON>.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> after defeating <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/2019_Conservative_Party_leadership_election\" title=\"2019 Conservative Party leadership election\">leadership contest</a>, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> after defeating <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a <a href=\"https://wikipedia.org/wiki/2019_Conservative_Party_leadership_election\" title=\"2019 Conservative Party leadership election\">leadership contest</a>, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2019 Conservative Party leadership election", "link": "https://wikipedia.org/wiki/2019_Conservative_Party_leadership_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "2024", "text": "A Saurya Airlines Bombardier CRJ200 crashes during takeoff from Tribhuvan International Airport in Kathmandu, Nepal killing 18.", "html": "2024 - A <a href=\"https://wikipedia.org/wiki/Saurya_Airlines\" title=\"Saurya Airlines\">Saurya Airlines</a> <a href=\"https://wikipedia.org/wiki/Bombardier_CRJ100/200\" title=\"Bombardier CRJ100/200\">Bombardier CRJ200</a> <a href=\"https://wikipedia.org/wiki/2024_Saurya_Airlines_Bombardier_CRJ200_crash\" title=\"2024 Saurya Airlines Bombardier CRJ200 crash\">crashes</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Tribhuvan_International_Airport\" title=\"Tribhuvan International Airport\">Tribhuvan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a>, Nepal killing 18.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Saurya_Airlines\" title=\"Saurya Airlines\">Saurya Airlines</a> <a href=\"https://wikipedia.org/wiki/Bombardier_CRJ100/200\" title=\"Bombardier CRJ100/200\">Bombardier CRJ200</a> <a href=\"https://wikipedia.org/wiki/2024_Saurya_Airlines_Bombardier_CRJ200_crash\" title=\"2024 Saurya Airlines Bombardier CRJ200 crash\">crashes</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Tribhuvan_International_Airport\" title=\"Tribhuvan International Airport\">Tribhuvan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a>, Nepal killing 18.", "links": [{"title": "Saurya Airlines", "link": "https://wikipedia.org/wiki/Saurya_Airlines"}, {"title": "Bombardier CRJ100/200", "link": "https://wikipedia.org/wiki/Bombardier_CRJ100/200"}, {"title": "2024 Saurya Airlines Bombardier CRJ200 crash", "link": "https://wikipedia.org/wiki/2024_Saurya_Airlines_Bombardier_CRJ200_crash"}, {"title": "Tribhuvan International Airport", "link": "https://wikipedia.org/wiki/Tribhuvan_International_Airport"}, {"title": "Kathman<PERSON>", "link": "https://wikipedia.org/wiki/Kathmandu"}]}], "Births": [{"year": "1242", "text": "<PERSON>, German Roman Catholic mystic, ecstatic, and stigmatic (d. 1312)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Roman Catholic mystic, ecstatic, and stigmatic (d. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Roman Catholic mystic, ecstatic, and stigmatic (d. 1312)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1468", "text": "<PERSON> of Saxony, Archduchess of Austria (d. 1524)", "html": "1468 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Archduchess_of_Austria\" title=\"<PERSON> of Saxony, Archduchess of Austria\"><PERSON> of Saxony, Archduchess of Austria</a> (d. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony,_Archduchess_of_Austria\" title=\"<PERSON> of Saxony, Archduchess of Austria\"><PERSON> of Saxony, Archduchess of Austria</a> (d. 1524)", "links": [{"title": "<PERSON> of Saxony, Archduchess of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony,_Archduchess_of_Austria"}]}, {"year": "1529", "text": "<PERSON>, Margrave of Baden-Durlach (d. 1577)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Baden-Durlach\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach\" title=\"<PERSON>, Margrave of Baden-Durlach\"><PERSON>, Margrave of Baden-Durlach</a> (d. 1577)", "links": [{"title": "<PERSON>, Margrave of Baden-Durlach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Durlach"}]}, {"year": "1561", "text": "<PERSON> of the Palatinate-Simmern (d. 1589)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/Maria_of_the_Palatinate-Simmern\" class=\"mw-redirect\" title=\"Maria of the Palatinate-Simmern\"><PERSON> of the Palatinate-Simmern</a> (d. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_the_Palatinate-Simmern\" class=\"mw-redirect\" title=\"<PERSON> of the Palatinate-Simmern\"><PERSON> of the Palatinate-Simmern</a> (d. 1589)", "links": [{"title": "Maria of the Palatinate-Simmern", "link": "https://wikipedia.org/wiki/Maria_of_the_Palatinate-Simmern"}]}, {"year": "1574", "text": "<PERSON> the Younger, Swiss physician and author (d. 1628)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swiss physician and author (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Swiss physician and author (d. 1628)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, 1st Duke of Shrewsbury, English politician, Lord High Treasurer (d. 1718)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Shrewsbury\" title=\"<PERSON>, 1st Duke of Shrewsbury\"><PERSON>, 1st Duke of Shrewsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Shrewsbury\" title=\"<PERSON>, 1st Duke of Shrewsbury\"><PERSON>, 1st Duke of Shrewsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1718)", "links": [{"title": "<PERSON>, 1st Duke of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Shrewsbury"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1689", "text": "<PERSON> <PERSON>, Duke of Gloucester, son of Queen <PERSON> of Great Britain and Prince <PERSON> of Denmark (d. 1700)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\">Queen <PERSON> of Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester\" title=\"Prince <PERSON>, Duke of Gloucester\">Prince <PERSON>, Duke of Gloucester</a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\">Queen Anne of Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1700)", "links": [{"title": "<PERSON> <PERSON>, Duke of Gloucester", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Gloucester"}, {"title": "<PERSON>, Queen of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain"}, {"title": "Prince <PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1725", "text": "<PERSON>, English sailor and priest (d. 1807)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and priest (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and priest (d. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, Ukrainian-Russian painter (d. 1825)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON>, Venezuelan commander and politician, second President of Venezuela (d. 1830)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolí<PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan commander and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar\" title=\"<PERSON><PERSON><PERSON> Bolí<PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan commander and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1786", "text": "<PERSON>, French mathematician and explorer (d. 1843)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and explorer (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and explorer (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, Danish mineralogist and geologist (d. 1865)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mineralogist and geologist (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mineralogist and geologist (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, French novelist and playwright (d. 1870)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON>, French composer and critic (d. 1856)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, American architect (d. 1892)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American boxer and gangster (d. 1855)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and gangster (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and gangster (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, Polish theorist and activist (d. 1902)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish theorist and activist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish theorist and activist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Polish-German mathematician and theorist (d. 1935)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and theorist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German mathematician and theorist (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, French mathematician and academic (d. 1941)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Picard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Picard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and academic (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Picard"}]}, {"year": "1857", "text": "<PERSON>, Danish journalist and author, Nobel Prize laureate (d. 1943)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1857", "text": "<PERSON>, Venezuelan general and politician, 27th President of Venezuela (d. 1935)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 27th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_G%C3%B3mez"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1860", "text": "Princess <PERSON> of Prussia (d. 1919)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Prussia\" title=\"Princess <PERSON> of Prussia\">Princess <PERSON> of Prussia</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Prussia\" title=\"Princess <PERSON> of Prussia\">Princess <PERSON> of Prussia</a> (d. 1919)", "links": [{"title": "Princess <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Prussia"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Czech painter and illustrator (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter and illustrator (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter and illustrator (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German actor and playwright (d. 1918)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and playwright (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and playwright (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English cricketer and coach (d. 1943)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Scottish minister and author (d. 1917)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and author (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Italian mob boss (d. 1954)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>og<PERSON>_<PERSON>izzini\" title=\"Calogero Vizzini\"><PERSON><PERSON><PERSON></a>, Italian mob boss (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calogero_Vizzini\" title=\"Calogero Vizzini\"><PERSON><PERSON><PERSON></a>, Italian mob boss (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Calogero_Vizzini"}]}, {"year": "1878", "text": "<PERSON>, 18th Baron of Dunsany, Irish author, poet, and playwright (d. 1957)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_18th_Baron_of_Dunsany\" class=\"mw-redirect\" title=\"<PERSON>, 18th Baron of Dunsany\"><PERSON>, 18th Baron of Dunsany</a>, Irish author, poet, and playwright (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_18th_Baron_of_Dunsany\" class=\"mw-redirect\" title=\"<PERSON>, 18th Baron of Dunsany\"><PERSON>, 18th Baron of Dunsany</a>, Irish author, poet, and playwright (d. 1957)", "links": [{"title": "<PERSON>, 18th Baron of Dunsany", "link": "https://wikipedia.org/wiki/<PERSON>,_18th_Baron_of_Dunsany"}]}, {"year": "1880", "text": "<PERSON>, Swiss-American composer and educator (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American composer and educator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American composer and educator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Italian actress (d. 1969)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author (d. 1965)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Jun%27ichir%C5%8D_Tan<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jun%27ichir%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun%27ichir%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Australian cricketer and coach (d. 1973)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer and coach (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer and coach (d. 1973)", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)"}]}, {"year": "1889", "text": "<PERSON>, American cryptanalyst (d. 1971)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cryptanalyst (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cryptanalyst (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English poet, novelist, critic (d. 1985)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, critic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, critic (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American pilot and author (d. 1937)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and author (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "Chief <PERSON>, Canadian actor (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, Canadian actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>\" title=\"Chief <PERSON>\">Chief <PERSON></a>, Canadian actor (d. 1981)", "links": [{"title": "Chief <PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American author, visual artist and ballet dancer (d. 1948)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, visual artist and ballet dancer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, visual artist and ballet dancer (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American lieutenant, Medal of Honor recipient (d. 2010)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1910", "text": "<PERSON>, American director and production designer (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and production designer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and production designer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, New Zealand author (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, American biologist and sailor (d. 2010)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Chance\" title=\"Britton Chance\"><PERSON><PERSON><PERSON></a>, American biologist and sailor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Britton Chance\"><PERSON><PERSON><PERSON></a>, American biologist and sailor (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Canadian pharmacologist and physician (d. 2015)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pharmacologist and physician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pharmacologist and physician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American-Canadian businessman and philanthropist (d. 2007)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and philanthropist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian businessman and philanthropist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian walker (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian walker (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian walker (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Filipino lawyer and jurist, 13th Chief Justice of the Supreme Court of the Philippines (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist, 13th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist, 13th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines\" class=\"mw-redirect\" title=\"Chief Justice of the Supreme Court of the Philippines\">Chief Justice of the Supreme Court of the Philippines</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the Supreme Court of the Philippines", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_Supreme_Court_of_the_Philippines"}]}, {"year": "1916", "text": "<PERSON>, American colonel and author (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian trumpet player, composer, and conductor (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trumpet player, composer, and conductor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trumpet player, composer, and conductor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian cricketer (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, American violinist and educator (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Rug<PERSON><PERSON>_R<PERSON>ci\" title=\"Rug<PERSON><PERSON> R<PERSON>ci\"><PERSON><PERSON><PERSON><PERSON></a>, American violinist and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rug<PERSON><PERSON>_R<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American violinist and educator (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruggie<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Australian lawyer and judge (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and judge (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, NASA manager (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, NASA manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, NASA manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American baseball player, coach, and journalist (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and journalist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and journalist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>zug\" title=\"<PERSON> Abzug\"><PERSON></a>, American lawyer and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abzug\" title=\"Bella Abzug\"><PERSON></a>, American lawyer and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Abzug"}]}, {"year": "1920", "text": "<PERSON>, American model and actress (d. 1969)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Italian tenor and actor (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American pianist and composer (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian radio host and author (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian radio host and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, English composer (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English composer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Greek anthropologist and archaeologist (d. 2021)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek anthropologist and archaeologist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek anthropologist and archaeologist (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American arts journalist (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American arts journalist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American arts journalist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American painter and sculptor", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Russian-Estonian philologist and academic (d. 1990)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Zara_Mints\" title=\"Zara Mints\">Zara Mints</a>, Russian-Estonian philologist and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zara_Mints\" title=\"Zara Mints\">Zara Mints</a>, Russian-Estonian philologist and academic (d. 1990)", "links": [{"title": "Zara Mints", "link": "https://wikipedia.org/wiki/Zara_Mints"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician, tenth Chief Minister of Gujarat (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, tenth <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, tenth <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Gujarat", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Gujarat"}]}, {"year": "1930", "text": "<PERSON>, American journalist and author (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Italian director, screenwriter, and cinematographer (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director, screenwriter, and cinematographer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian director, screenwriter, and cinematographer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erman<PERSON>_<PERSON>i"}]}, {"year": "1931", "text": "<PERSON><PERSON>, French commander (d. 1998)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French commander (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French commander (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_<PERSON>ly"}]}, {"year": "1932", "text": "<PERSON>, German astronomer and academic (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American golfer (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American football player (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (d. 2020)", "links": [{"title": "<PERSON> (defensive end)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)"}]}, {"year": "1934", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan accountant and politician (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/P._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan accountant and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>. <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan accountant and politician (d. 2017)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian cartoonist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American painter, illustrator, and academic (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, illustrator, and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, illustrator, and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English pianist, composer, and conductor (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English pianist, composer, and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English pianist, composer, and conductor (d. 2019)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1935", "text": "<PERSON>, South African cricketer (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actress and comedian", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, producer, and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, English architect, designed the Brentwood Cathedral", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Brentwood_Cathedral\" title=\"Brentwood Cathedral\">Brentwood Cathedral</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Brentwood_Cathedral\" title=\"Brentwood Cathedral\">Brentwood Cathedral</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Brentwood Cathedral", "link": "https://wikipedia.org/wiki/Brentwood_Cathedral"}]}, {"year": "1938", "text": "<PERSON>, American painter (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Walt Bella<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Walt Bellamy\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1941", "text": "<PERSON>, English banker and businessman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, English banker and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(banker)\" title=\"<PERSON> (banker)\"><PERSON></a>, English banker and businessman", "links": [{"title": "<PERSON> (banker)", "link": "https://wikipedia.org/wiki/<PERSON>(banker)"}]}, {"year": "1942", "text": "<PERSON>, German-English singer-songwriter and bass player (d. 2000)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, German-English singer-songwriter and bass player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, German-English singer-songwriter and bass player (d. 2000)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON>, American actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English physicist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Close\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian businessman and philanthropist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Azi<PERSON>_Pre<PERSON>ji\" title=\"Azim Premji\"><PERSON><PERSON><PERSON></a>, Indian businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azi<PERSON>_Pre<PERSON>ji\" title=\"Azim Premji\"><PERSON><PERSON><PERSON></a>, Indian businessman and philanthropist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Azim_<PERSON>ji"}]}, {"year": "1945", "text": "<PERSON>, Canadian-American astrophysicist and astronomer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(creationist)\" class=\"mw-redirect\" title=\"<PERSON> (creationist)\"><PERSON></a>, Canadian-American astrophysicist and astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(creationist)\" class=\"mw-redirect\" title=\"<PERSON> (creationist)\"><PERSON></a>, Canadian-American astrophysicist and astronomer", "links": [{"title": "<PERSON> (creationist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(creationist)"}]}, {"year": "1945", "text": "<PERSON>, English geologist, geophysicist, and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" class=\"mw-redirect\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist, geophysicist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" class=\"mw-redirect\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist, geophysicist, and academic", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(geologist)"}]}, {"year": "1946", "text": "<PERSON>, American comedian and actor (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actor (d. 2022)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>(comedian)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, French singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Herv%C3%A9_Vilard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Herv%C3%A9_Vilard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herv%C3%A9_Vilard"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American politician, 21st Governor of Montana", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Montana", "link": "https://wikipedia.org/wiki/Governor_of_Montana"}]}, {"year": "1949", "text": "<PERSON>, American actor and comedian", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Yugoslav singer-songwriter (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Stojakovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Yugoslav singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Stojakovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Yugoslav singer-songwriter (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jadranka_Stojakovi%C4%87"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, <PERSON> of Finsbury, English politician, Secretary of State for Culture, Media and Sport", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Finsbury\" title=\"<PERSON>, Baron <PERSON> of Finsbury\"><PERSON>, Baron <PERSON> of Finsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport\" title=\"Secretary of State for Culture, Media and Sport\">Secretary of State for Culture, Media and Sport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Finsbury\" title=\"<PERSON>, Baron <PERSON> of Finsbury\"><PERSON>, Baron <PERSON> of Finsbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport\" title=\"Secretary of State for Culture, Media and Sport\">Secretary of State for Culture, Media and Sport</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Finsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Finsbury"}, {"title": "Secretary of State for Culture, Media and Sport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Culture,_Media_and_Sport"}]}, {"year": "1952", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/1952\" title=\"1952\">1952</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1952\" title=\"1952\">1952</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "1952", "link": "https://wikipedia.org/wiki/1952"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American trumpet player, composer, and conductor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Japanese contemporary artist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese contemporary artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese contemporary artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English bishop", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Portuguese footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and politician, 44th Governor of Florida", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish footballer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French rock climber and mountaineer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rock climber and mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rock climber and mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Portuguese economist and politician, 118th Prime Minister of Portugal", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese economist and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese economist and politician, 118th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_Coelho"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1965", "text": "<PERSON>, Australian basketball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, American actor, director, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Sahrawi human rights activist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Aminatou_Haidar\" title=\"Aminatou Haidar\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sahrawi_people\" class=\"mw-redirect\" title=\"Sahrawi people\">Sahrawi</a> human rights activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aminatou_Haidar\" title=\"Aminatou Haidar\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sahrawi_people\" class=\"mw-redirect\" title=\"Sahrawi people\">Sahrawi</a> human rights activist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aminatou_Haidar"}, {"title": "Sahrawi people", "link": "https://wikipedia.org/wiki/Sahrawi_people"}]}, {"year": "1966", "text": "<PERSON>, English footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American actress and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Bahamian basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress, singer, and dancer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American film director and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Japanese sumo wrestler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Kai%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Hiroyuki\"><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kai%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Hiroyuki\"><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kai%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English racing driver and journalist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, English politician, Minister for Sport and the Olympics", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American model, fitness competitor, actress and professional wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, fitness competitor, actress and professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, fitness competitor, actress and professional wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Portuguese racing driver and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese racing driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese racing driver and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiago_<PERSON>iro"}]}, {"year": "1978", "text": "<PERSON>, American surfer (d. 2010)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>s\"><PERSON></a>, American surfer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andy Irons\"><PERSON></a>, American surfer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Salvadoran politician, 81st President of El Salvador", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran politician, 81st <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran politician, 81st <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>le"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Summer_Glau\" title=\"Summer Glau\">Summer Glau</a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Summer_Glau\" title=\"Summer Glau\">Summer Glau</a>, American actress", "links": [{"title": "Summer Glau", "link": "https://wikipedia.org/wiki/Summer_Glau"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian-New Zealand actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-New Zealand actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-New Zealand actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American hurdler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Rosol\" title=\"<PERSON><PERSON><PERSON><PERSON> Rosol\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Rosol\" title=\"<PERSON><PERSON><PERSON><PERSON> Rosol\"><PERSON><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luk%C3%A1%C5%A1_Rosol"}]}, {"year": "1986", "text": "<PERSON>, Australian actress and online producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and online producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and online producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>., English wrestler", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"Zack Sabre Jr.\"><PERSON>.</a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Sabre Jr.\"><PERSON>.</a>, English wrestler", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1987", "text": "<PERSON>, American actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South Korean singer and dancer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon\" title=\"<PERSON>-yeon\"><PERSON></a>, South Korean singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon\" title=\"<PERSON>-yeon\"><PERSON></a>, South Korean singer and dancer", "links": [{"title": "<PERSON>yeon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai-American singer-songwriter and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai-American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai-American singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ch<PERSON>un"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English-American mass murderer (d. 2014)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mass murderer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mass murderer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Canadian skier", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mika%C3%ABl_Kingsbury\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mika%C3%ABl_Kingsbury\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mika%C3%ABl_Kingsbury"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Japanese sumo wrestler", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chikara"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mix<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mix<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Australian conservationist, zookeeper, and actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian conservationist, zookeeper, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian conservationist, zookeeper, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_2001)\" title=\"<PERSON> (ice hockey, born 2001)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_2001)\" title=\"<PERSON> (ice hockey, born 2001)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 2001)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_2001)"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Drake_London\" title=\"Drake London\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Drake_London\" title=\"Drake London\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Drake_London"}]}], "Deaths": [{"year": "759", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Northumbria", "html": "759 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Northumbria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Northumbria\"><PERSON><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Northumbria\" class=\"mw-redirect\" title=\"Kingdom of Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Northumbria", "link": "https://wikipedia.org/wiki/Oswulf_of_Northumbria"}, {"title": "Kingdom of Northumbria", "link": "https://wikipedia.org/wiki/Kingdom_of_Northumbria"}]}, {"year": "811", "text": "<PERSON>, Chinese politician (b. 740)", "html": "811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (b. 740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (b. 740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "946", "text": "<PERSON> ibn <PERSON><PERSON><PERSON>, Egyptian ruler (b. 882)", "html": "946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Egyptian ruler (b. 882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ibn_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Egyptian ruler (b. 882)", "links": [{"title": "<PERSON> ibn <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1115", "text": "<PERSON> of Tuscany (b. 1046)", "html": "1115 - <a href=\"https://wikipedia.org/wiki/Matilda_of_Tuscany\" title=\"Matilda of Tuscany\"><PERSON> of Tuscany</a> (b. 1046)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_of_Tuscany\" title=\"<PERSON> of Tuscany\"><PERSON> of Tuscany</a> (b. 1046)", "links": [{"title": "<PERSON> of Tuscany", "link": "https://wikipedia.org/wiki/Matilda_of_Tuscany"}]}, {"year": "1129", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1053)", "html": "1129 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1053)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1053)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1198", "text": "<PERSON><PERSON> of Hanover, Bishop of Livonia", "html": "1198 - <a href=\"https://wikipedia.org/wiki/Berthold_of_Hanover\" title=\"Berthold of Hanover\"><PERSON><PERSON> of Hanover</a>, <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Riga\" title=\"Archbishopric of Riga\">Bishop of Livonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berthold_of_Hanover\" title=\"<PERSON>hold of Hanover\"><PERSON><PERSON> of Hanover</a>, <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Riga\" title=\"Archbishopric of Riga\">Bishop of Livonia</a>", "links": [{"title": "Berthold of Hanover", "link": "https://wikipedia.org/wiki/Berthold_of_Hanover"}, {"title": "Archbishopric of Riga", "link": "https://wikipedia.org/wiki/Archbishopric_of_Riga"}]}, {"year": "1345", "text": "<PERSON>, Flemish statesman (b. 1290)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish statesman (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish statesman (b. 1290)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "<PERSON>, Prince of Asturias (b. 1545)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a> (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a> (b. 1545)", "links": [{"title": "<PERSON>, Prince of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias"}]}, {"year": "1594", "text": "<PERSON>, English martyr and saint (b. 1544)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English martyr and saint (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English martyr and saint (b. 1544)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1601", "text": "<PERSON><PERSON>, Flemish painter (b. 1542)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (b. 1542)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1612", "text": "<PERSON>, Welsh politician and poet (b. 1567)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Welsh politician and poet (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Welsh politician and poet (b. 1567)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1739", "text": "<PERSON><PERSON><PERSON>, Italian composer and educator (b. 1686)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer and educator (b. 1686)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, English theologian and author (b. 1684)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and author (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and author (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American lawyer and politician, eighth President of the United States (b. 1782)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, eighth <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, eighth <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1891", "text": "<PERSON>, German-American journalist and politician (b. 1827)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and politician (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and politician (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Salvadoran journalist and poet (b. 1867)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran journalist and poet (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran journalist and poet (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Maltese educator and politician (b. 1835)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese educator and politician (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Maltese educator and politician (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Ukrainian-Russian painter (b. 1841)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Arkhip_Kuindzhi\" title=\"Arkhip Kuindzhi\"><PERSON><PERSON></a>, Ukrainian-Russian painter (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ark<PERSON>_Ku<PERSON>\" title=\"Arkhip Kuindzhi\"><PERSON><PERSON></a>, Ukrainian-Russian painter (b. 1841)", "links": [{"title": "Arkhip <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zhi"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author (b. 1892)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%<PERSON><PERSON><PERSON>_<PERSON>awa"}]}, {"year": "1957", "text": "<PERSON><PERSON>, French actor and director (b. 1885)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor and director (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor and director (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, English mountaineer and author (b. 1917)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English mountaineer and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English mountaineer and author (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress and producer (b. 1904)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American golfer (b. 1934)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Polish author and playwright (b. 1904)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"Wito<PERSON> Go<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and playwright (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and playwright (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>icz"}]}, {"year": "1970", "text": "<PERSON>, Indian businessman, philanthropist, and civil servant (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman, philanthropist, and civil servant (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian businessman, philanthropist, and civil servant (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (b. 1891)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1980", "text": "<PERSON>, English actor and comedian (b. 1925)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian missionary and martyr (b. 1953)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian missionary and martyr (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian missionary and martyr (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German-American biochemist and academic, Nobel Prize laureate (b. 1899)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Pakistani civil servant and author (b. 1917)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani civil servant and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani civil servant and author (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Polish-American novelist and short story writer, Nobel Prize laureate (b. 1902)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, French actress and singer (b. 1898)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and singer (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian lawyer and businessman (b. 1900)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Canadian lawyer and businessman (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Canadian lawyer and businessman (b. 1900)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}]}, {"year": "1994", "text": "<PERSON>, Cochiti Pueblo (Native American) Pueblo potter (b. 1915)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cochiti_Pueblo\" class=\"mw-redirect\" title=\"Cochiti Pueblo\">Cochiti Pueblo</a> (Native American) Pueblo potter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cochiti_Pueblo\" class=\"mw-redirect\" title=\"Cochiti Pueblo\">Cochiti Pueblo</a> (Native American) Pueblo potter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cochiti Pueblo", "link": "https://wikipedia.org/wiki/Cochiti_Pueblo"}]}, {"year": "1995", "text": "<PERSON>, English photographer and journalist (b. 1908)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Vincentian cricketer and activist (b. 1937)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vincentian cricketer and activist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Al<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vincentian cricketer and activist (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American colonel and jurist (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American colonel and jurist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American colonel and jurist (b. 1906)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1997", "text": "<PERSON>, Burmese general and politician, seventh Prime Minister of Burma (b. 1928)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese general and politician, seventh <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burmese general and politician, seventh <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Burma", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Burma"}]}, {"year": "2000", "text": "<PERSON>, Iranian poet and journalist (b. 1925)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iranian poet and journalist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iranian poet and journalist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ahmad_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian author, playwright, and composer (b. 1931)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, and composer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English physiologist and epidemiologist (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doll\"><PERSON></a>, English physiologist and epidemiologist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Doll\"><PERSON></a>, English physiologist and epidemiologist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American psychologist and author (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Greek opera singer (b. 1923)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek opera singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek opera singer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nicola_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American pianist and composer (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, French equestrian (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French equestrian (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French equestrian (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Northern Irish snooker player (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish <a href=\"https://wikipedia.org/wiki/Snooker\" title=\"Snooker\">snooker</a> player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish <a href=\"https://wikipedia.org/wiki/Snooker\" title=\"Snooker\">snooker</a> player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Snooker", "link": "https://wikipedia.org/wiki/Snooker"}]}, {"year": "2011", "text": "<PERSON>, German politician (b. 1966)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German politician (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German politician (b. 1966)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter and guitarist  (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Norwegian bassist and composer (b. 1970)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian bassist and composer (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian bassist and composer (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, French physician, neuroscientist, and author (b. 1961)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, neuroscientist, and author (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, neuroscientist, and author (b. 1961)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American football player (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor and director (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_Everett"}]}, {"year": "2012", "text": "<PERSON>, American actor and singer (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer and guitarist (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American physiologist and physicist, invented the CT scanner (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and physicist, invented the <a href=\"https://wikipedia.org/wiki/CT_scanner\" class=\"mw-redirect\" title=\"CT scanner\">CT scanner</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physiologist and physicist, invented the <a href=\"https://wikipedia.org/wiki/CT_scanner\" class=\"mw-redirect\" title=\"CT scanner\">CT scanner</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "CT scanner", "link": "https://wikipedia.org/wiki/CT_scanner"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Chilean author and illustrator (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Themo_Lobos\" title=\"Themo Lobos\"><PERSON><PERSON></a>, Chilean author and illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Themo_Lobos\" title=\"Themo Lobos\"><PERSON><PERSON></a>, Chilean author and illustrator (b. 1928)", "links": [{"title": "Themo <PERSON>", "link": "https://wikipedia.org/wiki/Themo_Lobos"}]}, {"year": "2012", "text": "<PERSON>, Ghanaian lawyer and politician, President of Ghana (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Ghana", "link": "https://wikipedia.org/wiki/President_of_Ghana"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish jurist and politician (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish jurist and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish jurist and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gregorio_Peces-Barba"}]}, {"year": "2013", "text": "<PERSON>, American pilot and activist, created the World Passport (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and activist, created the <a href=\"https://wikipedia.org/wiki/World_Passport\" title=\"World Passport\">World Passport</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and activist, created the <a href=\"https://wikipedia.org/wiki/World_Passport\" title=\"World Passport\">World Passport</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Passport", "link": "https://wikipedia.org/wiki/World_Passport"}]}, {"year": "2013", "text": "<PERSON>, American philosopher and academic (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON> <PERSON><PERSON>, American psychologist and sexologist (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia E<PERSON> Johnson\"><PERSON> <PERSON><PERSON></a>, American psychologist and sexologist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia E<PERSON>\"><PERSON> <PERSON><PERSON></a>, American psychologist and sexologist (b. 1925)", "links": [{"title": "Virginia <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South African lawyer and jurist, 19th Chief Justice of South Africa (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and jurist, 19th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and jurist, 19th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of South Africa", "link": "https://wikipedia.org/wiki/Chief_Justice_of_South_Africa"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Korean-American violinist and educator (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Korean-American violinist and educator (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Korean-American violinist and educator (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>e"}]}, {"year": "2014", "text": "<PERSON>, American basketball player (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German journalist and author (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and author (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German journalist and author (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American actress and screenwriter (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and screenwriter (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, South African-American journalist and critic (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American journalist and critic (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American journalist and critic (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American actress and singer (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian Gujarati playback singer", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Ha<PERSON>hi<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>val\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Gujarati playback singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>val\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Gujarati playback singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>hi<PERSON>_<PERSON>val"}]}, {"year": "2020", "text": "<PERSON>, American actor and television host (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regis_Philbin"}]}, {"year": "2021", "text": "<PERSON>, United States Naval Aviator and air show performer (b. 1949)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Naval Aviator and air show performer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Naval Aviator and air show performer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American serial killer (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English actor (b. 1941)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1941)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2023", "text": "<PERSON>, BBC News journalist and broadcaster (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/BBC_News\" title=\"BBC News\">BBC News</a> journalist and broadcaster (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/BBC_News\" title=\"BBC News\">BBC News</a> journalist and broadcaster (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BBC News", "link": "https://wikipedia.org/wiki/BBC_News"}]}, {"year": "2023", "text": "<PERSON>, Britain's first \"£1 million player\" (b. 1954)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Britain's first \"£1 million player\" (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Britain's first \"£1 million player\" (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Bangladeshi bassist and singer-songwriter (b. 1961)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi bassist and singer-songwriter (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi bassist and singer-songwriter (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Indonesian journalist and politician, 9th Vice President of Indonesia (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Hamzah_Haz\" title=\"Hamza<PERSON>z\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamzah_<PERSON>z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a> (b. 1940)", "links": [{"title": "Hamzah Haz", "link": "https://wikipedia.org/wiki/Hamzah_Haz"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Ukrainian engineer and designer (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian engineer and designer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian engineer and designer (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}