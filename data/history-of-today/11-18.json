{"date": "November 18", "url": "https://wikipedia.org/wiki/November_18", "data": {"Events": [{"year": "326", "text": "The old St. Peter's Basilica is consecrated by <PERSON> <PERSON>.", "html": "326 - The <a href=\"https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica\" title=\"Old St. Peter's Basilica\">old St. Peter's Basilica</a> is consecrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_Sylvester_<PERSON>\" title=\"Pope Sylvester I\"><PERSON> I</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica\" title=\"Old St. Peter's Basilica\">old St. Peter's Basilica</a> is consecrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_Sylvester_<PERSON>\" title=\"Pope Sylvester I\"><PERSON> I</a>.", "links": [{"title": "Old St. Peter's Basilica", "link": "https://wikipedia.org/wiki/Old_St._Peter%27s_Basilica"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "401", "text": "The Visigoths, led by king <PERSON><PERSON><PERSON> <PERSON>, cross the Alps and invade northern Italy.", "html": "401 - The <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>, led by king <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\"><PERSON>ari<PERSON> I</a>, cross the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a> and invade northern Italy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a>, led by king <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\">Alaric I</a>, cross the <a href=\"https://wikipedia.org/wiki/Alps\" title=\"Alps\">Alps</a> and invade northern Italy.", "links": [{"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "Alaric I", "link": "https://wikipedia.org/wiki/Alaric_I"}, {"title": "Alps", "link": "https://wikipedia.org/wiki/Alps"}]}, {"year": "1095", "text": "The Council of Clermont begins: called by Pope <PERSON>, it led to the First Crusade to the Holy Land.", "html": "1095 - The <a href=\"https://wikipedia.org/wiki/Council_of_Clermont\" title=\"Council of Clermont\">Council of Clermont</a> begins: called by <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\">Pope Urban II</a>, it led to the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> to the <a href=\"https://wikipedia.org/wiki/Holy_Land\" title=\"Holy Land\">Holy Land</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Clermont\" title=\"Council of Clermont\">Council of Clermont</a> begins: called by <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> II</a>, it led to the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> to the <a href=\"https://wikipedia.org/wiki/Holy_Land\" title=\"Holy Land\">Holy Land</a>.", "links": [{"title": "Council of Clermont", "link": "https://wikipedia.org/wiki/Council_of_Clermont"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Pope_Urban_II"}, {"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "Holy Land", "link": "https://wikipedia.org/wiki/Holy_Land"}]}, {"year": "1105", "text": "<PERSON><PERSON><PERSON><PERSON> is elected <PERSON><PERSON><PERSON> in opposition to <PERSON>.", "html": "1105 - <PERSON><PERSON><PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">Antipope</a> <a href=\"https://wikipedia.org/wiki/Antipop<PERSON>_Sylvester_IV\" title=\"Antipope Sylvester IV\"><PERSON> IV</a> in opposition to <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\">Pope <PERSON> II</a>.", "no_year_html": "<PERSON><PERSON><PERSON><PERSON> is elected <a href=\"https://wikipedia.org/wiki/Antipope\" title=\"Antipope\">Antipope</a> <a href=\"https://wikipedia.org/wiki/Antipop<PERSON>_Sylvester_IV\" title=\"Antipope Sylvester IV\"><PERSON> IV</a> in opposition to <a href=\"https://wikipedia.org/wiki/Pope_Paschal_II\" title=\"Pope Paschal II\">Pope Paschal II</a>.", "links": [{"title": "Antipope", "link": "https://wikipedia.org/wiki/Antipope"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_II"}]}, {"year": "1210", "text": "<PERSON> <PERSON> III excommunicates Holy Roman Emperor <PERSON> for invading the Kingdom of Sicily after promising to recognize papal control over it.", "html": "1210 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicates</a> <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> for invading the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily\" title=\"Kingdom of Sicily\">Kingdom of Sicily</a> after promising to recognize papal control over it.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicates</a> <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV</a> for invading the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily\" title=\"Kingdom of Sicily\">Kingdom of Sicily</a> after promising to recognize papal control over it.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Kingdom of Sicily", "link": "https://wikipedia.org/wiki/Kingdom_of_Sicily"}]}, {"year": "1302", "text": "<PERSON> <PERSON><PERSON><PERSON> issues the Papal bull Unam sanctam, claiming spiritual supremacy for the papacy.", "html": "1302 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_VIII\" title=\"<PERSON> <PERSON>ace VIII\">Pope <PERSON><PERSON><PERSON> VIII</a> issues the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">Papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Unam_sanctam\" title=\"Unam sanctam\">Unam sanctam</a></i>, claiming spiritual supremacy for the papacy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_VIII\" title=\"Pope <PERSON>ace VIII\">Pope <PERSON><PERSON><PERSON> VIII</a> issues the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">Papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Unam_sanctam\" title=\"Unam sanctam\">Unam sanctam</a></i>, claiming spiritual supremacy for the papacy.", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>if<PERSON>_VIII"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Unam sanctam", "link": "https://wikipedia.org/wiki/Unam_sanctam"}]}, {"year": "1421", "text": "St Elizabeth's flood: A dike in the Grote Hollandse Waard in the Netherlands breaks, killing about 10,000 people.", "html": "1421 - <a href=\"https://wikipedia.org/wiki/St._Elizabeth%27s_flood_(1421)\" title=\"St. Elizabeth's flood (1421)\">St Elizabeth's flood</a>: A dike in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hollandse_Waard\" title=\"Grote Hollandse Waard\">Grote Hollandse Waard</a> in the Netherlands breaks, killing about 10,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St._Elizabeth%27s_flood_(1421)\" title=\"St. Elizabeth's flood (1421)\">St Elizabeth's flood</a>: A dike in the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hollandse_Waard\" title=\"Grote Hollandse Waard\">Grote Hollandse Waard</a> in the Netherlands breaks, killing about 10,000 people.", "links": [{"title": "St. Elizabeth's flood (1421)", "link": "https://wikipedia.org/wiki/St._Elizabeth%27s_flood_(1421)"}, {"title": "<PERSON><PERSON> Holland<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Waard"}]}, {"year": "1493", "text": "<PERSON> first sights the island now known as Puerto Rico.", "html": "1493 - <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> first sights the island now known as <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"Christopher Columbus\"><PERSON></a> first sights the island now known as <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}]}, {"year": "1601", "text": "<PERSON><PERSON><PERSON><PERSON>, an Ottoman provincial governor, routs the Habsburg forces commanded by <PERSON><PERSON><PERSON> of Austria who were besieging Nagykanizsa.", "html": "1601 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> provincial governor, routs the Habsburg forces commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\">Archduke <PERSON> of Austria</a> who were <a href=\"https://wikipedia.org/wiki/Siege_of_Nagykanizsa\" title=\"Siege of Nagykanizsa\">besieging Nagykanizsa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON></a>, an <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> provincial governor, routs the Habsburg forces commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\">Archduke <PERSON> of Austria</a> who were <a href=\"https://wikipedia.org/wiki/Siege_of_Nagykanizsa\" title=\"Siege of Nagykanizsa\">besieging Nagykanizsa</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Siege of Nagykanizsa", "link": "https://wikipedia.org/wiki/Siege_of_Nagykanizsa"}]}, {"year": "1626", "text": "The new St. Peter's Basilica in Rome is consecrated.", "html": "1626 - The new <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> in Rome is consecrated.", "no_year_html": "The new <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> in Rome is consecrated.", "links": [{"title": "St. Peter's Basilica", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_Basilica"}]}, {"year": "1730", "text": "The future <PERSON> the <PERSON> of Prussia is granted a pardon by his father and is released from confinement.", "html": "1730 - The future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" class=\"mw-redirect\" title=\"<PERSON> II of Prussia\"><PERSON> the <PERSON></a> of Prussia is granted a pardon by his father and is released from confinement.", "no_year_html": "The future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" class=\"mw-redirect\" title=\"<PERSON> II of Prussia\"><PERSON> the <PERSON></a> of Prussia is granted a pardon by his father and is released from confinement.", "links": [{"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Frederick_II_of_Prussia"}]}, {"year": "1760", "text": "The rebuilt debtors' prison, at the Castellania in Valletta, receives the first prisoners.", "html": "1760 - The rebuilt <a href=\"https://wikipedia.org/wiki/Debtors%27_prison\" title=\"Debtors' prison\">debtors' prison</a>, at the <a href=\"https://wikipedia.org/wiki/Castellania_(Valletta)\" title=\"Castellania (Valletta)\">Castellania</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, receives the first prisoners.", "no_year_html": "The rebuilt <a href=\"https://wikipedia.org/wiki/Debtors%27_prison\" title=\"Debtors' prison\">debtors' prison</a>, at the <a href=\"https://wikipedia.org/wiki/Castellania_(Valletta)\" title=\"Castellania (Valletta)\">Castellania</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, receives the first prisoners.", "links": [{"title": "Debtors' prison", "link": "https://wikipedia.org/wiki/Debtors%27_prison"}, {"title": "Castellania (Valletta)", "link": "https://wikipedia.org/wiki/Castellania_(Valletta)"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}]}, {"year": "1803", "text": "The Battle of Vertières, the last major battle of the Haitian Revolution, is fought, leading to the establishment of the Republic of Haiti, the first black republic in the Western Hemisphere.", "html": "1803 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Verti%C3%A8res\" title=\"Battle of Vertières\">Battle of Vertières</a>, the last major battle of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>, is fought, leading to the establishment of the <a href=\"https://wikipedia.org/wiki/Republic_of_Haiti\" class=\"mw-redirect\" title=\"Republic of Haiti\">Republic of Haiti</a>, the first black republic in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Verti%C3%A8res\" title=\"Battle of Vertières\">Battle of Vertières</a>, the last major battle of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>, is fought, leading to the establishment of the <a href=\"https://wikipedia.org/wiki/Republic_of_Haiti\" class=\"mw-redirect\" title=\"Republic of Haiti\">Republic of Haiti</a>, the first black republic in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>.", "links": [{"title": "Battle of Vertières", "link": "https://wikipedia.org/wiki/Battle_of_Verti%C3%A8res"}, {"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}, {"title": "Republic of Haiti", "link": "https://wikipedia.org/wiki/Republic_of_Haiti"}, {"title": "Western Hemisphere", "link": "https://wikipedia.org/wiki/Western_Hemisphere"}]}, {"year": "1809", "text": "In a naval action during the Napoleonic Wars, French frigates defeat British East Indiamen in the Bay of Bengal.", "html": "1809 - In a <a href=\"https://wikipedia.org/wiki/Action_of_18_November_1809\" title=\"Action of 18 November 1809\">naval action</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>, French <a href=\"https://wikipedia.org/wiki/Frigate\" title=\"Frigate\">frigates</a> defeat British <a href=\"https://wikipedia.org/wiki/East_Indiamen\" class=\"mw-redirect\" title=\"East Indiamen\">East Indiamen</a> in the <a href=\"https://wikipedia.org/wiki/Bay_of_Bengal\" title=\"Bay of Bengal\">Bay of Bengal</a>.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/Action_of_18_November_1809\" title=\"Action of 18 November 1809\">naval action</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>, French <a href=\"https://wikipedia.org/wiki/Frigate\" title=\"Frigate\">frigates</a> defeat British <a href=\"https://wikipedia.org/wiki/East_Indiamen\" class=\"mw-redirect\" title=\"East Indiamen\">East Indiamen</a> in the <a href=\"https://wikipedia.org/wiki/Bay_of_Bengal\" title=\"Bay of Bengal\">Bay of Bengal</a>.", "links": [{"title": "Action of 18 November 1809", "link": "https://wikipedia.org/wiki/Action_of_18_November_1809"}, {"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Frigate", "link": "https://wikipedia.org/wiki/Frigate"}, {"title": "East Indiamen", "link": "https://wikipedia.org/wiki/East_Indiamen"}, {"title": "Bay of Bengal", "link": "https://wikipedia.org/wiki/Bay_of_Bengal"}]}, {"year": "1812", "text": "Napoleonic Wars: The Battle of Krasnoi ends in French defeat, but Marshal of France <PERSON>'s leadership leads to him becoming known as \"the bravest of the brave\".", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Krasnoi\" title=\"Battle of Krasnoi\">Battle of Krasnoi</a> ends in French defeat, but <a href=\"https://wikipedia.org/wiki/Marshal_of_France\" title=\"Marshal of France\">Marshal of France</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s leadership leads to him becoming known as \"the bravest of the brave\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Krasnoi\" title=\"Battle of Krasnoi\">Battle of Krasnoi</a> ends in French defeat, but <a href=\"https://wikipedia.org/wiki/Marshal_of_France\" title=\"Marshal of France\">Marshal of France</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s leadership leads to him becoming known as \"the bravest of the brave\".", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Krasnoi", "link": "https://wikipedia.org/wiki/Battle_of_Krasnoi"}, {"title": "Marshal of France", "link": "https://wikipedia.org/wiki/Marshal_of_France"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "King <PERSON> of Denmark signs the November constitution that declares Schleswig to be part of Denmark. This is seen by the German Confederation as a violation of the London Protocol and leads to the German-Danish war of 1864.", "html": "1863 - King <a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> signs the <a href=\"https://wikipedia.org/wiki/History_of_Schleswig-Holstein#The_November_Constitution\" title=\"History of Schleswig-Holstein\">November constitution</a> that declares <a href=\"https://wikipedia.org/wiki/Schleswig\" class=\"mw-redirect\" title=\"Schleswig\">Schleswig</a> to be part of Denmark. This is seen by the <a href=\"https://wikipedia.org/wiki/German_Confederation\" title=\"German Confederation\">German Confederation</a> as a violation of the <a href=\"https://wikipedia.org/wiki/London_Protocol_(1852)\" title=\"London Protocol (1852)\">London Protocol</a> and leads to the <a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">German-Danish war of 1864</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Christian_IX_of_Denmark\" title=\"Christian IX of Denmark\">Christian IX of Denmark</a> signs the <a href=\"https://wikipedia.org/wiki/History_of_Schleswig-Holstein#The_November_Constitution\" title=\"History of Schleswig-Holstein\">November constitution</a> that declares <a href=\"https://wikipedia.org/wiki/Schleswig\" class=\"mw-redirect\" title=\"Schleswig\">Schleswig</a> to be part of Denmark. This is seen by the <a href=\"https://wikipedia.org/wiki/German_Confederation\" title=\"German Confederation\">German Confederation</a> as a violation of the <a href=\"https://wikipedia.org/wiki/London_Protocol_(1852)\" title=\"London Protocol (1852)\">London Protocol</a> and leads to the <a href=\"https://wikipedia.org/wiki/Second_Schleswig_War\" title=\"Second Schleswig War\">German-Danish war of 1864</a>.", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_IX_of_Denmark"}, {"title": "History of Schleswig-Holstein", "link": "https://wikipedia.org/wiki/History_of_Schleswig-Holstein#The_November_Constitution"}, {"title": "Schleswig", "link": "https://wikipedia.org/wiki/Schleswig"}, {"title": "German Confederation", "link": "https://wikipedia.org/wiki/German_Confederation"}, {"title": "London Protocol (1852)", "link": "https://wikipedia.org/wiki/London_Protocol_(1852)"}, {"title": "Second Schleswig War", "link": "https://wikipedia.org/wiki/Second_Schleswig_War"}]}, {"year": "1867", "text": "An earthquake strikes the Virgin Islands, triggering the largest tsunami witnessed in the Caribbean and killing dozens.", "html": "1867 - <a href=\"https://wikipedia.org/wiki/1867_Virgin_Islands_earthquake_and_tsunami\" title=\"1867 Virgin Islands earthquake and tsunami\">An earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Virgin_Islands\" title=\"Virgin Islands\">Virgin Islands</a>, triggering the largest <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> witnessed in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> and killing dozens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1867_Virgin_Islands_earthquake_and_tsunami\" title=\"1867 Virgin Islands earthquake and tsunami\">An earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Virgin_Islands\" title=\"Virgin Islands\">Virgin Islands</a>, triggering the largest <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> witnessed in the <a href=\"https://wikipedia.org/wiki/Caribbean\" title=\"Caribbean\">Caribbean</a> and killing dozens.", "links": [{"title": "1867 Virgin Islands earthquake and tsunami", "link": "https://wikipedia.org/wiki/1867_Virgin_Islands_earthquake_and_tsunami"}, {"title": "Virgin Islands", "link": "https://wikipedia.org/wiki/Virgin_Islands"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "Caribbean", "link": "https://wikipedia.org/wiki/Caribbean"}]}, {"year": "1872", "text": "<PERSON> and 14 other women are arrested for voting illegally in the United States presidential election of 1872.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 14 other women are arrested for voting illegally in the United States <a href=\"https://wikipedia.org/wiki/1872_United_States_presidential_election\" title=\"1872 United States presidential election\">presidential election of 1872</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 14 other women are arrested for voting illegally in the United States <a href=\"https://wikipedia.org/wiki/1872_United_States_presidential_election\" title=\"1872 United States presidential election\">presidential election of 1872</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1872 United States presidential election", "link": "https://wikipedia.org/wiki/1872_United_States_presidential_election"}]}, {"year": "1883", "text": "In the \"day of two noons,\" American and Canadian railroad companies institute four standard continental time zones, ending the confusion of thousands of local times.", "html": "1883 - In the \"day of two noons,\" American and Canadian railroad companies institute four standard continental <a href=\"https://wikipedia.org/wiki/Time_zone\" title=\"Time zone\">time zones</a>, ending the confusion of thousands of <a href=\"https://wikipedia.org/wiki/Time_in_the_United_States#History\" title=\"Time in the United States\">local times</a>.", "no_year_html": "In the \"day of two noons,\" American and Canadian railroad companies institute four standard continental <a href=\"https://wikipedia.org/wiki/Time_zone\" title=\"Time zone\">time zones</a>, ending the confusion of thousands of <a href=\"https://wikipedia.org/wiki/Time_in_the_United_States#History\" title=\"Time in the United States\">local times</a>.", "links": [{"title": "Time zone", "link": "https://wikipedia.org/wiki/Time_zone"}, {"title": "Time in the United States", "link": "https://wikipedia.org/wiki/Time_in_the_United_States#History"}]}, {"year": "1901", "text": "Britain and the United States sign the Hay-Pauncefote Treaty, which nullifies the Clayton-Bulwer Treaty and withdraws British objections to an American-controlled canal in Panama.", "html": "1901 - Britain and the United States sign the <a href=\"https://wikipedia.org/wiki/Hay%E2%80%93Pauncefote_Treaty\" title=\"Hay-Pauncefote Treaty\">Hay-Pauncefote Treaty</a>, which nullifies the <a href=\"https://wikipedia.org/wiki/Clayton%E2%80%93Bulwer_Treaty\" title=\"Clayton-Bulwer Treaty\">Clayton-Bulwer Treaty</a> and withdraws British objections to an American-controlled canal in Panama.", "no_year_html": "Britain and the United States sign the <a href=\"https://wikipedia.org/wiki/Hay%E2%80%93Pauncefote_Treaty\" title=\"Hay-Pauncefote Treaty\">Hay-Pauncefote Treaty</a>, which nullifies the <a href=\"https://wikipedia.org/wiki/Clayton%E2%80%93Bulwer_Treaty\" title=\"Clayton-Bulwer Treaty\">Clayton-Bulwer Treaty</a> and withdraws British objections to an American-controlled canal in Panama.", "links": [{"title": "Hay-Pauncefote Treaty", "link": "https://wikipedia.org/wiki/Hay%E2%80%93Pauncefote_Treaty"}, {"title": "Clayton-Bulwer Treaty", "link": "https://wikipedia.org/wiki/Clayton%E2%80%93Bulwer_Treaty"}]}, {"year": "1903", "text": "The Hay-Bunau-Varilla Treaty is signed by the United States and Panama, giving the United States exclusive rights over the Panama Canal Zone.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Hay%E2%80%93Bunau-Varilla_Treaty\" title=\"Hay-Bunau-Varilla Treaty\">Hay-Bunau-Varilla Treaty</a> is signed by the United States and <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, giving the United States exclusive rights over the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Zone\" title=\"Panama Canal Zone\">Panama Canal Zone</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hay%E2%80%93Bunau-Varilla_Treaty\" title=\"Hay-Bunau-Varilla Treaty\">Hay-Bunau-Varilla Treaty</a> is signed by the United States and <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, giving the United States exclusive rights over the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Zone\" title=\"Panama Canal Zone\">Panama Canal Zone</a>.", "links": [{"title": "Hay-Bunau-Varilla Treaty", "link": "https://wikipedia.org/wiki/Hay%E2%80%93Bunau-Varilla_Treaty"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Panama Canal Zone", "link": "https://wikipedia.org/wiki/Panama_Canal_Zone"}]}, {"year": "1905", "text": "Prince <PERSON> of Denmark becomes King <PERSON><PERSON><PERSON> VII of Norway.", "html": "1905 - Prince <PERSON> of Denmark becomes King <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">Haakon VII of Norway</a>.", "no_year_html": "Prince <PERSON> of Denmark becomes King <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">Haakon VII of Norway</a>.", "links": [{"title": "Haakon VII of Norway", "link": "https://wikipedia.org/wiki/Haakon_VII_of_Norway"}]}, {"year": "1909", "text": "Two United States warships are sent to Nicaragua after 500 revolutionaries (including two Americans) are executed by order of <PERSON>.", "html": "1909 - Two United States <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warships</a> are sent to <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> after 500 <a href=\"https://wikipedia.org/wiki/Revolutionaries\" class=\"mw-redirect\" title=\"Revolutionaries\">revolutionaries</a> (including two Americans) are executed by order of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_Zela<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Two United States <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warships</a> are sent to <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a> after 500 <a href=\"https://wikipedia.org/wiki/Revolutionaries\" class=\"mw-redirect\" title=\"Revolutionaries\">revolutionaries</a> (including two Americans) are executed by order of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_Zelaya\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Warship", "link": "https://wikipedia.org/wiki/Warship"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Revolutionaries", "link": "https://wikipedia.org/wiki/Revolutionaries"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_Zelaya"}]}, {"year": "1910", "text": "In their campaign for women's voting rights, hundreds of suffragettes march to the British Parliament in London. Several are beaten by police, newspaper attention embarrasses the authorities, and the march is dubbed Black Friday.", "html": "1910 - In their campaign for women's voting rights, hundreds of <a href=\"https://wikipedia.org/wiki/Suffragettes\" class=\"mw-redirect\" title=\"Suffragettes\">suffragettes</a> march to the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">British Parliament</a> in London. Several are beaten by police, newspaper attention embarrasses the authorities, and the march is dubbed <a href=\"https://wikipedia.org/wiki/Black_Friday_(1910)\" title=\"Black Friday (1910)\">Black Friday</a>.", "no_year_html": "In their campaign for women's voting rights, hundreds of <a href=\"https://wikipedia.org/wiki/Suffragettes\" class=\"mw-redirect\" title=\"Suffragettes\">suffragettes</a> march to the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">British Parliament</a> in London. Several are beaten by police, newspaper attention embarrasses the authorities, and the march is dubbed <a href=\"https://wikipedia.org/wiki/Black_Friday_(1910)\" title=\"Black Friday (1910)\">Black Friday</a>.", "links": [{"title": "Suffragettes", "link": "https://wikipedia.org/wiki/Suffragettes"}, {"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}, {"title": "Black Friday (1910)", "link": "https://wikipedia.org/wiki/Black_Friday_(1910)"}]}, {"year": "1916", "text": "World War I: First Battle of the Somme: In France, British Expeditionary Force commander <PERSON> calls off the battle which started on July 1, 1916.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">First Battle of the Somme</a>: In France, <a href=\"https://wikipedia.org/wiki/British_Expeditionary_Force_(World_War_I)\" title=\"British Expeditionary Force (World War I)\">British Expeditionary Force</a> commander <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> calls off the battle which started on July 1, 1916.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">First Battle of the Somme</a>: In France, <a href=\"https://wikipedia.org/wiki/British_Expeditionary_Force_(World_War_I)\" title=\"British Expeditionary Force (World War I)\">British Expeditionary Force</a> commander <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> calls off the battle which started on July 1, 1916.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of the Somme", "link": "https://wikipedia.org/wiki/Battle_of_the_Somme"}, {"title": "British Expeditionary Force (World War I)", "link": "https://wikipedia.org/wiki/British_Expeditionary_Force_(World_War_I)"}, {"title": "<PERSON>, 1st Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>"}]}, {"year": "1918", "text": "Latvia declares its independence from Russia.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> declares its independence from Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> declares its independence from Russia.", "links": [{"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}]}, {"year": "1928", "text": "Release of the animated short Steamboat Willie, the first fully synchronized sound cartoon.", "html": "1928 - Release of the animated short <i><a href=\"https://wikipedia.org/wiki/Steamboat_Willie\" title=\"Steamboat Willie\">Steamboat Willie</a></i>, the first fully synchronized sound <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a>.", "no_year_html": "Release of the animated short <i><a href=\"https://wikipedia.org/wiki/Steamboat_Willie\" title=\"Steamboat Willie\">Steamboat Willie</a></i>, the first fully synchronized sound <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a>.", "links": [{"title": "Steamboat Willie", "link": "https://wikipedia.org/wiki/Steamboat_Willie"}, {"title": "Cartoon", "link": "https://wikipedia.org/wiki/Cartoon"}]}, {"year": "1929", "text": "Grand Banks earthquake: Off the south coast of Newfoundland in the Atlantic Ocean, a Richter magnitude 7.2 submarine earthquake, centered on the  Grand Banks, breaks 12 submarine transatlantic telegraph cables and triggers a tsunami that destroys many south coast communities in the Burin Peninsula.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/1929_Grand_Banks_earthquake\" title=\"1929 Grand Banks earthquake\">Grand Banks earthquake</a>: Off the south coast of <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> in the Atlantic Ocean, a <a href=\"https://wikipedia.org/wiki/Richter_magnitude\" class=\"mw-redirect\" title=\"Richter magnitude\">Richter magnitude</a> 7.2 submarine earthquake, centered on the <a href=\"https://wikipedia.org/wiki/Grand_Banks_of_Newfoundland\" title=\"Grand Banks of Newfoundland\">Grand Banks</a>, breaks 12 submarine <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cables</a> and triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that destroys many south coast communities in the <a href=\"https://wikipedia.org/wiki/Burin_Peninsula\" title=\"Burin Peninsula\">Burin Peninsula</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1929_Grand_Banks_earthquake\" title=\"1929 Grand Banks earthquake\">Grand Banks earthquake</a>: Off the south coast of <a href=\"https://wikipedia.org/wiki/Newfoundland_(island)\" title=\"Newfoundland (island)\">Newfoundland</a> in the Atlantic Ocean, a <a href=\"https://wikipedia.org/wiki/Richter_magnitude\" class=\"mw-redirect\" title=\"Richter magnitude\">Richter magnitude</a> 7.2 submarine earthquake, centered on the <a href=\"https://wikipedia.org/wiki/Grand_Banks_of_Newfoundland\" title=\"Grand Banks of Newfoundland\">Grand Banks</a>, breaks 12 submarine <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cables</a> and triggers a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that destroys many south coast communities in the <a href=\"https://wikipedia.org/wiki/Burin_Peninsula\" title=\"Burin Peninsula\">Burin Peninsula</a>.", "links": [{"title": "1929 Grand Banks earthquake", "link": "https://wikipedia.org/wiki/1929_Grand_Banks_earthquake"}, {"title": "Newfoundland (island)", "link": "https://wikipedia.org/wiki/Newfoundland_(island)"}, {"title": "Richter magnitude", "link": "https://wikipedia.org/wiki/Richter_magnitude"}, {"title": "Grand Banks of Newfoundland", "link": "https://wikipedia.org/wiki/Grand_Banks_of_Newfoundland"}, {"title": "Transatlantic telegraph cable", "link": "https://wikipedia.org/wiki/Transatlantic_telegraph_cable"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "Burin Peninsula", "link": "https://wikipedia.org/wiki/Burin_Peninsula"}]}, {"year": "1940", "text": "World War II: German leader <PERSON> and Italian Foreign Minister <PERSON><PERSON><PERSON> meet to discuss <PERSON>'s disastrous Italian invasion of Greece.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Hitler\"><PERSON></a> and Italian Foreign Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Galeazzo <PERSON>\"><PERSON><PERSON><PERSON></a> meet to discuss <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a>'s disastrous <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Greece\" class=\"mw-redirect\" title=\"Italian invasion of Greece\">Italian invasion of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Italian Foreign Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>az<PERSON>\"><PERSON><PERSON><PERSON></a> meet to discuss <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benito <PERSON>\"><PERSON></a>'s disastrous <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Greece\" class=\"mw-redirect\" title=\"Italian invasion of Greece\">Italian invasion of Greece</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Galeazzo <PERSON>iano", "link": "https://wikipedia.org/wiki/Galeazzo_Ciano"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Italian invasion of Greece", "link": "https://wikipedia.org/wiki/Italian_invasion_of_Greece"}]}, {"year": "1943", "text": "World War II: Battle of Berlin: Four hundred and forty Royal Air Force planes bomb Berlin causing only light damage and killing 131. The RAF loses nine aircraft and 53 air crew.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin_(RAF_campaign)\" title=\"Battle of Berlin (RAF campaign)\">Battle of Berlin</a>: Four hundred and forty <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> planes bomb Berlin causing only light damage and killing 131. The RAF loses nine aircraft and 53 air crew.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin_(RAF_campaign)\" title=\"Battle of Berlin (RAF campaign)\">Battle of Berlin</a>: Four hundred and forty <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> planes bomb Berlin causing only light damage and killing 131. The RAF loses nine aircraft and 53 air crew.", "links": [{"title": "Battle of Berlin (RAF campaign)", "link": "https://wikipedia.org/wiki/Battle_of_Berlin_(RAF_campaign)"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}]}, {"year": "1944", "text": "The Popular Socialist Youth is founded in Cuba.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Popular_Socialist_Youth\" title=\"Popular Socialist Youth\">Popular Socialist Youth</a> is founded in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Popular_Socialist_Youth\" title=\"Popular Socialist Youth\">Popular Socialist Youth</a> is founded in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Popular Socialist Youth", "link": "https://wikipedia.org/wiki/Popular_Socialist_Youth"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1947", "text": "The Ballantyne's Department Store fire in Christchurch, New Zealand, kills 41; it is the worst fire disaster in the history of New Zealand.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Ballantyne%27s_fire\" class=\"mw-redirect\" title=\"Ballantyne's fire\">Ballantyne's Department Store fire</a> in <a href=\"https://wikipedia.org/wiki/Christchurch\" title=\"Christchurch\">Christchurch</a>, New Zealand, kills 41; it is the worst fire disaster in the history of New Zealand.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ballantyne%27s_fire\" class=\"mw-redirect\" title=\"Ballantyne's fire\">Ballantyne's Department Store fire</a> in <a href=\"https://wikipedia.org/wiki/Christchurch\" title=\"Christchurch\">Christchurch</a>, New Zealand, kills 41; it is the worst fire disaster in the history of New Zealand.", "links": [{"title": "Ballantyne's fire", "link": "https://wikipedia.org/wiki/Ballantyne%27s_fire"}, {"title": "Christchurch", "link": "https://wikipedia.org/wiki/Christchurch"}]}, {"year": "1949", "text": "The Iva Valley Shooting occurs after the coal miners of Enugu in Nigeria go on strike over withheld wages; 21 miners are shot dead and 51 are wounded by police under the supervision of the British colonial administration of Nigeria.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Iva_Valley\" title=\"Iva Valley\">Iva Valley</a> Shooting occurs after the coal miners of <a href=\"https://wikipedia.org/wiki/Enugu_(city)\" title=\"Enugu (city)\">Enugu</a> in <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> go on <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> over withheld wages; 21 miners are shot dead and 51 are wounded by police under the supervision of the <a href=\"https://wikipedia.org/wiki/Colonial_Nigeria\" title=\"Colonial Nigeria\">British colonial administration of Nigeria</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iva_Valley\" title=\"Iva Valley\">Iva Valley</a> Shooting occurs after the coal miners of <a href=\"https://wikipedia.org/wiki/Enugu_(city)\" title=\"Enugu (city)\">Enugu</a> in <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a> go on <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> over withheld wages; 21 miners are shot dead and 51 are wounded by police under the supervision of the <a href=\"https://wikipedia.org/wiki/Colonial_Nigeria\" title=\"Colonial Nigeria\">British colonial administration of Nigeria</a>.", "links": [{"title": "Iva Valley", "link": "https://wikipedia.org/wiki/Iva_Valley"}, {"title": "Enugu (city)", "link": "https://wikipedia.org/wiki/Enugu_(city)"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}, {"title": "Colonial Nigeria", "link": "https://wikipedia.org/wiki/Colonial_Nigeria"}]}, {"year": "1961", "text": "United States President <PERSON> sends 18,000 military advisors to South Vietnam.", "html": "1961 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends 18,000 military advisors to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends 18,000 military advisors to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1963", "text": "The first push-button telephone goes into service.", "html": "1963 - The first <a href=\"https://wikipedia.org/wiki/Push-button_telephone\" title=\"Push-button telephone\">push-button telephone</a> goes into service.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Push-button_telephone\" title=\"Push-button telephone\">push-button telephone</a> goes into service.", "links": [{"title": "Push-button telephone", "link": "https://wikipedia.org/wiki/Push-button_telephone"}]}, {"year": "1970", "text": "U.S. President <PERSON> asks the U.S. Congress for $155 million in supplemental aid for the Cambodian government.", "html": "1970 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> asks the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> for $155 million in supplemental aid for the <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodian</a> government.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> asks the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> for $155 million in supplemental aid for the <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodian</a> government.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}]}, {"year": "1971", "text": "Oman declares its independence from the United Kingdom.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a> declares its independence from the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a> declares its independence from the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "links": [{"title": "Oman", "link": "https://wikipedia.org/wiki/Oman"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1978", "text": "The McDonnell Douglas F/A-18 Hornet makes its first flight, at the Naval Air Test Center in Maryland, United States.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F/A-18_Hornet\" title=\"McDonnell Douglas F/A-18 Hornet\">McDonnell Douglas F/A-18 Hornet</a> makes its first flight, at the <a href=\"https://wikipedia.org/wiki/Naval_Air_Station_Patuxent_River\" title=\"Naval Air Station Patuxent River\">Naval Air Test Center</a> in <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F/A-18_Hornet\" title=\"McDonnell Douglas F/A-18 Hornet\">McDonnell Douglas F/A-18 Hornet</a> makes its first flight, at the <a href=\"https://wikipedia.org/wiki/Naval_Air_Station_Patuxent_River\" title=\"Naval Air Station Patuxent River\">Naval Air Test Center</a> in <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, United States.", "links": [{"title": "McDonnell Douglas F/A-18 Hornet", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F/A-18_Hornet"}, {"title": "Naval Air Station Patuxent River", "link": "https://wikipedia.org/wiki/Naval_Air_Station_Patuxent_River"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}]}, {"year": "1978", "text": "In Jonestown, Guyana, <PERSON> leads his Peoples Temple to a mass murder-suicide that claimed 918 lives in all, 909 of them in Jonestown itself, including over 270 children.", "html": "1978 - In <a href=\"https://wikipedia.org/wiki/Jonestown\" title=\"Jonestown\">Jonestown, Guyana</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads his <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> to a mass <a href=\"https://wikipedia.org/wiki/Murder%E2%80%93suicide\" title=\"Murder-suicide\">murder-suicide</a> that claimed 918 lives in all, 909 of them in Jonestown itself, including over 270 children.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Jonestown\" title=\"Jonestown\">Jonestown, Guyana</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads his <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> to a mass <a href=\"https://wikipedia.org/wiki/Murder%E2%80%93suicide\" title=\"Murder-suicide\">murder-suicide</a> that claimed 918 lives in all, 909 of them in Jonestown itself, including over 270 children.", "links": [{"title": "Jonestown", "link": "https://wikipedia.org/wiki/Jonestown"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peoples Temple", "link": "https://wikipedia.org/wiki/Peoples_Temple"}, {"title": "Murder-suicide", "link": "https://wikipedia.org/wiki/Murder%E2%80%93suicide"}]}, {"year": "1983", "text": "Aeroflot Flight 6833 is hijacked en route from Tbilisi to Leningrad. After returning to Tbilisi, the aircraft is subsequentially raided on the ground, resulting in seven deaths.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6833\" title=\"Aeroflot Flight 6833\">Aeroflot Flight 6833</a> is hijacked en route from <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a> to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Leningrad</a>. After returning to Tbilisi, the aircraft is subsequentially raided on the ground, resulting in seven deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_6833\" title=\"Aeroflot Flight 6833\">Aeroflot Flight 6833</a> is hijacked en route from <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a> to <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Leningrad</a>. After returning to Tbilisi, the aircraft is subsequentially raided on the ground, resulting in seven deaths.", "links": [{"title": "Aeroflot Flight 6833", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_6833"}, {"title": "Tbilisi", "link": "https://wikipedia.org/wiki/Tbilisi"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1985", "text": "The first comic of <PERSON> and <PERSON><PERSON><PERSON> is published in ten newspapers.", "html": "1985 - The first comic of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON></a> is published in ten newspapers.", "no_year_html": "The first comic of <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON><PERSON><PERSON>\"><PERSON> and <PERSON><PERSON><PERSON></a> is published in ten newspapers.", "links": [{"title": "<PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "King's Cross fire: In London, 31 people die in a fire at the city's busiest underground station, King's Cross St Pancras.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/King%27s_Cross_fire\" title=\"King's Cross fire\">King's Cross fire</a>: In London, 31 people die in a fire at the city's busiest <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">underground</a> station, <a href=\"https://wikipedia.org/wiki/King%27s_Cross_St_Pancras_tube_station\" title=\"King's Cross St Pancras tube station\">King's Cross St Pancras</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King%27s_Cross_fire\" title=\"King's Cross fire\">King's Cross fire</a>: In London, 31 people die in a fire at the city's busiest <a href=\"https://wikipedia.org/wiki/London_Underground\" title=\"London Underground\">underground</a> station, <a href=\"https://wikipedia.org/wiki/King%27s_Cross_St_Pancras_tube_station\" title=\"King's Cross St Pancras tube station\">King's Cross St Pancras</a>.", "links": [{"title": "King's Cross fire", "link": "https://wikipedia.org/wiki/King%27s_Cross_fire"}, {"title": "London Underground", "link": "https://wikipedia.org/wiki/London_Underground"}, {"title": "King's Cross St Pancras tube station", "link": "https://wikipedia.org/wiki/King%27s_Cross_St_Pancras_tube_station"}]}, {"year": "1991", "text": "Shiite Muslim kidnappers in Lebanon release Anglican Church envoys <PERSON> and <PERSON>.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>ite\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON>ite</a> Muslim kidnappers in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> release <a href=\"https://wikipedia.org/wiki/Anglican_Communion\" title=\"Anglican Communion\">Anglican Church</a> envoys <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON>ite</a> Muslim kidnappers in <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a> release <a href=\"https://wikipedia.org/wiki/Anglican_Communion\" title=\"Anglican Communion\">Anglican Church</a> envoys <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(academic)\" title=\"<PERSON> (academic)\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ite"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Anglican Communion", "link": "https://wikipedia.org/wiki/Anglican_Communion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_(academic)"}]}, {"year": "1991", "text": "After an 87-day siege, the Croatian city of Vukovar capitulates to the besieging Yugoslav People's Army and allied Serb paramilitary forces.", "html": "1991 - After an 87-day <a href=\"https://wikipedia.org/wiki/Battle_of_Vukovar\" title=\"Battle of Vukovar\">siege</a>, the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> city of <a href=\"https://wikipedia.org/wiki/Vukovar\" title=\"Vukovar\">Vukovar</a> capitulates to the besieging Yugoslav People's Army and allied Serb paramilitary forces.", "no_year_html": "After an 87-day <a href=\"https://wikipedia.org/wiki/Battle_of_Vukovar\" title=\"Battle of Vukovar\">siege</a>, the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> city of <a href=\"https://wikipedia.org/wiki/Vukovar\" title=\"Vukovar\">Vukovar</a> capitulates to the besieging Yugoslav People's Army and allied Serb paramilitary forces.", "links": [{"title": "Battle of Vukovar", "link": "https://wikipedia.org/wiki/Battle_of_Vukovar"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Vukova<PERSON>", "link": "https://wikipedia.org/wiki/Vukovar"}]}, {"year": "1991", "text": "The autonomous Croatian Community of Herzeg-Bosnia, which would in 1993 become a republic, was established in Bosnia and Herzegovina.", "html": "1991 - The autonomous <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Community of Herzeg-Bosnia</a>, which would in 1993 become a republic, was established in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "The autonomous <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Community of Herzeg-Bosnia</a>, which would in 1993 become a republic, was established in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Croatian Republic of Herzeg-Bosnia", "link": "https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1993", "text": "In the United States, the North American Free Trade Agreement (NAFTA) is approved by the House of Representatives.", "html": "1993 - In the United States, the <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a> (NAFTA) is approved by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a>.", "no_year_html": "In the United States, the <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a> (NAFTA) is approved by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a>.", "links": [{"title": "North American Free Trade Agreement", "link": "https://wikipedia.org/wiki/North_American_Free_Trade_Agreement"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1993", "text": "In South Africa, 21 political parties approve a new constitution, expanding voting rights and ending white minority rule.", "html": "1993 - In South Africa, 21 political parties approve a new <a href=\"https://wikipedia.org/wiki/Constitution_of_South_Africa\" title=\"Constitution of South Africa\">constitution</a>, expanding voting rights and ending white minority rule.", "no_year_html": "In South Africa, 21 political parties approve a new <a href=\"https://wikipedia.org/wiki/Constitution_of_South_Africa\" title=\"Constitution of South Africa\">constitution</a>, expanding voting rights and ending white minority rule.", "links": [{"title": "Constitution of South Africa", "link": "https://wikipedia.org/wiki/Constitution_of_South_Africa"}]}, {"year": "1996", "text": "A fire occurs on a train traveling through the Channel Tunnel from France to England causing several injuries and damaging approximately 500 metres (1,600 ft) of tunnel.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/1996_Channel_Tunnel_fire\" title=\"1996 Channel Tunnel fire\">fire</a> occurs on a train traveling through the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a> from France to England causing several injuries and damaging approximately 500 metres (1,600 ft) of tunnel.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1996_Channel_Tunnel_fire\" title=\"1996 Channel Tunnel fire\">fire</a> occurs on a train traveling through the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a> from France to England causing several injuries and damaging approximately 500 metres (1,600 ft) of tunnel.", "links": [{"title": "1996 Channel Tunnel fire", "link": "https://wikipedia.org/wiki/1996_Channel_Tunnel_fire"}, {"title": "Channel Tunnel", "link": "https://wikipedia.org/wiki/Channel_Tunnel"}]}, {"year": "1999", "text": "At Texas A&M University, the Aggie Bonfire collapses killing 12 students and injuring 27 others.", "html": "1999 - At Texas A&amp;M University, the <a href=\"https://wikipedia.org/wiki/Aggie_Bonfire#1999_collapse\" title=\"Aggie Bonfire\">Aggie Bonfire</a> collapses killing 12 students and injuring 27 others.", "no_year_html": "At Texas A&amp;M University, the <a href=\"https://wikipedia.org/wiki/Aggie_Bonfire#1999_collapse\" title=\"Aggie Bonfire\">Aggie Bonfire</a> collapses killing 12 students and injuring 27 others.", "links": [{"title": "Aggie <PERSON>", "link": "https://wikipedia.org/wiki/Aggie_Bonfire#1999_collapse"}]}, {"year": "2002", "text": "Iraq disarmament crisis: United Nations weapons inspectors led by <PERSON> arrive in Iraq.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> weapons inspectors led by <a href=\"https://wikipedia.org/wiki/<PERSON>_Blix\" title=\"<PERSON> Blix\"><PERSON></a> arrive in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_disarmament_crisis\" title=\"Iraq disarmament crisis\">Iraq disarmament crisis</a>: <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> weapons inspectors led by <a href=\"https://wikipedia.org/wiki/Hans_Blix\" title=\"<PERSON> Blix\"><PERSON></a> arrive in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "links": [{"title": "Iraq disarmament crisis", "link": "https://wikipedia.org/wiki/Iraq_disarmament_crisis"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2003", "text": "The Massachusetts Supreme Judicial Court rules 4-3 in Goodridge v. Department of Public Health that the state's ban on same-sex marriage is unconstitutional and gives the state legislature 180 days to change the law making Massachusetts the first state in the United States to grant marriage rights to same-sex couples.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Massachusetts_Supreme_Judicial_Court\" title=\"Massachusetts Supreme Judicial Court\">Massachusetts Supreme Judicial Court</a> rules 4-3 in <i><a href=\"https://wikipedia.org/wiki/Goodridge_v._Department_of_Public_Health\" title=\"Goodridge v. Department of Public Health\">Goodridge v. Department of Public Health</a></i> that the state's ban on same-sex marriage is unconstitutional and gives the state legislature 180<span class=\"nowrap\"> </span>days to change the law making Massachusetts the first state in the United States to grant marriage rights to same-sex couples.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massachusetts_Supreme_Judicial_Court\" title=\"Massachusetts Supreme Judicial Court\">Massachusetts Supreme Judicial Court</a> rules 4-3 in <i><a href=\"https://wikipedia.org/wiki/Goodridge_v._Department_of_Public_Health\" title=\"Goodridge v. Department of Public Health\">Goodridge v. Department of Public Health</a></i> that the state's ban on same-sex marriage is unconstitutional and gives the state legislature 180<span class=\"nowrap\"> </span>days to change the law making Massachusetts the first state in the United States to grant marriage rights to same-sex couples.", "links": [{"title": "Massachusetts Supreme Judicial Court", "link": "https://wikipedia.org/wiki/Massachusetts_Supreme_Judicial_Court"}, {"title": "Goodridge v. Department of Public Health", "link": "https://wikipedia.org/wiki/Goodridge_v._Department_of_Public_Health"}]}, {"year": "2012", "text": "Pope <PERSON><PERSON><PERSON><PERSON> of Alexandria becomes the 118th Pope of the Coptic Orthodox Church of Alexandria.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON><PERSON> II of Alexandria\">Pope <PERSON><PERSON><PERSON><PERSON> II of Alexandria</a> becomes the <a href=\"https://wikipedia.org/wiki/List_of_Coptic_Orthodox_Popes_of_Alexandria\" class=\"mw-redirect\" title=\"List of Coptic Orthodox Popes of Alexandria\">118th</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Coptic_Orthodox_Church_of_Alexandria\" class=\"mw-redirect\" title=\"Pope of the Coptic Orthodox Church of Alexandria\">Pope of the Coptic Orthodox Church of Alexandria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON><PERSON> II of Alexandria\">Pope <PERSON><PERSON><PERSON><PERSON> II of Alexandria</a> becomes the <a href=\"https://wikipedia.org/wiki/List_of_Coptic_Orthodox_Popes_of_Alexandria\" class=\"mw-redirect\" title=\"List of Coptic Orthodox Popes of Alexandria\">118th</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Coptic_Orthodox_Church_of_Alexandria\" class=\"mw-redirect\" title=\"Pope of the Coptic Orthodox Church of Alexandria\">Pope of the Coptic Orthodox Church of Alexandria</a>.", "links": [{"title": "<PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>wa<PERSON><PERSON>_II_of_Alexandria"}, {"title": "List of Coptic Orthodox Popes of Alexandria", "link": "https://wikipedia.org/wiki/List_of_Coptic_Orthodox_Pope<PERSON>_of_Alexandria"}, {"title": "<PERSON> of the Coptic Orthodox Church of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Coptic_Orthodox_Church_of_Alexandria"}]}, {"year": "2013", "text": "NASA launches the MAVEN probe to Mars.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <a href=\"https://wikipedia.org/wiki/MAVEN\" title=\"MAVEN\">MAVEN</a> probe to <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <a href=\"https://wikipedia.org/wiki/MAVEN\" title=\"MAVEN\">MAVEN</a> probe to <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "MAVEN", "link": "https://wikipedia.org/wiki/MAVEN"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "2020", "text": "The Utah monolith, built sometime in 2016 is discovered by state biologists of the Utah Division of Wildlife Resources.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/Utah_monolith\" title=\"Utah monolith\">Utah monolith</a>, built sometime in 2016 is discovered by state biologists of the <a href=\"https://wikipedia.org/wiki/Utah_Division_of_Wildlife_Resources\" title=\"Utah Division of Wildlife Resources\">Utah Division of Wildlife Resources</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Utah_monolith\" title=\"Utah monolith\">Utah monolith</a>, built sometime in 2016 is discovered by state biologists of the <a href=\"https://wikipedia.org/wiki/Utah_Division_of_Wildlife_Resources\" title=\"Utah Division of Wildlife Resources\">Utah Division of Wildlife Resources</a>.", "links": [{"title": "Utah monolith", "link": "https://wikipedia.org/wiki/Utah_monolith"}, {"title": "Utah Division of Wildlife Resources", "link": "https://wikipedia.org/wiki/Utah_Division_of_Wildlife_Resources"}]}], "Births": [{"year": "701", "text": "<PERSON><PERSON>, Mayan ruler (d. 757)", "html": "701 - <a href=\"https://wikipedia.org/wiki/Itzam_K%27an_Ahk_II\" class=\"mw-redirect\" title=\"Itzam K'an Ahk II\">Itzam K'an Ahk II</a>, Mayan ruler (d. 757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Itzam_K%27an_Ahk_II\" class=\"mw-redirect\" title=\"Itzam K'an Ahk II\">Itzam K'an Ahk II</a>, Mayan ruler (d. 757)", "links": [{"title": "Itzam K'an Ahk II", "link": "https://wikipedia.org/wiki/Itzam_K%27an_Ahk_II"}]}, {"year": "709", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 782)", "html": "709 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dnin\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dnin\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 782)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dnin"}]}, {"year": "1522", "text": "<PERSON><PERSON>, Count of Egmont (d. 1568)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont\" title=\"<PERSON><PERSON>, Count of Egmont\"><PERSON><PERSON>, Count of Egmont</a> (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont\" title=\"<PERSON><PERSON>, Count of Egmont\"><PERSON><PERSON>, Count of Egmont</a> (d. 1568)", "links": [{"title": "<PERSON><PERSON>, Count of Egmont", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont"}]}, {"year": "1571", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian physician and polymath (d. 1654)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/Hippolytus_Guarinonius\" title=\"Hippolytus Guarinonius\"><PERSON><PERSON><PERSON><PERSON></a>, Italian physician and polymath (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolytus_Guarinonius\" title=\"Hippolytus Guarinonius\"><PERSON><PERSON><PERSON><PERSON> Guarinonius</a>, Italian physician and polymath (d. 1654)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hippolytus_Guarinonius"}]}, {"year": "1576", "text": "<PERSON>, Count of Hanau-Münzenberg (d. 1612)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (d. 1612)", "links": [{"title": "<PERSON>, Count of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON>, Italian wife of <PERSON>, Holy Roman Emperor (d. 1686)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>_(1630%E2%80%931686)\" title=\"<PERSON><PERSON><PERSON> (1630-1686)\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1630%E2%80%931686)\" title=\"<PERSON><PERSON><PERSON> (1630-1686)\"><PERSON><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1686)", "links": [{"title": "<PERSON><PERSON><PERSON> (1630-1686)", "link": "https://wikipedia.org/wiki/Eleonora_Gonzaga_(1630%E2%80%931686)"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1647", "text": "<PERSON>, French philosopher and author (d. 1706)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1727", "text": "<PERSON><PERSON><PERSON>, French physician and explorer (d. 1773)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (d. 1773)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON>, German harpsichord player and composer (d. 1800)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Carl <PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1756", "text": "<PERSON>, English bishop and philosopher (d. 1837)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)\" class=\"mw-redirect\" title=\"<PERSON> (bishop, born 1756)\"><PERSON></a>, English bishop and philosopher (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)\" class=\"mw-redirect\" title=\"<PERSON> (bishop, born 1756)\"><PERSON></a>, English bishop and philosopher (d. 1837)", "links": [{"title": "<PERSON> (bishop, born 1756)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop,_born_1756)"}]}, {"year": "1772", "text": "Prince <PERSON> of Prussia (d. 1806)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia_(1772%E2%80%931806)\" title=\"Prince <PERSON> of Prussia (1772-1806)\">Prince <PERSON> of Prussia</a> (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia_(1772%E2%80%931806)\" title=\"Prince <PERSON> of Prussia (1772-1806)\">Prince <PERSON> of Prussia</a> (d. 1806)", "links": [{"title": "Prince <PERSON> of Prussia (1772-1806)", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia_(1772%E2%80%931806)"}]}, {"year": "1774", "text": "<PERSON><PERSON> of Prussia, Queen of the Netherlands (d. 1837)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Prussia,_Queen_of_the_Netherlands\" title=\"<PERSON><PERSON> of Prussia, Queen of the Netherlands\"><PERSON><PERSON> of Prussia, Queen of the Netherlands</a> (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Prussia,_Queen_of_the_Netherlands\" title=\"<PERSON><PERSON> of Prussia, Queen of the Netherlands\"><PERSON><PERSON> of Prussia, Queen of the Netherlands</a> (d. 1837)", "links": [{"title": "<PERSON><PERSON> of Prussia, Queen of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Prussia,_Queen_of_the_Netherlands"}]}, {"year": "1785", "text": "<PERSON>, Scottish painter and academic (d. 1841)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish painter and academic (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish painter and academic (d. 1841)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1786", "text": "<PERSON>, German composer and conductor (d. 1826)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, French artist, photographer and inventor (d. 1851)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, photographer and inventor (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, photographer and inventor (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Italian general and politician, 6th Prime Minister of Italy (d. 1878)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_La_Marmora\" title=\"<PERSON>mor<PERSON>\"><PERSON></a>, Italian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Marmora\" title=\"<PERSON>mor<PERSON>\"><PERSON></a>, Italian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1878)", "links": [{"title": "<PERSON> Marmora", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rrero_La_Marmora"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1810", "text": "<PERSON><PERSON>, American botanist and academic (d. 1888)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Asa Gray\"><PERSON><PERSON></a>, American botanist and academic (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"As<PERSON> Gray\"><PERSON><PERSON></a>, American botanist and academic (d. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, Finnish-Swedish geologist and explorer (d. 1901)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6ld\" title=\"<PERSON>\"><PERSON></a>, Finnish-Swedish geologist and explorer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ld\" title=\"<PERSON>\"><PERSON></a>, Finnish-Swedish geologist and explorer (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ld"}]}, {"year": "1833", "text": "<PERSON>, English-Australian politician, 17th Premier of Victoria (d. 1895)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1895)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1836", "text": "<PERSON><PERSON> <PERSON><PERSON>, English playwright, poet, and illustrator (d. 1911)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English playwright, poet, and illustrator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English playwright, poet, and illustrator (d. 1911)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, German physicist and educator (d. 1894)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/August_Kundt\" title=\"August Kundt\">August <PERSON></a>, German physicist and educator (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Kundt\" title=\"August Kundt\">August <PERSON></a>, German physicist and educator (d. 1894)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_Kundt"}]}, {"year": "1856", "text": "Grand Duke <PERSON> of Russia (d. 1929)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia_(1856%E2%80%931929)\" title=\"Grand Duke <PERSON> of Russia (1856-1929)\">Grand Duke <PERSON> of Russia</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia_(1856%E2%80%931929)\" title=\"Grand Duke <PERSON> of Russia (1856-1929)\">Grand Duke <PERSON> of Russia</a> (d. 1929)", "links": [{"title": "Grand Duke <PERSON> of Russia (1856-1929)", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia_(1856%E2%80%931929)"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Polish pianist, composer, and politician, 2nd Prime Minister of the Republic of Poland (d. 1941)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>\" title=\"Ignacy <PERSON>\">Ignacy <PERSON></a>, Polish pianist, composer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Poland\">Prime Minister of the Republic of Poland</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignacy_<PERSON>\" title=\"Ignacy <PERSON>\">Igna<PERSON></a>, Polish pianist, composer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"Prime Minister of the Republic of Poland\">Prime Minister of the Republic of Poland</a> (d. 1941)", "links": [{"title": "<PERSON>gna<PERSON>", "link": "https://wikipedia.org/wiki/Igna<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Republic of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Republic_of_Poland"}]}, {"year": "1862", "text": "<PERSON>, American politician (d. 1940)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Australian politician, Premier of Western Australia (d. 1920)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1871", "text": "<PERSON>, English Catholic priest and novelist (d. 1914)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest and novelist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest and novelist (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American author and poet (d. 1935)<", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clarence Day\"><PERSON></a>, American author and poet (d. 1935)&lt;", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clarence Day\"><PERSON></a>, American author and poet (d. 1935)&lt;", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, French racing driver (d. 1950)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9mery\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9mery\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_H%C3%A9mery"}]}, {"year": "1880", "text": "<PERSON><PERSON>, Bulgarian architect, designed the Central Sofia Market Hall (d. 1952)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Naum_Torbov\" title=\"Naum Torbov\"><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Central_Sofia_Market_Hall\" title=\"Central Sofia Market Hall\">Central Sofia Market Hall</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naum_Torbov\" title=\"Naum Torbov\"><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Central_Sofia_Market_Hall\" title=\"Central Sofia Market Hall\">Central Sofia Market Hall</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naum_<PERSON>bov"}, {"title": "Central Sofia Market Hall", "link": "https://wikipedia.org/wiki/Central_Sofia_Market_Hall"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Italian-American soprano (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American soprano (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American soprano (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amelita_Gall<PERSON>-<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, English painter and critic (d. 1957)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English painter and critic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English painter and critic (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_Lewis"}]}, {"year": "1882", "text": "<PERSON>, French philosopher and author (d. 1973)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, pioneering Canadian forensic pathologist (d. 1959)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pioneering Canadian <a href=\"https://wikipedia.org/wiki/Forensic_pathologist\" class=\"mw-redirect\" title=\"Forensic pathologist\">forensic pathologist</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, pioneering Canadian <a href=\"https://wikipedia.org/wiki/Forensic_pathologist\" class=\"mw-redirect\" title=\"Forensic pathologist\">forensic pathologist</a> (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Forensic pathologist", "link": "https://wikipedia.org/wiki/Forensic_pathologist"}]}, {"year": "1883", "text": "<PERSON>, American judge and politician (d. 1981)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier and politician, 47th Prime Minister of Hungary (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>renc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a> (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}]}, {"year": "1888", "text": "<PERSON>, American screenwriter, novelist and journalist (d. 1973)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, novelist and journalist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, novelist and journalist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Polish-Russian politician (d. 1939)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Russian politician (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Russian politician (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Italian architect, industrial designer, furniture designer, artist, and publisher.(d. 1979)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Gio_Ponti\" title=\"Gio Ponti\"><PERSON><PERSON></a>, Italian architect, industrial designer, furniture designer, artist, and publisher.(d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gio_Ponti\" title=\"Gio Ponti\"><PERSON><PERSON></a>, Italian architect, industrial designer, furniture designer, artist, and publisher.(d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gio_Ponti"}]}, {"year": "1897", "text": "<PERSON>, <PERSON>, English physicist and academic, Nobel Prize laureate (d. 1974)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1974)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1899", "text": "<PERSON>, Hungarian-American violinist and conductor (d. 1985)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American violinist and conductor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American violinist and conductor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American author, philosopher and civil rights activist (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher and civil rights activist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, philosopher and civil rights activist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American statistician (d. 1984)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Indian actor, director, producer, and screenwriter (d. 1984)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American golfer (d. 1968)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1968)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Japanese composer and guitarist (d. 1978)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and guitarist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and guitarist (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1904", "text": "<PERSON>, 1st Viscount <PERSON> of Merton, English lieutenant and politician, Secretary of State for the Colonies (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Merton\" title=\"<PERSON>, 1st Viscount <PERSON> of Merton\"><PERSON>, 1st Viscount <PERSON> of Merton</a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Merton\" title=\"<PERSON>, 1st Viscount <PERSON> of Merton\"><PERSON>, 1st Viscount <PERSON> of Merton</a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1983)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Merton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>_of_Merton"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Turkish author and poet (d. 1954)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Sait_Faik_Abas%C4%B1yan%C4%B1k\" title=\"Sait Faik Abasıyanık\"><PERSON>t Faik A<PERSON>ı<PERSON></a>, Turkish author and poet (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sait_Faik_Abas%C4%B1yan%C4%B1k\" title=\"Sait Faik Abasıyanık\"><PERSON><PERSON> Faik <PERSON>ı<PERSON></a>, Turkish author and poet (d. 1954)", "links": [{"title": "Sait Faik Abasıyanık", "link": "https://wikipedia.org/wiki/Sait_Faik_Abas%C4%B1yan%C4%B1k"}]}, {"year": "1906", "text": "<PERSON>, Greek-English car designer, designed the mini car (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English car designer, designed the <a href=\"https://wikipedia.org/wiki/Mini\" title=\"Mini\">mini car</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-English car designer, designed the <a href=\"https://wikipedia.org/wiki/Mini\" title=\"Mini\">mini car</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mini", "link": "https://wikipedia.org/wiki/Mini"}]}, {"year": "1906", "text": "<PERSON>, German-American novelist, short story writer, and critic (d. 1949)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American novelist, short story writer, and critic (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American novelist, short story writer, and critic (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American neurobiologist and academic, Nobel Prize laureate (d. 1997)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurobiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurobiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1907", "text": "<PERSON>, Czech actor (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Cuban singer-songwriter and guitarist (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Compay_Segundo\" title=\"Compay Segundo\">Co<PERSON><PERSON></a>, Cuban singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comp<PERSON>_<PERSON>do\" title=\"Compay Segundo\"><PERSON><PERSON><PERSON></a>, Cuban singer-songwriter and guitarist (d. 2003)", "links": [{"title": "Comp<PERSON>do", "link": "https://wikipedia.org/wiki/Compay_Segundo"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, American actress, comedian, and singer (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Imogene_Coca\" title=\"Imogene Coca\">Imo<PERSON></a>, American actress, comedian, and singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imogene_Coca\" title=\"Imogene Coca\">Imogene Coca</a>, American actress, comedian, and singer (d. 2001)", "links": [{"title": "Imogene Coca", "link": "https://wikipedia.org/wiki/Imogene_Coca"}]}, {"year": "1909", "text": "<PERSON>, American singer-songwriter and producer, co-founded Capitol Records (d. 1976)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Capitol_Records\" title=\"Capitol Records\">Capitol Records</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Capitol_Records\" title=\"Capitol Records\">Capitol Records</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capitol Records", "link": "https://wikipedia.org/wiki/Capitol_Records"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Italian poet and author (d. 2000)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Australian rugby league player and coach (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hey\"><PERSON></a>, Australian rugby league player and coach (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hey\"><PERSON></a>, Australian rugby league player and coach (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English author (d. 1977)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Hungarian-French painter and illustrator (d. 1999)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-French painter and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-French painter and illustrator (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 36th <PERSON><PERSON><PERSON><PERSON> (d. 1969)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>ji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 36th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>ji\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 36th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1915", "text": "<PERSON>, American baseball player and umpire (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Mexican actor and singer (d. 1957)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor and singer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actor and singer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/%C4%B0l<PERSON>_Berk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0<PERSON><PERSON>_<PERSON>rk\" title=\"<PERSON><PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 2008)", "links": [{"title": "İlhan Berk", "link": "https://wikipedia.org/wiki/%C4%B0l<PERSON>_<PERSON>rk"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Welsh soldier, judge, and politician, Victoria Cross recipient (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh soldier, judge, and politician, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh soldier, judge, and politician, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 2007)", "links": [{"title": "Tasker <PERSON>", "link": "https://wikipedia.org/wiki/Task<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American playwright and producer (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Egyptian lawyer and politician, 77th Prime Minister of Egypt (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Egypt", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Egypt"}]}, {"year": "1920", "text": "<PERSON>,  English football player and manager (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American springboard diver (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American springboard diver (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American springboard diver (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Nicaraguan politician, 70th President of Nicaragua (d. 1967)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, 70th <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan politician, 70th <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American painter (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American painter (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American astronaut (d. 1998)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American politician (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON> (<PERSON><PERSON>) <PERSON><PERSON><PERSON>, Danish psychologist and politician (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Lise_%C3%98<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> (<PERSON><PERSON>) <PERSON></a>, Danish psychologist and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lise_%C3%98<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> (Lise) <PERSON><PERSON></a>, Danish psychologist and politician (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lise_%C3%98<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American R&B singer-songwriter (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Canadian journalist and author (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>lton_Nash\" title=\"Knowlton Nash\"><PERSON><PERSON></a>, Canadian journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lton_Nash\" title=\"Knowlton Nash\"><PERSON><PERSON></a>, Canadian journalist and author (d. 2014)", "links": [{"title": "<PERSON><PERSON> Nash", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Nash"}]}, {"year": "1928", "text": "<PERSON>, Filipino lawyer and politician, 5th Prime Minister of the Philippines (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Salvador_Laurel\" title=\"Salvador Laurel\"><PERSON></a>, Filipino lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Laurel\" title=\"Salvador Laurel\"><PERSON></a>, Filipino lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines\" title=\"Prime Minister of the Philippines\">Prime Minister of the Philippines</a> (d. 2004)", "links": [{"title": "Salvador Laurel", "link": "https://wikipedia.org/wiki/Salvador_Laurel"}, {"title": "Prime Minister of the Philippines", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Philippines"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American soprano and educator (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Angelo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gianna_D%27Angelo"}]}, {"year": "1932", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American painter, photographer, and director (d. 2008)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, photographer, and director (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, photographer, and director (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Greek journalist and diplomat (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and diplomat (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek journalist and diplomat (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German philosopher and politician (d. 1997)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and politician (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Italian cardinal", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American trumpet player (d. 1995)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, American trumpet player (d. 1995)", "links": [{"title": "<PERSON> (trumpeter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)"}]}, {"year": "1938", "text": "<PERSON>, Iraqi-Lebanese archbishop (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese archbishop (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Lebanese archbishop (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Malagasy politician, Prime Minister of Madagascar", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malagasy politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Madagascar\" title=\"Prime Minister of Madagascar\">Prime Minister of Madagascar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malagasy politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Madagascar\" title=\"Prime Minister of Madagascar\">Prime Minister of Madagascar</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Madagascar", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Madagascar"}]}, {"year": "1938", "text": "<PERSON>, Austrian skier", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Baroness <PERSON> Paddington, English journalist and politician, Leader of the House of Lords", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>_<PERSON>_Paddington\" title=\"<PERSON>, Baroness <PERSON> Paddington\"><PERSON>, Baroness <PERSON> of Paddington</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>_<PERSON>_Paddington\" title=\"<PERSON>, Baroness <PERSON> Paddington\"><PERSON>, Baroness <PERSON> of Paddington</a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_House_of_Lords\" title=\"Leader of the House of Lords\">Leader of the House of Lords</a>", "links": [{"title": "<PERSON>, <PERSON> Paddington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_<PERSON>_Paddington"}, {"title": "Leader of the House of Lords", "link": "https://wikipedia.org/wiki/Leader_of_the_House_of_Lords"}]}, {"year": "1939", "text": "<PERSON>, Hong Kong-French singer-songwriter and actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-French singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-French singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American novelist and poet (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and poet (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and poet (d. 2003)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1941", "text": "<PERSON>, American race car driver (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actor and director (d. 2003)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Argentinian cardinal", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German fashion designer, founded JOOP!", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/JOOP!\" title=\"JOOP!\">JOOP!</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/JOOP!\" title=\"JOOP!\">JOOP!</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "JOOP!", "link": "https://wikipedia.org/wiki/JOOP!"}]}, {"year": "1944", "text": "<PERSON>, American astronomer, archaeoastronomer, author, Director Griffith Observatory", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a>, <a href=\"https://wikipedia.org/wiki/Archaeoastronomer\" class=\"mw-redirect\" title=\"Archaeoastronomer\">archaeoastronomer</a>, author, Director <a href=\"https://wikipedia.org/wiki/Griffith_Observatory\" title=\"Griffith Observatory\">Griffith Observatory</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a>, <a href=\"https://wikipedia.org/wiki/Archaeoastronomer\" class=\"mw-redirect\" title=\"Archaeoastronomer\">archaeoastronomer</a>, author, Director <a href=\"https://wikipedia.org/wiki/Griffith_Observatory\" title=\"Griffith Observatory\">Griffith Observatory</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Astronomer", "link": "https://wikipedia.org/wiki/Astronomer"}, {"title": "Archaeoastronomer", "link": "https://wikipedia.org/wiki/Archaeoastronomer"}, {"title": "Griffith Observatory", "link": "https://wikipedia.org/wiki/Griffith_Observatory"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, American tribal chief (d. 2010)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_Mankiller\" title=\"<PERSON><PERSON><PERSON> Mankiller\"><PERSON><PERSON><PERSON></a>, American tribal chief (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_Mankill<PERSON>\" title=\"Wil<PERSON> Mankiller\"><PERSON><PERSON><PERSON></a>, American tribal chief (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>er"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Sri Lankan lawyer and politician, 6th President of Sri Lanka", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ksa"}, {"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}]}, {"year": "1946", "text": "<PERSON>, American author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Estonian singer-songwriter, guitarist, and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nis_M%C3%A4gi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nis_M%C3%A4gi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nis_M%C3%A4gi"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Japanese sumo wrestler (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Kong%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kong%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sumo wrestler (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kong%C5%8D_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Cuban-American sculptor and painter (d. 1985)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Men<PERSON>\" title=\"Ana Men<PERSON>\"><PERSON></a>, Cuban-American sculptor and painter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Men<PERSON>\" title=\"Ana Mendieta\"><PERSON></a>, Cuban-American sculptor and painter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Mendieta"}]}, {"year": "1948", "text": "<PERSON>, American football player (d. 2010)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German rock drummer and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rock drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rock drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Cuban-American rock bass player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American rock bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American rock bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American businessman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist and author (d. 2019)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian lawyer and politician, 36th Premier of Queensland", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 36th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1952", "text": "<PERSON><PERSON>, English-American actor and director", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American rock guitarist (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American comedian and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American composer and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Irish-English footballer and painter (d. 1995)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English footballer and painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English footballer and painter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American football player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Warren Moon\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American computer scientist, developed Rake Software (d. 2014)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed <a href=\"https://wikipedia.org/wiki/Rake_(software)\" title=\"Rake (software)\"><PERSON>ke Software</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, developed <a href=\"https://wikipedia.org/wiki/Rake_(software)\" title=\"Rake (software)\">Rake Software</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> (software)", "link": "https://wikipedia.org/wiki/Rake_(software)"}]}, {"year": "1957", "text": "<PERSON>, American bassist, composer, producer, and writer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, producer, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist, composer, producer, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Argentine-Israeli footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-Israeli footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-Israeli footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Cuban-American actor and comedian", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)\" class=\"mw-redirect\" title=\"<PERSON> (Northern Ireland footballer)\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)\" class=\"mw-redirect\" title=\"<PERSON> (Northern Ireland footballer)\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON> (Northern Ireland footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_footballer)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Latvian canoeist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian canoeist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Turkish director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Ye%C5%9Fim_Ustao%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye%C5%9Fim_Ustao%C4%9Flu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ye%C5%9Fim_Ustao%C4%9Flu"}]}, {"year": "1960", "text": "<PERSON>, English singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish screenwriter and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American guitarist, songwriter, member of the thrash metal band Metallica", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, member of the thrash metal band <a href=\"https://wikipedia.org/wiki/Metallica\" title=\"Metallica\">Metallica</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, member of the thrash metal band <a href=\"https://wikipedia.org/wiki/Metallica\" title=\"Metallica\">Metallica</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Metallica", "link": "https://wikipedia.org/wiki/Metallica"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player (d. 1986)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bias"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bowles\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Todd Bowles\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>es"}]}, {"year": "1963", "text": "<PERSON>, Danish footballer and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Dutch author and poet (d. 2015)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch author and poet (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and musician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American stylist and journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stylist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stylist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American rapper, producer, actor, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, actor, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Romany_Malco"}]}, {"year": "1968", "text": "<PERSON>, American baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Egyptian actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese mixed martial artist and wrestler (d. 2014)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist and wrestler (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mixed martial artist and wrestler (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor and comedian", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American lawyer and journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Australian model and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, English chemist and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_<PERSON>y\" title=\"Thér<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English chemist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_<PERSON>ffey\" title=\"Thér<PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON><PERSON></a>, English chemist and politician", "links": [{"title": "Thér<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Coffey"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American poet and academic", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist and speed skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist and speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist and speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>of"}]}, {"year": "1973", "text": "<PERSON><PERSON>, English television presenter and business expert (d. 2024)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television presenter and business expert (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English television presenter and business expert (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, South African cricketer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>c_<PERSON>s"}]}, {"year": "1974", "text": "<PERSON>, Irish footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actress, model, and fashion designer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Chlo%C3%AB_Se<PERSON>gny\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, model, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlo%C3%<PERSON>_Se<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, model, and fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chlo%C3%AB_Sevigny"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Norwegian racing driver", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English actress and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1975", "text": "<PERSON>, English comedian, actor, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Dominican-American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American rapper, producer, and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pastor <PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Pastor <PERSON></a>, American rapper, producer, and actor", "links": [{"title": "Pastor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)\" title=\"<PERSON> (basketball, born 1975)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)\" title=\"<PERSON> (basketball, born 1975)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1975)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1975)"}]}, {"year": "1976", "text": "<PERSON>, American voice actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American rapper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Francis"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>hagra<PERSON>\" title=\"<PERSON>hagra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>gra<PERSON>\" title=\"<PERSON>hagra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Matt Welsh\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matt_<PERSON>\" title=\"Matt Welsh\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matt_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Egyptian actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Fabolous\" title=\"Fabolous\">Fabolo<PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabolous\" title=\"Fabolous\">Fabolo<PERSON></a>, American rapper", "links": [{"title": "Fabolous", "link": "https://wikipedia.org/wiki/Fabolous"}]}, {"year": "1978", "text": "<PERSON>, Irish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Italian fencer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fencer_born_1978)\" title=\"<PERSON><PERSON> (fencer born 1978)\"><PERSON><PERSON></a>, Italian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fencer_born_1978)\" title=\"<PERSON><PERSON> (fencer born 1978)\"><PERSON><PERSON></a>, Italian fencer", "links": [{"title": "<PERSON><PERSON> (fencer born 1978)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fencer_born_1978)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Indian playback singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian playback singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian playback singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Saudi Arabian terrorist, hijacker of United Airlines Flight 175 (d. 2001)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> al-Gham<PERSON>\"><PERSON><PERSON></a>, Saudi Arabian terrorist, hijacker of <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_175\" title=\"United Airlines Flight 175\">United Airlines Flight 175</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saudi Arabian terrorist, hijacker of <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_175\" title=\"United Airlines Flight 175\">United Airlines Flight 175</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "United Airlines Flight 175", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_175"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chi<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Belgian racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American actress and dancer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Filipino singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> del<PERSON>\"><PERSON><PERSON></a>, Filipino singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Iranian-American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nasi<PERSON>_<PERSON>edrad\" title=\"Nasi<PERSON> Pedrad\"><PERSON><PERSON><PERSON></a>, Iranian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nasi<PERSON>_<PERSON>rad\" title=\"<PERSON>si<PERSON>edrad\"><PERSON><PERSON><PERSON></a>, Iranian-American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nasim_Pedrad"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Italian actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>itt<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>itt<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American Wikipedia editor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Wikipedia editor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Wikipedia editor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and comedian", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and comedian", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1983", "text": "<PERSON>, Norwegian computer programmer and engineer, created DeCSS", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer programmer and engineer, created <a href=\"https://wikipedia.org/wiki/DeCSS\" title=\"DeCSS\">DeCSS</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer programmer and engineer, created <a href=\"https://wikipedia.org/wiki/DeCSS\" title=\"DeCSS\">DeCSS</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "DeCSS", "link": "https://wikipedia.org/wiki/DeCSS"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese singer and dancer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese singer and dancer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yo<PERSON><PERSON>_Chiba"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Enar_J%C3%A4%C3%A4ger\" title=\"Enar <PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enar_J%C3%A4%C3%A4ger\" title=\"Enar <PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enar_J%C3%A4%C3%A4ger"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American sprinter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cal_Clutterbuck\" title=\"<PERSON> Clutterbuck\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Clutterbuck\" title=\"Cal Clutterbuck\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Cal <PERSON>lut<PERSON>buck", "link": "https://wikipedia.org/wiki/Cal_Clutterbuck"}]}, {"year": "1987", "text": "<PERSON><PERSON>, South Korean actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Yoon_Park\" title=\"Yoon Park\"><PERSON><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoon_Park\" title=\"Yoon Park\"><PERSON><PERSON></a>, South Korean actor", "links": [{"title": "Yoon Park", "link": "https://wikipedia.org/wiki/Yoon_Park"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American soccer player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Ivorian sprinter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9e_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ivorian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9e_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ivorian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9e_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Chinese tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Iraqi-Australian swimmer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Thai tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Noppawan_Lertcheewakarn\" title=\"Noppawan Lertcheewakarn\"><PERSON><PERSON><PERSON> Le<PERSON>cheewakarn</a>, Thai tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noppawan_Lertcheewakarn\" title=\"Noppawan Lertcheewakarn\"><PERSON><PERSON><PERSON> Le<PERSON>akarn</a>, Thai tennis player", "links": [{"title": "Noppawan Lertcheewakarn", "link": "https://wikipedia.org/wiki/Noppawan_Lertcheewakarn"}]}, {"year": "1991", "text": "<PERSON>, Canadian-American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor and director", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Mexican footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Henry_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_Mart%C3%ADn"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Miller"}]}, {"year": "1992", "text": "<PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese sprinter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Montenegrin tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danka_Kovini%C4%87"}]}, {"year": "1994", "text": "<PERSON>, Austrian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Qatari footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Akra<PERSON>_Afif\" title=\"Akram Afif\"><PERSON><PERSON><PERSON></a>, Qatari footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Akra<PERSON>_Afif\" title=\"Akram Afif\"><PERSON><PERSON><PERSON></a>, Qatari footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akram_Afif"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Kirk\" title=\"<PERSON> Kirk\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kirk\" title=\"Christian Kirk\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Shea <PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "Shea <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Spanish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Robert_<PERSON>%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_<PERSON>%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_S%C3%A1nchez"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2004", "text": "<PERSON><PERSON>, Mexican-Argentine footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-Argentine footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "942", "text": "<PERSON><PERSON> of Cluny, Frankish abbot and saint (b. c. 878)", "html": "942 - <a href=\"https://wikipedia.org/wiki/Odo_of_Cluny\" title=\"<PERSON><PERSON> of Cluny\"><PERSON><PERSON> of Cluny</a>, Frankish abbot and saint (b. c. 878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odo_of_Cluny\" title=\"<PERSON><PERSON> of Cluny\"><PERSON><PERSON> of Cluny</a>, Frankish abbot and saint (b. c. 878)", "links": [{"title": "<PERSON><PERSON> of Cluny", "link": "https://wikipedia.org/wiki/Odo_of_Cluny"}]}, {"year": "953", "text": "<PERSON><PERSON><PERSON> of Saxony, duchess of Lorraine (b. 931)", "html": "953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_953)\" title=\"<PERSON><PERSON><PERSON> of Saxony (died 953)\"><PERSON><PERSON><PERSON> of Saxony</a>, duchess of Lorraine (b. 931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_953)\" title=\"<PERSON><PERSON><PERSON> of Saxony (died 953)\"><PERSON><PERSON><PERSON> of Saxony</a>, duchess of Lorraine (b. 931)", "links": [{"title": "<PERSON><PERSON><PERSON> of Saxony (died 953)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_953)"}]}, {"year": "1100", "text": "<PERSON> of Bayeux, archbishop of York", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bayeux\" title=\"<PERSON> of Bayeux\"><PERSON> of Bayeux</a>, archbishop of York", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bayeux\" title=\"<PERSON> of Bayeux\"><PERSON> of Bayeux</a>, archbishop of York", "links": [{"title": "<PERSON> of Bayeux", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bayeux"}]}, {"year": "1154", "text": "<PERSON> of <PERSON><PERSON>enne, French queen consort (b. 1092)", "html": "1154 - <a href=\"https://wikipedia.org/wiki/Adelaide_of_Maurienne\" title=\"Adelaide of Maurienne\"><PERSON> of Maurienne</a>, French queen consort (b. 1092)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_of_Maurienne\" title=\"Adelaide of Maurienne\"><PERSON> of Maurienne</a>, French queen consort (b. 1092)", "links": [{"title": "Adelaide of Maurienne", "link": "https://wikipedia.org/wiki/Adelaide_of_Maurienne"}]}, {"year": "1170", "text": "<PERSON> the <PERSON>, margrave of Brandenburg (b. c. 1100)", "html": "1170 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bear\" title=\"<PERSON> the Bear\"><PERSON> the Bear</a>, margrave of Brandenburg (b. c. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bear\" title=\"<PERSON> the Bear\"><PERSON> the Bear</a>, margrave of Brandenburg (b. c. 1100)", "links": [{"title": "<PERSON> the Bear", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1259", "text": "<PERSON>, English scholar and theologian", "html": "1259 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1305", "text": "<PERSON>, duke of Brittany (b. 1239)", "html": "1305 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1239)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1239)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1313", "text": "<PERSON> of Portugal, Portuguese infanta (b. 1290)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/Constance_of_Portugal\" title=\"Constance of Portugal\">Constance of Portugal</a>, Portuguese infanta (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constance_of_Portugal\" title=\"Constance of Portugal\"><PERSON> of Portugal</a>, Portuguese infanta (b. 1290)", "links": [{"title": "Constance of Portugal", "link": "https://wikipedia.org/wiki/Constance_of_Portugal"}]}, {"year": "1349", "text": "<PERSON>, Margrave of Meissen (b. 1310)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Margrave of Meissen\"><PERSON>, Margrave of Meissen</a> (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Meissen\" title=\"<PERSON>, Margrave of Meissen\"><PERSON>, Margrave of Meissen</a> (b. 1310)", "links": [{"title": "<PERSON>, Margrave of Meissen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1441", "text": "<PERSON>, English cleric, astronomer, astrologer, magister and alleged necromancer", "html": "1441 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, astronomer, astrologer, magister and alleged necromancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, astronomer, astrologer, magister and alleged necromancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1472", "text": "<PERSON><PERSON>, titular patriarch of Constantinople (b. c. 1403)", "html": "1472 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Be<PERSON>rion\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON></a>, titular patriarch of Constantinople (b. c. 1403)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Be<PERSON>rion\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, titular patriarch of Constantinople (b. c. 1403)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Basilius_Bessarion"}]}, {"year": "1482", "text": "<PERSON><PERSON><PERSON>, Ottoman politician, 17th Grand Vizier of the Ottoman Empire", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman politician, 17th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1559", "text": "<PERSON>, English bishop (b. 1474)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1474)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, Korean writer and politician (b. 1509)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyung\" class=\"mw-redirect\" title=\"<PERSON>-hyung\"><PERSON></a>, Korean writer and politician (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyung\" class=\"mw-redirect\" title=\"<PERSON>-hyung\"><PERSON>yung</a>, Korean writer and politician (b. 1509)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yung"}]}, {"year": "1590", "text": "<PERSON>, 6th Earl of Shrewsbury, English commander and politician, Lord <PERSON>ard of Ireland (b. 1528)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Earl_of_Shrewsbury\" title=\"<PERSON>, 6th Earl of Shrewsbury\"><PERSON>, 6th Earl of Shrewsbury</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Steward_of_Ireland\" title=\"Lord <PERSON> Steward of Ireland\">Lord <PERSON> Steward of Ireland</a> (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Shrewsbury\" title=\"<PERSON>, 6th Earl of Shrewsbury\"><PERSON>, 6th Earl of Shrewsbury</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Steward_of_Ireland\" title=\"Lord <PERSON> Steward of Ireland\">Lord <PERSON> Steward of Ireland</a> (b. 1528)", "links": [{"title": "<PERSON>, 6th Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Shrewsbury"}, {"title": "Lord High Steward of Ireland", "link": "https://wikipedia.org/wiki/Lord_High_Steward_of_Ireland"}]}, {"year": "1664", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian and Hungarian military leader and statesman (b. 1620)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian and Hungarian military leader and statesman (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian and Hungarian military leader and statesman (b. 1620)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Zr%C3%ADnyi"}]}, {"year": "1724", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese priest (b. 1685)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Bart<PERSON><PERSON>u_de_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese priest (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_de_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese priest (b. 1685)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bart<PERSON>meu_de_Gusm%C3%A3o"}]}, {"year": "1785", "text": "<PERSON>, Duke of Orléans (b. 1725)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (b. 1725)", "links": [{"title": "<PERSON>, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, French shipbuilder and merchant (b. 1719)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Ladebat\" title=\"<PERSON><PERSON><PERSON>bat\"><PERSON><PERSON><PERSON></a>, French shipbuilder and merchant (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Ladebat\" title=\"<PERSON><PERSON><PERSON> Ladebat\"><PERSON><PERSON><PERSON></a>, French shipbuilder and merchant (b. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American general and senator (b. 1733)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and senator (b. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and senator (b. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, English engineer (b. 1745)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, German philosopher and academic, founded the <PERSON><PERSON><PERSON><PERSON> (b. 1748)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Illuminati"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian general and politician, 10th and 14th President of Peru (b. 1785)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian general and politician, 10th and 14th President of Peru (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian general and politician, 10th and 14th President of Peru (b. 1785)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra"}]}, {"year": "1852", "text": "<PERSON>, French-American nun and saint (b. 1769)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Rose_Philippine_Duchesne\" title=\"Rose Philippine Duchesne\"><PERSON></a>, French-American nun and saint (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rose_Philippine_Duchesne\" title=\"Rose Philippine Duchesne\"><PERSON></a>, French-American nun and saint (b. 1769)", "links": [{"title": "Rose <PERSON> Duchesne", "link": "https://wikipedia.org/wiki/Rose_Philippine_Duchesne"}]}, {"year": "1886", "text": "<PERSON> <PERSON><PERSON>, American general, lawyer, and politician, 21st President of the United States (b. 1829)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1829)", "links": [{"title": "Chester A<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1889", "text": "<PERSON>, Irish-English poet and scholar (b. 1824)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English poet and scholar (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English poet and scholar (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, English-French poet (b. 1877)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-French poet (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-French poet (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_Vivien"}]}, {"year": "1922", "text": "<PERSON>, French author and critic (b. 1871)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, 10th Prince of Sulmona Italian race car driver, explorer, and politician (b. 1871)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona\" title=\"<PERSON><PERSON><PERSON>, 10th Prince of Sulmona\"><PERSON><PERSON><PERSON>, 10th Prince of Sulmona</a> Italian race car driver, explorer, and politician (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona\" title=\"<PERSON><PERSON><PERSON>, 10th Prince of Sulmona\"><PERSON><PERSON><PERSON>, 10th Prince of Sulmona</a> Italian race car driver, explorer, and politician (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>, 10th Prince of Sulmona", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_Prince_of_Sulmona"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician (b. 1872)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai\" title=\"V. O. Chidambaram Pillai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai\" title=\"V. O. Chidambaram Pillai\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (b. 1872)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._<PERSON>._Chidambaram_Pillai"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Georgian historian and academic (b. 1876)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian historian and academic (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian historian and academic (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Canadian poet and author (b. 1879)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian poet and author (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian poet and author (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German chemist and physicist, Nobel Prize laureate (b. 1864)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ernst"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1941", "text": "<PERSON>, Chilean-Australian journalist and politician, 3rd Prime Minister of Australia (b. 1867)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Australian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Australian journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1952", "text": "<PERSON>, French poet and author (b. 1895)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89luard\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89luard\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89luard"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Danish footballer, physicist, and academic, Nobel Prize laureate (b. 1885)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer, physicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer, physicist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1965", "text": "<PERSON>, American agronomist and bureaucrat, 33rd Vice President of the United States, 11th  US Secretary of Agriculture (b. 1888)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and bureaucrat, 33rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, 11th <a href=\"https://wikipedia.org/wiki/US_Secretary_of_Agriculture\" class=\"mw-redirect\" title=\"US Secretary of Agriculture\">US Secretary of Agriculture</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and bureaucrat, 33rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, 11th <a href=\"https://wikipedia.org/wiki/US_Secretary_of_Agriculture\" class=\"mw-redirect\" title=\"US Secretary of Agriculture\">US Secretary of Agriculture</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "US Secretary of Agriculture", "link": "https://wikipedia.org/wiki/US_Secretary_of_Agriculture"}]}, {"year": "1969", "text": "<PERSON>, English trombonist and bandleader (b. 1902)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English trombonist and bandleader (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English trombonist and bandleader (b. 1902)", "links": [{"title": "<PERSON> (bandleader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bandleader)"}]}, {"year": "1969", "text": "<PERSON>, American businessman and diplomat, 44th United States Ambassador to the United Kingdom (b. 1888)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American businessman and diplomat, 44th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American businessman and diplomat, 44th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"United States Ambassador to the United Kingdom\">United States Ambassador to the United Kingdom</a> (b. 1888)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Ambassador to the United Kingdom", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Kingdom"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and guitarist (<PERSON> Horse) (b. 1943)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (<a href=\"https://wikipedia.org/wiki/Crazy_Horse_(band)\" title=\"Crazy Horse (band)\">Crazy Horse</a>) (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (<a href=\"https://wikipedia.org/wiki/Crazy_Horse_(band)\" title=\"Crazy Horse (band)\">Crazy Horse</a>) (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Crazy Horse (band)", "link": "https://wikipedia.org/wiki/Crazy_Horse_(band)"}]}, {"year": "1976", "text": "<PERSON>, American-French photographer and painter (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ray\" title=\"Man Ray\"><PERSON></a>, American-French photographer and painter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ray\" title=\"Man Ray\"><PERSON></a>, American-French photographer and painter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian-Austrian lawyer and politician, 15th Federal Chancellor of Austria (b. 1897)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Federal_Chancellor_of_Austria\" class=\"mw-redirect\" title=\"Federal Chancellor of Austria\">Federal Chancellor of Austria</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Austrian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Federal_Chancellor_of_Austria\" class=\"mw-redirect\" title=\"Federal Chancellor of Austria\">Federal Chancellor of Austria</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Chancellor of Austria", "link": "https://wikipedia.org/wiki/Federal_Chancellor_of_Austria"}]}, {"year": "1978", "text": "<PERSON>, American cult leader, founded Peoples Temple (b. 1931)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader, founded <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader, founded <a href=\"https://wikipedia.org/wiki/Peoples_Temple\" title=\"Peoples Temple\">Peoples Temple</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peoples Temple", "link": "https://wikipedia.org/wiki/Peoples_Temple"}]}, {"year": "1978", "text": "<PERSON>, American soldier, educator, and politician (b. 1925)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, educator, and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player, coach, and manager (b. 1901)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Canadian soldier, ice hockey player, and businessman (b. 1895)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soldier, ice hockey player, and businessman (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soldier, ice hockey player, and businessman (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Conn_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American journalist and author (b. 1907)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American model (b. 1960)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_Carangi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>_Carangi\" title=\"<PERSON><PERSON> Carang<PERSON>\"><PERSON><PERSON></a>, American model (b. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gia_<PERSON>ngi"}]}, {"year": "1987", "text": "<PERSON>, French cyclist (b. 1934)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak lawyer and politician, 9th President of Czechoslovakia (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gust%C3%A1v_Hus%C3%A1k"}, {"title": "President of Czechoslovakia", "link": "https://wikipedia.org/wiki/President_of_Czechoslovakia"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American singer-songwriter and bandleader (The Cab Calloway Orchestra) (b. 1907)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Cab_Calloway\" title=\"Cab Calloway\"><PERSON><PERSON></a>, American singer-songwriter and bandleader (<a href=\"https://wikipedia.org/wiki/The_Cab_Calloway_Orchestra\" title=\"The Cab Calloway Orchestra\">The Cab Calloway Orchestra</a>) (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ab_Calloway\" title=\"Cab Calloway\"><PERSON><PERSON></a>, American singer-songwriter and bandleader (<a href=\"https://wikipedia.org/wiki/The_Cab_Calloway_Orchestra\" title=\"The Cab Calloway Orchestra\">The Cab Calloway Orchestra</a>) (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cab_Calloway"}, {"title": "The Cab Calloway Orchestra", "link": "https://wikipedia.org/wiki/The_Cab_Calloway_Orchestra"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Austrian jet engine pioneer (b. 1900)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian jet engine pioneer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian jet engine pioneer (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian painter and illustrator (b. 1945)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and illustrator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and illustrator (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Romanian-English journalist (b. 1909)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Miron_Grindea\" title=\"Miron Grindea\"><PERSON><PERSON></a>, Romanian-English journalist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miron_Grindea\" title=\"Mir<PERSON> Grindea\"><PERSON><PERSON></a>, Romanian-English journalist (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mir<PERSON>_<PERSON>dea"}]}, {"year": "1998", "text": "<PERSON>, Indian-Canadian journalist and publisher (b. 1936)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Canadian journalist and publisher (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Canadian journalist and publisher (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American composer and author (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer and guitarist (b. 1941)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Polish-American football player 1939 All-America, 1941 New York Giants draft (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-<a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player <a href=\"https://wikipedia.org/wiki/1939_College_Football_All-America_Team\" title=\"1939 College Football All-America Team\">1939 All-America</a>, <a href=\"https://wikipedia.org/wiki/1941_NFL_draft\" title=\"1941 NFL draft\">1941</a> <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> draft (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-<a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> player <a href=\"https://wikipedia.org/wiki/1939_College_Football_All-America_Team\" title=\"1939 College Football All-America Team\">1939 All-America</a>, <a href=\"https://wikipedia.org/wiki/1941_NFL_draft\" title=\"1941 NFL draft\">1941</a> <a href=\"https://wikipedia.org/wiki/New_York_Giants\" title=\"New York Giants\">New York Giants</a> draft (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}, {"title": "1939 College Football All-America Team", "link": "https://wikipedia.org/wiki/1939_College_Football_All-America_Team"}, {"title": "1941 NFL draft", "link": "https://wikipedia.org/wiki/1941_NFL_draft"}, {"title": "New York Giants", "link": "https://wikipedia.org/wiki/New_York_Giants"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1928)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American composer and conductor (b. 1948)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American physicist and academic (b. 1905)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American pianist and composer (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American basketball player (b. 1944)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Robbins"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Dominican comedian and television host (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican comedian and television host (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican comedian and television host (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English-American astronomer and academic (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American astronomer and academic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish clown, singer, and accordion player (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Berm%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Spanish clown, singer, and accordion player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Berm%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Spanish clown, singer, and accordion player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilio_<PERSON>g%C3%B3n_Berm%C3%BAdez"}]}, {"year": "2012", "text": "<PERSON>, American businesswoman and philanthropist (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and philanthropist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and philanthropist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1983)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1983)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian nadaswaram player and composer (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/S._R._<PERSON><PERSON>_V<PERSON><PERSON><PERSON>\" title=\"S. R. <PERSON><PERSON> Vaidyana<PERSON>\">S. R<PERSON> <PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Nadaswaram\" title=\"Nadaswaram\">nadas<PERSON>m</a> player and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._R._<PERSON><PERSON>_V<PERSON><PERSON><PERSON>\" title=\"S. R. <PERSON><PERSON> Vaidyanathan\">S. R<PERSON> <PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Nadaswaram\" title=\"Nadaswaram\">nadas<PERSON>m</a> player and composer (b. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadaswaram"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>,  Serbian martial artist, founded Real Aikido (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ljubomir_Vra%C4%8Darevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian martial artist, founded <a href=\"https://wikipedia.org/wiki/Real_Aikido\" title=\"Real Aikido\">Real Aikido</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ljubomir_Vra%C4%8Darevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian martial artist, founded <a href=\"https://wikipedia.org/wiki/Real_Aikido\" title=\"Real Aikido\">Real Aikido</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ljubomir_Vra%C4%8Darevi%C4%87"}, {"title": "Real Aikido", "link": "https://wikipedia.org/wiki/Real_Aikido"}]}, {"year": "2013", "text": "<PERSON>, Canadian director and producer (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and producer (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Argentinian journalist and author (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian journalist and author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian journalist and author (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Jordanian educator and politician, 48th Prime Minister of Jordan (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jordanian educator and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Jordan\" class=\"mw-redirect\" title=\"List of Prime Ministers of Jordan\">Prime Minister of Jordan</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jordanian educator and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Jordan\" class=\"mw-redirect\" title=\"List of Prime Ministers of Jordan\">Prime Minister of Jordan</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Jordan", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Jordan"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian director and producer (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/C._Rudhraiya\" class=\"mw-redirect\" title=\"C. Rudhraiya\"><PERSON><PERSON></a>, Indian director and producer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._Rudhraiya\" class=\"mw-redirect\" title=\"C. Rudhraiya\"><PERSON><PERSON></a>, Indian director and producer (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Rud<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian-Moroccan terrorist (b. 1987)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian-Moroccan terrorist (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian-Moroccan terrorist (b. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American golfer (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American golfer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American golfer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, New Zealand rugby player (b. 1975)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American soul and funk singer (b. 1956)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, American soul and funk singer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, American soul and funk singer (b. 1956)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2016", "text": "<PERSON>, American surgeon and scientist (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and scientist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and scientist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Scottish-Australian hard rock guitarist (b. 1953)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian hard rock guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian hard rock guitarist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Canadian actor, comedian and writer (b. 1973) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, comedian and writer (b. 1973) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, comedian and writer (b. 1973) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress and talk show host (b. 1944)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Tabassum\" title=\"Ta<PERSON>su<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and talk show host (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tabassum\" title=\"Ta<PERSON>su<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and talk show host (b. 1944)", "links": [{"title": "Tabassum", "link": "https://wikipedia.org/wiki/Tabassum"}]}, {"year": "2024", "text": "<PERSON>, French singer and composer (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer and composer (b. 1929)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "2024", "text": "<PERSON>, American travel writer (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American travel writer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American basketball player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American basketball player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian drummer, record producer and actor (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer, record producer and actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer, record producer and actor (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}