{"date": "May 23", "url": "https://wikipedia.org/wiki/May_23", "data": {"Events": [{"year": "1430", "text": "<PERSON> of Arc is captured at the Siege of Compiègne by troops from the Burgundian faction.", "html": "1430 - <a href=\"https://wikipedia.org/wiki/Joan_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> is captured at the <a href=\"https://wikipedia.org/wiki/Siege_of_Compi%C3%A8gne\" title=\"Siege of Compiègne\">Siege of Compiègne</a> by troops from the <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundian faction</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> is captured at the <a href=\"https://wikipedia.org/wiki/Siege_of_Compi%C3%A8gne\" title=\"Siege of Compiègne\">Siege of Compiègne</a> by troops from the <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundian faction</a>.", "links": [{"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Siege of Compiègne", "link": "https://wikipedia.org/wiki/Siege_of_Compi%C3%A8gne"}, {"title": "Duchy of Burgundy", "link": "https://wikipedia.org/wiki/Duchy_of_Burgundy"}]}, {"year": "1498", "text": "<PERSON><PERSON><PERSON> is burned at the stake in Florence, Italy.", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a> is burned at the stake in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON>von<PERSON></a> is burned at the stake in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>, Italy.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Savonarola"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}]}, {"year": "1533", "text": "The marriage of King <PERSON> to <PERSON> Aragon is declared null and void.", "html": "1533 - The marriage of King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON></a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Aragon\"><PERSON> Aragon</a> is declared null and void.", "no_year_html": "The marriage of King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII\" title=\"<PERSON> VIII\"><PERSON> VIII</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Aragon\"><PERSON> Aragon</a> is declared null and void.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "Dutch rebels led by <PERSON> Nassau, defeat <PERSON>, Duke of Arenberg, and his loyalist troops in the Battle of Heiligerlee, opening the Eighty Years' War.", "html": "1568 - Dutch rebels led by <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> Nassau</a>, defeat <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Arenberg\" title=\"<PERSON>, Duke of Arenberg\"><PERSON>, Duke of Arenberg</a>, and his loyalist troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heiligerlee_(1568)\" title=\"Battle of Heiligerlee (1568)\">Battle of Heiligerlee</a>, opening the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "no_year_html": "Dutch rebels led by <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a>, defeat <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Arenberg\" title=\"<PERSON>, Duke of Arenberg\"><PERSON>, Duke of Arenberg</a>, and his loyalist troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heiligerlee_(1568)\" title=\"Battle of Heiligerlee (1568)\">Battle of Heiligerlee</a>, opening the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>.", "links": [{"title": "Louis of Nassau", "link": "https://wikipedia.org/wiki/Louis_of_Nassau"}, {"title": "<PERSON>, Duke of Arenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_Arenberg"}, {"title": "Battle of Heiligerlee (1568)", "link": "https://wikipedia.org/wiki/Battle_of_Heiligerlee_(1568)"}, {"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}]}, {"year": "1609", "text": "Official ratification of the Second Virginia Charter takes place.", "html": "1609 - Official ratification of the <a href=\"https://wikipedia.org/wiki/Second_Virginia_Charter\" title=\"Second Virginia Charter\">Second Virginia Charter</a> takes place.", "no_year_html": "Official ratification of the <a href=\"https://wikipedia.org/wiki/Second_Virginia_Charter\" title=\"Second Virginia Charter\">Second Virginia Charter</a> takes place.", "links": [{"title": "Second Virginia Charter", "link": "https://wikipedia.org/wiki/Second_Virginia_Charter"}]}, {"year": "1618", "text": "The Second Defenestration of Prague precipitates the Thirty Years' War.", "html": "1618 - The <a href=\"https://wikipedia.org/wiki/Defenestrations_of_Prague\" title=\"Defenestrations of Prague\">Second Defenestration of Prague</a> precipitates the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Defenestrations_of_Prague\" title=\"Defenestrations of Prague\">Second Defenestration of Prague</a> precipitates the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "Defenestrations of Prague", "link": "https://wikipedia.org/wiki/Defenestrations_of_Prague"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1706", "text": "<PERSON>, 1st Duke of Marlborough, defeats a French army under Marshal <PERSON>, duc <PERSON> at the Battle of Ramillies.", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, defeats a French army under <a href=\"https://wikipedia.org/wiki/Marshal_of_France\" title=\"Marshal of France\">Marshal</a> <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ramillies\" title=\"Battle of Ramillies\">Battle of Ramillies</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Marlborough\" title=\"<PERSON>, 1st Duke of Marlborough\"><PERSON>, 1st Duke of Marlborough</a>, defeats a French army under <a href=\"https://wikipedia.org/wiki/Marshal_of_France\" title=\"Marshal of France\">Marshal</a> <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ramillies\" title=\"Battle of Ramillies\">Battle of Ramillies</a>.", "links": [{"title": "<PERSON>, 1st Duke of Marlborough", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Marlborough"}, {"title": "Marshal of France", "link": "https://wikipedia.org/wiki/Marshal_of_France"}, {"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>,_du<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Ramillies", "link": "https://wikipedia.org/wiki/Battle_of_Ramillies"}]}, {"year": "1788", "text": "South Carolina became the eighth state to ratify the United States Constitution.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> became the eighth state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> became the eighth state to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "links": [{"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1793", "text": "Battle of Famars during the Flanders Campaign of the War of the First Coalition.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Battle_of_Famars\" title=\"Battle of Famars\">Battle of Famars</a> during the <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Famars\" title=\"Battle of Famars\">Battle of Famars</a> during the <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a> of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "links": [{"title": "Battle of Famars", "link": "https://wikipedia.org/wiki/Battle_of_Famars"}, {"title": "Low Countries theatre of the War of the First Coalition", "link": "https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition"}, {"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}]}, {"year": "1829", "text": "Accordion patent granted to <PERSON><PERSON> in Vienna, Austrian Empire.", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Accordion\" title=\"Accordion\">Accordion</a> patent granted to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Accordion\" title=\"Accordion\">Accordion</a> patent granted to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrian Empire</a>.", "links": [{"title": "Accordion", "link": "https://wikipedia.org/wiki/Accordion"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}]}, {"year": "1844", "text": "Báb: A merchant of Shiraz announces that he is a Prophet and founds a religious movement. He is considered to be a forerunner of the Baháʼí Faith.", "html": "1844 - <a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>: A merchant of Shiraz announces that he is a Prophet and founds a religious movement. He is considered to be a forerunner of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A1b\" title=\"Báb\"><PERSON><PERSON><PERSON></a>: A merchant of Shiraz announces that he is a Prophet and founds a religious movement. He is considered to be a forerunner of the <a href=\"https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith\" title=\"Baháʼí Faith\">Baháʼí Faith</a>.", "links": [{"title": "B<PERSON>b", "link": "https://wikipedia.org/wiki/B%C3%A1b"}, {"title": "Baháʼí Faith", "link": "https://wikipedia.org/wiki/Bah%C3%A1%CA%BC%C3%AD_Faith"}]}, {"year": "1846", "text": "Mexican-American War: President <PERSON> of Mexico unofficially declares war on the United States.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Mexico unofficially declares war on the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Mexico unofficially declares war on the United States.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "The General German Workers' Association, a precursor of the modern Social Democratic Party of Germany, is founded in Leipzig, Kingdom of Saxony.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/General_German_Workers%27_Association\" title=\"General German Workers' Association\">General German Workers' Association</a>, a precursor of the modern <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany\" title=\"Social Democratic Party of Germany\">Social Democratic Party of Germany</a>, is founded in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Saxony\" title=\"Kingdom of Saxony\">Kingdom of Saxony</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_German_Workers%27_Association\" title=\"General German Workers' Association\">General German Workers' Association</a>, a precursor of the modern <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany\" title=\"Social Democratic Party of Germany\">Social Democratic Party of Germany</a>, is founded in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Saxony\" title=\"Kingdom of Saxony\">Kingdom of Saxony</a>.", "links": [{"title": "General German Workers' Association", "link": "https://wikipedia.org/wiki/General_German_Workers%27_Association"}, {"title": "Social Democratic Party of Germany", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_of_Germany"}, {"title": "Leipzig", "link": "https://wikipedia.org/wiki/Leipzig"}, {"title": "Kingdom of Saxony", "link": "https://wikipedia.org/wiki/Kingdom_of_Saxony"}]}, {"year": "1873", "text": "The Canadian Parliament establishes the North-West Mounted Police, the forerunner of the Royal Canadian Mounted Police.", "html": "1873 - The Canadian Parliament establishes the <a href=\"https://wikipedia.org/wiki/North-West_Mounted_Police\" title=\"North-West Mounted Police\">North-West Mounted Police</a>, the forerunner of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a>.", "no_year_html": "The Canadian Parliament establishes the <a href=\"https://wikipedia.org/wiki/North-West_Mounted_Police\" title=\"North-West Mounted Police\">North-West Mounted Police</a>, the forerunner of the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a>.", "links": [{"title": "North-West Mounted Police", "link": "https://wikipedia.org/wiki/North-West_Mounted_Police"}, {"title": "Royal Canadian Mounted Police", "link": "https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police"}]}, {"year": "1900", "text": "American Civil War: Sergeant <PERSON> is awarded the Medal of Honor for his heroism in the Assault on the Battery Wagner in 1863.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Sergeant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for his heroism in the <a href=\"https://wikipedia.org/wiki/Assault_on_the_Battery_Wagner\" class=\"mw-redirect\" title=\"Assault on the Battery Wagner\">Assault on the Battery Wagner</a> in 1863.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Sergeant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> for his heroism in the <a href=\"https://wikipedia.org/wiki/Assault_on_the_Battery_Wagner\" class=\"mw-redirect\" title=\"Assault on the Battery Wagner\">Assault on the Battery Wagner</a> in 1863.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}, {"title": "Assault on the Battery Wagner", "link": "https://wikipedia.org/wiki/Assault_on_the_Battery_Wagner"}]}, {"year": "1905", "text": "Sultan <PERSON> publicly announces the creation of the Ullah millet for the Aromanians of the empire, which had been established one day earlier. For this reason, the Aromanian National Day is usually celebrated on May 23, although some do so on May 22 instead.", "html": "1905 - Sultan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> II</a> publicly announces the creation of the <a href=\"https://wikipedia.org/wiki/Ullah_millet\" title=\"Ullah millet\"><PERSON>llah millet</a> for the <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> of the empire, which had been established one day earlier. For this reason, the <a href=\"https://wikipedia.org/wiki/Aromanian_National_Day\" title=\"Aromanian National Day\">Aromanian National Day</a> is usually celebrated on May 23, although some do so on <a href=\"https://wikipedia.org/wiki/May_22\" title=\"May 22\">May 22</a> instead.", "no_year_html": "Sultan <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> publicly announces the creation of the <a href=\"https://wikipedia.org/wiki/Ullah_millet\" title=\"Ullah millet\">Ullah millet</a> for the <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> of the empire, which had been established one day earlier. For this reason, the <a href=\"https://wikipedia.org/wiki/Aromanian_National_Day\" title=\"Aromanian National Day\">Aromanian National Day</a> is usually celebrated on May 23, although some do so on <a href=\"https://wikipedia.org/wiki/May_22\" title=\"May 22\">May 22</a> instead.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> millet", "link": "https://wikipedia.org/wiki/U<PERSON>_millet"}, {"title": "Aromanians", "link": "https://wikipedia.org/wiki/Aromanians"}, {"title": "Aromanian National Day", "link": "https://wikipedia.org/wiki/Aromanian_National_Day"}, {"title": "May 22", "link": "https://wikipedia.org/wiki/May_22"}]}, {"year": "1907", "text": "The unicameral Parliament of Finland gathers for its first plenary session.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/Unicameralism\" title=\"Unicameralism\">unicameral</a> <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> gathers for its first plenary session.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Unicameralism\" title=\"Unicameralism\">unicameral</a> <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Parliament of Finland</a> gathers for its first plenary session.", "links": [{"title": "Unicameralism", "link": "https://wikipedia.org/wiki/Unicameralism"}, {"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}]}, {"year": "1911", "text": "The New York Public Library is dedicated.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/New_York_Public_Library\" title=\"New York Public Library\">New York Public Library</a> is dedicated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Public_Library\" title=\"New York Public Library\">New York Public Library</a> is dedicated.", "links": [{"title": "New York Public Library", "link": "https://wikipedia.org/wiki/New_York_Public_Library"}]}, {"year": "1915", "text": "World War I: Italy joins the Allies, fulfilling its part of the Treaty of London.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Italy joins the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>, fulfilling its part of the <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1915)\" title=\"Treaty of London (1915)\">Treaty of London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Italy joins the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>, fulfilling its part of the <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1915)\" title=\"Treaty of London (1915)\">Treaty of London</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "Treaty of London (1915)", "link": "https://wikipedia.org/wiki/Treaty_of_London_(1915)"}]}, {"year": "1932", "text": "In Brazil, four students are shot and killed during a manifestation against the Brazilian dictator <PERSON><PERSON><PERSON>, which resulted in the outbreak of the Constitutionalist Revolution several weeks later.", "html": "1932 - In <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, four students are shot and killed during a manifestation against the Brazilian dictator <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, which resulted in the outbreak of the <a href=\"https://wikipedia.org/wiki/Constitutionalist_Revolution\" title=\"Constitutionalist Revolution\">Constitutionalist Revolution</a> several weeks later.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, four students are shot and killed during a manifestation against the Brazilian dictator <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, which resulted in the outbreak of the <a href=\"https://wikipedia.org/wiki/Constitutionalist_Revolution\" title=\"Constitutionalist Revolution\">Constitutionalist Revolution</a> several weeks later.", "links": [{"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Get%C3%BAlio_Vargas"}, {"title": "Constitutionalist Revolution", "link": "https://wikipedia.org/wiki/Constitutionalist_Revolution"}]}, {"year": "1934", "text": "American bank robbers <PERSON> and <PERSON> are ambushed by police and killed in Bienville Parish, Louisiana.", "html": "1934 - American <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robbers</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are ambushed by police and killed in <a href=\"https://wikipedia.org/wiki/Bienville_Parish,_Louisiana\" title=\"Bienville Parish, Louisiana\">Bienville Parish, Louisiana</a>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robbers</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are ambushed by police and killed in <a href=\"https://wikipedia.org/wiki/Bienville_Parish,_Louisiana\" title=\"Bienville Parish, Louisiana\">Bienville Parish, Louisiana</a>.", "links": [{"title": "Bank robbery", "link": "https://wikipedia.org/wiki/Bank_robbery"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}, {"title": "Bienville Parish, Louisiana", "link": "https://wikipedia.org/wiki/Bienville_Parish,_Louisiana"}]}, {"year": "1934", "text": "The Auto-Lite strike culminates in the \"Battle of Toledo\", a five-day melée between 1,300 troops of the Ohio National Guard and 6,000 picketers.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Auto-Lite_strike\" class=\"mw-redirect\" title=\"Auto-Lite strike\">Auto-Lite strike</a> culminates in the \"Battle of Toledo\", a five-day melée between 1,300 troops of the <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> and 6,000 picketers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Auto-Lite_strike\" class=\"mw-redirect\" title=\"Auto-Lite strike\">Auto-Lite strike</a> culminates in the \"Battle of Toledo\", a five-day melée between 1,300 troops of the <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> and 6,000 picketers.", "links": [{"title": "Auto-Lite strike", "link": "https://wikipedia.org/wiki/Auto-Lite_strike"}, {"title": "Ohio National Guard", "link": "https://wikipedia.org/wiki/Ohio_National_Guard"}]}, {"year": "1939", "text": "The U.S. Navy submarine USS Squalus sinks off the coast of New Hampshire during a test dive, causing the death of 24 sailors and two civilian technicians. The remaining 32 sailors and one civilian naval architect are rescued the following day.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">U.S. Navy</a> <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Sailfish_(SS-192)\" title=\"USS Sailfish (SS-192)\">USS <i>Squalus</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> during a test dive, causing the death of 24 sailors and two civilian technicians. The remaining 32 sailors and one civilian naval architect are rescued the following day.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">U.S. Navy</a> <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Sailfish_(SS-192)\" title=\"USS Sailfish (SS-192)\">USS <i>Squalus</i></a> sinks off the coast of <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> during a test dive, causing the death of 24 sailors and two civilian technicians. The remaining 32 sailors and one civilian naval architect are rescued the following day.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "USS Sailfish (SS-192)", "link": "https://wikipedia.org/wiki/USS_Sailfish_(SS-192)"}, {"title": "New Hampshire", "link": "https://wikipedia.org/wiki/New_Hampshire"}]}, {"year": "1945", "text": "World War II: <PERSON>, head of the Schutzstaffel, commits suicide while in Allied custody.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/<PERSON>hutzstaffel\" title=\"Schutzstaffel\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, commits suicide while in <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> custody.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/<PERSON>hutzstaffel\" title=\"<PERSON>hutzstaffel\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, commits suicide while in <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> custody.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1945", "text": "World War II: Germany's Flensburg Government under <PERSON> is dissolved when its members are arrested by British forces.", "html": "1945 - World War II: Germany's <a href=\"https://wikipedia.org/wiki/Flensburg_Government\" title=\"Flensburg Government\">Flensburg Government</a> under <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a> is dissolved when its members are arrested by British forces.", "no_year_html": "World War II: Germany's <a href=\"https://wikipedia.org/wiki/Flensburg_Government\" title=\"Flensburg Government\">Flensburg Government</a> under <a href=\"https://wikipedia.org/wiki/Karl_D%C3%B6nitz\" title=\"<PERSON>\"><PERSON></a> is dissolved when its members are arrested by British forces.", "links": [{"title": "Flensburg Government", "link": "https://wikipedia.org/wiki/Flensburg_Government"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_D%C3%B6nitz"}]}, {"year": "1946", "text": "The start of a two-day tornado outbreak across the Central United States that spawned at least 15 significant tornadoes.", "html": "1946 - The start of a <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_May_23%E2%80%9324,_1946\" title=\"Tornado outbreak of May 23-24, 1946\">two-day tornado outbreak</a> across the Central United States that spawned at least 15 significant tornadoes.", "no_year_html": "The start of a <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_May_23%E2%80%9324,_1946\" title=\"Tornado outbreak of May 23-24, 1946\">two-day tornado outbreak</a> across the Central United States that spawned at least 15 significant tornadoes.", "links": [{"title": "Tornado outbreak of May 23-24, 1946", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_May_23%E2%80%9324,_1946"}]}, {"year": "1948", "text": "<PERSON>, the US Consul-General, is assassinated in Jerusalem, Israel.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the US Consul-General, is assassinated in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the US Consul-General, is assassinated in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1949", "text": "Cold War: The Western occupying powers approve the Basic Law and establish a new German state, the Federal Republic of Germany.", "html": "1949 - Cold War: The Western <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">occupying</a> powers approve the <a href=\"https://wikipedia.org/wiki/Basic_Law_for_the_Federal_Republic_of_Germany\" title=\"Basic Law for the Federal Republic of Germany\">Basic Law</a> and establish a new German state, the <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">Federal Republic of Germany</a>.", "no_year_html": "Cold War: The Western <a href=\"https://wikipedia.org/wiki/Allied-occupied_Germany\" title=\"Allied-occupied Germany\">occupying</a> powers approve the <a href=\"https://wikipedia.org/wiki/Basic_Law_for_the_Federal_Republic_of_Germany\" title=\"Basic Law for the Federal Republic of Germany\">Basic Law</a> and establish a new German state, the <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">Federal Republic of Germany</a>.", "links": [{"title": "Allied-occupied Germany", "link": "https://wikipedia.org/wiki/Allied-occupied_Germany"}, {"title": "Basic Law for the Federal Republic of Germany", "link": "https://wikipedia.org/wiki/Basic_Law_for_the_Federal_Republic_of_Germany"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}]}, {"year": "1951", "text": "Tibetans sign the Seventeen Point Agreement with China.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibetans</a> sign the <a href=\"https://wikipedia.org/wiki/Seventeen_Point_Agreement\" title=\"Seventeen Point Agreement\">Seventeen Point Agreement</a> with China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibetans</a> sign the <a href=\"https://wikipedia.org/wiki/Seventeen_Point_Agreement\" title=\"Seventeen Point Agreement\">Seventeen Point Agreement</a> with China.", "links": [{"title": "Tibet", "link": "https://wikipedia.org/wiki/Tibet"}, {"title": "Seventeen Point Agreement", "link": "https://wikipedia.org/wiki/Seventeen_Point_Agreement"}]}, {"year": "1960", "text": "A tsunami caused by an earthquake in Chile the previous day kills 61 people in Hilo, Hawaii.", "html": "1960 - A <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> caused by <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">an earthquake in Chile</a> the previous day kills 61 people in <a href=\"https://wikipedia.org/wiki/Hilo,_Hawaii\" title=\"Hilo, Hawaii\">Hilo, Hawaii</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> caused by <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">an earthquake in Chile</a> the previous day kills 61 people in <a href=\"https://wikipedia.org/wiki/Hilo,_Hawaii\" title=\"Hilo, Hawaii\">Hilo, Hawaii</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "1960 Valdivia earthquake", "link": "https://wikipedia.org/wiki/1960_Valdivia_earthquake"}, {"title": "Hilo, Hawaii", "link": "https://wikipedia.org/wiki/Hilo,_Hawaii"}]}, {"year": "1971", "text": "Seventy-eight people are killed when Aviogenex Flight 130 crashes on approach to Rijeka Airport in present-day Rijeka, Croatia (then the Socialist Federal Republic of Yugoslavia).", "html": "1971 - Seventy-eight people are killed when <a href=\"https://wikipedia.org/wiki/Aviogenex_Flight_130\" title=\"Aviogenex Flight 130\">Aviogenex Flight 130</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Rijeka_Airport\" title=\"Rijeka Airport\">Rijeka Airport</a> in present-day <a href=\"https://wikipedia.org/wiki/Rijeka\" title=\"Rijeka\">Rijeka</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> (then the <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>).", "no_year_html": "Seventy-eight people are killed when <a href=\"https://wikipedia.org/wiki/Aviogenex_Flight_130\" title=\"Aviogenex Flight 130\">Aviogenex Flight 130</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Rijeka_Airport\" title=\"Rijeka Airport\">Rijeka Airport</a> in present-day <a href=\"https://wikipedia.org/wiki/Rijeka\" title=\"Rijeka\">Rijeka</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> (then the <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>).", "links": [{"title": "Aviogenex Flight 130", "link": "https://wikipedia.org/wiki/Aviogenex_Flight_130"}, {"title": "Rijeka Airport", "link": "https://wikipedia.org/wiki/Rijeka_Airport"}, {"title": "Rijeka", "link": "https://wikipedia.org/wiki/Rijeka"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1971", "text": "The Intercontinental Hotel in Bucharest opens, becoming the second-tallest building in the city.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Bucharest\" title=\"Grand Hotel Bucharest\">Intercontinental Hotel</a> in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> opens, becoming the second-tallest building in the city.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Hotel_Bucharest\" title=\"Grand Hotel Bucharest\">Intercontinental Hotel</a> in <a href=\"https://wikipedia.org/wiki/Bucharest\" title=\"Bucharest\">Bucharest</a> opens, becoming the second-tallest building in the city.", "links": [{"title": "Grand Hotel Bucharest", "link": "https://wikipedia.org/wiki/Grand_Hotel_Bucharest"}, {"title": "Bucharest", "link": "https://wikipedia.org/wiki/Bucharest"}]}, {"year": "1978", "text": "A Tupolev Tu-144 crashes near the Russian town of Yegoryevsk, killing two.", "html": "1978 - A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> <a href=\"https://wikipedia.org/wiki/1978_Yegoryevsk_Tu-144_crash\" title=\"1978 Yegoryevsk Tu-144 crash\">crashes</a> near the Russian town of <a href=\"https://wikipedia.org/wiki/Yegoryevsk\" title=\"Yegoryevsk\">Yegoryevsk</a>, killing two.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> <a href=\"https://wikipedia.org/wiki/1978_Yegoryevsk_Tu-144_crash\" title=\"1978 Yegoryevsk Tu-144 crash\">crashes</a> near the Russian town of <a href=\"https://wikipedia.org/wiki/Yegoryevsk\" title=\"Yegoryevsk\">Yegoryevsk</a>, killing two.", "links": [{"title": "Tupolev Tu-144", "link": "https://wikipedia.org/wiki/Tupolev_Tu-144"}, {"title": "1978 Yegoryevsk Tu-144 crash", "link": "https://wikipedia.org/wiki/1978_Yegoryevsk_Tu-144_crash"}, {"title": "Yegoryevsk", "link": "https://wikipedia.org/wiki/Yegoryevsk"}]}, {"year": "1991", "text": "Aeroflot Flight 8556 crashes at Pulkovo Airport, killing 13.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_8556\" title=\"Aeroflot Flight 8556\">Aeroflot Flight 8556</a> crashes at <a href=\"https://wikipedia.org/wiki/Pulkovo_Airport\" title=\"Pulkovo Airport\">Pulkovo Airport</a>, killing 13.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_8556\" title=\"Aeroflot Flight 8556\">Aeroflot Flight 8556</a> crashes at <a href=\"https://wikipedia.org/wiki/Pulkovo_Airport\" title=\"Pulkovo Airport\">Pulkovo Airport</a>, killing 13.", "links": [{"title": "Aeroflot Flight 8556", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_8556"}, {"title": "Pulkovo Airport", "link": "https://wikipedia.org/wiki/Pulkovo_Airport"}]}, {"year": "1992", "text": "Italy's most prominent anti-mafia judge <PERSON>, his wife and three body guards are killed by the Corle<PERSON>i clan with a half-ton bomb near Capaci, Sicily. His friend and colleague <PERSON> will be assassinated less than two months later, making 1992 a turning point in the history of Italian Mafia prosecutions.", "html": "1992 - Italy's most prominent anti-mafia judge <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, his wife and three body guards are killed by the <a href=\"https://wikipedia.org/wiki/Corleonesi_Mafia_clan\" title=\"Corleonesi Mafia clan\">Corleonesi clan</a> with a half-ton bomb near <a href=\"https://wikipedia.org/wiki/Capaci\" title=\"Capaci\"><PERSON>aci</a>, <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>. His friend and colleague <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> will be assassinated less than two months later, making 1992 a turning point in the history of <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Italian Mafia</a> prosecutions.", "no_year_html": "Italy's most prominent anti-mafia judge <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, his wife and three body guards are killed by the <a href=\"https://wikipedia.org/wiki/Corleonesi_Mafia_clan\" title=\"Corleonesi Mafia clan\">Corleonesi clan</a> with a half-ton bomb near <a href=\"https://wikipedia.org/wiki/Capaci\" title=\"Capaci\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>. His friend and colleague <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> will be assassinated less than two months later, making 1992 a turning point in the history of <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Italian Mafia</a> prosecutions.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>"}, {"title": "Corleonesi Mafia clan", "link": "https://wikipedia.org/wiki/Corleonesi_Mafia_clan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Capaci"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sicilian Mafia", "link": "https://wikipedia.org/wiki/Sicilian_Mafia"}]}, {"year": "1995", "text": "The first version of the Java programming language is released.", "html": "1995 - The first version of the <a href=\"https://wikipedia.org/wiki/Java_(programming_language)\" title=\"Java (programming language)\">Java programming language</a> is released.", "no_year_html": "The first version of the <a href=\"https://wikipedia.org/wiki/Java_(programming_language)\" title=\"Java (programming language)\">Java programming language</a> is released.", "links": [{"title": "Java (programming language)", "link": "https://wikipedia.org/wiki/Java_(programming_language)"}]}, {"year": "1998", "text": "The Good Friday Agreement is accepted in a referendum in Northern Ireland with roughly 75% voting yes.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Good_Friday_Agreement\" title=\"Good Friday Agreement\">Good Friday Agreement</a> is accepted in a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> with roughly 75% voting yes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Good_Friday_Agreement\" title=\"Good Friday Agreement\">Good Friday Agreement</a> is accepted in a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> with roughly 75% voting yes.", "links": [{"title": "Good Friday Agreement", "link": "https://wikipedia.org/wiki/Good_Friday_Agreement"}, {"title": "Referendum", "link": "https://wikipedia.org/wiki/Referendum"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "2002", "text": "The \"55 parties\" clause of the Kyoto Protocol is reached after its ratification by Iceland.", "html": "2002 - The \"55 parties\" clause of the <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> is reached after its ratification by <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>.", "no_year_html": "The \"55 parties\" clause of the <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> is reached after its ratification by <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>.", "links": [{"title": "Kyoto Protocol", "link": "https://wikipedia.org/wiki/Kyoto_Protocol"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}]}, {"year": "2006", "text": "Alaskan stratovolcano Mount Cleveland erupts.", "html": "2006 - Alaskan <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Mount_Cleveland_(Alaska)\" title=\"Mount Cleveland (Alaska)\">Mount Cleveland</a> erupts.", "no_year_html": "Alaskan <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Mount_Cleveland_(Alaska)\" title=\"Mount Cleveland (Alaska)\">Mount Cleveland</a> erupts.", "links": [{"title": "Stratovolcano", "link": "https://wikipedia.org/wiki/Stratovolcano"}, {"title": "Mount Cleveland (Alaska)", "link": "https://wikipedia.org/wiki/Mount_Cleveland_(Alaska)"}]}, {"year": "2008", "text": "The International Court of Justice (ICJ) awards Middle Rocks to Malaysia and Pedra Branca (Pulau Batu Puteh) to Singapore, ending a 29-year territorial dispute between the two countries.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> (ICJ) awards <a href=\"https://wikipedia.org/wiki/Middle_Rocks\" title=\"Middle Rocks\">Middle Rocks</a> to <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> and <a href=\"https://wikipedia.org/wiki/Pedra_Branca,_Singapore\" title=\"Pedra Branca, Singapore\">Pedra Branca</a> (Pulau Batu Puteh) to <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, ending a 29-year <a href=\"https://wikipedia.org/wiki/Pedra_Branca_dispute\" title=\"Pedra Branca dispute\">territorial dispute</a> between the two countries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> (ICJ) awards <a href=\"https://wikipedia.org/wiki/Middle_Rocks\" title=\"Middle Rocks\">Middle Rocks</a> to <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> and <a href=\"https://wikipedia.org/wiki/Pedra_Branca,_Singapore\" title=\"Pedra Branca, Singapore\">Pedra Branca</a> (Pulau Batu Puteh) to <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, ending a 29-year <a href=\"https://wikipedia.org/wiki/Pedra_Branca_dispute\" title=\"Pedra Branca dispute\">territorial dispute</a> between the two countries.", "links": [{"title": "International Court of Justice", "link": "https://wikipedia.org/wiki/International_Court_of_Justice"}, {"title": "Middle Rocks", "link": "https://wikipedia.org/wiki/Middle_Rocks"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}, {"title": "Pedra Branca, Singapore", "link": "https://wikipedia.org/wiki/Pedra_Branca,_Singapore"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "Pedra Branca dispute", "link": "https://wikipedia.org/wiki/Pedra_Branca_dispute"}]}, {"year": "2013", "text": "A freeway bridge carrying Interstate 5 over the Skagit River collapses in Mount Vernon, Washington.", "html": "2013 - A freeway bridge carrying <a href=\"https://wikipedia.org/wiki/Interstate_5_in_Washington\" title=\"Interstate 5 in Washington\">Interstate 5</a> over the <a href=\"https://wikipedia.org/wiki/Skagit_River\" title=\"Skagit River\">Skagit River</a> <a href=\"https://wikipedia.org/wiki/I-5_Skagit_River_Bridge_collapse\" class=\"mw-redirect\" title=\"I-5 Skagit River Bridge collapse\">collapses</a> in <a href=\"https://wikipedia.org/wiki/Mount_Vernon,_Washington\" title=\"Mount Vernon, Washington\">Mount Vernon, Washington</a>.", "no_year_html": "A freeway bridge carrying <a href=\"https://wikipedia.org/wiki/Interstate_5_in_Washington\" title=\"Interstate 5 in Washington\">Interstate 5</a> over the <a href=\"https://wikipedia.org/wiki/Skagit_River\" title=\"Skagit River\">Skagit River</a> <a href=\"https://wikipedia.org/wiki/I-5_Skagit_River_Bridge_collapse\" class=\"mw-redirect\" title=\"I-5 Skagit River Bridge collapse\">collapses</a> in <a href=\"https://wikipedia.org/wiki/Mount_Vernon,_Washington\" title=\"Mount Vernon, Washington\">Mount Vernon, Washington</a>.", "links": [{"title": "Interstate 5 in Washington", "link": "https://wikipedia.org/wiki/Interstate_5_in_Washington"}, {"title": "Skagit River", "link": "https://wikipedia.org/wiki/Skagit_River"}, {"title": "I-5 Skagit River Bridge collapse", "link": "https://wikipedia.org/wiki/I-5_Skagit_River_Bridge_collapse"}, {"title": "Mount Vernon, Washington", "link": "https://wikipedia.org/wiki/Mount_Vernon,_Washington"}]}, {"year": "2014", "text": "Seven people, including the perpetrator, are killed and another 14 injured in a killing spree near the campus of University of California, Santa Barbara.", "html": "2014 - Seven people, including the perpetrator, are killed and another 14 injured in <a href=\"https://wikipedia.org/wiki/2014_Isla_Vista_killings\" title=\"2014 Isla Vista killings\">a killing spree</a> near the campus of <a href=\"https://wikipedia.org/wiki/University_of_California,_Santa_Barbara\" title=\"University of California, Santa Barbara\">University of California, Santa Barbara</a>.", "no_year_html": "Seven people, including the perpetrator, are killed and another 14 injured in <a href=\"https://wikipedia.org/wiki/2014_Isla_Vista_killings\" title=\"2014 Isla Vista killings\">a killing spree</a> near the campus of <a href=\"https://wikipedia.org/wiki/University_of_California,_Santa_Barbara\" title=\"University of California, Santa Barbara\">University of California, Santa Barbara</a>.", "links": [{"title": "2014 Isla Vista killings", "link": "https://wikipedia.org/wiki/2014_Isla_Vista_killings"}, {"title": "University of California, Santa Barbara", "link": "https://wikipedia.org/wiki/University_of_California,_Santa_Barbara"}]}, {"year": "2015", "text": "At least 30 people are killed as a result of floods and tornadoes in Texas, Oklahoma, and northern Mexico.", "html": "2015 - At least 30 people are killed as a result of <a href=\"https://wikipedia.org/wiki/2015_Texas%E2%80%93Oklahoma_flood_and_tornado_outbreak\" title=\"2015 Texas-Oklahoma flood and tornado outbreak\">floods and tornadoes</a> in Texas, Oklahoma, and northern Mexico.", "no_year_html": "At least 30 people are killed as a result of <a href=\"https://wikipedia.org/wiki/2015_Texas%E2%80%93Oklahoma_flood_and_tornado_outbreak\" title=\"2015 Texas-Oklahoma flood and tornado outbreak\">floods and tornadoes</a> in Texas, Oklahoma, and northern Mexico.", "links": [{"title": "2015 Texas-Oklahoma flood and tornado outbreak", "link": "https://wikipedia.org/wiki/2015_Texas%E2%80%93Oklahoma_flood_and_tornado_outbreak"}]}, {"year": "2016", "text": "Two suicide bombings, conducted by the Islamic State of Iraq and Syria, kill at least 45 potential army recruits in Aden, Yemen.", "html": "2016 - Two suicide bombings, conducted by the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria\" class=\"mw-redirect\" title=\"Islamic State of Iraq and Syria\">Islamic State of Iraq and Syria</a>, kill at least 45 potential army recruits in <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>.", "no_year_html": "Two suicide bombings, conducted by the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria\" class=\"mw-redirect\" title=\"Islamic State of Iraq and Syria\">Islamic State of Iraq and Syria</a>, kill at least 45 potential army recruits in <a href=\"https://wikipedia.org/wiki/Aden\" title=\"Aden\">Aden</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>.", "links": [{"title": "Islamic State of Iraq and Syria", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria"}, {"title": "Aden", "link": "https://wikipedia.org/wiki/Aden"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}]}, {"year": "2016", "text": "Eight bombings are carried out by the Islamic State of Iraq and Syria in Jableh and Tartus, coastline cities in Syria. One hundred eighty-four people are killed and at least 200 people injured.", "html": "2016 - Eight bombings are carried out by the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria\" class=\"mw-redirect\" title=\"Islamic State of Iraq and Syria\">Islamic State of Iraq and Syria</a> in <a href=\"https://wikipedia.org/wiki/Jableh\" title=\"Jable<PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tartus\" title=\"Tartus\">Tartus</a>, coastline cities in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>. One hundred eighty-four people are killed and at least 200 people injured.", "no_year_html": "Eight bombings are carried out by the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria\" class=\"mw-redirect\" title=\"Islamic State of Iraq and Syria\">Islamic State of Iraq and Syria</a> in <a href=\"https://wikipedia.org/wiki/Jableh\" title=\"Jable<PERSON>\">J<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Tartus\" title=\"Tartus\">Tartus</a>, coastline cities in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>. One hundred eighty-four people are killed and at least 200 people injured.", "links": [{"title": "Islamic State of Iraq and Syria", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_Syria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>h"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tartus"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2017", "text": "Philippine President <PERSON> declares martial law in Mindanao, following the <PERSON>ute's attack in Marawi.", "html": "2017 - Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, following the <a href=\"https://wikipedia.org/wiki/Maute_group\" title=\"Maute group\"><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Marawi\" class=\"mw-redirect\" title=\"Battle of Marawi\">attack in Marawi</a>.", "no_year_html": "Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>, following the <a href=\"https://wikipedia.org/wiki/Maute_group\" title=\"Maute group\"><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Marawi\" class=\"mw-redirect\" title=\"Battle of Marawi\">attack in Marawi</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}, {"title": "Mindanao", "link": "https://wikipedia.org/wiki/Mindanao"}, {"title": "Maute group", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_group"}, {"title": "Battle of Marawi", "link": "https://wikipedia.org/wiki/Battle_of_Marawi"}]}, {"year": "2021", "text": "A cable car falls from a mountain near Lake Maggiore in northern Italy, killing 14 people.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/Stresa%E2%80%93Mottarone_cable_car_crash\" title=\"Stresa-Mottarone cable car crash\">cable car falls</a> from a mountain near <a href=\"https://wikipedia.org/wiki/Lake_Maggiore\" title=\"Lake Maggiore\">Lake Maggiore</a> in northern Italy, killing 14 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Stresa%E2%80%93Mottarone_cable_car_crash\" title=\"Stresa-Mottarone cable car crash\">cable car falls</a> from a mountain near <a href=\"https://wikipedia.org/wiki/Lake_Maggiore\" title=\"Lake Maggiore\">Lake Maggiore</a> in northern Italy, killing 14 people.", "links": [{"title": "Stresa<PERSON><PERSON><PERSON><PERSON> cable car crash", "link": "https://wikipedia.org/wiki/Stresa%E2%80%93Mottarone_cable_car_crash"}, {"title": "Lake Maggiore", "link": "https://wikipedia.org/wiki/Lake_Maggiore"}]}, {"year": "2021", "text": "Ryanair Flight 4978 is forced to land by Belarusian authorities to detain dissident journalist <PERSON>.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Ryanair_Flight_4978\" title=\"Ryanair Flight 4978\">Ryanair Flight 4978</a> is forced to land by <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarusian</a> authorities to detain dissident journalist <a href=\"https://wikipedia.org/wiki/Roman_Protasevich\" title=\"<PERSON>tasevich\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ryanair_Flight_4978\" title=\"Ryanair Flight 4978\">Ryanair Flight 4978</a> is forced to land by <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarusian</a> authorities to detain dissident journalist <a href=\"https://wikipedia.org/wiki/Roman_Protasevich\" title=\"<PERSON>vich\"><PERSON></a>.", "links": [{"title": "Ryanair Flight 4978", "link": "https://wikipedia.org/wiki/Ryanair_Flight_4978"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2022", "text": "<PERSON> of the Australian Labor Party is sworn in as the 31st Prime Minister of Australia after winning the 2022 Australian federal election, ending 9 years of conservative rule.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Australian_Labor_Party\" title=\"Australian Labor Party\">Australian Labor Party</a> is sworn in as the 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> after winning the <a href=\"https://wikipedia.org/wiki/2022_Australian_federal_election\" title=\"2022 Australian federal election\">2022 Australian federal election</a>, ending 9 years of <a href=\"https://wikipedia.org/wiki/Coalition_(Australia)\" title=\"Coalition (Australia)\">conservative rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Australian_Labor_Party\" title=\"Australian Labor Party\">Australian Labor Party</a> is sworn in as the 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> after winning the <a href=\"https://wikipedia.org/wiki/2022_Australian_federal_election\" title=\"2022 Australian federal election\">2022 Australian federal election</a>, ending 9 years of <a href=\"https://wikipedia.org/wiki/Coalition_(Australia)\" title=\"Coalition (Australia)\">conservative rule</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australian Labor Party", "link": "https://wikipedia.org/wiki/Australian_Labor_Party"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "2022 Australian federal election", "link": "https://wikipedia.org/wiki/2022_Australian_federal_election"}, {"title": "Coalition (Australia)", "link": "https://wikipedia.org/wiki/Coalition_(Australia)"}]}], "Births": [{"year": "635", "text": "<PERSON><PERSON><PERSON><PERSON>, Mayan king (d. 702)", "html": "635 - <a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Kan_Bahlam_II\" title=\"Kʼinich Kan Bahlam II\">Kʼinich Kan Bahlam II</a>, Mayan king (d. 702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Kan_Bahlam_II\" title=\"Kʼinich Kan Bahlam II\">Kʼ<PERSON>ch Kan Bahlam II</a>, Mayan king (d. 702)", "links": [{"title": "Kʼinich Kan Bahlam II", "link": "https://wikipedia.org/wiki/K%CA%<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_II"}]}, {"year": "675", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, King of <PERSON><PERSON><PERSON><PERSON> dynasty, Tamil Nadu, India", "html": "675 - <a href=\"https://wikipedia.org/wiki/Perumbidugu_Mutharaiyar_II\" title=\"Perumbidugu Mutharaiyar II\">Perumbidugu <PERSON> II</a>, King of <a href=\"https://wikipedia.org/wiki/Mu<PERSON>raiyar_dynasty\" title=\"Mutharaiyar dynasty\">Mu<PERSON>raiyar dynasty</a>, Tamil Nadu, India", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Perumbidugu_Mutharaiyar_II\" title=\"Perumbidugu Mutharaiyar II\">Perumbidugu <PERSON> II</a>, King of <a href=\"https://wikipedia.org/wiki/Mu<PERSON>raiyar_dynasty\" title=\"Mutharaiyar dynasty\">Mu<PERSON><PERSON><PERSON> dynasty</a>, Tamil Nadu, India", "links": [{"title": "Perumbidugu <PERSON> II", "link": "https://wikipedia.org/wiki/Perumbidugu_Mutharaiyar_II"}, {"title": "Mutharaiyar dynasty", "link": "https://wikipedia.org/wiki/Mu<PERSON><PERSON>yar_dynasty"}]}, {"year": "1052", "text": "<PERSON> of France (d. 1108)", "html": "1052 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1108)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1108)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}]}, {"year": "1100", "text": "Emperor <PERSON><PERSON> of Song (d. 1161)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1161)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1161)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1127", "text": "<PERSON><PERSON><PERSON> of Goryeo, Korean monarch of the Goryeo dynasty (d. 1173)", "html": "1127 - <a href=\"https://wikipedia.org/wiki/Ui<PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean monarch of the Goryeo dynasty (d. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ui<PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean monarch of the Goryeo dynasty (d. 1173)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Uijong_of_Goryeo"}]}, {"year": "1330", "text": "<PERSON><PERSON> of Goryeo, Korean ruler (d. 1374)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON> of Goryeo\"><PERSON><PERSON> of Goryeo</a>, Korean ruler (d. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON> of Goryeo\"><PERSON><PERSON> of Goryeo</a>, Korean ruler (d. 1374)", "links": [{"title": "<PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Gongmin_of_Goryeo"}]}, {"year": "1586", "text": "<PERSON>, German composer and organist (d. 1666)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and organist (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and organist (d. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, Spanish mathematician and philosopher (d. 1682)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mathematician and philosopher (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mathematician and philosopher (d. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1614", "text": "<PERSON><PERSON><PERSON>, Flemish Baroque painter (d. 1675)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Flemalle\" title=\"<PERSON><PERSON><PERSON> Flemalle\"><PERSON><PERSON><PERSON></a>, Flemish Baroque painter (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Flemalle\" title=\"<PERSON><PERSON><PERSON> Flemalle\"><PERSON><PERSON><PERSON></a>, Flemish Baroque painter (d. 1675)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>t_Flemalle"}]}, {"year": "1617", "text": "<PERSON>, English astrologer and politician (d. 1692)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer and politician (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer and politician (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON>, Landgrave of Hesse-Kassel, noble of Hesse-Kassel (d. 1663)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a>, noble of Hesse-Kassel (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Kassel\" title=\"<PERSON>, Landgrave of Hesse-Kassel\"><PERSON>, Landgrave of Hesse-Kassel</a>, noble of Hesse-Kassel (d. 1663)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Kassel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Kassel"}]}, {"year": "1707", "text": "<PERSON>, Swedish botanist, physician, and zoologist (d. 1778)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Linnaeus\"><PERSON></a>, Swedish botanist, physician, and zoologist (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Linnaeus\" title=\"<PERSON> Linnaeus\"><PERSON></a>, Swedish botanist, physician, and zoologist (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, Scottish-English anatomist and physician (d. 1783)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anatomist)\" title=\"<PERSON> (anatomist)\"><PERSON></a>, Scottish-English anatomist and physician (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anatomist)\" title=\"<PERSON> (anatomist)\"><PERSON></a>, Scottish-English anatomist and physician (d. 1783)", "links": [{"title": "<PERSON> (anatomist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anatomist)"}]}, {"year": "1729", "text": "<PERSON>, Italian poet and educator (d. 1799)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and educator (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and educator (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "Prince <PERSON> of Prussia, Prussian prince and general (d. 1813)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a>, Prussian prince and general (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Prussia\" title=\"Prince <PERSON> of Prussia\">Prince <PERSON> of Prussia</a>, Prussian prince and general (d. 1813)", "links": [{"title": "Prince <PERSON> of Prussia", "link": "https://wikipedia.org/wiki/Prince_Augustus_<PERSON>_of_Prussia"}]}, {"year": "1734", "text": "<PERSON>, German physician and astrologer (d. 1815)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astrologer (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astrologer (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, Italian organist and composer (d. 1801)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (d. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, Austrian earl and general (d. 1862)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian earl and general (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian earl and general (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, French admiral and explorer (d. 1842)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Urville\" title=\"<PERSON>Urville\"><PERSON>rville</a>, French admiral and explorer (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Urville\" title=\"<PERSON>Urville\"><PERSON>rville</a>, French admiral and explorer (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Urville"}]}, {"year": "1790", "text": "<PERSON>, French neoclassical sculptor (d. 1852)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neoclassical sculptor (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neoclassical sculptor (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, Czech pianist and composer (d. 1870)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, English architect, designed the Upper Brook Street Chapel and Halifax Town Hall (d. 1860)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester\" title=\"Upper Brook Street Chapel, Manchester\">Upper Brook Street Chapel</a> and <a href=\"https://wikipedia.org/wiki/Halifax_Town_Hall\" title=\"Halifax Town Hall\">Halifax Town Hall</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester\" title=\"Upper Brook Street Chapel, Manchester\">Upper Brook Street Chapel</a> and <a href=\"https://wikipedia.org/wiki/Halifax_Town_Hall\" title=\"Halifax Town Hall\">Halifax Town Hall</a> (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Upper Brook Street Chapel, Manchester", "link": "https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester"}, {"title": "Halifax Town Hall", "link": "https://wikipedia.org/wiki/Halifax_Town_Hall"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican general and president (1855) (d. 1877)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/R%C3%B3mulo_D%C3%ADaz_de_la_Vega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general and president (1855) (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3mulo_D%C3%ADaz_de_la_Vega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general and president (1855) (d. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3mulo_D%C3%ADaz_de_la_Vega"}]}, {"year": "1810", "text": "<PERSON>, American journalist and critic (d. 1850)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Unconstitutional Mexican interim president (d. 1862)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pezuela\" title=\"<PERSON> Pezuela\"><PERSON></a>, Unconstitutional Mexican interim president (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ezuela\" title=\"<PERSON> Pezuela\"><PERSON></a>, Unconstitutional Mexican interim president (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ezuel<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American engineer, designed the Eads Bridge (d. 1887)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/E<PERSON>_Bridge\" title=\"Eads Bridge\">Eads Bridge</a> (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> E<PERSON>\"><PERSON></a>, American engineer, designed the <a href=\"https://wikipedia.org/wiki/E<PERSON>_Bridge\" title=\"Eads Bridge\">Eads Bridge</a> (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eads Bridge", "link": "https://wikipedia.org/wiki/Eads_Bridge"}]}, {"year": "1820", "text": "<PERSON>, American lawyer and judge (d. 1891)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, American general and politician, 30th Governor of Rhode Island (d. 1881)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON>, Latvian architect (d. 1891)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/J%C4%81nis_Fr%C4%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian architect (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%81nis_Fr%C4%AB<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian architect (d. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%81nis_Fr%C4%ABdrihs_Baumanis"}]}, {"year": "1834", "text": "<PERSON>, Danish painter and academic (d. 1890)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and academic (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and academic (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON>, Swiss mechanical engineer and inventor (d. 1919)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mallet\" title=\"<PERSON><PERSON><PERSON> Mallet\"><PERSON><PERSON><PERSON></a>, Swiss mechanical engineer and inventor (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mallet\" title=\"Ana<PERSON><PERSON> Mallet\"><PERSON><PERSON><PERSON></a>, Swiss mechanical engineer and inventor (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mallet"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON>, Polish pianist and composer (d. 1912)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist and composer (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, Norwegian painter (d. 1932)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian painter (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian painter (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Irish-Australian politician, 2nd Premier of Western Australia (d. 1910)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Iranian religious leader (d. 1921)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/%CA%BBAbdu%27l-Bah%C3%A1\" title=\"ʻ<PERSON><PERSON><PERSON><PERSON>l-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian religious leader (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%CA%BBAbdu%27l-Bah%C3%A1\" title=\"ʻ<PERSON><PERSON><PERSON><PERSON>l-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Iranian religious leader (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%CA%BBAbdu%27l-Bah%C3%A1"}]}, {"year": "1848", "text": "<PERSON>, German pilot and engineer (d. 1896)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English author and activist (d. 1924)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Isabella Ford\"><PERSON></a>, English author and activist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Isabella Ford\"><PERSON></a>, English author and activist (d. 1924)", "links": [{"title": "Isabella Ford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian painter (d. 1927)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>pl-R%C3%B3nai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_Rippl-R%C3%B3nai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian painter (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3z<PERSON>_Rippl-R%C3%B3nai"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish architect (d. 1930)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Horodecki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish architect (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Ho<PERSON>ecki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish architect (d. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Horod<PERSON>i"}]}, {"year": "1864", "text": "<PERSON>, American fencer (d. 1939)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (d. 1939)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(fencer)"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian jurist and politician, 11th President of Brazil (d. 1942)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Epit%C3%A1cio_Pessoa\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian jurist and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Epit%C3%<PERSON>cio_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian jurist and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Epit%C3%A1cio_Pessoa"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1875", "text": "<PERSON>, American businessman and philanthropist (d. 1966)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Canadian pole vaulter (d. 1960)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pole vaulter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pole vaulter (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1939)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Douglas_Fairbanks\" title=\"Douglas Fairbanks\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Douglas_Fairbanks\" title=\"Douglas Fairbanks\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Italian sociologist and demographer (d. 1965)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sociologist and demographer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sociologist and demographer (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Norwegian mathematician and theorist (d. 1963)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Thor<PERSON>f_Skolem\" title=\"Thor<PERSON>f Skolem\"><PERSON><PERSON><PERSON></a>, Norwegian mathematician and theorist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thor<PERSON><PERSON>_S<PERSON>lem\" title=\"Thor<PERSON>f Skolem\"><PERSON><PERSON><PERSON></a>, Norwegian mathematician and theorist (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thoralf_Skolem"}]}, {"year": "1887", "text": "<PERSON>, Estonian-Russian sailor and captain (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1in\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian sailor and captain (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1in\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian sailor and captain (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikolai_Vek%C5%A1in"}]}, {"year": "1887", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English historian (d. 1941)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/C._R._M._F._<PERSON>well\" title=\"C. R. M. F. <PERSON>well\"><PERSON>. <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._R._M._F._<PERSON>well\" title=\"C. R. M. F. Cru<PERSON>well\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English historian (d. 1941)", "links": [{"title": "C. R. M. F. <PERSON>", "link": "https://wikipedia.org/wiki/C._R._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Dutch writer (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch writer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch writer (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American baseball player and police officer (d. 1972)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wheat\"><PERSON></a>, American baseball player and police officer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Zack Wheat\"><PERSON></a>, American baseball player and police officer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>heat"}]}, {"year": "1889", "text": "<PERSON>, German educator and politician (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English-American actor and singer (d. 1966)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Swedish novelist, playwright, and poet, Nobel Prize laureate (d. 1974)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish novelist, playwright, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A4<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1892", "text": "<PERSON>, 7th <PERSON>, British peer (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Earl_<PERSON>\" title=\"<PERSON>, 7th <PERSON>\"><PERSON>, 7th Earl <PERSON></a>, British peer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_<PERSON>_<PERSON>\" title=\"<PERSON>, 7th <PERSON>\"><PERSON>, 7th Earl <PERSON></a>, British peer (d. 1975)", "links": [{"title": "<PERSON>, 7th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Russian-German SS officer (d. 1966)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1897", "text": "<PERSON>, Scottish motorcycle racer (d. 1937)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American soldier, journalist, and author (d. 1989)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dell\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dell\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_O%27Dell"}]}, {"year": "1898", "text": "<PERSON>, German soldier and politician (d. 1945)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American super-centenarian (d. 2015)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American super-centenarian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American super-centenarian (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German lawyer and politician (d. 1946)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German lawyer and theorist (d. 1954)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lawyer and theorist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lawyer and theorist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American physicist and engineer, Nobel Prize laureate (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, French pilot (d. 1934)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON>uche<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pilot (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_Boucher\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pilot (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Boucher"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Japanese supercentenarian (d. 2024)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese supercentenarian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Itooka\"><PERSON><PERSON></a>, Japanese supercentenarian (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ooka"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Swiss author and photographer (d. 1942)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and photographer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and photographer (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American author and educator (d. 1952)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English architect and academic (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, American actor and comedian (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Scatman_Crothers\" title=\"Scatman Crothers\"><PERSON><PERSON><PERSON></a>, American actor and comedian (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scatman_Crothers\" title=\"Scatman Crothers\"><PERSON><PERSON><PERSON></a>, American actor and comedian (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>roth<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American painter and academic  (d. 1962)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American clarinet player, composer, and bandleader (d. 2004)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American clarinet player, composer, and bandleader (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American clarinet player, composer, and bandleader (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Canadian boxer (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian boxer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian boxer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German cardinal (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cardinal (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English tennis player (d. 1983)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French pianist and composer (d. 1997)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7aix"}]}, {"year": "1912", "text": "<PERSON>, American actor (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1989)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1914", "text": "<PERSON>, English visionary landscape artist (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English visionary landscape artist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English visionary landscape artist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, American journalist and author (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sibley\"><PERSON><PERSON><PERSON></a>, American journalist and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sibley\"><PERSON><PERSON><PERSON></a>, American journalist and author (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bley"}]}, {"year": "1914", "text": "<PERSON>, <PERSON> of Lodsworth, English economist, journalist, and prominent Catholic layperson (d. 1981)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth\" title=\"<PERSON>, Baroness <PERSON> of Lodsworth\"><PERSON>, Baroness <PERSON> of Lodsworth</a>, English economist, journalist, and prominent Catholic layperson (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth\" title=\"<PERSON>, Baroness <PERSON> of Lodsworth\"><PERSON>, Baroness <PERSON> of Lodsworth</a>, English economist, journalist, and prominent Catholic layperson (d. 1981)", "links": [{"title": "<PERSON>, <PERSON> of Lodsworth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>_of_Lodsworth"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American physicist and chemist, invented CorningW<PERSON> (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist and chemist, invented <a href=\"https://wikipedia.org/wiki/CorningWare\" title=\"CorningWare\">CorningWare</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American physicist and chemist, invented <a href=\"https://wikipedia.org/wiki/CorningWare\" title=\"CorningWare\">CorningWare</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "CorningWare", "link": "https://wikipedia.org/wiki/CorningWare"}]}, {"year": "1917", "text": "<PERSON>, American mathematician and meteorologist (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and meteorologist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and meteorologist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English cricketer and sportscaster (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American author and playwright (d. 1988)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (d. 1988)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_(comics)"}]}, {"year": "1919", "text": "<PERSON>, Puerto Rican contralto and a member of the Puerto Rican Senate (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican contralto and a member of the <a href=\"https://wikipedia.org/wiki/Senate_of_Puerto_Rico\" title=\"Senate of Puerto Rico\">Puerto Rican Senate</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican contralto and a member of the <a href=\"https://wikipedia.org/wiki/Senate_of_Puerto_Rico\" title=\"Senate of Puerto Rico\">Puerto Rican Senate</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez"}, {"title": "Senate of Puerto Rico", "link": "https://wikipedia.org/wiki/Senate_of_Puerto_Rico"}]}, {"year": "1919", "text": "<PERSON>, American actress, singer, and dancer (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American singer (d. 1993)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Connell\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Helen_O%27Connell"}]}, {"year": "1921", "text": "<PERSON>, British jazz musician and broadcaster (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British jazz musician and broadcaster (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British jazz musician and broadcaster (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Catalan-Spanish pianist (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan-Spanish pianist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Catalan-Spanish pianist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American virologist and microbiologist (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and microbiologist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American virologist and microbiologist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, German author and activist (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and activist (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American biologist and geneticist, Nobel Prize laureate (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1926", "text": "<PERSON>, Indian bishop (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON>Souza\"><PERSON></a>, Indian bishop (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON>Souza\"><PERSON></a>, Indian bishop (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Basil_Salvadore_D%27Souza"}]}, {"year": "1926", "text": "<PERSON>, Lithuanian-South African activist and politician (d. 1995)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-South African activist and politician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-South African activist and politician (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer and actress (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English actor (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Russian runner (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Swedish-Austrian actress (d. 1982)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Austrian actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Austrian actress (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German poet and critic (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Syrian-French journalist and author (d. 1998)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Kevork_A<PERSON>mian\" title=\"Kevork Ajemian\"><PERSON><PERSON><PERSON></a>, Syrian-French journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kevork_A<PERSON>mian\" title=\"Kevork Ajemian\"><PERSON><PERSON><PERSON></a>, Syrian-French journalist and author (d. 1998)", "links": [{"title": "Kevork Ajemian", "link": "https://wikipedia.org/wiki/Kevork_Ajemian"}]}, {"year": "1933", "text": "<PERSON>, English actress", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Swedish motorcycle racer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ove_Fundin\" title=\"Ove Fundin\"><PERSON><PERSON></a>, Swedish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ove_Fundin\" title=\"Ove Fundin\"><PERSON><PERSON></a>, Swedish motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ove_Fundin"}]}, {"year": "1934", "text": "<PERSON>, electronic engineer and inventor of the Moog synthesizer (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, electronic engineer and inventor of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_synthesizer\" title=\"<PERSON><PERSON> synthesizer\">Moog synthesizer</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, electronic engineer and inventor of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_synthesizer\" title=\"<PERSON><PERSON> synthesizer\">Moog synthesizer</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Moog synthesizer", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_synthesizer"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Swedish author (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, German soprano and actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Ingeborg_Hallstein\" title=\"Ingeborg Hallstein\">Ingeborg Hallstein</a>, German soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_Hallstein\" title=\"Ingeborg Hallstein\">Ingeborg Hallstein</a>, German soprano and actress", "links": [{"title": "Ingeborg Hallstein", "link": "https://wikipedia.org/wiki/Ingeborg_Hallstein"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French-American composer and conductor (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American composer and conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, German director and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON> (musician), Norwegian saxophonist  (d. 2002)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON> (musician)</a>, Norwegian saxophonist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON> (musician)</a>, Norwegian saxophonist (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>_(musician)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, French race car driver", "html": "1940 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>e"}]}, {"year": "1940", "text": "<PERSON>, Argentinian mathematician and academic (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian mathematician and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian mathematician and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American director, producer, and screenwriter (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" title=\"Zalman King\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" title=\"Zal<PERSON> King\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American basketball player, coach, and executive", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thorn"}]}, {"year": "1942", "text": "<PERSON>, Romanian philosopher, author, and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian philosopher, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian philosopher, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian director, screenwriter, and choreographer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, screenwriter, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, screenwriter, and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Solomon Islands politician, 1st Prime Minister of the Solomon Islands (d. 2016)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon Islands politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Solomon_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Solomon Islands\">Prime Minister of the Solomon Islands</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon Islands politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Solomon_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Solomon Islands\">Prime Minister of the Solomon Islands</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Solomon Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Solomon_Islands"}]}, {"year": "1944", "text": "<PERSON>, Australian tennis player and sportscaster", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian director, screenwriter, and author (d. 1991)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director, screenwriter, and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director, screenwriter, and author (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian golfer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1947", "text": "<PERSON>, American poet and translator (d. 1995)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and translator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, French actress, director, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American cardinal", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Peruvian lawyer and politician, 61st and 64th President of Peru (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Alan_<PERSON>c%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Peruvian lawyer and politician, 61st and 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Peruvian lawyer and politician, 61st and 64th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_Garc%C3%ADa"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1950", "text": "<PERSON>, Irish republican and Sinn <PERSON> politician, Deputy First Minister of Northern Ireland (d. 2017)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican and Sinn <PERSON> politician, <a href=\"https://wikipedia.org/wiki/Deputy_First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"Deputy First Minister of Northern Ireland\">Deputy First Minister of Northern Ireland</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican and Sinn <PERSON> politician, <a href=\"https://wikipedia.org/wiki/Deputy_First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"Deputy First Minister of Northern Ireland\">Deputy First Minister of Northern Ireland</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy First Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/Deputy_First_Minister_of_Northern_Ireland"}]}, {"year": "1950", "text": "<PERSON>, American serial killer (d. 1980)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a>, American serial killer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Russian chess player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Greek economist and politician, 185th Prime Minister of Greece", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, 185th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek economist and politician, 185th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sam<PERSON>s"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1952", "text": "<PERSON>, English photographer and journalist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Northern Irish international footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Northern Irish international footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1954", "text": "Marvelous <PERSON>, American boxer and actor (d. 2021)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ous <PERSON>\"><PERSON><PERSON> <PERSON></a>, American boxer and actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ous <PERSON>\"><PERSON><PERSON> <PERSON></a>, American boxer and actor (d. 2021)", "links": [{"title": "Marvelous <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Irish singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bloom"}]}, {"year": "1956", "text": "<PERSON>, Italian illustrator and painter (d. 1988)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian illustrator and painter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian illustrator and painter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Austrian politician and diplomat, Foreign Minister of Austria", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Austria\" class=\"mw-redirect\" title=\"Foreign Minister of Austria\">Foreign Minister of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foreign Minister of Austria", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_Austria"}]}, {"year": "1956", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Show<PERSON>ter\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Buck Show<PERSON>ter\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist, author, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, game show host, and entrepreneur", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, game show host, and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, game show host, and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_De<PERSON>\" title=\"<PERSON> DeLaria\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> DeL<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "Lea DeLaria", "link": "https://wikipedia.org/wiki/Lea_DeLaria"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Dutch tennis player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch tennis player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Scottish Australian gender activist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_May-<PERSON>\" title=\"<PERSON><PERSON> May-Welby\"><PERSON><PERSON> May-<PERSON></a>, Scottish Australian gender activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_May-<PERSON>\" title=\"<PERSON><PERSON> May-Welby\"><PERSON><PERSON> May-<PERSON></a>, Scottish Australian gender activist", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_May-We<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Swiss mathematician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Swiss lawyer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Spanish footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_Hontiyuelo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_Hontiyuelo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German director, producer, screenwriter, and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, screenwriter, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, screenwriter, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Zimbabwean-English cricketer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Swedish politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English musician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American race car driver and businessman, co-founded Bryan <PERSON>ta Autosport", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Autosport\" title=\"Bryan Herta Autosport\"><PERSON> Autosport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Autosport\" title=\"Bryan Herta Autosport\"><PERSON> Autosport</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Autosport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Autosport"}]}, {"year": "1971", "text": "<PERSON>, English journalist and politician, former Chancellor of the Exchequer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, former <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Brazilian race car driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Australian entrepreneur", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, Australian entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, Australian entrepreneur", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English cricketer and umpire", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter, guitarist, actress, and poet", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, guitarist, actress, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, guitarist, actress, and poet", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German politician, German Federal Minister of Family Affairs", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Family_Affairs,_Senior_Citizens,_Women_and_Youth\" title=\"Federal Ministry of Family Affairs, Senior Citizens, Women and Youth\">German Federal Minister of Family Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Family_Affairs,_Senior_Citizens,_Women_and_Youth\" title=\"Federal Ministry of Family Affairs, Senior Citizens, Women and Youth\">German Federal Minister of Family Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Federal Ministry of Family Affairs, Senior Citizens, Women and Youth", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Family_Affairs,_Senior_Citizens,_Women_and_Youth"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1976)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born May 1976)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1976)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born May 1976)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, born May 1976)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1976)"}]}, {"year": "1977", "text": "<PERSON>, British actor, director and writer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, director and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, director and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Russian figure skater", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American drummer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2018)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Belgian-Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian-Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Portuguese footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Fern%C3%A1<PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Fern%C3%A1<PERSON><PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Fern%C3%A1<PERSON><PERSON>_(footballer,_born_1985)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Wim_Stroetinga\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_Stroetinga\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_St<PERSON>etinga"}]}, {"year": "1985", "text": "<PERSON>, Scottish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American film director and screenwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian-Azerbaijani figure skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Azerbaijani figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Azerbaijani figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian actress, director, producer, and screenwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Otto\" title=\"<PERSON> Otto\"><PERSON></a>, Australian actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Otto\" title=\"<PERSON> Otto\"><PERSON></a>, Australian actress, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American wrestler (d. 2023)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Canadian biathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian biathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American golfer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Morgan_Pressel\" title=\"Morgan Pressel\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morgan_Pressel\" title=\"Morgan Pressel\"><PERSON></a>, American golfer", "links": [{"title": "Morgan Pressel", "link": "https://wikipedia.org/wiki/Morgan_Pressel"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, British tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovakian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Krist%C3%ADna_Ku%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovakian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krist%C3%ADna_Ku%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovakian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krist%C3%ADna_Ku%C4%8Dov%C3%A1"}]}, {"year": "1990", "text": "<PERSON>, Estonian volleyball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>-<PERSON><PERSON><PERSON>, German singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Chilean footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Pinares"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, German ski jumper", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Kathari<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Kathari<PERSON>\"><PERSON><PERSON><PERSON></a>, German ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Katharina <PERSON>\"><PERSON><PERSON><PERSON></a>, German ski jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Ghanaian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1996)\" title=\"<PERSON> (footballer, born 1996)\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1996)\" title=\"<PERSON> (footballer, born 1996)\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON> (footballer, born 1996)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1996)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/R%C4%83z<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C4%83<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C4%83z<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Spanish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Coy_Craft\" title=\"Coy Craft\"><PERSON><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coy_Craft\" title=\"Coy Craft\"><PERSON><PERSON></a>, American footballer", "links": [{"title": "Coy Craft", "link": "https://wikipedia.org/wiki/Coy_Craft"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Swedish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1997)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1997)\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1997)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1997)\"><PERSON><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1997)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1997)"}]}, {"year": "1997", "text": "<PERSON>, New Zealand basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian racing driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/S%C3%A9rgio_Sette_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9rgio_Sette_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rgio_Sette_C%C3%A2mara"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Bahraini track and field sprinter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Eid_Naser\" title=\"Salwa Eid Naser\"><PERSON><PERSON></a>, Bahraini track and field sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Eid_Naser\" title=\"Salwa Eid Naser\"><PERSON><PERSON></a>, Bahraini track and field sprinter", "links": [{"title": "Salwa Eid Naser", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ser"}]}, {"year": "1999", "text": "<PERSON>, American internet personality", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer and songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Trinidad_Cardona\" title=\"Trinidad Cardona\">Trinidad Cardona</a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trinidad_Cardona\" title=\"Trinidad Cardona\">Trinidad Cardona</a>, American singer and songwriter", "links": [{"title": "Trinidad Cardona", "link": "https://wikipedia.org/wiki/Trinidad_Cardona"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Georgian-American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Brazilian-Italian racing driver", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Italian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}], "Deaths": [{"year": "230", "text": "<PERSON>, pope of the Catholic Church", "html": "230 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_I\" title=\"Pope Urban I\"><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_I\" title=\"Pope Urban I\">Urban <PERSON></a>, pope of the Catholic Church", "links": [{"title": "Pope Urban I", "link": "https://wikipedia.org/wiki/Pope_Urban_I"}]}, {"year": "922", "text": "<PERSON>, Chinese general and governor", "html": "922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a>, Chinese general and governor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Si<PERSON>\"><PERSON></a>, Chinese general and governor", "links": [{"title": "Li Si<PERSON>hao", "link": "https://wikipedia.org/wiki/Li_Si<PERSON>hao"}]}, {"year": "962", "text": "<PERSON><PERSON><PERSON> of Gembloux, Frankish abbot (b. 892)", "html": "962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Gembloux\" title=\"<PERSON><PERSON><PERSON> of Gembloux\"><PERSON><PERSON><PERSON> of Gembloux</a>, Frankish abbot (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Gembloux\" title=\"<PERSON><PERSON><PERSON> of Gembloux\"><PERSON><PERSON><PERSON> of Gembloux</a>, Frankish abbot (b. 892)", "links": [{"title": "<PERSON><PERSON><PERSON> of Gembloux", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>loux"}]}, {"year": "1125", "text": "<PERSON>, Holy Roman Emperor (b. 1086)", "html": "1125 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1086)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1086)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1304", "text": "<PERSON><PERSON>, French poet and composer", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1338", "text": "<PERSON>, Countess of Arundel, English noble (b. 1287)", "html": "1338 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Arundel\" title=\"<PERSON>, Countess of Arundel\"><PERSON>, Countess of Arundel</a>, English noble (b. 1287)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Arundel\" title=\"<PERSON>, Countess of Arundel\"><PERSON>, Countess of Arundel</a>, English noble (b. 1287)", "links": [{"title": "<PERSON>, Countess of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_of_Arundel"}]}, {"year": "1370", "text": "<PERSON><PERSON><PERSON>, Mongol emperor (b. 1320)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/Toghon_Tem%C3%BCr\" title=\"Toghon Temür\"><PERSON><PERSON><PERSON></a>, Mongol emperor (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toghon_Tem%C3%BCr\" title=\"Toghon Temür\"><PERSON><PERSON><PERSON></a>, Mongol emperor (b. 1320)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toghon_Tem%C3%BCr"}]}, {"year": "1423", "text": "<PERSON><PERSON><PERSON> (b. 1328)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/Antipope_<PERSON>_XIII\" title=\"Antipope Benedict XIII\">Antipop<PERSON> <PERSON></a> (b. 1328)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_<PERSON>_XIII\" title=\"Antipope Benedict XIII\">Antipop<PERSON> <PERSON></a> (b. 1328)", "links": [{"title": "Antipope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1498", "text": "<PERSON><PERSON><PERSON>, Italian friar and preacher (b. 1452)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a>, Italian friar and preacher (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a>, Italian friar and preacher (b. 1452)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Savonarola"}]}, {"year": "1523", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1466)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshitane\" title=\"Ashikaga Yoshitane\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshitane\" title=\"Ashikaga Yoshitane\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1466)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashika<PERSON>_<PERSON>shitane"}]}, {"year": "1524", "text": "<PERSON>, First Emperor of Safavid Empire (b. 1487)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Emperor of <a href=\"https://wikipedia.org/wiki/Safavid_Empire\" class=\"mw-redirect\" title=\"Safavid Empire\">Safavid Empire</a> (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a>, First Emperor of <a href=\"https://wikipedia.org/wiki/Safavid_Empire\" class=\"mw-redirect\" title=\"Safavid Empire\">Safavid Empire</a> (b. 1487)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Safavid Empire", "link": "https://wikipedia.org/wiki/Safavid_Empire"}]}, {"year": "1591", "text": "<PERSON>, English organist and composer (b. 1525)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1525)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, English bishop (b. 1605)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON><PERSON>, Grand Duke of Tuscany (b. 1610)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1610)", "links": [{"title": "<PERSON><PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1691", "text": "<PERSON><PERSON>, French astronomer and instrument maker (b. 1622)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French astronomer and instrument maker (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French astronomer and instrument maker (b. 1622)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, Scottish pirate (b. 1645)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pirate (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish pirate (b. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Polish martyr (b. 1700)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ben_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Polish martyr (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ben_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Polish martyr (b. 1700)", "links": [{"title": "<PERSON> ben <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, English-American printer (b. 1663)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Colonial_printer)\" class=\"mw-redirect\" title=\"<PERSON> (Colonial printer)\"><PERSON></a>, English-American printer (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Colonial_printer)\" class=\"mw-redirect\" title=\"<PERSON> (Colonial printer)\"><PERSON></a>, English-American printer (b. 1663)", "links": [{"title": "<PERSON> (Colonial printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Colonial_printer)"}]}, {"year": "1754", "text": "<PERSON>, the Elder, English architect, designed The Circus and Queen Square (b. 1704)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_the_Elder\" title=\"<PERSON>, the Elder\"><PERSON>, the Elder</a>, English architect, designed <a href=\"https://wikipedia.org/wiki/The_Circus,_Bath\" title=\"The Circus, Bath\">The Circus</a> and <a href=\"https://wikipedia.org/wiki/Queen_Square_(Bath)\" class=\"mw-redirect\" title=\"Queen Square (Bath)\">Queen Square</a> (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_the_Elder\" title=\"<PERSON>, the Elder\"><PERSON>, the Elder</a>, English architect, designed <a href=\"https://wikipedia.org/wiki/The_Circus,_Bath\" title=\"The Circus, Bath\">The Circus</a> and <a href=\"https://wikipedia.org/wiki/Queen_Square_(Bath)\" class=\"mw-redirect\" title=\"Queen Square (Bath)\">Queen Square</a> (b. 1704)", "links": [{"title": "<PERSON>, the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_the_Elder"}, {"title": "The Circus, Bath", "link": "https://wikipedia.org/wiki/The_Circus,_Bath"}, {"title": "Queen Square (Bath)", "link": "https://wikipedia.org/wiki/Queen_Square_(Bath)"}]}, {"year": "1783", "text": "<PERSON>, American lawyer and politician (b. 1725)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (b. 1725)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON>, French general and diplomat (b. 1772)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and diplomat (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and diplomat (b. 1772)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ra<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON>, American clergyman and botanist (b. 1753)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American clergyman and botanist (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American clergyman and botanist (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, German philosopher and theologian (b. 1765)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and theologian (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English lieutenant and explorer (b. 1797)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and explorer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and explorer (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "August<PERSON><PERSON><PERSON>, French mathematician and academic (b. 1789)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/August<PERSON>-<PERSON>\" title=\"August<PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, French mathematician and academic (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August<PERSON>-<PERSON>_<PERSON>\" title=\"August<PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, French mathematician and academic (b. 1789)", "links": [{"title": "August<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American general (b. 1809)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German historian and academic (b. 1795)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Austrian politician (b. 1805)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German mineralogist, physicist, and mathematician (b. 1798)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mineralogist, physicist, and mathematician (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mineralogist, physicist, and mathematician (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Norwegian director, playwright, and poet (b. 1828)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director, playwright, and poet (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian director, playwright, and poet (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, French poet and author (b. 1842)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Copp%C3%A9e"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian-Austrian field marshal (b. 1856)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Austrian field marshal (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-Austrian field marshal (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svetozar_Boroevi%C4%87"}]}, {"year": "1921", "text": "<PERSON>, Swedish shot putter and tug of war competitor (b. 1872)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Swedish shot putter and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Swedish shot putter and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1934", "text": "<PERSON>, American criminal (b. 1909)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barrow\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clyde_Barrow\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1909)", "links": [{"title": "Clyde Barrow", "link": "https://wikipedia.org/wiki/Clyde_Barrow"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Estonian journalist and politician (b. 1860)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American criminal (b. 1910)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American criminal (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American businessman and philanthropist, founded the Standard Oil Company and Rockefeller University (b. 1839)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil Company</a> and <a href=\"https://wikipedia.org/wiki/Rockefeller_University\" title=\"Rockefeller University\">Rockefeller University</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil Company</a> and <a href=\"https://wikipedia.org/wiki/Rockefeller_University\" title=\"Rockefeller University\">Rockefeller University</a> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Standard Oil", "link": "https://wikipedia.org/wiki/Standard_Oil"}, {"title": "Rockefeller University", "link": "https://wikipedia.org/wiki/Rockefeller_University"}]}, {"year": "1938", "text": "<PERSON>, Swiss-American painter (b. 1871)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American painter (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American painter (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek composer and conductor (b. 1886)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek composer and conductor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek composer and conductor (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panagiotis_Toundas"}]}, {"year": "1945", "text": "<PERSON>, German commander and politician, Reich Minister of the Interior (b. 1900)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">Reich Minister of the Interior</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">Reich Minister of the Interior</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of the Interior (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Swiss author and poet (b. 1878)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Belgian painter and illustrator (b. 1872)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and illustrator (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and illustrator (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Latvian-Estonian poet and politician (b. 1883)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Suits\"><PERSON></a>, Latvian-Estonian poet and politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gustav Suits\"><PERSON></a>, Latvian-Estonian poet and politician (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1960", "text": "<PERSON>, French engineer and inventor, created Neon lighting (b. 1870)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and inventor, created <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">Neon lighting</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and inventor, created <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">Neon lighting</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Neon lighting", "link": "https://wikipedia.org/wiki/Neon_lighting"}]}, {"year": "1962", "text": "<PERSON>, French engineer (b. 1879)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"Louis <PERSON>\"><PERSON></a>, French engineer (b. 1879)", "links": [{"title": "Louis <PERSON>", "link": "https://wikipedia.org/wiki/Louis_Coatalen"}]}, {"year": "1963", "text": "<PERSON>, Estonian author and politician (b. 1904)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, Estonian author and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, Estonian author and politician (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American sculptor (b. 1906)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor (b. 1906)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_(sculptor)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American comedian and actor (b. 1894)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and actor (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Sri Lankan geographer and academic (b. 1932)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/S._Se<PERSON>yagam\" title=\"S. Se<PERSON>vanayagam\"><PERSON><PERSON></a>, Sri Lankan geographer and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Se<PERSON>vanayagam\" title=\"S. Se<PERSON>vana<PERSON>gam\"><PERSON><PERSON></a>, Sri Lankan geographer and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON><PERSON>yagam"}]}, {"year": "1981", "text": "<PERSON>, American baseball player (b. 1933)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1933)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English author and poet (b. 1911)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Heppenstall\"><PERSON><PERSON></a>, English author and poet (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Heppenstall\"><PERSON><PERSON></a>, English author and poet (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, singer, and producer (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (b. 1898)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1981", "text": "<PERSON>, Belarusian-Canadian lawyer and politician (b. 1909)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Belarusian-Canadian lawyer and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Belarusian-Canadian lawyer and politician (b. 1909)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)"}]}, {"year": "1986", "text": "<PERSON>, American actor (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Russian director and producer (b. 1915)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian director and producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian director and producer (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German computer hacker (b. 1965)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hacker)\" title=\"<PERSON> (hacker)\"><PERSON></a>, German computer hacker (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hacker)\" title=\"<PERSON> (hacker)\"><PERSON></a>, German computer hacker (b. 1965)", "links": [{"title": "<PERSON> (hacker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hacker)"}]}, {"year": "1991", "text": "<PERSON>, German pianist and composer (b. 1895)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Belgian academic and politician, 50th Prime Minister of Belgium (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian academic and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1991", "text": "<PERSON>, Canadian director, screenwriter, and producer (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, screenwriter, and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, screenwriter, and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Greek footballer (b. 1948)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Italian lawyer and judge (b. 1939)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Norwegian poet (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Russian journalist and activist (b. 1934)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and activist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and activist (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American general and lawyer (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Taylor\"><PERSON><PERSON></a>, American general and lawyer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Taylor\"><PERSON><PERSON></a>, American general and lawyer (b. 1908)", "links": [{"title": "<PERSON><PERSON> Taylor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Taylor"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American wrestler (b. 1965)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Owen Hart\"><PERSON></a>, Canadian-American wrestler (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON> <PERSON>, Australian activist and last speaker of the Gaagudju language (b. c. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist and last speaker of the <a href=\"https://wikipedia.org/wiki/Gaagudju_language\" title=\"Gaagudju language\">Gaagudju language</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1920</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist and last speaker of the <a href=\"https://wikipedia.org/wiki/Gaagudju_language\" title=\"Gaagudju language\">Gaagudju language</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1920</span>)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gaagudju language", "link": "https://wikipedia.org/wiki/Gaagudju_language"}]}, {"year": "2002", "text": "<PERSON>, American golfer and journalist (b. 1912)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and journalist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and journalist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "2006", "text": "<PERSON>, American colonel and politician, 69th United States Secretary of the Treasury (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 69th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and manager (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and manager (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Spanish mountaineer (b. 1967)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON>_de_<PERSON>za\" title=\"I<PERSON><PERSON> de Olza\"><PERSON><PERSON><PERSON></a>, Spanish mountaineer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON>_<PERSON>_<PERSON>za\" title=\"I<PERSON><PERSON>cho<PERSON> de Olza\"><PERSON><PERSON><PERSON></a>, Spanish mountaineer (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>lza", "link": "https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter and poet (b. 1935)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Phillips\" title=\"<PERSON> Phillips\"><PERSON></a>, American singer-songwriter and poet (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Utah_Phillips\" title=\"<PERSON> Phillips\"><PERSON></a>, American singer-songwriter and poet (b. 1935)", "links": [{"title": "Utah Phillips", "link": "https://wikipedia.org/wiki/Utah_Phillips"}]}, {"year": "2009", "text": "<PERSON><PERSON>, South Korean soldier and politician, 9th President of South Korea (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>yun"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "2010", "text": "<PERSON>, Dominican-American baseball player (b. 1972)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lima\" title=\"José <PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Lima\" title=\"José <PERSON>\"><PERSON></a>, Dominican-American baseball player (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Lima"}]}, {"year": "2010", "text": "<PERSON>, English director, producer, and screenwriter (b. 1970)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Simon Monjack\"><PERSON></a>, English director, producer, and screenwriter (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Spanish cyclist (b. 1978)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American historian, author, and academic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Dominican baseball player, coach, and scout (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Guerrero\"><PERSON><PERSON></a>, Dominican baseball player, coach, and scout (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Guerrero\"><PERSON><PERSON></a>, Dominican baseball player, coach, and scout (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Epy_Guerrero"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Turkish police officer and politician, 15th Governor of Istanbul Province (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7%C4%B1o%C4%9Flu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish police officer and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Istanbul_Province\" class=\"mw-redirect\" title=\"List of Governors of Istanbul Province\">Governor of Istanbul Province</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7%C4%B1o%C4%9Flu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish police officer and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Istanbul_Province\" class=\"mw-redirect\" title=\"List of Governors of Istanbul Province\">Governor of Istanbul Province</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7%C4%B1o%C4%9Flu"}, {"title": "List of Governors of Istanbul Province", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Istanbul_Province"}]}, {"year": "2013", "text": "<PERSON>, Egyptian-French singer-songwriter and guitarist (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and guitarist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter and guitarist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American basketball player (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Russian linguist and academic (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian linguist and academic (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian linguist and academic (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>ha<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ha<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ha<PERSON>_<PERSON>tri"}]}, {"year": "2015", "text": "<PERSON>, American actress, comedian and playwright (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and playwright (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and playwright (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Pro-Russian Ukrainian separatist leader (b. 1975)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pro-Russian Ukrainian separatist leader (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pro-Russian Ukrainian separatist leader (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Salvadoran-American physicist and engineer (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American physicist and engineer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American physicist and engineer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American mathematician and academic, Nobel Prize laureate (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American mathematician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American mathematician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1928)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2017", "text": "<PERSON>, English actor (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Japanese professional wrestler (b. 1997)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese professional wrestler (b. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English long-distance runner (b. 1938)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long-distance runner (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long-distance runner (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American children's book designer, illustrator, and writer best known for The Very Hungry Caterpillar (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American children's book designer, illustrator, and writer best known for <i><a href=\"https://wikipedia.org/wiki/The_Very_Hungry_Caterpillar\" title=\"The Very Hungry Caterpillar\">The Very Hungry Caterpillar</a></i> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American children's book designer, illustrator, and writer best known for <i><a href=\"https://wikipedia.org/wiki/The_Very_Hungry_Caterpillar\" title=\"The Very Hungry Caterpillar\">The Very Hungry Caterpillar</a></i> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Very Hungry Caterpillar", "link": "https://wikipedia.org/wiki/The_Very_Hung<PERSON>_Caterpillar"}]}, {"year": "2024", "text": "<PERSON>, American military historian and author (b. 1955)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military historian and author (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military historian and author (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American filmmaker (b. 1970)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}