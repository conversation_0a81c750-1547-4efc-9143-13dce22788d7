{"date": "February 17", "url": "https://wikipedia.org/wiki/February_17", "data": {"Events": [{"year": "1370", "text": "Northern Crusades: Grand Duchy of Lithuania and the Teutonic Knights meet in the Battle of Rudau.", "html": "1370 - <a href=\"https://wikipedia.org/wiki/Northern_Crusades\" title=\"Northern Crusades\">Northern Crusades</a>: <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> and the <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Knights</a> meet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Rudau\" title=\"Battle of Rudau\">Battle of Rudau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northern_Crusades\" title=\"Northern Crusades\">Northern Crusades</a>: <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> and the <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Knights</a> meet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Rudau\" title=\"Battle of Rudau\">Battle of Rudau</a>.", "links": [{"title": "Northern Crusades", "link": "https://wikipedia.org/wiki/Northern_Crusades"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Teutonic Knights", "link": "https://wikipedia.org/wiki/Teutonic_Knights"}, {"title": "Battle of Rudau", "link": "https://wikipedia.org/wiki/Battle_of_Rudau"}]}, {"year": "1411", "text": "Following the successful campaigns during the Ottoman Interregnum, <PERSON>, one of the sons of <PERSON><PERSON><PERSON>, becomes Sultan of the Ottoman Empire with the support of <PERSON><PERSON> of Wallachia.", "html": "1411 - Following the successful campaigns during the <a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>, <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"<PERSON> Çelebi\"><PERSON></a>, one of the sons of <a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\">Bayezid I</a>, becomes <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> with the support of <a href=\"https://wikipedia.org/wiki/Mircea_I_of_Wallachia\" class=\"mw-redirect\" title=\"Mircea I of Wallachia\"><PERSON><PERSON> I of Wallachia</a>.", "no_year_html": "Following the successful campaigns during the <a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>, <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"<PERSON> Çelebi\"><PERSON></a>, one of the sons of <a href=\"https://wikipedia.org/wiki/Bayezid_I\" title=\"Bayezid I\">Bayezid I</a>, becomes <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">Sultan</a> of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> with the support of <a href=\"https://wikipedia.org/wiki/Mircea_I_of_Wallachia\" class=\"mw-redirect\" title=\"Mircea I of Wallachia\"><PERSON><PERSON> I of Wallachia</a>.", "links": [{"title": "Ottoman Interregnum", "link": "https://wikipedia.org/wiki/Ottoman_Interregnum"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Musa_%C3%87elebi"}, {"title": "Bayezid I", "link": "https://wikipedia.org/wiki/<PERSON>ez<PERSON>_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON> I of Wallachia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Wallachia"}]}, {"year": "1500", "text": "<PERSON> and <PERSON> attempt to subdue the peasantry of Dithmarschen, Denmark, in the Battle of Hemmingstedt.", "html": "1500 - Duke <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a> and <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark\" title=\"<PERSON>, King of Denmark\"><PERSON></a> attempt to subdue the <a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasantry</a> of <a href=\"https://wikipedia.org/wiki/Dithmarschen\" title=\"Dithmarschen\">Dithmarschen</a>, Denmark, in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hemmingstedt\" title=\"Battle of Hemmingstedt\">Battle of Hemmingstedt</a>.", "no_year_html": "Duke <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a> and Duke <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark\" title=\"<PERSON>, King of Denmark\"><PERSON></a> attempt to subdue the <a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasantry</a> of <a href=\"https://wikipedia.org/wiki/Dithmarschen\" title=\"Dithmarschen\">Dithmarschen</a>, Denmark, in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hemmingstedt\" title=\"Battle of Hemmingstedt\">Battle of Hemmingstedt</a>.", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark"}, {"title": "<PERSON>, King of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_Denmark"}, {"title": "Peasant", "link": "https://wikipedia.org/wiki/Peasant"}, {"title": "Dithmarschen", "link": "https://wikipedia.org/wiki/Dithmarschen"}, {"title": "Battle of Hemmingstedt", "link": "https://wikipedia.org/wiki/Battle_of_Hemmingstedt"}]}, {"year": "1600", "text": "On his way to be burned at the stake for heresy, at Campo de' Fiori in Rome, the philosopher <PERSON><PERSON><PERSON><PERSON> has a wooden vise put on his tongue to prevent him continuing to speak.", "html": "1600 - On his way to be burned at the stake for heresy, at <a href=\"https://wikipedia.org/wiki/Campo_de%27_Fiori\" title=\"Campo de' Fiori\"><PERSON> de<PERSON>ori</a> in Rome, the philosopher <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> has a wooden vise put on his tongue to prevent him continuing to speak.", "no_year_html": "On his way to be burned at the stake for heresy, at <a href=\"https://wikipedia.org/wiki/Campo_de%27_Fiori\" title=\"Campo de' Fiori\"><PERSON>ori</a> in Rome, the philosopher <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> has a wooden vise put on his tongue to prevent him continuing to speak.", "links": [{"title": "Campo de' Fiori", "link": "https://wikipedia.org/wiki/Campo_de%27_<PERSON>ori"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON><PERSON><PERSON><PERSON> proclaims himself Khan of the Later Jin, precursor to the Qing Dynasty.", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Qing_dynasty#Khans_of_Later_Jin_(1616-1636)\" title=\"List of emperors of the Qing dynasty\">Khan</a> of the <a href=\"https://wikipedia.org/wiki/Later_Jin_(1616%E2%80%931636)\" title=\"Later Jin (1616-1636)\">Later Jin</a>, precursor to the <a href=\"https://wikipedia.org/wiki/Qing_Dynasty\" class=\"mw-redirect\" title=\"Qing Dynasty\">Qing Dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Qing_dynasty#Khans_of_Later_Jin_(1616-1636)\" title=\"List of emperors of the Qing dynasty\">Khan</a> of the <a href=\"https://wikipedia.org/wiki/Later_Jin_(1616%E2%80%931636)\" title=\"Later Jin (1616-1636)\">Later Jin</a>, precursor to the <a href=\"https://wikipedia.org/wiki/Qing_Dynasty\" class=\"mw-redirect\" title=\"Qing Dynasty\">Qing Dynasty</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ci"}, {"title": "List of emperors of the Qing dynasty", "link": "https://wikipedia.org/wiki/List_of_emperors_of_the_Qing_dynasty#Khans_of_Later_Jin_(1616-1636)"}, {"title": "<PERSON> <PERSON> (1616-1636)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1616%E2%80%931636)"}, {"title": "Qing Dynasty", "link": "https://wikipedia.org/wiki/Qing_Dynasty"}]}, {"year": "1621", "text": "<PERSON><PERSON> is appointed as first military commander of the English Plymouth Colony in North America.", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ish\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is appointed as first military commander of the English <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> in North America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is appointed as first military commander of the English <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> in North America.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ish"}, {"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}]}, {"year": "1674", "text": "An earthquake strikes the Indonesian island of Ambon. It triggers a 100 m (330 ft) megatsunami which drowns over 2,300 people.", "html": "1674 - An <a href=\"https://wikipedia.org/wiki/1674_Ambon_earthquake_and_megatsunami\" title=\"1674 Ambon earthquake and megatsunami\">earthquake strikes</a> the Indonesian island of <a href=\"https://wikipedia.org/wiki/Ambon_Island\" title=\"Ambon Island\">Ambon</a>. It triggers a 100 m (330 ft) <a href=\"https://wikipedia.org/wiki/Megatsunami\" title=\"Megatsunami\">megatsunami</a> which drowns over 2,300 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1674_Ambon_earthquake_and_megatsunami\" title=\"1674 Ambon earthquake and megatsunami\">earthquake strikes</a> the Indonesian island of <a href=\"https://wikipedia.org/wiki/Ambon_Island\" title=\"Ambon Island\">Ambon</a>. It triggers a 100 m (330 ft) <a href=\"https://wikipedia.org/wiki/Megatsunami\" title=\"Megatsunami\">megatsunami</a> which drowns over 2,300 people.", "links": [{"title": "1674 Ambon earthquake and megatsunami", "link": "https://wikipedia.org/wiki/1674_Ambon_earthquake_and_megatsunami"}, {"title": "Ambon Island", "link": "https://wikipedia.org/wiki/Ambon_Island"}, {"title": "Megatsunami", "link": "https://wikipedia.org/wiki/Megatsunami"}]}, {"year": "1676", "text": "Sixteen men of <PERSON><PERSON><PERSON><PERSON>'s expedition are lost at Evangelistas Islets at the western end of the Strait of Magellan.", "html": "1676 - Sixteen men of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition\" title=\"Antonio <PERSON> expedition\"><PERSON><PERSON><PERSON><PERSON> <PERSON>'s expedition</a> are lost at <a href=\"https://wikipedia.org/wiki/Evangelistas_Islets\" title=\"Evangelistas Islets\">Evan<PERSON>as Islets</a> at the western end of the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "no_year_html": "Sixteen men of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition\" title=\"Antonio <PERSON> expedition\"><PERSON><PERSON><PERSON><PERSON> <PERSON>'s expedition</a> are lost at <a href=\"https://wikipedia.org/wiki/Evangelistas_Islets\" title=\"Evangelistas Islets\">Evangelistas Islets</a> at the western end of the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "links": [{"title": "<PERSON> expedition", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_expedition"}, {"title": "Evangelistas <PERSON>", "link": "https://wikipedia.org/wiki/Evangelistas_Islets"}, {"title": "Strait of Magellan", "link": "https://wikipedia.org/wiki/Strait_of_Magellan"}]}, {"year": "1739", "text": "The Battle of Vasai commences as the Marathas move to invade Portuguese-occupied territory.", "html": "1739 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Vasai\" title=\"Battle of Vasai\">Battle of Vasai</a> commences as the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Marathas</a> move to invade Portuguese-occupied territory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Vasai\" title=\"Battle of Vasai\">Battle of Vasai</a> commences as the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Marathas</a> move to invade Portuguese-occupied territory.", "links": [{"title": "Battle of Vasai", "link": "https://wikipedia.org/wiki/Battle_of_Vasai"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1753", "text": "In Sweden, February 17 is followed by March 1 as the country moves from the Julian calendar to the Gregorian calendar.", "html": "1753 - In Sweden, February 17 is followed by <a href=\"https://wikipedia.org/wiki/March_1\" title=\"March 1\">March 1</a> as the country moves from the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a> to the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>.", "no_year_html": "In Sweden, February 17 is followed by <a href=\"https://wikipedia.org/wiki/March_1\" title=\"March 1\">March 1</a> as the country moves from the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a> to the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>.", "links": [{"title": "March 1", "link": "https://wikipedia.org/wiki/March_1"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}, {"title": "Gregorian calendar", "link": "https://wikipedia.org/wiki/Gregorian_calendar"}]}, {"year": "1801", "text": "United States presidential election: A tie in the Electoral College between <PERSON> and <PERSON> is resolved when <PERSON> is elected President of the United States and <PERSON> Vice President by the United States House of Representatives.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/1800_United_States_presidential_election\" title=\"1800 United States presidential election\">United States presidential election</a>: A tie in the <a href=\"https://wikipedia.org/wiki/United_States_Electoral_College\" title=\"United States Electoral College\">Electoral College</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is resolved when <PERSON> is elected <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> and <PERSON> <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1800_United_States_presidential_election\" title=\"1800 United States presidential election\">United States presidential election</a>: A tie in the <a href=\"https://wikipedia.org/wiki/United_States_Electoral_College\" title=\"United States Electoral College\">Electoral College</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is resolved when <PERSON> is elected <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> and <PERSON> <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President</a> by the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a>.", "links": [{"title": "1800 United States presidential election", "link": "https://wikipedia.org/wiki/1800_United_States_presidential_election"}, {"title": "United States Electoral College", "link": "https://wikipedia.org/wiki/United_States_Electoral_College"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1814", "text": "War of the Sixth Coalition: The Battle of Mormant.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mormant\" title=\"Battle of Mormant\">Battle of Mormant</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mormant\" title=\"Battle of Mormant\">Battle of Mormant</a>.", "links": [{"title": "War of the Sixth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Sixth_Coalition"}, {"title": "Battle of Mormant", "link": "https://wikipedia.org/wiki/Battle_of_Mormant"}]}, {"year": "1819", "text": "The United States House of Representatives passes the Missouri Compromise for the first time.", "html": "1819 - The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> passes the <a href=\"https://wikipedia.org/wiki/Missouri_Compromise\" title=\"Missouri Compromise\">Missouri Compromise</a> for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> passes the <a href=\"https://wikipedia.org/wiki/Missouri_Compromise\" title=\"Missouri Compromise\">Missouri Compromise</a> for the first time.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Missouri Compromise", "link": "https://wikipedia.org/wiki/Missouri_Compromise"}]}, {"year": "1838", "text": "Weenen massacre: Hundreds of Voortrekkers along the Blaukraans River, Natal are killed by Zulus.", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Weenen_massacre\" title=\"Weenen massacre\">Weenen massacre</a>: Hundreds of <a href=\"https://wikipedia.org/wiki/Voortrekkers\" class=\"mw-redirect\" title=\"Voortrekkers\">Voortrekkers</a> along the Blaukraans River, <a href=\"https://wikipedia.org/wiki/KwaZulu-Natal\" title=\"KwaZulu-Natal\">Natal</a> are killed by <a href=\"https://wikipedia.org/wiki/Zulu_people\" title=\"Zulu people\">Zulus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Weenen_massacre\" title=\"Weenen massacre\">Weenen massacre</a>: Hundreds of <a href=\"https://wikipedia.org/wiki/Voortrekkers\" class=\"mw-redirect\" title=\"Voortrekkers\">Voortrekkers</a> along the Blaukraans River, <a href=\"https://wikipedia.org/wiki/KwaZulu-Natal\" title=\"KwaZulu-Natal\">Natal</a> are killed by <a href=\"https://wikipedia.org/wiki/Zulu_people\" title=\"Zulu people\">Zulus</a>.", "links": [{"title": "Weenen massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Voortrekkers", "link": "https://wikipedia.org/wiki/Voortrekkers"}, {"title": "KwaZulu-Natal", "link": "https://wikipedia.org/wiki/KwaZulu-Natal"}, {"title": "Zulu people", "link": "https://wikipedia.org/wiki/Zulu_people"}]}, {"year": "1854", "text": "The United Kingdom recognizes the independence of the Orange Free State.", "html": "1854 - The United Kingdom recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>.", "no_year_html": "The United Kingdom recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>.", "links": [{"title": "Orange Free State", "link": "https://wikipedia.org/wiki/Orange_Free_State"}]}, {"year": "1859", "text": "Cochinchina Campaign: The French Navy captures the Citadel of Saigon, a fortress manned by 1,000 Nguyễn dynasty soldiers, en route to conquering Saigon and other regions of southern Viet Nam.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Cochinchina_Campaign\" class=\"mw-redirect\" title=\"Cochinchina Campaign\">Cochinchina Campaign</a>: The <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> captures the <a href=\"https://wikipedia.org/wiki/Citadel_of_Saigon\" title=\"Citadel of Saigon\">Citadel of Saigon</a>, a fortress manned by 1,000 <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Nguyễn dynasty</a> soldiers, en route to conquering <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> and other regions of southern Viet Nam.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cochinchina_Campaign\" class=\"mw-redirect\" title=\"Cochinchina Campaign\">Cochinchina Campaign</a>: The <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> captures the <a href=\"https://wikipedia.org/wiki/Citadel_of_Saigon\" title=\"Citadel of Saigon\">Citadel of Saigon</a>, a fortress manned by 1,000 <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty\" title=\"Nguyễn dynasty\">Nguyễn dynasty</a> soldiers, en route to conquering <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> and other regions of southern Viet Nam.", "links": [{"title": "Cochinchina Campaign", "link": "https://wikipedia.org/wiki/Cochinchina_Campaign"}, {"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Citadel of Saigon", "link": "https://wikipedia.org/wiki/Citadel_of_Saigon"}, {"title": "Nguyễn dynasty", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_dynasty"}, {"title": "Saigon", "link": "https://wikipedia.org/wiki/Saigon"}]}, {"year": "1863", "text": "A group of citizens of Geneva found an International Committee for Relief to the Wounded, which later became known as the International Committee of the Red Cross.", "html": "1863 - A group of citizens of <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> found an International Committee for Relief to the Wounded, which later became known as the <a href=\"https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross\" title=\"International Committee of the Red Cross\">International Committee of the Red Cross</a>.", "no_year_html": "A group of citizens of <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a> found an International Committee for Relief to the Wounded, which later became known as the <a href=\"https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross\" title=\"International Committee of the Red Cross\">International Committee of the Red Cross</a>.", "links": [{"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "International Committee of the Red Cross", "link": "https://wikipedia.org/wiki/International_Committee_of_the_Red_Cross"}]}, {"year": "1864", "text": "American Civil War: The <PERSON>. <PERSON><PERSON> becomes the first submarine to engage and sink a warship, the USS Housatonic.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><i><PERSON><PERSON> <PERSON><PERSON></i></a> becomes the first submarine to <a href=\"https://wikipedia.org/wiki/Sinking_of_USS_Housatonic\" title=\"Sinking of USS Housatonic\">engage and sink a warship</a>, the <a href=\"https://wikipedia.org/wiki/USS_Housatonic_(1861)\" title=\"USS Housatonic (1861)\">USS <i>Housatonic</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><i><PERSON><PERSON> <PERSON><PERSON></i></a> becomes the first submarine to <a href=\"https://wikipedia.org/wiki/Sinking_of_USS_Housatonic\" title=\"Sinking of USS Housatonic\">engage and sink a warship</a>, the <a href=\"https://wikipedia.org/wiki/USS_Housatonic_(1861)\" title=\"USS Housatonic (1861)\">USS <i>Housatonic</i></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Sinking of USS Housatonic", "link": "https://wikipedia.org/wiki/Sinking_of_USS_Housatonic"}, {"title": "USS Housatonic (1861)", "link": "https://wikipedia.org/wiki/USS_Housatonic_(1861)"}]}, {"year": "1865", "text": "American Civil War: Columbia, South Carolina, is burned as Confederate forces flee from advancing Union forces.", "html": "1865 - American Civil War: <a href=\"https://wikipedia.org/wiki/Columbia,_South_Carolina\" title=\"Columbia, South Carolina\">Columbia, South Carolina</a>, is <a href=\"https://wikipedia.org/wiki/Capture_of_Columbia\" title=\"Capture of Columbia\">burned</a> as <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces flee from advancing <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Columbia,_South_Carolina\" title=\"Columbia, South Carolina\">Columbia, South Carolina</a>, is <a href=\"https://wikipedia.org/wiki/Capture_of_Columbia\" title=\"Capture of Columbia\">burned</a> as <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces flee from advancing <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> forces.", "links": [{"title": "Columbia, South Carolina", "link": "https://wikipedia.org/wiki/Columbia,_South_Carolina"}, {"title": "Capture of Columbia", "link": "https://wikipedia.org/wiki/Capture_of_Columbia"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1905", "text": "Russian Revolution of 1905: Grand Duke <PERSON> of Russia is assassinated in the Moscow Kremlin by Socialist Revolutionary <PERSON>.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Russian_Revolution_of_1905\" title=\"Russian Revolution of 1905\">Russian Revolution of 1905</a>: <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> is assassinated in the <a href=\"https://wikipedia.org/wiki/Moscow_Kremlin\" class=\"mw-redirect\" title=\"Moscow Kremlin\">Moscow Kremlin</a> by <a href=\"https://wikipedia.org/wiki/Socialist_Revolutionary_Party\" title=\"Socialist Revolutionary Party\">Socialist Revolutionary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Revolution_of_1905\" title=\"Russian Revolution of 1905\">Russian Revolution of 1905</a>: <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a> is assassinated in the <a href=\"https://wikipedia.org/wiki/Moscow_Kremlin\" class=\"mw-redirect\" title=\"Moscow Kremlin\">Moscow Kremlin</a> by <a href=\"https://wikipedia.org/wiki/Socialist_Revolutionary_Party\" title=\"Socialist Revolutionary Party\">Socialist Revolutionary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Russian Revolution of 1905", "link": "https://wikipedia.org/wiki/Russian_Revolution_of_1905"}, {"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}, {"title": "Moscow Kremlin", "link": "https://wikipedia.org/wiki/Moscow_Kremlin"}, {"title": "Socialist Revolutionary Party", "link": "https://wikipedia.org/wiki/Socialist_Revolutionary_Party"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "The Armory Show opens in New York City, displaying works of artists who are to become some of the most influential painters of the early 20th century.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Armory_Show\" title=\"Armory Show\">Armory Show</a> opens in New York City, displaying works of artists who are to become some of the most influential painters of the early 20th century.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Armory_Show\" title=\"Armory Show\">Armory Show</a> opens in New York City, displaying works of artists who are to become some of the most influential painters of the early 20th century.", "links": [{"title": "Armory Show", "link": "https://wikipedia.org/wiki/Armory_Show"}]}, {"year": "1919", "text": "The Ukrainian People's Republic asks the Entente and the United States for help fighting the Bolsheviks.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic#Winter_1918.E2.80.9319\" title=\"Ukrainian People's Republic\">Ukrainian People's Republic</a> asks the <a href=\"https://wikipedia.org/wiki/Triple_Entente\" title=\"Triple Entente\">Entente</a> and the United States for help fighting the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic#Winter_1918.E2.80.9319\" title=\"Ukrainian People's Republic\">Ukrainian People's Republic</a> asks the <a href=\"https://wikipedia.org/wiki/Triple_Entente\" title=\"Triple Entente\">Entente</a> and the United States for help fighting the <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a>.", "links": [{"title": "Ukrainian People's Republic", "link": "https://wikipedia.org/wiki/Ukrainian_People%27s_Republic#Winter_1918.E2.80.9319"}, {"title": "Triple Entente", "link": "https://wikipedia.org/wiki/Triple_Entente"}, {"title": "Bolsheviks", "link": "https://wikipedia.org/wiki/Bolsheviks"}]}, {"year": "1944", "text": "World War II: The Battle of Eniwetok begins. The battle ends in an American victory on February 22.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Eniwetok\" title=\"Battle of Eniwetok\">Battle of Eniwetok</a> begins. The battle ends in an American victory on <a href=\"https://wikipedia.org/wiki/February_22\" title=\"February 22\">February 22</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Eniwetok\" title=\"Battle of Eniwetok\">Battle of Eniwetok</a> begins. The battle ends in an American victory on <a href=\"https://wikipedia.org/wiki/February_22\" title=\"February 22\">February 22</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Eniwetok", "link": "https://wikipedia.org/wiki/Battle_of_Eniwetok"}, {"title": "February 22", "link": "https://wikipedia.org/wiki/February_22"}]}, {"year": "1944", "text": "World War II: Operation Hailstone begins: U.S. naval air, surface, and submarine attack against Truk Lagoon, Japan's main base in the central Pacific, in support of the Eniwetok invasion.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Hailstone\" title=\"Operation Hailstone\">Operation Hailstone</a> begins: U.S. naval air, surface, and submarine attack against <a href=\"https://wikipedia.org/wiki/Truk_Lagoon\" class=\"mw-redirect\" title=\"Truk Lagoon\">Truk Lagoon</a>, Japan's main base in the central Pacific, in support of the <a href=\"https://wikipedia.org/wiki/Battle_of_Eniwetok\" title=\"Battle of Eniwetok\">Eniwetok invasion</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Hailstone\" title=\"Operation Hailstone\">Operation Hailstone</a> begins: U.S. naval air, surface, and submarine attack against <a href=\"https://wikipedia.org/wiki/Truk_Lagoon\" class=\"mw-redirect\" title=\"Truk Lagoon\">Truk Lagoon</a>, Japan's main base in the central Pacific, in support of the <a href=\"https://wikipedia.org/wiki/Battle_of_Eniwetok\" title=\"Battle of Eniwetok\">Eniwetok invasion</a>.", "links": [{"title": "Operation Hailstone", "link": "https://wikipedia.org/wiki/Operation_Hailstone"}, {"title": "Truk Lagoon", "link": "https://wikipedia.org/wiki/Truk_Lagoon"}, {"title": "Battle of Eniwetok", "link": "https://wikipedia.org/wiki/Battle_of_Eniwetok"}]}, {"year": "1948", "text": "The Al-Waziri coup briefly ousts the ruling Hamidaddin dynasty of Yemen; Imam <PERSON><PERSON> is killed.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Al-Waziri_coup\" title=\"Al-Waziri coup\">Al-Waziri coup</a> briefly ousts the ruling Hamidaddin dynasty of <a href=\"https://wikipedia.org/wiki/Mutawakkilite_Kingdom_of_Yemen\" class=\"mw-redirect\" title=\"Mutawakkilite Kingdom of Yemen\">Yemen</a>; <a href=\"https://wikipedia.org/wiki/Imams_of_Yemen\" title=\"Imams of Yemen\">Imam</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Al-Waziri_coup\" title=\"Al-Waziri coup\">Al-Waziri coup</a> briefly ousts the ruling Hamidaddin dynasty of <a href=\"https://wikipedia.org/wiki/Mutawakkilite_Kingdom_of_Yemen\" class=\"mw-redirect\" title=\"Mutawakkilite Kingdom of Yemen\">Yemen</a>; <a href=\"https://wikipedia.org/wiki/Imams_of_Yemen\" title=\"Imams of Yemen\">Imam</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is killed.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> coup", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mutawakkilite Kingdom of Yemen", "link": "https://wikipedia.org/wiki/Mutawakkilite_Kingdom_of_Yemen"}, {"title": "Imams of Yemen", "link": "https://wikipedia.org/wiki/Imams_of_Yemen"}, {"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON> begins his term as the first President of Israel.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins his term as the first <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins his term as the first <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1959", "text": "Project Vanguard: Vanguard 2: The first weather satellite is launched to measure cloud-cover distribution.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Project_Vanguard\" title=\"Project Vanguard\">Project Vanguard</a>: <a href=\"https://wikipedia.org/wiki/Vanguard_2\" title=\"Vanguard 2\">Vanguard 2</a>: The first <a href=\"https://wikipedia.org/wiki/Weather_satellite\" title=\"Weather satellite\">weather satellite</a> is launched to measure cloud-cover distribution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Vanguard\" title=\"Project Vanguard\">Project Vanguard</a>: <a href=\"https://wikipedia.org/wiki/Vanguard_2\" title=\"Vanguard 2\">Vanguard 2</a>: The first <a href=\"https://wikipedia.org/wiki/Weather_satellite\" title=\"Weather satellite\">weather satellite</a> is launched to measure cloud-cover distribution.", "links": [{"title": "Project Vanguard", "link": "https://wikipedia.org/wiki/Project_Vanguard"}, {"title": "Vanguard 2", "link": "https://wikipedia.org/wiki/Vanguard_2"}, {"title": "Weather satellite", "link": "https://wikipedia.org/wiki/Weather_satellite"}]}, {"year": "1959", "text": "A Turkish Airlines Vickers Viscount crashes near Gatwick Airport, killing 14; Turkish prime minister <PERSON><PERSON> survives the crash.", "html": "1959 - A <a href=\"https://wikipedia.org/wiki/Turkish_Airlines\" title=\"Turkish Airlines\">Turkish Airlines</a> <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> <a href=\"https://wikipedia.org/wiki/1959_Turkish_Airlines_Gatwick_crash\" class=\"mw-redirect\" title=\"1959 Turkish Airlines Gatwick crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">Gatwick Airport</a>, killing 14; <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Turkish prime minister</a> <a href=\"https://wikipedia.org/wiki/Adnan_Menderes\" title=\"<PERSON><PERSON>der<PERSON>\"><PERSON><PERSON></a> survives the crash.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Turkish_Airlines\" title=\"Turkish Airlines\">Turkish Airlines</a> <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> <a href=\"https://wikipedia.org/wiki/1959_Turkish_Airlines_Gatwick_crash\" class=\"mw-redirect\" title=\"1959 Turkish Airlines Gatwick crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Gatwick_Airport\" title=\"Gatwick Airport\">Gatwick Airport</a>, killing 14; <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Turkish prime minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Menderes\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> survives the crash.", "links": [{"title": "Turkish Airlines", "link": "https://wikipedia.org/wiki/Turkish_Airlines"}, {"title": "Vickers Viscount", "link": "https://wikipedia.org/wiki/Vickers_Viscount"}, {"title": "1959 Turkish Airlines Gatwick crash", "link": "https://wikipedia.org/wiki/1959_Turkish_Airlines_Gatwick_crash"}, {"title": "Gatwick Airport", "link": "https://wikipedia.org/wiki/Gatwick_Airport"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "In <PERSON><PERSON> v. <PERSON> the Supreme Court of the United States rules that congressional districts have to be approximately equal in population.", "html": "1964 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules that <a href=\"https://wikipedia.org/wiki/United_States_congressional_district\" class=\"mw-redirect\" title=\"United States congressional district\">congressional districts</a> have to be approximately equal in population.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> v<PERSON>\"><PERSON><PERSON> v<PERSON></a></i> the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> rules that <a href=\"https://wikipedia.org/wiki/United_States_congressional_district\" class=\"mw-redirect\" title=\"United States congressional district\">congressional districts</a> have to be approximately equal in population.", "links": [{"title": "Wesberry v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_v._<PERSON>"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "United States congressional district", "link": "https://wikipedia.org/wiki/United_States_congressional_district"}]}, {"year": "1964", "text": "Gabonese president <PERSON> is toppled by a coup and his rival, <PERSON><PERSON><PERSON><PERSON><PERSON>, is installed in his place.", "html": "1964 - Gabonese president <a href=\"https://wikipedia.org/wiki/L%C3%A9on_M%27ba\" title=\"<PERSON>ba\"><PERSON></a> is toppled by <a href=\"https://wikipedia.org/wiki/1964_Gabon_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1964 Gabon coup d'état\">a coup</a> and his rival, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, is installed in his place.", "no_year_html": "Gabonese president <a href=\"https://wikipedia.org/wiki/L%C3%A9on_M%27ba\" title=\"<PERSON>ba\"><PERSON></a> is toppled by <a href=\"https://wikipedia.org/wiki/1964_Gabon_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1964 Gabon coup d'état\">a coup</a> and his rival, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, is installed in his place.", "links": [{"title": "Léon M<PERSON>ba", "link": "https://wikipedia.org/wiki/L%C3%A9on_M%27ba"}, {"title": "1964 Gabon coup d'état", "link": "https://wikipedia.org/wiki/1964_Gabon_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Project Ranger: The Ranger 8 probe launches on its mission to photograph the Mare Tranquillitatis region of the Moon in preparation for the crewed Apollo missions. Mare Tranquillitatis or the \"Sea of Tranquility\" would become the site chosen for the Apollo 11 lunar landing.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Project Ranger</a>: The <a href=\"https://wikipedia.org/wiki/Ranger_8\" title=\"Ranger 8\">Ranger 8</a> probe launches on its mission to photograph the <i><a href=\"https://wikipedia.org/wiki/Mare_Tranquillitatis\" title=\"Mare Tranquillitatis\">Mare Tranquillitatis</a></i> region of the Moon in preparation for the crewed <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> missions. <i>Mare Tranquillitatis</i> or the \"Sea of Tranquility\" would become the site chosen for the <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> lunar landing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Project Ranger</a>: The <a href=\"https://wikipedia.org/wiki/Ranger_8\" title=\"Ranger 8\">Ranger 8</a> probe launches on its mission to photograph the <i><a href=\"https://wikipedia.org/wiki/Mare_Tranquillitatis\" title=\"Mare Tranquillitatis\">Mare Tranquillitatis</a></i> region of the Moon in preparation for the crewed <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> missions. <i>Mare Tranquillitatis</i> or the \"Sea of Tranquility\" would become the site chosen for the <a href=\"https://wikipedia.org/wiki/Apollo_11\" title=\"Apollo 11\">Apollo 11</a> lunar landing.", "links": [{"title": "Ranger program", "link": "https://wikipedia.org/wiki/Ranger_program"}, {"title": "Ranger 8", "link": "https://wikipedia.org/wiki/Ranger_8"}, {"title": "Mare Tranquillitatis", "link": "https://wikipedia.org/wiki/Mare_Tranquillitatis"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11"}]}, {"year": "1966", "text": "Aeroflot Flight 065 crashes during take-off from Sheremetyevo International Airport, killing 21.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_065\" title=\"Aeroflot Flight 065\">Aeroflot Flight 065</a> crashes during take-off from <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, killing 21.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_065\" title=\"Aeroflot Flight 065\">Aeroflot Flight 065</a> crashes during take-off from <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a>, killing 21.", "links": [{"title": "Aeroflot Flight 065", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_065"}, {"title": "Sheremetyevo International Airport", "link": "https://wikipedia.org/wiki/Sheremetyevo_International_Airport"}]}, {"year": "1969", "text": "American aquanaut <PERSON> dies of carbon dioxide poisoning while attempting to repair a leak in the SEALAB III underwater habitat. The SEALAB project was subsequently abandoned.", "html": "1969 - American aquanaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Berry L<PERSON> Cannon\"><PERSON></a> dies of carbon dioxide poisoning while attempting to repair a leak in the <a href=\"https://wikipedia.org/wiki/SEALAB_III\" class=\"mw-redirect\" title=\"SEALAB III\">SEALAB III</a> underwater habitat. The SEALAB project was subsequently abandoned.", "no_year_html": "American aquanaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Berry L. Cannon\"><PERSON></a> dies of carbon dioxide poisoning while attempting to repair a leak in the <a href=\"https://wikipedia.org/wiki/SEALAB_III\" class=\"mw-redirect\" title=\"SEALAB III\">SEALAB III</a> underwater habitat. The SEALAB project was subsequently abandoned.", "links": [{"title": "<PERSON> L<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "SEALAB III", "link": "https://wikipedia.org/wiki/SEALAB_III"}]}, {"year": "1970", "text": "the family of <PERSON>, United States Army captain, is found murdered in their home in Ft. Bragg, North Carolina.  Eventually, <PERSON> himself was charged with and convicted of the murder of his pregnant wife and two daughters.", "html": "1970 - the family of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> captain, is found murdered in their home in <a href=\"https://wikipedia.org/wiki/Fort_Liberty\" class=\"mw-redirect\" title=\"Fort Liberty\">Ft. Bragg</a>, North Carolina. Eventually, <PERSON> himself was charged with and convicted of the murder of his pregnant wife and two daughters.", "no_year_html": "the family of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> captain, is found murdered in their home in <a href=\"https://wikipedia.org/wiki/Fort_Liberty\" class=\"mw-redirect\" title=\"Fort Liberty\">Ft. <PERSON></a>, North Carolina. Eventually, <PERSON> himself was charged with and convicted of the murder of his pregnant wife and two daughters.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Fort Liberty", "link": "https://wikipedia.org/wiki/Fort_Liberty"}]}, {"year": "1972", "text": "Cumulative sales of the Volkswagen Beetle exceed those of the Ford Model T.", "html": "1972 - Cumulative sales of the <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> exceed those of the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a>.", "no_year_html": "Cumulative sales of the <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> exceed those of the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a>.", "links": [{"title": "Volkswagen Beetle", "link": "https://wikipedia.org/wiki/Volkswagen_Beetle"}, {"title": "Ford Model T", "link": "https://wikipedia.org/wiki/Ford_Model_T"}]}, {"year": "1974", "text": "<PERSON>, a disgruntled U.S. Army private, buzzes the White House in a stolen helicopter.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/1974_White_House_helicopter_incident\" title=\"1974 White House helicopter incident\"><PERSON></a>, a disgruntled <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> private, buzzes the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in a stolen <a href=\"https://wikipedia.org/wiki/Helicopter\" title=\"Helicopter\">helicopter</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1974_White_House_helicopter_incident\" title=\"1974 White House helicopter incident\"><PERSON></a>, a disgruntled <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> private, buzzes the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in a stolen <a href=\"https://wikipedia.org/wiki/Helicopter\" title=\"Helicopter\">helicopter</a>.", "links": [{"title": "1974 White House helicopter incident", "link": "https://wikipedia.org/wiki/1974_White_House_helicopter_incident"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "Helicopter", "link": "https://wikipedia.org/wiki/Helicopter"}]}, {"year": "1978", "text": "The Troubles: The Provisional IRA detonates an incendiary bomb at the La Mon restaurant, near Belfast, killing 12 and seriously injuring 30 others, all Protestants.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonates an <a href=\"https://wikipedia.org/wiki/Incendiary_bomb\" class=\"mw-redirect\" title=\"Incendiary bomb\">incendiary bomb</a> at the <a href=\"https://wikipedia.org/wiki/La_Mon_restaurant_bombing\" title=\"La Mon restaurant bombing\">La Mon restaurant</a>, near Belfast, killing 12 and seriously injuring 30 others, all Protestants.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonates an <a href=\"https://wikipedia.org/wiki/Incendiary_bomb\" class=\"mw-redirect\" title=\"Incendiary bomb\">incendiary bomb</a> at the <a href=\"https://wikipedia.org/wiki/La_Mon_restaurant_bombing\" title=\"La Mon restaurant bombing\">La Mon restaurant</a>, near Belfast, killing 12 and seriously injuring 30 others, all Protestants.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Incendiary bomb", "link": "https://wikipedia.org/wiki/Incendiary_bomb"}, {"title": "La Mon restaurant bombing", "link": "https://wikipedia.org/wiki/La_Mon_restaurant_bombing"}]}, {"year": "1979", "text": "The Sino-Vietnamese War begins.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Sino-Vietnamese War</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Sino-Vietnamese War</a> begins.", "links": [{"title": "Sino-Vietnamese War", "link": "https://wikipedia.org/wiki/Sino-Vietnamese_War"}]}, {"year": "1980", "text": "First winter ascent of Mount Everest by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "html": "1980 - First winter ascent of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a> by <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON><PERSON><PERSON>_Wielicki\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_<PERSON>\" title=\"Leszek <PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "First winter ascent of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a> by <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON><PERSON><PERSON>_Wielicki\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Leszek <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Wielicki"}, {"title": "Leszek Cichy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>k_<PERSON>y"}]}, {"year": "1991", "text": "Ryan International Airlines Flight 590 crashes during takeoff from Cleveland Hopkins International Airport, killing both pilots, the aircraft's only occupants.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ryan_International_Airlines_Flight_590\" title=\"Ryan International Airlines Flight 590\">Ryan International Airlines Flight 590</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport\" title=\"Cleveland Hopkins International Airport\">Cleveland Hopkins International Airport</a>, killing both pilots, the aircraft's only occupants.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ryan_International_Airlines_Flight_590\" title=\"Ryan International Airlines Flight 590\">Ryan International Airlines Flight 590</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport\" title=\"Cleveland Hopkins International Airport\">Cleveland Hopkins International Airport</a>, killing both pilots, the aircraft's only occupants.", "links": [{"title": "Ryan International Airlines Flight 590", "link": "https://wikipedia.org/wiki/Ryan_International_Airlines_Flight_590"}, {"title": "Cleveland Hopkins International Airport", "link": "https://wikipedia.org/wiki/Cleveland_Hopkins_International_Airport"}]}, {"year": "1992", "text": "First Nagorno-Karabakh War: Armenian troops massacre more than 20 Azerbaijani civilians during the Capture of Garadaghly.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/First_Nagorno-Karabakh_War\" title=\"First Nagorno-Karabakh War\">First Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> troops massacre more than 20 <a href=\"https://wikipedia.org/wiki/Azerbaijanis\" title=\"Azerbaijanis\">Azerbaijani</a> civilians during the <a href=\"https://wikipedia.org/wiki/Capture_of_Garadaghly\" title=\"Capture of Garadaghly\">Capture of Garadaghly</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Nagorno-Karabakh_War\" title=\"First Nagorno-Karabakh War\">First Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> troops massacre more than 20 <a href=\"https://wikipedia.org/wiki/Azerbaijanis\" title=\"Azerbaijanis\">Azerbaijani</a> civilians during the <a href=\"https://wikipedia.org/wiki/Capture_of_Garadaghly\" title=\"Capture of Garadaghly\">Capture of Garadaghly</a>.", "links": [{"title": "First Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/First_Nagorno-Karabakh_War"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijanis", "link": "https://wikipedia.org/wiki/Azerbaijanis"}, {"title": "Capture of Garadaghly", "link": "https://wikipedia.org/wiki/Capture_of_Garadaghly"}]}, {"year": "1995", "text": "The Cenepa War between Peru and Ecuador ends on a ceasefire brokered by the UN.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Cenepa_War\" title=\"Cenepa War\">Cenepa War</a> between <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> and <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> ends on a <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">ceasefire</a> brokered by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cenepa_War\" title=\"Cenepa War\">Cenepa War</a> between <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> and <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> ends on a <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">ceasefire</a> brokered by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a>.", "links": [{"title": "Cenepa War", "link": "https://wikipedia.org/wiki/Cenepa_War"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Ceasefire", "link": "https://wikipedia.org/wiki/Ceasefire"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1996", "text": "In Philadelphia, world champion <PERSON> beats the Deep Blue supercomputer in a chess match.", "html": "1996 - In <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, world champion <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> beats the <a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a> <a href=\"https://wikipedia.org/wiki/Supercomputer\" title=\"Supercomputer\">supercomputer</a> in a <a href=\"https://wikipedia.org/wiki/Chess\" title=\"Chess\">chess</a> match.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, world champion <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> beats the <a href=\"https://wikipedia.org/wiki/Deep_Blue_(chess_computer)\" title=\"Deep Blue (chess computer)\">Deep Blue</a> <a href=\"https://wikipedia.org/wiki/Supercomputer\" title=\"Supercomputer\">supercomputer</a> in a <a href=\"https://wikipedia.org/wiki/Chess\" title=\"Chess\">chess</a> match.", "links": [{"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Blue (chess computer)", "link": "https://wikipedia.org/wiki/Deep_Blue_(chess_computer)"}, {"title": "Supercomputer", "link": "https://wikipedia.org/wiki/Supercomputer"}, {"title": "Chess", "link": "https://wikipedia.org/wiki/Chess"}]}, {"year": "1996", "text": "NASA's Discovery Program begins as the NEAR Shoemaker spacecraft lifts off on the first mission ever to orbit and land on an asteroid, 433 Eros.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Discovery_Program\" title=\"Discovery Program\">Discovery Program</a> begins as the <a href=\"https://wikipedia.org/wiki/NEAR_Shoemaker\" title=\"NEAR Shoemaker\">NEAR Shoemaker</a> spacecraft lifts off on the first mission ever to orbit and land on an asteroid, <a href=\"https://wikipedia.org/wiki/433_Eros\" title=\"433 Eros\">433 Eros</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Discovery_Program\" title=\"Discovery Program\">Discovery Program</a> begins as the <a href=\"https://wikipedia.org/wiki/NEAR_Shoemaker\" title=\"NEAR Shoemaker\">NEAR Shoemaker</a> spacecraft lifts off on the first mission ever to orbit and land on an asteroid, <a href=\"https://wikipedia.org/wiki/433_Eros\" title=\"433 Eros\">433 Eros</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Discovery Program", "link": "https://wikipedia.org/wiki/Discovery_Program"}, {"title": "NEAR Shoemaker", "link": "https://wikipedia.org/wiki/NEAR_Shoemaker"}, {"title": "433 <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/433_Eros"}]}, {"year": "1996", "text": "The 8.2 Mw  Biak earthquake shakes the Papua province of eastern Indonesia with a maximum Mercalli intensity of VIII (Severe). A large tsunami followed, leaving 166 people dead or missing and 423 injured.", "html": "1996 - The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1996_Biak_earthquake\" title=\"1996 Biak earthquake\">Biak earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Papua_(province)\" title=\"Papua (province)\">Papua</a> province of eastern <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A large tsunami followed, leaving 166 people dead or missing and 423 injured.", "no_year_html": "The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1996_Biak_earthquake\" title=\"1996 Biak earthquake\">Biak earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Papua_(province)\" title=\"Papua (province)\">Papua</a> province of eastern <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VIII (<i>Severe</i>). A large tsunami followed, leaving 166 people dead or missing and 423 injured.", "links": [{"title": "1996 Biak earthquake", "link": "https://wikipedia.org/wiki/1996_Biak_earthquake"}, {"title": "Papua (province)", "link": "https://wikipedia.org/wiki/Papua_(province)"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "2006", "text": "A massive mudslide occurs in Southern Leyte, Philippines; the official death toll is set at 1,126.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/2006_Southern_Leyte_mudslide\" title=\"2006 Southern Leyte mudslide\">massive mudslide</a> occurs in <a href=\"https://wikipedia.org/wiki/Southern_Leyte\" title=\"Southern Leyte\">Southern Leyte</a>, Philippines; the official death toll is set at 1,126.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2006_Southern_Leyte_mudslide\" title=\"2006 Southern Leyte mudslide\">massive mudslide</a> occurs in <a href=\"https://wikipedia.org/wiki/Southern_Leyte\" title=\"Southern Leyte\">Southern Leyte</a>, Philippines; the official death toll is set at 1,126.", "links": [{"title": "2006 Southern Leyte mudslide", "link": "https://wikipedia.org/wiki/2006_Southern_Leyte_mudslide"}, {"title": "Southern Leyte", "link": "https://wikipedia.org/wiki/Southern_Leyte"}]}, {"year": "2008", "text": "Kosovo declares independence from Serbia.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> <a href=\"https://wikipedia.org/wiki/2008_Kosovo_declaration_of_independence\" title=\"2008 Kosovo declaration of independence\">declares independence</a> from <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> <a href=\"https://wikipedia.org/wiki/2008_Kosovo_declaration_of_independence\" title=\"2008 Kosovo declaration of independence\">declares independence</a> from <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>.", "links": [{"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}, {"title": "2008 Kosovo declaration of independence", "link": "https://wikipedia.org/wiki/2008_Kosovo_declaration_of_independence"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}]}, {"year": "2011", "text": "Arab Spring: Libyan protests against <PERSON><PERSON><PERSON>'s regime begin.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>: <a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2011)\" title=\"Libyan civil war (2011)\">Libyan protests</a> against <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"History of Libya under <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>'s regime</a> begin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab_Spring\" title=\"Arab Spring\">Arab Spring</a>: <a href=\"https://wikipedia.org/wiki/Libyan_civil_war_(2011)\" title=\"Libyan civil war (2011)\">Libyan protests</a> against <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"History of Libya under <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>'s regime</a> begin.", "links": [{"title": "Arab Spring", "link": "https://wikipedia.org/wiki/Arab_Spring"}, {"title": "Libyan civil war (2011)", "link": "https://wikipedia.org/wiki/Libyan_civil_war_(2011)"}, {"title": "History of Libya under <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "Arab Spring: In Bahrain, security forces launch a deadly pre-dawn raid on protesters in Pearl Roundabout in Manama; the day is locally known as Bloody Thursday.", "html": "2011 - Arab Spring: In <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Special_Security_Force_Command\" title=\"Special Security Force Command\">security forces</a> launch a deadly <a href=\"https://wikipedia.org/wiki/Police_raid\" title=\"Police raid\">pre-dawn raid</a> on protesters in <a href=\"https://wikipedia.org/wiki/Pearl_Roundabout\" title=\"Pearl Roundabout\">Pearl Roundabout</a> in <a href=\"https://wikipedia.org/wiki/Manama\" title=\"Manama\">Manama</a>; the day is locally known as <a href=\"https://wikipedia.org/wiki/Bloody_Thursday_(Bahrain)\" title=\"Bloody Thursday (Bahrain)\">Bloody Thursday</a>.", "no_year_html": "Arab Spring: In <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Special_Security_Force_Command\" title=\"Special Security Force Command\">security forces</a> launch a deadly <a href=\"https://wikipedia.org/wiki/Police_raid\" title=\"Police raid\">pre-dawn raid</a> on protesters in <a href=\"https://wikipedia.org/wiki/Pearl_Roundabout\" title=\"Pearl Roundabout\">Pearl Roundabout</a> in <a href=\"https://wikipedia.org/wiki/Manama\" title=\"Manama\">Manama</a>; the day is locally known as <a href=\"https://wikipedia.org/wiki/Bloody_Thursday_(Bahrain)\" title=\"Bloody Thursday (Bahrain)\">Bloody Thursday</a>.", "links": [{"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}, {"title": "Special Security Force Command", "link": "https://wikipedia.org/wiki/Special_Security_Force_Command"}, {"title": "Police raid", "link": "https://wikipedia.org/wiki/Police_raid"}, {"title": "Pearl Roundabout", "link": "https://wikipedia.org/wiki/Pearl_Roundabout"}, {"title": "Manama", "link": "https://wikipedia.org/wiki/Manama"}, {"title": "Bloody Thursday (Bahrain)", "link": "https://wikipedia.org/wiki/Bloody_Thursday_(Bahrain)"}]}, {"year": "2015", "text": "Eighteen people are killed and 78 injured in a stampede at a Mardi Gras parade in Haiti.", "html": "2015 - Eighteen people are killed and 78 injured in a <a href=\"https://wikipedia.org/wiki/2015_Haiti_Carnival_stampede\" title=\"2015 Haiti Carnival stampede\">stampede</a> at a <a href=\"https://wikipedia.org/wiki/Mardi_Gras\" title=\"Mardi Gras\"><PERSON><PERSON></a> parade in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>.", "no_year_html": "Eighteen people are killed and 78 injured in a <a href=\"https://wikipedia.org/wiki/2015_Haiti_Carnival_stampede\" title=\"2015 Haiti Carnival stampede\">stampede</a> at a <a href=\"https://wikipedia.org/wiki/Mardi_Gras\" title=\"Mardi Gras\"><PERSON><PERSON></a> parade in <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>.", "links": [{"title": "2015 Haiti Carnival stampede", "link": "https://wikipedia.org/wiki/2015_Haiti_Carnival_stampede"}, {"title": "Mardi Gras", "link": "https://wikipedia.org/wiki/Mardi_<PERSON>"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}]}, {"year": "2016", "text": "Military vehicles explode outside a Turkish Armed Forces barracks in Ankara, Turkey, killing at least 29 people and injuring 61 others.", "html": "2016 - Military vehicles <a href=\"https://wikipedia.org/wiki/February_2016_Ankara_bombing\" title=\"February 2016 Ankara bombing\">explode</a> outside a <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> barracks in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey, killing at least 29 people and injuring 61 others.", "no_year_html": "Military vehicles <a href=\"https://wikipedia.org/wiki/February_2016_Ankara_bombing\" title=\"February 2016 Ankara bombing\">explode</a> outside a <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> barracks in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey, killing at least 29 people and injuring 61 others.", "links": [{"title": "February 2016 Ankara bombing", "link": "https://wikipedia.org/wiki/February_2016_Ankara_bombing"}, {"title": "Turkish Armed Forces", "link": "https://wikipedia.org/wiki/Turkish_Armed_Forces"}, {"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}]}], "Births": [{"year": "624", "text": "<PERSON>, Chinese empress consort (d. 705)", "html": "624 - <a href=\"https://wikipedia.org/wiki/Wu_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese empress consort (d. 705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese empress consort (d. 705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wu_Zetian"}]}, {"year": "1028", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Persian scholar and imam (d. 1085)", "html": "1028 - <a href=\"https://wikipedia.org/wiki/Al-Juwayni\" title=\"Al-Juwayni\"><PERSON><PERSON><PERSON><PERSON></a>, Persian scholar and imam (d. 1085)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Juwayni\" title=\"Al-Juwayni\"><PERSON><PERSON><PERSON></a>, Persian scholar and imam (d. 1085)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Juwayni"}]}, {"year": "1490", "text": "<PERSON>, duke of Bourbon (d. 1527)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON></a>, duke of Bourbon (d. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON></a>, duke of Bourbon (d. 1527)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1519", "text": "<PERSON>, French Grand Chamberlain (d. 1563)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON></a>, French Grand Chamberlain (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON></a>, French Grand Chamberlain (d. 1563)", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Guise"}]}, {"year": "1524", "text": "<PERSON>, French cardinal (d. 1574)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Lorraine\" title=\"<PERSON>, <PERSON> of Lorraine\"><PERSON></a>, French cardinal (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Lorraine\" title=\"<PERSON>, Cardinal of Lorraine\"><PERSON></a>, French cardinal (d. 1574)", "links": [{"title": "<PERSON>, <PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1646", "text": "<PERSON>, <PERSON><PERSON> <PERSON>, French economist (d. 1714)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_sieur_<PERSON>_<PERSON>\" title=\"<PERSON>, sieur <PERSON>\"><PERSON>, sieur <PERSON></a>, French economist (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_sieur_<PERSON>_<PERSON>\" title=\"<PERSON>, sieur <PERSON>\"><PERSON>, sieur <PERSON></a>, French economist (d. 1714)", "links": [{"title": "<PERSON>, <PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON><PERSON>, Italian violinist and composer (d. 1713)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian violinist and composer (d. 1713)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, German astronomer and academic (d. 1762)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "Horace<PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss physicist and meteorologist (d. 1799)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Horace-B%C3%A9n%C3%A9dict_de_Saussure\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de Saussure\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss physicist and meteorologist (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Horace-B%C3%A9n%C3%A9dict_de_Saussure\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de Saussure\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss physicist and meteorologist (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horace-B%C3%A9n%C3%A9dict_de_Saussure"}]}, {"year": "1752", "text": "<PERSON>, German author and playwright (d. 1831)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, French cartographer and explorer (d. 1803)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cartographer and explorer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cartographer and explorer (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, Scottish antiquarian, cartographer, author, numismatist and historian (d. 1826)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish antiquarian, cartographer, author, numismatist and historian (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish antiquarian, cartographer, author, numismatist and historian (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, English captain (d. 1805)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English captain (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English captain (d. 1805)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1781", "text": "<PERSON>, French physician, invented the stethoscope (d. 1826)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, invented the <a href=\"https://wikipedia.org/wiki/Stethoscope\" title=\"Stethoscope\">stethoscope</a> (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician, invented the <a href=\"https://wikipedia.org/wiki/Stethoscope\" title=\"Stethoscope\">stethoscope</a> (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>c"}, {"title": "Stethoscope", "link": "https://wikipedia.org/wiki/Stethoscope"}]}, {"year": "1796", "text": "<PERSON>, German physician and botanist (d. 1866)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and botanist (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and botanist (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON> (von) <PERSON>, German lawyer and ornithologist who visited and studied the Faroe Islands (d. 1874)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(von)_<PERSON>\" title=\"<PERSON> (von) <PERSON>\"><PERSON> (von) <PERSON></a>, German lawyer and ornithologist who visited and studied the Faroe Islands (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(von)_<PERSON>\" title=\"<PERSON> (von) <PERSON>\"><PERSON> (von) <PERSON></a>, German lawyer and ornithologist who visited and studied the Faroe Islands (d. 1874)", "links": [{"title": "<PERSON> (von) <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(von)_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, Luxembourgish jurist and politician, 7th Prime Minister of Luxembourg (d. 1904)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourgish jurist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89douard_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourgish jurist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_Thilges"}, {"title": "Prime Minister of Luxembourg", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg"}]}, {"year": "1820", "text": "<PERSON>, Belgian violinist and composer (d. 1881)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, Irish-American actress and dancer (d. 1861)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and dancer (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and dancer (d. 1861)", "links": [{"title": "Lola Montez", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American sculptor (d. 1902)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Spanish author, poet, and playwright (d. 1870)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer\" title=\"<PERSON>\"><PERSON></a>, Spanish author, poet, and playwright (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer\" title=\"<PERSON>\"><PERSON></a>, Spanish author, poet, and playwright (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9cquer"}]}, {"year": "1843", "text": "<PERSON>, American businessman, founded Montgomery Ward (d. 1913)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Aaron Montgomery Ward\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Montgomery_Ward\" title=\"Montgomery Ward\">Montgomery Ward</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Aaron Montgomery Ward\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Montgomery_Ward\" title=\"Montgomery Ward\">Montgomery Ward</a> (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Montgomery Ward", "link": "https://wikipedia.org/wiki/Montgomery_Ward"}]}, {"year": "1848", "text": "<PERSON>, Australian poet and publisher (d. 1920)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and publisher (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and publisher (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Swiss chef (d. 1903)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chef (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chef (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, German businessman (d. 1902)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON> of Waldeck and Pyrmont, duchess of Albany (d. 1922)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Waldeck_and_Pyrmont\" class=\"mw-redirect\" title=\"Princess <PERSON> of Waldeck and Pyrmont\"><PERSON> of Waldeck and Pyrmont</a>, duchess of Albany (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Waldeck_and_Pyrmont\" class=\"mw-redirect\" title=\"Princess <PERSON> of Waldeck and Pyrmont\"><PERSON> of Waldeck and Pyrmont</a>, duchess of Albany (d. 1922)", "links": [{"title": "Princess <PERSON> of Waldeck and Pyrmont", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Waldeck_and_Pyrmont"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Japanese general, author, and poet (d. 1922)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Mori_%C5%8Cgai\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general, author, and poet (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>_%C5%8Cgai\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general, author, and poet (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mori_%C5%8Cgai"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Slovak priest, botanist, and painter (d. 1929)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest, botanist, and painter (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest, botanist, and painter (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Australian journalist, author, and poet (d. 1941)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ban<PERSON> Paterson\"><PERSON><PERSON></a>, Australian journalist, author, and poet (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Paterson\"><PERSON><PERSON></a>, Australian journalist, author, and poet (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Paterson"}]}, {"year": "1874", "text": "<PERSON>, American businessman (d. 1956)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Swiss explorer and author (d. 1904)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss explorer and author (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss explorer and author (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, French sergeant and politician (d. 1932)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sergeant and politician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sergeant and politician (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Ma<PERSON>ot"}]}, {"year": "1879", "text": "<PERSON>, American educational reformer, social activist and author (d. 1958)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educational reformer, social activist and author (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educational reformer, social activist and author (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American nurse midwife, founded Frontier Nursing Service (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Nurse_midwife\" title=\"Nurse midwife\">nurse midwife</a>, founded <a href=\"https://wikipedia.org/wiki/Frontier_Nursing_Service\" title=\"Frontier Nursing Service\">Frontier Nursing Service</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Nurse_midwife\" title=\"Nurse midwife\">nurse midwife</a>, founded <a href=\"https://wikipedia.org/wiki/Frontier_Nursing_Service\" title=\"Frontier Nursing Service\">Frontier Nursing Service</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nurse midwife", "link": "https://wikipedia.org/wiki/Nurse_midwife"}, {"title": "Frontier Nursing Service", "link": "https://wikipedia.org/wiki/Frontier_Nursing_Service"}]}, {"year": "1887", "text": "<PERSON>, Luxembourgish lawyer and politician, 15th Prime Minister of Luxembourg (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourgish lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Luxembourg", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Finnish composer and critic (d. 1947)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and critic (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer and critic (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English Catholic priest (d. 1957)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German-American physicist and academic, Nobel Prize laureate (d. 1969)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Stern"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1890", "text": "<PERSON>, English-Australian statistician, biologist, and geneticist (d. 1962)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian statistician, biologist, and geneticist (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian statistician, biologist, and geneticist (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German-Israeli mathematician and academic (d. 1965)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli mathematician and academic (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli mathematician and academic (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American baseball player and journalist (d. 1965)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi-Indian poet and author (d. 1954)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi-Indian poet and author (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi-Indian poet and author (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American actress (d. 1998)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Iranian-French author and translator (d. 1951)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-French author and translator (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-French author and translator (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>at"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (d. 1984)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, known as \"<PERSON><PERSON><PERSON>\", Spanish bullfighter (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Rodr%C3%ADguez_Ortega"}]}, {"year": "1904", "text": "<PERSON>, German-American political scientist, philosopher, and academic (d. 1980)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist, philosopher, and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist, philosopher, and academic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian mathematician (d. 1977)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter"}]}, {"year": "1906", "text": "<PERSON>, American actress (d. 2002)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Chinese general and politician, Vice Premier of the People's Republic of China (d. 2007)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bo_<PERSON>"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "1910", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2005)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, German-American author, poet, and scholar (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American author, poet, and scholar (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American author, poet, and scholar (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American author (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1990)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1916", "text": "<PERSON>, Russian rugby player and pilot (d. 1940)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian rugby player and pilot (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian rugby player and pilot (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian cricketer (d. 1984)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Italian footballer and actor (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and actor (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American poet and academic (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, French mathematician (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian historian and academic (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._<PERSON><PERSON>_Careless\" title=\"J. M. <PERSON><PERSON> Careless\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Care<PERSON></a>, Canadian historian and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_Careless\" title=\"J. M. <PERSON><PERSON> Careless\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Care<PERSON></a>, Canadian historian and academic (d. 2009)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON>_Careless"}]}, {"year": "1919", "text": "<PERSON>, American actress and singer (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American tennis player (d. 1945)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Hunt\"><PERSON></a>, American tennis player (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Norwegian director and screenwriter (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian director and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian director and screenwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivo_Caprino"}]}, {"year": "1920", "text": "<PERSON>, American disability and communication disorder advocate (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disability and communication disorder advocate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disability and communication disorder advocate (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American illustrator (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American biochemist and academic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biochemist and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American R&B singer-songwriter (d. 1969)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American clarinet player and bandleader (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and bandleader (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and bandleader (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeFranco"}]}, {"year": "1924", "text": "<PERSON>, American singer and author (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English composer and conductor (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor and director (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Puerto Rican actress and singer (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese virologist (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese virologist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese virologist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chilean-French director and screenwriter", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American rabbi and author (d. 2002)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rabbi and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rabbi and author (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Baron <PERSON> of Liddesdale, English lieutenant and politician, Secretary of State for Business, Innovation and Skills (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Liddesdale\" title=\"<PERSON>, Baron <PERSON> of Liddesdale\"><PERSON>, Baron <PERSON> of Liddesdale</a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Liddesdale\" title=\"<PERSON>, Baron <PERSON> of Liddesdale\"><PERSON>, Baron <PERSON> of Liddesdale</a>, English lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills\" class=\"mw-redirect\" title=\"Secretary of State for Business, Innovation and Skills\">Secretary of State for Business, Innovation and Skills</a> (d. 1993)", "links": [{"title": "<PERSON>, Baron <PERSON> of Liddesdale", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Liddesdale"}, {"title": "Secretary of State for Business, Innovation and Skills", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Business,_Innovation_and_Skills"}]}, {"year": "1929", "text": "<PERSON>, English actress and singer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American baseball player, coach, and manager (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager (d. 2023)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1930", "text": "<PERSON>, Ukrainian-Israeli physicist and academic (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Israeli physicist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Israeli physicist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English author (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Czech actress and singer (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99ina_Jir%C3%A1skov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99ina_Jir%C3%A1skov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actress and singer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99ina_Jir%C3%A1skov%C3%A1"}]}, {"year": "1931", "text": "<PERSON>, American football coach (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ryan\"><PERSON></a>, American football coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ryan\"><PERSON></a>, American football coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American captain and politician (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor (d. 2003)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian comedian, actor, and author (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, actor, and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, actor, and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ies"}]}, {"year": "1935", "text": "<PERSON>, English-American actress", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player and actor (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American model and actress, Miss America 1959 (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1959\" title=\"Miss America 1959\">Miss America 1959</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress, <a href=\"https://wikipedia.org/wiki/Miss_America_1959\" title=\"Miss America 1959\">Miss America 1959</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Miss America 1959", "link": "https://wikipedia.org/wiki/Miss_America_1959"}]}, {"year": "1940", "text": "<PERSON>, Mexican singer-songwriter, actor, and producer (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, actor, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, actor, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Fern%C3%A1ndez"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2006)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actress, singer, and director", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American activist, co-founded the Black Panther Party (d. 1989)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther Party</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}]}, {"year": "1944", "text": "<PERSON>, Welsh saxophonist, keyboard player, and composer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh saxophonist, keyboard player, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh saxophonist, keyboard player, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American actress, dancer, and choreographer (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, dancer, and choreographer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, dancer, and choreographer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Irish actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>icker"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Iranian-American author and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Shahrnush_Parsipur\" title=\"Shahrnush Parsipur\"><PERSON>rnush Parsipur</a>, Iranian-American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shahrnush_Parsipur\" title=\"Shahrnush Parsipur\"><PERSON><PERSON><PERSON> Parsipur</a>, Iranian-American author and academic", "links": [{"title": "Shahrnush Parsipur", "link": "https://wikipedia.org/wiki/Shahrnush_Parsipur"}]}, {"year": "1948", "text": "<PERSON>, Mexican singer-songwriter, producer, and actor (d. 2019)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, producer, and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter, producer, and actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9"}]}, {"year": "1949", "text": "<PERSON>, English guitarist and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and coach (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Green\"><PERSON></a>, American football player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Pakistani soldier and pilot (d. 1971)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani soldier and pilot (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani soldier and pilot (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>-<PERSON><PERSON>, German gymnast and physician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, German gymnast and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, German gymnast and physician", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B%C3%<PERSON><PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech musician (d. 1991)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Padr%C5%AFn%C4%9Bk\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech musician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Padr%C5%AFn%C4%9Bk\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech musician (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Padr%C5%AFn%C4%9Bk"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Israeli basketball player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Chinese author and academic, Nobel Prize laureate", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yan\"><PERSON></a>, Chinese author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Yan\"><PERSON></a>, Chinese author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1956", "text": "<PERSON>, American actor and game show host", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter, accordion player, and pianist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter, accordion player, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter, accordion player, and pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Moroccan-Israeli rabbi and politician, Israeli Minister of Internal Affairs", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan-Israeli rabbi and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Israel)\" title=\"Ministry of Interior (Israel)\">Israeli Minister of Internal Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan-Israeli rabbi and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Israel)\" title=\"Ministry of Interior (Israel)\">Israeli Minister of Internal Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "Ministry of Interior (Israel)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Israel)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American swimmer and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American swimmer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Canadian hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uff\"><PERSON><PERSON></a>, Canadian hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uff\"><PERSON><PERSON></a>, Canadian hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English politician, Shadow Leader of the House of Commons", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Leader of the House of Commons", "link": "https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons"}]}, {"year": "1961", "text": "<PERSON>, English politician, Shadow Secretary of State for Defence", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Eagle\" title=\"Maria Eagle\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Eagle\" title=\"Maria Eagle\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Eagle"}, {"title": "Shadow Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Russian anthropologist, historian, and sociologist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian anthropologist, historian, and sociologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian anthropologist, historian, and sociologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Phillips"}]}, {"year": "1963", "text": "<PERSON> the <PERSON>, American comedian and voice actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cable_Guy\" title=\"<PERSON> the Cable Guy\"><PERSON> the Cable Guy</a>, American comedian and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cable_Guy\" title=\"<PERSON> the Cable Guy\"><PERSON> the Cable Guy</a>, American comedian and voice actor", "links": [{"title": "<PERSON> the <PERSON> Guy", "link": "https://wikipedia.org/wiki/<PERSON>_the_Cable_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English mountaineer (d. 1995)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Taiwanese-American businessman, co-founded Nvidia", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Nvidia\" title=\"Nvidia\">Nvidia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Nvidia\" title=\"Nvidia\">Nvidia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Nvidia", "link": "https://wikipedia.org/wiki/Nvidia"}]}, {"year": "1963", "text": "<PERSON>, American basketball player, executive, and businessman", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, executive, and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, executive, and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Canadian gymnast (d. 1991)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>w<PERSON>\" title=\"Sherry <PERSON>wco\"><PERSON><PERSON></a>, Canadian gymnast (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>w<PERSON>\" title=\"Sherry <PERSON>wco\"><PERSON><PERSON></a>, Canadian gymnast (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>wco"}]}, {"year": "1965", "text": "<PERSON>, American director and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Michael_Bay\" title=\"Michael Bay\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Michael_Bay\" title=\"Michael Bay\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michael_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player, manager, and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, manager, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, manager, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luc_Robitaille"}]}, {"year": "1968", "text": "<PERSON>, Italian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese journalist and activist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Wu%27erkaixi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wu%27erkaixi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chinese journalist and activist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wu%27erkaixi"}]}, {"year": "1969", "text": "<PERSON>, French martial artist and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French martial artist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French martial artist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Russian handball player (d. 2017)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian handball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian handball player (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-Australian actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American model and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter, guitarist, actor, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French figure skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and musician (d. 2022)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Argentine model and businesswoman", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vale<PERSON>_Ma<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine model and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vale<PERSON>_Ma<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine model and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valeria_Mazza"}]}, {"year": "1973", "text": "<PERSON>, American basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Serbian footballer (d. 2018)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Goran_Bunjev%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goran_Bunjev%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Goran_Bunjev%C4%8Devi%C4%87"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, French rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Iba%C3%B1ez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rapha%C3%ABl_Iba%C3%B1ez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rapha%C3%ABl_Iba%C3%B1ez"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese guitarist, songwriter, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Japanese guitarist, songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1974", "text": "<PERSON>, American actor, director, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jerry_O%27Connell"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player (d. 2012)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ta%C5%<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaspars_Asta%C5%A1enko"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Prospal\" title=\"V<PERSON><PERSON><PERSON> Prospal\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A1clav_Prospal\" title=\"V<PERSON><PERSON><PERSON> Prospal\"><PERSON><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A1clav_Prospal"}]}, {"year": "1976", "text": "<PERSON>, American actress and model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English actor and playwright", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Conrad_<PERSON>mora"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Israeli footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ban"}]}, {"year": "1981", "text": "<PERSON>, American actor, director, and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American model, media personality, actress, singer, DJ, author and businesswoman", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Paris_Hilton\" title=\"Paris Hilton\"><PERSON> Hilton</a>, American model, media personality, actress, singer, DJ, author and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paris_Hilton\" title=\"Paris Hilton\">Paris Hilton</a>, American model, media personality, actress, singer, DJ, author and businesswoman", "links": [{"title": "Paris Hilton", "link": "https://wikipedia.org/wiki/Paris_Hilton"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Swedish footballer (d. 2014)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Pontus_Segerstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pont<PERSON>_Segerstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pontus_Segerstr%C3%B6m"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_February_1982)\" title=\"<PERSON><PERSON> (footballer, born February 1982)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_February_1982)\" title=\"<PERSON><PERSON> (footballer, born February 1982)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born February 1982)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_February_1982)"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South African cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/AB_de_Villiers\" title=\"AB de Villiers\"><PERSON> de Villiers</a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AB_de_Villiers\" title=\"AB de Villiers\">AB de Villiers</a>, South African cricketer", "links": [{"title": "AB de Villiers", "link": "https://wikipedia.org/wiki/AB_de_Villiers"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Polish basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1984", "text": "<PERSON>, Australian 3.0 point wheelchair basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/3.0_point_player\" class=\"mw-redirect\" title=\"3.0 point player\">3.0 point</a> <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/3.0_point_player\" class=\"mw-redirect\" title=\"3.0 point player\">3.0 point</a> <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}, {"title": "3.0 point player", "link": "https://wikipedia.org/wiki/3.0_point_player"}, {"title": "Wheelchair basketball", "link": "https://wikipedia.org/wiki/Wheelchair_basketball"}]}, {"year": "1985", "text": "<PERSON>, Norwegian ski jumper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)\" title=\"<PERSON> (ski jumper)\"><PERSON></a>, Norwegian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)\" title=\"<PERSON> (ski jumper)\"><PERSON></a>, Norwegian ski jumper", "links": [{"title": "<PERSON> (ski jumper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ski_jumper)"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Brazilian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/%C3%8Dsis_Valverde\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%8Dsis_Valverde\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%8Dsis_Valverde"}]}, {"year": "1988", "text": "<PERSON>, Czech ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>um", "link": "https://wikipedia.org/wiki/Case_Keenum"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Ukrainian boxer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American actor and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Chord_Overstreet\" title=\"Chord Overstreet\"><PERSON><PERSON> Overstreet</a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chord_Overstreet\" title=\"Chord Overstreet\"><PERSON><PERSON> Overstreet</a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>reet", "link": "https://wikipedia.org/wiki/Chord_Overstreet"}]}, {"year": "1990", "text": "<PERSON>, Canadian speed skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marianne_St-Gelais"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Bosnian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Edin_Vi%C5%A1%C4%87a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edin_Vi%C5%A1%C4%87a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edin_Vi%C5%A1%C4%87a"}]}, {"year": "1991", "text": "<PERSON>, American basketball player and coach", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ey"}]}, {"year": "1991", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English actress, filmmaker, and activist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, filmmaker, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, filmmaker, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Spanish motorcycle racer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_M%C3%A1rquez"}]}, {"year": "1994", "text": "<PERSON>, American ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Madison_Keys\" title=\"Madison Keys\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madison_Keys\" title=\"Madison Keys\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Madison_Keys"}]}, {"year": "1996", "text": "<PERSON>, Swedish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1996)\" title=\"<PERSON> (ice hockey, born 1996)\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1996)\" title=\"<PERSON> (ice hockey, born 1996)\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1996)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1996)"}]}, {"year": "1996", "text": "<PERSON>, South African-American actress and singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Zeki_%C3%87elik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeki_%C3%87elik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeki_%C3%87elik"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "364", "text": "<PERSON><PERSON>, Roman emperor (b. 331)", "html": "364 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, Roman emperor (b. 331)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, Roman emperor (b. 331)", "links": [{"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "440", "text": "<PERSON><PERSON><PERSON>, Armenian monk, linguist, and theologian (b. 360)", "html": "440 - <a href=\"https://wikipedia.org/wiki/Mesrop_Mashtots\" title=\"Mesrop Mashtots\"><PERSON><PERSON><PERSON></a>, Armenian monk, linguist, and theologian (b. 360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mesrop_Mashto<PERSON>\" title=\"Mesrop Mashtots\"><PERSON><PERSON><PERSON></a>, Armenian monk, linguist, and theologian (b. 360)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mesrop_Ma<PERSON>ts"}]}, {"year": "923", "text": "<PERSON><PERSON><PERSON><PERSON>, Persian scholar (b. 839)", "html": "923 - <a href=\"https://wikipedia.org/wiki/Al-Tabari\" title=\"Al-Tabari\"><PERSON><PERSON><PERSON></a>, Persian scholar (b. 839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Tabari\" title=\"Al-Tabari\"><PERSON><PERSON>bari</a>, Persian scholar (b. 839)", "links": [{"title": "Al-Tabari", "link": "https://wikipedia.org/wiki/Al-Tabari"}]}, {"year": "1178", "text": "<PERSON><PERSON><PERSON> of Ratzeburg, bishop of Ratzeburg", "html": "1178 - <a href=\"https://wikipedia.org/wiki/Evermode_of_Ratzeburg\" title=\"Evermode of Ratzeburg\">Evermode of Ratzeburg</a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Ratzeburg\" class=\"mw-redirect\" title=\"Bishop of Ratzeburg\">bishop of Ratzeburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evermode_of_Ratzeburg\" title=\"Evermode of Ratzeburg\">Evermode of Ratzeburg</a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Ratzeburg\" class=\"mw-redirect\" title=\"Bishop of Ratzeburg\">bishop of Ratzeburg</a>", "links": [{"title": "Evermode of Ratzeburg", "link": "https://wikipedia.org/wiki/Evermode_of_Ratzeburg"}, {"title": "Bishop of Ratzeburg", "link": "https://wikipedia.org/wiki/<PERSON>_of_Ratzeburg"}]}, {"year": "1220", "text": "<PERSON><PERSON><PERSON>, Duke of Lorraine", "html": "1220 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON>, Duke of Lorraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Lorraine\"><PERSON><PERSON><PERSON>, Duke of Lorraine</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Lorraine"}]}, {"year": "1339", "text": "<PERSON>, Duke of Austria (b. 1301)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1301)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria"}]}, {"year": "1371", "text": "<PERSON> of Bulgaria", "html": "1371 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "links": [{"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria"}]}, {"year": "1500", "text": "<PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst, German noble (b. before 1463)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Oldenburg-Delmenhorst\" title=\"<PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst\"><PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst</a>, German noble (b. before 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Oldenburg-Delmenhorst\" title=\"<PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst\"><PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst</a>, German noble (b. before 1463)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Oldenburg-Delmenhorst", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Count_of_Oldenburg-Delmenhorst"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mathematician, astronomer, and philosopher (b. 1548)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician, astronomer, and philosopher (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician, astronomer, and philosopher (b. 1548)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON><PERSON>, Grand Duke of Tuscany (b. 1549)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>, Grand Duke of Tuscany</a> (b. 1549)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_de%27_Medici"}]}, {"year": "1624", "text": "<PERSON>, Spanish priest and historian (b. 1536)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Juan_de_Mariana\" title=\"Juan <PERSON> Mariana\"><PERSON></a>, Spanish priest and historian (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_<PERSON>_Mariana\" title=\"Juan de Mariana\"><PERSON></a>, Spanish priest and historian (b. 1536)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1659", "text": "<PERSON>, French politician, French Minister of Finance (b. 1593)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)\" class=\"mw-redirect\" title=\"Minister of the Economy, Finances and Industry (France)\">French Minister of Finance</a> (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)\" class=\"mw-redirect\" title=\"Minister of the Economy, Finances and Industry (France)\">French Minister of Finance</a> (b. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of the Economy, Finances and Industry (France)", "link": "https://wikipedia.org/wiki/Minister_of_the_Economy,_Finances_and_Industry_(France)"}]}, {"year": "1673", "text": "<PERSON><PERSON><PERSON>, French actor and playwright (b. 1622)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/Moli%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and playwright (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moli%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and playwright (b. 1622)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moli%C3%A8re"}]}, {"year": "1680", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English politician (b. 1599)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1599)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Dutch biologist, zoologist, and entomologist (b. 1637)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch biologist, zoologist, and entomologist (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch biologist, zoologist, and entomologist (b. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dam"}]}, {"year": "1715", "text": "<PERSON>, French orientalist and archaeologist (b. 1646)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and archaeologist (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and archaeologist (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, French organist and composer (b. 1669)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1768", "text": "<PERSON>, English lawyer and politician, Speaker of the House of Commons (b. 1691)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">Speaker of the House of Commons</a> (b. 1691)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1841", "text": "<PERSON><PERSON>, Italian guitarist and composer (b. 1770)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian guitarist and composer (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian guitarist and composer (b. 1770)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Puerto Rican political activist, the first woman Independentista in the island (b. 1773)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Barbudo\" title=\"María de las Mercedes Barbudo\"><PERSON> Mercedes Barbudo</a>, Puerto Rican political activist, the first woman <i>Independentista</i> in the island (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Barbudo\" title=\"María de las Mercedes Barbudo\"><PERSON> Mercedes Barbudo</a>, Puerto Rican political activist, the first woman <i>Independentista</i> in the island (b. 1773)", "links": [{"title": "María de las Mercedes Barbudo", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Barbudo"}]}, {"year": "1854", "text": "<PERSON>, English painter, engraver, and illustrator (b. 1789)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter, engraver, and illustrator (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter, engraver, and illustrator (b. 1789)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(painter)"}]}, {"year": "1856", "text": "<PERSON>, German journalist and poet (b. 1797)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and poet (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and poet (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino priests:\n<PERSON> (b. 1837)\n<PERSON> (b. 1799)\n<PERSON><PERSON><PERSON><PERSON> (b. 1835)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Gomburza\" title=\"Gomburza\">Go<PERSON>ur<PERSON></a>, Filipino priests:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1837)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a> (b. 1799)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Jac<PERSON>o_Zamora\" title=\"Jac<PERSON>o <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1835)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gomburza\" title=\"Gomburza\">Gomburza</a>, Filipino priests:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_B<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1837)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a> (b. 1799)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Jac<PERSON>o_Zamora\" title=\"<PERSON><PERSON><PERSON>o <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1835)</li>\n</ul>", "links": [{"title": "Gomburza", "link": "https://wikipedia.org/wiki/Gomburza"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Burgos"}, {"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacinto_Zamora"}]}, {"year": "<PERSON> (b. 1837)", "text": null, "html": "<PERSON> (b. 1837) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Jos%C3%A9_B<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Jos%C3%A9_Burgos\" title=\"<PERSON>\"><PERSON></a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Jos%C3%A9_Burgos"}]}, {"year": "<PERSON> (b. 1799)", "text": null, "html": "<PERSON> (b. 1799) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a> (b. 1799)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON> (b. 1835)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON> (b. 1835) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/J<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/J<PERSON><PERSON>o_Zamora"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Belgian astronomer, mathematician, and sociologist (b. 1796)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian astronomer, mathematician, and sociologist (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian astronomer, mathematician, and sociologist (b. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American publisher and politician (b. 1819)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sholes\"><PERSON></a>, American publisher and politician (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "Grand Duke <PERSON> of Russia, fifth son and seventh child of Tsar <PERSON> (b. 1857)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>, fifth son and seventh child of <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> II</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\">Grand Duke <PERSON> of Russia</a>, fifth son and seventh child of <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> II</a> (b. 1857)", "links": [{"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}, {"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}]}, {"year": "1905", "text": "<PERSON>, English-American religious leader, leader in the Latter Day Saint movement (b. 1815)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader, leader in the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American religious leader, leader in the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Latter Day Saint movement", "link": "https://wikipedia.org/wiki/Latter_Day_Saint_movement"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, American tribal leader (b. 1829)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Welsh sailor and explorer (b. 1876)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sailor and explorer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sailor and explorer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 7th Prime Minister of Canada (b. 1841)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Finnish composer (b. 1868)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish composer (b. 1868)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON> of Belgium (b. 1875)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"<PERSON> of Belgium\"><PERSON> of Belgium</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"<PERSON> of Belgium\"><PERSON> of Belgium</a> (b. 1875)", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Belgium"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, German chess player and theoretician (b. 1862)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chess player and theoretician (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chess player and theoretician (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German violinist and educator (b. 1859)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(violinist)\" title=\"<PERSON> (violinist)\"><PERSON></a>, German violinist and educator (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(violinist)\" title=\"<PERSON> (violinist)\"><PERSON></a>, German violinist and educator (b. 1859)", "links": [{"title": "<PERSON> (violinist)", "link": "https://wikipedia.org/wiki/<PERSON>_(violinist)"}]}, {"year": "1946", "text": "<PERSON>, American actress and singer (b. 1889)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Imam of Yemen (b. 1904)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>Din\"><PERSON><PERSON> ed-Din</a>, <a href=\"https://wikipedia.org/wiki/Imams_of_Yemen\" title=\"Imams of Yemen\">Imam of Yemen</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> ed-Din\"><PERSON><PERSON> ed-Din</a>, <a href=\"https://wikipedia.org/wiki/Imams_of_Yemen\" title=\"Imams of Yemen\">Imam of Yemen</a> (b. 1904)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Imams of Yemen", "link": "https://wikipedia.org/wiki/Imams_of_Yemen"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish physician and politician, Turkish Minister of Health (b. 1887)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/L%C3%BCtfi_K%C4%B1rdar\" title=\"<PERSON>ü<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%BCtfi_K%C4%B1rdar\" title=\"<PERSON>ü<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BCtfi_K%C4%B1rdar"}, {"title": "Ministry of Health (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American actress (b. 1894)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Naldi"}]}, {"year": "1962", "text": "<PERSON>, American actor (b. 1907)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German-American pianist, composer, and conductor (b. 1876)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist, composer, and conductor (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist, composer, and conductor (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German-American painter (b. 1880)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American aquanaut (b. 1935)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Berry L<PERSON> Cannon\"><PERSON> <PERSON></a>, American aquanaut (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Berry L<PERSON> Cannon\"><PERSON></a>, American aquanaut (b. 1935)", "links": [{"title": "<PERSON> L<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Israeli novelist, short story writer, and poet, Nobel Prize laureate (b. 1888)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli novelist, short story writer, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli novelist, short story writer, and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1970", "text": "<PERSON>, American composer and conductor (b. 1900)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" class=\"mw-redirect\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and conductor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" class=\"mw-redirect\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and conductor (b. 1900)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1972", "text": "<PERSON> <PERSON><PERSON>, American race car driver (b. 1935)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Friday_<PERSON><PERSON>\" title=\"Friday Hassler\">Friday <PERSON><PERSON></a>, American race car driver (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Friday_<PERSON><PERSON>\" title=\"Friday <PERSON>sler\">Friday <PERSON><PERSON></a>, American race car driver (b. 1935)", "links": [{"title": "Friday <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Friday_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Ugandan archbishop and saint (b. 1922)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan archbishop and saint (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan archbishop and saint (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor (b. 1905)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American baseball player and umpire (b. 1922)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Nestor_<PERSON>\" title=\"Nestor Chyla<PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nest<PERSON>_<PERSON>\" title=\"Nestor <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and umpire (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_<PERSON><PERSON>k"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American pianist and composer (b. 1917)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/The<PERSON><PERSON>_Monk\" title=\"Thelonious Monk\"><PERSON><PERSON><PERSON> Monk</a>, American pianist and composer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The<PERSON><PERSON>_Monk\" title=\"Thelon<PERSON> Monk\"><PERSON><PERSON><PERSON> Monk</a>, American pianist and composer (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and director (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Indian-American philosopher and author (b. 1895)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamurti\"><PERSON><PERSON><PERSON></a>, Indian-American philosopher and author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamu<PERSON>i\"><PERSON><PERSON><PERSON></a>, Indian-American philosopher and author (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>i"}]}, {"year": "1988", "text": "<PERSON>, English archaeologist and scholar (b. 1923)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and scholar (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and scholar (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian educator and politician, 11th Chief Minister of Bihar (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>i_Thakur\" title=\"Karp<PERSON><PERSON> Thakur\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educator and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Bihar\" class=\"mw-redirect\" title=\"List of Chief Ministers of Bihar\">Chief Minister of Bihar</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Thakur\" title=\"Ka<PERSON><PERSON><PERSON> Thakur\"><PERSON><PERSON><PERSON><PERSON></a>, Indian educator and politician, 11th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Bihar\" class=\"mw-redirect\" title=\"List of Chief Ministers of Bihar\">Chief Minister of Bihar</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_Thakur"}, {"title": "List of Chief Ministers of Bihar", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Bihar"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American baseball player (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French mountaineer, skier, and pilot (b. 1951)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mountaineer, skier, and pilot (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mountaineer, skier, and pilot (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American journalist and author (b. 1951)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, German soldier, philosopher, and author (b. 1895)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, philosopher, and author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, philosopher, and author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_J%C3%BCnger"}]}, {"year": "2003", "text": "<PERSON>, American baseball player (b. 1979)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Mexican lawyer and politician, 51st President of Mexico (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_L%C3%B3pez_Portillo"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "2005", "text": "<PERSON>, Irish-American actor (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Herlihy\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Herlihy\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_O%27Herlihy"}]}, {"year": "2005", "text": "<PERSON>, Argentinian footballer and manager (b. 1935)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Omar_S%C3%ADvori\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Omar_S%C3%ADvori\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Omar_S%C3%ADvori"}]}, {"year": "2006", "text": "<PERSON>, American drummer (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Chilean bullfighter and journalist (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Conchita_Cintr%C3%B3n\" title=\"Conchita Cintrón\"><PERSON><PERSON><PERSON></a>, Chilean bullfighter and journalist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conchita_Cintr%C3%B3n\" title=\"Conchita Cintrón\"><PERSON><PERSON><PERSON></a>, Chilean bullfighter and journalist (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Conchita_Cintr%C3%B3n"}]}, {"year": "2010", "text": "<PERSON>, American actress and singer (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English engineer and politician, Shadow Chancellor of the Exchequer (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer\" title=\"Shadow Chancellor of the Exchequer\">Shadow Chancellor of the Exchequer</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer\" title=\"Shadow Chancellor of the Exchequer\">Shadow Chancellor of the Exchequer</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Shadow Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and bass player (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American singer-songwriter and bass player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American singer-songwriter and bass player (b. 1943)", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>(bassist)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Dutch mathematician and theorist (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and theorist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician and theorist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, German-American psychologist and academic (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American psychologist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American psychologist and academic (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English actor (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli singer-songwriter and actor (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli singer-songwriter and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli singer-songwriter and actor (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1975)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>c<PERSON>ready\" title=\"<PERSON><PERSON>read<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>y\" title=\"<PERSON><PERSON>read<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_M<PERSON><PERSON>y"}]}, {"year": "2014", "text": "<PERSON>, American guitarist, keyboard player, and producer (b. 1952)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, keyboard player, and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, keyboard player, and producer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German politician and diplomat, President of the United Nations General Assembly (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician and diplomat, <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician and diplomat, <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United Nations General Assembly", "link": "https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly"}]}, {"year": "2014", "text": "<PERSON>, Jamaican singer (b. 1965)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Jamaican singer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Jamaican singer (b. 1965)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2015", "text": "<PERSON>, American-Canadian football player and manager (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON> (Canadian football)\"><PERSON></a>, American-Canadian football player and manager (b. 1935)", "links": [{"title": "<PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Dutch politician (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1928)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese general and pilot (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and pilot (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and pilot (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Trinidadian cricketer (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Egyptian journalist (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian journalist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, French historian, author, and journalist (b. 1949)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and journalist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, author, and journalist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American baseball player (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Polish film director (b. 1940)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82awski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish film director (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82awski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish film director (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%BBu%C5%82<PERSON>ki"}]}, {"year": "2017", "text": "<PERSON>, American politician (b. 1923)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American Roman Catholic theologian (b. 1933)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic theologian (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic theologian (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American talk show host and author (b. 1951)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and author (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host and author (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Tanzanian politician (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tanzanian politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Tanzanian politician (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Sri Lankan politician (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Jayawickrama_Perera\" title=\"<PERSON><PERSON><PERSON>wick<PERSON>era\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>wickrama_Perera\" title=\"<PERSON><PERSON><PERSON> Jayawick<PERSON> Perera\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gamini_Jayawickrama_Perera"}]}, {"year": "2025", "text": "<PERSON><PERSON><PERSON>, Mexican singer, songwriter and actress (b. 1947)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Pa<PERSON><PERSON>_la_del_Barrio\" title=\"Paqui<PERSON> la del Barrio\"><PERSON><PERSON><PERSON> del Barrio</a>, Mexican singer, songwriter and actress (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_la_del_Barrio\" title=\"Pa<PERSON><PERSON> la del Barrio\"><PERSON><PERSON><PERSON> del Barrio</a>, Mexican singer, songwriter and actress (b. 1947)", "links": [{"title": "Paquita la del Barrio", "link": "https://wikipedia.org/wiki/Paquita_la_del_Barrio"}]}, {"year": "2025", "text": "<PERSON><PERSON>, Dutch politician (b. 1933)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Frits_Bolkestein\" title=\"Frits Bolkestein\"><PERSON><PERSON></a>, Dutch politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Bolkestein\" title=\"Frits Bolkestein\"><PERSON><PERSON></a>, Dutch politician (b. 1933)", "links": [{"title": "Frits Bolkestein", "link": "https://wikipedia.org/wiki/Frits_Bolkestein"}]}, {"year": "2025", "text": "<PERSON>, English drummer, songwriter, and producer (b. 1955)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, Australian blood plasma donor (b. 1936)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)\" title=\"<PERSON> (blood donor)\"><PERSON></a>, Australian blood plasma donor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)\" title=\"<PERSON> (blood donor)\"><PERSON></a>, Australian blood plasma donor (b. 1936)", "links": [{"title": "<PERSON> (blood donor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blood_donor)"}]}]}}