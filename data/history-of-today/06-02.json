{"date": "June 2", "url": "https://wikipedia.org/wiki/June_2", "data": {"Events": [{"year": "455", "text": "Sack of Rome: Vandals enter Rome, and plunder the city for two weeks.", "html": "455 - <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(455)\" title=\"Sack of Rome (455)\">Sack of Rome</a>: <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a> enter Rome, and plunder the city for two weeks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(455)\" title=\"Sack of Rome (455)\">Sack of Rome</a>: <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a> enter Rome, and plunder the city for two weeks.", "links": [{"title": "Sack of Rome (455)", "link": "https://wikipedia.org/wiki/Sack_of_Rome_(455)"}, {"title": "Vandals", "link": "https://wikipedia.org/wiki/Vandals"}]}, {"year": "1098", "text": "First Crusade: The first Siege of Antioch ends as Crusader forces take the city; the second siege began five days later.", "html": "1098 - <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: The first <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch\" title=\"Siege of Antioch\">Siege of Antioch</a> ends as Crusader forces take the city; the second siege began five days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: The first <a href=\"https://wikipedia.org/wiki/Siege_of_Antioch\" title=\"Siege of Antioch\">Siege of Antioch</a> ends as Crusader forces take the city; the second siege began five days later.", "links": [{"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "Siege of Antioch", "link": "https://wikipedia.org/wiki/Siege_of_Antioch"}]}, {"year": "1608", "text": "The Colony of Virginia gets a charter, extending borders from \"sea to sea\".", "html": "1608 - The <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a> gets a charter, extending borders from \"sea to sea\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a> gets a charter, extending borders from \"sea to sea\".", "links": [{"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}]}, {"year": "1615", "text": "The first Récollet missionaries arrive at Quebec City, from Rouen, France.", "html": "1615 - The first <a href=\"https://wikipedia.org/wiki/R%C3%A9collet\" class=\"mw-redirect\" title=\"Récollet\"><PERSON><PERSON><PERSON>llet</a> <a href=\"https://wikipedia.org/wiki/Missionaries\" class=\"mw-redirect\" title=\"Missionaries\">missionaries</a> arrive at <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a>, from <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a>, France.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/R%C3%A9collet\" class=\"mw-redirect\" title=\"Récollet\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Missionaries\" class=\"mw-redirect\" title=\"Missionaries\">missionaries</a> arrive at <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a>, from <a href=\"https://wikipedia.org/wiki/Rouen\" title=\"Rouen\">Rouen</a>, France.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9collet"}, {"title": "Missionaries", "link": "https://wikipedia.org/wiki/Missionaries"}, {"title": "Quebec City", "link": "https://wikipedia.org/wiki/Quebec_City"}, {"title": "Rouen", "link": "https://wikipedia.org/wiki/Rouen"}]}, {"year": "1676", "text": "Franco-Dutch War: France ensured the supremacy of its naval fleet for the remainder of the war with its victory in the Battle of Palermo.", "html": "1676 - <a href=\"https://wikipedia.org/wiki/Franco-Dutch_War\" title=\"Franco-Dutch War\">Franco-Dutch War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> ensured the supremacy of its naval fleet for the remainder of the war with its victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Palermo\" title=\"Battle of Palermo\">Battle of Palermo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Dutch_War\" title=\"Franco-Dutch War\">Franco-Dutch War</a>: <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> ensured the supremacy of its naval fleet for the remainder of the war with its victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Palermo\" title=\"Battle of Palermo\">Battle of Palermo</a>.", "links": [{"title": "Franco-Dutch War", "link": "https://wikipedia.org/wiki/Franco-Dutch_War"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Battle of Palermo", "link": "https://wikipedia.org/wiki/Battle_of_Palermo"}]}, {"year": "1692", "text": "<PERSON> is the first person to be tried for witchcraft in Salem, Massachusetts; she was found guilty the same day and hanged on June 10.", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first person to be <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">tried for witchcraft</a> in <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, Massachusetts</a>; she was found guilty the same day and hanged on June 10.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first person to be <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">tried for witchcraft</a> in <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, Massachusetts</a>; she was found guilty the same day and hanged on June 10.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}, {"title": "Salem, Massachusetts", "link": "https://wikipedia.org/wiki/Salem,_Massachusetts"}]}, {"year": "1763", "text": "Pontiac's Rebellion: At what is now Mackinaw City, Michigan, Chippewas capture Fort Michilimackinac by diverting the garrison's attention with a game of lacrosse, then chasing a ball into the fort.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Pontiac%27s_Rebellion\" class=\"mw-redirect\" title=\"Pontiac's Rebellion\">Pontiac's Rebellion</a>: At what is now <a href=\"https://wikipedia.org/wiki/Mackinaw_City,_Michigan\" title=\"Mackinaw City, Michigan\">Mackinaw City, Michigan</a>, <a href=\"https://wikipedia.org/wiki/Chippewa\" class=\"mw-redirect\" title=\"Chippewa\">Chippewas</a> capture <a href=\"https://wikipedia.org/wiki/Fort_Michilimackinac\" title=\"Fort Michilimackinac\">Fort Michilimackinac</a> by diverting the garrison's attention with a game of <a href=\"https://wikipedia.org/wiki/Lacrosse\" title=\"Lacrosse\">lacrosse</a>, then chasing a ball into the fort.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pontiac%27s_Rebellion\" class=\"mw-redirect\" title=\"Pontiac's Rebellion\">Pontiac's Rebellion</a>: At what is now <a href=\"https://wikipedia.org/wiki/Mackinaw_City,_Michigan\" title=\"Mackinaw City, Michigan\">Mackinaw City, Michigan</a>, <a href=\"https://wikipedia.org/wiki/Chippewa\" class=\"mw-redirect\" title=\"Chippewa\">Chippewas</a> capture <a href=\"https://wikipedia.org/wiki/Fort_Michilimackinac\" title=\"Fort Michilimackinac\">Fort Michilimackinac</a> by diverting the garrison's attention with a game of <a href=\"https://wikipedia.org/wiki/Lacrosse\" title=\"Lacrosse\">lacrosse</a>, then chasing a ball into the fort.", "links": [{"title": "Pontiac's Rebellion", "link": "https://wikipedia.org/wiki/Pontiac%27s_Rebellion"}, {"title": "Mackinaw City, Michigan", "link": "https://wikipedia.org/wiki/Mackinaw_City,_Michigan"}, {"title": "Chippewa", "link": "https://wikipedia.org/wiki/Chippewa"}, {"title": "Fort Michilimackinac", "link": "https://wikipedia.org/wiki/Fort_Michilimackinac"}, {"title": "Lacrosse", "link": "https://wikipedia.org/wiki/Lacrosse"}]}, {"year": "1774", "text": "Intolerable Acts: The Quartering Act of 1774 is enacted, allowing a governor in colonial America to house British soldiers in uninhabited houses, outhouses, barns, or other buildings if suitable quarters are not provided.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Intolerable_Acts\" title=\"Intolerable Acts\">Intolerable Acts</a>: The <a href=\"https://wikipedia.org/wiki/Quartering_Act\" class=\"mw-redirect\" title=\"Quartering Act\">Quartering Act of 1774</a> is enacted, allowing a governor in <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">colonial America</a> to house British soldiers in uninhabited houses, outhouses, barns, or other buildings if suitable quarters are not provided.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Intolerable_Acts\" title=\"Intolerable Acts\">Intolerable Acts</a>: The <a href=\"https://wikipedia.org/wiki/Quartering_Act\" class=\"mw-redirect\" title=\"Quartering Act\">Quartering Act of 1774</a> is enacted, allowing a governor in <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">colonial America</a> to house British soldiers in uninhabited houses, outhouses, barns, or other buildings if suitable quarters are not provided.", "links": [{"title": "Intolerable Acts", "link": "https://wikipedia.org/wiki/Intolerable_Acts"}, {"title": "Quartering Act", "link": "https://wikipedia.org/wiki/Quartering_Act"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}]}, {"year": "1780", "text": "The anti-Catholic Gordon Riots in London leave an estimated 300 to 700 people dead.", "html": "1780 - The anti-Catholic <a href=\"https://wikipedia.org/wiki/Gordon_Riots\" title=\"Gordon Riots\">Gordon Riots</a> in London leave an estimated 300 to 700 people dead.", "no_year_html": "The anti-Catholic <a href=\"https://wikipedia.org/wiki/Gordon_Riots\" title=\"Gordon Riots\">Gordon Riots</a> in London leave an estimated 300 to 700 people dead.", "links": [{"title": "Gordon Riots", "link": "https://wikipedia.org/wiki/Gordon_Riots"}]}, {"year": "1793", "text": "French Revolution: <PERSON>, leader of the Parisian National Guard, arrests 22 Girondists selected by <PERSON><PERSON><PERSON>, setting the stage for the Reign of Terror.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON>ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the Parisian National Guard, arrests 22 <a href=\"https://wikipedia.org/wiki/Girondist\" class=\"mw-redirect\" title=\"Girondist\">Girondists</a> selected by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, setting the stage for the <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON>ois_Han<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the Parisian National Guard, arrests 22 <a href=\"https://wikipedia.org/wiki/Girondist\" class=\"mw-redirect\" title=\"Girondist\">Girondists</a> selected by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, setting the stage for the <a href=\"https://wikipedia.org/wiki/Reign_of_Terror\" title=\"Reign of Terror\">Reign of Terror</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>riot"}, {"title": "Girondist", "link": "https://wikipedia.org/wiki/Girondist"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Reign of Terror", "link": "https://wikipedia.org/wiki/Reign_of_Terror"}]}, {"year": "1805", "text": "Napoleonic Wars: A Franco-Spanish fleet recaptures from the British the island of Diamond Rock, which guards the entrance to the bay leading to Fort-de-France, Martinique.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: A <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\"><PERSON></a>-<a href=\"https://wikipedia.org/wiki/Enlightenment_in_Spain\" title=\"Enlightenment in Spain\">Spanish</a> fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Diamond_Rock\" title=\"Battle of Diamond Rock\">recaptures from the British</a> the island of <a href=\"https://wikipedia.org/wiki/Diamond_Rock\" title=\"Diamond Rock\">Diamond Rock</a>, which guards the entrance to the bay leading to <a href=\"https://wikipedia.org/wiki/Fort-de-France\" title=\"Fort-de-France\">Fort-de-France</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: A <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\"><PERSON></a>-<a href=\"https://wikipedia.org/wiki/Enlightenment_in_Spain\" title=\"Enlightenment in Spain\">Spanish</a> fleet <a href=\"https://wikipedia.org/wiki/Battle_of_Diamond_Rock\" title=\"Battle of Diamond Rock\">recaptures from the British</a> the island of <a href=\"https://wikipedia.org/wiki/Diamond_Rock\" title=\"Diamond Rock\">Diamond Rock</a>, which guards the entrance to the bay leading to <a href=\"https://wikipedia.org/wiki/Fort-de-France\" title=\"Fort-de-France\">Fort-de-France</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\"><PERSON><PERSON></a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}, {"title": "Enlightenment in Spain", "link": "https://wikipedia.org/wiki/Enlightenment_in_Spain"}, {"title": "Battle of Diamond Rock", "link": "https://wikipedia.org/wiki/Battle_of_Diamond_Rock"}, {"title": "Diamond Rock", "link": "https://wikipedia.org/wiki/Diamond_Rock"}, {"title": "Fort-de-France", "link": "https://wikipedia.org/wiki/Fort-de-France"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1848", "text": "The Slavic Congress opens in Prague.", "html": "1848 - The <a href=\"https://wikipedia.org/wiki/Prague_Slavic_Congress,_1848\" title=\"Prague Slavic Congress, 1848\">Slavic Congress</a> opens in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prague_Slavic_Congress,_1848\" title=\"Prague Slavic Congress, 1848\">Slavic Congress</a> opens in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>.", "links": [{"title": "Prague Slavic Congress, 1848", "link": "https://wikipedia.org/wiki/Prague_Slavic_Congress,_1848"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1866", "text": "The Fenians defeat Canadian forces at Ridgeway and Fort Erie, but the raids end soon after.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/Fenian_Brotherhood\" title=\"Fenian Brotherhood\">Fenians</a> defeat Canadian forces at <a href=\"https://wikipedia.org/wiki/Battle_of_Ridgeway\" title=\"Battle of Ridgeway\">Ridgeway</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Erie_(1866)\" title=\"Battle of Fort Erie (1866)\">Fort Erie</a>, but the raids end soon after.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fenian_Brotherhood\" title=\"Fenian Brotherhood\">Fenians</a> defeat Canadian forces at <a href=\"https://wikipedia.org/wiki/Battle_of_Ridgeway\" title=\"Battle of Ridgeway\">Ridgeway</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Erie_(1866)\" title=\"Battle of Fort Erie (1866)\">Fort Erie</a>, but the raids end soon after.", "links": [{"title": "Fenian Brotherhood", "link": "https://wikipedia.org/wiki/Fenian_Brotherhood"}, {"title": "Battle of Ridgeway", "link": "https://wikipedia.org/wiki/Battle_of_Ridgeway"}, {"title": "Battle of Fort Erie (1866)", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Erie_(1866)"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> applies for a patent for his wireless telegraph.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> applies for a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for his <a href=\"https://wikipedia.org/wiki/Wireless_telegraphy\" title=\"Wireless telegraphy\">wireless telegraph</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> applies for a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for his <a href=\"https://wikipedia.org/wiki/Wireless_telegraphy\" title=\"Wireless telegraphy\">wireless telegraph</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Wireless telegraphy", "link": "https://wikipedia.org/wiki/Wireless_telegraphy"}]}, {"year": "1909", "text": "<PERSON> becomes Prime Minister of Australia for the third time.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> for the third time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> for the third time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1910", "text": "<PERSON>, a co-founder of Rolls-Royce Limited, becomes the first man to make a non-stop double crossing of the English Channel by plane.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a co-founder of <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a>, becomes the first man to make a non-stop double crossing of the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> by plane.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Rolls\"><PERSON></a>, a co-founder of <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a>, becomes the first man to make a non-stop double crossing of the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> by plane.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rolls-Royce Limited", "link": "https://wikipedia.org/wiki/Rolls-Royce_Limited"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}]}, {"year": "1919", "text": "Anarchists simultaneously set off bombs in eight separate U.S. cities.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">Anarchists</a> simultaneously <a href=\"https://wikipedia.org/wiki/1919_United_States_anarchist_bombings\" title=\"1919 United States anarchist bombings\">set off bombs</a> in eight separate U.S. cities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anarchism\" title=\"Anarchism\">Anarchists</a> simultaneously <a href=\"https://wikipedia.org/wiki/1919_United_States_anarchist_bombings\" title=\"1919 United States anarchist bombings\">set off bombs</a> in eight separate U.S. cities.", "links": [{"title": "Anarchism", "link": "https://wikipedia.org/wiki/Anarchism"}, {"title": "1919 United States anarchist bombings", "link": "https://wikipedia.org/wiki/1919_United_States_anarchist_bombings"}]}, {"year": "1924", "text": "U.S. President <PERSON> signs the Indian Citizenship Act into law, granting citizenship to all Native Americans born within the territorial limits of the United States.", "html": "1924 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Indian_Citizenship_Act\" title=\"Indian Citizenship Act\">Indian Citizenship Act</a> into law, granting <a href=\"https://wikipedia.org/wiki/Citizenship\" title=\"Citizenship\">citizenship</a> to all <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a> born within the territorial limits of the United States.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Indian_Citizenship_Act\" title=\"Indian Citizenship Act\">Indian Citizenship Act</a> into law, granting <a href=\"https://wikipedia.org/wiki/Citizenship\" title=\"Citizenship\">citizenship</a> to all <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native Americans</a> born within the territorial limits of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Indian Citizenship Act", "link": "https://wikipedia.org/wiki/Indian_Citizenship_Act"}, {"title": "Citizenship", "link": "https://wikipedia.org/wiki/Citizenship"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}]}, {"year": "1941", "text": "World War II: German paratroopers murder Greek civilians in the villages of Kondomari and Alikianos.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German <a href=\"https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)\" class=\"mw-redirect\" title=\"<PERSON>chi<PERSON>jäger (World War II)\">paratroopers</a> murder Greek civilians in the villages of <a href=\"https://wikipedia.org/wiki/Massacre_of_Kondomari\" title=\"Massacre of Kondomari\">Kondomari</a> and <a href=\"https://wikipedia.org/wiki/Alikianos_executions\" title=\"Alikianos executions\">Alikianos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German <a href=\"https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>jäger (World War II)\">paratroopers</a> murder Greek civilians in the villages of <a href=\"https://wikipedia.org/wiki/Massacre_of_Kondomari\" title=\"Massacre of Kondomari\">Kondomari</a> and <a href=\"https://wikipedia.org/wiki/Alikianos_executions\" title=\"Alikianos executions\">Alikianos</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (World War II)", "link": "https://wikipedia.org/wiki/Fallschirmj%C3%A4ger_(World_War_II)"}, {"title": "Massacre of Kondomari", "link": "https://wikipedia.org/wiki/Massacre_of_Kondomari"}, {"title": "Aliki<PERSON>s executions", "link": "https://wikipedia.org/wiki/Alikianos_executions"}]}, {"year": "1946", "text": "Birth of the Italian Republic: In a referendum, Italians vote to turn Italy from a monarchy into a Republic. After the referendum, King <PERSON><PERSON> of Italy is exiled.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Italian_constitutional_referendum,_1946\" class=\"mw-redirect\" title=\"Italian constitutional referendum, 1946\">Birth of the Italian Republic</a>: In a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italians</a> vote to turn Italy from a <a href=\"https://wikipedia.org/wiki/Monarchy\" title=\"Monarchy\">monarchy</a> into a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">Republic</a>. After the referendum, King <a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II of Italy</a> is exiled.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italian_constitutional_referendum,_1946\" class=\"mw-redirect\" title=\"Italian constitutional referendum, 1946\">Birth of the Italian Republic</a>: In a <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italians</a> vote to turn Italy from a <a href=\"https://wikipedia.org/wiki/Monarchy\" title=\"Monarchy\">monarchy</a> into a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">Republic</a>. After the referendum, King <a href=\"https://wikipedia.org/wiki/Umberto_II_of_Italy\" title=\"Umberto II of Italy\">Umberto II of Italy</a> is exiled.", "links": [{"title": "Italian constitutional referendum, 1946", "link": "https://wikipedia.org/wiki/Italian_constitutional_referendum,_1946"}, {"title": "Referendum", "link": "https://wikipedia.org/wiki/Referendum"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}, {"title": "Monarchy", "link": "https://wikipedia.org/wiki/Monarchy"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "<PERSON>berto II of Italy", "link": "https://wikipedia.org/wiki/Umberto_II_of_Italy"}]}, {"year": "1953", "text": "The coronation of Queen <PERSON> at Westminster Abbey becomes the first British coronation and one of the first major international events to be televised.", "html": "1953 - The <a href=\"https://wikipedia.org/wiki/Coronation_of_Queen_<PERSON>_II\" class=\"mw-redirect\" title=\"Coronation of Queen <PERSON> II\">coronation of Queen <PERSON> II</a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> becomes the first British coronation and one of the first major international events to be televised.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coronation_of_Queen_<PERSON>_II\" class=\"mw-redirect\" title=\"Coronation of Queen <PERSON>\">coronation of Queen <PERSON> II</a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a> becomes the first British coronation and one of the first major international events to be televised.", "links": [{"title": "Coronation of Queen <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_Queen_<PERSON>_II"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1955", "text": "The USSR and Yugoslavia sign the Belgrade declaration and thus normalize relations between the two countries, discontinued since 1948.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a> and <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> sign the <a href=\"https://wikipedia.org/wiki/Belgrade_declaration\" title=\"Belgrade declaration\">Belgrade declaration</a> and thus normalize relations between the two countries, discontinued since <a href=\"https://wikipedia.org/wiki/1948\" title=\"1948\">1948</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a> and <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> sign the <a href=\"https://wikipedia.org/wiki/Belgrade_declaration\" title=\"Belgrade declaration\">Belgrade declaration</a> and thus normalize relations between the two countries, discontinued since <a href=\"https://wikipedia.org/wiki/1948\" title=\"1948\">1948</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "Belgrade declaration", "link": "https://wikipedia.org/wiki/Belgrade_declaration"}, {"title": "1948", "link": "https://wikipedia.org/wiki/1948"}]}, {"year": "1962", "text": "During the FIFA World Cup, police had to intervene multiple times in fights between Chilean and Italian players in one of the most violent games in football history.", "html": "1962 - During the <a href=\"https://wikipedia.org/wiki/1962_FIFA_World_Cup\" title=\"1962 FIFA World Cup\">FIFA World Cup</a>, police had to intervene multiple times in fights between <a href=\"https://wikipedia.org/wiki/Chile_national_football_team\" title=\"Chile national football team\">Chilean</a> and <a href=\"https://wikipedia.org/wiki/Italy_national_football_team\" title=\"Italy national football team\">Italian</a> players in <a href=\"https://wikipedia.org/wiki/Battle_of_Santiago_(1962_FIFA_World_Cup)\" title=\"Battle of Santiago (1962 FIFA World Cup)\">one of the most violent games</a> in <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> history.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/1962_FIFA_World_Cup\" title=\"1962 FIFA World Cup\">FIFA World Cup</a>, police had to intervene multiple times in fights between <a href=\"https://wikipedia.org/wiki/Chile_national_football_team\" title=\"Chile national football team\">Chilean</a> and <a href=\"https://wikipedia.org/wiki/Italy_national_football_team\" title=\"Italy national football team\">Italian</a> players in <a href=\"https://wikipedia.org/wiki/Battle_of_Santiago_(1962_FIFA_World_Cup)\" title=\"Battle of Santiago (1962 FIFA World Cup)\">one of the most violent games</a> in <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> history.", "links": [{"title": "1962 FIFA World Cup", "link": "https://wikipedia.org/wiki/1962_FIFA_World_Cup"}, {"title": "Chile national football team", "link": "https://wikipedia.org/wiki/Chile_national_football_team"}, {"title": "Italy national football team", "link": "https://wikipedia.org/wiki/Italy_national_football_team"}, {"title": "Battle of Santiago (1962 FIFA World Cup)", "link": "https://wikipedia.org/wiki/Battle_of_Santiago_(1962_FIFA_World_Cup)"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}]}, {"year": "1964", "text": "The Palestine Liberation Organization (PLO) is formed.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> (PLO) is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> (PLO) is formed.", "links": [{"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}]}, {"year": "1966", "text": "Surveyor program: Surveyor 1 lands in Oceanus Procellarum on the Moon, becoming the first U.S. spacecraft to soft-land on another world.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor program</a>: <i><a href=\"https://wikipedia.org/wiki/Surveyor_1\" title=\"Surveyor 1\">Surveyor 1</a></i> lands in <a href=\"https://wikipedia.org/wiki/Oceanus_Procellarum\" title=\"Oceanus Procellarum\">Oceanus Procellarum</a> on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>, becoming the first U.S. <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to soft-land on another world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor program</a>: <i><a href=\"https://wikipedia.org/wiki/Surveyor_1\" title=\"Surveyor 1\">Surveyor 1</a></i> lands in <a href=\"https://wikipedia.org/wiki/Oceanus_Procellarum\" title=\"Oceanus Procellarum\">Oceanus Procellarum</a> on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>, becoming the first U.S. <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> to soft-land on another world.", "links": [{"title": "Surveyor program", "link": "https://wikipedia.org/wiki/Surveyor_program"}, {"title": "Surveyor 1", "link": "https://wikipedia.org/wiki/Surveyor_1"}, {"title": "Oceanus Procellarum", "link": "https://wikipedia.org/wiki/Oceanus_Procellarum"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}]}, {"year": "1967", "text": "<PERSON> is executed in Colorado's gas chamber, in the last pre-Furman execution in the United States.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_Colorado\" title=\"Capital punishment in Colorado\">Colorado</a>'s <a href=\"https://wikipedia.org/wiki/Gas_chamber\" title=\"Gas chamber\">gas chamber</a>, in the last <a href=\"https://wikipedia.org/wiki/Furman_v._Georgia\" title=\"Furman v. Georgia\">pre-<i><PERSON>rman</i></a> execution in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mass_murderer)\" title=\"<PERSON> (mass murderer)\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_Colorado\" title=\"Capital punishment in Colorado\">Colorado</a>'s <a href=\"https://wikipedia.org/wiki/Gas_chamber\" title=\"Gas chamber\">gas chamber</a>, in the last <a href=\"https://wikipedia.org/wiki/Furman_v._Georgia\" title=\"Furman v. Georgia\">pre-<i><PERSON>rman</i></a> execution in the United States.", "links": [{"title": "<PERSON> (mass murderer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mass_murderer)"}, {"title": "Capital punishment in Colorado", "link": "https://wikipedia.org/wiki/Capital_punishment_in_Colorado"}, {"title": "Gas chamber", "link": "https://wikipedia.org/wiki/Gas_chamber"}, {"title": "<PERSON><PERSON> v. Georgia", "link": "https://wikipedia.org/wiki/Furman_v._Georgia"}]}, {"year": "1967", "text": "Protests in West Berlin against the arrival of the Shah of Iran are brutally suppressed, during which <PERSON><PERSON> is killed by a police officer. His death results in the founding of the terrorist group Movement 2 June.", "html": "1967 - Protests in <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">West Berlin</a> against the arrival of the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\">Shah</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> are brutally suppressed, during which <a href=\"https://wikipedia.org/wiki/Benno_Ohnesorg\" class=\"mw-redirect\" title=\"Benno Ohnesorg\"><PERSON><PERSON></a> is killed by a police officer. His death results in the founding of the <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> group <a href=\"https://wikipedia.org/wiki/Movement_2_June\" class=\"mw-redirect\" title=\"Movement 2 June\">Movement 2 June</a>.", "no_year_html": "Protests in <a href=\"https://wikipedia.org/wiki/West_Berlin\" title=\"West Berlin\">West Berlin</a> against the arrival of the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> are brutally suppressed, during which <a href=\"https://wikipedia.org/wiki/Benno_Ohnesorg\" class=\"mw-redirect\" title=\"Benno Ohnesorg\"><PERSON><PERSON></a> is killed by a police officer. His death results in the founding of the <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist</a> group <a href=\"https://wikipedia.org/wiki/Movement_2_June\" class=\"mw-redirect\" title=\"Movement 2 June\">Movement 2 June</a>.", "links": [{"title": "West Berlin", "link": "https://wikipedia.org/wiki/West_Berlin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shah"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benno_Ohnesorg"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "Movement 2 June", "link": "https://wikipedia.org/wiki/Movement_2_June"}]}, {"year": "1979", "text": "Pope <PERSON> starts his first official visit to his native Poland, becoming the first Pope to visit a Communist country.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> starts his first official visit to his native Poland, becoming the first <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Pope\">Pope</a> to visit a <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> starts his first official visit to his native Poland, becoming the first <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">Pope</a> to visit a <a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">Communist</a> country.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}]}, {"year": "1983", "text": "After an emergency landing because of an in-flight fire, twenty-three passengers aboard Air Canada Flight 797 are killed when a flashover occurs as the plane's doors open. Because of this incident, numerous new safety regulations are put in place.", "html": "1983 - After an emergency landing because of an in-flight fire, twenty-three passengers aboard <a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_797\" title=\"Air Canada Flight 797\">Air Canada Flight 797</a> are killed when a <a href=\"https://wikipedia.org/wiki/Flashover\" title=\"Flashover\">flashover</a> occurs as the plane's doors open. Because of this incident, numerous new safety regulations are put in place.", "no_year_html": "After an emergency landing because of an in-flight fire, twenty-three passengers aboard <a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_797\" title=\"Air Canada Flight 797\">Air Canada Flight 797</a> are killed when a <a href=\"https://wikipedia.org/wiki/Flashover\" title=\"Flashover\">flashover</a> occurs as the plane's doors open. Because of this incident, numerous new safety regulations are put in place.", "links": [{"title": "Air Canada Flight 797", "link": "https://wikipedia.org/wiki/Air_Canada_Flight_797"}, {"title": "Flashover", "link": "https://wikipedia.org/wiki/Flashover"}]}, {"year": "1990", "text": "The Lower Ohio Valley tornado outbreak spawns 66 confirmed tornadoes in Illinois, Indiana, Kentucky, and Ohio, killing 12.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/June_1990_Lower_Ohio_Valley_tornado_outbreak\" title=\"June 1990 Lower Ohio Valley tornado outbreak\">Lower Ohio Valley tornado outbreak</a> spawns 66 confirmed tornadoes in <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>, <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a>, and <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, killing 12.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/June_1990_Lower_Ohio_Valley_tornado_outbreak\" title=\"June 1990 Lower Ohio Valley tornado outbreak\">Lower Ohio Valley tornado outbreak</a> spawns 66 confirmed tornadoes in <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a>, <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a>, and <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, killing 12.", "links": [{"title": "June 1990 Lower Ohio Valley tornado outbreak", "link": "https://wikipedia.org/wiki/June_1990_Lower_Ohio_Valley_tornado_outbreak"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}, {"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}]}, {"year": "1997", "text": "In Denver, <PERSON> is convicted on 15 counts of murder and conspiracy for his role in the 1995 bombing of the Alfred P. Murrah Federal Building in Oklahoma City, in which 168 people died. He was executed four years later.", "html": "1997 - In <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted on 15 counts of murder and <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> for his role in the <a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">bombing</a> of the <a href=\"https://wikipedia.org/wiki/Alfred_P._Murrah_Federal_Building\" title=\"Alfred P. Murrah Federal Building\">Alfred P. Murrah Federal Building</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a>, in which 168 people died. He was executed four years later.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted on 15 counts of murder and <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> for his role in the <a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">bombing</a> of the <a href=\"https://wikipedia.org/wiki/Alfred_P._Murrah_Federal_Building\" title=\"Alfred P. Murrah Federal Building\">Alfred P. Murrah Federal Building</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma_City\" title=\"Oklahoma City\">Oklahoma City</a>, in which 168 people died. He was executed four years later.", "links": [{"title": "Denver", "link": "https://wikipedia.org/wiki/Denver"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Conspiracy (criminal)", "link": "https://wikipedia.org/wiki/Conspiracy_(criminal)"}, {"title": "1995", "link": "https://wikipedia.org/wiki/1995"}, {"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}, {"title": "<PERSON> Federal Building", "link": "https://wikipedia.org/wiki/Alfred_<PERSON>_Murrah_Federal_Building"}, {"title": "Oklahoma City", "link": "https://wikipedia.org/wiki/Oklahoma_City"}]}, {"year": "1998", "text": "Space Shuttle Discovery is launched on STS-91, the final mission of the Shuttle-Mir program.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-91\" title=\"STS-91\">STS-91</a>, the final mission of the <a href=\"https://wikipedia.org/wiki/Shuttle%E2%80%93Mir_program\" title=\"Shuttle-Mir program\">Shuttle-Mir program</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-91\" title=\"STS-91\">STS-91</a>, the final mission of the <a href=\"https://wikipedia.org/wiki/Shuttle%E2%80%93Mir_program\" title=\"Shuttle-Mir program\">Shuttle-Mir program</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-91", "link": "https://wikipedia.org/wiki/STS-91"}, {"title": "Shuttle-Mir program", "link": "https://wikipedia.org/wiki/Shuttle%E2%80%93Mir_program"}]}, {"year": "2003", "text": "Europe launches its first voyage to another planet, Mars. The European Space Agency's Mars Express probe launches from the Baikonur space center in Kazakhstan.", "html": "2003 - Europe launches its first voyage to another planet, <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>. The <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>'s <i><a href=\"https://wikipedia.org/wiki/Mars_Express\" title=\"Mars Express\">Mars Express</a></i> probe launches from the <a href=\"https://wikipedia.org/wiki/Baikonur\" title=\"Baikonur\">Baikonur</a> space center in <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "no_year_html": "Europe launches its first voyage to another planet, <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>. The <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>'s <i><a href=\"https://wikipedia.org/wiki/Mars_Express\" title=\"Mars Express\">Mars Express</a></i> probe launches from the <a href=\"https://wikipedia.org/wiki/Baikonur\" title=\"Baikonur\">Baikonur</a> space center in <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "links": [{"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}, {"title": "Mars Express", "link": "https://wikipedia.org/wiki/Mars_Express"}, {"title": "Baikonur", "link": "https://wikipedia.org/wiki/Baikonur"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}, {"year": "2012", "text": "Former Egyptian President <PERSON><PERSON><PERSON> is sentenced to life imprisonment for his role in the killing of demonstrators during the 2011 Egyptian revolution.", "html": "2012 - Former <a href=\"https://wikipedia.org/wiki/Egyptian_President\" class=\"mw-redirect\" title=\"Egyptian President\">Egyptian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sentenced to <a href=\"https://wikipedia.org/wiki/Life_imprisonment\" title=\"Life imprisonment\">life imprisonment</a> for his role in the killing of demonstrators during the <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">2011 Egyptian revolution</a>.", "no_year_html": "Former <a href=\"https://wikipedia.org/wiki/Egyptian_President\" class=\"mw-redirect\" title=\"Egyptian President\">Egyptian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sentenced to <a href=\"https://wikipedia.org/wiki/Life_imprisonment\" title=\"Life imprisonment\">life imprisonment</a> for his role in the killing of demonstrators during the <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">2011 Egyptian revolution</a>.", "links": [{"title": "Egyptian President", "link": "https://wikipedia.org/wiki/Egyptian_President"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}, {"title": "Life imprisonment", "link": "https://wikipedia.org/wiki/Life_imprisonment"}, {"title": "2011 Egyptian revolution", "link": "https://wikipedia.org/wiki/2011_Egyptian_revolution"}]}, {"year": "2014", "text": "Telangana officially becomes the 29th state of India, formed from ten districts of northwestern Andhra Pradesh.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Telangana\" title=\"Telangana\">Telangana</a> officially <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh_Reorganisation_Act,_2014\" title=\"Andhra Pradesh Reorganisation Act, 2014\">becomes</a> the 29th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from ten districts of northwestern <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Telangana\" title=\"Telangana\">Telangana</a> officially <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh_Reorganisation_Act,_2014\" title=\"Andhra Pradesh Reorganisation Act, 2014\">becomes</a> the 29th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from ten districts of northwestern <a href=\"https://wikipedia.org/wiki/Andhra_Pradesh\" title=\"Andhra Pradesh\">Andhra Pradesh</a>.", "links": [{"title": "Telangana", "link": "https://wikipedia.org/wiki/Telangana"}, {"title": "Andhra Pradesh Reorganisation Act, 2014", "link": "https://wikipedia.org/wiki/Andhra_Pradesh_Reorganisation_Act,_2014"}, {"title": "States of India", "link": "https://wikipedia.org/wiki/States_of_India"}, {"title": "Andhra Pradesh", "link": "https://wikipedia.org/wiki/Andhra_Pradesh"}]}, {"year": "2022", "text": "Following a request from Ankara, the United Nations officially changed the name of the Republic of Turkey in the organization from what was previously known as \"Turkey\" to \"Türkiye.\"", "html": "2022 - Following a request from <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> officially changed the name of the <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Republic of Turkey</a> in the organization from what was previously known as \"Turkey\" to \"<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Türkiye</a>.\"", "no_year_html": "Following a request from <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> officially changed the name of the <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Republic of Turkey</a> in the organization from what was previously known as \"Turkey\" to \"<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Türkiye</a>.\"", "links": [{"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "2023", "text": "A collision between two passenger trains and a parked freight train near the city of Balasor, Odisha in eastern India, results in 296 deaths and more than 1,200 people injured.", "html": "2023 - A <a href=\"https://wikipedia.org/wiki/2023_Odisha_train_collision\" title=\"2023 Odisha train collision\">collision between two passenger trains and a parked freight train</a> near the city of <a href=\"https://wikipedia.org/wiki/Ba<PERSON>or\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Odisha\" title=\"Odisha\">Odisha</a> in eastern <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, results in 296 deaths and more than 1,200 people injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2023_Odisha_train_collision\" title=\"2023 Odisha train collision\">collision between two passenger trains and a parked freight train</a> near the city of <a href=\"https://wikipedia.org/wiki/Balasor\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Odisha\" title=\"Odisha\">Odisha</a> in eastern <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, results in 296 deaths and more than 1,200 people injured.", "links": [{"title": "2023 Odisha train collision", "link": "https://wikipedia.org/wiki/2023_Odisha_train_collision"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>or"}, {"title": "Odisha", "link": "https://wikipedia.org/wiki/Odisha"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}], "Births": [{"year": "1305", "text": "<PERSON>, ruler of Ilkhanate (d. 1335)", "html": "1305 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sa%27id_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ruler of Ilkhanate (d. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abu_Sa%27<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ruler of Ilkhanate (d. 1335)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sa%27id_<PERSON>_<PERSON>"}]}, {"year": "1423", "text": "<PERSON> of Naples (d. 1494)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1494)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples"}]}, {"year": "1489", "text": "<PERSON>, Duke of Vendôme (d. 1537)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Vend%C3%B4me\" title=\"<PERSON>, Duke of Vendôme\"><PERSON>, Duke of Vendôme</a> (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Vend%C3%B4me\" title=\"<PERSON>, Duke of Vendôme\"><PERSON>, Duke of Vendôme</a> (d. 1537)", "links": [{"title": "<PERSON>, Duke of Vendôme", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Vend%C3%B4me"}]}, {"year": "1535", "text": "<PERSON> (d. 1605)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> (d. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>"}]}, {"year": "1602", "text": "<PERSON>, Count of East Frisia, Ruler of East Frisia (d. 1628)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON>, Count of East Frisia\"><PERSON>, Count of East Frisia</a>, Ruler of East Frisia (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON>, Count of East Frisia\"><PERSON>, Count of East Frisia</a>, Ruler of East Frisia (d. 1628)", "links": [{"title": "<PERSON>, Count of East Frisia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_East_Frisia"}]}, {"year": "1621", "text": "<PERSON><PERSON><PERSON>, Courland-born soldier in Swedish service (d. 1693)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Courland-born soldier in Swedish service (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Courland-born soldier in Swedish service (d. 1693)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "(baptized) <PERSON>, Dutch painter (d. 1649)", "html": "1621 - (baptized) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1649)", "no_year_html": "(baptized) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON>, 2nd Earl of Clarendon (d. 1709)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Clarendon\" title=\"<PERSON>, 2nd Earl of Clarendon\"><PERSON>, 2nd Earl of Clarendon</a> (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Clarendon\" title=\"<PERSON>, 2nd Earl of Clarendon\"><PERSON>, 2nd Earl of Clarendon</a> (d. 1709)", "links": [{"title": "<PERSON>, 2nd Earl of Clarendon", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Clarendon"}]}, {"year": "1644", "text": "<PERSON>, English medical writer (d. 1713)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English medical writer (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English medical writer (d. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON>, First Lady of the United States (d. 1802)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/Martha_Washington\" title=\"Martha Washington\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martha_Washington\" title=\"Martha Washington\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martha_Washington"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1739", "text": "<PERSON><PERSON><PERSON>, American colonel and politician, 45th Deputy Governor of Rhode Island (d. 1815)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island\" class=\"mw-redirect\" title=\"Deputy Governor of Rhode Island\">Deputy Governor of Rhode Island</a> (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel and politician, 45th <a href=\"https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island\" class=\"mw-redirect\" title=\"Deputy Governor of Rhode Island\">Deputy Governor of Rhode Island</a> (d. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Deputy_Governor_of_Rhode_Island"}]}, {"year": "1740", "text": "<PERSON>, French philosopher and politician (d. 1814)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Marquis_<PERSON>_<PERSON>\" title=\"Marquis <PERSON>\">Marquis <PERSON></a>, French philosopher and politician (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marquis_<PERSON>_<PERSON>\" title=\"Marquis <PERSON>\">Marquis <PERSON></a>, French philosopher and politician (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, Italian occultist and explorer (d. 1795)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian occultist and explorer (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian occultist and explorer (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON> of Roanoke, American planter and politician, 8th United States Ambassador to Russia (d. 1833)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Roanoke\" title=\"<PERSON> of Roanoke\"><PERSON> of Roanoke</a>, American planter and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Russia\" class=\"mw-redirect\" title=\"United States Ambassador to Russia\">United States Ambassador to Russia</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Roanoke\" title=\"<PERSON> of Roanoke\"><PERSON> of Roanoke</a>, American planter and politician, 8th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Russia\" class=\"mw-redirect\" title=\"United States Ambassador to Russia\">United States Ambassador to Russia</a> (d. 1833)", "links": [{"title": "<PERSON> of Roanoke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Roanoke"}, {"title": "United States Ambassador to Russia", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Russia"}]}, {"year": "1774", "text": "<PERSON>, English-Australian explorer and politician (d. 1850)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Australian explorer and politician (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English-Australian explorer and politician (d. 1850)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}]}, {"year": "1813", "text": "<PERSON>, Irish-New Zealand politician, 9th Prime Minister of New Zealand (d. 1896)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 2nd Premier of Quebec (d. 1905)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/G%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9d%C3%A9on_<PERSON>uimet"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1835", "text": "<PERSON> (d. 1914)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON></a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON></a> (d. 1914)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "Duchess <PERSON> of Oldenburg (d. 1900)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Oldenburg\" class=\"mw-redirect\" title=\"Duchess <PERSON> of Oldenburg\">Duchess <PERSON> of Oldenburg</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Oldenburg\" class=\"mw-redirect\" title=\"Duchess <PERSON> of Oldenburg\">Duchess <PERSON> of Oldenburg</a> (d. 1900)", "links": [{"title": "Duchess <PERSON> of Oldenburg", "link": "https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Oldenburg"}]}, {"year": "1840", "text": "<PERSON>, English novelist and poet (d. 1928)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON>, French artist (d. 1895)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French artist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French artist (d. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Munier"}]}, {"year": "1857", "text": "<PERSON>, English composer and educator (d. 1934)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Danish author and poet, Nobel Prize laureate (d. 1919)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Swedish actress and manager (d. 1935)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Concordia_Selander\" title=\"Concordia Selander\"><PERSON><PERSON></a>, Swedish actress and manager (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concordia_Selander\" title=\"Concordia Selander\"><PERSON><PERSON></a>, Swedish actress and manager (d. 1935)", "links": [{"title": "Concordia <PERSON>", "link": "https://wikipedia.org/wiki/Concordia_Selander"}]}, {"year": "1863", "text": "<PERSON>, Croatian-Austrian pianist, composer, and conductor (d. 1942)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Austrian pianist, composer, and conductor (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Austrian pianist, composer, and conductor (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, English cricketer (d. 1901)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Sierra Leone Creole advocate and activist for cultural nationalism (d. 1960)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Adelaide_Casely-Hayford\" title=\"Adelaide Casely-Hayford\">Adelaide Casely-Hayford</a>, <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Creole_people\" title=\"Sierra Leone Creole people\">Sierra Leone Creole</a> <a href=\"https://wikipedia.org/wiki/Advocate\" title=\"Advocate\">advocate</a> and activist for cultural <a href=\"https://wikipedia.org/wiki/Nationalism\" title=\"Nationalism\">nationalism</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_Casely-Hayford\" title=\"Adelaide Casely-Hayford\">Adelaide Casely-Hayford</a>, <a href=\"https://wikipedia.org/wiki/Sierra_Leone_Creole_people\" title=\"Sierra Leone Creole people\">Sierra Leone Creole</a> <a href=\"https://wikipedia.org/wiki/Advocate\" title=\"Advocate\">advocate</a> and activist for cultural <a href=\"https://wikipedia.org/wiki/Nationalism\" title=\"Nationalism\">nationalism</a> (d. 1960)", "links": [{"title": "<PERSON>ly-Hayford", "link": "https://wikipedia.org/wiki/Adelaide_Casely-Hayford"}, {"title": "Sierra Leone Creole people", "link": "https://wikipedia.org/wiki/Sierra_Leone_Creole_people"}, {"title": "Advocate", "link": "https://wikipedia.org/wiki/Advocate"}, {"title": "Nationalism", "link": "https://wikipedia.org/wiki/Nationalism"}]}, {"year": "1866", "text": "<PERSON>, American baseball player and manager (d. 1937)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and manager (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and manager (d. 1937)", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(catcher)"}]}, {"year": "1875", "text": "<PERSON>, American businessman and politician, 50th Mayor of Flint, Michigan (d. 1973)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Flint,_Michigan\" title=\"List of mayors of Flint, Michigan\">Mayor of Flint, Michigan</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Flint,_Michigan\" title=\"List of mayors of Flint, Michigan\">Mayor of Flint, Michigan</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of mayors of Flint, Michigan", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Flint,_Michigan"}]}, {"year": "1878", "text": "<PERSON>, English violinist and bandleader (d. 1912)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and bandleader (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and bandleader (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American golfer (d. 1971)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1971)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, American lawyer and judge (d. 1969)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Thur<PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral and pilot (d. 1945)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral and pilot (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral and pilot (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi"}]}, {"year": "1899", "text": "<PERSON><PERSON>, German animator and director (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German animator and director (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ini<PERSON>\"><PERSON><PERSON></a>, German animator and director (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Reiniger"}]}, {"year": "1899", "text": "<PERSON>, American environmentalist and photographer (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and photographer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and photographer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English painter and educator (d. 1974)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and educator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Hungarian-American swimmer and actor (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American swimmer and actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American swimmer and actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American journalist and author (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> West\"><PERSON></a>, American journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dorothy_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English poet and publisher (d. 1987)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and publisher (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and publisher (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American sprinter (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American runner (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English author (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ym"}]}, {"year": "1913", "text": "<PERSON>, English-Hong Kong educator and politician (d. 2015)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Hong Kong educator and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tu\"><PERSON></a>, English-Hong Kong educator and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Romanian spy (d. 1992)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian spy (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian spy (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, German photographer and director (d. 2006)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and director (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and director (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian-American illustrator (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American journalist and author (d. 2011)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician, 41st Governor of Tennessee (d. 1969)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, American-English actress (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-English actress (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Polish-German author and critic (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German author and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German author and critic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American businessman (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Tex_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tex_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tex_Schramm"}]}, {"year": "1920", "text": "<PERSON>, English screenwriter and producer (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American photographer and philanthropist (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and philanthropist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and philanthropist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American trumpet player (d. 1983)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Royal\"><PERSON></a>, American trumpet player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Hungarian-English businessman and philanthropist (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-English businessman and philanthropist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-English businessman and philanthropist (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Hungarian priest (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Szennay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian priest (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Szennay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian priest (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A1s_Szennay"}]}, {"year": "1922", "text": "<PERSON>, Spanish director and screenwriter (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian-English actress (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American mathematician and economist, Nobel Prize laureate (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1924", "text": "<PERSON>, Canadian journalist, author, and activist (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>, Canadian journalist, author, and activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>, Canadian journalist, author, and activist (d. 2007)", "links": [{"title": "June <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 41st <PERSON><PERSON><PERSON><PERSON> (d. 1977)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Masanobu\" title=\"<PERSON><PERSON><PERSON><PERSON> Masanobu\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 41st <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Masanobu\" title=\"<PERSON><PERSON><PERSON><PERSON> Masanobu\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 41st <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiyonoyama_<PERSON>u"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1926", "text": "<PERSON>, Irish-American actor (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milo_O%27Shea"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American author, screenwriter, and animator (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Biggers\" title=\"<PERSON><PERSON> Biggers\"><PERSON><PERSON></a>, American author, screenwriter, and animator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Biggers\"><PERSON><PERSON></a>, American author, screenwriter, and animator (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Biggers"}]}, {"year": "1927", "text": "<PERSON>, English footballer (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Hungarian singer (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Kov%C3%A1cs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Kov%C3%A1cs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian singer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erzsi_Kov%C3%A1cs"}]}, {"year": "1928", "text": "<PERSON>, English footballer (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, English footballer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)\" title=\"<PERSON> (footballer, born 1928)\"><PERSON></a>, English footballer (d. 1999)", "links": [{"title": "<PERSON> (footballer, born 1928)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1928)"}]}, {"year": "1929", "text": "<PERSON>, American architect, author, and academic (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Juster\" title=\"<PERSON> Juster\"><PERSON></a>, American architect, author, and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norton_Juster\" title=\"<PERSON> Juster\"><PERSON></a>, American architect, author, and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Juster"}]}, {"year": "1929", "text": "<PERSON>, Australian tennis player (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American captain, pilot, and astronaut (d. 1999)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, governor of Chuuk State, Micronesia (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Sasao_Gouland\" title=\"Sasao Gouland\"><PERSON><PERSON><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/Chuuk_State\" title=\"Chuuk State\">Chuuk State</a>, Micronesia (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sasao_Gouland\" title=\"Sasao Gouland\"><PERSON><PERSON><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/Chuuk_State\" title=\"Chuuk State\">Chuuk State</a>, Micronesia (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sasao_<PERSON>nd"}, {"title": "Chuuk State", "link": "https://wikipedia.org/wiki/Chuuk_State"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and coach (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, drag racer (d. 1971)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)\" title=\"<PERSON> (drag racer)\">Lew \"Sneaky <PERSON>\" <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Drag_racing\" title=\"Drag racing\">drag racer</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)\" title=\"<PERSON> (drag racer)\"><PERSON>w \"Sneaky <PERSON>\" <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Drag_racing\" title=\"Drag racing\">drag racer</a> (d. 1971)", "links": [{"title": "<PERSON> (drag racer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)"}, {"title": "Drag racing", "link": "https://wikipedia.org/wiki/Drag_racing"}]}, {"year": "1934", "text": "<PERSON>, American singer (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (d. 2009)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1935", "text": "<PERSON>, American-Canadian novelist and short story writer (d. 2003)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian novelist and short story writer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian novelist and short story writer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Greek poet and educator (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and educator (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and educator (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian race walker (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Volodymyr_Holubnychy\" title=\"Volodymyr Ho<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian race walker (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volodymyr_<PERSON>chy\" title=\"Volodymyr <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian race walker (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Volodymyr_Holubnychy"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English lawyer and judge", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actress (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2012)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1937", "text": "<PERSON>, Canadian figure skater and choreographer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater and choreographer", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(figure_skater)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American screenwriter and playwright", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Der<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English historian and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Lord <PERSON>, Scottish lawyer and judge", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American musician (d. 1980)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician (d. 1980)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1939", "text": "<PERSON>, American golfer (d. 2000)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON> of Greece (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"Constantine II of Greece\">Constantine II of Greece</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_II_of_Greece\" title=\"Constantine II of Greece\">Constantine II of Greece</a> (d. 2023)", "links": [{"title": "Constantine II of Greece", "link": "https://wikipedia.org/wiki/Constantine_II_of_Greece"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Turkish businessman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C3%9Cnal_Aysal\" title=\"Ünal Aysal\"><PERSON><PERSON> Aysal</a>, Turkish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cnal_Aysal\" title=\"Ünal Aysal\"><PERSON><PERSON> Aysal</a>, Turkish businessman", "links": [{"title": "Ünal Aysal", "link": "https://wikipedia.org/wiki/%C3%9Cnal_Aysal"}]}, {"year": "1941", "text": "<PERSON>, American actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian-American ice hockey player and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Swiss jazz pianist (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ir%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss jazz pianist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ir%C3%A8ne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss jazz pianist (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ir%C3%A8ne_<PERSON>zer"}]}, {"year": "1941", "text": "<PERSON>, English drummer, songwriter, and producer (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian politician, 32nd Premier of Queensland (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 32nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 2023)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1943", "text": "<PERSON>, American actor and director", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian cardinal", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Crescenzio_<PERSON>\" title=\"Crescenzio <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crescenzio_<PERSON>e\" title=\"Crescenzio Sep<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal", "links": [{"title": "Crescenzio <PERSON>", "link": "https://wikipedia.org/wiki/Crescenzio_Sepe"}]}, {"year": "1944", "text": "<PERSON>, American actor (d. 2004)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor_born_1944)\" class=\"mw-redirect\" title=\"<PERSON> (actor born 1944)\"><PERSON></a>, American actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor_born_1944)\" class=\"mw-redirect\" title=\"<PERSON> (actor born 1944)\"><PERSON></a>, American actor (d. 2004)", "links": [{"title": "<PERSON> (actor born 1944)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor_born_1944)"}]}, {"year": "1944", "text": "<PERSON>, American composer and conductor (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English painter, sculptor, and photographer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter, sculptor, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter, sculptor, and photographer", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1945", "text": "<PERSON>, American businesswoman and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Swedish director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>str%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>str%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasse_Hallstr%C3%B6m"}]}, {"year": "1946", "text": "<PERSON>, English serial killer (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English serial killer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English serial killer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English astronomer and physicist (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and physicist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist and critic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American journalist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian actress and singer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager (d. 2021)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Mom%C4%8Dilo_Vukoti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mom%C4%8Dilo_Vukoti%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mom%C4%8Dilo_Vukoti%C4%87"}]}, {"year": "1951", "text": "<PERSON>, American artist, gay rights activist, and designer of the rainbow flag (d. 2017)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American artist, gay rights activist, and designer of the rainbow flag (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American artist, gay rights activist, and designer of the rainbow flag (d. 2017)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1951", "text": "<PERSON>, Dutch footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arnold_M%C3%BChren"}]}, {"year": "1951", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Lord <PERSON>, Scottish lawyer, judge, and educator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Lord Ki<PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer, judge, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Lord Kinc<PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer, judge, and educator", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American sports executive, 14th Commissioner of the National Hockey League", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports executive, 14th Commissioner of the <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports executive, 14th Commissioner of the <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Norwegian saxophonist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Norwegian saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Norwegian saxophonist", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1953", "text": "<PERSON>, American golfer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American philosopher, author, and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cornel_West\" title=\"Cornel West\"><PERSON><PERSON><PERSON></a>, American philosopher, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornel_West\" title=\"Cornel West\"><PERSON><PERSON><PERSON></a>, American philosopher, author, and academic", "links": [{"title": "Cornel West", "link": "https://wikipedia.org/wiki/Cornel_West"}]}, {"year": "1954", "text": "<PERSON>, American actor and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American comedian and actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian businessman, co-founded Infosys", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Nandan_Nilekani\" title=\"Nandan Nilekani\"><PERSON><PERSON></a>, Indian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Infosys\" title=\"Infosys\">Infosys</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nandan_Nilekani\" title=\"Nandan Nilekani\"><PERSON><PERSON></a>, Indian businessman, co-founded <a href=\"https://wikipedia.org/wiki/Infosys\" title=\"Infosys\">Infosys</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nandan_Nilekani"}, {"title": "Infosys", "link": "https://wikipedia.org/wiki/Infosys"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1956", "text": "<PERSON>, Dutch race car driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American wrestler and football player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Dutch photographer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Di<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch photographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stra"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nch\" title=\"<PERSON> Lunch\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lunch\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nch"}]}, {"year": "1960", "text": "<PERSON>, Russian runner", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English singer-songwriter and actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American race car driver and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Dez_Cadena\" title=\"Dez Cadena\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dez_Cadena\" title=\"Dez Cadena\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>z Cadena", "link": "https://wikipedia.org/wiki/<PERSON>z_Cadena"}]}, {"year": "1962", "text": "<PERSON>, South African-American runner and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Indian actor (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German director and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Link\"><PERSON></a>, German director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Link\"><PERSON></a>, German director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Link"}]}, {"year": "1965", "text": "<PERSON>, Canadian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Russ_Courtnall"}]}, {"year": "1965", "text": "<PERSON>, Australian cricketer and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Haitian born Canadian-American professional bodybuilder", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian born Canadian-American professional bodybuilder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian born Canadian-American professional bodybuilder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American activist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Spanish singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1966", "text": "<PERSON>, Dutch swimmer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian heptathlete and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Remigija_Nazarovien%C4%97\" title=\"Remigija Nazarovienė\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian heptathlete and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remigija_Nazarovien%C4%97\" title=\"Remigija <PERSON>zarov<PERSON>ė\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian heptathlete and coach", "links": [{"title": "Remigija <PERSON>", "link": "https://wikipedia.org/wiki/Remigija_Nazarovien%C4%97"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (left-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(left-handed_pitcher)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, British politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American television host", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_personality)\" class=\"mw-redirect\" title=\"<PERSON> (television personality)\"><PERSON></a>, American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(television_personality)\" class=\"mw-redirect\" title=\"<PERSON> (television personality)\"><PERSON></a>, American television host", "links": [{"title": "<PERSON> (television personality)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_personality)"}]}, {"year": "1968", "text": "<PERSON>, American comedian and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (entertainer)\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (entertainer)\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(entertainer)"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Brazilian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Paulo_S%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1969)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1969)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_S%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1969)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1969)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1969)", "link": "https://wikipedia.org/wiki/Paulo_S%C3%A<PERSON><PERSON><PERSON>_(footballer,_born_1969)"}]}, {"year": "1969", "text": "<PERSON>, American tennis player, radio host, and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player, radio host, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player, radio host, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON> <PERSON>, American rapper and actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/B_Real\" class=\"mw-redirect\" title=\"B Real\"><PERSON> <PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B_Real\" class=\"mw-redirect\" title=\"B Real\"><PERSON> <PERSON></a>, American rapper and actor", "links": [{"title": "B Real", "link": "https://wikipedia.org/wiki/B_Real"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Czech translator and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Kate%C5%99<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech translator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kate%C5%99<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech translator and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kate%C5%99<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor, comedian, game show host, and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, game show host, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, game show host, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Iba%C3%B1ez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Iba%C3%B1ez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Iba%C3%B1ez"}]}, {"year": "1972", "text": "<PERSON>, American actor and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Dominican-American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Neifi_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neifi_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Neifi_P%C3%A9rez"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian-American chess player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American mixed martial artist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvatore_<PERSON>ibona"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Czech ice hockey player (d. 2007)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Cech\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Cech\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_%C4%8Cech"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian mixed martial artist and boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian mixed martial artist and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian mixed martial artist and boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>-<PERSON><PERSON>, English singer-songwriter and keyboard player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> All<PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teet_Allas"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, American wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/A.J<PERSON>_Styles\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Styles\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A.J._Styles\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, biotechnologist and astronaut, the first Korean in space", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon\" title=\"<PERSON> So-yeon\"><PERSON>on</a>, <a href=\"https://wikipedia.org/wiki/Biotechnologist\" class=\"mw-redirect\" title=\"Biotechnologist\">biotechnologist</a> and <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a>, the first <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">Korean</a> in space", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon\" title=\"<PERSON> So-yeon\"><PERSON>yeon</a>, <a href=\"https://wikipedia.org/wiki/Biotechnologist\" class=\"mw-redirect\" title=\"Biotechnologist\">biotechnologist</a> and <a href=\"https://wikipedia.org/wiki/Astronaut\" title=\"Astronaut\">astronaut</a>, the first <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">Korean</a> in space", "links": [{"title": "<PERSON>yeon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yeon"}, {"title": "Biotechnologist", "link": "https://wikipedia.org/wiki/Biotechnologist"}, {"title": "Astronaut", "link": "https://wikipedia.org/wiki/Astronaut"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Brazilian-American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rin"}]}, {"year": "1979", "text": "<PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boucher\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boucher\"><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian-American drummer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian-American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian-American drummer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American soccer player and coach", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Polish bass player and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>z_Wr%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>z_Wr%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish bass player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tomasz_Wr%C3%B3<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Taiwanese baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>-hui_<PERSON>\" class=\"mw-redirect\" title=\"Chin-hui <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-hui_T<PERSON>o\" class=\"mw-redirect\" title=\"Chin-hui T<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>ui_<PERSON><PERSON>o"}]}, {"year": "1982", "text": "<PERSON>, Canadian actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jewel_Staite\" title=\"Jewel Staite\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jewel_Staite\" title=\"<PERSON> Staite\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON> Staite", "link": "https://wikipedia.org/wiki/Jewel_Staite"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON>, Swiss skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Livers\"><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Livers\"><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Australian-Tongan rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Tongan rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Maltese politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, South African netball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African netball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mary<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, French rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Sri Lankan cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ha"}]}, {"year": "1988", "text": "<PERSON>, Argentine footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>g%C3%BCero\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>g%C3%BCero\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Ag%C3%BCero"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American actress, rapper, and comedian", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Awkwafina\" title=\"Awkwafina\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress, rapper, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Awkwafina\" title=\"Awkwafina\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress, rapper, and comedian", "links": [{"title": "Awkwa<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Awkwafina"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Bulgarian canoeist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stamenova\" title=\"<PERSON><PERSON><PERSON> Stamenova\"><PERSON><PERSON><PERSON></a>, Bulgarian canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stamenova\" title=\"<PERSON><PERSON><PERSON> Stamenova\"><PERSON><PERSON><PERSON></a>, Bulgarian canoeist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stamenova"}]}, {"year": "1989", "text": "<PERSON>, Australian cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1990", "text": "<PERSON>, Australian rules footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Australian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipina singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON><PERSON></a>, Filipina singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON><PERSON></a>, Filipina singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1997", "text": "<PERSON>, American YouTuber", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Woz\" title=\"<PERSON> the Woz\"><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woz\" title=\"<PERSON> the Woz\"><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON> the Woz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Woz"}]}, {"year": "1999", "text": "<PERSON>, Australian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Indonesian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian rules footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hu\" title=\"Madison Hu\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madison_Hu\" title=\"Madison Hu\"><PERSON></a>, American actress", "links": [{"title": "Madison Hu", "link": "https://wikipedia.org/wiki/Madison_Hu"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/F<PERSON><PERSON>_Pole\" title=\"Fon<PERSON> Pole\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Pole\" title=\"F<PERSON><PERSON> Pole\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fonua_Pole"}]}], "Deaths": [{"year": "657", "text": "<PERSON>", "html": "657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "891", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Abbasid general (b. 842)", "html": "891 - <a href=\"https://wikipedia.org/wiki/Al-Mu<PERSON>aq\" title=\"Al-Muwaffaq\"><PERSON><PERSON><PERSON></a>, Abbasid general (b. 842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Muwaffaq\" title=\"Al-Muwaffaq\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Abbasid general (b. 842)", "links": [{"title": "Al-Mu<PERSON><PERSON>aq", "link": "https://wikipedia.org/wiki/Al-<PERSON>ffaq"}]}, {"year": "910", "text": "<PERSON><PERSON> of Provence (b. 845)", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Provence\" title=\"<PERSON><PERSON> of Provence\"><PERSON><PERSON> of Provence</a> (b. 845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Provence\" title=\"<PERSON><PERSON> of Provence\"><PERSON><PERSON> of Provence</a> (b. 845)", "links": [{"title": "<PERSON><PERSON> of Provence", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Provence"}]}, {"year": "1200", "text": "Bishop <PERSON> of Oxford", "html": "1200 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Oxford\" title=\"<PERSON> of Oxford\"><PERSON> of Oxford</a>", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Oxford\" title=\"<PERSON> of Oxford\"><PERSON> of Oxford</a>", "links": [{"title": "<PERSON> of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1258", "text": "<PERSON>, Count of Urgell", "html": "1258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Urgell\" title=\"<PERSON>, Count of Urgell\"><PERSON>, Count of Urgell</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Urgell\" title=\"<PERSON>, Count of Urgell\"><PERSON>, Count of Urgell</a>", "links": [{"title": "<PERSON>, Count of Urgell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1292", "text": "<PERSON>, Welsh nobleman and rebel leader", "html": "1292 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> a<PERSON>\"><PERSON> a<PERSON></a>, Welsh nobleman and rebel leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> a<PERSON>\"><PERSON> a<PERSON></a>, Welsh nobleman and rebel leader", "links": [{"title": "<PERSON> ap <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1418", "text": "<PERSON> of Lancaster, queen of <PERSON> of Castile", "html": "1418 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster\"><PERSON> of Lancaster</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Lancaster\"><PERSON> of Lancaster</a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a>", "links": [{"title": "<PERSON> of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON>, Duke of Trujillo, Constable of Castile", "html": "1453 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Trujillo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Trujillo\"><PERSON><PERSON><PERSON>, Duke of Trujillo</a>, Constable of Castile", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Trujillo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Trujillo\"><PERSON><PERSON><PERSON>, Duke of Trujillo</a>, Constable of Castile", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Trujillo", "link": "https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Trujillo"}]}, {"year": "1567", "text": "<PERSON>, head of the <PERSON><PERSON><PERSON> dynasty in Ireland (b. 1530)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_chieftain)\" title=\"<PERSON> (Irish chieftain)\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/O%27Neil<PERSON>_dynasty\" title=\"<PERSON><PERSON><PERSON> dynasty\"><PERSON><PERSON><PERSON> dynasty</a> in Ireland (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_chieftain)\" title=\"<PERSON> (Irish chieftain)\"><PERSON></a>, head of the <a href=\"https://wikipedia.org/wiki/O%27Neil<PERSON>_dynasty\" title=\"<PERSON><PERSON><PERSON> dynasty\"><PERSON><PERSON><PERSON> dynasty</a> in Ireland (b. 1530)", "links": [{"title": "<PERSON> (Irish chieftain)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(Irish_chieftain)"}, {"title": "<PERSON><PERSON><PERSON> dynasty", "link": "https://wikipedia.org/wiki/O%27Neill_dynasty"}]}, {"year": "1572", "text": "<PERSON>, 4th Duke of Norfolk (b. 1536)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a> (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a> (b. 1536)", "links": [{"title": "<PERSON>, 4th Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk"}]}, {"year": "1581", "text": "<PERSON>, 4th Earl of Morton, Scottish soldier and politician, Lord Chancellor of Scotland (b. 1525)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl of Morton\"><PERSON>, 4th Earl of Morton</a>, Scottish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Earl of Morton\"><PERSON>, 4th Earl of Morton</a>, Scottish soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland\" title=\"Lord Chancellor of Scotland\">Lord Chancellor of Scotland</a> (b. 1525)", "links": [{"title": "<PERSON>, 4th Earl of Morton", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Morton"}, {"title": "Lord Chancellor of Scotland", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_Scotland"}]}, {"year": "1603", "text": "<PERSON> of Wąbrzeźno, Roman Catholic priest (b. 1575)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C4%85brze%C5%BAno\" title=\"<PERSON> of Wąbrzeźno\"><PERSON> of Wąbrzeźno</a>, Roman Catholic priest (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_W%C4%85brze%C5%BAno\" title=\"<PERSON> of Wąbrzeźno\"><PERSON> of Wąbrzeźno</a>, Roman Catholic priest (b. 1575)", "links": [{"title": "<PERSON> of Wąbrzeźno", "link": "https://wikipedia.org/wiki/Bernard_of_W%C4%85brze%C5%BAno"}]}, {"year": "1693", "text": "<PERSON>, English soldier and politician, Postmaster General of the United Kingdom (b. 1621)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Postmaster General of the United Kingdom", "link": "https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom"}]}, {"year": "1701", "text": "<PERSON>, French author (b. 1607)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1607)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ud%C3%A9ry"}]}, {"year": "1716", "text": "<PERSON><PERSON>, Japanese painter and educator (b. 1658)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/Ogata_K%C5%8Drin\" title=\"Ogata Kōrin\"><PERSON><PERSON></a>, Japanese painter and educator (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ogata_K%C5%8Drin\" title=\"Ogata Kōrin\"><PERSON><PERSON></a>, Japanese painter and educator (b. 1658)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ogata_K%C5%8Drin"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON>, Scottish minister and theologian (b. 1680)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish minister and theologian (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish minister and theologian (b. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1761", "text": "<PERSON>, Swedish businessman (b. 1685)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6mer\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jonas_Alstr%C3%B6mer"}]}, {"year": "1785", "text": "<PERSON>, French mathematician and academic (b. 1713)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, English painter (b. 1747)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter (b. 1747)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1853", "text": "<PERSON>, 21st Baron <PERSON>, English general (b. 1777)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>\" title=\"<PERSON>, 21st Baron <PERSON>\"><PERSON>, 21st Baron <PERSON></a>, English general (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>\" title=\"<PERSON>, 21st Baron <PERSON>\"><PERSON>, 21st Baron <PERSON></a>, English general (b. 1777)", "links": [{"title": "<PERSON>, 21st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Baron_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON>, American judge and politician (b. 1783)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Ner_Middleswarth\" title=\"Ner Middleswarth\"><PERSON><PERSON></a>, American judge and politician (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ner_Middleswarth\" title=\"Ner Middleswarth\"><PERSON><PERSON></a>, American judge and politician (b. 1783)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ner_Middleswarth"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Polish psychologist, historian, and philosopher (b. 1806)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish psychologist, historian, and philosopher (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish psychologist, historian, and philosopher (b. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, French lexicographer and philosopher (b. 1801)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and philosopher (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and philosopher (b. 1801)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Littr%C3%A9"}]}, {"year": "1882", "text": "<PERSON>, Italian general and politician  (b. 1807)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian general and politician (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian general and politician (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Canadian missionary and author (b. 1844)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian missionary and author (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian missionary and author (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish painter (b. 1886)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/H%C3%<PERSON>seyin_Avni_Lifij\" title=\"Hüseyin Avni Lifij\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish painter (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%BCseyin_Avni_Lifij\" title=\"Hüseyin Avni Lifij\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish painter (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%<PERSON><PERSON>in_Avni_Lifij"}]}, {"year": "1929", "text": "<PERSON>, Mexican general (b. 1889)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican general (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American runner and triple jumper (b. 1878)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and triple jumper (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner and triple jumper (b. 1878)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)"}]}, {"year": "1937", "text": "<PERSON>, French organist and composer (b. 1870)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_V<PERSON>ne"}]}, {"year": "1941", "text": "<PERSON>, American baseball player (b. 1903)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer and trumpet player (b. 1908)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, 1st Baron <PERSON>, English sailor and politician (b. 1867)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English sailor and politician (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English sailor and politician (b. 1867)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German physician (b. 1904)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, German physician (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German SS officer (b. 1904)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1948", "text": "<PERSON>, German physician (b. 1897)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, German physician (b. 1903)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Wald<PERSON>r_<PERSON><PERSON>\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German physician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wald<PERSON>r_<PERSON><PERSON>\" title=\"Wald<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German physician (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldemar_Hoven"}]}, {"year": "1948", "text": "<PERSON><PERSON>, German SS officer (b. 1905)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>evers"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Bulgarian architect, designed the Central Sofia Market Hall (b. 1880)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Naum_Torbov\" title=\"Naum Torbov\"><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Central_Sofia_Market_Hall\" title=\"Central Sofia Market Hall\">Central Sofia Market Hall</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naum_Torbov\" title=\"Naum Torbov\"><PERSON><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/Central_Sofia_Market_Hall\" title=\"Central Sofia Market Hall\">Central Sofia Market Hall</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naum_<PERSON>bov"}, {"title": "Central Sofia Market Hall", "link": "https://wikipedia.org/wiki/Central_Sofia_Market_Hall"}]}, {"year": "1956", "text": "<PERSON>, Danish-American actor and director (b. 1886)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actor and director (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American actor and director (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Italian actress (b. 1884)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director, producer, and playwright (b. 1889)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and playwright (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and playwright (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>-West, English author and poet (b. 1892)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Vita_Sackville-West\" title=\"Vita Sackville-West\">Vita Sackville-West</a>, English author and poet (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vita_Sackville-West\" title=\"Vita Sackville-West\">Vita Sackville-West</a>, English author and poet (b. 1892)", "links": [{"title": "Vita <PERSON>ville-West", "link": "https://wikipedia.org/wiki/Vita_Sackville-West"}]}, {"year": "1967", "text": "<PERSON><PERSON>, German student and activist (b. 1940)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ben<PERSON>_<PERSON>org\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Ohnesorg\"><PERSON><PERSON></a>, German student and activist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ben<PERSON>_<PERSON>org\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Ohnesorg\"><PERSON><PERSON></a>, German student and activist (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benno_Ohnesorg"}]}, {"year": "1968", "text": "<PERSON>, Canadian pianist and composer (b. 1929)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor (b. 1917)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Turkish author (b. 1914)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish author (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, French director, producer, and screenwriter (b. 1922)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, New Zealand race car driver and engineer, founded the McLaren racing team (b. 1937)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bruce McLaren\"><PERSON></a>, New Zealand race car driver and engineer, founded the <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren racing team</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bruce McLaren\"><PERSON></a>, New Zealand race car driver and engineer, founded the <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren racing team</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McLaren", "link": "https://wikipedia.org/wiki/McLaren"}]}, {"year": "1970", "text": "<PERSON>, Italian soldier, journalist, and academic (b. 1888)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, journalist, and academic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, journalist, and academic (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Spanish anarchist feminist (b. 1895)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_<PERSON>il\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish anarchist feminist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_Saornil\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish anarchist feminist (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luc%C3%ADa_S%C3%A1nchez_Saornil"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver (b. 1949)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English soldier and geographer (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English soldier and geographer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English soldier and geographer (b. 1887)", "links": [{"title": "<PERSON> (geographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)"}]}, {"year": "1976", "text": "<PERSON>, Bolivian general and politician, 61st President of Bolivia (b. 1920)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 61st <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 61st <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1977", "text": "<PERSON>, German footballer (b. 1952)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Northern Irish-born American actor (b. 1931)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-born American actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-born American actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Spanish footballer and coach (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Yeste\" class=\"mw-redirect\" title=\"Santiago Bernabéu Yeste\"><PERSON> Bern<PERSON>é<PERSON></a>, Spanish footballer and coach (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Yeste\" class=\"mw-redirect\" title=\"Santiago Bernabéu Yeste\"><PERSON></a>, Spanish footballer and coach (b. 1895)", "links": [{"title": "Santiago Bernabéu Yeste", "link": "https://wikipedia.org/wiki/Santiago_Bernab%C3%A9u_Yeste"}]}, {"year": "1979", "text": "<PERSON>, American actor (b. 1934)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Pakistani lawyer and politician, 5th President of Pakistan (b. 1904)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry\" title=\"Fazal Ilahi Chaudhry\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry\" title=\"Fazal Ilahi Chaudhry\"><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1904)", "links": [{"title": "Fazal Ilahi Chaudhry", "link": "https://wikipedia.org/wiki/Fazal_Ilahi_Chaudhry"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1982", "text": "<PERSON>, Bangladeshi Islamic scholar (b. 1894)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi Islamic scholar (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi Islamic scholar (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian singer-songwriter (b. 1949)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player and coach (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player (b. 1901)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Aur%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aur%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aur%C3%A8le_<PERSON><PERSON>t"}]}, {"year": "1987", "text": "<PERSON>, Indian-American priest and psychotherapist (b. 1931)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit_priest)\" title=\"<PERSON> (Jesuit priest)\"><PERSON></a>, Indian-American priest and psychotherapist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Jesuit_priest)\" title=\"<PERSON> (Jesuit priest)\"><PERSON></a>, Indian-American priest and psychotherapist (b. 1931)", "links": [{"title": "<PERSON> (Jesuit priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Jesuit_priest)"}]}, {"year": "1987", "text": "<PERSON>, American bandleader and songwriter (b. 1910)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and songwriter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and songwriter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Spanish guitarist (b. 1893)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Segovia\" title=\"Andrés Se<PERSON>\"><PERSON></a>, Spanish guitarist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Segovia\" title=\"Andrés <PERSON>\"><PERSON></a>, Spanish guitarist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Segovia"}]}, {"year": "1988", "text": "<PERSON>, Indian actor, director, and producer (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor, director, and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian cricketer and footballer (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_a%27Beckett\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_a%27B<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_a%27B<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English actor (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Turkish poet and author (b. 1927)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish poet and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish poet and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American director, producer, and screenwriter (b. 1908)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American director, producer, and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American director, producer, and screenwriter (b. 1908)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1993", "text": "<PERSON>, American baseball player, coach, and sportscaster (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Algerian journalist, writer and poet (b. 1954)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian journalist, writer and poet (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian journalist, writer and poet (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian philosopher, author, and academic (b. 1927)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher, author, and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher, author, and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Hungarian-American cinematographer and director (b. 1901)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer and director (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer and director (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English author (b. 1921)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American game show host (b. 1956)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American trumpet player, singer, and bandleader (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, singer, and bandleader (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, singer, and bandleader (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>ham"}]}, {"year": "1997", "text": "<PERSON>, American tennis champion (b. 1908)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis champion (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis champion (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Jamaican singer (b. 1949)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Braithwaite\"><PERSON></a>, Jamaican singer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Braithwaite\"><PERSON></a>, Jamaican singer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian ophthalmologist, academic, and politician (b. 1927)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ophthalmologist, academic, and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ophthalmologist, academic, and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American golfer (b. 1939)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English mathematician, cosmologist, and historian (b. 1912)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, cosmologist, and historian (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, cosmologist, and historian (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American actress and comedian (b. 1908)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Imogene_Coca\" title=\"Imogene Coca\">Imo<PERSON></a>, American actress and comedian (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imogene_Coca\" title=\"Imogene Coca\">Imogene Coca</a>, American actress and comedian (b. 1908)", "links": [{"title": "Imogene Coca", "link": "https://wikipedia.org/wiki/Imogene_Coca"}]}, {"year": "2001", "text": "<PERSON>, American boxer (b. 1922)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Joey_Maxim\" title=\"Joey Maxim\"><PERSON></a>, American boxer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joey_Maxim\" title=\"Joey Maxim\"><PERSON></a>, American boxer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Dutch director and photographer (b. 1937)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director and photographer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director and photographer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American wrestler and manager (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian broadcaster and philanthropist (b. 1906)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Alma_Ricard\" title=\"Alma Ricard\"><PERSON> Ricard</a>, Canadian broadcaster and philanthropist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alma_Ricard\" title=\"Alma Ricard\"><PERSON> Ricard</a>, Canadian broadcaster and philanthropist (b. 1906)", "links": [{"title": "Alma Ricard", "link": "https://wikipedia.org/wiki/Alma_Ricard"}]}, {"year": "2005", "text": "<PERSON>, Canadian lawyer and politician (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lucien <PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lucien <PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Norwegian skier (b. 1930)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gundersen\"><PERSON><PERSON></a>, Norwegian skier (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gundersen\"><PERSON><PERSON></a>, Norwegian skier (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Lebanese journalist and educator (b. 1950)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese journalist and educator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese journalist and educator (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, English civil servant and spy (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English civil servant and spy (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English civil servant and spy (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English rugby player and coach (b. 1952)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby player and coach (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby player and coach (b. 1952)", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Japanese pianist and composer (b. 1949)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kentar%C5%8D_<PERSON>eda"}]}, {"year": "2007", "text": "<PERSON>, Chinese engineer and politician, 1st Vice Premier of the People's Republic of China (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Premier of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice Premier of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Premier_of_the_People%27s_Republic_of_China"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Diddley\"><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Diddley\"><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American author (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Nicaraguan businessman and political activist (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan businessman and political activist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan businessman and political activist (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English-American soldier, actor, television personality, and game show host (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier, actor, television personality, and game show host (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier, actor, television personality, and game show host (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American basketball player (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actress (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian pianist and conductor (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and conductor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Chinese politician, 8th Mayor of Beijing (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Beijing\" title=\"Mayor of Beijing\">Mayor of Beijing</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Beijing\" title=\"Mayor of Beijing\">Mayor of Beijing</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}, {"title": "Mayor of Beijing", "link": "https://wikipedia.org/wiki/Mayor_of_Beijing"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian singer-songwriter and guitarist (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Mandawuy_Yunupingu\" title=\"Manda<PERSON>y Yunupingu\"><PERSON><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mandawuy_Yunupingu\" title=\"Manda<PERSON>y Yunupingu\"><PERSON><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manda<PERSON>y_Yunupingu"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Serbian footballer and manager (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ivica_Brzi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>a_Brzi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer and manager (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivica_Brzi%C4%87"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Russian bobsledder (b. 1984)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian bobsledder (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian bobsledder (b. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American pharmacologist and chemist (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and chemist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and chemist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, East Timorese politician, President of East Timor (b. 1963)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(East_Timorese_politician)\" title=\"<PERSON> (East Timorese politician)\"><PERSON></a>, East Timorese politician, <a href=\"https://wikipedia.org/wiki/President_of_East_Timor\" class=\"mw-redirect\" title=\"President of East Timor\">President of East Timor</a> (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(East_Timorese_politician)\" title=\"<PERSON> (East Timorese politician)\"><PERSON></a>, East Timorese politician, <a href=\"https://wikipedia.org/wiki/President_of_East_Timor\" class=\"mw-redirect\" title=\"President of East Timor\">President of East Timor</a> (b. 1963)", "links": [{"title": "<PERSON> (East Timorese politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(East_Timorese_politician)"}, {"title": "President of East Timor", "link": "https://wikipedia.org/wiki/President_of_East_Timor"}]}, {"year": "2015", "text": "<PERSON>, American biologist and academic, Nobel Prize laureate (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rose\"><PERSON></a>, American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2017", "text": "<PERSON>, English actor (b. 1921)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American football player (b. 1971)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English rugby league footballer (b. 1982)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league footballer (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league footballer (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Israeli politician (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Israeli_politician)\" title=\"<PERSON> (Israeli politician)\"><PERSON></a>, Israeli politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Israeli_politician)\" title=\"<PERSON> (Israeli politician)\"><PERSON></a>, Israeli politician (b. 1937)", "links": [{"title": "<PERSON> (Israeli politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Israeli_politician)"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American actress and singer (b. 1922)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}