{"date": "February 21", "url": "https://wikipedia.org/wiki/February_21", "data": {"Events": [{"year": "452 or 453", "text": "<PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis, is martyred in Palestine.", "html": "452 or 453 - <a href=\"https://wikipedia.org/wiki/452\" title=\"452\">452</a> or <a href=\"https://wikipedia.org/wiki/453\" title=\"453\">453</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Bishop_of_Scythopolis\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis\"><PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis</a>, is martyred in Palestine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/452\" title=\"452\">452</a> or <a href=\"https://wikipedia.org/wiki/453\" title=\"453\">453</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Bishop_of_Scythopolis\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis\"><PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis</a>, is martyred in Palestine.", "links": [{"title": "452", "link": "https://wikipedia.org/wiki/452"}, {"title": "453", "link": "https://wikipedia.org/wiki/453"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, Bishop of Scythopolis", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Bishop_of_Scythopolis"}]}, {"year": "1245", "text": "<PERSON>, the first known Bishop of Finland, is granted resignation after confessing to torture and forgery.", "html": "1245 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop_of_Finland)\" title=\"<PERSON> (bishop of Finland)\"><PERSON></a>, the first known <a href=\"https://wikipedia.org/wiki/Bishop_of_Finland\" class=\"mw-redirect\" title=\"Bishop of Finland\">Bishop of Finland</a>, is granted resignation after confessing to torture and <a href=\"https://wikipedia.org/wiki/Forgery\" title=\"Forgery\">forgery</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop_of_Finland)\" title=\"<PERSON> (bishop of Finland)\"><PERSON></a>, the first known <a href=\"https://wikipedia.org/wiki/Bishop_of_Finland\" class=\"mw-redirect\" title=\"Bishop of Finland\">Bishop of Finland</a>, is granted resignation after confessing to torture and <a href=\"https://wikipedia.org/wiki/Forgery\" title=\"Forgery\">forgery</a>.", "links": [{"title": "<PERSON> (bishop of Finland)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop_of_Finland)"}, {"title": "Bishop of Finland", "link": "https://wikipedia.org/wiki/Bishop_of_Finland"}, {"title": "Forgery", "link": "https://wikipedia.org/wiki/Forgery"}]}, {"year": "1440", "text": "The Prussian Confederation is formed.", "html": "1440 - The <a href=\"https://wikipedia.org/wiki/Prussian_Confederation\" title=\"Prussian Confederation\">Prussian Confederation</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prussian_Confederation\" title=\"Prussian Confederation\">Prussian Confederation</a> is formed.", "links": [{"title": "Prussian Confederation", "link": "https://wikipedia.org/wiki/Prussian_Confederation"}]}, {"year": "1613", "text": "<PERSON> is unanimously elected Tsar by a national assembly, beginning the Romanov dynasty of Imperial Russia.", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> I</a> is unanimously elected <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> by a <a href=\"https://wikipedia.org/wiki/Zemsky_Sobor\" title=\"Zemsky Sobor\">national assembly</a>, beginning the <a href=\"https://wikipedia.org/wiki/Romanov_dynasty\" class=\"mw-redirect\" title=\"Romanov dynasty\">Romanov dynasty</a> of <a href=\"https://wikipedia.org/wiki/Imperial_Russia\" class=\"mw-redirect\" title=\"Imperial Russia\">Imperial Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> I</a> is unanimously elected <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> by a <a href=\"https://wikipedia.org/wiki/Zemsky_Sobor\" title=\"Zemsky Sobor\">national assembly</a>, beginning the <a href=\"https://wikipedia.org/wiki/Romanov_dynasty\" class=\"mw-redirect\" title=\"Romanov dynasty\">Romanov dynasty</a> of <a href=\"https://wikipedia.org/wiki/Imperial_Russia\" class=\"mw-redirect\" title=\"Imperial Russia\">Imperial Russia</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}, {"title": "Zemsky Sobor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sobor"}, {"title": "Romanov dynasty", "link": "https://wikipedia.org/wiki/Romanov_dynasty"}, {"title": "Imperial Russia", "link": "https://wikipedia.org/wiki/Imperial_Russia"}]}, {"year": "1797", "text": "A force of 1,400 French soldiers invaded Britain at Fishguard in support of the Society of United Irishmen. They were defeated by 500 British reservists.", "html": "1797 - A force of 1,400 French soldiers <a href=\"https://wikipedia.org/wiki/Battle_of_Fishguard\" title=\"Battle of Fishguard\">invaded Britain</a> at <a href=\"https://wikipedia.org/wiki/Fishguard\" title=\"Fishguard\">Fishguard</a> in support of the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">Society of United Irishmen</a>. They were defeated by 500 British reservists.", "no_year_html": "A force of 1,400 French soldiers <a href=\"https://wikipedia.org/wiki/Battle_of_Fishguard\" title=\"Battle of Fishguard\">invaded Britain</a> at <a href=\"https://wikipedia.org/wiki/Fishguard\" title=\"Fishguard\">Fishguard</a> in support of the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">Society of United Irishmen</a>. They were defeated by 500 British reservists.", "links": [{"title": "Battle of Fishguard", "link": "https://wikipedia.org/wiki/Battle_of_Fishguard"}, {"title": "Fishguard", "link": "https://wikipedia.org/wiki/Fishguard"}, {"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}]}, {"year": "1804", "text": "The first self-propelling steam locomotive makes its outing at the Pen-y-Darren Ironworks in Wales.", "html": "1804 - The first self-propelling <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> makes its outing at the <a href=\"https://wikipedia.org/wiki/Penydarren_Ironworks\" title=\"Penydarren Ironworks\">Pen-y-Darren Ironworks</a> in Wales.", "no_year_html": "The first self-propelling <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> makes its outing at the <a href=\"https://wikipedia.org/wiki/Penydarren_Ironworks\" title=\"Penydarren Ironworks\">Pen-y-Darren Ironworks</a> in Wales.", "links": [{"title": "Steam locomotive", "link": "https://wikipedia.org/wiki/Steam_locomotive"}, {"title": "Penydarren Ironworks", "link": "https://wikipedia.org/wiki/Penydarren_Ironworks"}]}, {"year": "1808", "text": "Without a previous declaration of war, Russian troops cross the border to Sweden at Abborfors in eastern Finland, thus beginning the Finnish War, in which Sweden will lose the eastern half of the country (i.e. Finland) to Russia.", "html": "1808 - Without a previous declaration of war, Russian troops cross the border to Sweden at <a href=\"https://wikipedia.org/wiki/Abborfors\" class=\"mw-redirect\" title=\"Abborfors\">Abborfors</a> in eastern Finland, thus beginning the <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>, in which Sweden will lose the eastern half of the country (i.e. Finland) to Russia.", "no_year_html": "Without a previous declaration of war, Russian troops cross the border to Sweden at <a href=\"https://wikipedia.org/wiki/Abborfors\" class=\"mw-redirect\" title=\"Abborfors\">Abborfors</a> in eastern Finland, thus beginning the <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>, in which Sweden will lose the eastern half of the country (i.e. Finland) to Russia.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abborfors"}, {"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}]}, {"year": "1828", "text": "Initial issue of the Cherokee Phoenix is the first periodical to use the Cherokee syllabary invented by Sequo<PERSON>.", "html": "1828 - Initial issue of the <a href=\"https://wikipedia.org/wiki/Cherokee_Phoenix\" title=\"Cherokee Phoenix\">Cherokee Phoenix</a> is the first periodical to use the <a href=\"https://wikipedia.org/wiki/Cherokee_syllabary\" title=\"Cherokee syllabary\">Cherokee syllabary</a> invented by <a href=\"https://wikipedia.org/wiki/Sequoyah\" title=\"Sequoyah\">Sequoyah</a>.", "no_year_html": "Initial issue of the <a href=\"https://wikipedia.org/wiki/Cherokee_Phoenix\" title=\"Cherokee Phoenix\">Cherokee Phoenix</a> is the first periodical to use the <a href=\"https://wikipedia.org/wiki/Cherokee_syllabary\" title=\"Cherokee syllabary\">Cherokee syllabary</a> invented by <a href=\"https://wikipedia.org/wiki/Sequoyah\" title=\"Sequoyah\">Sequoyah</a>.", "links": [{"title": "Cherokee Phoenix", "link": "https://wikipedia.org/wiki/Cherokee_Phoenix"}, {"title": "Cherokee syllabary", "link": "https://wikipedia.org/wiki/Cherokee_syllabary"}, {"title": "Sequoyah", "link": "https://wikipedia.org/wiki/Sequoyah"}]}, {"year": "1842", "text": "<PERSON> is granted the first U.S. patent for the sewing machine.", "html": "1842 - <PERSON> is granted the first U.S. patent for the <a href=\"https://wikipedia.org/wiki/Sewing_machine\" title=\"Sewing machine\">sewing machine</a>.", "no_year_html": "<PERSON> is granted the first U.S. patent for the <a href=\"https://wikipedia.org/wiki/Sewing_machine\" title=\"Sewing machine\">sewing machine</a>.", "links": [{"title": "Sewing machine", "link": "https://wikipedia.org/wiki/Sewing_machine"}]}, {"year": "1848", "text": "<PERSON> and <PERSON> publish The Communist Manifesto.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publish <i><a href=\"https://wikipedia.org/wiki/The_Communist_Manifesto\" title=\"The Communist Manifesto\">The Communist Manifesto</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publish <i><a href=\"https://wikipedia.org/wiki/The_Communist_Manifesto\" title=\"The Communist Manifesto\">The Communist Manifesto</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Communist Manifesto", "link": "https://wikipedia.org/wiki/The_Communist_Manifesto"}]}, {"year": "1861", "text": "Mariehamn, the capital city of Åland, is founded.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Mariehamn\" title=\"Mariehamn\">Mariehamn</a>, the capital city of <a href=\"https://wikipedia.org/wiki/%C3%85land\" title=\"Åland\">Åland</a>, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariehamn\" title=\"Mariehamn\">Mariehamn</a>, the capital city of <a href=\"https://wikipedia.org/wiki/%C3%85land\" title=\"Åland\">Åland</a>, is founded.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mariehamn"}, {"title": "Åland", "link": "https://wikipedia.org/wiki/%C3%85land"}]}, {"year": "1862", "text": "American Civil War: Battle of Valverde is fought near Fort Craig in New Mexico Territory.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Valverde\" title=\"Battle of Valverde\">Battle of Valverde</a> is fought near <a href=\"https://wikipedia.org/wiki/Fort_Craig\" title=\"Fort Craig\">Fort Craig</a> in <a href=\"https://wikipedia.org/wiki/New_Mexico_Territory\" title=\"New Mexico Territory\">New Mexico Territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Valverde\" title=\"Battle of Valverde\">Battle of Valverde</a> is fought near <a href=\"https://wikipedia.org/wiki/Fort_Craig\" title=\"Fort Craig\">Fort Craig</a> in <a href=\"https://wikipedia.org/wiki/New_Mexico_Territory\" title=\"New Mexico Territory\">New Mexico Territory</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Valverde", "link": "https://wikipedia.org/wiki/Battle_of_Valverde"}, {"title": "Fort Craig", "link": "https://wikipedia.org/wiki/Fort_Craig"}, {"title": "New Mexico Territory", "link": "https://wikipedia.org/wiki/New_Mexico_Territory"}]}, {"year": "1874", "text": "The Oakland Daily Tribune publishes its first edition.", "html": "1874 - The <i><a href=\"https://wikipedia.org/wiki/Oakland_Daily_Tribune\" class=\"mw-redirect\" title=\"Oakland Daily Tribune\">Oakland Daily Tribune</a></i> publishes its first edition.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Oakland_Daily_Tribune\" class=\"mw-redirect\" title=\"Oakland Daily Tribune\">Oakland Daily Tribune</a></i> publishes its first edition.", "links": [{"title": "Oakland Daily Tribune", "link": "https://wikipedia.org/wiki/Oakland_Daily_Tribune"}]}, {"year": "1878", "text": "The first telephone directory is issued in New Haven, Connecticut.", "html": "1878 - The first <a href=\"https://wikipedia.org/wiki/Telephone_directory\" title=\"Telephone directory\">telephone directory</a> is issued in <a href=\"https://wikipedia.org/wiki/New_Haven,_Connecticut\" title=\"New Haven, Connecticut\">New Haven, Connecticut</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Telephone_directory\" title=\"Telephone directory\">telephone directory</a> is issued in <a href=\"https://wikipedia.org/wiki/New_Haven,_Connecticut\" title=\"New Haven, Connecticut\">New Haven, Connecticut</a>.", "links": [{"title": "Telephone directory", "link": "https://wikipedia.org/wiki/Telephone_directory"}, {"title": "New Haven, Connecticut", "link": "https://wikipedia.org/wiki/New_Haven,_Connecticut"}]}, {"year": "1885", "text": "The newly completed Washington Monument is dedicated.", "html": "1885 - The newly completed <a href=\"https://wikipedia.org/wiki/Washington_Monument\" title=\"Washington Monument\">Washington Monument</a> is dedicated.", "no_year_html": "The newly completed <a href=\"https://wikipedia.org/wiki/Washington_Monument\" title=\"Washington Monument\">Washington Monument</a> is dedicated.", "links": [{"title": "Washington Monument", "link": "https://wikipedia.org/wiki/Washington_Monument"}]}, {"year": "1896", "text": "An Englishman raised in Australia, <PERSON>, fought an Irishman, <PERSON>, in an American promoted event which technically took place in Mexico, winning the 1896 World Heavyweight Championship in boxing.", "html": "1896 - An Englishman raised in Australia, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fought an Irishman, <a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, in an American promoted event which technically took place in Mexico, winning the <a href=\"https://wikipedia.org/wiki/1896_World_Heavyweight_Championship\" class=\"mw-redirect\" title=\"1896 World Heavyweight Championship\">1896 World Heavyweight Championship</a> in <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a>.", "no_year_html": "An Englishman raised in Australia, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fought an Irishman, <a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, in an American promoted event which technically took place in Mexico, winning the <a href=\"https://wikipedia.org/wiki/1896_World_Heavyweight_Championship\" class=\"mw-redirect\" title=\"1896 World Heavyweight Championship\">1896 World Heavyweight Championship</a> in <a href=\"https://wikipedia.org/wiki/Boxing\" title=\"Boxing\">boxing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}, {"title": "1896 World Heavyweight Championship", "link": "https://wikipedia.org/wiki/1896_World_Heavyweight_Championship"}, {"title": "Boxing", "link": "https://wikipedia.org/wiki/Boxing"}]}, {"year": "1913", "text": "Ioannina is incorporated into the Greek state after the Balkan Wars.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a\" title=\"<PERSON>oan<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is incorporated into the <a href=\"https://wikipedia.org/wiki/Greek_state\" class=\"mw-redirect\" title=\"Greek state\">Greek state</a> after the <a href=\"https://wikipedia.org/wiki/Balkan_Wars\" title=\"Balkan Wars\">Balkan Wars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is incorporated into the <a href=\"https://wikipedia.org/wiki/Greek_state\" class=\"mw-redirect\" title=\"Greek state\">Greek state</a> after the <a href=\"https://wikipedia.org/wiki/Balkan_Wars\" title=\"Balkan Wars\">Balkan Wars</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>a"}, {"title": "Greek state", "link": "https://wikipedia.org/wiki/Greek_state"}, {"title": "Balkan Wars", "link": "https://wikipedia.org/wiki/Balkan_Wars"}]}, {"year": "1916", "text": "World War I: In France, the Battle of Verdun begins.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: In France, the <a href=\"https://wikipedia.org/wiki/Battle_of_Verdun\" title=\"Battle of Verdun\">Battle of Verdun</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: In France, the <a href=\"https://wikipedia.org/wiki/Battle_of_Verdun\" title=\"Battle of Verdun\">Battle of Verdun</a> begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Verdun", "link": "https://wikipedia.org/wiki/Battle_of_Verdun"}]}, {"year": "1918", "text": "The last Carolina parakeet dies in captivity at the Cincinnati Zoo.", "html": "1918 - The last <a href=\"https://wikipedia.org/wiki/Carolina_parakeet\" title=\"Carolina parakeet\">Carolina parakeet</a> dies in captivity at the <a href=\"https://wikipedia.org/wiki/Cincinnati_Zoo\" class=\"mw-redirect\" title=\"Cincinnati Zoo\">Cincinnati Zoo</a>.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Carolina_parakeet\" title=\"Carolina parakeet\">Carolina parakeet</a> dies in captivity at the <a href=\"https://wikipedia.org/wiki/Cincinnati_Zoo\" class=\"mw-redirect\" title=\"Cincinnati Zoo\">Cincinnati Zoo</a>.", "links": [{"title": "Carolina parakeet", "link": "https://wikipedia.org/wiki/Carolina_parakeet"}, {"title": "Cincinnati Zoo", "link": "https://wikipedia.org/wiki/Cincinnati_Zoo"}]}, {"year": "1919", "text": "German socialist <PERSON> is assassinated. His death results in the establishment of the Bavarian Soviet Republic and parliament and government fleeing Munich, Germany.", "html": "1919 - German socialist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a>. His death results in the establishment of the <a href=\"https://wikipedia.org/wiki/Bavarian_Soviet_Republic\" title=\"Bavarian Soviet Republic\">Bavarian Soviet Republic</a> and parliament and government fleeing <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, Germany.", "no_year_html": "German socialist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a>. His death results in the establishment of the <a href=\"https://wikipedia.org/wiki/Bavarian_Soviet_Republic\" title=\"Bavarian Soviet Republic\">Bavarian Soviet Republic</a> and parliament and government fleeing <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, Germany.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "Bavarian Soviet Republic", "link": "https://wikipedia.org/wiki/Bavarian_Soviet_Republic"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}]}, {"year": "1921", "text": "Constituent Assembly of the Democratic Republic of Georgia adopts the country's first constitution.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_Georgia\" title=\"Constituent Assembly of Georgia\">Constituent Assembly</a> of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> adopts the country's first constitution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_Georgia\" title=\"Constituent Assembly of Georgia\">Constituent Assembly</a> of the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Georgia\" title=\"Democratic Republic of Georgia\">Democratic Republic of Georgia</a> adopts the country's first constitution.", "links": [{"title": "Constituent Assembly of Georgia", "link": "https://wikipedia.org/wiki/Constituent_Assembly_of_Georgia"}, {"title": "Democratic Republic of Georgia", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Georgia"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON> takes control of Tehran during a successful coup.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Rez%C4%81_Sh%C4%81h\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> takes control of Tehran during a <a href=\"https://wikipedia.org/wiki/1921_Persian_coup_d%27%C3%A9tat\" title=\"1921 Persian coup d'état\">successful coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rez%C4%81_Sh%C4%81h\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> takes control of Tehran during a <a href=\"https://wikipedia.org/wiki/1921_Persian_coup_d%27%C3%A9tat\" title=\"1921 Persian coup d'état\">successful coup</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rez%C4%81_Sh%C4%81h"}, {"title": "1921 Persian coup d'état", "link": "https://wikipedia.org/wiki/1921_Persian_coup_d%27%C3%A9tat"}]}, {"year": "1925", "text": "The New Yorker publishes its first issue.", "html": "1925 - <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> publishes its first issue.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> publishes its first issue.", "links": [{"title": "The New Yorker", "link": "https://wikipedia.org/wiki/The_New_Yorker"}]}, {"year": "1929", "text": "In the first battle of the Warlord Rebellion in northeastern Shandong against the Nationalist government of China, a 24,000-strong rebel force led by <PERSON> was defeated at Zhifu by 7,000 NRA troops.", "html": "1929 - In the first battle of the <a href=\"https://wikipedia.org/wiki/Warlord_Rebellion_in_northeastern_Shandong\" title=\"Warlord Rebellion in northeastern Shandong\">Warlord Rebellion in northeastern Shandong</a> against the <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist government</a> of China, a 24,000-strong rebel force led by <PERSON> was defeated at Zhifu by 7,000 NRA troops.", "no_year_html": "In the first battle of the <a href=\"https://wikipedia.org/wiki/Warlord_Rebellion_in_northeastern_Shandong\" title=\"Warlord Rebellion in northeastern Shandong\">Warlord Rebellion in northeastern Shandong</a> against the <a href=\"https://wikipedia.org/wiki/Nationalist_government\" title=\"Nationalist government\">Nationalist government</a> of China, a 24,000-strong rebel force led by <PERSON> was defeated at Zhifu by 7,000 NRA troops.", "links": [{"title": "Warlord Rebellion in northeastern Shandong", "link": "https://wikipedia.org/wiki/Warlord_Rebellion_in_northeastern_Shandong"}, {"title": "Nationalist government", "link": "https://wikipedia.org/wiki/Nationalist_government"}]}, {"year": "1937", "text": "The League of Nations bans foreign national \"volunteers\" in the Spanish Civil War.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> bans foreign national \"<a href=\"https://wikipedia.org/wiki/Unlawful_combatant\" title=\"Unlawful combatant\">volunteers</a>\" in the <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a> bans foreign national \"<a href=\"https://wikipedia.org/wiki/Unlawful_combatant\" title=\"Unlawful combatant\">volunteers</a>\" in the <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>.", "links": [{"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}, {"title": "Unlawful combatant", "link": "https://wikipedia.org/wiki/Unlawful_combatant"}, {"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}]}, {"year": "1945", "text": "World War II: During the Battle of Iwo Jima, Japanese kamikaze planes sink the escort carrier USS Bismarck Sea and damage the USS Saratoga.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>, Japanese <a href=\"https://wikipedia.org/wiki/Kamikaze\" title=\"Kamikaze\">kamikaze</a> planes sink the escort carrier <a href=\"https://wikipedia.org/wiki/USS_Bismarck_Sea\" title=\"USS Bismarck Sea\">USS <i>Bismarck Sea</i></a> and damage the <a href=\"https://wikipedia.org/wiki/USS_Saratoga_(CV-3)\" title=\"USS Saratoga (CV-3)\">USS <i>Saratoga</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>, Japanese <a href=\"https://wikipedia.org/wiki/Kamikaze\" title=\"Kamikaze\">kamikaze</a> planes sink the escort carrier <a href=\"https://wikipedia.org/wiki/USS_Bismarck_Sea\" title=\"USS Bismarck Sea\">USS <i>Bismarck Sea</i></a> and damage the <a href=\"https://wikipedia.org/wiki/USS_Saratoga_(CV-3)\" title=\"USS Saratoga (CV-3)\">USS <i>Saratoga</i></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>ka<PERSON>"}, {"title": "USS Bismarck Sea", "link": "https://wikipedia.org/wiki/USS_Bismarck_Sea"}, {"title": "USS Saratoga (CV-3)", "link": "https://wikipedia.org/wiki/USS_Saratoga_(CV-3)"}]}, {"year": "1945", "text": "World War II:  the Brazilian Expeditionary Force defeat the German forces in the Battle of Monte Castello on the Italian front.", "html": "1945 - World War II: the <a href=\"https://wikipedia.org/wiki/Brazilian_Expeditionary_Force\" title=\"Brazilian Expeditionary Force\">Brazilian Expeditionary Force</a> defeat the German forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Castello\" title=\"Battle of Monte Castello\">Battle of Monte Castello</a> on the <a href=\"https://wikipedia.org/wiki/Italian_Campaign_(World_War_II)\" class=\"mw-redirect\" title=\"Italian Campaign (World War II)\">Italian front</a>.", "no_year_html": "World War II: the <a href=\"https://wikipedia.org/wiki/Brazilian_Expeditionary_Force\" title=\"Brazilian Expeditionary Force\">Brazilian Expeditionary Force</a> defeat the German forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Monte_Castello\" title=\"Battle of Monte Castello\">Battle of Monte Castello</a> on the <a href=\"https://wikipedia.org/wiki/Italian_Campaign_(World_War_II)\" class=\"mw-redirect\" title=\"Italian Campaign (World War II)\">Italian front</a>.", "links": [{"title": "Brazilian Expeditionary Force", "link": "https://wikipedia.org/wiki/Brazilian_Expeditionary_Force"}, {"title": "Battle of Monte Castello", "link": "https://wikipedia.org/wiki/Battle_of_Monte_Castello"}, {"title": "Italian Campaign (World War II)", "link": "https://wikipedia.org/wiki/Italian_Campaign_(World_War_II)"}]}, {"year": "1947", "text": "In New York City, <PERSON> demonstrates the first \"instant camera\", the Polaroid Land Camera, to a meeting of the Optical Society of America.", "html": "1947 - In New York City, <a href=\"https://wikipedia.org/wiki/Edwin_Land\" class=\"mw-redirect\" title=\"Edwin Land\"><PERSON></a> demonstrates the first \"<a href=\"https://wikipedia.org/wiki/Instant_camera\" title=\"Instant camera\">instant camera</a>\", the <a href=\"https://wikipedia.org/wiki/Polaroid_Land_Camera\" class=\"mw-redirect\" title=\"Polaroid Land Camera\">Polaroid Land Camera</a>, to a meeting of the <a href=\"https://wikipedia.org/wiki/Optical_Society_of_America\" class=\"mw-redirect\" title=\"Optical Society of America\">Optical Society of America</a>.", "no_year_html": "In New York City, <a href=\"https://wikipedia.org/wiki/Edwin_Land\" class=\"mw-redirect\" title=\"Edwin Land\"><PERSON></a> demonstrates the first \"<a href=\"https://wikipedia.org/wiki/Instant_camera\" title=\"Instant camera\">instant camera</a>\", the <a href=\"https://wikipedia.org/wiki/Polaroid_Land_Camera\" class=\"mw-redirect\" title=\"Polaroid Land Camera\">Polaroid Land Camera</a>, to a meeting of the <a href=\"https://wikipedia.org/wiki/Optical_Society_of_America\" class=\"mw-redirect\" title=\"Optical Society of America\">Optical Society of America</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edwin_<PERSON>"}, {"title": "Instant camera", "link": "https://wikipedia.org/wiki/Instant_camera"}, {"title": "Polaroid Land Camera", "link": "https://wikipedia.org/wiki/Polaroid_Land_Camera"}, {"title": "Optical Society of America", "link": "https://wikipedia.org/wiki/Optical_Society_of_America"}]}, {"year": "1948", "text": "NASCAR is incorporated.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> is incorporated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASCAR\" title=\"NASCAR\">NASCAR</a> is incorporated.", "links": [{"title": "NASCAR", "link": "https://wikipedia.org/wiki/NASCAR"}]}, {"year": "1952", "text": "The British government, under <PERSON>, abolishes identity cards in the UK to \"set the people free\".", "html": "1952 - The British government, under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, abolishes <a href=\"https://wikipedia.org/wiki/Identity_document\" title=\"Identity document\">identity cards</a> in the UK to \"set the people free\".", "no_year_html": "The British government, under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, abolishes <a href=\"https://wikipedia.org/wiki/Identity_document\" title=\"Identity document\">identity cards</a> in the UK to \"set the people free\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Identity document", "link": "https://wikipedia.org/wiki/Identity_document"}]}, {"year": "1952", "text": "The Bengali Language Movement protests occur at the University of Dhaka in East Pakistan (now Bangladesh).", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Bengali_Language_Movement\" class=\"mw-redirect\" title=\"Bengali Language Movement\">Bengali Language Movement</a> protests occur at the <a href=\"https://wikipedia.org/wiki/University_of_Dhaka\" title=\"University of Dhaka\">University of Dhaka</a> in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> (now <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bengali_Language_Movement\" class=\"mw-redirect\" title=\"Bengali Language Movement\">Bengali Language Movement</a> protests occur at the <a href=\"https://wikipedia.org/wiki/University_of_Dhaka\" title=\"University of Dhaka\">University of Dhaka</a> in <a href=\"https://wikipedia.org/wiki/East_Pakistan\" title=\"East Pakistan\">East Pakistan</a> (now <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>).", "links": [{"title": "Bengali Language Movement", "link": "https://wikipedia.org/wiki/Bengali_Language_Movement"}, {"title": "University of Dhaka", "link": "https://wikipedia.org/wiki/University_of_Dhaka"}, {"title": "East Pakistan", "link": "https://wikipedia.org/wiki/East_Pakistan"}, {"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}]}, {"year": "1958", "text": "The CND symbol, aka peace symbol, commissioned by the Direct Action Committee in protest against the Atomic Weapons Research Establishment, is designed and completed by <PERSON>.", "html": "1958 - The CND symbol, aka <a href=\"https://wikipedia.org/wiki/Peace_symbol\" class=\"mw-redirect\" title=\"Peace symbol\">peace symbol</a>, commissioned by the <a href=\"https://wikipedia.org/wiki/Direct_Action_Committee\" title=\"Direct Action Committee\">Direct Action Committee</a> in protest against the <a href=\"https://wikipedia.org/wiki/Atomic_Weapons_Research_Establishment\" class=\"mw-redirect\" title=\"Atomic Weapons Research Establishment\">Atomic Weapons Research Establishment</a>, is designed and completed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The CND symbol, aka <a href=\"https://wikipedia.org/wiki/Peace_symbol\" class=\"mw-redirect\" title=\"Peace symbol\">peace symbol</a>, commissioned by the <a href=\"https://wikipedia.org/wiki/Direct_Action_Committee\" title=\"Direct Action Committee\">Direct Action Committee</a> in protest against the <a href=\"https://wikipedia.org/wiki/Atomic_Weapons_Research_Establishment\" class=\"mw-redirect\" title=\"Atomic Weapons Research Establishment\">Atomic Weapons Research Establishment</a>, is designed and completed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Peace symbol", "link": "https://wikipedia.org/wiki/Peace_symbol"}, {"title": "Direct Action Committee", "link": "https://wikipedia.org/wiki/Direct_Action_Committee"}, {"title": "Atomic Weapons Research Establishment", "link": "https://wikipedia.org/wiki/Atomic_Weapons_Research_Establishment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "The Convention on Psychotropic Substances is signed at Vienna.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Convention_on_Psychotropic_Substances\" title=\"Convention on Psychotropic Substances\">Convention on Psychotropic Substances</a> is signed at <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Convention_on_Psychotropic_Substances\" title=\"Convention on Psychotropic Substances\">Convention on Psychotropic Substances</a> is signed at <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "links": [{"title": "Convention on Psychotropic Substances", "link": "https://wikipedia.org/wiki/Convention_on_Psychotropic_Substances"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1972", "text": "United States President <PERSON> visits China to normalize Sino-American relations.", "html": "1972 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> visits China to normalize <a href=\"https://wikipedia.org/wiki/Sino-American_relations\" class=\"mw-redirect\" title=\"Sino-American relations\">Sino-American relations</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> visits China to normalize <a href=\"https://wikipedia.org/wiki/Sino-American_relations\" class=\"mw-redirect\" title=\"Sino-American relations\">Sino-American relations</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sino-American relations", "link": "https://wikipedia.org/wiki/Sino-American_relations"}]}, {"year": "1972", "text": "The Soviet uncrewed spaceship Luna 20 lands on the Moon.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> uncrewed <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spaceship</a> <a href=\"https://wikipedia.org/wiki/Luna_20\" title=\"Luna 20\">Luna 20</a> lands on the Moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> uncrewed <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spaceship</a> <a href=\"https://wikipedia.org/wiki/Luna_20\" title=\"Luna 20\">Luna 20</a> lands on the Moon.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Luna 20", "link": "https://wikipedia.org/wiki/Luna_20"}]}, {"year": "1973", "text": "Over the Sinai Desert, Israeli fighter aircraft shoot down Libyan Arab Airlines Flight 114 jet killing 108 people.", "html": "1973 - Over the <a href=\"https://wikipedia.org/wiki/Sinai_Desert\" class=\"mw-redirect\" title=\"Sinai Desert\">Sinai Desert</a>, Israeli <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a> shoot down <a href=\"https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_114\" title=\"Libyan Arab Airlines Flight 114\">Libyan Arab Airlines Flight 114</a> jet killing 108 people.", "no_year_html": "Over the <a href=\"https://wikipedia.org/wiki/Sinai_Desert\" class=\"mw-redirect\" title=\"Sinai Desert\">Sinai Desert</a>, Israeli <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a> shoot down <a href=\"https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_114\" title=\"Libyan Arab Airlines Flight 114\">Libyan Arab Airlines Flight 114</a> jet killing 108 people.", "links": [{"title": "Sinai Desert", "link": "https://wikipedia.org/wiki/Sinai_Desert"}, {"title": "Fighter aircraft", "link": "https://wikipedia.org/wiki/Fighter_aircraft"}, {"title": "Libyan Arab Airlines Flight 114", "link": "https://wikipedia.org/wiki/Libyan_Arab_Airlines_Flight_114"}]}, {"year": "1974", "text": "The last Israeli soldiers leave the west bank of the Suez Canal pursuant to a truce with Egypt.", "html": "1974 - The last Israeli soldiers leave the west bank of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> pursuant to a truce with <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "The last Israeli soldiers leave the west bank of the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> pursuant to a truce with <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1975", "text": "Watergate scandal: Former United States Attorney General <PERSON> and former White House aides <PERSON><PERSON> <PERSON><PERSON> and <PERSON> are sentenced to prison.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Former <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> and former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> aides <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are sentenced to prison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Former <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a> and former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> aides <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are sentenced to prison.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON> is arrested by the Federal Bureau of Investigation for selling national secrets to the Soviet Union in Arlington County, Virginia.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested by the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a> for selling national secrets to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested by the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a> for selling national secrets to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in <a href=\"https://wikipedia.org/wiki/Arlington_County,_Virginia\" title=\"Arlington County, Virginia\">Arlington County, Virginia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Arlington County, Virginia", "link": "https://wikipedia.org/wiki/Arlington_County,_Virginia"}]}, {"year": "1995", "text": "<PERSON> lands in Leader, Saskatchewan, Canada becoming the first person to make a solo flight across the Pacific Ocean in a balloon.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Leader,_Saskatchewan\" title=\"Leader, Saskatchewan\">Leader, Saskatchewan</a>, Canada becoming the first person to make a solo flight across the Pacific Ocean in a <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">balloon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands in <a href=\"https://wikipedia.org/wiki/Leader,_Saskatchewan\" title=\"Leader, Saskatchewan\">Leader, Saskatchewan</a>, Canada becoming the first person to make a solo flight across the Pacific Ocean in a <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">balloon</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leader, Saskatchewan", "link": "https://wikipedia.org/wiki/Leader,_Saskatchewan"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "2013", "text": "At least 17 people are killed and 119 injured following several bombings in the Indian city of Hyderabad.", "html": "2013 - At least 17 people are killed and 119 injured following <a href=\"https://wikipedia.org/wiki/2013_Hyderabad_blasts\" title=\"2013 Hyderabad blasts\">several bombings</a> in the <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">Indian</a> city of <a href=\"https://wikipedia.org/wiki/Hyderabad\" title=\"Hyderabad\">Hyderabad</a>.", "no_year_html": "At least 17 people are killed and 119 injured following <a href=\"https://wikipedia.org/wiki/2013_Hyderabad_blasts\" title=\"2013 Hyderabad blasts\">several bombings</a> in the <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">Indian</a> city of <a href=\"https://wikipedia.org/wiki/Hyderabad\" title=\"Hyderabad\">Hyderabad</a>.", "links": [{"title": "2013 Hyderabad blasts", "link": "https://wikipedia.org/wiki/2013_Hyderabad_blasts"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Hyderabad", "link": "https://wikipedia.org/wiki/Hyderabad"}]}, {"year": "2022", "text": "In the prelude to the 2022 Russian invasion of Ukraine Russian President <PERSON> declares the Luhansk People's Republic and Donetsk People's Republic as independent from Ukraine, and moves troops into the region. The action is condemned by the United Nations.", "html": "2022 - In the <a href=\"https://wikipedia.org/wiki/Prelude_to_the_2022_Russian_invasion_of_Ukraine\" class=\"mw-redirect\" title=\"Prelude to the 2022 Russian invasion of Ukraine\">prelude to the 2022 Russian invasion of Ukraine</a> Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Luhansk_People%27s_Republic\" title=\"Luhansk People's Republic\">Luhansk People's Republic</a> and <a href=\"https://wikipedia.org/wiki/Donetsk_People%27s_Republic\" title=\"Donetsk People's Republic\">Donetsk People's Republic</a> as independent from <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, and moves troops into the region. The action is condemned by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Prelude_to_the_2022_Russian_invasion_of_Ukraine\" class=\"mw-redirect\" title=\"Prelude to the 2022 Russian invasion of Ukraine\">prelude to the 2022 Russian invasion of Ukraine</a> Russian President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Luhansk_People%27s_Republic\" title=\"Luhansk People's Republic\">Luhansk People's Republic</a> and <a href=\"https://wikipedia.org/wiki/Donetsk_People%27s_Republic\" title=\"Donetsk People's Republic\">Donetsk People's Republic</a> as independent from <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a>, and moves troops into the region. The action is condemned by the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Prelude to the 2022 Russian invasion of Ukraine", "link": "https://wikipedia.org/wiki/Prelude_to_the_2022_Russian_invasion_of_Ukraine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Luhansk People's Republic", "link": "https://wikipedia.org/wiki/Luhansk_People%27s_Republic"}, {"title": "Donetsk People's Republic", "link": "https://wikipedia.org/wiki/Donetsk_People%27s_Republic"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}], "Births": [{"year": "921", "text": "<PERSON>, Japanese astrologer (d. 1005)", "html": "921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abe no Seimei\"><PERSON> no <PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Astrology\" title=\"Astrology\">astrologer</a> (d. 1005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Abe no Seimei\"><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Astrology\" title=\"Astrology\">astrologer</a> (d. 1005)", "links": [{"title": "<PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i"}, {"title": "Astrology", "link": "https://wikipedia.org/wiki/Astrology"}]}, {"year": "1397", "text": "<PERSON> of Portugal (d. 1471)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy\" class=\"mw-redirect\" title=\"In<PERSON><PERSON> <PERSON>, Duchess of Burgundy\"><PERSON> of Portugal</a> (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy\" class=\"mw-redirect\" title=\"In<PERSON><PERSON> <PERSON>, Duchess of Burgundy\"><PERSON> of Portugal</a> (d. 1471)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duchess of Burgundy", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Duchess_of_Burgundy"}]}, {"year": "1462", "text": "<PERSON>, princess of Castile (d. 1530)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Belt<PERSON>\"><PERSON></a>, princess of Castile (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Belt<PERSON>\"><PERSON></a>, princess of Castile (d. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1484", "text": "<PERSON>, Elector of Brandenburg (d. 1535)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (d. 1535)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>ector_of_Brandenburg"}]}, {"year": "1498", "text": "<PERSON>, 4th Earl of Westmorland, English Earl (d. 1549)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Westmorland\" title=\"<PERSON>, 4th Earl of Westmorland\"><PERSON>, 4th Earl of Westmorland</a>, English Earl (d. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Westmorland\" title=\"<PERSON>, 4th Earl of Westmorland\"><PERSON>, 4th Earl of Westmorland</a>, English Earl (d. 1549)", "links": [{"title": "<PERSON>, 4th Earl of Westmorland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Westmorland"}]}, {"year": "1541", "text": "<PERSON>, Count of Hanau-Lichtenberg (d. 1599)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1599)", "links": [{"title": "<PERSON>, Count of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1556", "text": "<PERSON><PERSON>, German astronomer, composer, and theorist (d. 1615)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ius\"><PERSON><PERSON></a>, German astronomer, composer, and theorist (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Calvisius\"><PERSON><PERSON></a>, German astronomer, composer, and theorist (d. 1615)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>visius"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON>, Italian military commander (d. 1680)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian military commander (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian military commander (d. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, Massachusetts colonist, executed as a witch (d. 1692)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nurse\"><PERSON></a>, Massachusetts colonist, executed as a witch (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Nurse\" title=\"<PERSON> Nurse\"><PERSON></a>, Massachusetts colonist, executed as a witch (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, 1st Baron <PERSON>, English admiral and politician (d. 1781)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English admiral and politician (d. 1781)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1728", "text": "<PERSON> of Russia (d. 1762)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1762)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON> of Württemberg (d. 1835)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg\" title=\"Catharina of Württemberg\"><PERSON><PERSON><PERSON> of Württemberg</a> (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg\" title=\"Catharina of Württemberg\"><PERSON><PERSON><PERSON> of Württemberg</a> (d. 1835)", "links": [{"title": "Cat<PERSON>na of Württemberg", "link": "https://wikipedia.org/wiki/Catharina_of_W%C3%BCrttemberg"}]}, {"year": "1788", "text": "<PERSON>, British scientist, inventor and engineer who was knighted for developing the first working electric telegraph (d. 1873)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist, inventor and engineer who was knighted for developing the first working <a href=\"https://wikipedia.org/wiki/Electric_telegraph\" class=\"mw-redirect\" title=\"Electric telegraph\">electric telegraph</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist, inventor and engineer who was knighted for developing the first working <a href=\"https://wikipedia.org/wiki/Electric_telegraph\" class=\"mw-redirect\" title=\"Electric telegraph\">electric telegraph</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electric telegraph", "link": "https://wikipedia.org/wiki/Electric_telegraph"}]}, {"year": "1791", "text": "<PERSON>, Austrian pianist and composer (d. 1857)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, Mexican general and politician, 8th President of Mexico (d. 1876)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON> Anna\"><PERSON></a>, Mexican general and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON> Anna\"><PERSON></a>, Mexican general and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_L%C3%B3<PERSON><PERSON>_<PERSON>_Santa_Anna"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1801", "text": "<PERSON>, English cardinal (d. 1890)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Spanish poet and playwright (d. 1893)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Zor<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Zorrilla\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>rilla"}]}, {"year": "1821", "text": "<PERSON>, American publisher, founded Charles Scribner's Sons (d. 1871)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons\" title=\"<PERSON>'s Sons\"><PERSON>'s Sons</a> (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons\" title=\"<PERSON>'s Sons\"><PERSON>'s Sons</a> (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Charles <PERSON>'s Sons", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Sons"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, French pianist and composer (d. 1891)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_Delibes\" title=\"Léo Delibes\"><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9o_Delibes\" title=\"Léo Delibes\"><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1891)", "links": [{"title": "Léo <PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_Delibes"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, French organist and composer (d. 1937)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Welsh-English sculptor and academic (d. 1952)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>sco<PERSON>_<PERSON>\" title=\"Goscombe John\"><PERSON><PERSON><PERSON> <PERSON></a>, Welsh-English sculptor and academic (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sco<PERSON>_<PERSON>\" title=\"Goscombe John\"><PERSON><PERSON><PERSON> <PERSON></a>, Welsh-English sculptor and academic (d. 1952)", "links": [{"title": "Goscombe John", "link": "https://wikipedia.org/wiki/Goscombe_John"}]}, {"year": "1865", "text": "<PERSON>, English author and educator, founded the Bedales School (d. 1967)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator, founded the <a href=\"https://wikipedia.org/wiki/Bedales_School\" title=\"Bedales School\">Bedales School</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator, founded the <a href=\"https://wikipedia.org/wiki/Bedales_School\" title=\"Bedales School\">Bedales School</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bedales School", "link": "https://wikipedia.org/wiki/Bedales_School"}]}, {"year": "1867", "text": "<PERSON>, German banker and philanthropist (d. 1934)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and philanthropist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and philanthropist (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, French super-centenarian, oldest verified person ever (d. 1997)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French super-centenarian, oldest verified person ever (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French super-centenarian, oldest verified person ever (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, French-Indian spiritual leader (d. 1973)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Indian spiritual leader (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Indian spiritual leader (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English soldier, bandmaster, and composer (d. 1945)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, bandmaster, and composer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, bandmaster, and composer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Russian-French actor, director, and playwright (d. 1957)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-French actor, director, and playwright (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-French actor, director, and playwright (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general and politician, 54th Japanese Minister of War (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general and politician, 54th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general and politician, 54th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko<PERSON>chi<PERSON>_Anami"}, {"title": "Ministry of War of Japan", "link": "https://wikipedia.org/wiki/Ministry_of_War_of_Japan"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, English author and playwright (d. 1965)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and playwright (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and playwright (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American psychiatrist and psychoanalyst (d. 1949)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and psychoanalyst (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and psychoanalyst (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Austrian-American actress (d. 1979)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actress (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actress (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Spanish guitarist (d. 1987)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Segovia\" title=\"Andrés <PERSON>\"><PERSON></a>, Spanish guitarist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Segovia\" title=\"Andrés <PERSON>\"><PERSON></a>, Spanish guitarist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Segovia"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Indian chemist and academic (d. 1955)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swaroop_Bhatnagar\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Swaroop Bhatnagar\"><PERSON><PERSON>hat<PERSON></a>, Indian chemist and academic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Swaroop_Bhatnagar\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Swaroop Bhatnagar\"><PERSON><PERSON>hat<PERSON></a>, Indian chemist and academic (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>waroop_Bhatnagar"}]}, {"year": "1895", "text": "<PERSON>, Danish biochemist and physiologist, Nobel Prize laureate (d. 1976)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Indian poet and author (d. 1961)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>ath<PERSON> '<PERSON><PERSON>'\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>ath<PERSON> '<PERSON>'\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Suryakant_Tripathi_%27Nirala%27"}]}, {"year": "1900", "text": "<PERSON>, French singer and actress (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English theologian and academic (d. 1963)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and academic (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, French-American essayist and memoirist (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Ana%C3%AFs_Nin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American essayist and memoirist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana%C3%AFs_Nin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American essayist and memoirist (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ana%C3%AFs_Nin"}]}, {"year": "1903", "text": "<PERSON>, French poet and author (d. 1976)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-American poet, playwright, and composer (d. 1973)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>den\"><PERSON><PERSON> <PERSON><PERSON></a>, English-American poet, playwright, and composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-American poet, playwright, and composer (d. 1973)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>den"}]}, {"year": "1909", "text": "<PERSON>, Swiss painter, sculptor, and illustrator (d. 2015)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter, sculptor, and illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter, sculptor, and illustrator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "1910", "text": "<PERSON>, English fighter pilot in World War II (d. 1982)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fighter pilot in World War II (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English fighter pilot in World War II (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American actress and singer (d. 1974)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Arline_Judge\" title=\"Arline Judge\"><PERSON><PERSON><PERSON></a>, American actress and singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arline_Judge\" title=\"Arline Judge\"><PERSON><PERSON><PERSON></a>, American actress and singer (d. 1974)", "links": [{"title": "Arline Judge", "link": "https://wikipedia.org/wiki/A<PERSON>e_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Finnish soldier and pilot (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish soldier and pilot (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish soldier and pilot (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1965)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American psychiatrist and physician (d. 1944)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and physician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and physician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Trinidad-British journalist and activist (d. 1964)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidad-British journalist and activist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidad-British journalist and activist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actress and singer (d. 1967)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Prime Minister of Slovenia (d. 2017)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1a\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of Slovenia (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1a\" title=\"<PERSON>\"><PERSON></a>, Prime Minister of Slovenia (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_Vratu%C5%A1a"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American actress and dancer (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American pianist and composer (d. 1965)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech animator (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_Miler\" title=\"Zdeněk Miler\"><PERSON><PERSON><PERSON><PERSON> Miler</a>, Czech animator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zden%C4%9Bk_Miler\" title=\"Zdeněk Miler\"><PERSON><PERSON><PERSON><PERSON> Mile<PERSON></a>, Czech animator (d. 2011)", "links": [{"title": "Zdeněk Miler", "link": "https://wikipedia.org/wiki/Zden%C4%9Bk_Miler"}]}, {"year": "1921", "text": "<PERSON>, American philosopher and academic (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American aeronautical engineer (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aeronautical engineer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American aeronautical engineer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American computer scientist and cryptanalyst (d. 1980)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and cryptanalyst (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and cryptanalyst (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American computer scientist and engineer (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lma <PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thelma E<PERSON>\"><PERSON><PERSON></a>, American computer scientist and engineer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rin"}]}, {"year": "1924", "text": "<PERSON>, Zimbabwean educator and politician, 2nd President of Zimbabwe (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Zimbabwe\" title=\"President of Zimbabwe\">President of Zimbabwe</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zimbabwe", "link": "https://wikipedia.org/wiki/President_of_Zimbabwe"}]}, {"year": "1925", "text": "<PERSON>, American director and screenwriter (d. 1984)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American basketball player, coach, and sportscaster (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American journalist and author (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican actor, director, producer, and screenwriter (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Chespirito\" title=\"Chespirito\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican actor, director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chespirito\" title=\"Chespirito\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican actor, director, producer, and screenwriter (d. 2014)", "links": [{"title": "Chespirito", "link": "https://wikipedia.org/wiki/Chespirito"}]}, {"year": "1933", "text": "<PERSON>, American film director, producer, and screenwriter (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director, producer, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and pianist (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Scottish actor (d. 1994)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician (d. 1996)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_Jordan"}]}, {"year": "1937", "text": "<PERSON>, Australian runner and politician, Mayor of the Gold Coast (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast\" title=\"Mayor of the Gold Coast\">Mayor of the Gold Coast</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast\" title=\"Mayor of the Gold Coast\">Mayor of the Gold Coast</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of the Gold Coast", "link": "https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast"}]}, {"year": "1937", "text": "<PERSON> of Norway", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> V of Norway\"><PERSON> of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" class=\"mw-redirect\" title=\"<PERSON> V of Norway\"><PERSON> of Norway</a>", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/Harald_V_of_Norway"}]}, {"year": "1937", "text": "<PERSON>, American actor", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English racing driver (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American activist and politician (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Trinidadian-American historian and academic (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" title=\"<PERSON> (professor)\"><PERSON></a>, Trinidadian-American historian and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" title=\"<PERSON> (professor)\"><PERSON></a>, Trinidadian-American historian and academic (d. 2013)", "links": [{"title": "<PERSON> (professor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)"}]}, {"year": "1942", "text": "<PERSON><PERSON>, German actress, director, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American businessman, co-founded DreamWorks and Geffen Records", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/DreamWorks_Pictures\" title=\"DreamWorks Pictures\">DreamWorks</a> and <a href=\"https://wikipedia.org/wiki/Geffen_Records\" title=\"Geffen Records\">Geffen Records</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/DreamWorks_Pictures\" title=\"DreamWorks Pictures\">DreamWorks</a> and <a href=\"https://wikipedia.org/wiki/Geffen_Records\" title=\"Geffen Records\">Geffen Records</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DreamWorks Pictures", "link": "https://wikipedia.org/wiki/DreamWorks_Pictures"}, {"title": "Geffen Records", "link": "https://wikipedia.org/wiki/Geffen_Records"}]}, {"year": "1945", "text": "<PERSON>, English golfer (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actress and singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Tyne_Daly\" title=\"<PERSON> Daly\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyne_Daly\" title=\"Tyne Daly\"><PERSON></a>, American actress and singer", "links": [{"title": "Tyne Daly", "link": "https://wikipedia.org/wiki/Tyne_Daly"}]}, {"year": "1946", "text": "<PERSON>, English actor and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor and director (d. 2016)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Snowe\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olympia_Snowe"}]}, {"year": "1949", "text": "<PERSON>, American illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brunner\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Brunner\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Swedish footballer (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>str%C3%B6m"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian politician and diplomat, 5th President of Ethiopia", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Work_Zewde\" title=\"<PERSON><PERSON><PERSON>-<PERSON> Zewde\"><PERSON><PERSON><PERSON>-<PERSON></a>, Ethiopian politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/President_of_Ethiopia\" title=\"President of Ethiopia\">President of Ethiopia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_Zewde\" title=\"<PERSON><PERSON><PERSON>-<PERSON> Zewde\"><PERSON><PERSON><PERSON>-<PERSON></a>, Ethiopian politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/President_of_Ethiopia\" title=\"President of Ethiopia\">President of Ethiopia</a>", "links": [{"title": "Sahle-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Work_Zewde"}, {"title": "President of Ethiopia", "link": "https://wikipedia.org/wiki/President_of_Ethiopia"}]}, {"year": "1951", "text": "<PERSON>, American keyboard player (d. 2006)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, English bass player, songwriter, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Russian diplomat, former Ambassador of Russia to the United Nations (d. 2017)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian diplomat, former <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_Russia_to_the_United_Nations\" title=\"Permanent Representative of Russia to the United Nations\">Ambassador of Russia to the United Nations</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian diplomat, former <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_Russia_to_the_United_Nations\" title=\"Permanent Representative of Russia to the United Nations\">Ambassador of Russia to the United Nations</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Permanent Representative of Russia to the United Nations", "link": "https://wikipedia.org/wiki/Permanent_Representative_of_Russia_to_the_United_Nations"}]}, {"year": "1953", "text": "<PERSON>, American actress and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, British politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor, singer, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Northern Irish singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian-American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1958", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Spanish singer-songwriter and painter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Cano\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Cano\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Cano"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Bulgarian economist and politician, 52nd Prime Minister of Bulgaria", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Plamen_<PERSON>ki\" title=\"<PERSON>lam<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian economist and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plam<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian economist and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Plam<PERSON>_<PERSON>"}, {"title": "Prime Minister of Bulgaria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria"}]}, {"year": "1961", "text": "<PERSON>, American actor and businessman", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American psychologist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American novelist and journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American novelist, short story writer, and essayist (d. 2008)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and musician (d. 2019)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Ranking_Roger\" title=\"Ranking Roger\">Ranking Roger</a>, English singer-songwriter and musician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranking_Roger\" title=\"Ranking Roger\">Ranking Roger</a>, English singer-songwriter and musician (d. 2019)", "links": [{"title": "Ranking Roger", "link": "https://wikipedia.org/wiki/Ranking_Roger"}]}, {"year": "1963", "text": "<PERSON>, New Zealand golfer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American astronaut and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American astronaut", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)"}]}, {"year": "1965", "text": "<PERSON>, Australian journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_presenter)\" title=\"<PERSON> (news presenter)\"><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_presenter)\" title=\"<PERSON> (news presenter)\"><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON> (news presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_presenter)"}]}, {"year": "1967", "text": "<PERSON>, American runner and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Finnish athlete and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish athlete and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish athlete and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Welsh singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Austrian skier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American soccer player and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swedish golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Faroese singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Campo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Campo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Campo"}]}, {"year": "1975", "text": "<PERSON>, Australian swimmer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_(swimmer)"}]}, {"year": "1976", "text": "<PERSON>, English comedian, actor and television presenter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American musician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>den<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gidden<PERSON>\"><PERSON><PERSON><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>den<PERSON>\" title=\"<PERSON><PERSON><PERSON>idden<PERSON>\"><PERSON><PERSON><PERSON></a>, American musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actor and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Puerto Rican wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Puerto Rican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Puerto Rican wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1979", "text": "<PERSON>, Guadeloupean-French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean-French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean-French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pascal_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, comedian, director, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fast\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Italian singer-songwriter and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tiz<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tiz<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, 5th King of Bhutan", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Jigme_Khes<PERSON>_<PERSON>_<PERSON>\" title=\"Jigme Khesar Nam<PERSON>\">Jigme Khesar <PERSON></a>, 5th <a href=\"https://wikipedia.org/wiki/King_of_Bhutan\" title=\"King of Bhutan\">King of Bhutan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jigme_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Jigme Khesar Namgyel <PERSON>\">Jigme Khesar <PERSON></a>, 5th <a href=\"https://wikipedia.org/wiki/King_of_Bhutan\" title=\"King of Bhutan\">King of Bhutan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "King of Bhutan", "link": "https://wikipedia.org/wiki/King_of_Bhutan"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ada"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, South African DJ and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>bo<PERSON>_<PERSON>_<PERSON>\" title=\"Tebogo Jack<PERSON> Magu<PERSON>\"><PERSON><PERSON><PERSON></a>, South African DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Tebogo Jack<PERSON> Magu<PERSON>\"><PERSON><PERSON><PERSON></a>, South African DJ and producer", "links": [{"title": "Tebogo <PERSON>", "link": "https://wikipedia.org/wiki/Tebo<PERSON>_Jack<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Venezuelan baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Franklin_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franklin_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franklin_Guti%C3%A9rrez"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/M%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1984", "text": "<PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Samaras\" title=\"<PERSON><PERSON> Samaras\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Samaras\" title=\"<PERSON><PERSON> Samaras\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Samaras"}]}, {"year": "1986", "text": "<PERSON>, Welsh singer-songwriter and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Charlotte_Church\" title=\"Charlotte Church\"><PERSON></a>, Welsh singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_Church\" title=\"Charlotte Church\"><PERSON></a>, Welsh singer-songwriter and actress", "links": [{"title": "Charlotte Church", "link": "https://wikipedia.org/wiki/Charlotte_Church"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Eniol<PERSON>_Aluko\" title=\"Eniola Aluko\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eniol<PERSON>_Aluko\" title=\"Eniola Aluko\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eniola_Aluko"}]}, {"year": "1987", "text": "<PERSON>, American actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Dont%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dont%C3%A9_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dont%C3%A9_Greene"}]}, {"year": "1989", "text": "<PERSON>, American actor, model, dancer, film producer and singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Corbin Bleu\"><PERSON></a>, American actor, model, dancer, film producer and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Corbin Bleu\"><PERSON></a>, American actor, model, dancer, film producer and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bleu"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Argentine footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Federico_Fern%C3%<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>by"}]}, {"year": "1991", "text": "<PERSON>, English actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Algerian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>un\" title=\"<PERSON>yun\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yun\" title=\"<PERSON>yun\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yun"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Travis"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON> (footballer, born 1992)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)\" title=\"<PERSON> (footballer, born 1992)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1992)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1992)"}]}, {"year": "1993", "text": "<PERSON>, Cameroonian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Dutch footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Chinese tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actress and singer-songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Or<PERSON>\" title=\"<PERSON><PERSON> Orrantia\"><PERSON><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Orrantia\"><PERSON><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rantia"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1996", "text": "<PERSON>, American tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1996", "text": "<PERSON>, English actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Thai actor and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-iamkajorn\" title=\"<PERSON><PERSON><PERSON>-iamkajorn\"><PERSON><PERSON><PERSON></a>, Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-iamkajorn\" title=\"<PERSON><PERSON><PERSON>-iamkajorn\"><PERSON><PERSON><PERSON>-<PERSON></a>, Thai actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>awin_Opas-iam<PERSON><PERSON>rn"}]}], "Deaths": [{"year": "4 AD", "text": "<PERSON>, Roman consul and grandson of <PERSON> (b. 20 BC)", "html": "4 AD - 4 AD - <a href=\"https://wikipedia.org/wiki/Gaius_Caesar\" title=\"Gaius Caesar\">Gaius Caesar</a>, Roman <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">consul</a> and grandson of <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a> (b. 20 BC)", "no_year_html": "4 AD - <a href=\"https://wikipedia.org/wiki/Gaius_Caesar\" title=\"Gaius Caesar\">Gaius Caesar</a>, Roman <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">consul</a> and grandson of <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a> (b. 20 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roman consul", "link": "https://wikipedia.org/wiki/Roman_consul"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "675", "text": "<PERSON><PERSON><PERSON> of Grandval, prior of the Benedictine monastery of Grandval", "html": "675 - <a href=\"https://wikipedia.org/wiki/<PERSON>oald_of_Grandval\" title=\"<PERSON><PERSON><PERSON> of Grandval\"><PERSON><PERSON><PERSON> of Grandval</a>, <a href=\"https://wikipedia.org/wiki/Priory#Other_prior_and_priories\" title=\"Priory\">prior</a> of the <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> <a href=\"https://wikipedia.org/wiki/Monastery\" title=\"Monastery\">monastery</a> of <a href=\"https://wikipedia.org/wiki/Grandval,_Switzerland\" title=\"Grandval, Switzerland\">Grandval</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oa<PERSON>_of_Grandval\" title=\"<PERSON><PERSON><PERSON> of Grandval\"><PERSON><PERSON><PERSON> of Grandval</a>, <a href=\"https://wikipedia.org/wiki/Priory#Other_prior_and_priories\" title=\"Priory\">prior</a> of the <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> <a href=\"https://wikipedia.org/wiki/Monastery\" title=\"Monastery\">monastery</a> of <a href=\"https://wikipedia.org/wiki/Grandval,_Switzerland\" title=\"Grandval, Switzerland\">Grandval</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Grandval", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Grandval"}, {"title": "Priory", "link": "https://wikipedia.org/wiki/Priory#Other_prior_and_priories"}, {"title": "Benedictine", "link": "https://wikipedia.org/wiki/Benedictine"}, {"title": "Monastery", "link": "https://wikipedia.org/wiki/Monastery"}, {"title": "Grandval, Switzerland", "link": "https://wikipedia.org/wiki/Grandval,_Switzerland"}]}, {"year": "1184", "text": "<PERSON><PERSON>, Japanese shōgun (b. 1154)", "html": "1184 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoshinaka\" title=\"Minamoto no Yoshinaka\"><PERSON><PERSON> no Yoshinaka</a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">shōgun</a> (b. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoshinaka\" title=\"Minamoto no Yoshinaka\"><PERSON><PERSON> no Yoshinaka</a>, Japanese <a href=\"https://wikipedia.org/wiki/List_of_sh%C5%8Dguns\" class=\"mw-redirect\" title=\"List of shōguns\">shōgun</a> (b. 1154)", "links": [{"title": "<PERSON><PERSON> no <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "List of shōguns", "link": "https://wikipedia.org/wiki/List_of_sh%C5%8Dguns"}]}, {"year": "1211", "text": "<PERSON><PERSON><PERSON>, archbishop of Tarentaise and crusader", "html": "1211 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Brian%C3%A7on\" title=\"<PERSON><PERSON><PERSON> de Briançon\"><PERSON><PERSON><PERSON></a>, archbishop of Tarentaise and crusader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Brian%C3%A7on\" title=\"<PERSON><PERSON><PERSON> de Briançon\"><PERSON><PERSON><PERSON> Briançon</a>, archbishop of Tarentaise and crusader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Brian%C3%A7on"}]}, {"year": "1267", "text": "<PERSON> of Ibelin, <PERSON><PERSON><PERSON> of Cyprus", "html": "1267 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Ibelin,_<PERSON><PERSON><PERSON>_of_Cyprus\" title=\"<PERSON> of Ibelin, <PERSON><PERSON><PERSON> of Cyprus\"><PERSON> of Ibelin, <PERSON><PERSON>chal of Cyprus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_I<PERSON>,_<PERSON><PERSON><PERSON>_of_Cyprus\" title=\"<PERSON> of Ibelin, <PERSON><PERSON><PERSON> of Cyprus\"><PERSON> of Ibelin, <PERSON><PERSON>chal of Cyprus</a>", "links": [{"title": "<PERSON> of Ibelin, <PERSON><PERSON><PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_of_Cyprus"}]}, {"year": "1437", "text": "<PERSON> of Scotland (b. 1394; assassinated)", "html": "1437 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> I of Scotland\"><PERSON> of Scotland</a> (b. 1394; assassinated)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1394; assassinated)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1471", "text": "<PERSON>, Czech bishop and theologian (b. 1396)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech bishop and theologian (b. 1396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech bishop and theologian (b. 1396)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1513", "text": "<PERSON> (b. 1443)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Julius_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Pope Julius II\">Pope <PERSON> II</a> (b. 1443)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1543", "text": "<PERSON>, Somalian general (b. 1507)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> ibn <PERSON></a>, Somalian general (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, Somalian general (b. 1507)", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1554", "text": "<PERSON><PERSON><PERSON><PERSON>, German botanist and physician (b. 1498)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German botanist and physician (b. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German botanist and physician (b. 1498)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, Korean poet and scholar (b. 1501)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Shi<PERSON>\"><PERSON></a>, Korean poet and scholar (b. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Shik\"><PERSON></a>, Korean poet and scholar (b. 1501)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "1590", "text": "<PERSON>, 3rd Earl of Warwick, English nobleman and general (b. 1528)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Warwick\" title=\"<PERSON>, 3rd Earl of Warwick\"><PERSON>, 3rd Earl of Warwick</a>, English nobleman and general (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Warwick\" title=\"<PERSON>, 3rd Earl of Warwick\"><PERSON>, 3rd Earl of Warwick</a>, English nobleman and general (b. 1528)", "links": [{"title": "<PERSON>, 3rd Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Warwick"}]}, {"year": "1595", "text": "<PERSON>, English priest and poet (b. 1561)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" class=\"mw-redirect\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, English priest and poet (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" class=\"mw-redirect\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, English priest and poet (b. 1561)", "links": [{"title": "<PERSON> (Jesuit)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)"}]}, {"year": "1677", "text": "<PERSON><PERSON>, Dutch philosopher and scholar (b. 1632)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/Baruch_Spinoza\" title=\"Baruch Spinoza\"><PERSON><PERSON></a>, Dutch philosopher and scholar (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baruch_Spinoza\" title=\"Baruch Spinoza\"><PERSON><PERSON></a>, Dutch philosopher and scholar (b. 1632)", "links": [{"title": "Baruch Spinoz<PERSON>", "link": "https://wikipedia.org/wiki/Baruch_Spinoza"}]}, {"year": "1715", "text": "<PERSON>, 3rd Baron <PERSON>, English politician (b. 1637)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron Baltimore\"><PERSON>, 3rd Baron <PERSON></a>, English politician (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron Baltimore\"><PERSON>, 3rd Baron <PERSON></a>, English politician (b. 1637)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1730", "text": "<PERSON> <PERSON> (b. 1649)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (b. 1649)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, German jurist and diplomat (b. 1756)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and diplomat (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and diplomat (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, French general (b. 1781)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_de_Beauharnais\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>_Beauharnais\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_de_Beauharnais"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Indian queen and freedom fighter (b. 1778)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian queen and freedom fighter (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chen<PERSON>\"><PERSON><PERSON></a>, Indian queen and freedom fighter (b. 1778)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kit<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1800)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor Ni<PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1800)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nink%C5%8D"}]}, {"year": "1862", "text": "<PERSON><PERSON>, German poet and physician (b. 1786)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and physician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and physician (b. 1786)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, English-Australian politician, 3rd Premier of Tasmania (b. 1804)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1804)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1891", "text": "<PERSON>, American lieutenant and police officer (b. 1846)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and police officer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and police officer (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, last known Carolina parakeet (h. fl. 1885)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Incas_(parakeet)\" title=\"Incas (parakeet)\">Incas</a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/Carolina_parakeet\" title=\"Carolina parakeet\">Carolina parakeet</a> (h. <abbr title=\"floruit ('flourished' - known to have been active at a particular time or during a particular period)\">fl.</abbr><span style=\"white-space:nowrap;\"> 1885</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Incas_(parakeet)\" title=\"Incas (parakeet)\">Incas</a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/Carolina_parakeet\" title=\"Carolina parakeet\">Carolina parakeet</a> (h. <abbr title=\"floruit ('flourished' - known to have been active at a particular time or during a particular period)\">fl.</abbr><span style=\"white-space:nowrap;\"> 1885</span>)", "links": [{"title": "Incas (parakeet)", "link": "https://wikipedia.org/wiki/Incas_(parakeet)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endling"}, {"title": "Carolina parakeet", "link": "https://wikipedia.org/wiki/Carolina_parakeet"}]}, {"year": "1919", "text": "<PERSON>, German journalist and politician, Minister-President of Bavaria (b. 1867)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister-President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister-President of Bavaria\">Minister-President of Bavaria</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister-President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister-President of Bavaria\">Minister-President of Bavaria</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister-President of Bavaria", "link": "https://wikipedia.org/wiki/Minister-President_of_Bavaria"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Dutch physicist and academic, Nobel Prize laureate (b. 1853)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1853)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Nicaraguan rebel leader (b. 1895)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Augusto_C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan rebel leader (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusto_C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nicaraguan rebel leader (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Augusto_C%C3%A9sar_<PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American astronomer and academic (b. 1868)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian physician and academic, Nobel Prize laureate (b. 1891)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Hungarian-French racing driver (b. 1873)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-French racing driver (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-French racing driver (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ren<PERSON>_<PERSON>sz"}]}, {"year": "1945", "text": "<PERSON>, Scottish rugby player and runner (b. 1902)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player and runner (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player and runner (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Belgian journalist (b. 1911)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Streel\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Streel\" title=\"<PERSON>\"><PERSON></a>, Belgian journalist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Streel"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American composer (b. 1881)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English footballer (b. 1936)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American minister and activist (b. 1925)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> X\"><PERSON></a>, American minister and activist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American author and screenwriter (b. 1929)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian pathologist and pharmacologist, Nobel Prize laureate (b. 1898)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pathologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pathologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1972", "text": "<PERSON>, Chinese general and politician (b. 1914)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1914)\" title=\"<PERSON> (politician, born 1914)\"><PERSON></a>, Chinese general and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1914)\" title=\"<PERSON> (politician, born 1914)\"><PERSON></a>, Chinese general and politician (b. 1914)", "links": [{"title": "<PERSON> (politician, born 1914)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1914)"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian-American dancer and choreographer (b. 1891)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-American dancer and choreographer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-American dancer and choreographer (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, French cardinal (b. 1884)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, French cardinal (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, French cardinal (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Tisserant"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and businessman, co-founded <PERSON> (b. 1930)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German-Swiss author (b. 1914)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, German-Israeli historian and philosopher (b. 1897)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem\" title=\"G<PERSON><PERSON> Scholem\"><PERSON><PERSON><PERSON></a>, German-Israeli historian and philosopher (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem\" title=\"G<PERSON><PERSON> Scholem\"><PERSON><PERSON><PERSON></a>, German-Israeli historian and philosopher (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>holem"}]}, {"year": "1984", "text": "<PERSON>, Russian novelist and short story writer, Nobel Prize laureate (b. 1905)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1985", "text": "<PERSON>, South African-American actor (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American novelist (b. 1895)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian poet, critic, and academic (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet, critic, and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet, critic, and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Indian actress (b. 1936)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>uta<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (b. 1936)", "links": [{"title": "Nutan", "link": "https://wikipedia.org/wiki/Nutan"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Danish seismologist and geophysicist (b. 1888)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"In<PERSON>hmann\"><PERSON><PERSON></a>, Danish seismologist and geophysicist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"In<PERSON>\"><PERSON><PERSON></a>, Danish seismologist and geophysicist (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German general and pilot (b. 1913)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and pilot (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English dramatist (b. 1924)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dramatist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American pianist, composer, and conductor (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American biochemist and pharmacologist, Nobel Prize laureate (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Finnish soldier and pilot (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish soldier and pilot (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish soldier and pilot (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American baseball player and politician (b. 1930)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and politician (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English actor and producer (b. 1942)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Welsh footballer and manager (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Cuban author, screenwriter, and critic (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban author, screenwriter, and critic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban author, screenwriter, and critic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish painter, photographer, and sculptor (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_Beksi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter, photographer, and sculptor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_Beksi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter, photographer, and sculptor (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zdzis%C5%82aw_Beksi%C5%84ski"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American author and screenwriter, co-founded Milestone Media (b. 1962)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uff<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/Milestone_Media\" title=\"Milestone Media\">Milestone Media</a> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and screenwriter, co-founded <a href=\"https://wikipedia.org/wiki/Milestone_Media\" title=\"Milestone Media\">Milestone Media</a> (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Milestone Media", "link": "https://wikipedia.org/wiki/Milestone_Media"}]}, {"year": "2011", "text": "<PERSON>, American physician and activist (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, American general (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>tandler\" title=\"<PERSON><PERSON> <PERSON><PERSON>dler\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>dler\" title=\"<PERSON><PERSON> <PERSON><PERSON>dler\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1922)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>dler"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Swedish footballer (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Cuban-American baseball player (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON><PERSON>ri"}]}, {"year": "2014", "text": "<PERSON>, Australian snowboarder (b. 1985)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)\" title=\"<PERSON> (snowboarder)\"><PERSON></a>, Australian snowboarder (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)\" title=\"<PERSON> (snowboarder)\"><PERSON></a>, Australian snowboarder (b. 1985)", "links": [{"title": "<PERSON> (snowboarder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)"}]}, {"year": "2014", "text": "<PERSON>, German-American historian, playwright, and academic (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, playwright, and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, playwright, and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Russian general, pilot, and astronaut (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Aleksei_Gubarev\" title=\"Aleksei Gubarev\"><PERSON><PERSON><PERSON>uba<PERSON></a>, Russian general, pilot, and astronaut (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksei_Gubarev\" title=\"Aleksei Gubarev\"><PERSON><PERSON><PERSON></a>, Russian general, pilot, and astronaut (b. 1931)", "links": [{"title": "Aleksei Gubarev", "link": "https://wikipedia.org/wiki/Aleksei_Gubarev"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Iranian journalist and politician (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Sade<PERSON>_<PERSON>\" title=\"Sadeq <PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sade<PERSON>_<PERSON>\" title=\"Sad<PERSON>q <PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sadeq_Tabatabaei"}]}, {"year": "2015", "text": "<PERSON>, American trumpet player, composer, and educator (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and educator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and educator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Scottish-English captain and pilot (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Scottish-English captain and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Scottish-English captain and pilot (b. 1919)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_(pilot)"}]}, {"year": "2017", "text": "<PERSON>, Guinean teacher and politician (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Guinean teacher and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Guinean teacher and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "2018", "text": "<PERSON>, American evangelist (b. 1918)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American film director (b. 1924)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American musician and actor (b. 1942)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Colombian classical pianist (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Arboleda\" title=\"<PERSON><PERSON><PERSON> Arboleda\"><PERSON><PERSON><PERSON></a>, Colombian classical pianist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rboleda\" title=\"<PERSON><PERSON><PERSON> Arboleda\"><PERSON><PERSON><PERSON></a>, Colombian classical pianist (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rboleda"}]}, {"year": "2021", "text": "<PERSON>, Australian rugby league player (b. 1958)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>,  United States Army brigadier general and decorated veteran of the Vietnam War (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Army brigadier general and decorated veteran of the Vietnam War (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Army brigadier general and decorated veteran of the Vietnam War (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American Secret Service agent (b. 1932)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)\" title=\"<PERSON> (Secret Service)\"><PERSON></a>, American Secret Service agent (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)\" title=\"<PERSON> (Secret Service)\"><PERSON></a>, American Secret Service agent (b. 1932)", "links": [{"title": "<PERSON> (Secret Service)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Secret_Service)"}]}, {"year": "2025", "text": "<PERSON><PERSON>, American actress (b. 1946)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}]}}