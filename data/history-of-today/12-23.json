{"date": "December 23", "url": "https://wikipedia.org/wiki/December_23", "data": {"Events": [{"year": "484", "text": "The Arian Vandal Kingdom ceases its persecution of Nicene Christianity.", "html": "484 - The <a href=\"https://wikipedia.org/wiki/Arianism\" title=\"Arianism\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Vandal_Kingdom\" title=\"Vandal Kingdom\">Vandal Kingdom</a> ceases its persecution of <a href=\"https://wikipedia.org/wiki/Nicene_Christianity\" title=\"Nicene Christianity\">Nicene Christianity</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arianism\" title=\"Arianism\">Arian</a> <a href=\"https://wikipedia.org/wiki/Vandal_Kingdom\" title=\"Vandal Kingdom\">Vandal Kingdom</a> ceases its persecution of <a href=\"https://wikipedia.org/wiki/Nicene_Christianity\" title=\"Nicene Christianity\">Nicene Christianity</a>.", "links": [{"title": "Arianism", "link": "https://wikipedia.org/wiki/Arianism"}, {"title": "Vandal Kingdom", "link": "https://wikipedia.org/wiki/Vandal_Kingdom"}, {"title": "Nicene Christianity", "link": "https://wikipedia.org/wiki/Nicene_Christianity"}]}, {"year": "558", "text": "<PERSON><PERSON><PERSON> <PERSON> is crowned King of the Franks.", "html": "558 - <a href=\"https://wikipedia.org/wiki/Chlothar_I\" title=\"Chlothar I\"><PERSON><PERSON><PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Frankish_kings\" title=\"List of Frankish kings\">King of the Franks</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlot<PERSON>_I\" title=\"Chlothar I\"><PERSON>lot<PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Frankish_kings\" title=\"List of Frankish kings\">King of the Franks</a>.", "links": [{"title": "Chlothar I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "List of Frankish kings", "link": "https://wikipedia.org/wiki/List_of_Frankish_kings"}]}, {"year": "583", "text": "Maya queen <PERSON><PERSON> is crowned ruler of Palenque.", "html": "583 - Maya queen <a href=\"https://wikipedia.org/wiki/Yohl_Ik%27nal\" class=\"mw-redirect\" title=\"Yohl Ik'nal\"><PERSON><PERSON> <PERSON>'nal</a> is crowned ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Maya queen <a href=\"https://wikipedia.org/wiki/Yohl_Ik%27nal\" class=\"mw-redirect\" title=\"Yohl Ik'nal\"><PERSON><PERSON> <PERSON>'nal</a> is crowned ruler of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>l", "link": "https://wikipedia.org/wiki/Yohl_Ik%27nal"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "962", "text": "The Sack of Aleppo as part of the Arab-Byzantine wars: Under the future Emperor <PERSON><PERSON>, Byzantine troops storm the city of Aleppo.", "html": "962 - The <a href=\"https://wikipedia.org/wiki/Sack_of_Aleppo_(962)\" title=\"Sack of Aleppo (962)\">Sack of Aleppo</a> as part of the <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: Under the future <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Nikephoros_II_Phokas\" title=\"Nikephoros II Phokas\">Nice<PERSON> Phocas</a>, <a href=\"https://wikipedia.org/wiki/Byzantine_army\" title=\"Byzantine army\">Byzantine troops</a> storm the city of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sack_of_Aleppo_(962)\" title=\"Sack of Aleppo (962)\">Sack of Aleppo</a> as part of the <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: Under the future <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Emperor</a> <a href=\"https://wikipedia.org/wiki/Nikephoros_II_Phokas\" title=\"Nikephoros II Phokas\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Byzantine_army\" title=\"Byzantine army\">Byzantine troops</a> storm the city of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>.", "links": [{"title": "Sack of Aleppo (962)", "link": "https://wikipedia.org/wiki/Sack_of_Aleppo_(962)"}, {"title": "Arab-Byzantine wars", "link": "https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "Nikephoros II Phokas", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_II_<PERSON>okas"}, {"title": "Byzantine army", "link": "https://wikipedia.org/wiki/Byzantine_army"}, {"title": "Aleppo", "link": "https://wikipedia.org/wiki/Aleppo"}]}, {"year": "1299", "text": "The Ilkhanate ruler <PERSON><PERSON><PERSON> defeats a Mamluk army that opposes his invasion into Syria in the Battle of Wadi al-Khaznadar near Homs.", "html": "1299 - The <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Ilkhanate</a> ruler <a href=\"https://wikipedia.org/wiki/Ghazan\" title=\"Gha<PERSON>\"><PERSON><PERSON><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> army that opposes his invasion into Syria in the <a href=\"https://wikipedia.org/wiki/Battle_of_Wadi_al-Khaznadar\" title=\"Battle of Wadi al-Khaznadar\">Battle of Wadi al-Khaznadar</a> near <a href=\"https://wikipedia.org/wiki/Homs\" title=\"Homs\">Homs</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Ilkhanate</a> ruler <a href=\"https://wikipedia.org/wiki/Ghazan\" title=\"Ghazan\"><PERSON><PERSON><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> army that opposes his invasion into Syria in the <a href=\"https://wikipedia.org/wiki/Battle_of_Wadi_al-Khaznadar\" title=\"Battle of Wadi al-Khaznadar\">Battle of Wadi al-Khaznadar</a> near <a href=\"https://wikipedia.org/wiki/Homs\" title=\"Homs\">Homs</a>.", "links": [{"title": "Ilkhanate", "link": "https://wikipedia.org/wiki/Ilkhanate"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Mamluk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Battle of Wadi al-Khaznadar", "link": "https://wikipedia.org/wiki/Battle_of_Wadi_al-Khaznadar"}, {"title": "Homs", "link": "https://wikipedia.org/wiki/Homs"}]}, {"year": "1598", "text": "Arauco War: Governor of Chile <PERSON> is killed in the Battle of Curalaba by Mapuches led by <PERSON><PERSON><PERSON><PERSON>.", "html": "1598 - <a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: <a href=\"https://wikipedia.org/wiki/Governor_of_Chile\" class=\"mw-redirect\" title=\"Governor of Chile\">Governor of Chile</a> <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Garc%C3%ADa_%C3%93%C3%B1ez_de_Loyola\" title=\"<PERSON>\"><PERSON></a> is killed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Curalaba\" title=\"Battle of Curalaba\">Battle of Curalaba</a> by <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> led by <a href=\"https://wikipedia.org/wiki/Pelantaru\" class=\"mw-redirect\" title=\"Pelantaru\">Pelantaru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: <a href=\"https://wikipedia.org/wiki/Governor_of_Chile\" class=\"mw-redirect\" title=\"Governor of Chile\">Governor of Chile</a> <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Garc%C3%ADa_%C3%93%C3%B1ez_de_Loyola\" title=\"<PERSON>\"><PERSON></a> is killed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Curalaba\" title=\"Battle of Curalaba\">Battle of Curalaba</a> by <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> led by <a href=\"https://wikipedia.org/wiki/Pelantaru\" class=\"mw-redirect\" title=\"Pelantaru\">Pelantaru</a>.", "links": [{"title": "Arauco War", "link": "https://wikipedia.org/wiki/Arauco_War"}, {"title": "Governor of Chile", "link": "https://wikipedia.org/wiki/Governor_of_Chile"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Garc%C3%ADa_%C3%93%C3%B1ez_de_Loyola"}, {"title": "Battle of Curalaba", "link": "https://wikipedia.org/wiki/Battle_of_Curalaba"}, {"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}, {"title": "Pelantaru", "link": "https://wikipedia.org/wiki/Pelantaru"}]}, {"year": "1688", "text": "As part of the Glorious Revolution, King <PERSON> of England flees from England to Paris after being deposed in favor of his son-in-law and nephew, <PERSON> of Orange and his daughter <PERSON>.", "html": "1688 - As part of the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> flees from England to Paris after being deposed in favor of his son-in-law and nephew, <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"William III of England\"><PERSON> of Orange</a> and his daughter <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON></a>.", "no_year_html": "As part of the <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> flees from England to Paris after being deposed in favor of his son-in-law and nephew, <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> III of England\"><PERSON> Orange</a> and his daughter <a href=\"https://wikipedia.org/wiki/Mary_II_of_England\" class=\"mw-redirect\" title=\"Mary II of England\"><PERSON></a>.", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}]}, {"year": "1773", "text": "Moscow State Academy of Choreography was founded under the reign of <PERSON>. It is the second ballet school in Russia after Vaganova Academy of Russian Ballet.", "html": "1773 - <a href=\"https://wikipedia.org/wiki/Moscow_State_Academy_of_Choreography\" title=\"Moscow State Academy of Choreography\">Moscow State Academy of Choreography</a> was founded under the reign of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> II</a>. It is the second ballet school in Russia after <a href=\"https://wikipedia.org/wiki/Vaganova_Academy_of_Russian_Ballet\" title=\"Vaganova Academy of Russian Ballet\">Vaganova Academy of Russian Ballet</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moscow_State_Academy_of_Choreography\" title=\"Moscow State Academy of Choreography\">Moscow State Academy of Choreography</a> was founded under the reign of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> II</a>. It is the second ballet school in Russia after <a href=\"https://wikipedia.org/wiki/Vaganova_Academy_of_Russian_Ballet\" title=\"Vaganova Academy of Russian Ballet\">Vaganova Academy of Russian Ballet</a>.", "links": [{"title": "Moscow State Academy of Choreography", "link": "https://wikipedia.org/wiki/Moscow_State_Academy_of_Choreography"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vaganova Academy of Russian Ballet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ova_Academy_of_Russian_Ballet"}]}, {"year": "1783", "text": "<PERSON> resigns as commander-in-chief of the Continental Army at the Maryland State House in Annapolis, Maryland.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/George_Washington%27s_resignation_as_commander-in-chief\" title=\"<PERSON>'s resignation as commander-in-chief\">resigns as commander-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> at the <a href=\"https://wikipedia.org/wiki/Maryland_State_House\" title=\"Maryland State House\">Maryland State House</a> in <a href=\"https://wikipedia.org/wiki/Annapolis,_Maryland\" title=\"Annapolis, Maryland\">Annapolis, Maryland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/George_Washington%27s_resignation_as_commander-in-chief\" title=\"<PERSON>'s resignation as commander-in-chief\">resigns as commander-in-chief</a> of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> at the <a href=\"https://wikipedia.org/wiki/Maryland_State_House\" title=\"Maryland State House\">Maryland State House</a> in <a href=\"https://wikipedia.org/wiki/Annapolis,_Maryland\" title=\"Annapolis, Maryland\">Annapolis, Maryland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "<PERSON>'s resignation as commander-in-chief", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_resignation_as_commander-in-chief"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Maryland State House", "link": "https://wikipedia.org/wiki/Maryland_State_House"}, {"title": "Annapolis, Maryland", "link": "https://wikipedia.org/wiki/Annapolis,_Maryland"}]}, {"year": "1793", "text": "The Battle of Savenay: A decisive defeat of the royalist counter-revolutionaries in War in the Vendée during the French Revolution.", "html": "1793 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Savenay\" title=\"Battle of Savenay\">Battle of Savenay</a>: A decisive defeat of the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">royalist</a> counter-revolutionaries in <a href=\"https://wikipedia.org/wiki/War_in_the_Vend%C3%A9e\" title=\"War in the Vendée\">War in the Vendée</a> during the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Savenay\" title=\"Battle of Savenay\">Battle of Savenay</a>: A decisive defeat of the <a href=\"https://wikipedia.org/wiki/House_of_Bourbon\" title=\"House of Bourbon\">royalist</a> counter-revolutionaries in <a href=\"https://wikipedia.org/wiki/War_in_the_Vend%C3%A9e\" title=\"War in the Vendée\">War in the Vendée</a> during the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>.", "links": [{"title": "Battle of Savenay", "link": "https://wikipedia.org/wiki/Battle_of_Savenay"}, {"title": "House of Bourbon", "link": "https://wikipedia.org/wiki/House_of_Bourbon"}, {"title": "War in the Vendée", "link": "https://wikipedia.org/wiki/War_in_the_Vend%C3%A9e"}, {"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}]}, {"year": "1815", "text": "The novel Emma by <PERSON> is first published.", "html": "1815 - The novel <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(novel)\" title=\"<PERSON> (novel)\">Emma</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jane <PERSON>\"><PERSON></a> is first published.", "no_year_html": "The novel <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(novel)\" title=\"<PERSON> (novel)\">Emma</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is first published.", "links": [{"title": "Emma (novel)", "link": "https://wikipedia.org/wiki/<PERSON>_(novel)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "First day of the Constantinople Conference which resulted in agreement for political reforms in the Balkans.", "html": "1876 - First day of the <a href=\"https://wikipedia.org/wiki/Constantinople_Conference\" title=\"Constantinople Conference\">Constantinople Conference</a> which resulted in agreement for political reforms in the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a>.", "no_year_html": "First day of the <a href=\"https://wikipedia.org/wiki/Constantinople_Conference\" title=\"Constantinople Conference\">Constantinople Conference</a> which resulted in agreement for political reforms in the <a href=\"https://wikipedia.org/wiki/Balkans\" title=\"Balkans\">Balkans</a>.", "links": [{"title": "Constantinople Conference", "link": "https://wikipedia.org/wiki/Constantinople_Conference"}, {"title": "Balkans", "link": "https://wikipedia.org/wiki/Balkans"}]}, {"year": "1893", "text": "The opera <PERSON><PERSON> and <PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON> is first performed.", "html": "1893 - The opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_(opera)\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON> (opera)\"><PERSON><PERSON> and <PERSON><PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a> is first performed.", "no_year_html": "The opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_(opera)\" title=\"<PERSON><PERSON> and <PERSON><PERSON><PERSON> (opera)\"><PERSON><PERSON> and <PERSON><PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a> is first performed.", "links": [{"title": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> (opera)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_(opera)"}, {"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1905", "text": "The Tampere conference, where <PERSON> and <PERSON> meet for the first time, is held in Tampere, Finland.", "html": "1905 - The <a href=\"https://wikipedia.org/wiki/Tampere_conference_of_1905\" title=\"Tampere conference of 1905\">Tampere conference</a>, where <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet for the first time, is held in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere, Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tampere_conference_of_1905\" title=\"Tampere conference of 1905\">Tampere conference</a>, where <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet for the first time, is held in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere, Finland</a>.", "links": [{"title": "Tampere conference of 1905", "link": "https://wikipedia.org/wiki/Tampere_conference_of_1905"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lenin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}]}, {"year": "1913", "text": "The Federal Reserve Act is signed into law by President <PERSON>, creating the Federal Reserve System.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Federal_Reserve_Act\" title=\"Federal Reserve Act\">Federal Reserve Act</a> is signed into law by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, creating the <a href=\"https://wikipedia.org/wiki/Federal_Reserve_System\" class=\"mw-redirect\" title=\"Federal Reserve System\">Federal Reserve System</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Reserve_Act\" title=\"Federal Reserve Act\">Federal Reserve Act</a> is signed into law by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, creating the <a href=\"https://wikipedia.org/wiki/Federal_Reserve_System\" class=\"mw-redirect\" title=\"Federal Reserve System\">Federal Reserve System</a>.", "links": [{"title": "Federal Reserve Act", "link": "https://wikipedia.org/wiki/Federal_Reserve_Act"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Federal Reserve System", "link": "https://wikipedia.org/wiki/Federal_Reserve_System"}]}, {"year": "1914", "text": "World War I: Australian and New Zealand troops arrive in Cairo, Egypt.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Australian and New Zealand troops arrive in <a href=\"https://wikipedia.org/wiki/Cairo,_Egypt\" class=\"mw-redirect\" title=\"Cairo, Egypt\">Cairo, Egypt</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Australian and New Zealand troops arrive in <a href=\"https://wikipedia.org/wiki/Cairo,_Egypt\" class=\"mw-redirect\" title=\"Cairo, Egypt\">Cairo, Egypt</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Cairo, Egypt", "link": "https://wikipedia.org/wiki/Cairo,_Egypt"}]}, {"year": "1914", "text": "World War I: During the Battle of Sarikamish, Ottoman forces mistook one another for Russian troops. The following friendly fire incident leaves 2,000 Ottomans dead and many more wounded.", "html": "1914 - World War I: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarikamish\" title=\"Battle of Sarikamish\">Battle of Sarikamish</a>, Ottoman forces mistook one another for Russian troops. The following friendly fire incident leaves 2,000 Ottomans dead and many more wounded.", "no_year_html": "World War I: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Sarikamish\" title=\"Battle of Sarikamish\">Battle of Sarikamish</a>, Ottoman forces mistook one another for Russian troops. The following friendly fire incident leaves 2,000 Ottomans dead and many more wounded.", "links": [{"title": "Battle of Sarikamish", "link": "https://wikipedia.org/wiki/Battle_of_Sarikamish"}]}, {"year": "1916", "text": "World War I: Battle of Magdhaba: Allied forces defeat Turkish forces in the Sinai Peninsula.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/Battle_of_Magdhaba\" title=\"Battle of Magdhaba\">Battle of Magdhaba</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> forces defeat <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turkish</a> forces in the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Battle_of_Magdhaba\" title=\"Battle of Magdhaba\">Battle of Magdhaba</a>: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> forces defeat <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turkish</a> forces in the <a href=\"https://wikipedia.org/wiki/Sinai_Peninsula\" title=\"Sinai Peninsula\">Sinai Peninsula</a>.", "links": [{"title": "Battle of Magdhaba", "link": "https://wikipedia.org/wiki/Battle_of_Magdhaba"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Sinai Peninsula", "link": "https://wikipedia.org/wiki/Sinai_Peninsula"}]}, {"year": "1919", "text": "Sex Disqualification (Removal) Act 1919 becomes law in the United Kingdom.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Sex_Disqualification_(Removal)_Act_1919\" title=\"Sex Disqualification (Removal) Act 1919\">Sex Disqualification (Removal) Act 1919</a> becomes law in the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sex_Disqualification_(Removal)_Act_1919\" title=\"Sex Disqualification (Removal) Act 1919\">Sex Disqualification (Removal) Act 1919</a> becomes law in the United Kingdom.", "links": [{"title": "Sex Disqualification (Removal) Act 1919", "link": "https://wikipedia.org/wiki/Sex_Disqualification_(Removal)_Act_1919"}]}, {"year": "1936", "text": "Colombia becomes a signatory to the Buenos Aires copyright treaty.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires</a> <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires</a> <a href=\"https://wikipedia.org/wiki/Copyright\" title=\"Copyright\">copyright</a> <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a>.", "links": [{"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Buenos Aires Convention", "link": "https://wikipedia.org/wiki/Buenos_Aires_Convention"}, {"title": "Copyright", "link": "https://wikipedia.org/wiki/Copyright"}, {"title": "Treaty", "link": "https://wikipedia.org/wiki/Treaty"}]}, {"year": "1936", "text": "Spanish Civil War: The Spanish Republic legalizes the Regional Defence Council of Aragon.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spanish Republic</a> legalizes the <a href=\"https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon\" title=\"Regional Defence Council of Aragon\">Regional Defence Council of Aragon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spanish Republic</a> legalizes the <a href=\"https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon\" title=\"Regional Defence Council of Aragon\">Regional Defence Council of Aragon</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}, {"title": "Regional Defence Council of Aragon", "link": "https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon"}]}, {"year": "1941", "text": "World War II: After 15 days of fighting, the Imperial Japanese Army occupies Wake Island.", "html": "1941 - World War II: After <a href=\"https://wikipedia.org/wiki/Battle_of_Wake_Island\" title=\"Battle of Wake Island\">15 days of fighting</a>, the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> occupies <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a>.", "no_year_html": "World War II: After <a href=\"https://wikipedia.org/wiki/Battle_of_Wake_Island\" title=\"Battle of Wake Island\">15 days of fighting</a>, the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> occupies <a href=\"https://wikipedia.org/wiki/Wake_Island\" title=\"Wake Island\">Wake Island</a>.", "links": [{"title": "Battle of Wake Island", "link": "https://wikipedia.org/wiki/Battle_of_Wake_Island"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Wake Island", "link": "https://wikipedia.org/wiki/Wake_Island"}]}, {"year": "1947", "text": "The transistor is first demonstrated at Bell Laboratories.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Transistor\" title=\"Transistor\">transistor</a> is first demonstrated at <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Laboratories</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Transistor\" title=\"Transistor\">transistor</a> is first demonstrated at <a href=\"https://wikipedia.org/wiki/Bell_Labs\" title=\"Bell Labs\">Bell Laboratories</a>.", "links": [{"title": "Transistor", "link": "https://wikipedia.org/wiki/Transistor"}, {"title": "Bell Labs", "link": "https://wikipedia.org/wiki/Bell_Labs"}]}, {"year": "1948", "text": "Seven Japanese military and political leaders convicted of war crimes by the International Military Tribunal for the Far East are executed by Allied occupation authorities at Sugamo Prison in Tokyo, Japan.", "html": "1948 - Seven <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> military and political leaders convicted of war crimes by the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> are executed by Allied occupation authorities at <a href=\"https://wikipedia.org/wiki/Sugamo_Prison\" title=\"Sugamo Prison\">Sugamo Prison</a> in Tokyo, Japan.", "no_year_html": "Seven <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> military and political leaders convicted of war crimes by the <a href=\"https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East\" title=\"International Military Tribunal for the Far East\">International Military Tribunal for the Far East</a> are executed by Allied occupation authorities at <a href=\"https://wikipedia.org/wiki/Sugamo_Prison\" title=\"Sugamo Prison\">Sugamo Prison</a> in Tokyo, Japan.", "links": [{"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "International Military Tribunal for the Far East", "link": "https://wikipedia.org/wiki/International_Military_Tribunal_for_the_Far_East"}, {"title": "Sugamo Prison", "link": "https://wikipedia.org/wiki/Sugamo_Prison"}]}, {"year": "1950", "text": "General <PERSON> dies in a jeep accident and is replaced by General <PERSON> in the Eighth United States Army.", "html": "1950 - General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in a jeep accident and is replaced by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Eighth_United_States_Army\" class=\"mw-redirect\" title=\"Eighth United States Army\">Eighth United States Army</a>.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in a jeep accident and is replaced by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Eighth_United_States_Army\" class=\"mw-redirect\" title=\"Eighth United States Army\">Eighth United States Army</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Eighth United States Army", "link": "https://wikipedia.org/wiki/Eighth_United_States_Army"}]}, {"year": "1954", "text": "First successful kidney transplant is performed by <PERSON><PERSON> and <PERSON>.", "html": "1954 - First successful <a href=\"https://wikipedia.org/wiki/Kidney_transplantation\" title=\"Kidney transplantation\">kidney transplant</a> is performed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "First successful <a href=\"https://wikipedia.org/wiki/Kidney_transplantation\" title=\"Kidney transplantation\">kidney transplant</a> is performed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Kidney transplantation", "link": "https://wikipedia.org/wiki/Kidney_transplantation"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "The first film adaptation of <PERSON><PERSON><PERSON><PERSON>'s novel The Unknown Soldier, directed by <PERSON><PERSON>, premieres.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(1955_film)\" title=\"The Unknown Soldier (1955 film)\">first film adaptation</a> of <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON>na\" title=\"Väinö Linna\"><PERSON><PERSON><PERSON><PERSON></a>'s novel <i><a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)\" title=\"The Unknown Soldier (novel)\">The Unknown Soldier</a></i>, directed by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, premieres.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(1955_film)\" title=\"The Unknown Soldier (1955 film)\">first film adaptation</a> of <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON>na\" title=\"Väin<PERSON> Linna\"><PERSON><PERSON><PERSON><PERSON></a>'s novel <i><a href=\"https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)\" title=\"The Unknown Soldier (novel)\">The Unknown Soldier</a></i>, directed by <a href=\"https://wikipedia.org/wiki/Ed<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, premieres.", "links": [{"title": "The Unknown Soldier (1955 film)", "link": "https://wikipedia.org/wiki/The_Unknown_Soldier_(1955_film)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Linna"}, {"title": "The Unknown Soldier (novel)", "link": "https://wikipedia.org/wiki/The_Unknown_Soldier_(novel)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ed<PERSON>_<PERSON>ne"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON> née <PERSON> is murdered in the so-called \"oven homicide\" case in Krootila, Kokemäki, Finland.", "html": "1960 - <PERSON><PERSON><PERSON> née <PERSON> is murdered in the so-called \"<a href=\"https://wikipedia.org/wiki/Oven_homicide\" title=\"Oven homicide\">oven homicide</a>\" case in Krootila, <a href=\"https://wikipedia.org/wiki/Kokem%C3%A4ki\" title=\"Kokemä<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "<PERSON><PERSON><PERSON> née <PERSON> is murdered in the so-called \"<a href=\"https://wikipedia.org/wiki/Oven_homicide\" title=\"Oven homicide\">oven homicide</a>\" case in Krootila, <a href=\"https://wikipedia.org/wiki/Kokem%C3%A4ki\" title=\"Kokemä<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "Oven homicide", "link": "https://wikipedia.org/wiki/Oven_homicide"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kokem%C3%A4ki"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1968", "text": "The 82 sailors from the USS Pueblo are released after eleven months of internment in North Korea.", "html": "1968 - The 82 sailors from the <a href=\"https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)\" title=\"USS Pueblo (AGER-2)\">USS <i>Pueblo</i></a> are released after eleven months of internment in <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "no_year_html": "The 82 sailors from the <a href=\"https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)\" title=\"USS Pueblo (AGER-2)\">USS <i>Pueblo</i></a> are released after eleven months of internment in <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "links": [{"title": "USS Pueblo (AGER-2)", "link": "https://wikipedia.org/wiki/USS_Pueblo_(AGER-2)"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "1970", "text": "The North Tower of the World Trade Center in Manhattan, New York, New York is topped out at 417 metres (1,368 ft), making it the tallest building in the world.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/List_of_tenants_in_1_World_Trade_Center_(1971%E2%80%932001)\" class=\"mw-redirect\" title=\"List of tenants in 1 World Trade Center (1971-2001)\">North Tower</a> of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York, New York</a> is topped out at 417 metres (1,368 ft), making it the tallest building in the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/List_of_tenants_in_1_World_Trade_Center_(1971%E2%80%932001)\" class=\"mw-redirect\" title=\"List of tenants in 1 World Trade Center (1971-2001)\">North Tower</a> of the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> in <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a>, <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York, New York</a> is topped out at 417 metres (1,368 ft), making it the tallest building in the world.", "links": [{"title": "List of tenants in 1 World Trade Center (1971-2001)", "link": "https://wikipedia.org/wiki/List_of_tenants_in_1_World_Trade_Center_(1971%E2%80%932001)"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1970", "text": "The Democratic Republic of the Congo officially becomes a one-party state.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a> officially becomes a <a href=\"https://wikipedia.org/wiki/One-party_state\" title=\"One-party state\">one-party state</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a> officially becomes a <a href=\"https://wikipedia.org/wiki/One-party_state\" title=\"One-party state\">one-party state</a>.", "links": [{"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "One-party state", "link": "https://wikipedia.org/wiki/One-party_state"}]}, {"year": "1972", "text": "The Immaculate Reception is caught by <PERSON> to win the Pittsburgh Steelers their first ever playoff victory, after defeating the Oakland Raiders.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/Immaculate_Reception\" title=\"Immaculate Reception\">Immaculate Reception</a> is caught by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to win the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> their first ever playoff victory, after defeating the <a href=\"https://wikipedia.org/wiki/Oakland_Raiders\" title=\"Oakland Raiders\">Oakland Raiders</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Immaculate_Reception\" title=\"Immaculate Reception\">Immaculate Reception</a> is caught by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to win the <a href=\"https://wikipedia.org/wiki/Pittsburgh_Steelers\" title=\"Pittsburgh Steelers\">Pittsburgh Steelers</a> their first ever playoff victory, after defeating the <a href=\"https://wikipedia.org/wiki/Oakland_Raiders\" title=\"Oakland Raiders\">Oakland Raiders</a>.", "links": [{"title": "Immaculate Reception", "link": "https://wikipedia.org/wiki/Immaculate_Reception"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pittsburgh Steelers", "link": "https://wikipedia.org/wiki/Pittsburgh_Steelers"}, {"title": "Oakland Raiders", "link": "https://wikipedia.org/wiki/Oakland_Raiders"}]}, {"year": "1972", "text": "A 6.5 magnitude earthquake strikes the Nicaraguan capital of Managua killing more than 10,000.", "html": "1972 - A <a href=\"https://wikipedia.org/wiki/1972_Nicaragua_earthquake\" title=\"1972 Nicaragua earthquake\">6.5 magnitude earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> capital of <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a> killing more than 10,000.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1972_Nicaragua_earthquake\" title=\"1972 Nicaragua earthquake\">6.5 magnitude earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> capital of <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a> killing more than 10,000.", "links": [{"title": "1972 Nicaragua earthquake", "link": "https://wikipedia.org/wiki/1972_Nicaragua_earthquake"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Managua", "link": "https://wikipedia.org/wiki/Managua"}]}, {"year": "1972", "text": "The 16 survivors of the Andes flight disaster are rescued after 73 days, surviving by cannibalism.", "html": "1972 - The 16 survivors of the <a href=\"https://wikipedia.org/wiki/1972_Andes_flight_disaster\" class=\"mw-redirect\" title=\"1972 Andes flight disaster\">Andes flight disaster</a> are rescued after 73 days, surviving by cannibalism.", "no_year_html": "The 16 survivors of the <a href=\"https://wikipedia.org/wiki/1972_Andes_flight_disaster\" class=\"mw-redirect\" title=\"1972 Andes flight disaster\">Andes flight disaster</a> are rescued after 73 days, surviving by cannibalism.", "links": [{"title": "1972 Andes flight disaster", "link": "https://wikipedia.org/wiki/1972_Andes_flight_disaster"}]}, {"year": "1978", "text": "Alitalia Flight 4128 crashes into the Tyrrhenian Sea while on approach to Falcone Borsellino Airport in Palermo, Italy, killing 108.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Alitalia_Flight_4128\" title=\"Alitalia Flight 4128\">Alitalia Flight 4128</a> crashes into the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a> while on approach to <a href=\"https://wikipedia.org/wiki/Falcone_Borsellino_Airport\" class=\"mw-redirect\" title=\"Falcone Borsellino Airport\">Falcone Borsellino Airport</a> in <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing 108.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alitalia_Flight_4128\" title=\"Alitalia Flight 4128\">Alitalia Flight 4128</a> crashes into the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a> while on approach to <a href=\"https://wikipedia.org/wiki/Falcone_Borsellino_Airport\" class=\"mw-redirect\" title=\"Falcone Borsellino Airport\">Falcone Borsellino Airport</a> in <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>, killing 108.", "links": [{"title": "Alitalia Flight 4128", "link": "https://wikipedia.org/wiki/Alitalia_Flight_4128"}, {"title": "Tyrrhenian Sea", "link": "https://wikipedia.org/wiki/Tyrrhenian_Sea"}, {"title": "Falcone Borsellino Airport", "link": "https://wikipedia.org/wiki/Falcone_Borsellino_Airport"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1979", "text": "Soviet-Afghan War: Soviet Union forces occupy Kabul, the Afghan capital.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: Soviet Union forces occupy <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, the <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghan</a> capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: Soviet Union forces occupy <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a>, the <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghan</a> capital.", "links": [{"title": "Soviet-Afghan War", "link": "https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War"}, {"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1984", "text": "After experiencing an engine fire, Aeroflot Flight 3519 attempts to make an emergency landing at Krasnoyarsk International Airport but crashes, killing 110 of the 111 people on board.", "html": "1984 - After experiencing an engine fire, <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3519\" title=\"Aeroflot Flight 3519\">Aeroflot Flight 3519</a> attempts to make an emergency landing at <a href=\"https://wikipedia.org/wiki/Krasnoyarsk_International_Airport\" title=\"Krasnoyarsk International Airport\">Krasnoyarsk International Airport</a> but crashes, killing 110 of the 111 people on board.", "no_year_html": "After experiencing an engine fire, <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_3519\" title=\"Aeroflot Flight 3519\">Aeroflot Flight 3519</a> attempts to make an emergency landing at <a href=\"https://wikipedia.org/wiki/Krasnoyarsk_International_Airport\" title=\"Krasnoyarsk International Airport\">Krasnoyarsk International Airport</a> but crashes, killing 110 of the 111 people on board.", "links": [{"title": "Aeroflot Flight 3519", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_3519"}, {"title": "Krasnoyarsk International Airport", "link": "https://wikipedia.org/wiki/Krasnoyarsk_International_Airport"}]}, {"year": "1986", "text": "<PERSON>, piloted by <PERSON> and <PERSON><PERSON>, lands at Edwards Air Force Base in California becoming the first aircraft to fly non-stop around the world without aerial or ground refueling.", "html": "1986 - <i><a href=\"https://wikipedia.org/wiki/Rutan_Voyager\" title=\"Rutan Voyager\">Voyager</a></i>, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, lands at <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Edwards Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> becoming the first aircraft to fly non-stop around the world without <a href=\"https://wikipedia.org/wiki/Aerial_refueling\" title=\"Aerial refueling\">aerial</a> or ground refueling.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Rutan_Voyager\" title=\"Rutan Voyager\">Voyager</a></i>, piloted by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, lands at <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Edwards Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> becoming the first aircraft to fly non-stop around the world without <a href=\"https://wikipedia.org/wiki/Aerial_refueling\" title=\"Aerial refueling\">aerial</a> or ground refueling.", "links": [{"title": "Rutan Voyager", "link": "https://wikipedia.org/wiki/Rutan_Voyager"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Edwards Air Force Base", "link": "https://wikipedia.org/wiki/Edwards_Air_Force_Base"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Aerial refueling", "link": "https://wikipedia.org/wiki/Aerial_refueling"}]}, {"year": "1990", "text": "History of Slovenia: In a referendum, 88.5% of Slovenia's overall electorate vote for independence from Yugoslavia.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/History_of_Slovenia\" title=\"History of Slovenia\">History of Slovenia</a>: In a <a href=\"https://wikipedia.org/wiki/Slovenian_independence_referendum,_1990\" class=\"mw-redirect\" title=\"Slovenian independence referendum, 1990\">referendum</a>, 88.5% of <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>'s overall electorate vote for independence from <a href=\"https://wikipedia.org/wiki/Yugoslavia\" title=\"Yugoslavia\">Yugoslavia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_Slovenia\" title=\"History of Slovenia\">History of Slovenia</a>: In a <a href=\"https://wikipedia.org/wiki/Slovenian_independence_referendum,_1990\" class=\"mw-redirect\" title=\"Slovenian independence referendum, 1990\">referendum</a>, 88.5% of <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>'s overall electorate vote for independence from <a href=\"https://wikipedia.org/wiki/Yugoslavia\" title=\"Yugoslavia\">Yugoslavia</a>.", "links": [{"title": "History of Slovenia", "link": "https://wikipedia.org/wiki/History_of_Slovenia"}, {"title": "Slovenian independence referendum, 1990", "link": "https://wikipedia.org/wiki/Slovenian_independence_referendum,_1990"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "Yugoslavia", "link": "https://wikipedia.org/wiki/Yugoslavia"}]}, {"year": "2002", "text": "A U.S. MQ-1 Predator is shot down by an Iraqi MiG-25 in the first combat engagement between a drone and conventional aircraft.", "html": "2002 - A U.S. <a href=\"https://wikipedia.org/wiki/General_Atomics_MQ-1_Predator#Iraq\" title=\"General Atomics MQ-1 Predator\">MQ-1 Predator</a> is shot down by an Iraqi <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_MiG-25\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>vich MiG-25\">MiG-25</a> in the first combat engagement between a drone and conventional aircraft.", "no_year_html": "A U.S. <a href=\"https://wikipedia.org/wiki/General_Atomics_MQ-1_Predator#Iraq\" title=\"General Atomics MQ-1 Predator\">MQ-1 Predator</a> is shot down by an Iraqi <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-25\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-25\">MiG-25</a> in the first combat engagement between a drone and conventional aircraft.", "links": [{"title": "General Atomics MQ-1 Predator", "link": "https://wikipedia.org/wiki/General_Atomics_MQ-1_Predator#Iraq"}, {"title": "Miko<PERSON><PERSON><PERSON><PERSON><PERSON> MiG-25", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-25"}]}, {"year": "2003", "text": "An explosion at the PetroChina Chuandongbei natural gas field in Kai County, Chongqing, China, kills at least 234.", "html": "2003 - An explosion at the <a href=\"https://wikipedia.org/wiki/PetroChina\" title=\"PetroChina\">PetroChina</a> Chuandongbei natural gas field in <a href=\"https://wikipedia.org/wiki/Kai_County\" class=\"mw-redirect\" title=\"Kai County\">Kai County</a>, <a href=\"https://wikipedia.org/wiki/Chongqing\" title=\"Chongqing\">Chongqing</a>, China, kills at least 234.", "no_year_html": "An explosion at the <a href=\"https://wikipedia.org/wiki/PetroChina\" title=\"PetroChina\">PetroChina</a> Chuandongbei natural gas field in <a href=\"https://wikipedia.org/wiki/Kai_County\" class=\"mw-redirect\" title=\"Kai County\">Kai County</a>, <a href=\"https://wikipedia.org/wiki/Chongqing\" title=\"Chongqing\">Chongqing</a>, China, kills at least 234.", "links": [{"title": "PetroChina", "link": "https://wikipedia.org/wiki/PetroChina"}, {"title": "Kai County", "link": "https://wikipedia.org/wiki/Kai_County"}, {"title": "Chongqing", "link": "https://wikipedia.org/wiki/Chongqing"}]}, {"year": "2005", "text": "An Antonov An-140, Azerbaijan Airlines Flight 217 from Baku, Azerbaijan, to Aktau, Kazakhstan, heading across the Caspian Sea, crashes, killing 23 people.", "html": "2005 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-140\" title=\"Antonov An-140\">Antonov An-140</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_217\" title=\"Azerbaijan Airlines Flight 217\">Azerbaijan Airlines Flight 217</a> from <a href=\"https://wikipedia.org/wiki/Baku\" title=\"Baku\">Baku</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, to <a href=\"https://wikipedia.org/wiki/Aktau\" title=\"Aktau\">Akt<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, heading across the <a href=\"https://wikipedia.org/wiki/Caspian_Sea\" title=\"Caspian Sea\">Caspian Sea</a>, crashes, killing 23 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-140\" title=\"Antonov An-140\">Antonov An-140</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_217\" title=\"Azerbaijan Airlines Flight 217\">Azerbaijan Airlines Flight 217</a> from <a href=\"https://wikipedia.org/wiki/Baku\" title=\"Baku\">Baku</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, to <a href=\"https://wikipedia.org/wiki/Aktau\" title=\"Aktau\">Aktau</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, heading across the <a href=\"https://wikipedia.org/wiki/Caspian_Sea\" title=\"Caspian Sea\">Caspian Sea</a>, crashes, killing 23 people.", "links": [{"title": "Antonov An-140", "link": "https://wikipedia.org/wiki/Antonov_An-140"}, {"title": "Azerbaijan Airlines Flight 217", "link": "https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_217"}, {"title": "Baku", "link": "https://wikipedia.org/wiki/Baku"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Aktau", "link": "https://wikipedia.org/wiki/Aktau"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "Caspian Sea", "link": "https://wikipedia.org/wiki/Caspian_Sea"}]}, {"year": "2007", "text": "An agreement is made for the Kingdom of Nepal to be abolished and the country to become a federal republic with the Prime Minister becoming head of state.", "html": "2007 - An agreement is made for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Nepal\" title=\"Kingdom of Nepal\">Kingdom of Nepal</a> to be abolished and the country to become a <a href=\"https://wikipedia.org/wiki/Federal_republic\" title=\"Federal republic\">federal republic</a> with the <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Nepal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Nepal\">Prime Minister</a> becoming head of state.", "no_year_html": "An agreement is made for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Nepal\" title=\"Kingdom of Nepal\">Kingdom of Nepal</a> to be abolished and the country to become a <a href=\"https://wikipedia.org/wiki/Federal_republic\" title=\"Federal republic\">federal republic</a> with the <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Nepal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Nepal\">Prime Minister</a> becoming head of state.", "links": [{"title": "Kingdom of Nepal", "link": "https://wikipedia.org/wiki/Kingdom_of_Nepal"}, {"title": "Federal republic", "link": "https://wikipedia.org/wiki/Federal_republic"}, {"title": "List of Prime Ministers of Nepal", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Nepal"}]}, {"year": "2008", "text": "A coup d'état occurs in Guinea hours after the death of President <PERSON><PERSON><PERSON>.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/2008_Guinean_coup_d%27%C3%A9tat\" title=\"2008 Guinean coup d'état\">coup d'état</a> occurs in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> hours after the death of President <a href=\"https://wikipedia.org/wiki/Lansana_Cont%C3%A9\" title=\"<PERSON>ns<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2008_Guinean_coup_d%27%C3%A9tat\" title=\"2008 Guinean coup d'état\">coup d'état</a> occurs in <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> hours after the death of President <a href=\"https://wikipedia.org/wiki/Lansana_Cont%C3%A9\" title=\"<PERSON>ns<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "2008 Guinean coup d'état", "link": "https://wikipedia.org/wiki/2008_Guinean_coup_d%27%C3%A9tat"}, {"title": "Guinea", "link": "https://wikipedia.org/wiki/Guinea"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lansana_Cont%C3%A9"}]}, {"year": "2015", "text": "A bomb explodes at Istanbul's Sabiha Gökçen Airport, killing one airport cleaner. The Kurdistan Freedom Hawks claim responsibility for the attack four days later.", "html": "2015 - A bomb <a href=\"https://wikipedia.org/wiki/2015_Sabiha_G%C3%B6k%C3%A7en_Airport_bombing\" title=\"2015 Sabiha Gökçen Airport bombing\">explodes</a> at Istanbul's <a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_Airport\" class=\"mw-redirect\" title=\"Sabiha Gökçen Airport\">Sabiha Gökçen Airport</a>, killing one airport cleaner. The <a href=\"https://wikipedia.org/wiki/Kurdistan_Freedom_Hawks\" title=\"Kurdistan Freedom Hawks\">Kurdistan Freedom Hawks</a> claim responsibility for the attack four days later.", "no_year_html": "A bomb <a href=\"https://wikipedia.org/wiki/2015_Sabiha_G%C3%B6k%C3%A7en_Airport_bombing\" title=\"2015 Sabiha Gökçen Airport bombing\">explodes</a> at Istanbul's <a href=\"https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_Airport\" class=\"mw-redirect\" title=\"Sabiha Gökçen Airport\">Sabiha Gökçen Airport</a>, killing one airport cleaner. The <a href=\"https://wikipedia.org/wiki/Kurdistan_Freedom_Hawks\" title=\"Kurdistan Freedom Hawks\">Kurdistan Freedom Hawks</a> claim responsibility for the attack four days later.", "links": [{"title": "2015 Sabiha Gökçen Airport bombing", "link": "https://wikipedia.org/wiki/2015_Sabiha_G%C3%B6k%C3%A7en_Airport_bombing"}, {"title": "Sabiha Gökçen Airport", "link": "https://wikipedia.org/wiki/Sabiha_G%C3%B6k%C3%A7en_Airport"}, {"title": "Kurdistan Freedom Hawks", "link": "https://wikipedia.org/wiki/Kurdistan_Freedom_Hawks"}]}], "Births": [{"year": "968", "text": "Emperor <PERSON><PERSON><PERSON> of Song, emperor of the Song dynasty (d. 1022)", "html": "968 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON><PERSON> of Song\">Emperor <PERSON><PERSON><PERSON> of Song</a>, emperor of the Song dynasty (d. 1022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Song\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON><PERSON> of Song\">Emperor <PERSON><PERSON><PERSON> of Song</a>, emperor of the Song dynasty (d. 1022)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Song"}]}, {"year": "1173", "text": "<PERSON>, duke of Bavaria (d. 1231)", "html": "1173 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (d. 1231)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (d. 1231)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1513", "text": "<PERSON>, English scholar and diplomat (d. 1577)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English scholar and diplomat (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English scholar and diplomat (d. 1577)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}]}, {"year": "1525", "text": "<PERSON>, duke of Mecklenburg (d. 1576)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (d. 1576)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1573", "text": "<PERSON>, Italian painter, sculptor and architect (d. 1632)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, sculptor and architect (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter, sculptor and architect (d. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1582", "text": "<PERSON><PERSON><PERSON>, Italian organist and composer (d. 1663)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (d. 1663)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1544", "text": "<PERSON> of Saxony, only child and heiress of <PERSON>, Elector of Saxony (d. 1577)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, only child and heiress of <PERSON>, Elector of Saxony (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, only child and heiress of <PERSON>, Elector of Saxony (d. 1577)", "links": [{"title": "Anna of Saxony", "link": "https://wikipedia.org/wiki/Anna_of_Saxony"}]}, {"year": "1597", "text": "<PERSON>, German poet and composer (d. 1639)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer (d. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON> Emperor, Chinese emperor (d. 1627)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Tianqi_Emperor\" title=\"Tianqi Emperor\">Tianqi Emperor</a>, Chinese emperor (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tianqi_Emperor\" title=\"Tianqi Emperor\">Tianqi Emperor</a>, Chinese emperor (d. 1627)", "links": [{"title": "<PERSON><PERSON>qi Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1613", "text": "<PERSON>, Swedish field marshal and politician, Lord High Constable of Sweden (d. 1676)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden\" title=\"Lord High Constable of Sweden\">Lord High Constable of Sweden</a> (d. 1676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>el"}, {"title": "Lord High Constable of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_Sweden"}]}, {"year": "1621", "text": "<PERSON><PERSON><PERSON>, 1st Earl of Nottingham, English lawyer and politician, Lord Chancellor of England (d. 1682)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON><PERSON><PERSON>, 1st Earl of Nottingham\"><PERSON><PERSON><PERSON>, 1st Earl of Nottingham</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_England\" class=\"mw-redirect\" title=\"Lord Chancellor of England\">Lord Chancellor of England</a> (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Earl_of_Nottingham\" title=\"<PERSON><PERSON><PERSON>, 1st Earl of Nottingham\"><PERSON><PERSON><PERSON>, 1st Earl of Nottingham</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_England\" class=\"mw-redirect\" title=\"Lord Chancellor of England\">Lord Chancellor of England</a> (d. 1682)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Earl of Nottingham", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Earl_of_Nottingham"}, {"title": "Lord Chancellor of England", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_England"}]}, {"year": "1621", "text": "<PERSON>, English lawyer and judge (d. 1678)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, French composer (d. 1755)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON><PERSON><PERSON>, Indian emperor (d. 1751)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/Pam<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Pam<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian emperor (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pam<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Pam<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian emperor (d. 1751)", "links": [{"title": "Pamheiba", "link": "https://wikipedia.org/wiki/Pamheiba"}]}, {"year": "1713", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 3rd <PERSON><PERSON><PERSON><PERSON> (d. 1749)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/Mar<PERSON><PERSON>_Gondazaemon\" title=\"<PERSON>uy<PERSON> Gondazaemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 3rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gondazaemon\" title=\"<PERSON><PERSON><PERSON> Gondazaemon\"><PERSON><PERSON><PERSON> Gondazaemon</a>, Japanese sumo wrestler, the 3rd <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1749)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>emon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1732", "text": "<PERSON>, English businessman and inventor, invented the Water frame and Spinning frame (d. 1792)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and inventor, invented the <a href=\"https://wikipedia.org/wiki/Water_frame\" title=\"Water frame\">Water frame</a> and <a href=\"https://wikipedia.org/wiki/Spinning_frame\" title=\"Spinning frame\">Spinning frame</a> (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and inventor, invented the <a href=\"https://wikipedia.org/wiki/Water_frame\" title=\"Water frame\">Water frame</a> and <a href=\"https://wikipedia.org/wiki/Spinning_frame\" title=\"Spinning frame\">Spinning frame</a> (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Water frame", "link": "https://wikipedia.org/wiki/Water_frame"}, {"title": "Spinning frame", "link": "https://wikipedia.org/wiki/Spinning_frame"}]}, {"year": "1750", "text": "<PERSON> of Saxony (d. 1827)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (d. 1827)", "links": [{"title": "<PERSON> of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony"}]}, {"year": "1758", "text": "<PERSON>, American soldier and politician (d. 1834)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American soldier and politician (d. 1834)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1766", "text": "<PERSON>, Swedish physicist and chemist (d. 1852)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and chemist (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and chemist (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON> of Russia (d. 1825)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1825)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1782", "text": "<PERSON>, American lawyer, civil servant, politician, and businessperson (d. 1865)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, American lawyer, civil servant, politician, and businessperson (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Virginia_politician)\" title=\"<PERSON> (Virginia politician)\"><PERSON></a>, American lawyer, civil servant, politician, and businessperson (d. 1865)", "links": [{"title": "<PERSON> (Virginia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Virginia_politician)"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON>, French philologist, orientalist, and scholar (d. 1832)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Champo<PERSON>on\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philologist, orientalist, and scholar (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A7ois_Champollion\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philologist, orientalist, and scholar (d. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1793", "text": "<PERSON><PERSON>, emir of Afghanistan (d. 1863)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emir of Afghanistan (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, emir of Afghanistan (d. 1863)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, French author, critic, and academic (d. 1869)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, critic, and academic (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, critic, and academic (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, American religious leader, founder of the Latter Day Saint movement (d. 1844)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, founder of the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a> (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, founder of the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a> (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Latter Day Saint movement", "link": "https://wikipedia.org/wiki/Latter_Day_Saint_movement"}]}, {"year": "1807", "text": "<PERSON>, Spanish Roman Catholic archbishop and missionary (d. 1870)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Roman Catholic archbishop and missionary (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Roman Catholic archbishop and missionary (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, English zoologist (d. 1873)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, German Egyptologist (d. 1884)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Egyptologist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Egyptologist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Scottish-English author (d. 1904)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English author (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Smiles\"><PERSON></a>, Scottish-English author (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON>, French historian and statesman (d. 1904)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and statesman (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and statesman (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Dutch pastor and poet (d. 1889)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ten_Kate\" title=\"<PERSON> Kate\"><PERSON></a>, Dutch pastor and poet (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ten_Kate\" title=\"<PERSON>\"><PERSON></a>, Dutch pastor and poet (d. 1889)", "links": [{"title": "<PERSON> ten Kate", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ten_Kate"}]}, {"year": "1822", "text": "<PERSON>, German engineer (d. 1875)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON>, German poet and author (d. 1902)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and author (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, Slovene-Hungarian author and educator (d. 1917)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Murkovics\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene-Hungarian author and educator (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Murkovics\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene-Hungarian author and educator (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Murkovics"}]}, {"year": "1843", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 1924)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1854", "text": "<PERSON>, English botanist and author (d. 1926)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and author (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American lawyer, politician, and businessman (d. 1908)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessman (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessman (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American educator, school administrator, and businessperson (d. 1964)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, school administrator, and businessperson (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, school administrator, and businessperson (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businesswoman and philanthropist (d. 1919)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>am <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American businesswoman and philanthropist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Madam <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American businesswoman and philanthropist (d. 1919)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American painter (d. 1953)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Ukrainian-American engineer and academic (d. 1972)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American engineer and academic (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American engineer and academic (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, French illustrator, painter, and engraver (d. 1964)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator, painter, and engraver (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator, painter, and engraver (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English cricketer (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, New Zealand-American actress and broadcaster (d. 1994)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-American actress and broadcaster (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-American actress and broadcaster (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Italian lieutenant and author (d. 1957)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lieutenant and author (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Lam<PERSON>\"><PERSON></a>, Italian lieutenant and author (d. 1957)", "links": [{"title": "<PERSON> Lampedusa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American-Canadian supercentenarian (d. 2014)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian supercentenarian (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian supercentenarian (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, French actress and stage director (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and stage director (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and stage director (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American cartoonist (d. 1975)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American author and academic (d. 1990)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, American author and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Indian lawyer and politician, 5th Prime Minister of India (d. 1987)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1907", "text": "<PERSON>, Cape Verdean author and poet (d. 2005)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Cape Verdean author and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Cape Verdean author and poet (d. 2005)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1907", "text": "<PERSON>, American general and politician (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish Zionist leader (d. 1942)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish <a href=\"https://wikipedia.org/wiki/Zionism\" title=\"Zionism\">Zionist</a> leader (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish <a href=\"https://wikipedia.org/wiki/Zionism\" title=\"Zionism\">Zionist</a> leader (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Zionism", "link": "https://wikipedia.org/wiki/Zionism"}]}, {"year": "1908", "text": "<PERSON>, American economist (d. 1985)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Armenian-Canadian photographer (d. 2002)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>su<PERSON>_Ka<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-Canadian photographer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>su<PERSON>_Ka<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian-Canadian photographer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yousu<PERSON>_Karsh"}]}, {"year": "1910", "text": "<PERSON>, German general (d. 1961)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American actor (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2002)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1911", "text": "<PERSON><PERSON>, English-Danish physician and immunologist, Nobel Prize laureate (d. 1994)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kaj Je<PERSON>\"><PERSON><PERSON></a>, English-Danish physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Danish physician and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1912", "text": "<PERSON>, American organic chemist and academic (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organic chemist and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organic chemist and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Italian director and screenwriter (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Italian-American dancer and choreographer (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Greco\" title=\"<PERSON>\"><PERSON></a>, Italian-American dancer and choreographer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Greco\" title=\"<PERSON>\"><PERSON></a>, Italian-American dancer and choreographer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Greco"}]}, {"year": "1918", "text": "<PERSON>, German soldier, economist, and politician, 5th Chancellor of Germany (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, economist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier, economist, and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1919", "text": "<PERSON>, American general and pilot (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Canadian actor and director (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and director (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, French discus thrower, shot putter, and pianist (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French discus thrower, shot putter, and pianist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French discus thrower, shot putter, and pianist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Argentinian race car driver (d. 1954)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Onofre_Marim%C3%B3n\" title=\"Onofre Mari<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian race car driver (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onofre_Marim%C3%B3n\" title=\"Onofre Mari<PERSON>\"><PERSON>of<PERSON></a>, Argentinian race car driver (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onofre_Marim%C3%B3n"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian journalist and radio host (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnther_Schifter\" title=\"G<PERSON><PERSON><PERSON> Schifter\"><PERSON><PERSON><PERSON><PERSON></a>, Austrian journalist and radio host (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BC<PERSON>her_Schifter\" title=\"G<PERSON><PERSON><PERSON> Schifter\"><PERSON><PERSON><PERSON><PERSON>hifter</a>, Austrian journalist and radio host (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnt<PERSON>_Schifter"}]}, {"year": "1923", "text": "<PERSON>, American admiral and pilot, Medal of Honor recipient (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1924", "text": "<PERSON>, American basketball player and politician (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English author and politician (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, English publisher (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Unwin\"><PERSON><PERSON></a>, English publisher (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>win\" title=\"<PERSON><PERSON> Unwin\"><PERSON><PERSON></a>, English publisher (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>win"}]}, {"year": "1926", "text": "<PERSON>, American poet and essayist (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and essayist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and essayist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American singer-songwriter (d. 1988)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American jazz trumpet player, flugelhorn player, and singer (d. 1988)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>et <PERSON>\"><PERSON><PERSON></a>, American jazz trumpet player, flugelhorn player, and singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American jazz trumpet player, flugelhorn player, and singer (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chet_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American professional bowler (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional bowler (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional bowler (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American comedian and actor", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American soldier, academic, and diplomat, United States Ambassador to East Germany (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_East_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to East Germany\">United States Ambassador to East Germany</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_East_Germany\" class=\"mw-redirect\" title=\"United States Ambassador to East Germany\">United States Ambassador to East Germany</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to East Germany", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_East_Germany"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Emperor of Japan", "html": "1933 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Emperor of Japan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Emperor of Japan", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>hito"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American baseball player (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player and sportscaster (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English singer-songwriter (d. 1966)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter (d. 1966)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1935", "text": "<PERSON>, Malaysian footballer and manager (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American R&B singer (d. 1984)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American football player (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2020)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1937", "text": "<PERSON>, American screenwriter and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American painter, historian, and educator (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, historian, and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, historian, and educator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American computer scientist and engineer, co-developed the Transmission Control Protocol", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer, co-developed the <a href=\"https://wikipedia.org/wiki/Transmission_Control_Protocol\" title=\"Transmission Control Protocol\">Transmission Control Protocol</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer, co-developed the <a href=\"https://wikipedia.org/wiki/Transmission_Control_Protocol\" title=\"Transmission Control Protocol\">Transmission Control Protocol</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Transmission Control Protocol", "link": "https://wikipedia.org/wiki/Transmission_Control_Protocol"}]}, {"year": "1939", "text": "<PERSON>, American sculptor and painter (d. 1995)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Pakistani businessman and politician, 12th President of Pakistan (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian politician (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Scottish jazz singer (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish jazz singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish jazz singer (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian rugby league player (d. 1986)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American soul singer-songwriter (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Eugene_Record\" title=\"Eugene Record\"><PERSON></a>, American soul singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eugene_Record\" title=\"Eugene Record\"><PERSON></a>, American soul singer-songwriter (d. 2005)", "links": [{"title": "Eugene Record", "link": "https://wikipedia.org/wiki/Eugene_Record"}]}, {"year": "1941", "text": "<PERSON>, English businessman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "1941", "text": "<PERSON>, American folk singer-songwriter and musician (d. 1980)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and musician (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and musician (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian lawyer and politician, 25th Governor-General of Australia", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1943", "text": "<PERSON>, American baseball player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1943", "text": "<PERSON>, Russian-French mathematician and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-French mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-French mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American serial killer (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor, voice artist, and comedian", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, voice artist, and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "Queen <PERSON><PERSON><PERSON> of Sweden", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>_of_Sweden\" title=\"Queen <PERSON><PERSON><PERSON> of Sweden\">Queen <PERSON><PERSON><PERSON> of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>_of_Sweden\" title=\"Queen <PERSON><PERSON><PERSON> of Sweden\">Queen <PERSON><PERSON><PERSON> of Sweden</a>", "links": [{"title": "Queen <PERSON><PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>_of_Sweden"}]}, {"year": "1944", "text": "<PERSON>, American general", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Egyptian lawyer, judge, and politician, President of Egypt", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Egypt\" class=\"mw-redirect\" title=\"List of Presidents of Egypt\">President of Egypt</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Egypt\" class=\"mw-redirect\" title=\"List of Presidents of Egypt\">President of Egypt</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Presidents of Egypt", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Egypt"}]}, {"year": "1945", "text": "<PERSON>, English journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Slovak soprano and actress (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak soprano and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak soprano and actress (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edita_Gruberov%C3%A1"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English screenwriter, producer, and composer (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English screenwriter, producer, and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English screenwriter, producer, and composer (d. 2011)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1947", "text": "<PERSON>, American runner", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner", "links": [{"title": "<PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON>(runner)"}]}, {"year": "1948", "text": "<PERSON>, English politician, Minister of State for Europe", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1948", "text": "<PERSON>, American guitarist, composer, and journalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, composer, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, composer, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American football player and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American runner", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American screenwriter and producer (d. 2012)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>inhold_<PERSON>ege\" title=\"<PERSON>inhold Weege\"><PERSON><PERSON><PERSON></a>, American screenwriter and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>in<PERSON>_<PERSON>ege\" title=\"<PERSON>inhold Weege\"><PERSON><PERSON><PERSON></a>, American screenwriter and producer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American obstetrician and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American obstetrician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American obstetrician and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Baron <PERSON>, English general", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English general", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Spanish footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, South Korean author and educator", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean author and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English guitarist and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American journalist, publisher, activist, and pundit", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist, publisher, activist, and pundit", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist, publisher, activist, and pundit", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Estonian architect and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s_Alver"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American religious leader and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Gerrit_<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON>rit <PERSON><PERSON>\">G<PERSON><PERSON> <PERSON><PERSON></a>, American religious leader and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ger<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON>rit <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American religious leader and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerrit_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Estonian radio host and politician (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Raivo_J%C3%A4rvi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian radio host and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raivo_J%C3%A4rvi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian radio host and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raivo_J%C3%A4rvi"}]}, {"year": "1954", "text": "<PERSON>, American tennis player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish poet and playwright", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English-Australian singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Italian race car driver (d. 2001)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English guitarist and songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1957", "text": "<PERSON>, Canadian singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Australian rugby league player and businessman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williams"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Egyptian journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>zza<PERSON> Kam<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kam<PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Kenyan-English biologist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Kenyan-English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan-English biologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish model and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Filipino actress and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lo<PERSON>\"><PERSON><PERSON></a>, Filipino actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Belgian race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Romanian-German physicist and chemist, Nobel Prize laureate", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stefan Hell\"><PERSON></a>, Romanian-German physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stefan_Hell\" title=\"Stefan Hell\"><PERSON></a>, Romanian-German physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "Stefan Hell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1962", "text": "<PERSON>, South Korean director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-gyu\" title=\"<PERSON>-gyu\"><PERSON>yu</a>, South Korean director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yu\" title=\"<PERSON>gyu\"><PERSON>yu</a>, South Korean director, producer, and screenwriter", "links": [{"title": "<PERSON>gyu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yu"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Japanese wrestler and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and voice actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1963", "text": "<PERSON><PERSON>, German businessman", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Vedder\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Brazilian singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian-French singer-songwriter and model", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French singer-songwriter and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French singer-songwriter and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Jamaican-Canadian boxer, coach, and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American journalist, actress, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, actress, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American painter and animator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and animator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and animator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Puerto Rican-American photographer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_T<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_T<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_T<PERSON><PERSON>k"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Canadian speed skater and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>riona_LeMay_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Harris"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Scottish singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1971", "text": "<PERSON>, Canadian actor (d. 2010)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English banker, journalist, and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker, journalist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker, journalist, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>-<PERSON><PERSON><PERSON>, English model, actress, and author (d. 2017)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, actress, and author (d. 2017)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>venant\" title=\"Wim <PERSON>venant\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>ven<PERSON>\" title=\"Wim <PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON><PERSON><PERSON>ant"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Italian singer-songwriter and composer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Italian singer-songwriter and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Italian singer-songwriter and composer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1972", "text": "<PERSON>, Canadian actor, voice actor and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian Po<PERSON>\"><PERSON></a>, Canadian actor, voice actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian Po<PERSON>\"><PERSON></a>, Canadian actor, voice actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_<PERSON>za"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Ecuadorian footballer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ecuadorian footballer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-Swedish singer-songwriter, guitarist, and producer (d. 2004)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Swedish singer-songwriter, guitarist, and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-Swedish singer-songwriter, guitarist, and producer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Starlight\" title=\"Lady Starlight\"><PERSON> Starlight</a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Starlight\" title=\"Lady Starlight\"><PERSON> Starlight</a>, American singer-songwriter", "links": [{"title": "Lady Starlight", "link": "https://wikipedia.org/wiki/Lady_Starlight"}]}, {"year": "1976", "text": "<PERSON>, American hurdler and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Uzbek-Greek footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uzbek-Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uzbek-Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American wrestler and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English television presenter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(presenter)\" class=\"mw-redirect\" title=\"<PERSON> (presenter)\"><PERSON></a>, English television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(presenter)\" class=\"mw-redirect\" title=\"<PERSON> (presenter)\"><PERSON></a>, English television presenter", "links": [{"title": "<PERSON> (presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(presenter)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Alge_Crumpler\" title=\"Alge Crumpler\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alge_Crumpler\" title=\"Alge Crumpler\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alge_<PERSON>rumpler"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Norwegian trumpeter and composer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian trumpeter and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian trumpeter and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Finnish singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M%C3%A4enp%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4enp%C3%A4%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jari_M%C3%A4enp%C3%A4%C3%A4"}]}, {"year": "1977", "text": "<PERSON>, American basketball player and blogger", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Canadian-American singer-songwriter and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>o"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ale%C5%A1_Kotal%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ale%C5%A1_Kotal%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>š <PERSON>", "link": "https://wikipedia.org/wiki/Ale%C5%A1_Kotal%C3%ADk"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/V%C3%ADctor_Mart%C3%ADnez_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%ADctor_Mart%C3%ADnez_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Mart%C3%ADnez_(baseball)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Canadian swimmer, model, and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian swimmer, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian swimmer, model, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Warren"}]}, {"year": "1979", "text": "<PERSON>, Kenyan runner", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ii"}]}, {"year": "1979", "text": "<PERSON>, Swedish ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American model, television personality, and actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, television personality, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, television personality, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American author and educator", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese javelin thrower", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Murak<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Murak<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mu<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Mu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ami"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Correia\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Corre<PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ia"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Cuban boxer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Yuriorkis_Gamboa\" title=\"Yuriorkis Gamboa\"><PERSON><PERSON><PERSON></a>, Cuban boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuri<PERSON><PERSON>_Gamboa\" title=\"Yuriorkis Gamboa\"><PERSON><PERSON><PERSON></a>, Cuban boxer", "links": [{"title": "Yuriorkis Gamboa", "link": "https://wikipedia.org/wiki/Yuriorkis_Gamboa"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Japanese manga artist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Polish-Australian diver, explorer, photographer, and author (d. 2011)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Australian diver, explorer, photographer, and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Australian diver, explorer, photographer, and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentine footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Zbyn%C4%9Bk_Mich%C3%A1lek\" title=\"Zbyněk Michálek\"><PERSON>byn<PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zbyn%C4%9Bk_Mich%C3%A1lek\" title=\"Zbyněk Michálek\"><PERSON>byn<PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "Zbyněk Michálek", "link": "https://wikipedia.org/wiki/Zbyn%C4%9Bk_Mich%C3%A1lek"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Canadian-Croatian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-Croatian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-Croatian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1982", "text": "<PERSON>, Austrian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English runner", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ram%C3%ADrez"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Israeli singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English drummer and songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress and singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French motorcycle racer (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Beau_Champion\" title=\"Beau Champion\">Beau Champion</a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beau_Champion\" title=\"Beau Champion\"><PERSON> Champion</a>, Australian rugby league player", "links": [{"title": "Beau Champion", "link": "https://wikipedia.org/wiki/<PERSON>_Champion"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Bal%C3%A1zs_Dzsudzs%C3%A1k\" title=\"Balázs Dzsudzsák\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal%C3%A1zs_Dzsudzs%C3%A1k\" title=\"Balázs Dzsudzsák\"><PERSON><PERSON><PERSON><PERSON><PERSON>dz<PERSON></a>, Hungarian footballer", "links": [{"title": "Balázs D<PERSON>ák", "link": "https://wikipedia.org/wiki/Bal%C3%A1zs_Dzsudzs%C3%A1k"}]}, {"year": "1986", "text": "<PERSON><PERSON> <PERSON><PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, New Zealand rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4"}]}, {"year": "1988", "text": "<PERSON>, American beauty queen, Miss America 2013", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_America_2013\" title=\"Miss America 2013\">Miss America 2013</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_America_2013\" title=\"Miss America 2013\">Miss America 2013</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America 2013", "link": "https://wikipedia.org/wiki/Miss_America_2013"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Estonian painter and poet", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian painter and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Brice_Dja_Dj%C3%A9dj%C3%A9\" title=\"Brice Dja Djédjé\">Brice Dja Djédjé</a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brice_Dja_Dj%C3%A9dj%C3%A9\" title=\"Brice Dja Djédjé\"><PERSON>rice Dja Djédjé</a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brice_Dja_Dj%C3%A9dj%C3%A9"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, English snooker player ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English snooker player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English snooker player ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Tanzanian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mb<PERSON>_Samatta\" title=\"Mbwana Samatta\"><PERSON><PERSON><PERSON></a>, Tanzanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mb<PERSON>_Samatta\" title=\"Mbwana Samatta\"><PERSON><PERSON><PERSON></a>, Tanzanian footballer", "links": [{"title": "Mbwana Samatta", "link": "https://wikipedia.org/wiki/Mbwana_Samatta"}]}, {"year": "1992", "text": "<PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Alexander\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bart<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Serbian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luka_Jovi%C4%87"}]}, {"year": "1999", "text": "<PERSON> , Brazilian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> </a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> </a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Nigerian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Guinean-French basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean-French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guinean-French basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian actor and musician", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Finn <PERSON>\"><PERSON></a>, Canadian actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Finn <PERSON>\"><PERSON></a>, Canadian actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentine footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Facundo_<PERSON>\" title=\"Facundo <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>\" title=\"Facundo <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>acund<PERSON>", "link": "https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, German footballer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "423", "text": "<PERSON>, ruler of Northern Wei (b. 392)", "html": "423 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON> of Northern Wei\"><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> (b. 392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON> of Northern Wei\"><PERSON></a>, ruler of <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> (b. 392)", "links": [{"title": "Emperor <PERSON><PERSON> of Northern Wei", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Northern_Wei"}, {"title": "Northern Wei", "link": "https://wikipedia.org/wiki/Northern_Wei"}]}, {"year": "484", "text": "<PERSON><PERSON><PERSON>, Vandal king", "html": "484 - <a href=\"https://wikipedia.org/wiki/Huneric\" title=\"Huneric\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Huneric\" title=\"Huneric\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> king", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Huneric"}]}, {"year": "668", "text": "<PERSON> of Beth <PERSON>, bishop and saint (b. 594)", "html": "668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Beth_Q<PERSON>\" title=\"Gabriel of Beth Qustan\"><PERSON> of Beth Qustan</a>, bishop and saint (b. 594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Beth_Qust<PERSON>\" title=\"<PERSON> of Beth Qustan\"><PERSON> of Beth Qustan</a>, bishop and saint (b. 594)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "679", "text": "<PERSON><PERSON><PERSON> II, Frankish king (probable; b. 650)", "html": "679 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON>gobert II\"><PERSON><PERSON><PERSON> II</a>, Frankish king (probable; b. 650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Dagobert II\"><PERSON><PERSON><PERSON> II</a>, Frankish king (probable; b. 650)", "links": [{"title": "Dagobert II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}]}, {"year": "761", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish bishop (b. 700)", "html": "761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> bishop (b. 700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frank<PERSON> bishop (b. 700)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "889", "text": "<PERSON>, bishop of Constance", "html": "889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(bishop_of_Constance)\" title=\"<PERSON> (bishop of Constance)\"><PERSON> II</a>, bishop of <a href=\"https://wikipedia.org/wiki/Bishopric_of_Constance\" class=\"mw-redirect\" title=\"Bishopric of Constance\">Constance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_(bishop_of_Constance)\" title=\"<PERSON> (bishop of Constance)\"><PERSON> II</a>, bishop of <a href=\"https://wikipedia.org/wiki/Bishopric_of_Constance\" class=\"mw-redirect\" title=\"Bishopric of Constance\">Constance</a>", "links": [{"title": "<PERSON> (bishop of Constance)", "link": "https://wikipedia.org/wiki/<PERSON>_II_(bishop_of_Constance)"}, {"title": "Bishopric of Constance", "link": "https://wikipedia.org/wiki/Bishopric_of_Constance"}]}, {"year": "910", "text": "<PERSON><PERSON> of Preslav, Bulgarian missionary and scholar", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Naum\" title=\"<PERSON> Naum\"><PERSON><PERSON> of Preslav</a>, Bulgarian missionary and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Naum\" title=\"Saint Naum\"><PERSON><PERSON> of Preslav</a>, Bulgarian missionary and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Naum"}]}, {"year": "918", "text": "<PERSON>, king of East Francia (b. 890)", "html": "918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Francia</a> (b. 890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Francia</a> (b. 890)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}]}, {"year": "940", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 909)", "html": "940 - <a href=\"https://wikipedia.org/wiki/Ar-<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Ar-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ar-<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Ar-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>-<PERSON><PERSON>"}]}, {"year": "1172", "text": "<PERSON><PERSON>, Italian cardinal", "html": "1172 - <a href=\"https://wikipedia.org/wiki/Ugo_Ventimiglia\" title=\"Ugo Ventimiglia\"><PERSON><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ugo_Ventimiglia\" title=\"Ugo Ventimiglia\"><PERSON><PERSON></a>, Italian cardinal", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugo_Ventimiglia"}]}, {"year": "1193", "text": "<PERSON><PERSON>, patron saint of Iceland (b. 1133)", "html": "1193 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Saint <PERSON><PERSON>\"><PERSON><PERSON></a>, patron saint of Iceland (b. 1133)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, patron saint of Iceland (b. 1133)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1230", "text": "<PERSON><PERSON><PERSON><PERSON> of Navarre, queen of England (b. 1165)", "html": "1230 - <a href=\"https://wikipedia.org/wiki/Berengaria_of_Navarre\" title=\"Berengaria of Navarre\"><PERSON><PERSON><PERSON><PERSON> of Navarre</a>, queen of England (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berengaria_of_Navarre\" title=\"Berengaria of Navarre\"><PERSON><PERSON><PERSON><PERSON> of Navarre</a>, queen of England (b. 1165)", "links": [{"title": "Berengaria of Navarre", "link": "https://wikipedia.org/wiki/Berengaria_of_Navarre"}]}, {"year": "1304", "text": "<PERSON> of Habsburg, duchess regent of Bavaria  (b. 1253)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Habsburg\" title=\"Matilda of Habsburg\"><PERSON> of Habsburg</a>, duchess regent of Bavaria (b. 1253)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Habsburg\" title=\"Matilda of Habsburg\">Matilda of Habsburg</a>, duchess regent of Bavaria (b. 1253)", "links": [{"title": "<PERSON> of Habsburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Habsburg"}]}, {"year": "1383", "text": "<PERSON> of Bourbon, Queen of Bohemia (b. 1320)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON> of Bourbon, Queen of Bohemia\"><PERSON> of Bourbon, Queen of Bohemia</a> (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON> of Bourbon, Queen of Bohemia\"><PERSON>, Queen of Bohemia</a> (b. 1320)", "links": [{"title": "<PERSON> of Bourbon, Queen of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Bohemia"}]}, {"year": "1384", "text": "<PERSON>, ruler of Epirus", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, ruler of Epirus", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, ruler of Epirus", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_Preljubovi%C4%87"}]}, {"year": "1392", "text": "<PERSON> of Castile, duchess of York (b. 1355)", "html": "1392 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Castile,_Duchess_of_York\" title=\"Isabella of Castile, Duchess of York\">Isabella of Castile</a>, duchess of York (b. 1355)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Castile,_Duchess_of_York\" title=\"Isabella of Castile, Duchess of York\">Isabella of Castile</a>, duchess of York (b. 1355)", "links": [{"title": "<PERSON> of Castile, Duchess of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile,_Duchess_of_York"}]}, {"year": "1556", "text": "<PERSON>, English cleric, playwright, and educator (b. 1504)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, playwright, and educator (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric, playwright, and educator (b. 1504)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, German theologian (executed; date of birth unknown)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (executed; date of birth unknown)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (executed; date of birth unknown)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1575", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (b. 1531)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Nobutomo\" title=\"<PERSON><PERSON><PERSON> Nobutomo\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Nobutomo\" title=\"<PERSON><PERSON><PERSON> Nobutomo\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1531)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>butomo"}]}, {"year": "1588", "text": "<PERSON>, duke of Guise (b. 1550)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON></a>, duke of Guise (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON></a>, duke of Guise (b. 1550)", "links": [{"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_Guise"}]}, {"year": "1631", "text": "<PERSON>, English poet and playwright (b. 1563)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON>, Italian painter (b. 1552)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, French poet and academic (b. 1582)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and academic (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and academic (b. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>"}]}, {"year": "1652", "text": "<PERSON>, English minister and theologian (b. 1585)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister and theologian (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister and theologian (b. 1585)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}]}, {"year": "1675", "text": "<PERSON>, duc <PERSON>, French general and diplomat (b. 1602)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc <PERSON>\"><PERSON>, duc <PERSON></a>, French general and diplomat (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc <PERSON>\"><PERSON>, duc <PERSON></a>, French general and diplomat (b. 1602)", "links": [{"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_du<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1722", "text": "<PERSON>, French mathematician and academic (b. 1654)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON>, Scottish spy (b. 1725)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish spy (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish spy (b. 1725)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON><PERSON><PERSON>, Canadian nun and saint, founded Grey Nuns (b. 1701)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27Youville\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and saint, founded <a href=\"https://wikipedia.org/wiki/Grey_Nuns\" title=\"Grey Nuns\">Grey Nuns</a> (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Youville\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian nun and saint, founded <a href=\"https://wikipedia.org/wiki/Grey_Nuns\" title=\"Grey Nuns\">Grey Nuns</a> (b. 1701)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27Youville"}, {"title": "Grey Nuns", "link": "https://wikipedia.org/wiki/Grey_Nuns"}]}, {"year": "1779", "text": "<PERSON>, 3rd Earl of Bristol, English admiral and politician, Chief Secretary for Ireland (b. 1724)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Bristol\" title=\"<PERSON>, 3rd Earl of Bristol\"><PERSON>, 3rd Earl of Bristol</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bristol\" title=\"<PERSON>, 3rd Earl of Bristol\"><PERSON>, 3rd Earl of Bristol</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1724)", "links": [{"title": "<PERSON>, 3rd Earl of Bristol", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bristol"}, {"title": "Chief Secretary for Ireland", "link": "https://wikipedia.org/wiki/Chief_Secretary_for_Ireland"}]}, {"year": "1789", "text": "<PERSON><PERSON><PERSON>, French priest and educator (b. 1712)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27%C3%89p%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and educator (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27%C3%89p%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and educator (b. 1712)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>%27%C3%89p%C3%A9e"}]}, {"year": "1795", "text": "<PERSON>, English general and politician (b. 1730)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1730)\" title=\"<PERSON> (British Army officer, born 1730)\"><PERSON></a>, English general and politician (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer,_born_1730)\" title=\"<PERSON> (British Army officer, born 1730)\"><PERSON></a>, English general and politician (b. 1730)", "links": [{"title": "<PERSON> (British Army officer, born 1730)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer,_born_1730)"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON>, Swedish explorer and author (b. 1723)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish explorer and author (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish explorer and author (b. 1723)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, English economist and demographer (b. 1766)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and demographer (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and demographer (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American businessman and poker player (b. 1824)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and poker player (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and poker player (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English poet and philosopher (b. 1858)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and philosopher (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Constance_Naden"}]}, {"year": "1892", "text": "<PERSON>, Brigadier General in the Regular United States Army, brother in law to President <PERSON>.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brigadier General in the Regular United States Army, brother in law to President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brigadier General in the Regular United States Army, brother in law to President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English archbishop and academic (b. 1821)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Frederick_Temple\" title=\"Frederick Temple\">Frederick Temple</a>, English archbishop and academic (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_Temple\" title=\"Frederick Temple\">Frederick Temple</a>, English archbishop and academic (b. 1821)", "links": [{"title": "Frederick Temple", "link": "https://wikipedia.org/wiki/Frederick_Temple"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, last emperor of the Gaza Empire (b. c.1850)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Gungunhana\" title=\"Gungunhana\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, last emperor of the <a href=\"https://wikipedia.org/wiki/Gaza_Empire\" title=\"Gaza Empire\">Gaza Empire</a> (b. c.1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gungunhana\" title=\"Gungunhana\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, last emperor of the <a href=\"https://wikipedia.org/wiki/Gaza_Empire\" title=\"Gaza Empire\">Gaza Empire</a> (b. c.1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gun<PERSON>hana"}, {"title": "Gaza Empire", "link": "https://wikipedia.org/wiki/Gaza_Empire"}]}, {"year": "1912", "text": "<PERSON>, German anthropologist and academic (b. 1850)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and academic (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and academic (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Indian monk, missionary, and educator (b. 1856)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk, missionary, and educator (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk, missionary, and educator (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Turkish lieutenant and educator (b. 1906)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish lieutenant and educator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish lieutenant and educator (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American meteorologist and photographer (b. 1865)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist and photographer (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American meteorologist and photographer (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Indonesia-born Dutch pilot and engineer, designed the Fokker Dr.I and Fokker D.VII (b. 1890)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesia-born Dutch pilot and engineer, designed the <a href=\"https://wikipedia.org/wiki/Fokker_Dr.I\" title=\"Fokker Dr.I\">Fokker Dr.I</a> and <a href=\"https://wikipedia.org/wiki/Fokker_D.VII\" title=\"Fokker D.VII\">Fokker D.VII</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesia-born Dutch pilot and engineer, designed the <a href=\"https://wikipedia.org/wiki/Fokker_Dr.I\" title=\"Fokker Dr.I\">Fokker Dr.I</a> and <a href=\"https://wikipedia.org/wiki/Fokker_D.VII\" title=\"Fokker D.VII\">Fokker D.VII</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fokker Dr.I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dr.I"}, {"title": "Fokker D.VII", "link": "https://wikipedia.org/wiki/Fokker_D.VII"}]}, {"year": "1946", "text": "<PERSON>, American gynecologist and academic (b. 1873)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gynecologist and academic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gynecologist and academic (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Japanese general (b. 1883)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%8D\" title=\"<PERSON>\"><PERSON></a>, Japanese general (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%8D\" title=\"<PERSON>\"><PERSON></a>, Japanese general (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mut%C5%8D"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese general and politician, 40th Prime Minister of Japan (b. 1884)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Seishir%C5%8D_Itaga<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seishir%C5%8D_Itaga<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seishir%C5%8D_<PERSON><PERSON>ki"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1888)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Heitar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heitar%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heitar%C5%8D_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1878)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Japanese general (b. 1883)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese diplomat and politician, 32nd Prime Minister of Japan (b. 1878)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/K%C5%8Dki_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese diplomat and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8Dki_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese diplomat and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dki_<PERSON><PERSON>a"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1950", "text": "<PERSON>, Italian composer (b. 1878)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>,  Soviet general and politician, head of the People's Commissariat for Internal Affairs  (b. 1899)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet general and politician, head of the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NKVD\">People's Commissariat for Internal Affairs</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet general and politician, head of the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NK<PERSON>\">People's Commissariat for Internal Affairs</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavrentiy_Beria"}, {"title": "NKVD", "link": "https://wikipedia.org/wiki/NKVD"}]}, {"year": "1954", "text": "<PERSON>, French soldier and sculptor (b. 1897)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soldier and sculptor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soldier and sculptor (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Ich%C3%A9"}]}, {"year": "1961", "text": "<PERSON>, American author (b. 1875)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, German general (b. 1910)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor (b. 1886)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian lieutenant and politician, Prime Minister of Estonia in exile (b. 1890)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile\" class=\"mw-redirect\" title=\"Prime Minister of Estonia in exile\">Prime Minister of Estonia in exile</a> (b. 1890)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Warma"}, {"title": "Prime Minister of Estonia in exile", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia_in_exile"}]}, {"year": "1972", "text": "<PERSON>, Russian engineer, designed the Tupolev Tu-95 and Tupolev Tu-104 (b. 1888)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-95\" title=\"Tupolev Tu-95\">Tupolev Tu-95</a> and <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-104\" title=\"Tupolev Tu-104\">Tupolev Tu-104</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-95\" title=\"Tupolev Tu-95\">Tupolev Tu-95</a> and <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-104\" title=\"Tupolev Tu-104\">Tupolev Tu-104</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tupolev Tu-95", "link": "https://wikipedia.org/wiki/Tupolev_Tu-95"}, {"title": "Tupolev Tu-104", "link": "https://wikipedia.org/wiki/Tupolev_Tu-104"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American screenwriter, created <PERSON>uiding Light and As the World Turns (b. 1901)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Guiding_Light\" title=\"Guiding Light\">Guiding Light</a></i> and <i><a href=\"https://wikipedia.org/wiki/As_the_World_Turns\" title=\"As the World Turns\">As the World Turns</a></i> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Guiding_Light\" title=\"Guiding Light\">Guiding Light</a></i> and <i><a href=\"https://wikipedia.org/wiki/As_the_World_Turns\" title=\"As the World Turns\">As the World Turns</a></i> (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Guiding Light", "link": "https://wikipedia.org/wiki/Guiding_Light"}, {"title": "As the World Turns", "link": "https://wikipedia.org/wiki/As_the_World_Turns"}]}, {"year": "1979", "text": "<PERSON>, American-Italian art collector (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian art collector (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian art collector (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1920)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish painter and illustrator (b. 1910)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian author and playwright (b. 1896)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French interior designer (b. 1934)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French interior designer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French interior designer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincent_Fourcade"}]}, {"year": "1994", "text": "<PERSON>, English actor, director, and playwright (b. 1905)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and playwright (b. 1905)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1995", "text": "<PERSON><PERSON>, English actor (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pat<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Italian-American author and illustrator (b. 1927)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Orlando\"><PERSON></a>, Italian-American author and illustrator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Orlando\"><PERSON></a>, Italian-American author and illustrator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1924)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Danish-American comedian, pianist, and conductor (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American comedian, pianist, and conductor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American comedian, pianist, and conductor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Nigerian lawyer and politician, 3rd Governor of Oyo State (b. 1930)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oyo_State\" class=\"mw-redirect\" title=\"Governor of Oyo State\">Governor of Oyo State</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Oyo_State\" class=\"mw-redirect\" title=\"Governor of Oyo State\">Governor of Oyo State</a> (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>la_Ige"}, {"title": "Governor of Oyo State", "link": "https://wikipedia.org/wiki/Governor_of_Oyo_State"}]}, {"year": "2004", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 9th Prime Minister of India (b. 1921)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Hungarian footballer and manager (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti"}]}, {"year": "2005", "text": "<PERSON>, Chinese writer and politician, member of the Gang of Four (b. 1931)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer and politician, member of the <a href=\"https://wikipedia.org/wiki/Gang_of_Four\" title=\"Gang of Four\">Gang of Four</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer and politician, member of the <a href=\"https://wikipedia.org/wiki/Gang_of_Four\" title=\"Gang of Four\">Gang of Four</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gang of Four", "link": "https://wikipedia.org/wiki/Gang_of_Four"}]}, {"year": "2006", "text": "<PERSON>, English actor (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American pianist and composer (b. 1952)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English footballer (b. 1947)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1947)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "2007", "text": "<PERSON>, Jr., American physiologist and academic (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physiologist and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American physiologist and academic (b. 1924)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "2007", "text": "<PERSON>, American dancer and choreographer (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian pianist and composer (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American colonel, Medal of Honor recipient (b. 1939)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Tibetan general and politician (b. 1910)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>apoi_<PERSON>awang_Jigme\" title=\"Ngapoi Ngawang Jigme\"><PERSON><PERSON><PERSON></a>, Tibetan general and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ap<PERSON>_<PERSON>awang_Jigme\" title=\"Ngapoi Ngawang Jigme\"><PERSON><PERSON><PERSON></a>, Tibetan general and politician (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>awang_Jigme"}]}, {"year": "2009", "text": "<PERSON>, Belgian theologian and academic (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian theologian and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian theologian and academic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American soldier and pilot (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Indian lawyer and politician, 7th Chief Minister of Kerala (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chief Minister of Kerala", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Kerala"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish economist and politician (b. 1946)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ayd%C4%B1n_Menderes\" title=\"<PERSON><PERSON><PERSON><PERSON> Menderes\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish economist and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayd%C4%B1n_Menderes\" title=\"<PERSON><PERSON><PERSON><PERSON> Menderes\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish economist and politician (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayd%C4%B1n_Menderes"}]}, {"year": "2012", "text": "<PERSON>, American educator and murderer (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and murderer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and murderer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Brazilian mixed martial artist and kick-boxer (b. 1979)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist and kick-boxer (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist and kick-boxer (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Greek-American sculptor (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American sculptor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-American sculptor (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Russian general and weapons designer, designed the AK-47 rifle (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and weapons designer, designed the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47 rifle</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and weapons designer, designed the <a href=\"https://wikipedia.org/wiki/AK-47\" title=\"AK-47\">AK-47 rifle</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "AK-47", "link": "https://wikipedia.org/wiki/AK-47"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American saxophonist, composer, and educator (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and educator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and educator (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American drummer and composer (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian poet and educator (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/G._<PERSON><PERSON>_<PERSON>rud<PERSON>pa\" title=\"G. S. Shivarud<PERSON>pa\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and educator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G._<PERSON><PERSON>_<PERSON>rud<PERSON>pa\" title=\"G. S. Shivarud<PERSON>pa\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and educator (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American philanthropist and art collector (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, American philanthropist and art collector (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, American philanthropist and art collector (b. 1928)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(philanthropist)"}]}, {"year": "2014", "text": "<PERSON>, Canadian lawyer and author (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and author (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and author (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American statistician and academic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American pharmacologist and biochemist, Nobel Prize laureate (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2015", "text": "<PERSON>, English footballer and manager (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, French biologist, pharmacist, and academic (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French biologist, pharmacist, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French biologist, pharmacist, and academic (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Turkish admiral and politician, 18th Prime Minister of Turkey (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ulusu\" class=\"mw-redirect\" title=\"Bü<PERSON> Ulusu\"><PERSON><PERSON><PERSON></a>, Turkish admiral and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ulusu\" class=\"mw-redirect\" title=\"Bü<PERSON> Ulusu\"><PERSON><PERSON><PERSON></a>, Turkish admiral and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%BClent_Ulusu"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "2017", "text": "<PERSON>, Irish educator and politician (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American singer and guitarist (b. 1945)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leslie_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American writer (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American TikTok personality and stand-up comedian (b. 1979)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American TikTok personality and stand-up comedian (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American TikTok personality and stand-up comedian (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American performance artist (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.L\" title=\"<PERSON>.L\"><PERSON>.<PERSON></a>, American performance artist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.L\" title=\"William <PERSON>.L\"><PERSON>.<PERSON></a>, American performance artist (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>.L"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Indian director and screenwriter (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian director and screenwriter (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hya<PERSON>_Benegal"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Surinamese general and politician, 9th President of Suriname (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/D%C3%A9si_Bouterse\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese general and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9si_Bouterse\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Surinamese general and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9si_Bouterse"}, {"title": "President of Suriname", "link": "https://wikipedia.org/wiki/President_of_Suriname"}]}, {"year": "2024", "text": "<PERSON>, Swiss snowboarder (b. 1998)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss snowboarder (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss snowboarder (b. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}