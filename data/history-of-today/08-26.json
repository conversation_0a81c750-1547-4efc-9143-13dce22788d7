{"date": "August 26", "url": "https://wikipedia.org/wiki/August_26", "data": {"Events": [{"year": "683", "text": "<PERSON><PERSON><PERSON> I's army kills 11,000 people of Medina including notable Sahabas in Battle of al-Harrah.", "html": "683 - <a href=\"https://wikipedia.org/wiki/Yazi<PERSON>_<PERSON>\" title=\"Yazid I\"><PERSON>zi<PERSON> I</a>'s army kills 11,000 people of <a href=\"https://wikipedia.org/wiki/Medina\" title=\"Medina\">Medina</a> including notable <a href=\"https://wikipedia.org/wiki/Sahaba\" class=\"mw-redirect\" title=\"Sahaba\">Sa<PERSON>as</a> in <a href=\"https://wikipedia.org/wiki/Battle_of_al-Harrah\" class=\"mw-redirect\" title=\"Battle of al-Harrah\">Battle of al-Harrah</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"Yazid I\"><PERSON><PERSON><PERSON> I</a>'s army kills 11,000 people of <a href=\"https://wikipedia.org/wiki/Medina\" title=\"Medina\">Medina</a> including notable <a href=\"https://wikipedia.org/wiki/Sahaba\" class=\"mw-redirect\" title=\"Sahaba\">Sahabas</a> in <a href=\"https://wikipedia.org/wiki/Battle_of_al-Harrah\" class=\"mw-redirect\" title=\"Battle of al-Harrah\">Battle of al-Harrah</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Medina", "link": "https://wikipedia.org/wiki/Medina"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>haba"}, {"title": "Battle of al-Harrah", "link": "https://wikipedia.org/wiki/Battle_of_al-Harrah"}]}, {"year": "1071", "text": "The Seljuq Turks defeat the Byzantine army at the Battle of Manzikert, and soon gain control of most of Anatolia.", "html": "1071 - The <a href=\"https://wikipedia.org/wiki/Seljuq_Turks\" class=\"mw-redirect\" title=\"Seljuq Turks\">Seljuq Turks</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_army\" title=\"Byzantine army\">Byzantine army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Manzikert\" title=\"Battle of Manzikert\">Battle of Manzikert</a>, and soon gain control of most of <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Seljuq_Turks\" class=\"mw-redirect\" title=\"Seljuq Turks\">Seljuq Turks</a> defeat the <a href=\"https://wikipedia.org/wiki/Byzantine_army\" title=\"Byzantine army\">Byzantine army</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Manzikert\" title=\"Battle of Manzikert\">Battle of Manzikert</a>, and soon gain control of most of <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolia</a>.", "links": [{"title": "Seljuq Turks", "link": "https://wikipedia.org/wiki/Seljuq_Turks"}, {"title": "Byzantine army", "link": "https://wikipedia.org/wiki/Byzantine_army"}, {"title": "Battle of Manzikert", "link": "https://wikipedia.org/wiki/Battle_of_Manzikert"}, {"title": "Anatolia", "link": "https://wikipedia.org/wiki/Anatolia"}]}, {"year": "1278", "text": "<PERSON><PERSON><PERSON> of Hungary and <PERSON> of Germany defeat <PERSON><PERSON> of Bohemia in the Battle on the Marchfeld near Dürnkrut in (then) Moravia.", "html": "1278 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"La<PERSON>laus IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> defeat <a href=\"https://wikipedia.org/wiki/Ottokar_II_of_Bohemia\" title=\"Ottokar II of Bohemia\"><PERSON><PERSON> II of Bohemia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_on_the_Marchfeld\" title=\"Battle on the Marchfeld\">Battle on the Marchfeld</a> near <a href=\"https://wikipedia.org/wiki/D%C3%BCrnkrut,_Austria\" title=\"Dürnkrut, Austria\">Dürnkrut</a> in (then) <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"La<PERSON>laus IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> defeat <a href=\"https://wikipedia.org/wiki/Ottokar_II_of_Bohemia\" title=\"<PERSON>kar II of Bohemia\"><PERSON><PERSON> II of Bohemia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_on_the_Marchfeld\" title=\"Battle on the Marchfeld\">Battle on the Marchfeld</a> near <a href=\"https://wikipedia.org/wiki/D%C3%BCrnkrut,_Austria\" title=\"Dürnkrut, Austria\">Dürnkrut</a> in (then) <a href=\"https://wikipedia.org/wiki/Moravia\" title=\"Moravia\">Moravia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>laus_IV_of_Hungary"}, {"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}, {"title": "Ottokar II of Bohemia", "link": "https://wikipedia.org/wiki/Ottokar_II_of_Bohemia"}, {"title": "Battle on the Marchfeld", "link": "https://wikipedia.org/wiki/Battle_on_the_Marchfeld"}, {"title": "Dürnkrut, Austria", "link": "https://wikipedia.org/wiki/D%C3%BCrnkrut,_Austria"}, {"title": "Moravia", "link": "https://wikipedia.org/wiki/Moravia"}]}, {"year": "1303", "text": "Chittorgarh falls to the Delhi Sultanate.", "html": "1303 - <a href=\"https://wikipedia.org/wiki/Siege_of_Chittorgarh_(1303)\" title=\"Siege of Chittorgarh (1303)\">Chittorgarh falls</a> to the Delhi Sultanate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Chittorgarh_(1303)\" title=\"Siege of Chittorgarh (1303)\">Chittorgarh falls</a> to the Delhi Sultanate.", "links": [{"title": "Siege of Chittorgarh (1303)", "link": "https://wikipedia.org/wiki/Siege_of_Chittorgarh_(1303)"}]}, {"year": "1346", "text": "At the Battle of Crécy, an English army easily defeats a French one twice its size.", "html": "1346 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_Cr%C3%A9cy\" title=\"Battle of Crécy\">Battle of Crécy</a>, an English army easily defeats a French one twice its size.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_Cr%C3%A9cy\" title=\"Battle of Crécy\">Battle of Crécy</a>, an English army easily defeats a French one twice its size.", "links": [{"title": "Battle of Crécy", "link": "https://wikipedia.org/wiki/Battle_of_Cr%C3%A9cy"}]}, {"year": "1444", "text": "Battle of St. Jakob an der Birs: A vastly outnumbered force of Swiss Confederates is defeated by the <PERSON><PERSON><PERSON> (future <PERSON> of France) and his army of 'Armagnacs' near Basel.", "html": "1444 - <a href=\"https://wikipedia.org/wiki/Battle_of_St._Jakob_an_der_Birs\" title=\"Battle of St. Jakob an der Birs\">Battle of St. Jakob an der Birs</a>: A vastly outnumbered force of Swiss Confederates is defeated by the <PERSON><PERSON><PERSON> (future <a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\"><PERSON> of France</a>) and his army of 'Armagnacs' near <a href=\"https://wikipedia.org/wiki/Basel\" title=\"Basel\">Basel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_St._Jakob_an_der_Birs\" title=\"Battle of St. Jakob an der Birs\">Battle of St. Jakob an der Birs</a>: A vastly outnumbered force of Swiss Confederates is defeated by the <PERSON><PERSON><PERSON> (future <a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\"><PERSON> of France</a>) and his army of 'Armagnacs' near <a href=\"https://wikipedia.org/wiki/Basel\" title=\"Basel\">Basel</a>.", "links": [{"title": "Battle of St. Jakob an der Birs", "link": "https://wikipedia.org/wiki/Battle_of_St._<PERSON>_<PERSON>_<PERSON>_B<PERSON>"}, {"title": "Louis XI of France", "link": "https://wikipedia.org/wiki/Louis_XI_of_France"}, {"title": "Basel", "link": "https://wikipedia.org/wiki/Basel"}]}, {"year": "1542", "text": "<PERSON> Orellana crosses South America from Guayaquil on the Pacific coast to the mouth of the Amazon River on the Atlantic coast.", "html": "1542 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Orellana\" title=\"Francisco de Orellana\"><PERSON> Orellana</a> crosses South America from Guayaquil on the Pacific coast to the mouth of the Amazon River on the Atlantic coast.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Orellana\" title=\"Francisco de Orellana\"><PERSON> Orellana</a> crosses South America from Guayaquil on the Pacific coast to the mouth of the Amazon River on the Atlantic coast.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "Dutch-Portuguese War: Second Battle of San Salvador: The Dutch force the Spanish garrison at San Salvador (modern day Keelung, Taiwan) to surrender, ending the short-lived Spanish colony on Formosa and replacing it with a new Dutch administration.", "html": "1642 - <a href=\"https://wikipedia.org/wiki/Dutch%E2%80%93Portuguese_War\" title=\"Dutch-Portuguese War\">Dutch-Portuguese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_San_Salvador_(1642)\" title=\"Battle of San Salvador (1642)\">Second Battle of San Salvador</a>: The Dutch force the Spanish garrison at San Salvador (modern day <a href=\"https://wikipedia.org/wiki/Keelung\" title=\"Keelung\">Keelung</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>) to surrender, ending the short-lived <a href=\"https://wikipedia.org/wiki/Spanish_Formosa\" title=\"Spanish Formosa\">Spanish colony on Formosa</a> and replacing it with <a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">a new Dutch administration</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dutch%E2%80%93Portuguese_War\" title=\"Dutch-Portuguese War\">Dutch-Portuguese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_San_Salvador_(1642)\" title=\"Battle of San Salvador (1642)\">Second Battle of San Salvador</a>: The Dutch force the Spanish garrison at San Salvador (modern day <a href=\"https://wikipedia.org/wiki/Keelung\" title=\"Keelung\">Keelung</a>, <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>) to surrender, ending the short-lived <a href=\"https://wikipedia.org/wiki/Spanish_Formosa\" title=\"Spanish Formosa\">Spanish colony on Formosa</a> and replacing it with <a href=\"https://wikipedia.org/wiki/Dutch_Formosa\" title=\"Dutch Formosa\">a new Dutch administration</a>.", "links": [{"title": "Dutch-Portuguese War", "link": "https://wikipedia.org/wiki/Dutch%E2%80%93Portuguese_War"}, {"title": "Battle of San Salvador (1642)", "link": "https://wikipedia.org/wiki/Battle_of_San_Salvador_(1642)"}, {"title": "Keelung", "link": "https://wikipedia.org/wiki/Keelung"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Spanish Formosa", "link": "https://wikipedia.org/wiki/Spanish_Formosa"}, {"title": "Dutch Formosa", "link": "https://wikipedia.org/wiki/Dutch_Formosa"}]}, {"year": "1648", "text": "The Fronde: First Fronde: In the wake of the successful Battle of Lens, <PERSON>, Chief Minister of France, suddenly orders the arrest of the leaders of the Parlement of Paris, provoking the rest of Paris to break into insurrection and barricade the streets the next day.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/The_Fronde\" title=\"The Fronde\">The Fronde</a>: <a href=\"https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parlementary_Fronde_(1648-1649)\" title=\"The Fronde\">First Fronde</a>: In the wake of the successful <a href=\"https://wikipedia.org/wiki/Battle_of_Lens\" title=\"Battle of Lens\">Battle of Lens</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chief_minister_of_France\" title=\"Chief minister of France\">Chief Minister of France</a>, suddenly orders the arrest of the leaders of the <a href=\"https://wikipedia.org/wiki/Parlement_of_Paris\" title=\"Parlement of Paris\">Parlement of Paris</a>, provoking the rest of <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to <a href=\"https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parlementary_Fronde_(1648-1649)\" title=\"The Fronde\">break into insurrection and barricade the streets</a> the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Fronde\" title=\"The Fronde\">The Fronde</a>: <a href=\"https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parlementary_Fronde_(1648-1649)\" title=\"The Fronde\">First Fronde</a>: In the wake of the successful <a href=\"https://wikipedia.org/wiki/Battle_of_Lens\" title=\"Battle of Lens\">Battle of Lens</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chief_minister_of_France\" title=\"Chief minister of France\">Chief Minister of France</a>, suddenly orders the arrest of the leaders of the <a href=\"https://wikipedia.org/wiki/Parlement_of_Paris\" title=\"Parlement of Paris\">Parlement of Paris</a>, provoking the rest of <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to <a href=\"https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parlementary_Fronde_(1648-1649)\" title=\"The Fronde\">break into insurrection and barricade the streets</a> the next day.", "links": [{"title": "The Fronde", "link": "https://wikipedia.org/wiki/The_Fronde"}, {"title": "The Fronde", "link": "https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parle<PERSON>_Fronde_(1648-1649)"}, {"title": "Battle of Lens", "link": "https://wikipedia.org/wiki/Battle_of_Lens"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief minister of France", "link": "https://wikipedia.org/wiki/Chief_minister_of_France"}, {"title": "Parlement of Paris", "link": "https://wikipedia.org/wiki/Parlement_of_Paris"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "The Fronde", "link": "https://wikipedia.org/wiki/The_Fronde#First_Fronde,_the_Parle<PERSON>_Fronde_(1648-1649)"}]}, {"year": "1748", "text": "The first Lutheran denomination in North America, the Pennsylvania Ministerium, is founded in Philadelphia.", "html": "1748 - The first <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> denomination in North America, the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Ministerium\" title=\"Pennsylvania Ministerium\">Pennsylvania Ministerium</a>, is founded in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> denomination in North America, the <a href=\"https://wikipedia.org/wiki/Pennsylvania_Ministerium\" title=\"Pennsylvania Ministerium\">Pennsylvania Ministerium</a>, is founded in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "Lutheran", "link": "https://wikipedia.org/wiki/Lutheran"}, {"title": "Pennsylvania Ministerium", "link": "https://wikipedia.org/wiki/Pennsylvania_Ministerium"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1767", "text": "Jesuits all over Chile are arrested as the Spanish Empire suppresses the Society of Jesus.", "html": "1767 - <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuits</a> all over <a href=\"https://wikipedia.org/wiki/Colonial_Chile\" title=\"Colonial Chile\">Chile</a> are arrested as the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> <a href=\"https://wikipedia.org/wiki/Suppression_of_the_Society_of_Jesus\" title=\"Suppression of the Society of Jesus\">suppresses the Society of Jesus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuits</a> all over <a href=\"https://wikipedia.org/wiki/Colonial_Chile\" title=\"Colonial Chile\">Chile</a> are arrested as the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a> <a href=\"https://wikipedia.org/wiki/Suppression_of_the_Society_of_Jesus\" title=\"Suppression of the Society of Jesus\">suppresses the Society of Jesus</a>.", "links": [{"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}, {"title": "Colonial Chile", "link": "https://wikipedia.org/wiki/Colonial_Chile"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Suppression of the Society of Jesus", "link": "https://wikipedia.org/wiki/Suppression_of_the_Society_of_Jesus"}]}, {"year": "1768", "text": "Captain <PERSON> sets sail from England on board HMS Endeavour.", "html": "1768 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/First_voyage_of_<PERSON>_<PERSON>\" title=\"First voyage of James <PERSON>\">sets sail</a> from England on board <a href=\"https://wikipedia.org/wiki/HMS_Endeavour\" title=\"HMS Endeavour\">HMS <i>Endeavour</i></a>.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/First_voyage_of_<PERSON>_<PERSON>\" title=\"First voyage of <PERSON>\">sets sail</a> from England on board <a href=\"https://wikipedia.org/wiki/HMS_Endeavour\" title=\"HMS Endeavour\">HMS <i>Endeavour</i></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First voyage of <PERSON>", "link": "https://wikipedia.org/wiki/First_voyage_of_<PERSON>_<PERSON>"}, {"title": "HMS Endeavour", "link": "https://wikipedia.org/wiki/HMS_Endeavour"}]}, {"year": "1778", "text": "The first recorded ascent of Triglav, the highest mountain in Slovenia.", "html": "1778 - The first recorded ascent of <a href=\"https://wikipedia.org/wiki/Triglav\" title=\"Triglav\">Triglav</a>, the highest mountain in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>.", "no_year_html": "The first recorded ascent of <a href=\"https://wikipedia.org/wiki/Triglav\" title=\"Triglav\">Triglav</a>, the highest mountain in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>.", "links": [{"title": "Triglav", "link": "https://wikipedia.org/wiki/Triglav"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}]}, {"year": "1789", "text": "The Declaration of the Rights of Man and of the Citizen is approved by the National Constituent Assembly of France.", "html": "1789 - The <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Man_and_of_the_Citizen\" title=\"Declaration of the Rights of Man and of the Citizen\">Declaration of the Rights of Man and of the Citizen</a> is approved by the National Constituent Assembly of France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Man_and_of_the_Citizen\" title=\"Declaration of the Rights of Man and of the Citizen\">Declaration of the Rights of Man and of the Citizen</a> is approved by the National Constituent Assembly of France.", "links": [{"title": "Declaration of the Rights of Man and of the Citizen", "link": "https://wikipedia.org/wiki/Declaration_of_the_Rights_of_Man_and_of_the_Citizen"}]}, {"year": "1791", "text": "<PERSON> is granted a United States patent for the steamboat.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> is granted a United States patent for the <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> is granted a United States patent for the <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamboat</a>.", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)"}, {"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}]}, {"year": "1810", "text": "The former viceroy <PERSON> de Liniers of the Viceroyalty of the Río de la Plata is executed after the defeat of his counter-revolution.", "html": "1810 - The former viceroy <a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers\" class=\"mw-redirect\" title=\"Santiago de Liniers\">Santiago de Liniers</a> of the <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroyalty of the Río de la Plata</a> is executed after the defeat of his <a href=\"https://wikipedia.org/wiki/Liniers_Counter-revolution\" class=\"mw-redirect\" title=\"Liniers Counter-revolution\">counter-revolution</a>.", "no_year_html": "The former viceroy <a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers\" class=\"mw-redirect\" title=\"Santiago de Liniers\">Santiago de Liniers</a> of the <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroyalty of the Río de la Plata</a> is executed after the defeat of his <a href=\"https://wikipedia.org/wiki/Liniers_Counter-revolution\" class=\"mw-redirect\" title=\"Liniers Counter-revolution\">counter-revolution</a>.", "links": [{"title": "Santiago de Liniers", "link": "https://wikipedia.org/wiki/Santiago_de_Liniers"}, {"title": "Viceroyalty of the Río de la Plata", "link": "https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata"}, {"title": "Liniers Counter-revolution", "link": "https://wikipedia.org/wiki/Liniers_Counter-revolution"}]}, {"year": "1813", "text": "War of the Sixth Coalition: An impromptu battle takes place when French and Prussian-Russian forces accidentally run into each other near Liegnitz, Prussia (now Legnica, Poland).", "html": "1813 - <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: An <a href=\"https://wikipedia.org/wiki/Battle_of_Katzbach\" class=\"mw-redirect\" title=\"Battle of Katzbach\">impromptu battle</a> takes place when French and Prussian-Russian forces accidentally run into each other near <a href=\"https://wikipedia.org/wiki/Liegnitz\" class=\"mw-redirect\" title=\"Liegnitz\">Liegnitz</a>, Prussia (now Legnica, Poland).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a>: An <a href=\"https://wikipedia.org/wiki/Battle_of_Katzbach\" class=\"mw-redirect\" title=\"Battle of Katzbach\">impromptu battle</a> takes place when French and Prussian-Russian forces accidentally run into each other near <a href=\"https://wikipedia.org/wiki/Liegnitz\" class=\"mw-redirect\" title=\"Liegnitz\">Liegnitz</a>, Prussia (now Legnica, Poland).", "links": [{"title": "War of the Sixth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Sixth_Coalition"}, {"title": "Battle of Katzbach", "link": "https://wikipedia.org/wiki/Battle_of_Katzbach"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Liegnitz"}]}, {"year": "1814", "text": "Chilean War of Independence: Infighting between the rebel forces of <PERSON> and <PERSON> erupts in the Battle of Las Tres Acequias.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Chilean_War_of_Independence\" title=\"Chilean War of Independence\">Chilean War of Independence</a>: Infighting between the rebel forces of <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Bernardo_<PERSON>%27Higgins\" title=\"<PERSON>\"><PERSON></a> erupts in the <a href=\"https://wikipedia.org/wiki/Battle_of_Las_Tres_Acequias\" title=\"Battle of Las Tres Acequias\">Battle of Las Tres Acequias</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chilean_War_of_Independence\" title=\"Chilean War of Independence\">Chilean War of Independence</a>: Infighting between the rebel forces of <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Higgins\" title=\"<PERSON>\"><PERSON></a> erupts in the <a href=\"https://wikipedia.org/wiki/Battle_of_Las_Tres_Acequias\" title=\"Battle of Las Tres Acequias\">Battle of Las Tres Acequias</a>.", "links": [{"title": "Chilean War of Independence", "link": "https://wikipedia.org/wiki/Chilean_War_of_Independence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernardo_O%27Higgins"}, {"title": "Battle of Las Tres Acequias", "link": "https://wikipedia.org/wiki/Battle_of_Las_Tres_Acequias"}]}, {"year": "1833", "text": "The great 1833 Kathmandu-Bihar earthquake causes major damage in Nepal, northern India and Tibet, a total of 500 people perish.", "html": "1833 - The great <a href=\"https://wikipedia.org/wiki/1833_Kathmandu%E2%80%93Bihar_earthquake\" class=\"mw-redirect\" title=\"1833 Kathmandu-Bihar earthquake\">1833 Kathmandu-Bihar earthquake</a> causes major damage in Nepal, northern India and Tibet, a total of 500 people perish.", "no_year_html": "The great <a href=\"https://wikipedia.org/wiki/1833_Kathmandu%E2%80%93Bihar_earthquake\" class=\"mw-redirect\" title=\"1833 Kathmandu-Bihar earthquake\">1833 Kathmandu-Bihar earthquake</a> causes major damage in Nepal, northern India and Tibet, a total of 500 people perish.", "links": [{"title": "1833 Kathmandu-Bihar earthquake", "link": "https://wikipedia.org/wiki/1833_Kathmandu%E2%80%93B<PERSON>ar_earthquake"}]}, {"year": "1849", "text": "President <PERSON><PERSON> of the First Republic of Haiti has the Senate and Chamber of Deputies proclaim him the Emperor of Haiti, abolishing the Republic and inaugurating the Second Empire of Haiti.", "html": "1849 - President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Haiti_(1822%E2%80%931844)\" class=\"mw-redirect\" title=\"Republic of Haiti (1822-1844)\">First Republic of Haiti</a> has the Senate and Chamber of Deputies proclaim him the <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Haiti\" title=\"List of monarchs of Haiti\">Emperor of Haiti</a>, abolishing the Republic and inaugurating the <a href=\"https://wikipedia.org/wiki/Second_Empire_of_Haiti\" title=\"Second Empire of Haiti\">Second Empire of Haiti</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Haiti_(1822%E2%80%931844)\" class=\"mw-redirect\" title=\"Republic of Haiti (1822-1844)\">First Republic of Haiti</a> has the Senate and Chamber of Deputies proclaim him the <a href=\"https://wikipedia.org/wiki/List_of_monarchs_of_Haiti\" title=\"List of monarchs of Haiti\">Emperor of Haiti</a>, abolishing the Republic and inaugurating the <a href=\"https://wikipedia.org/wiki/Second_Empire_of_Haiti\" title=\"Second Empire of Haiti\">Second Empire of Haiti</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Republic of Haiti (1822-1844)", "link": "https://wikipedia.org/wiki/Republic_of_Haiti_(1822%E2%80%931844)"}, {"title": "List of monarchs of Haiti", "link": "https://wikipedia.org/wiki/List_of_monarchs_of_Haiti"}, {"title": "Second Empire of Haiti", "link": "https://wikipedia.org/wiki/Second_Empire_of_Haiti"}]}, {"year": "1863", "text": "The Swedish-language liberal newspaper Helsingfors Dagblad proposed the current blue-and-white cross flag as the flag of Finland.", "html": "1863 - The Swedish-language liberal newspaper <i>Helsingfors Dagblad</i> proposed the current blue-and-white cross flag as the <a href=\"https://wikipedia.org/wiki/Flag_of_Finland\" title=\"Flag of Finland\">flag of Finland</a>.", "no_year_html": "The Swedish-language liberal newspaper <i>Helsingfors Dagblad</i> proposed the current blue-and-white cross flag as the <a href=\"https://wikipedia.org/wiki/Flag_of_Finland\" title=\"Flag of Finland\">flag of Finland</a>.", "links": [{"title": "Flag of Finland", "link": "https://wikipedia.org/wiki/Flag_of_Finland"}]}, {"year": "1883", "text": "The 1883 eruption of Krakatoa begins its final, paroxysmal, stage.", "html": "1883 - The <a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">1883 eruption of Krakatoa</a> begins its final, paroxysmal, stage.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">1883 eruption of Krakatoa</a> begins its final, paroxysmal, stage.", "links": [{"title": "1883 eruption of Krakatoa", "link": "https://wikipedia.org/wiki/1883_eruption_of_Krakatoa"}]}, {"year": "1914", "text": "World War I: The German colony of Togoland surrenders to French and British forces after a 20-day campaign. Togoland is the first German colony to fall to Allied hands in World War I.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The German <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> of <a href=\"https://wikipedia.org/wiki/Togoland\" title=\"Togoland\">Togoland</a> surrenders to French and British forces after a <a href=\"https://wikipedia.org/wiki/Togoland_Campaign\" class=\"mw-redirect\" title=\"Togoland Campaign\">20-day campaign</a>. Togoland is the first German colony to fall to Allied hands in World War I.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The German <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> of <a href=\"https://wikipedia.org/wiki/Togoland\" title=\"Togoland\">Togoland</a> surrenders to French and British forces after a <a href=\"https://wikipedia.org/wiki/Togoland_Campaign\" class=\"mw-redirect\" title=\"Togoland Campaign\">20-day campaign</a>. Togoland is the first German colony to fall to Allied hands in World War I.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "Togoland", "link": "https://wikipedia.org/wiki/Togoland"}, {"title": "Togoland Campaign", "link": "https://wikipedia.org/wiki/Togoland_Campaign"}]}, {"year": "1914", "text": "World War I: During the retreat from Mons, the British II Corps commanded by General Sir <PERSON> fights a vigorous and successful defensive action at Le Cateau.", "html": "1914 - World War I: During the retreat from <a href=\"https://wikipedia.org/wiki/Battle_of_Mons\" title=\"Battle of Mons\"><PERSON><PERSON></a>, the British II Corps commanded by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a> fights a vigorous and successful defensive action at <a href=\"https://wikipedia.org/wiki/Battle_of_Le_Cateau\" title=\"Battle of Le Cateau\"><PERSON></a>.", "no_year_html": "World War I: During the retreat from <a href=\"https://wikipedia.org/wiki/Battle_of_Mons\" title=\"Battle of Mons\">Mon<PERSON></a>, the British II Corps commanded by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a> fights a vigorous and successful defensive action at <a href=\"https://wikipedia.org/wiki/Battle_of_Le_Cateau\" title=\"Battle of Le Cateau\"><PERSON></a>.", "links": [{"title": "Battle of Mons", "link": "https://wikipedia.org/wiki/Battle_of_Mons"}, {"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Le Cateau", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "The 19th amendment to United States Constitution, giving women the right to vote, is certified.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">19th amendment</a> to <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>, giving women the right to vote, is certified.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution\" title=\"Nineteenth Amendment to the United States Constitution\">19th amendment</a> to <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>, giving women the right to vote, is certified.", "links": [{"title": "Nineteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Nineteenth_Amendment_to_the_United_States_Constitution"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1922", "text": "Greco-Turkish War (1919-22): Turkish army launched what has come to be known to the Turks as the Great Offensive (Büyük Taarruz). The major Greek defense positions were overrun.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)\" class=\"mw-redirect\" title=\"Greco-Turkish War (1919-22)\">Greco-Turkish War (1919-22)</a>: Turkish army launched what has come to be known to the Turks as the <a href=\"https://wikipedia.org/wiki/Great_Offensive\" title=\"Great Offensive\">Great Offensive</a> (Büyük Taarruz). The major Greek defense positions were overrun.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)\" class=\"mw-redirect\" title=\"Greco-Turkish War (1919-22)\">Greco-Turkish War (1919-22)</a>: Turkish army launched what has come to be known to the Turks as the <a href=\"https://wikipedia.org/wiki/Great_Offensive\" title=\"Great Offensive\">Great Offensive</a> (Büyük Taarruz). The major Greek defense positions were overrun.", "links": [{"title": "Greco-Turkish War (1919-22)", "link": "https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)"}, {"title": "Great Offensive", "link": "https://wikipedia.org/wiki/Great_Offensive"}]}, {"year": "1936", "text": "Spanish Civil War: Santander falls to the nationalists and the republican interprovincial council is dissolved.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Santander\" title=\"Battle of Santander\">Santander falls</a> to the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">nationalists</a> and the republican <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Palencia_and_Burgos\" title=\"Interprovincial Council of Santander, Palencia and Burgos\">interprovincial council</a> is dissolved.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Santander\" title=\"Battle of Santander\">Santander falls</a> to the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">nationalists</a> and the republican <a href=\"https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Palencia_and_Burgos\" title=\"Interprovincial Council of Santander, Palencia and Burgos\">interprovincial council</a> is dissolved.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Battle of Santander", "link": "https://wikipedia.org/wiki/Battle_of_Santander"}, {"title": "Nationalist faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)"}, {"title": "Interprovincial Council of Santander, Palencia and Burgos", "link": "https://wikipedia.org/wiki/Interprovincial_Council_of_Santander,_Pa<PERSON>cia_and_Burgos"}]}, {"year": "1940", "text": "World War II: Chad becomes the first French colony to join the Allies under the administration of <PERSON>, France's first black colonial governor.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a> becomes the first French <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> to join the Allies under the administration of <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_%C3%89bou%C3%A9\" title=\"<PERSON>\"><PERSON></a>, France's first black colonial <a href=\"https://wikipedia.org/wiki/Governor\" title=\"Governor\">governor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a> becomes the first French <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> to join the Allies under the administration of <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_%C3%89bou%C3%A9\" title=\"<PERSON>\"><PERSON></a>, France's first black colonial <a href=\"https://wikipedia.org/wiki/Governor\" title=\"Governor\">governor</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Chad", "link": "https://wikipedia.org/wiki/Chad"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_%C3%89bou%C3%A9"}, {"title": "Governor", "link": "https://wikipedia.org/wiki/Governor"}]}, {"year": "1942", "text": "The Holocaust in Ukraine: At Chortkiv, the Ukrainian police and German Schutzpolizei deport two thousand Jews to Bełżec extermination camp. Five hundred of the sick and children are murdered on the spot. This continued until the next day.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: At <a href=\"https://wikipedia.org/wiki/Chortkiv\" title=\"Chortkiv\"><PERSON>rt<PERSON><PERSON></a>, the Ukrainian police and German <a href=\"https://wikipedia.org/wiki/Schutzpolizei_(Nazi_Germany)\" title=\"<PERSON><PERSON>tzpolizei (Nazi Germany)\">Schutzpolizei</a> deport two thousand Jews to <a href=\"https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp\" class=\"mw-redirect\" title=\"Bełżec extermination camp\">Bełżec extermination camp</a>. Five hundred of the sick and children are murdered on the spot. This continued until the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust_in_Ukraine\" title=\"The Holocaust in Ukraine\">The Holocaust in Ukraine</a>: At <a href=\"https://wikipedia.org/wiki/Chortkiv\" title=\"Chortkiv\">Chort<PERSON>v</a>, the Ukrainian police and German <a href=\"https://wikipedia.org/wiki/Schutzpolizei_(Nazi_Germany)\" title=\"<PERSON><PERSON>tzpolizei (Nazi Germany)\">Schutzpolizei</a> deport two thousand Jews to <a href=\"https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp\" class=\"mw-redirect\" title=\"Bełżec extermination camp\">Bełżec extermination camp</a>. Five hundred of the sick and children are murdered on the spot. This continued until the next day.", "links": [{"title": "The Holocaust in Ukraine", "link": "https://wikipedia.org/wiki/The_Holocaust_in_Ukraine"}, {"title": "Chortkiv", "link": "https://wikipedia.org/wiki/Chortkiv"}, {"title": "<PERSON><PERSON>tzpolizei (Nazi Germany)", "link": "https://wikipedia.org/wiki/Schutzpolizei_(Nazi_Germany)"}, {"title": "Bełżec extermination camp", "link": "https://wikipedia.org/wiki/Be%C5%82%C5%BCec_extermination_camp"}]}, {"year": "1944", "text": "World War II: <PERSON> enters Paris.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters Paris.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> enters Paris.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "The South African Border War starts with the battle at Omugulugwombashe.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/South_African_Border_War\" title=\"South African Border War\">South African Border War</a> starts with the battle at <a href=\"https://wikipedia.org/wiki/Omugulugwombashe\" title=\"<PERSON>mu<PERSON>lugwo<PERSON>she\">Omugulugwombashe</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_African_Border_War\" title=\"South African Border War\">South African Border War</a> starts with the battle at <a href=\"https://wikipedia.org/wiki/Omugulugwomba<PERSON>\" title=\"Omugulugwo<PERSON>she\">Omugulugwombashe</a>.", "links": [{"title": "South African Border War", "link": "https://wikipedia.org/wiki/South_African_Border_War"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mu<PERSON>lugwomba<PERSON>"}]}, {"year": "1969", "text": "Aeroflot Flight 1770 crashes while landing at Vnukovo International Airport, killing 16.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1770\" class=\"mw-redirect\" title=\"Aeroflot Flight 1770\">Aeroflot Flight 1770</a> crashes while landing at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a>, killing 16.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1770\" class=\"mw-redirect\" title=\"Aeroflot Flight 1770\">Aeroflot Flight 1770</a> crashes while landing at <a href=\"https://wikipedia.org/wiki/Vnukovo_International_Airport\" title=\"Vnukovo International Airport\">Vnukovo International Airport</a>, killing 16.", "links": [{"title": "Aeroflot Flight 1770", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_1770"}, {"title": "Vnukovo International Airport", "link": "https://wikipedia.org/wiki/Vnukovo_International_Airport"}]}, {"year": "1970", "text": "The fiftieth anniversary of American women being able to vote is marked by a nationwide Women's Strike for Equality.", "html": "1970 - The fiftieth anniversary of American women being able to vote is marked by a nationwide <a href=\"https://wikipedia.org/wiki/Women%27s_Strike_for_Equality\" title=\"Women's Strike for Equality\">Women's Strike for Equality</a>.", "no_year_html": "The fiftieth anniversary of American women being able to vote is marked by a nationwide <a href=\"https://wikipedia.org/wiki/Women%27s_Strike_for_Equality\" title=\"Women's Strike for Equality\">Women's Strike for Equality</a>.", "links": [{"title": "Women's Strike for Equality", "link": "https://wikipedia.org/wiki/Women%27s_Strike_for_Equality"}]}, {"year": "1972", "text": "The Games of the XX Olympiad open in Munich, West Germany.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/1972_Summer_Olympics\" title=\"1972 Summer Olympics\">Games of the XX Olympiad</a> open in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1972_Summer_Olympics\" title=\"1972 Summer Olympics\">Games of the XX Olympiad</a> open in <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a>.", "links": [{"title": "1972 Summer Olympics", "link": "https://wikipedia.org/wiki/1972_Summer_Olympics"}, {"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}]}, {"year": "1977", "text": "The Charter of the French Language is adopted by the National Assembly of Quebec.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/Charter_of_the_French_Language\" title=\"Charter of the French Language\">Charter of the French Language</a> is adopted by the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Quebec\" title=\"National Assembly of Quebec\">National Assembly of Quebec</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Charter_of_the_French_Language\" title=\"Charter of the French Language\">Charter of the French Language</a> is adopted by the <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Quebec\" title=\"National Assembly of Quebec\">National Assembly of Quebec</a>.", "links": [{"title": "Charter of the French Language", "link": "https://wikipedia.org/wiki/Charter_of_the_French_Language"}, {"title": "National Assembly of Quebec", "link": "https://wikipedia.org/wiki/National_Assembly_of_Quebec"}]}, {"year": "1978", "text": "Papal conclave: <PERSON><PERSON> is elected as Pope <PERSON>.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Papal_conclave,_August_1978\" class=\"mw-redirect\" title=\"Papal conclave, August 1978\">Papal conclave</a>: <PERSON><PERSON> is elected as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Papal_conclave,_August_1978\" class=\"mw-redirect\" title=\"Papal conclave, August 1978\">Papal conclave</a>: <PERSON><PERSON> is elected as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> I</a>.", "links": [{"title": "Papal conclave, August 1978", "link": "https://wikipedia.org/wiki/Papal_conclave,_August_1978"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "After <PERSON> plants a bomb at Harvey's Resort Hotel in Stateline, Nevada, in the United States, the FBI inadvertently detonates the bomb during its disarming.", "html": "1980 - After <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Resort_Hotel_bombing\" title=\"Harvey's Resort Hotel bombing\">plants a bomb</a> at <a href=\"https://wikipedia.org/wiki/Harvey%27s_Resort_Hotel\" class=\"mw-redirect\" title=\"Harvey's Resort Hotel\">Harvey's Resort Hotel</a> in <a href=\"https://wikipedia.org/wiki/Stateline,_Nevada\" title=\"Stateline, Nevada\">Stateline, Nevada</a>, in the United States, the FBI inadvertently detonates the bomb during its disarming.", "no_year_html": "After <PERSON> B<PERSON>ges <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Resort_Hotel_bombing\" title=\"Harvey's Resort Hotel bombing\">plants a bomb</a> at <a href=\"https://wikipedia.org/wiki/Harvey%27s_Resort_Hotel\" class=\"mw-redirect\" title=\"Harvey's Resort Hotel\">Harvey's Resort Hotel</a> in <a href=\"https://wikipedia.org/wiki/Stateline,_Nevada\" title=\"Stateline, Nevada\">Stateline, Nevada</a>, in the United States, the FBI inadvertently detonates the bomb during its disarming.", "links": [{"title": "Harvey's Resort Hotel bombing", "link": "https://wikipedia.org/wiki/Harvey%27s_Resort_Hotel_bombing"}, {"title": "Harvey's Resort Hotel", "link": "https://wikipedia.org/wiki/Harvey%27s_Resort_Hotel"}, {"title": "Stateline, Nevada", "link": "https://wikipedia.org/wiki/Stateline,_Nevada"}]}, {"year": "1993", "text": "Sakha Avia Flight 301 crashes on approach to Aldan Airport, killing all 24 aboard.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Sakha_Avia_Flight_301\" title=\"Sakha Avia Flight 301\">Sakha Avia Flight 301</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Aldan_Airport\" title=\"Aldan Airport\">Aldan Airport</a>, killing all 24 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sakha_Avia_Flight_301\" title=\"Sakha Avia Flight 301\">Sakha Avia Flight 301</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Aldan_Airport\" title=\"Aldan Airport\">Aldan Airport</a>, killing all 24 aboard.", "links": [{"title": "Sakha Avia Flight 301", "link": "https://wikipedia.org/wiki/Sakha_Avia_Flight_301"}, {"title": "Aldan Airport", "link": "https://wikipedia.org/wiki/Aldan_Airport"}]}, {"year": "1997", "text": "<PERSON><PERSON> massacre occurs in Algeria, leaving 60 to 100 people dead.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>i_Ali_massacre\" title=\"Beni Ali massacre\">Beni Ali massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, leaving 60 to 100 people dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>i_Ali_massacre\" title=\"Beni Ali massacre\">Beni Ali massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>, leaving 60 to 100 people dead.", "links": [{"title": "<PERSON><PERSON> massacre", "link": "https://wikipedia.org/wiki/Beni_Ali_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "1998", "text": "The first flight of the Boeing Delta III ends in disaster 75 seconds after liftoff resulting in the loss of the Galaxy X  communications satellite.", "html": "1998 - The first flight of the Boeing <a href=\"https://wikipedia.org/wiki/Delta_III\" title=\"Delta III\">Delta III</a> ends in disaster 75 seconds after liftoff resulting in the loss of the Galaxy X communications satellite.", "no_year_html": "The first flight of the Boeing <a href=\"https://wikipedia.org/wiki/Delta_III\" title=\"Delta III\">Delta III</a> ends in disaster 75 seconds after liftoff resulting in the loss of the Galaxy X communications satellite.", "links": [{"title": "Delta III", "link": "https://wikipedia.org/wiki/Delta_III"}]}, {"year": "1999", "text": "Russia begins the Second Chechen War in response to the Invasion of Dagestan by the Islamic International Peacekeeping Brigade.", "html": "1999 - Russia begins the <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a> in response to the <a href=\"https://wikipedia.org/wiki/Invasion_of_Dagestan\" class=\"mw-redirect\" title=\"Invasion of Dagestan\">Invasion of Dagestan</a> by the <a href=\"https://wikipedia.org/wiki/Islamic_International_Peacekeeping_Brigade\" title=\"Islamic International Peacekeeping Brigade\">Islamic International Peacekeeping Brigade</a>.", "no_year_html": "Russia begins the <a href=\"https://wikipedia.org/wiki/Second_Chechen_War\" title=\"Second Chechen War\">Second Chechen War</a> in response to the <a href=\"https://wikipedia.org/wiki/Invasion_of_Dagestan\" class=\"mw-redirect\" title=\"Invasion of Dagestan\">Invasion of Dagestan</a> by the <a href=\"https://wikipedia.org/wiki/Islamic_International_Peacekeeping_Brigade\" title=\"Islamic International Peacekeeping Brigade\">Islamic International Peacekeeping Brigade</a>.", "links": [{"title": "Second Chechen War", "link": "https://wikipedia.org/wiki/Second_Chechen_War"}, {"title": "Invasion of Dagestan", "link": "https://wikipedia.org/wiki/Invasion_of_Dagestan"}, {"title": "Islamic International Peacekeeping Brigade", "link": "https://wikipedia.org/wiki/Islamic_International_Peacekeeping_Brigade"}]}, {"year": "2003", "text": "A Beechcraft 1900 operating as Colgan Air Flight 9446 crashes after taking off from Barnstable Municipal Airport in Yarmouth, Massachusetts, killing both pilots on board.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> operating as <a href=\"https://wikipedia.org/wiki/Colgan_Air_Flight_9446\" title=\"Colgan Air Flight 9446\">Colgan Air Flight 9446</a> crashes after taking off from <a href=\"https://wikipedia.org/wiki/Barnstable_Municipal_Airport\" class=\"mw-redirect\" title=\"Barnstable Municipal Airport\">Barnstable Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Yarmouth,_Massachusetts\" title=\"Yarmouth, Massachusetts\">Yarmouth, Massachusetts</a>, killing both pilots on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> operating as <a href=\"https://wikipedia.org/wiki/Colgan_Air_Flight_9446\" title=\"Colgan Air Flight 9446\">Colgan Air Flight 9446</a> crashes after taking off from <a href=\"https://wikipedia.org/wiki/Barnstable_Municipal_Airport\" class=\"mw-redirect\" title=\"Barnstable Municipal Airport\">Barnstable Municipal Airport</a> in <a href=\"https://wikipedia.org/wiki/Yarmouth,_Massachusetts\" title=\"Yarmouth, Massachusetts\">Yarmouth, Massachusetts</a>, killing both pilots on board.", "links": [{"title": "Beechcraft 1900", "link": "https://wikipedia.org/wiki/Beechcraft_1900"}, {"title": "Colgan Air Flight 9446", "link": "https://wikipedia.org/wiki/Colgan_Air_Flight_9446"}, {"title": "Barnstable Municipal Airport", "link": "https://wikipedia.org/wiki/Barnstable_Municipal_Airport"}, {"title": "Yarmouth, Massachusetts", "link": "https://wikipedia.org/wiki/Yarmouth,_Massachusetts"}]}, {"year": "2009", "text": "Kidnapping victim <PERSON><PERSON><PERSON> is discovered alive in California after being missing for over 18 years. Her captors, <PERSON> and <PERSON> are apprehended.", "html": "2009 - Kidnapping victim <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is discovered alive in California after being missing for over 18 years. Her captors, <PERSON> and <PERSON> are apprehended.", "no_year_html": "Kidnapping victim <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kidnapping of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is discovered alive in California after being missing for over 18 years. Her captors, <PERSON> and <PERSON> are apprehended.", "links": [{"title": "Kidnapping of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kidnapping_of_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "The Boeing 787 Dreamliner, Boeing's all-new composite airliner, receives certification from the EASA and the FAA.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Boeing_787_Dreamliner\" title=\"Boeing 787 Dreamliner\">Boeing 787 Dreamliner</a>, Boeing's all-new composite airliner, receives certification from the <a href=\"https://wikipedia.org/wiki/European_Aviation_Safety_Agency\" class=\"mw-redirect\" title=\"European Aviation Safety Agency\">EASA</a> and the <a href=\"https://wikipedia.org/wiki/Federal_Aviation_Administration\" title=\"Federal Aviation Administration\">FAA</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boeing_787_Dreamliner\" title=\"Boeing 787 Dreamliner\">Boeing 787 Dreamliner</a>, Boeing's all-new composite airliner, receives certification from the <a href=\"https://wikipedia.org/wiki/European_Aviation_Safety_Agency\" class=\"mw-redirect\" title=\"European Aviation Safety Agency\">EASA</a> and the <a href=\"https://wikipedia.org/wiki/Federal_Aviation_Administration\" title=\"Federal Aviation Administration\">FAA</a>.", "links": [{"title": "Boeing 787 Dreamliner", "link": "https://wikipedia.org/wiki/Boeing_787_Dreamliner"}, {"title": "European Aviation Safety Agency", "link": "https://wikipedia.org/wiki/European_Aviation_Safety_Agency"}, {"title": "Federal Aviation Administration", "link": "https://wikipedia.org/wiki/Federal_Aviation_Administration"}]}, {"year": "2014", "text": "The Jay Report into the Rotherham child sexual exploitation scandal is published.", "html": "2014 - The Jay Report into the <a href=\"https://wikipedia.org/wiki/Rotherham_child_sexual_exploitation_scandal\" title=\"Rotherham child sexual exploitation scandal\">Rotherham child sexual exploitation scandal</a> is published.", "no_year_html": "The Jay Report into the <a href=\"https://wikipedia.org/wiki/Rotherham_child_sexual_exploitation_scandal\" title=\"Rotherham child sexual exploitation scandal\">Rotherham child sexual exploitation scandal</a> is published.", "links": [{"title": "Rotherham child sexual exploitation scandal", "link": "https://wikipedia.org/wiki/Rotherham_child_sexual_exploitation_scandal"}]}, {"year": "2015", "text": "Two U.S. journalists are shot and killed by a disgruntled former coworker while conducting a live report in Moneta, Virginia.", "html": "2015 - Two U.S. journalists are <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Murders of <PERSON> and <PERSON>\">shot and killed</a> by a disgruntled former coworker while conducting a live report in <a href=\"https://wikipedia.org/wiki/Moneta,_Virginia\" title=\"Moneta, Virginia\">Moneta, Virginia</a>.", "no_year_html": "Two U.S. journalists are <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Murders of <PERSON> and <PERSON>\">shot and killed</a> by a disgruntled former coworker while conducting a live report in <a href=\"https://wikipedia.org/wiki/Moneta,_Virginia\" title=\"Moneta, Virginia\">Moneta, Virginia</a>.", "links": [{"title": "Murders of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Murders_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Moneta, Virginia", "link": "https://wikipedia.org/wiki/Moneta,_Virginia"}]}, {"year": "2018", "text": "Three people are killed and eleven wounded during a mass shooting at a Madden NFL '19 video game tournament in Jacksonville, Florida.", "html": "2018 - Three people are killed and eleven wounded during a <a href=\"https://wikipedia.org/wiki/Jacksonville_Landing_shooting\" title=\"Jacksonville Landing shooting\">mass shooting</a> at a <a href=\"https://wikipedia.org/wiki/Madden_NFL_19\" title=\"Madden NFL 19\">Madden NFL '19</a> video game tournament in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>.", "no_year_html": "Three people are killed and eleven wounded during a <a href=\"https://wikipedia.org/wiki/Jacksonville_Landing_shooting\" title=\"Jacksonville Landing shooting\">mass shooting</a> at a <a href=\"https://wikipedia.org/wiki/Madden_NFL_19\" title=\"Madden NFL 19\">Madden NFL '19</a> video game tournament in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>.", "links": [{"title": "Jacksonville Landing shooting", "link": "https://wikipedia.org/wiki/Jacksonville_Landing_shooting"}, {"title": "Madden NFL 19", "link": "https://wikipedia.org/wiki/Madden_NFL_19"}, {"title": "Jacksonville, Florida", "link": "https://wikipedia.org/wiki/Jacksonville,_Florida"}]}, {"year": "2021", "text": "During the 2021 Kabul airlift, a suicide bombing at Hamid Karzai International Airport kills 13 US military personnel and at least 169 Afghan civilians.", "html": "2021 - During the <a href=\"https://wikipedia.org/wiki/2021_Kabul_airlift\" title=\"2021 Kabul airlift\">2021 Kabul airlift</a>, a <a href=\"https://wikipedia.org/wiki/2021_Kabul_airport_attack\" title=\"2021 Kabul airport attack\">suicide bombing at Hamid Karzai International Airport</a> kills 13 US military personnel and at least 169 Afghan civilians.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/2021_Kabul_airlift\" title=\"2021 Kabul airlift\">2021 Kabul airlift</a>, a <a href=\"https://wikipedia.org/wiki/2021_Kabul_airport_attack\" title=\"2021 Kabul airport attack\">suicide bombing at Hamid Karzai International Airport</a> kills 13 US military personnel and at least 169 Afghan civilians.", "links": [{"title": "2021 Kabul airlift", "link": "https://wikipedia.org/wiki/2021_Kabul_airlift"}, {"title": "2021 Kabul airport attack", "link": "https://wikipedia.org/wiki/2021_Kabul_airport_attack"}]}, {"year": "2023", "text": "Exactly 5 years after the 2018 Jacksonville Landing shooting, there is another shooting in Jacksonville, Florida, leaving 3 people dead.", "html": "2023 - Exactly 5 years after the 2018 <a href=\"https://wikipedia.org/wiki/Jacksonville_Landing_shooting\" title=\"Jacksonville Landing shooting\">Jacksonville Landing shooting</a>, there is <a href=\"https://wikipedia.org/wiki/2023_Jacksonville_shooting\" title=\"2023 Jacksonville shooting\">another shooting</a> in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>, leaving 3 people dead.", "no_year_html": "Exactly 5 years after the 2018 <a href=\"https://wikipedia.org/wiki/Jacksonville_Landing_shooting\" title=\"Jacksonville Landing shooting\">Jacksonville Landing shooting</a>, there is <a href=\"https://wikipedia.org/wiki/2023_Jacksonville_shooting\" title=\"2023 Jacksonville shooting\">another shooting</a> in <a href=\"https://wikipedia.org/wiki/Jacksonville,_Florida\" title=\"Jacksonville, Florida\">Jacksonville, Florida</a>, leaving 3 people dead.", "links": [{"title": "Jacksonville Landing shooting", "link": "https://wikipedia.org/wiki/Jacksonville_Landing_shooting"}, {"title": "2023 Jacksonville shooting", "link": "https://wikipedia.org/wiki/2023_Jacksonville_shooting"}, {"title": "Jacksonville, Florida", "link": "https://wikipedia.org/wiki/Jacksonville,_Florida"}]}], "Births": [{"year": "1548", "text": "<PERSON>, Italian painter (d. 1612)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1582", "text": "<PERSON><PERSON><PERSON> of Bisignano, Italian Franciscan friar and saint (d. 1637)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bisignano\" title=\"<PERSON><PERSON><PERSON> of Bisignano\"><PERSON><PERSON><PERSON> of Bisignano</a>, Italian Franciscan friar and saint (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bisignano\" title=\"<PERSON><PERSON><PERSON> of Bisignano\"><PERSON><PERSON><PERSON> of Bisignano</a>, Italian Franciscan friar and saint (d. 1637)", "links": [{"title": "<PERSON><PERSON><PERSON> of Bisignano", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bisignano"}]}, {"year": "1596", "text": "<PERSON>, Elector <PERSON>, Bohemian king (d. 1632)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_Palatine\" class=\"mw-redirect\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, Bohemian king (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>ector_<PERSON>latine\" class=\"mw-redirect\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a>, Bohemian king (d. 1632)", "links": [{"title": "<PERSON>, Elector <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON>, English politician, Prime Minister of the United Kingdom (d. 1745)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1694", "text": "<PERSON><PERSON>, English colonial minister, academic, and politician (d. 1755)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English colonial minister, academic, and politician (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English colonial minister, academic, and politician (d. 1755)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French singer-songwriter (d. 1791)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter (d. 1791)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1728", "text": "<PERSON>, Swiss mathematician, physicist, and astronomer (d. 1777)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician, physicist, and astronomer (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician, physicist, and astronomer (d. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON><PERSON><PERSON>, French mineralogist and geologist (d. 1790)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle\" title=\"<PERSON><PERSON><PERSON>'Isle\"><PERSON><PERSON><PERSON>Isle</a>, French mineralogist and geologist (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle\" title=\"<PERSON><PERSON><PERSON>'Isle\"><PERSON><PERSON><PERSON>Isle</a>, French mineralogist and geologist (d. 1790)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rom%C3%A9_de_l%27Isle"}]}, {"year": "1740", "text": "<PERSON><PERSON><PERSON>, French inventor, invented the hot air balloon (d. 1810)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\"><PERSON><PERSON><PERSON></a>, French inventor, invented the <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\"><PERSON><PERSON><PERSON></a>, French inventor, invented the <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (d. 1810)", "links": [{"title": "Montgolfier brothers", "link": "https://wikipedia.org/wiki/Montgol<PERSON>r_brothers"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1743", "text": "<PERSON>, French chemist and biologist (d. 1794)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and biologist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and biologist (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, Spanish-born Mexican bishop (d. 1825)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Queipo\" title=\"<PERSON> Queip<PERSON>\"><PERSON></a>, Spanish-born Mexican bishop (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Queipo\" title=\"<PERSON> Queip<PERSON>\"><PERSON></a>, Spanish-born Mexican bishop (d. 1825)", "links": [{"title": "<PERSON> y Que<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>o"}]}, {"year": "1775", "text": "<PERSON>, German publicist and academic (d. 1851)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and academic (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and academic (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON>, astronomer, director of the Astronomical Observatory of Naples (d. 1817)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, astronomer, director of the Astronomical Observatory of Naples (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, astronomer, director of the Astronomical Observatory of Naples (d. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Federigo_<PERSON>i"}]}, {"year": "1792", "text": "<PERSON>, Uruguayan soldier and politician, 4th President of Uruguay (d. 1857)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Uruguay", "link": "https://wikipedia.org/wiki/President_of_Uruguay"}]}, {"year": "1797", "text": "<PERSON> of Alaska, Russian Orthodox missionary priest, then the first Orthodox bishop and archbishop in the Americas, and finally the Metropolitan of Moscow and all Russia (d. 1879)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Saint_Innocent_of_Alaska\" class=\"mw-redirect\" title=\"Saint Innocent of Alaska\">Saint <PERSON> of Alaska</a>, <a href=\"https://wikipedia.org/wiki/Russian_Orthodox\" class=\"mw-redirect\" title=\"Russian Orthodox\">Russian Orthodox</a> <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> <a href=\"https://wikipedia.org/wiki/Priesthood_(Orthodox_Church)\" class=\"mw-redirect\" title=\"Priesthood (Orthodox Church)\">priest</a>, then the first Orthodox bishop and archbishop in the Americas, and finally the <a href=\"https://wikipedia.org/wiki/Metropolitan_bishop#Eastern_Orthodox\" title=\"Metropolitan bishop\">Metropolitan</a> of Moscow and all Russia (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Innocent_of_Alaska\" class=\"mw-redirect\" title=\"Saint Innocent of Alaska\">Saint <PERSON> of Alaska</a>, <a href=\"https://wikipedia.org/wiki/Russian_Orthodox\" class=\"mw-redirect\" title=\"Russian Orthodox\">Russian Orthodox</a> <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> <a href=\"https://wikipedia.org/wiki/Priesthood_(Orthodox_Church)\" class=\"mw-redirect\" title=\"Priesthood (Orthodox Church)\">priest</a>, then the first Orthodox bishop and archbishop in the Americas, and finally the <a href=\"https://wikipedia.org/wiki/Metropolitan_bishop#Eastern_Orthodox\" title=\"Metropolitan bishop\">Metropolitan</a> of Moscow and all Russia (d. 1879)", "links": [{"title": "<PERSON> of Alaska", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_of_Alaska"}, {"title": "Russian Orthodox", "link": "https://wikipedia.org/wiki/Russian_Orthodox"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Missionary"}, {"title": "Priesthood (Orthodox Church)", "link": "https://wikipedia.org/wiki/Priesthood_(Orthodox_Church)"}, {"title": "Metropolitan bishop", "link": "https://wikipedia.org/wiki/Metropolitan_bishop#Eastern_Orthodox"}]}, {"year": "1819", "text": "<PERSON>, Prince Consort of the United Kingdom (d. 1861)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince Consort\"><PERSON>, Prince Consort</a> of the United Kingdom (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_Consort\" class=\"mw-redirect\" title=\"<PERSON>, Prince <PERSON>\"><PERSON>, Prince Consort</a> of the United Kingdom (d. 1861)", "links": [{"title": "<PERSON>, Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_Consort"}]}, {"year": "1824", "text": "<PERSON>, British painter (d. 1885)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, English cricketer (d. 1932)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Danish actress (d. 1939)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B8<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B8<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clara_Sch%C3%B8nfeld"}]}, {"year": "1862", "text": "<PERSON>, Canadian songwriter and bandleader (d. 1926)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian songwriter and bandleader (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian songwriter and bandleader (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Scottish-Australian engineer, designed the Spencer Street Power Station (d. 1946)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian engineer, designed the <a href=\"https://wikipedia.org/wiki/Spencer_Street_Power_Station\" title=\"Spencer Street Power Station\">Spencer Street Power Station</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian engineer, designed the <a href=\"https://wikipedia.org/wiki/Spencer_Street_Power_Station\" title=\"Spencer Street Power Station\">Spencer Street Power Station</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Spencer Street Power Station", "link": "https://wikipedia.org/wiki/Spencer_Street_Power_Station"}]}, {"year": "1873", "text": "<PERSON>, American engineer and academic, invented the Audion tube (d. 1961)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lee <PERSON>\"><PERSON></a>, American engineer and academic, invented the <a href=\"https://wikipedia.org/wiki/Audion_tube\" class=\"mw-redirect\" title=\"Audion tube\">Audion tube</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lee <PERSON>\"><PERSON></a>, American engineer and academic, invented the <a href=\"https://wikipedia.org/wiki/Audion_tube\" class=\"mw-redirect\" title=\"Audion tube\">Audion tube</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Audion tube", "link": "https://wikipedia.org/wiki/Audion_tube"}]}, {"year": "1874", "text": "<PERSON><PERSON>, American novelist, short story writer, and playwright (d. 1938)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Zona_Gale\" title=\"Zona Gale\"><PERSON><PERSON></a>, American novelist, short story writer, and playwright (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zona_Gale\" title=\"Zona Gale\"><PERSON><PERSON></a>, American novelist, short story writer, and playwright (d. 1938)", "links": [{"title": "Zona Gale", "link": "https://wikipedia.org/wiki/Zona_Gale"}]}, {"year": "1875", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-Canadian historian and politician, 15th Governor General of Canada (d. 1940)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>r\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-Canadian historian and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>r\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish-Canadian historian and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1940)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1880", "text": "<PERSON>, Italian-French author, poet, playwright, and critic (d. 1918)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Apollinaire\" title=\"Guillaume Apollinaire\"><PERSON></a>, Italian-French author, poet, playwright, and critic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>linaire\" title=\"Guillaume Apollinaire\"><PERSON></a>, Italian-French author, poet, playwright, and critic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillaume_Apollinaire"}]}, {"year": "1882", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1964)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1882", "text": "<PERSON>, English footballer (d. 1966)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 1966)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1885", "text": "<PERSON>, French author and poet (d. 1972)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Maltese architect and developer (d. 1974)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect and developer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect and developer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Indian author and playwright (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Acharya <PERSON>\">A<PERSON></a>, Indian author and playwright (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"A<PERSON>\">A<PERSON></a>, Indian author and playwright (d. 1960)", "links": [{"title": "A<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American baseball player and farmer (d. 1989)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sparky <PERSON>\"><PERSON><PERSON></a>, American baseball player and farmer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sparky <PERSON>\"><PERSON><PERSON></a>, American baseball player and farmer (d. 1989)", "links": [{"title": "Spa<PERSON>", "link": "https://wikipedia.org/wiki/Spa<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Bulgarian soldier and politician (d. 1990)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian soldier and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian soldier and politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, South Korean activist and politician, 2nd President of South Korea (d. 1990)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean activist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean activist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1898", "text": "<PERSON>, American-Italian art collector and philanthropist (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian art collector and philanthropist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian art collector and philanthropist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American nurse, recipient of the Medal of Freedom (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, recipient of the <a href=\"https://wikipedia.org/wiki/Medal_of_Freedom_(1945)\" title=\"Medal of Freedom (1945)\">Medal of Freedom</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, recipient of the <a href=\"https://wikipedia.org/wiki/Medal_of_Freedom_(1945)\" title=\"Medal of Freedom (1945)\">Medal of Freedom</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Freedom (1945)", "link": "https://wikipedia.org/wiki/Medal_of_Freedom_(1945)"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, German-American engineer and businessman (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer and businessman (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, German-American engineer and businessman (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Australian author and poet (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German SS officer and engineer (d. 1945)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and engineer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and engineer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1901", "text": "<PERSON>, American singer and bandleader (d. 1972)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American general and diplomat, United States Ambassador to South Vietnam (d. 1987)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Vietnam\" class=\"mw-redirect\" title=\"United States Ambassador to South Vietnam\">United States Ambassador to South Vietnam</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_South_Vietnam\" class=\"mw-redirect\" title=\"United States Ambassador to South Vietnam\">United States Ambassador to South Vietnam</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to South Vietnam", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_South_Vietnam"}]}, {"year": "1901", "text": "<PERSON>, Chinese general and politician, 2nd Foreign Minister of the People's Republic of China (d. 1972)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marshal)\" title=\"<PERSON> (marshal)\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Foreign Minister of the People's Republic of China\">Foreign Minister of the People's Republic of China</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marshal)\" title=\"<PERSON> (marshal)\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Foreign Minister of the People's Republic of China\">Foreign Minister of the People's Republic of China</a> (d. 1972)", "links": [{"title": "<PERSON> (marshal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(marshal)"}, {"title": "Foreign Minister of the People's Republic of China", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China"}]}, {"year": "1903", "text": "<PERSON>, American author (d. 1992)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English-American author and academic (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author and academic (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English footballer and cricketer (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English tennis player (d. 2000)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Austin\"><PERSON></a>, English tennis player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Polish-American physician and virologist, developed the polio vaccine (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American physician and virologist, developed the <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American physician and virologist, developed the <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Polio vaccine", "link": "https://wikipedia.org/wiki/Polio_vaccine"}]}, {"year": "1908", "text": "<PERSON>, Prussian-American linguist and scholar (d. 1967)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-American linguist and scholar (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-American linguist and scholar (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American screenwriter and producer (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, South African cricketer and educator (d. 1976)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and educator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor (d. 1981)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1981)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1909", "text": "<PERSON>, American baseball player (d. 1978)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (d. 1978)", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(outfielder)"}]}, {"year": "1910", "text": "<PERSON>, Albanian-Indian nun, missionary, Catholic saint, and Nobel Prize laureate (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-Indian nun, missionary, Catholic saint, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian-Indian nun, missionary, Catholic saint, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1911", "text": "<PERSON>, American author and screenwriter (d. 1974)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Binder"}]}, {"year": "1912", "text": "<PERSON>, British supercentenarian (d. 2024)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British supercentenarian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British supercentenarian (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Belgian-Argentinian author and translator (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON>zar\" title=\"<PERSON>\"><PERSON></a>, Belgian-Argentinian author and translator (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1zar\" title=\"<PERSON>\"><PERSON></a>, Belgian-Argentinian author and translator (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_Cort%C3%A1zar"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish soldier and poet (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca\" title=\"Fazıl Hüsnü Dağlarca\"><PERSON>az<PERSON><PERSON> Hüsnü Dağlarca</a>, Turkish soldier and poet (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca\" title=\"Fazıl Hüsnü Dağlarca\"><PERSON><PERSON><PERSON><PERSON> Hüsnü Dağlarca</a>, Turkish soldier and poet (d. 2008)", "links": [{"title": "Fazıl Hüsnü Dağlarca", "link": "https://wikipedia.org/wiki/Faz%C4%B1l_H%C3%BCsn%C3%BC_Da%C4%9Flarca"}]}, {"year": "1915", "text": "<PERSON>, English composer and conductor (d. 1982)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and conductor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American physicist and mathematician (d. 2020)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American priest and academic (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American priest and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American priest and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American illustrator (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Parker\"><PERSON><PERSON></a>, American illustrator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Parker\"><PERSON><PERSON></a>, American illustrator (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Thai general and politician, 16th Prime Minister of Thailand (d. 2019)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"Pre<PERSON>\"><PERSON><PERSON></a>, Thai general and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pre<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai general and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prem_Tin<PERSON>anonda"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Israeli mathematician and scholar (d. 1994)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Amitsur\" title=\"Shimshon Amitsur\"><PERSON><PERSON><PERSON></a>, Israeli mathematician and scholar (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>itsur\" title=\"Shi<PERSON>hon Amitsur\"><PERSON><PERSON><PERSON></a>, Israeli mathematician and scholar (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Amitsur"}]}, {"year": "1921", "text": "<PERSON>, American journalist and author (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American journalist and author (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, German pianist and conductor (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American baseball player (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American economist and academic (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French scholar and politician, Minister of Justice for France (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(France)\" title=\"Ministry of Justice (France)\">Minister of Justice for France</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(France)\" title=\"Ministry of Justice (France)\">Minister of Justice for France</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Justice (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(France)"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian director, screenwriter, and cinematographer (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian director, screenwriter, and cinematographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian director, screenwriter, and cinematographer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Hungarian economist and politician (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Etelka_Keser%C5%B1\" title=\"Etelka Keserű\"><PERSON>tel<PERSON>ű</a>, Hungarian economist and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Etelka_Keser%C5%B1\" title=\"Etelka Keserű\"><PERSON>tel<PERSON></a>, Hungarian economist and politician (d. 2018)", "links": [{"title": "Etelka Keserű", "link": "https://wikipedia.org/wiki/Etelka_Keser%C5%B1"}]}, {"year": "1925", "text": "<PERSON>-<PERSON>, Chilean composer (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Armenian violinist and educator (d. 1999)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>tsikian\" title=\"<PERSON><PERSON> Tsitsikian\"><PERSON><PERSON></a>, Armenian violinist and educator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>ikian\" title=\"<PERSON><PERSON> Tsitsikian\"><PERSON><PERSON></a>, Armenian violinist and educator (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tsitsikian"}]}, {"year": "1926", "text": "<PERSON>, American painter and author (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Indian businessman and philanthropist, co-founded Hero Cycles (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Prakash <PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Hero_Cycles\" title=\"Hero Cycles\">Hero Cycles</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Indian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Hero_Cycles\" title=\"Hero Cycles\">Hero Cycles</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Hero Cycles", "link": "https://wikipedia.org/wiki/Hero_Cycles"}]}, {"year": "1929", "text": "<PERSON>, Zambian soldier and politician, 1st Vice President of Zambia (d. 1996)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zambia\" class=\"mw-redirect\" title=\"Vice President of Zambia\">Vice President of Zambia</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_President_of_Zambia\" class=\"mw-redirect\" title=\"Vice President of Zambia\">Vice President of Zambia</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Zambia", "link": "https://wikipedia.org/wiki/Vice_President_of_Zambia"}]}, {"year": "1930", "text": "<PERSON>, Guyanese cricketer and coach (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese cricketer and coach (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian water polo player (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/K%C3%A1lm%C3%A1n_Markovits\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian water polo player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1lm%C3%A1n_Markovits\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian water polo player (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1lm%C3%A1n_Markovits"}]}, {"year": "1932", "text": "<PERSON>, Chilean basketball player (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chilean basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Chilean basketball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American basketball player, coach, and sportscaster (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and sportscaster (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1934", "text": "<PERSON>, Australian rugby player, coach, lawyer and politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player, coach, lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, Australian rugby player, coach, lawyer and politician", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English computer scientist and academic (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sp%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sp%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sp%C3%A4<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American political scientist and academic (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2013)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1938", "text": "<PERSON>, English drummer (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Jet_Black\" title=\"Jet Black\"><PERSON></a>, English drummer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jet_Black\" title=\"Jet Black\"><PERSON></a>, English drummer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Black"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Israeli businessman and politician (d. 2007)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli businessman and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli businessman and politician (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nch<PERSON>_<PERSON>stein"}]}, {"year": "1939", "text": "<PERSON>, Brazilian banker and financier", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian banker and financier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian banker and financier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2017)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1940", "text": "<PERSON>, English journalist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American dancer and singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Vic Dana\"><PERSON></a>, American dancer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dana\" title=\"Vic Dana\"><PERSON></a>, American dancer and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dana"}]}, {"year": "1940", "text": "<PERSON>, American voice actor, producer, and screenwriter (d. 2008)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English musician and songwriter (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English drummer and singer (d. 2005)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer and singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer and singer (d. 2005)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1941", "text": "<PERSON>, English actress, producer, and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, French-Swiss director and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hroeder\" title=\"<PERSON><PERSON> Schroeder\"><PERSON><PERSON></a>, French-Swiss director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hroeder\" title=\"<PERSON><PERSON> Schroeder\"><PERSON><PERSON></a>, French-Swiss director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barbet_Schroeder"}]}, {"year": "1942", "text": "<PERSON>, <PERSON>, English lawyer and politician (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Malaysian football coach and player (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian football coach and player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian football coach and player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Brazilian singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1944", "text": "<PERSON>, English guitarist and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>, English geographer and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and drummer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American sergeant and politician, 1st Secretary of Homeland Security", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Tom_Ridge\" title=\"Tom Ridge\"><PERSON></a>, American sergeant and politician, 1st <a href=\"https://wikipedia.org/wiki/Secretary_of_Homeland_Security\" class=\"mw-redirect\" title=\"Secretary of Homeland Security\">Secretary of Homeland Security</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_Ridge\" title=\"Tom Ridge\"><PERSON></a>, American sergeant and politician, 1st <a href=\"https://wikipedia.org/wiki/Secretary_of_Homeland_Security\" class=\"mw-redirect\" title=\"Secretary of Homeland Security\">Secretary of Homeland Security</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tom_<PERSON>"}, {"title": "Secretary of Homeland Security", "link": "https://wikipedia.org/wiki/Secretary_of_Homeland_Security"}]}, {"year": "1946", "text": "<PERSON>, Chinese engineer and politician, 14th Chinese Minister of Education", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1946)\" title=\"<PERSON> (born 1946)\"><PERSON></a>, Chinese engineer and politician, 14th <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Education of the People's Republic of China\">Chinese Minister of Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1946)\" title=\"<PERSON> (born 1946)\"><PERSON></a>, Chinese engineer and politician, 14th <a href=\"https://wikipedia.org/wiki/Ministry_of_Education_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Ministry of Education of the People's Republic of China\">Chinese Minister of Education</a>", "links": [{"title": "<PERSON> (born 1946)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1946)"}, {"title": "Ministry of Education of the People's Republic of China", "link": "https://wikipedia.org/wiki/Ministry_of_Education_of_the_People%27s_Republic_of_China"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Romanian footballer and manager (d. 2007)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian footballer and manager (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Azerbaijani cleric", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani cleric", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Azerbaijani cleric", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian-American singer-songwriter, guitarist, and producer (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter, guitarist, and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter, guitarist, and producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, German weightlifter (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German weightlifter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON>\"><PERSON><PERSON></a>, German weightlifter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1951", "text": "<PERSON>, American physicist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>ryon_Baltimore\" title=\"Bryon Baltimore\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ryon_Baltimore\" title=\"Bryon Baltimore\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>n <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor (d. 2003)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American journalist and puzzle creator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and puzzle creator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and puzzle creator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian general and politician, 27th Governor General of Australia", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "1953", "text": "<PERSON>, Italian statistician and sociologist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian statistician and sociologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian statistician and sociologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Irish footballer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English golfer and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, English golfer and sportscaster", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1954", "text": "<PERSON>, American race car driver and businessman", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, British academic and educator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English historian and curator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian chemist and educator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian chemist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian chemist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giuseppe_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English viola player and composer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English viola player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English viola player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American football player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American poet and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Swedish musician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Dr<PERSON>_<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON></a>, Swedish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON></a>, Swedish musician", "links": [{"title": "Dr. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Belgian cyclist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American saxophonist, composer, and bandleader", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Marsalis\"><PERSON><PERSON><PERSON></a>, American saxophonist, composer, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Marsalis\"><PERSON><PERSON><PERSON></a>, American saxophonist, composer, and bandleader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American model and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Algerian-French singer-songwriter and pianist (d. 2022)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vi\" title=\"<PERSON>\"><PERSON></a>, Algerian-French singer-songwriter and pianist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vi\" title=\"<PERSON>\"><PERSON></a>, Algerian-French singer-songwriter and pianist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_L%C3%A9vi"}]}, {"year": "1961", "text": "<PERSON>, American baseball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American hurdler", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Roger_Kingdom\" title=\"Roger Kingdom\"><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roger_Kingdom\" title=\"Roger Kingdom\"><PERSON></a>, American hurdler", "links": [{"title": "Roger Kingdom", "link": "https://wikipedia.org/wiki/Roger_Kingdom"}]}, {"year": "1963", "text": "<PERSON>, English cricketer and umpire", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American author, critic, and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>liger\" title=\"<PERSON><PERSON>liger\"><PERSON><PERSON></a>, American author, critic, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>liger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author, critic, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pat<PERSON>_<PERSON>pliger"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, English-American author and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American author and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Allegra_<PERSON>ston"}]}, {"year": "1964", "text": "<PERSON>, American-Canadian football player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Israeli footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>adok_Malka\" title=\"Zadok Malka\"><PERSON><PERSON><PERSON></a>, Israeli footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>adok_Malka\" title=\"Zadok Malka\"><PERSON><PERSON><PERSON></a>, Israeli footballer and manager", "links": [{"title": "Zadok Malka", "link": "https://wikipedia.org/wiki/Zadok_Malka"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German boxer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, 1st Vice President of Azerbaijan, goodwill ambassador of UNESCO and ISESCO", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 1st Vice President of Azerbaijan, goodwill ambassador of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> and <a href=\"https://wikipedia.org/wiki/ISESCO\" class=\"mw-redirect\" title=\"ISESCO\">ISESCO</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 1st Vice President of Azerbaijan, goodwill ambassador of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a> and <a href=\"https://wikipedia.org/wiki/ISESCO\" class=\"mw-redirect\" title=\"ISESCO\">ISESCO</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "UNESCO", "link": "https://wikipedia.org/wiki/UNESCO"}, {"title": "ISESCO", "link": "https://wikipedia.org/wiki/ISESCO"}]}, {"year": "1965", "text": "<PERSON>, English mathematician and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1966", "text": "<PERSON>, Dutch field hockey player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Scottish singer-songwriter and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Scottish journalist and politician, Secretary of State for Education", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1968", "text": "<PERSON>, English cyclist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American drummer and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1970", "text": "<PERSON>, American actress, comedian, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, South African cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Mexican-American singer-songwriter and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Thal%C3%ADa\" title=\"Thal<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thal%C3%ADa\" title=\"Thal<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American singer-songwriter and actress", "links": [{"title": "Thalía", "link": "https://wikipedia.org/wiki/Thal%C3%ADa"}]}, {"year": "1973", "text": "<PERSON>, English boxer (d. 2012)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ensberg"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Spanish singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>aia_<PERSON>\" title=\"Amaia <PERSON>ro\"><PERSON><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aia <PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amaia_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Swedish swimmer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"There<PERSON>\"><PERSON><PERSON></a>, Swedish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English rugby player and cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_<PERSON>ba\" title=\"Saeko <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON>_<PERSON>ba\" title=\"Saek<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saeko_Chiba"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Arriaza_Pazos\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>aza Pazos\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Arriaza_Pazos\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Arriaza_Pazos"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_C<PERSON>\" title=\"<PERSON><PERSON> C<PERSON>kin\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_Papamakarios\" title=\"Man<PERSON> Papamakarios\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_Papamakarios\" title=\"Man<PERSON> Papamakarios\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "Man<PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Papamakarios"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Barbadian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tino_Best\" title=\"Tino Best\"><PERSON><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tino_Best\" title=\"Tino Best\"><PERSON><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Best"}]}, {"year": "1981", "text": "<PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6nig\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6nig\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_B%C3%B6nig"}]}, {"year": "1981", "text": "<PERSON>, Greek basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Canadian wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American comedian, actor, writer, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, writer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mattia_Cassani"}]}, {"year": "1983", "text": "<PERSON>, Spanish race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Porteiro\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Porteiro\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Porteiro"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Malaysian squash player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian <a href=\"https://wikipedia.org/wiki/Squash_(sport)\" title=\"Squash (sport)\">squash</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian <a href=\"https://wikipedia.org/wiki/Squash_(sport)\" title=\"Squash (sport)\">squash</a> player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Squash (sport)", "link": "https://wikipedia.org/wiki/Squash_(sport)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian decathlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian decathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gridiron_football)\" title=\"<PERSON> (gridiron football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gridiron_football)\" title=\"<PERSON> (gridiron football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (gridiron football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gridiron_football)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Guyanese-American rapper, singer, and songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American rapper, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American rapper, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Jhn"}]}, {"year": "1986", "text": "<PERSON>, Turkish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer, dancer, actress and model", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, dancer, actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, dancer, actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player and coach (d. 2014)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Venezuelan baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elvis_Andrus"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-Came<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-Camelia <PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-Camelia_<PERSON>gu"}]}, {"year": "1990", "text": "<PERSON>, Argentine footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American cross-country skier", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cross-country skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Dylan_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dylan_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dylan_O%27Brien"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American actress and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American football player (d. 2023)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Venezuelan baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Ranger_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranger_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ranger_Su%C3%A1rez"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>rda<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cordae"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Korean rapper and record producer ", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_So-yeon\" class=\"mw-redirect\" title=\"<PERSON><PERSON> So-yeon\"><PERSON><PERSON></a>, Korean rapper and record producer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_So-yeon\" class=\"mw-redirect\" title=\"<PERSON><PERSON> So-yeon\"><PERSON><PERSON></a>, Korean rapper and record producer ", "links": [{"title": "<PERSON><PERSON>-yeon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-yeon"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2002", "text": "<PERSON>, American rapper", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Tecca\" title=\"Lil Tecca\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Tecca\" title=\"Lil Tecca\"><PERSON></a>, American rapper", "links": [{"title": "Lil <PERSON>", "link": "https://wikipedia.org/wiki/Lil_<PERSON>cca"}]}], "Deaths": [{"year": "787", "text": "<PERSON><PERSON><PERSON>, duke of Benevento", "html": "787 - <a href=\"https://wikipedia.org/wiki/Arechis_II_of_Benevento\" title=\"Arechi<PERSON> II of Benevento\"><PERSON><PERSON><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Benevento\" title=\"Duchy of Benevento\">Benevento</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Are<PERSON>s_II_of_Benevento\" title=\"Are<PERSON><PERSON> II of Benevento\"><PERSON><PERSON><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Benevento\" title=\"Duchy of Benevento\">Benevento</a>", "links": [{"title": "Arechis II of Benevento", "link": "https://wikipedia.org/wiki/Are<PERSON>s_II_of_Benevento"}, {"title": "Duchy of Benevento", "link": "https://wikipedia.org/wiki/Duchy_of_Benevento"}]}, {"year": "887", "text": "<PERSON><PERSON><PERSON><PERSON>, emperor of Japan (b. 830)", "html": "887 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 830)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dk%C5%8D"}]}, {"year": "1214", "text": "<PERSON> of Constantinople", "html": "1214 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> IV of Constantinople\"><PERSON> of Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> IV of Constantinople\"><PERSON> of Constantinople</a>", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople"}]}, {"year": "1278", "text": "<PERSON><PERSON> of Bohemia (b. 1233)", "html": "1278 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Bohemia\" title=\"<PERSON><PERSON> II of Bohemia\"><PERSON><PERSON> of Bohemia</a> (b. 1233)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_of_Bohemia\" title=\"<PERSON><PERSON> II of Bohemia\"><PERSON><PERSON> of Bohemia</a> (b. 1233)", "links": [{"title": "Ottokar II of Bohemia", "link": "https://wikipedia.org/wiki/Ottokar_II_of_Bohemia"}]}, {"year": "1346", "text": "<PERSON>, Count of Alençon (b. 1297)", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Alen%C3%A7on\" title=\"<PERSON>, Count of Alençon\"><PERSON>, Count of Alençon</a> (b. 1297)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Alen%C3%A7on\" title=\"<PERSON>, Count of Alençon\"><PERSON>, Count of Alençon</a> (b. 1297)", "links": [{"title": "<PERSON>, Count of Alençon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Al<PERSON>%C3%A7on"}]}, {"year": "1346", "text": "<PERSON>, Count of Flanders (b. 1304)", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a> (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON>, Count of Flanders</a> (b. 1304)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1346", "text": "<PERSON>, Count of Blois", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Blois\" title=\"<PERSON>, Count of Blois\"><PERSON>, Count of Blois</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Blois\" title=\"<PERSON>, Count of Blois\"><PERSON>, Count of Blois</a>", "links": [{"title": "<PERSON>, Count of Blois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1346", "text": "<PERSON>, Duke of Lorraine (b. 1320)", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1320)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Lorraine"}]}, {"year": "1346", "text": "<PERSON> of Bohemia (b. 1296)", "html": "1346 - <a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1296)", "links": [{"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bohemia"}]}, {"year": "1349", "text": "<PERSON>, English archbishop, mathematician, and physicist (b. 1290)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop, mathematician, and physicist (b. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop, mathematician, and physicist (b. 1290)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1399", "text": "<PERSON>, Grand Prince of Tver (b. 1333)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Tver\" title=\"<PERSON> II of Tver\"><PERSON> II</a>, Grand Prince of Tver (b. 1333)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Tver\" title=\"<PERSON> II of Tver\"><PERSON> II</a>, Grand Prince of Tver (b. 1333)", "links": [{"title": "<PERSON> of Tver", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Tver"}]}, {"year": "1462", "text": "<PERSON>, Despotess of the Morea", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Despotess of the Morea", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Despotess of the Morea", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>"}]}, {"year": "1486", "text": "<PERSON>, Elector of Saxony (b. 1441)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1441)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1441)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1500", "text": "<PERSON>, Count of Hanau-Münzenberg (b. 1449)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnzenberg\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (b. 1449)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-M%C3%BCnz<PERSON>\" title=\"<PERSON>, Count of Hanau-Münzenberg\"><PERSON>, Count of Hanau-Münzenberg</a> (b. 1449)", "links": [{"title": "<PERSON>, Count of Hanau-Münzenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_Hanau-M%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1551", "text": "<PERSON>, queen of <PERSON> of Sweden (b. 1516)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1516)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1572", "text": "<PERSON><PERSON>, French philosopher and logician (b. 1515)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philosopher and logician (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French philosopher and logician (b. 1515)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ramus"}]}, {"year": "1595", "text": "<PERSON><PERSON><PERSON><PERSON>, Prior of Crato (b. 1531)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3<PERSON>,_Prior_of_Crato\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prior of Crato\"><PERSON><PERSON><PERSON><PERSON>, Prior of Crato</a> (b. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio,_Prior_of_Crato\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prior of Crato\"><PERSON><PERSON><PERSON><PERSON>, Prior of Crato</a> (b. 1531)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prior of Crato", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio,_Prior_of_Crato"}]}, {"year": "1666", "text": "<PERSON><PERSON>, Dutch painter and educator (b. 1580)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and educator (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and educator (b. 1580)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frans_Hals"}]}, {"year": "1714", "text": "Con<PERSON><PERSON>, Ruler of Wallachia (b. 1654)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu\" title=\"Constantin <PERSON>rân<PERSON>anu\">Con<PERSON><PERSON></a>, Ruler of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a> (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu\" title=\"Constantin <PERSON>\">Constant<PERSON></a>, Ruler of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a> (b. 1654)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}]}, {"year": "1714", "text": "<PERSON>, English bishop and author (b. 1632)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and author (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and author (b. 1632)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}]}, {"year": "1723", "text": "<PERSON><PERSON>, Dutch microscopist and biologist (b. 1632)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microscopist and biologist (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microscopist and biologist (b. 1632)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, Swedish nobleman and military leader (b. 1706)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> August<PERSON></a>, Swedish nobleman and military leader (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish nobleman and military leader (b. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, 1st Viscount <PERSON>, English soldier and politician, 3rd Secretary of State for the Colonies (b. 1716)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Sack<PERSON>\" title=\"<PERSON>, 1st Viscount Sa<PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1716)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1810", "text": "<PERSON>, 1st Count of Buenos Aires, French-Spanish sailor and politician, 10th Viceroyalty of the Río de la Plata (b. 1753)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers,_1st_Count_of_Buenos_Aires\" title=\"Santiago de Lin<PERSON>, 1st Count of Buenos Aires\"><PERSON>, 1st Count of Buenos Aires</a>, French-Spanish sailor and politician, 10th <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroyalty of the Río de la Plata</a> (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_de_Liniers,_1st_Count_of_Buenos_Aires\" title=\"Santiago de Liniers, 1st Count of Buenos Aires\"><PERSON>, 1st Count of Buenos Aires</a>, French-Spanish sailor and politician, 10th <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroyalty of the Río de la Plata</a> (b. 1753)", "links": [{"title": "<PERSON>, 1st Count of Buenos Aires", "link": "https://wikipedia.org/wiki/Santiago_de_Lin<PERSON>,_1st_Count_of_Buenos_Aires"}, {"title": "Viceroyalty of the Río de la Plata", "link": "https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata"}]}, {"year": "1813", "text": "<PERSON>, German soldier and author (b. 1791)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German soldier and author (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German soldier and author (b. 1791)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%B6<PERSON>_(author)"}]}, {"year": "1850", "text": "<PERSON> of France (b. 1773)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of France (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, German astronomer and academic (b. 1791)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Syrian Roman Catholic nun; later canonized (b. 1846)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Maria<PERSON> Ba<PERSON>ardy\"><PERSON><PERSON></a>, Syrian Roman Catholic nun; later canonized (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Maria<PERSON> Ba<PERSON>ardy\"><PERSON><PERSON></a>, Syrian Roman Catholic nun; later canonized (b. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mariam_<PERSON>y"}]}, {"year": "1910", "text": "<PERSON>, American psychologist and philosopher (b. 1842)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German publicist and politician (b. 1875)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and politician (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and politician (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Hungarian jurist and politician, Prime Minister of Hungary (b. 1848)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Wekerle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Wekerle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Wekerle"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Ukrainian anarchist military commander (b. 1890)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian anarchist military commander (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian anarchist military commander (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ro_Petrenko"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American actor, director, and screenwriter (b. 1883)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and screenwriter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and screenwriter (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Turkish composer and songwriter (b. 1873)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/B%C3%AEmen_%C5%9Een\" title=\"B<PERSON><PERSON> Ş<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish composer and songwriter (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%AEmen_%C5%9Een\" title=\"<PERSON>î<PERSON> Şen\"><PERSON><PERSON><PERSON></a>, Turkish composer and songwriter (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%AEmen_%C5%9Een"}]}, {"year": "1944", "text": "<PERSON>, German lawyer and diplomat (b. 1909)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_zu_<PERSON>z\" title=\"<PERSON> zu <PERSON>z\"><PERSON> zu <PERSON></a>, German lawyer and diplomat (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_zu_<PERSON>z\" title=\"<PERSON> zu <PERSON>z\"><PERSON> zu <PERSON></a>, German lawyer and diplomat (b. 1909)", "links": [{"title": "<PERSON> zu <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Austrian author and playwright (b. 1890)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American actress and screenwriter (b. 1887)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German-American activist (b. 1881)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Wagenknecht"}]}, {"year": "1958", "text": "<PERSON>, English composer and educator (b. 1872)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian geophysicist and poet (b. 1894)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian geophysicist and poet (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian geophysicist and poet (b. 1894)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress (b. 1905)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English pilot and sailor (b. 1901)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and sailor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and sailor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American pilot and explorer (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and explorer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and explorer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Norwegian geologist and academic (b. 1885)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian geologist and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian geologist and academic (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, German-American soprano (b. 1888)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American soprano (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American soprano (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, German-American author and illustrator, created <PERSON><PERSON><PERSON> (b. 1898)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ous <PERSON>\"><PERSON><PERSON><PERSON></a></i> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. A<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German-American author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ous_<PERSON>\" title=\"<PERSON><PERSON>ous <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a></i> (b. 1898)", "links": [{"title": "H. A<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Curious <PERSON>", "link": "https://wikipedia.org/wiki/Curious_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French-American actor, singer, and producer (b. 1899)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor, singer, and producer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor, singer, and producer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Argentinian footballer and manager (b. 1916)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Finnish author, translator, and academic (b. 1908)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author, translator, and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author, translator, and academic (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, German-Austrian actress (b. 1874)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian actress (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian actress (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American animator, director, and voice actor (b. 1908)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Tex_Avery\" title=\"Tex Avery\"><PERSON></a>, American animator, director, and voice actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tex_Avery\" title=\"Tex Avery\"><PERSON></a>, American animator, director, and voice actor (b. 1908)", "links": [{"title": "Tex Avery", "link": "https://wikipedia.org/wiki/Tex_Avery"}]}, {"year": "1981", "text": "<PERSON>, American trade union leader, co-founded the American Civil Liberties Union (b. 1884)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trade union leader, co-founded the <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trade union leader, co-founded the <a href=\"https://wikipedia.org/wiki/American_Civil_Liberties_Union\" title=\"American Civil Liberties Union\">American Civil Liberties Union</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "American Civil Liberties Union", "link": "https://wikipedia.org/wiki/American_Civil_Liberties_Union"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter (b. 1914)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Barbadian-English cricketer and manager (b. 1919)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Barbadian-English cricketer and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Barbadian-English cricketer and manager (b. 1919)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1987", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1897)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ittig"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1988", "text": "<PERSON>, Portuguese singer-songwriter (b. 1957)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Portuguese singer-songwriter (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Portuguese singer-songwriter (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Pai%C3%A3o"}]}, {"year": "1989", "text": "<PERSON>, American author (b. 1903)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Irving <PERSON>\"><PERSON></a>, American author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Thai artist (b. 1934)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Thai artist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Thai artist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American fashion commentator, TV and radio personality, and fashion show producer (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion commentator, TV and radio personality, and fashion show producer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion commentator, TV and radio personality, and fashion show producer (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Belgian author and illustrator (b. 1925)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Finnish architect, co-designed the Kaleva Church (b. 1923)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Re<PERSON>_<PERSON>til%C3%A4\" class=\"mw-redirect\" title=\"<PERSON><PERSON>tilä\"><PERSON><PERSON></a>, Finnish architect, co-designed the <a href=\"https://wikipedia.org/wiki/Kaleva_Church\" title=\"Kaleva Church\">Kaleva Church</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re<PERSON>_<PERSON>til%C3%A4\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Pietilä\"><PERSON><PERSON></a>, Finnish architect, co-designed the <a href=\"https://wikipedia.org/wiki/Kaleva_Church\" title=\"Kaleva Church\">Kaleva Church</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reima_Pietil%C3%A4"}, {"title": "Kaleva Church", "link": "https://wikipedia.org/wiki/Kaleva_Church"}]}, {"year": "1995", "text": "<PERSON>, English-Scottish author and poet (b. 1934)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-Scottish author and poet (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-Scottish author and poet (b. 1934)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1998", "text": "<PERSON>, American physicist, Nobel Prize laureate (b. 1918)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2000", "text": "<PERSON>, Iranian engineer and academic (b. 1939)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian engineer and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian engineer and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English tennis player (b. 1906)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Austin\"><PERSON></a>, English tennis player (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Polish-Canadian painter and educator (b. 1904)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Faroese educator and politician, 8th Prime Minister of the Faroe Islands (b. 1940)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese educator and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese educator and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "2003", "text": "<PERSON>, American football player and coach (b. 1937)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American singer-songwriter and actress (b. 1952)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian guitarist and songwriter  (b. 1960)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amour\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amour\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_D%27Amour"}]}, {"year": "2005", "text": "<PERSON>, American art collector and interior designer (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and interior designer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector and interior designer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Canadian wrestler and politician (b. 1949)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Moondog King\"><PERSON><PERSON></a>, Canadian wrestler and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_King\" title=\"Moondog King\"><PERSON><PERSON></a>, Canadian wrestler and politician (b. 1949)", "links": [{"title": "Moondog King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_King"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Polish-German lawyer and politician, Minister of Intra-German Relations (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Intra-German_Relations\" title=\"Minister of Intra-German Relations\">Minister of Intra-German Relations</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Intra-German Relations", "link": "https://wikipedia.org/wiki/Minister_of_Intra-German_Relations"}]}, {"year": "2006", "text": "<PERSON>, Barbadian cricketer and coach (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American landscape photographer (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American landscape photographer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American landscape photographer (b. 1916)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)"}]}, {"year": "2007", "text": "<PERSON>, Luxembourger jurist and politician, 20th Prime Minister of Luxembourg (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger jurist and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger jurist and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg\" class=\"mw-redirect\" title=\"Prime Minister of Luxembourg\">Prime Minister of Luxembourg</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thorn"}, {"title": "Prime Minister of Luxembourg", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Luxembourg"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American journalist and novelist (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and novelist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and novelist (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Catalan priest and scholar (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan priest and scholar (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan priest and scholar (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Taiwanese-English mountaineer and author (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Band\" title=\"George Band\"><PERSON></a>, Taiwanese-English mountaineer and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Band\" title=\"George Band\"><PERSON></a>, Taiwanese-English mountaineer and author (b. 1929)", "links": [{"title": "George Band", "link": "https://wikipedia.org/wiki/<PERSON>_Band"}]}, {"year": "2011", "text": "<PERSON>, American computer scientist and academic (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Scottish sergeant (b. 1949)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sergeant (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish sergeant (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American composer and businessman (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Russ_Alben"}]}, {"year": "2012", "text": "<PERSON>, American academic and diplomat, United States Ambassador to Italy (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Italy", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Italy"}]}, {"year": "2012", "text": "<PERSON>, Canadian director and producer (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-German physicist and academic (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-German physicist and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-German physicist and academic (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, French soldier (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_de_Saint_Marc\" title=\"H<PERSON>lie de Saint Marc\"><PERSON><PERSON><PERSON> <PERSON></a>, French soldier (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9<PERSON>_de_Saint_Marc\" title=\"H<PERSON>lie de Saint Marc\"><PERSON><PERSON><PERSON></a>, French soldier (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soldier and politician, 62nd Governor of Ohio (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soldier and politician (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, French lawyer and politician (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American historian, photographer, and author (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hales\"><PERSON></a>, American historian, photographer, and author (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Hales\"><PERSON></a>, American historian, photographer, and author (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English journalist (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ch%C5%ABsei_Sone\" title=\"<PERSON><PERSON><PERSON>e\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch%C5%ABsei_Sone\" title=\"<PERSON><PERSON><PERSON> Sone\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch%C5%ABsei_Sone"}]}, {"year": "2015", "text": "<PERSON>, American activist (b. 1905)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American theologian, author, and academic (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet and author (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and author (b. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Greek politician (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Filipino bishop (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Francisco_San_Diego\" title=\"Francisco San Diego\">Francisco San Diego</a>, Filipino bishop (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_San_Diego\" title=\"Francisco San Diego\">Francisco San Diego</a>, Filipino bishop (b. 1935)", "links": [{"title": "Francisco San Diego", "link": "https://wikipedia.org/wiki/Francisco_San_Diego"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American film director (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American film director (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American film director (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American playwright and author (b. 1927)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American animator (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ruby\"><PERSON></a>, American animator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American television game show host (b. 1923)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television game show host (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television game show host (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish footballer and manager (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish footballer and manager (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish footballer and manager (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sven-G%C3%B<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actor and professional wrestler (b. 1960)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and professional wrestler (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and professional wrestler (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Eudy"}]}]}}