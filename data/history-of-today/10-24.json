{"date": "October 24", "url": "https://wikipedia.org/wiki/October_24", "data": {"Events": [{"year": "69", "text": "In the Second Battle of Bedriacum, troops loyal to <PERSON><PERSON><PERSON><PERSON> defeat those of Emperor <PERSON><PERSON><PERSON><PERSON>.", "html": "69 - AD 69 - In the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Bedriacum\" class=\"mw-redirect\" title=\"Second Battle of Bedriacum\">Second Battle of Bedriacum</a>, troops loyal to <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a> defeat those of Emperor <a href=\"https://wikipedia.org/wiki/Vitellius\" title=\"Vitellius\">Vitelli<PERSON></a>.", "no_year_html": "AD 69 - In the <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Bedriacum\" class=\"mw-redirect\" title=\"Second Battle of Bedriacum\">Second Battle of Bedriacum</a>, troops loyal to <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a> defeat those of Emperor <a href=\"https://wikipedia.org/wiki/Vitellius\" title=\"Vitellius\">Vitelli<PERSON></a>.", "links": [{"title": "Second Battle of Bedriacum", "link": "https://wikipedia.org/wiki/Second_Battle_of_Bedriacum"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}]}, {"year": "1260", "text": "Chartres Cathedral is dedicated in the presence of King <PERSON> of France.", "html": "1260 - <a href=\"https://wikipedia.org/wiki/Chartres_Cathedral\" title=\"Chartres Cathedral\">Chartres Cathedral</a> is dedicated in the presence of King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chartres_Cathedral\" title=\"Chartres Cathedral\">Chartres Cathedral</a> is dedicated in the presence of King <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> IX of France\"><PERSON> of France</a>.", "links": [{"title": "Chartres Cathedral", "link": "https://wikipedia.org/wiki/Chartres_Cathedral"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}]}, {"year": "1260", "text": "After defeating the Mongols at the Battle of Ain Jalut and assassinating the previous Mamluk sultan, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ascends to the Egyptian throne as the fourth sultan of the Mamluk Sultanate.", "html": "1260 - After defeating the <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Mongols</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Jalut\" title=\"Battle of Ain Jalut\">Battle of Ain Jalut</a> and assassinating the previous Mamluk sultan, <a href=\"https://wikipedia.org/wiki/Qutuz\" title=\"Qutuz\">Qutuz</a>, <a href=\"https://wikipedia.org/wiki/Baybars\" title=\"Baybars\">Baybars</a> ascends to the Egyptian throne as the fourth sultan of the <a href=\"https://wikipedia.org/wiki/Mamluk_Sultanate\" title=\"Mamluk Sultanate\">Mamluk Sultanate</a>.", "no_year_html": "After defeating the <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Mongols</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Jalut\" title=\"Battle of Ain Jalut\">Battle of Ain Jalut</a> and assassinating the previous Mamluk sultan, <a href=\"https://wikipedia.org/wiki/Qutuz\" title=\"Qutuz\">Qutuz</a>, <a href=\"https://wikipedia.org/wiki/Baybars\" title=\"Baybars\">Baybars</a> ascends to the Egyptian throne as the fourth sultan of the <a href=\"https://wikipedia.org/wiki/Mamluk_Sultanate\" title=\"Mamluk Sultanate\">Mamluk Sultanate</a>.", "links": [{"title": "Ilkhanate", "link": "https://wikipedia.org/wiki/Ilkhanate"}, {"title": "Battle of Ain Jalut", "link": "https://wikipedia.org/wiki/Battle_of_Ain_Jalut"}, {"title": "Qutuz", "link": "https://wikipedia.org/wiki/Qutuz"}, {"title": "Baybars", "link": "https://wikipedia.org/wiki/Baybars"}, {"title": "Mamluk Sultanate", "link": "https://wikipedia.org/wiki/Mamluk_Sultanate"}]}, {"year": "1360", "text": "The Treaty of Brétigny is ratified, marking the end of the first phase of the Hundred Years' War.", "html": "1360 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Br%C3%A9tigny\" title=\"Treaty of Brétigny\">Treaty of Brétigny</a> is ratified, marking the end of the first phase of the Hundred Years' War.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Br%C3%A9tigny\" title=\"Treaty of Brétigny\">Treaty of Brétigny</a> is ratified, marking the end of the first phase of the Hundred Years' War.", "links": [{"title": "Treaty of Brétigny", "link": "https://wikipedia.org/wiki/Treaty_of_Br%C3%A9tigny"}]}, {"year": "1590", "text": "<PERSON>, the governor of the second Roanoke Colony, returns to England after an unsuccessful search for the \"lost\" colonists.", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a>, the governor of the second Roanoke Colony, returns to England after an unsuccessful search for the \"lost\" colonists.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)\" title=\"<PERSON> (colonist and artist)\"><PERSON></a>, the governor of the second Roanoke Colony, returns to England after an unsuccessful search for the \"lost\" colonists.", "links": [{"title": "<PERSON> (colonist and artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist_and_artist)"}]}, {"year": "1596", "text": "The second Spanish armada sets sail to strike against England, but is smashed by storms off Cape Finisterre forcing a retreat to port.", "html": "1596 - The <a href=\"https://wikipedia.org/wiki/2nd_Spanish_Armada\" title=\"2nd Spanish Armada\">second Spanish armada</a> sets sail to strike against England, but is smashed by storms off <a href=\"https://wikipedia.org/wiki/Cape_Finisterre\" title=\"Cape Finisterre\">Cape Finisterre</a> forcing a retreat to port.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2nd_Spanish_Armada\" title=\"2nd Spanish Armada\">second Spanish armada</a> sets sail to strike against England, but is smashed by storms off <a href=\"https://wikipedia.org/wiki/Cape_Finisterre\" title=\"Cape Finisterre\">Cape Finisterre</a> forcing a retreat to port.", "links": [{"title": "2nd Spanish Armada", "link": "https://wikipedia.org/wiki/2nd_Spanish_Armada"}, {"title": "Cape Finisterre", "link": "https://wikipedia.org/wiki/Cape_Finisterre"}]}, {"year": "1641", "text": "<PERSON><PERSON> of Kinard, the leader of the Irish Rebellion, issues his Proclamation of Dungannon, justifying the uprising and declaring continued loyalty to King <PERSON> of England.", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Felim_O%27Neill_of_Kinard\" title=\"<PERSON><PERSON> of Kinard\"><PERSON><PERSON> of Kinard</a>, the leader of the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1641\" title=\"Irish Rebellion of 1641\">Irish Rebellion</a>, issues his <a href=\"https://wikipedia.org/wiki/Proclamation_of_Dungannon\" title=\"Proclamation of Dungannon\">Proclamation of Dungannon</a>, justifying the uprising and declaring continued loyalty to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fe<PERSON>_O%27Neill_of_Kinard\" title=\"<PERSON><PERSON> of Kinard\"><PERSON><PERSON> of Kinard</a>, the leader of the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1641\" title=\"Irish Rebellion of 1641\">Irish Rebellion</a>, issues his <a href=\"https://wikipedia.org/wiki/Proclamation_of_Dungannon\" title=\"Proclamation of Dungannon\">Proclamation of Dungannon</a>, justifying the uprising and declaring continued loyalty to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON><PERSON> of Kinard", "link": "https://wikipedia.org/wiki/Felim_O%27Neill_of_Kinard"}, {"title": "Irish Rebellion of 1641", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1641"}, {"title": "Proclamation of Dungannon", "link": "https://wikipedia.org/wiki/Proclamation_of_Dungannon"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1648", "text": "The Peace of Westphalia is signed, marking the end of the Thirty Years' War and the Eighty Years' War.", "html": "1648 - The <a href=\"https://wikipedia.org/wiki/Peace_of_Westphalia\" title=\"Peace of Westphalia\">Peace of Westphalia</a> is signed, marking the end of the Thirty Years' War and the Eighty Years' War.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_of_Westphalia\" title=\"Peace of Westphalia\">Peace of Westphalia</a> is signed, marking the end of the Thirty Years' War and the Eighty Years' War.", "links": [{"title": "Peace of Westphalia", "link": "https://wikipedia.org/wiki/Peace_of_Westphalia"}]}, {"year": "1795", "text": "Poland is completely consumed by Russia, Prussia and Austria.", "html": "1795 - Poland is <a href=\"https://wikipedia.org/wiki/Third_Partition_of_Poland\" title=\"Third Partition of Poland\">completely consumed</a> by Russia, Prussia and Austria.", "no_year_html": "Poland is <a href=\"https://wikipedia.org/wiki/Third_Partition_of_Poland\" title=\"Third Partition of Poland\">completely consumed</a> by Russia, Prussia and Austria.", "links": [{"title": "Third Partition of Poland", "link": "https://wikipedia.org/wiki/Third_Partition_of_Poland"}]}, {"year": "1812", "text": "Napoleonic Wars: The Battle of Maloyaroslavets takes place near Moscow.", "html": "1812 - Napoleonic Wars: The <a href=\"https://wikipedia.org/wiki/Battle_of_Maloyaroslavets\" title=\"Battle of Maloyaroslavets\">Battle of Maloyaroslavets</a> takes place near Moscow.", "no_year_html": "Napoleonic Wars: The <a href=\"https://wikipedia.org/wiki/Battle_of_Maloyaroslavets\" title=\"Battle of Maloyaroslavets\">Battle of Maloyaroslavets</a> takes place near Moscow.", "links": [{"title": "Battle of Maloyaroslavets", "link": "https://wikipedia.org/wiki/Battle_of_Maloyaroslavets"}]}, {"year": "1813", "text": "Treaty of Gulistan: The Russo-Persian War of 1804-1813 comes to a close with the signing of the Treaty of Gulistan, under which terms Qajar Iran agrees to cede the bulk of its Caucasian territories, which comprise much of modern Dagestan, Georgia, Armenia, and Azerbaijan, to the Russian Empire.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Gulistan\" title=\"Treaty of Gulistan\">Treaty of Gulistan</a>: The <a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1804%E2%80%931813)\" title=\"Russo-Persian War (1804-1813)\">Russo-Persian War of 1804-1813</a> comes to a close with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Gulistan\" title=\"Treaty of Gulistan\">Treaty of Gulistan</a>, under which terms <a href=\"https://wikipedia.org/wiki/Qajar_Iran\" title=\"Qajar Iran\">Qajar Iran</a> agrees to cede the bulk of its Caucasian territories, which comprise much of modern <a href=\"https://wikipedia.org/wiki/Dagestan\" title=\"Dagestan\">Dagestan</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, to the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Gulistan\" title=\"Treaty of Gulistan\">Treaty of Gulistan</a>: The <a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1804%E2%80%931813)\" title=\"Russo-Persian War (1804-1813)\">Russo-Persian War of 1804-1813</a> comes to a close with the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Gulistan\" title=\"Treaty of Gulistan\">Treaty of Gulistan</a>, under which terms <a href=\"https://wikipedia.org/wiki/Qajar_Iran\" title=\"Qajar Iran\">Qajar Iran</a> agrees to cede the bulk of its Caucasian territories, which comprise much of modern <a href=\"https://wikipedia.org/wiki/Dagestan\" title=\"Dagestan\">Dagestan</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, and <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, to the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "links": [{"title": "Treaty of Gulistan", "link": "https://wikipedia.org/wiki/Treaty_of_Gulistan"}, {"title": "Russo-Persian War (1804-1813)", "link": "https://wikipedia.org/wiki/Russo-Persian_War_(1804%E2%80%931813)"}, {"title": "Treaty of Gulistan", "link": "https://wikipedia.org/wiki/Treaty_of_Gulistan"}, {"title": "Qajar Iran", "link": "https://wikipedia.org/wiki/Qajar_Iran"}, {"title": "Dagestan", "link": "https://wikipedia.org/wiki/Dagestan"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1851", "text": "<PERSON> discovers the moons <PERSON><PERSON><PERSON> and Ariel orbiting Uranus.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the moons Umbriel and Ariel orbiting Uranus.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the moons Umbriel and Ariel orbiting Uranus.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1857", "text": "Sheffield F.C., the world's oldest association football club still in operation, is founded in England.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Sheffield_F.C.\" title=\"Sheffield F.C.\">Sheffield F.C.</a>, the world's oldest association football club still in operation, is founded in England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheffield_F.C.\" title=\"Sheffield F.C.\">Sheffield F.C.</a>, the world's oldest association football club still in operation, is founded in England.", "links": [{"title": "Sheffield F.C.", "link": "https://wikipedia.org/wiki/Sheffield_F.C."}]}, {"year": "1860", "text": "Convention of Peking: The Second Opium War formally comes to a close, with Qing China ceding Kowloon in perpetuity to the victorious British Empire.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Convention_of_Peking\" title=\"Convention of Peking\">Convention of Peking</a>: The <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a> formally comes to a close, with <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> ceding <a href=\"https://wikipedia.org/wiki/Kowloon\" title=\"Kowloon\">Kowloon</a> in perpetuity to the victorious <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Convention_of_Peking\" title=\"Convention of Peking\">Convention of Peking</a>: The <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a> formally comes to a close, with <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> ceding <a href=\"https://wikipedia.org/wiki/Kowloon\" title=\"Kowloon\">Kowloon</a> in perpetuity to the victorious <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "Convention of Peking", "link": "https://wikipedia.org/wiki/Convention_of_Peking"}, {"title": "Second Opium War", "link": "https://wikipedia.org/wiki/Second_Opium_War"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Kowloon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1861", "text": "The first transcontinental telegraph line across the United States is completed.", "html": "1861 - The <a href=\"https://wikipedia.org/wiki/First_transcontinental_telegraph\" title=\"First transcontinental telegraph\">first transcontinental telegraph</a> line across the United States is completed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_transcontinental_telegraph\" title=\"First transcontinental telegraph\">first transcontinental telegraph</a> line across the United States is completed.", "links": [{"title": "First transcontinental telegraph", "link": "https://wikipedia.org/wiki/First_transcontinental_telegraph"}]}, {"year": "1871", "text": "An estimated 17 to 22 Chinese immigrants are lynched in Los Angeles, California.", "html": "1871 - An estimated 17 to 22 Chinese immigrants are <a href=\"https://wikipedia.org/wiki/Chinese_massacre_of_1871\" class=\"mw-redirect\" title=\"Chinese massacre of 1871\">lynched</a> in Los Angeles, California.", "no_year_html": "An estimated 17 to 22 Chinese immigrants are <a href=\"https://wikipedia.org/wiki/Chinese_massacre_of_1871\" class=\"mw-redirect\" title=\"Chinese massacre of 1871\">lynched</a> in Los Angeles, California.", "links": [{"title": "Chinese massacre of 1871", "link": "https://wikipedia.org/wiki/Chinese_massacre_of_1871"}]}, {"year": "1876", "text": "Shinpūren rebellion: Upset at the Westernisation of Meiji Japan and the abolition of the Tokugawa feudal hierarchy, the Keishintō, a group of extremist Shinto former samurai, launch a surprise attack against the Meiji government in Kumamoto Prefecture.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Shinp%C5%ABren_rebellion\" title=\"Shinpūren rebellion\">Shinpūren rebellion</a>: Upset at the Westernisation of <a href=\"https://wikipedia.org/wiki/Meiji_period\" class=\"mw-redirect\" title=\"Meiji period\">Meiji Japan</a> and the abolition of the <a href=\"https://wikipedia.org/wiki/Edo_society\" title=\"Edo society\">Tokugawa feudal hierarchy</a>, the <i>Keishintō</i>, a group of extremist <a href=\"https://wikipedia.org/wiki/Shinto\" title=\"Shinto\">Shinto</a> <a href=\"https://wikipedia.org/wiki/Shizoku\" title=\"<PERSON><PERSON><PERSON>\">former samurai</a>, launch a surprise attack against the Meiji government in <a href=\"https://wikipedia.org/wiki/Kumamoto_Prefecture\" title=\"Kumamoto Prefecture\">Kumamoto Prefecture</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shinp%C5%ABren_rebellion\" title=\"Shinpūren rebellion\">Shinpūren rebellion</a>: Upset at the Westernisation of <a href=\"https://wikipedia.org/wiki/Meiji_period\" class=\"mw-redirect\" title=\"Meiji period\">Meiji Japan</a> and the abolition of the <a href=\"https://wikipedia.org/wiki/Edo_society\" title=\"Edo society\">Tokugawa feudal hierarchy</a>, the <i>Keishintō</i>, a group of extremist <a href=\"https://wikipedia.org/wiki/Shinto\" title=\"Shinto\">Shinto</a> <a href=\"https://wikipedia.org/wiki/Shizoku\" title=\"<PERSON>zo<PERSON>\">former samurai</a>, launch a surprise attack against the Meiji government in <a href=\"https://wikipedia.org/wiki/Kumamoto_Prefecture\" title=\"Kumamoto Prefecture\">Kumamoto Prefecture</a>.", "links": [{"title": "Shinpūren rebellion", "link": "https://wikipedia.org/wiki/Shinp%C5%ABren_rebellion"}, {"title": "Meiji period", "link": "https://wikipedia.org/wiki/Meiji_period"}, {"title": "Edo society", "link": "https://wikipedia.org/wiki/Edo_society"}, {"title": "Shin<PERSON>", "link": "https://wikipedia.org/wiki/Shinto"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Kumamoto Prefecture", "link": "https://wikipedia.org/wiki/Kumamoto_Prefecture"}]}, {"year": "1886", "text": "Normanton incident: As the British merchant vessel <PERSON><PERSON> sinks off the coast of Japan, her European officers appear to commandeer the ship’s lifeboats for themselves, leaving her Asian crew and passengers to die and conjuring significant political outrage in Japan.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_incident\" title=\"Normanton incident\">Normanton incident</a>: As the British merchant vessel <i>Normanton</i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>, her European officers appear to commandeer the ship’s lifeboats for themselves, leaving her Asian crew and passengers to die and conjuring significant political outrage in Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_incident\" title=\"Normanton incident\">Normanton incident</a>: As the British merchant vessel <i>Normanton</i> sinks off the coast of <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>, her European officers appear to commandeer the ship’s lifeboats for themselves, leaving her Asian crew and passengers to die and conjuring significant political outrage in Japan.", "links": [{"title": "Normanton incident", "link": "https://wikipedia.org/wiki/Norman<PERSON>_incident"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1889", "text": "<PERSON> delivers the Tenterfield Oration, effectively starting the federation process in Australia.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Tenterfield_Oration\" title=\"Tenterfield Oration\">Tenterfield Oration</a>, effectively starting the federation process in Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Tenterfield_Oration\" title=\"Tenterfield Oration\">Tenterfield Oration</a>, effectively starting the federation process in Australia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henry_<PERSON>"}, {"title": "Tenterfield Oration", "link": "https://wikipedia.org/wiki/Tenterfield_Oration"}]}, {"year": "1894", "text": "First Sino-Japanese War: Battle of Jiuliancheng: Under the command of General <PERSON><PERSON><PERSON>, the Imperial Japanese Army covertly crosses the Yalu River into Qing territory and launches an assault on the fortifications at Hushan.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jiuliancheng\" title=\"Battle of Jiuliancheng\">Battle of Jiuliancheng</a>: Under the command of General <a href=\"https://wikipedia.org/wiki/Yamagata_Aritomo\" title=\"Yamagata Aritomo\">Yamagata Aritomo</a>, the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> covertly crosses the <a href=\"https://wikipedia.org/wiki/Yalu_River\" title=\"Yalu River\">Yalu River</a> into <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> territory and launches an assault on the fortifications at <a href=\"https://wikipedia.org/wiki/Hushan_Great_Wall\" title=\"Hushan Great Wall\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jiuliancheng\" title=\"Battle of Jiuliancheng\">Battle of Jiuliancheng</a>: Under the command of General <a href=\"https://wikipedia.org/wiki/Yamagata_Aritomo\" title=\"Yamagata Aritomo\">Yamagata Aritomo</a>, the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> covertly crosses the <a href=\"https://wikipedia.org/wiki/Yalu_River\" title=\"Yalu River\">Yalu River</a> into <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing</a> territory and launches an assault on the fortifications at <a href=\"https://wikipedia.org/wiki/Hushan_Great_Wall\" title=\"Hushan Great Wall\"><PERSON><PERSON></a>.", "links": [{"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}, {"title": "Battle of Jiuliancheng", "link": "https://wikipedia.org/wiki/Battle_of_Jiuliancheng"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yamagata_Aritomo"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Yalu River", "link": "https://wikipedia.org/wiki/Yalu_River"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Hushan Great Wall", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Great_Wall"}]}, {"year": "1900", "text": "U.S. Government announces plans to buy Danish West Indies for $7 million.", "html": "1900 - U.S. Government announces plans to buy <a href=\"https://wikipedia.org/wiki/Danish_West_Indies\" title=\"Danish West Indies\">Danish West Indies</a> for $7 million.", "no_year_html": "U.S. Government announces plans to buy <a href=\"https://wikipedia.org/wiki/Danish_West_Indies\" title=\"Danish West Indies\">Danish West Indies</a> for $7 million.", "links": [{"title": "Danish West Indies", "link": "https://wikipedia.org/wiki/Danish_West_Indies"}]}, {"year": "1901", "text": "<PERSON> becomes the first person to go over Niagara Falls in a barrel.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to go over Niagara Falls in a barrel.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to go over Niagara Falls in a barrel.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "Guatemala's Santa María volcano begins to erupt, becoming the third-largest eruption of the 20th century.", "html": "1902 - Guatemala's <a href=\"https://wikipedia.org/wiki/1902_eruption_of_Santa_Mar%C3%ADa\" title=\"1902 eruption of Santa María\">Santa María volcano begins to erupt</a>, becoming the third-largest eruption of the 20th century.", "no_year_html": "Guatemala's <a href=\"https://wikipedia.org/wiki/1902_eruption_of_Santa_Mar%C3%ADa\" title=\"1902 eruption of Santa María\">Santa María volcano begins to erupt</a>, becoming the third-largest eruption of the 20th century.", "links": [{"title": "1902 eruption of Santa María", "link": "https://wikipedia.org/wiki/1902_eruption_of_Santa_Mar%C3%ADa"}]}, {"year": "1911", "text": "<PERSON><PERSON> remains in the air nine minutes and 45 seconds in a glider at Kill Devil Hills, North Carolina.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Wright\"><PERSON><PERSON></a> remains in the air nine minutes and 45 seconds in a glider at Kill Devil Hills, North Carolina.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Wright\"><PERSON><PERSON></a> remains in the air nine minutes and 45 seconds in a glider at Kill Devil Hills, North Carolina.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "First Balkan War: The Battle of Kirk Kilisse concludes with a Bulgarian victory against the Ottoman Empire.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kirk_Kilisse\" title=\"Battle of Kirk Kilisse\">Battle of Kirk Kilisse</a> concludes with a Bulgarian victory against the Ottoman Empire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kirk_Kilisse\" title=\"Battle of Kirk Kilisse\">Battle of Kirk Kilisse</a> concludes with a Bulgarian victory against the Ottoman Empire.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Kirk Kilisse", "link": "https://wikipedia.org/wiki/Battle_of_Kirk_<PERSON>"}]}, {"year": "1912", "text": "First Balkan War: The Battle of Kumanovo concludes with the Serbian victory against the Ottoman Empire.", "html": "1912 - First Balkan War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kumanovo\" title=\"Battle of Kumanovo\">Battle of Kumanovo</a> concludes with the Serbian victory against the Ottoman Empire.", "no_year_html": "First Balkan War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kumanovo\" title=\"Battle of Kumanovo\">Battle of Kumanovo</a> concludes with the Serbian victory against the Ottoman Empire.", "links": [{"title": "Battle of Kumanovo", "link": "https://wikipedia.org/wiki/Battle_of_Kumanovo"}]}, {"year": "1917", "text": "World War I: Italy suffers a disastrous defeat at the Battle of Caporetto on the Austro-Italian front.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Italy suffers a disastrous defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Caporetto\" title=\"Battle of Caporetto\">Battle of Caporetto</a> on the Austro-Italian front.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Italy suffers a disastrous defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_Caporetto\" title=\"Battle of Caporetto\">Battle of Caporetto</a> on the Austro-Italian front.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Caporetto", "link": "https://wikipedia.org/wiki/Battle_of_Caporetto"}]}, {"year": "1918", "text": "World War I: Italian victory in the Battle of Vittorio Veneto.", "html": "1918 - World War I: Italian victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vittorio_Veneto\" title=\"Battle of Vittorio Veneto\">Battle of Vittorio Veneto</a>.", "no_year_html": "World War I: Italian victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vittorio_Veneto\" title=\"Battle of Vittorio Veneto\">Battle of Vittorio Veneto</a>.", "links": [{"title": "Battle of Vittorio Veneto", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>itt<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>'s last performance takes place at the Garrick Theatre in Detroit.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s last performance takes place at the Garrick Theatre in Detroit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s last performance takes place at the Garrick Theatre in Detroit.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "\"Black Thursday\" on the New York Stock Exchange.", "html": "1929 - \"<a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">Black Thursday</a>\" on the New York Stock Exchange.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">Black Thursday</a>\" on the New York Stock Exchange.", "links": [{"title": "Wall Street Crash of 1929", "link": "https://wikipedia.org/wiki/Wall_Street_Crash_of_1929"}]}, {"year": "1930", "text": "A bloodless coup d'état in Brazil ends the First Republic, replacing it with the Vargas Era.", "html": "1930 - A bloodless <a href=\"https://wikipedia.org/wiki/Brazilian_Revolution_of_1930\" title=\"Brazilian Revolution of 1930\">coup d'état</a> in Brazil ends the First Republic, replacing it with the Vargas Era.", "no_year_html": "A bloodless <a href=\"https://wikipedia.org/wiki/Brazilian_Revolution_of_1930\" title=\"Brazilian Revolution of 1930\">coup d'état</a> in Brazil ends the First Republic, replacing it with the Vargas Era.", "links": [{"title": "Brazilian Revolution of 1930", "link": "https://wikipedia.org/wiki/Brazilian_Revolution_of_1930"}]}, {"year": "1931", "text": "The George Washington Bridge opens to public traffic over the Hudson River.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/George_Washington_Bridge\" title=\"George Washington Bridge\">George Washington Bridge</a> opens to public traffic over the Hudson River.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/George_Washington_Bridge\" title=\"George Washington Bridge\">George Washington Bridge</a> opens to public traffic over the Hudson River.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington_Bridge"}]}, {"year": "1944", "text": "World War II: Japan's center force is temporarily repulsed in the Battle of Leyte Gulf.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japan's center force is temporarily repulsed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Japan's center force is temporarily repulsed in the <a href=\"https://wikipedia.org/wiki/Battle_of_Leyte_Gulf\" title=\"Battle of Leyte Gulf\">Battle of Leyte Gulf</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Leyte Gulf", "link": "https://wikipedia.org/wiki/Battle_of_Leyte_Gulf"}]}, {"year": "1944", "text": "World War II: The USS Shark (SS-314) was lost with all 87 hands in the Bashi Straits after torpedoing the Japanese freighter <PERSON>san <PERSON>.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/USS_Shark_(SS-314)\" title=\"USS Shark (SS-314)\">USS <i>Shark</i> (SS-314)</a> was lost with all 87 hands in the <a href=\"https://wikipedia.org/wiki/Bashi_Channel\" title=\"Bashi Channel\">Bashi Straits</a> after torpedoing the Japanese freighter <i><a href=\"https://wikipedia.org/wiki/Arisan_Maru\" title=\"Arisan Maru\"><PERSON><PERSON></a></i>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/USS_Shark_(SS-314)\" title=\"USS Shark (SS-314)\">USS <i>Shark</i> (SS-314)</a> was lost with all 87 hands in the <a href=\"https://wikipedia.org/wiki/Bashi_Channel\" title=\"Bashi Channel\">Bashi Straits</a> after torpedoing the Japanese freighter <i><a href=\"https://wikipedia.org/wiki/Arisan_Maru\" title=\"Arisan Maru\"><PERSON><PERSON></a></i>.", "links": [{"title": "USS Shark (SS-314)", "link": "https://wikipedia.org/wiki/USS_Shark_(SS-314)"}, {"title": "Bashi Channel", "link": "https://wikipedia.org/wiki/Bashi_Channel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1944", "text": "World War II: The USS Tang (SS-306) sank in the Formosa Strait after being struck by its own torpedo, with 78 of its crew lost.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/USS_Tang_(SS-306)\" title=\"USS Tang (SS-306)\">USS <i><PERSON></i> (SS-306)</a> sank in the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Formosa Strait</a> after being struck by its own torpedo, with 78 of its crew lost.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/USS_Tang_(SS-306)\" title=\"USS Tang (SS-306)\">USS <i>Tang</i> (SS-306)</a> sank in the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Formosa Strait</a> after being struck by its own torpedo, with 78 of its crew lost.", "links": [{"title": "USS Tang (SS-306)", "link": "https://wikipedia.org/wiki/USS_<PERSON>_(SS-306)"}, {"title": "Taiwan Strait", "link": "https://wikipedia.org/wiki/Taiwan_Strait"}]}, {"year": "1945", "text": "The United Nations Charter comes into effect.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/United_Nations_Charter\" class=\"mw-redirect\" title=\"United Nations Charter\">United Nations Charter</a> comes into effect.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations_Charter\" class=\"mw-redirect\" title=\"United Nations Charter\">United Nations Charter</a> comes into effect.", "links": [{"title": "United Nations Charter", "link": "https://wikipedia.org/wiki/United_Nations_Charter"}]}, {"year": "1946", "text": "A camera on board the V-2 No. 13 rocket takes the first photograph of earth from outer space.", "html": "1946 - A camera on board the <a href=\"https://wikipedia.org/wiki/V-2_No._13\" title=\"V-2 No. 13\">V-2 No. 13</a> rocket takes the first photograph of earth from outer space.", "no_year_html": "A camera on board the <a href=\"https://wikipedia.org/wiki/V-2_No._13\" title=\"V-2 No. 13\">V-2 No. 13</a> rocket takes the first photograph of earth from outer space.", "links": [{"title": "V-2 No. 13", "link": "https://wikipedia.org/wiki/V-2_No._13"}]}, {"year": "1947", "text": "Famed animator <PERSON> testifies before the House Un-American Activities Committee, naming Disney employees he believes to be communists.", "html": "1947 - Famed animator <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a> testifies before the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a>, naming Disney employees he believes to be communists.", "no_year_html": "Famed animator <a href=\"https://wikipedia.org/wiki/Walt_Disney\" title=\"Walt Disney\"><PERSON></a> testifies before the <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a>, naming Disney employees he believes to be communists.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}]}, {"year": "1947", "text": "United Air Lines Flight 608 crashes in the Bryce Canyon National Park in Garfield County, Utah, while attempting an emergency landing at Bryce Canyon Airport, killing 52 people.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_608\" title=\"United Air Lines Flight 608\">United Air Lines Flight 608</a> crashes in the <a href=\"https://wikipedia.org/wiki/Bryce_Canyon_National_Park\" title=\"Bryce Canyon National Park\">Bryce Canyon National Park</a> in <a href=\"https://wikipedia.org/wiki/Garfield_County,_Utah\" title=\"Garfield County, Utah\">Garfield County, Utah</a>, while attempting an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Bryce_Canyon_Airport\" title=\"Bryce Canyon Airport\">Bryce Canyon Airport</a>, killing 52 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_608\" title=\"United Air Lines Flight 608\">United Air Lines Flight 608</a> crashes in the <a href=\"https://wikipedia.org/wiki/Bryce_Canyon_National_Park\" title=\"Bryce Canyon National Park\">Bryce Canyon National Park</a> in <a href=\"https://wikipedia.org/wiki/Garfield_County,_Utah\" title=\"Garfield County, Utah\">Garfield County, Utah</a>, while attempting an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Bryce_Canyon_Airport\" title=\"Bryce Canyon Airport\">Bryce Canyon Airport</a>, killing 52 people.", "links": [{"title": "United Air Lines Flight 608", "link": "https://wikipedia.org/wiki/United_Air_Lines_Flight_608"}, {"title": "Bryce Canyon National Park", "link": "https://wikipedia.org/wiki/Bryce_Canyon_National_Park"}, {"title": "Garfield County, Utah", "link": "https://wikipedia.org/wiki/Garfield_County,_Utah"}, {"title": "Emergency landing", "link": "https://wikipedia.org/wiki/Emergency_landing"}, {"title": "Bryce Canyon Airport", "link": "https://wikipedia.org/wiki/Bryce_Canyon_Airport"}]}, {"year": "1949", "text": "The cornerstone of the United Nations Headquarters is laid.", "html": "1949 - The cornerstone of the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> is laid.", "no_year_html": "The cornerstone of the <a href=\"https://wikipedia.org/wiki/United_Nations_Headquarters\" class=\"mw-redirect\" title=\"United Nations Headquarters\">United Nations Headquarters</a> is laid.", "links": [{"title": "United Nations Headquarters", "link": "https://wikipedia.org/wiki/United_Nations_Headquarters"}]}, {"year": "1950", "text": "Annexation of Tibet by the People's Republic of China: The People’s Liberation Army ceases all military operations in Tibet, ending the Battle of Chamdo.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Annexation_of_Tibet_by_the_People%27s_Republic_of_China\" title=\"Annexation of Tibet by the People's Republic of China\">Annexation of Tibet by the People's Republic of China</a>: The <a href=\"https://wikipedia.org/wiki/People%E2%80%99s_Liberation_Army\" class=\"mw-redirect\" title=\"People’s Liberation Army\">People’s Liberation Army</a> ceases all military operations in <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibet</a>, ending the <a href=\"https://wikipedia.org/wiki/Battle_of_Chamdo\" title=\"Battle of Chamdo\">Battle of Chamdo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Annexation_of_Tibet_by_the_People%27s_Republic_of_China\" title=\"Annexation of Tibet by the People's Republic of China\">Annexation of Tibet by the People's Republic of China</a>: The <a href=\"https://wikipedia.org/wiki/People%E2%80%99s_Liberation_Army\" class=\"mw-redirect\" title=\"People’s Liberation Army\">People’s Liberation Army</a> ceases all military operations in <a href=\"https://wikipedia.org/wiki/Tibet\" title=\"Tibet\">Tibet</a>, ending the <a href=\"https://wikipedia.org/wiki/Battle_of_Chamdo\" title=\"Battle of Chamdo\">Battle of Chamdo</a>.", "links": [{"title": "Annexation of Tibet by the People's Republic of China", "link": "https://wikipedia.org/wiki/Annexation_of_Tibet_by_the_People%27s_Republic_of_China"}, {"title": "People’s Liberation Army", "link": "https://wikipedia.org/wiki/People%E2%80%99s_Liberation_Army"}, {"title": "Tibet", "link": "https://wikipedia.org/wiki/Tibet"}, {"title": "Battle of Chamdo", "link": "https://wikipedia.org/wiki/Battle_of_Chamdo"}]}, {"year": "1954", "text": "US President <PERSON> pledges United States support to South Vietnam.", "html": "1954 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges United States support to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pledges United States support to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1957", "text": "The United States Air Force starts the X-20 Dyna-Soar crewed space program.", "html": "1957 - The United States Air Force starts the <a href=\"https://wikipedia.org/wiki/X-20_Dyna-Soar\" class=\"mw-redirect\" title=\"X-20 Dyna-Soar\">X-20 Dyna-Soar</a> crewed space program.", "no_year_html": "The United States Air Force starts the <a href=\"https://wikipedia.org/wiki/X-20_Dyna-Soar\" class=\"mw-redirect\" title=\"X-20 Dyna-Soar\">X-20 Dyna-Soar</a> crewed space program.", "links": [{"title": "X-20 Dyna-Soar", "link": "https://wikipedia.org/wiki/X-20_Dyna-Soar"}]}, {"year": "1960", "text": "Nedelin catastrophe: An R-16 ballistic missile explodes on the launch pad at the Soviet Union's Baikonur Cosmodrome space facility, killing over 100 people, including Field Marshal <PERSON><PERSON><PERSON>.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Nedelin_catastrophe\" title=\"Nedelin catastrophe\">Nedelin catastrophe</a>: An <a href=\"https://wikipedia.org/wiki/R-16_(missile)\" title=\"R-16 (missile)\">R-16</a> <a href=\"https://wikipedia.org/wiki/Ballistic_missile\" title=\"Ballistic missile\">ballistic missile</a> explodes on the launch pad at the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> space facility, killing over 100 people, including Field Marshal <a href=\"https://wikipedia.org/wiki/Mitrofan_Nedelin\" title=\"Mitrofan Nedelin\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_catastrophe\" title=\"Nedelin catastrophe\">Nedelin catastrophe</a>: An <a href=\"https://wikipedia.org/wiki/R-16_(missile)\" title=\"R-16 (missile)\">R-16</a> <a href=\"https://wikipedia.org/wiki/Ballistic_missile\" title=\"Ballistic missile\">ballistic missile</a> explodes on the launch pad at the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> space facility, killing over 100 people, including Field Marshal <a href=\"https://wikipedia.org/wiki/Mitrofan_Nedelin\" title=\"Mitrofan Nedelin\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Nedelin catastrophe", "link": "https://wikipedia.org/wiki/<PERSON>elin_catastrophe"}, {"title": "R-16 (missile)", "link": "https://wikipedia.org/wiki/R-16_(missile)"}, {"title": "Ballistic missile", "link": "https://wikipedia.org/wiki/Ballistic_missile"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Baikonur Cosmodrome", "link": "https://wikipedia.org/wiki/Baikonur_Cosmodrome"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "An oxygen leak from an R-9 Desna missile at the Baikonur Cosmodrome triggers a fire that kills seven people.", "html": "1963 - An oxygen leak from an <a href=\"https://wikipedia.org/wiki/R-9_<PERSON>na\" title=\"R-9 Desna\">R-9 Desna</a> missile at the Baikonur Cosmodrome triggers a fire that kills seven people.", "no_year_html": "An oxygen leak from an <a href=\"https://wikipedia.org/wiki/R-9_<PERSON>na\" title=\"R-9 Desna\">R-9 <PERSON>na</a> missile at the Baikonur Cosmodrome triggers a fire that kills seven people.", "links": [{"title": "R-9 Des<PERSON>", "link": "https://wikipedia.org/wiki/R-9_<PERSON><PERSON>"}]}, {"year": "1964", "text": "Northern Rhodesia gains independence from the United Kingdom and becomes Zambia.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Northern_Rhodesia\" title=\"Northern Rhodesia\">Northern Rhodesia</a> gains independence from the United Kingdom and becomes Zambia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northern_Rhodesia\" title=\"Northern Rhodesia\">Northern Rhodesia</a> gains independence from the United Kingdom and becomes Zambia.", "links": [{"title": "Northern Rhodesia", "link": "https://wikipedia.org/wiki/Northern_Rhodesia"}]}, {"year": "1975", "text": "In Iceland, 90% of women take part in a national strike, refusing to work in protest of gender inequality.", "html": "1975 - In Iceland, 90% of women <a href=\"https://wikipedia.org/wiki/1975_Icelandic_women%27s_strike\" title=\"1975 Icelandic women's strike\">take part in a national strike</a>, refusing to work in protest of gender inequality.", "no_year_html": "In Iceland, 90% of women <a href=\"https://wikipedia.org/wiki/1975_Icelandic_women%27s_strike\" title=\"1975 Icelandic women's strike\">take part in a national strike</a>, refusing to work in protest of gender inequality.", "links": [{"title": "1975 Icelandic women's strike", "link": "https://wikipedia.org/wiki/1975_Icelandic_women%27s_strike"}]}, {"year": "1980", "text": "The government of Poland legalizes the Solidarity trade union.", "html": "1980 - The government of Poland legalizes the <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity trade union</a>.", "no_year_html": "The government of Poland legalizes the <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity trade union</a>.", "links": [{"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON> is sentenced to 45 years in prison, the longest sentence handed down by a British court, for the attempted bombing of an El Al flight at Heathrow Airport.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Nezar_Hindawi\" class=\"mw-redirect\" title=\"Nezar Hindawi\"><PERSON><PERSON><PERSON></a> is sentenced to 45 years in prison, the longest sentence handed down by a British court, for the attempted bombing of an El Al flight at Heathrow Airport.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nez<PERSON>_<PERSON>wi\" class=\"mw-redirect\" title=\"Nez<PERSON> Hindawi\"><PERSON><PERSON><PERSON></a> is sentenced to 45 years in prison, the longest sentence handed down by a British court, for the attempted bombing of an El Al flight at Heathrow Airport.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nez<PERSON>_<PERSON>wi"}]}, {"year": "1990", "text": "Italian prime minister <PERSON><PERSON><PERSON> reveals to the Italian parliament the existence of Gladio, the Italian NATO force formed in 1956, intended to be activated in the event of a Warsaw Pact invasion.", "html": "1990 - Italian prime minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> reveals to the Italian parliament the existence of <a href=\"https://wikipedia.org/wiki/Operation_Gladio\" title=\"Operation Gladio\">Gladio</a>, the Italian NATO force formed in 1956, intended to be activated in the event of a Warsaw Pact invasion.", "no_year_html": "Italian prime minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> reveals to the Italian parliament the existence of <a href=\"https://wikipedia.org/wiki/Operation_Gladio\" title=\"Operation Gladio\">Gladio</a>, the Italian NATO force formed in 1956, intended to be activated in the event of a Warsaw Pact invasion.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Operation Gladio", "link": "https://wikipedia.org/wiki/Operation_Gladio"}]}, {"year": "1992", "text": "The Toronto Blue Jays become the first Major League Baseball team based outside the United States to win the World Series.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Toronto_Blue_Jays\" title=\"Toronto Blue Jays\">Toronto Blue Jays</a> become the first Major League Baseball team based outside the United States to win the World Series.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Toronto_Blue_Jays\" title=\"Toronto Blue Jays\">Toronto Blue Jays</a> become the first Major League Baseball team based outside the United States to win the World Series.", "links": [{"title": "Toronto Blue Jays", "link": "https://wikipedia.org/wiki/Toronto_Blue_Jays"}]}, {"year": "1998", "text": "Deep Space 1 is launched to explore the asteroid belt and test new spacecraft technologies.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Deep_Space_1\" title=\"Deep Space 1\">Deep Space 1</a> is launched to explore the asteroid belt and test new spacecraft technologies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deep_Space_1\" title=\"Deep Space 1\">Deep Space 1</a> is launched to explore the asteroid belt and test new spacecraft technologies.", "links": [{"title": "Deep Space 1", "link": "https://wikipedia.org/wiki/Deep_Space_1"}]}, {"year": "2003", "text": "Concorde makes its last commercial flight.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> makes its last commercial flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> makes its last commercial flight.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}]}, {"year": "2004", "text": "Arsenal Football Club loses to Manchester United, ending a row of unbeaten matches at 49 matches, which is the record in the Premier League.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Arsenal_F.C.\" title=\"Arsenal F.C.\">Arsenal Football Club</a> loses to Manchester United, ending a row of unbeaten matches at 49 matches, which is the record in the Premier League.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arsenal_F.C.\" title=\"Arsenal F.C.\">Arsenal Football Club</a> loses to Manchester United, ending a row of unbeaten matches at 49 matches, which is the record in the Premier League.", "links": [{"title": "Arsenal F.C.", "link": "https://wikipedia.org/wiki/Arsenal_F.C."}]}, {"year": "2005", "text": "Hurricane <PERSON><PERSON><PERSON> makes landfall in Florida, resulting in 35 direct and 26 indirect fatalities and causing $20.6B USD in damage.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Hurricane_Wilma\" title=\"Hurricane Wilma\">Hurricane <PERSON><PERSON><PERSON></a> makes landfall in Florida, resulting in 35 direct and 26 indirect fatalities and causing $20.6B USD in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Wilma\" title=\"Hurricane Wilma\">Hurricane <PERSON><PERSON><PERSON></a> makes landfall in Florida, resulting in 35 direct and 26 indirect fatalities and causing $20.6B USD in damage.", "links": [{"title": "Hurricane <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hurricane_Wilma"}]}, {"year": "2007", "text": "Chang'e 1, the first satellite in the Chinese Lunar Exploration Program, is launched from Xichang Satellite Launch Center.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27e_1\" title=\"Chang'e 1\">Chang'e 1</a>, the first satellite in the Chinese Lunar Exploration Program, is launched from Xichang Satellite Launch Center.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27e_1\" title=\"Chang'e 1\">Chang'e 1</a>, the first satellite in the Chinese Lunar Exploration Program, is launched from Xichang Satellite Launch Center.", "links": [{"title": "Chang'e 1", "link": "https://wikipedia.org/wiki/Chang%27e_1"}]}, {"year": "2008", "text": "\"Bloody Friday\" saw many of the world's stock exchanges experience the worst declines in their history, with drops of around 10% in most indices.", "html": "2008 - \"<a href=\"https://wikipedia.org/wiki/Stock_market_crash#Crash_of_2008-2009\" title=\"Stock market crash\">Bloody Friday</a>\" saw many of the world's stock exchanges experience the worst declines in their history, with drops of around 10% in most indices.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/Stock_market_crash#Crash_of_2008-2009\" title=\"Stock market crash\">Bloody Friday</a>\" saw many of the world's stock exchanges experience the worst declines in their history, with drops of around 10% in most indices.", "links": [{"title": "Stock market crash", "link": "https://wikipedia.org/wiki/Stock_market_crash#Crash_of_2008-2009"}]}, {"year": "2014", "text": "The China National Space Administration launches an experimental lunar mission, Chang'e 5-T1, which will loop behind the Moon and return to Earth.", "html": "2014 - The China National Space Administration launches an experimental lunar mission, <a href=\"https://wikipedia.org/wiki/Chang%27e_5-T1\" title=\"Chang'e 5-T1\">Chang'e 5-T1</a>, which will loop behind the Moon and return to Earth.", "no_year_html": "The China National Space Administration launches an experimental lunar mission, <a href=\"https://wikipedia.org/wiki/Chang%27e_5-T1\" title=\"Chang'e 5-T1\">Chang'e 5-T1</a>, which will loop behind the Moon and return to Earth.", "links": [{"title": "Chang<PERSON><PERSON> 5-T1", "link": "https://wikipedia.org/wiki/Chang%27e_5-T1"}]}, {"year": "2015", "text": "A driver crashes into the Oklahoma State Homecoming parade, killing four people and injuring 34.", "html": "2015 - A driver <a href=\"https://wikipedia.org/wiki/2015_Oklahoma_State_University_homecoming_parade_attack\" title=\"2015 Oklahoma State University homecoming parade attack\">crashes</a> into the Oklahoma State Homecoming parade, killing four people and injuring 34.", "no_year_html": "A driver <a href=\"https://wikipedia.org/wiki/2015_Oklahoma_State_University_homecoming_parade_attack\" title=\"2015 Oklahoma State University homecoming parade attack\">crashes</a> into the Oklahoma State Homecoming parade, killing four people and injuring 34.", "links": [{"title": "2015 Oklahoma State University homecoming parade attack", "link": "https://wikipedia.org/wiki/2015_Oklahoma_State_University_homecoming_parade_attack"}]}, {"year": "2016", "text": "A French surveillance aircraft flying to Libya crashes on takeoff in Malta, killing all five people on board.", "html": "2016 - A French surveillance aircraft flying to Libya <a href=\"https://wikipedia.org/wiki/2016_Malta_Fairchild_Merlin_crash\" title=\"2016 Malta Fairchild Merlin crash\">crashes</a> on takeoff in Malta, killing all five people on board.", "no_year_html": "A French surveillance aircraft flying to Libya <a href=\"https://wikipedia.org/wiki/2016_Malta_Fairchild_Merlin_crash\" title=\"2016 Malta Fairchild Merlin crash\">crashes</a> on takeoff in Malta, killing all five people on board.", "links": [{"title": "2016 Malta Fairchild Merlin crash", "link": "https://wikipedia.org/wiki/2016_Malta_Fairchild_Merlin_crash"}]}, {"year": "2016", "text": "Three heavily-armed terrorists from the Islamic State - Khorasan Province open fire on and eventually suicide bomb a police training centre in Balochistan, Pakistan, killing at least 59 cadets and injuring more than 165 others.", "html": "2016 - Three heavily-armed terrorists from the <a href=\"https://wikipedia.org/wiki/Islamic_State_%E2%80%93_Khorasan_Province\" title=\"Islamic State - Khorasan Province\">Islamic State - Khorasan Province</a> <a href=\"https://wikipedia.org/wiki/2016_Quetta_police_training_college_attack\" title=\"2016 Quetta police training college attack\">open fire on and eventually suicide bomb</a> a police training centre in <a href=\"https://wikipedia.org/wiki/Balochistan\" title=\"Balochistan\">Balochistan</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing at least 59 cadets and injuring more than 165 others.", "no_year_html": "Three heavily-armed terrorists from the <a href=\"https://wikipedia.org/wiki/Islamic_State_%E2%80%93_Khorasan_Province\" title=\"Islamic State - Khorasan Province\">Islamic State - Khorasan Province</a> <a href=\"https://wikipedia.org/wiki/2016_Quetta_police_training_college_attack\" title=\"2016 Quetta police training college attack\">open fire on and eventually suicide bomb</a> a police training centre in <a href=\"https://wikipedia.org/wiki/Balochistan\" title=\"Balochistan\">Balochistan</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing at least 59 cadets and injuring more than 165 others.", "links": [{"title": "Islamic State - Khorasan Province", "link": "https://wikipedia.org/wiki/Islamic_State_%E2%80%93_Khorasan_Province"}, {"title": "2016 Quetta police training college attack", "link": "https://wikipedia.org/wiki/2016_Quetta_police_training_college_attack"}, {"title": "Balochistan", "link": "https://wikipedia.org/wiki/Balochistan"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2018", "text": "The world's longest sea crossing, the Hong Kong-Zhuhai-Macau Bridge, opens for public traffic.", "html": "2018 - The world's longest sea crossing, the <a href=\"https://wikipedia.org/wiki/Hong_Kong%E2%80%93Zhuhai%E2%80%93Macau_Bridge\" title=\"Hong Kong-Zhuhai-Macau Bridge\">Hong Kong-Zhuhai-Macau Bridge</a>, opens for public traffic.", "no_year_html": "The world's longest sea crossing, the <a href=\"https://wikipedia.org/wiki/Hong_Kong%E2%80%93Zhuhai%E2%80%93Macau_Bridge\" title=\"Hong Kong-Zhuhai-Macau Bridge\">Hong Kong-Zhuhai-Macau Bridge</a>, opens for public traffic.", "links": [{"title": "Hong Kong-Zhuhai-Macau Bridge", "link": "https://wikipedia.org/wiki/Hong_Kong%E2%80%93Zhuhai%E2%80%93Macau_Bridge"}]}], "Births": [{"year": "51", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 96)", "html": "51 - AD 51 - <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a>, Roman emperor (d. 96)", "no_year_html": "AD 51 - <a href=\"https://wikipedia.org/wiki/Domitian\" title=\"Domitian\">Dom<PERSON><PERSON></a>, Roman emperor (d. 96)", "links": [{"title": "Domitian", "link": "https://wikipedia.org/wiki/Domitian"}]}, {"year": "1378", "text": "<PERSON>, Duke of Rothesay heir to the throne of Scotland (d. 1402)", "html": "1378 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Rothesay\" title=\"<PERSON>, Duke of Rothesay\"><PERSON>, Duke of Rothesay</a> heir to the throne of Scotland (d. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Rothesay\" title=\"<PERSON>, Duke of Rothesay\"><PERSON>, Duke of Rothesay</a> heir to the throne of Scotland (d. 1402)", "links": [{"title": "<PERSON>, Duke of Rothesay", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Rothesay"}]}, {"year": "1503", "text": "<PERSON> of Portugal (d. 1539)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1539)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Isabella_of_Portugal"}]}, {"year": "1561", "text": "<PERSON>, English conspirator (Babington Plot) (d. 1586)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Plot\" title=\"Babington Plot\">Babington Plot</a>) (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conspirator (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Plot\" title=\"Babington Plot\">Babington Plot</a>) (d. 1586)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Babington Plot", "link": "https://wikipedia.org/wiki/Babington_Plot"}]}, {"year": "1632", "text": "<PERSON><PERSON>, Dutch biologist and microbiologist (d. 1723)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch biologist and microbiologist (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch biologist and microbiologist (d. 1723)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, Italian philosopher (d. 1712)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, Dutch entomologist (d. 1704)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch entomologist (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch entomologist (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1675", "text": "<PERSON>, 1st Viscount <PERSON>, English field marshal and politician, Lord Lieutenant of Buckinghamshire (d. 1749)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (d. 1749)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Lord Lieutenant of Buckinghamshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire"}]}, {"year": "1713", "text": "<PERSON>, French soprano and actress (d. 1794)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano and actress (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano and actress (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German author and translator (d. 1839)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and translator (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and translator (d. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, British philanthropist, sheriff and banker (d. 1885)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British philanthropist, sheriff and banker (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British philanthropist, sheriff and banker (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, American author and poet (d. 1879)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON>, Piedmontese-Italian statesman, novelist and painter (d. 1866)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Massimo_d%27Azeglio\" title=\"<PERSON><PERSON>Azegli<PERSON>\"><PERSON><PERSON></a>, Piedmontese-Italian statesman, novelist and painter (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Massimo_d%27Azeglio\" title=\"<PERSON><PERSON>'Azegli<PERSON>\"><PERSON><PERSON></a>, Piedmontese-Italian statesman, novelist and painter (d. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_d%27Azeglio"}]}, {"year": "1804", "text": "<PERSON>, German physicist and academic (d. 1891)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, German composer and conductor (d. 1885)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Finnish explorer, orientalist, and professor (d. 1852)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish explorer, <a href=\"https://wikipedia.org/wiki/Orientalism\" title=\"Orientalism\">orientalist</a>, and professor (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish explorer, <a href=\"https://wikipedia.org/wiki/Orientalism\" title=\"Orientalism\">orientalist</a>, and professor (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_August_Wallin"}, {"title": "Orientalism", "link": "https://wikipedia.org/wiki/Orientalism"}]}, {"year": "1830", "text": "<PERSON>, English biologist and painter (d. 1890)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Marianne_<PERSON>\" title=\"Marianne <PERSON>\"><PERSON></a>, English biologist and painter (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marianne_North\" title=\"Marianne North\"><PERSON></a>, English biologist and painter (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marianne_North"}]}, {"year": "1838", "text": "<PERSON>, American stuntwoman and educator (d. 1921)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stuntwoman and educator (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stuntwoman and educator (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Dutch chemist and academic (d. 1907)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch chemist and academic (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch chemist and academic (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American lawyer and politician, 27th Vice President of the United States (d. 1912)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1857", "text": "<PERSON>, American baseball player (d. 1894)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williamson"}]}, {"year": "1868", "text": "<PERSON>, Belgian-French explorer and author (d. 1969)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, Belgian-French explorer and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, Belgian-French explorer and author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-N%C3%A9el"}]}, {"year": "1872", "text": "<PERSON>, Irish long jumper (d. 1957)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish long jumper (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish long jumper (d. 1957)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)"}]}, {"year": "1873", "text": "<PERSON><PERSON> <PERSON><PERSON>, British mathematician and physicist (d. 1956)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Whittaker\"><PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and physicist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hit<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Whittaker\"><PERSON><PERSON> <PERSON><PERSON></a>, British mathematician and physicist (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Russian painter and set designer (d. 1958)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and set designer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and set designer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Burmese monk and activist (d. 1931)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Saya San\"><PERSON><PERSON></a>, Burmese monk and activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Saya San\"><PERSON><PERSON></a>, Burmese monk and activist (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Say<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON> <PERSON><PERSON>, American bandleader and producer (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American bandleader and producer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American bandleader and producer (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, English actress (d. 1976)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Swedish actor (d. 1944)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish actor (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m"}]}, {"year": "1885", "text": "<PERSON>, Irish engineer and poet (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer and poet (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer and poet (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON> Eugen<PERSON> of Battenberg (d. 1969)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Victoria_E<PERSON><PERSON>_of_Battenberg\" title=\"Victoria Eugenie of Battenberg\"><PERSON> E<PERSON> of Battenberg</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_E<PERSON><PERSON>_of_Battenberg\" title=\"Victoria Eugenie of Battenberg\"><PERSON> Eugen<PERSON> of Battenberg</a> (d. 1969)", "links": [{"title": "Victoria Eugenie of Battenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Battenberg"}]}, {"year": "1887", "text": "<PERSON><PERSON>, French cyclist and pilot (d. 1917)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Octave_Lapize\" title=\"Octave Lapize\">Octave <PERSON></a>, French cyclist and pilot (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_Lapize\" title=\"Octave Lapize\">Octave <PERSON></a>, French cyclist and pilot (d. 1917)", "links": [{"title": "Octave Lapize", "link": "https://wikipedia.org/wiki/Octave_Lapize"}]}, {"year": "1891", "text": "<PERSON>, Dominican soldier and politician, 36th President of the Dominican Republic (d. 1961)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican soldier and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Dominican Republic", "link": "https://wikipedia.org/wiki/President_of_the_Dominican_Republic"}]}, {"year": "1891", "text": "<PERSON>, American journalist, author, and educator (d. 1985)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and educator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and educator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian author, poet, and playwright (d. 1987)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>adhya<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English actor and singer (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and singer (d. 1981)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1896", "text": "<PERSON>, American make-up artist and businesswoman (d. 1994)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American make-up artist and businesswoman (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American make-up artist and businesswoman (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Chinese general, 1st Minister of National Defense of the People's Republic of China (d. 1974)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Peng_De<PERSON>i\" title=\"Peng <PERSON>\"><PERSON><PERSON></a>, Chinese general, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defense_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Minister of National Defense of the People's Republic of China\">Minister of National Defense of the People's Republic of China</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peng_De<PERSON>\" title=\"Peng <PERSON>\"><PERSON><PERSON></a>, Chinese general, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defense_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Minister of National Defense of the People's Republic of China\">Minister of National Defense of the People's Republic of China</a> (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peng_Dehuai"}, {"title": "Minister of National Defense of the People's Republic of China", "link": "https://wikipedia.org/wiki/Minister_of_National_Defense_of_the_People%27s_Republic_of_China"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Japanese photographer (d. 1988)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Teik%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Teik%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Teik%C5%8D_<PERSON><PERSON>ni"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Polish-American actress, singer, and dancer (d. 1959)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Gil<PERSON>\"><PERSON><PERSON></a>, Polish-American actress, singer, and dancer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American actress, singer, and dancer (d. 1959)", "links": [{"title": "<PERSON><PERSON> Gray", "link": "https://wikipedia.org/wiki/Gilda_Gray"}]}, {"year": "1903", "text": "<PERSON>, American FBI agent (d. 1960)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agent (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agent (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}]}, {"year": "1904", "text": "<PERSON>, American director and playwright (d. 1961)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hart\" title=\"Moss Hart\"><PERSON></a>, American director and playwright (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moss_Hart\" title=\"Moss Hart\"><PERSON></a>, American director and playwright (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hart"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi activist (d. 1932)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/A.K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi activist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A.K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi activist (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>lani"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Slovenian historian and academic (d. 1988)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian historian and academic (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian historian and academic (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Russian mathematician and cryptographer (d. 1968)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and cryptographer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and cryptographer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Montserratian nurse and social worker (d. 1986)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Montserratian nurse and social worker (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Montserratian nurse and social worker (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Canadian geologist and geophysicist (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and geophysicist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and geophysicist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American runner (d. 1966)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American admiral (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American singer (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, German SS officer and journalist (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Alquen\" title=\"<PERSON><PERSON>'Alque<PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and journalist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_d%27Alquen\" title=\"<PERSON><PERSON> d'Alquen\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and journalist (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gunter_d%27Alquen"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and politician (d. 1984)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American general (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Polish-Israeli lawyer and judge (d. 1982)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli lawyer and judge (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli lawyer and judge (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Canadian cardinal (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9go<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9go<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_Gr%C3%A9goire"}]}, {"year": "1911", "text": "<PERSON>, American singer and harmonica player (d. 1986)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian footballer (d. 1992)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>l<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>l<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>l<PERSON><PERSON>_Bindea"}]}, {"year": "1912", "text": "<PERSON>, German conductor (music) (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (music) (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (music) (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American television director (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Murray_Golden\" title=\"Murray Golden\"><PERSON></a>, American television director (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murray_Golden\" title=\"Murray Golden\"><PERSON></a>, American television director (d. 1991)", "links": [{"title": "<PERSON> Golden", "link": "https://wikipedia.org/wiki/Murray_Golden"}]}, {"year": "1913", "text": "<PERSON>, Italian actor and singer (d. 1984)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American colonel (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Czechoslovakian canoeist (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek\" title=\"František Čapek\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian canoeist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek\" title=\"František Čapek\"><PERSON><PERSON><PERSON><PERSON></a>, Czechoslovakian canoeist (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_%C4%8Capek"}]}, {"year": "1914", "text": "<PERSON>, Indian Independence movement revolutionary and Officer of Indian National Army (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Independence movement revolutionary and Officer of <a href=\"https://wikipedia.org/wiki/Indian_National_Army\" title=\"Indian National Army\">Indian National Army</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian Independence movement revolutionary and Officer of <a href=\"https://wikipedia.org/wiki/Indian_National_Army\" title=\"Indian National Army\">Indian National Army</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Indian National Army", "link": "https://wikipedia.org/wiki/Indian_National_Army"}]}, {"year": "1915", "text": "<PERSON>, American author and illustrator (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, English journalist and author (d. 1988)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Scottish soprano and actress (d. 2011)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soprano and actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soprano and actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American activist (d. 2003)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, English author (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American engineer and pilot (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, British singer (d. 1952)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, British singer (d. 1952)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, French mathematician and academic (d. 1996)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and academic (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English footballer and manager (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian illustrator (d. 2015)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian illustrator (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American educator and politician, Mayor of Tucson (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Arizona_politician)\" title=\"<PERSON> (Arizona politician)\"><PERSON></a>, American educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Tucson,_Arizona\" title=\"List of mayors of Tucson, Arizona\">Mayor of Tucson</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Arizona_politician)\" title=\"<PERSON> (Arizona politician)\"><PERSON></a>, American educator and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Tucson,_Arizona\" title=\"List of mayors of Tucson, Arizona\">Mayor of Tucson</a> (d. 2014)", "links": [{"title": "<PERSON> (Arizona politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Arizona_politician)"}, {"title": "List of mayors of Tucson, Arizona", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Tucson,_Arizona"}]}, {"year": "1923", "text": "<PERSON>, English lieutenant and journalist (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Day\" title=\"Robin Day\"><PERSON></a>, English lieutenant and journalist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Day\" title=\"Robin Day\"><PERSON></a>, English lieutenant and journalist (d. 2000)", "links": [{"title": "<PERSON> Day", "link": "https://wikipedia.org/wiki/<PERSON>_Day"}]}, {"year": "1923", "text": "<PERSON>, British-born American poet  (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American poet (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American poet (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, South African cardiologist and physician (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cardiologist and physician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cardiologist and physician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress and singer (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress and singer (d. 1996)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Turkish historian and academic (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>at Sezgin\"><PERSON><PERSON></a>, Turkish historian and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>at_<PERSON>\" title=\"Fuat Sezgin\"><PERSON><PERSON></a>, Turkish historian and academic (d. 2018)", "links": [{"title": "Fuat Sezgin", "link": "https://wikipedia.org/wiki/<PERSON>at_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian composer and educator (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American author and illustrator (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ldstein"}]}, {"year": "1925", "text": "<PERSON>, American-French singer-songwriter and pianist (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and pianist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and pianist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Australian cricketer (d. 1982)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Vietnamese-Cambodian politician co-founded the Khmer Rouge (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese-Cambodian politician co-founded the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese-Cambodian politician co-founded the <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}]}, {"year": "1925", "text": "<PERSON>, English journalist and radio host (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and radio host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and radio host (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Spanish author and screenwriter (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Y._A._Tittle\" title=\"Y. A. Tittle\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y._A._Tittle\" title=\"Y. A. Tittle\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player (d. 2017)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y._<PERSON><PERSON>_Tittle"}]}, {"year": "1927", "text": "<PERSON>, French singer-songwriter, pianist, and actor (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9caud\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, pianist, and actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9caud\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, pianist, and actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_B%C3%A9caud"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, French actor and singer (d. 1992)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and singer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American author and poet (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and poet (d. 2013)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1928", "text": "<PERSON>, American baseball player (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2002)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1929", "text": "<PERSON>, Canadian activist, author, and director (d. 1977)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist, author, and director (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian activist, author, and director (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American composer and educator (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, 27th Baroness <PERSON>, English wife of <PERSON> (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_27th_Baroness_<PERSON>\" title=\"<PERSON>, 27th Baroness <PERSON>\"><PERSON>, 27th Baroness <PERSON></a>, English wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>Home\"><PERSON></a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_27th_Baroness_<PERSON>\" title=\"<PERSON>, 27th Baroness <PERSON>\"><PERSON>, 27th Baroness <PERSON></a>, English wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (d. 2012)", "links": [{"title": "<PERSON>-Home, 27th Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_27th_Baroness_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Home"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Bulgarian author and playwright (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian author and playwright (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian author and playwright (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Armenian actor (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian actor (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American voice actor (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Angel\"><PERSON></a>, American voice actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_Angel\" title=\"Jack Angel\"><PERSON></a>, American voice actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON> <PERSON>, American singer-songwriter and guitarist (d. 1959)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">The Big Bopper</a>, American singer-songwriter and guitarist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Big_Bopper\" title=\"The Big Bopper\">The Big Bopper</a>, American singer-songwriter and guitarist (d. 1959)", "links": [{"title": "The Big Bopper", "link": "https://wikipedia.org/wiki/The_<PERSON>_Bopper"}]}, {"year": "1930", "text": "<PERSON>, English poet, author, and playwright (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Norwegian sociologist and mathematician (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian sociologist and mathematician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian sociologist and mathematician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English-born Scottish race car driver and 6th Baronet <PERSON> (d. 1969)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Scottish race car driver and 6th Baronet <PERSON> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Scottish race car driver and 6th Baronet <PERSON> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON> Pahang (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Pahang\" title=\"<PERSON> of Pahang\"><PERSON> of Pahang</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Pahang\" title=\"<PERSON> of Pahang\"><PERSON> of Pahang</a> (d. 2019)", "links": [{"title": "<PERSON> of Pahang", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Pahang"}]}, {"year": "1931", "text": "<PERSON>, Russian-German pianist and composer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Sofia_Gubaidulina\" title=\"Sofia Gubaidulina\"><PERSON></a>, Russian-German pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofia_Gubaidulina\" title=\"Sofia Gubaidulina\"><PERSON></a>, Russian-German pianist and composer", "links": [{"title": "Sofia Gubaidulina", "link": "https://wikipedia.org/wiki/Sofia_Gubaidulina"}]}, {"year": "1931", "text": "<PERSON>, Japanese actor (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American author and educator (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, French physicist and academic, Nobel Prize laureate (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1932", "text": "<PERSON>, English journalist, author, poet, and playwright (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, poet, and playwright (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, poet, and playwright (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian economist and academic, Nobel Prize laureate (d. 2021)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1933", "text": "<PERSON>, English gangster (d. 2000)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_twins\" title=\"Kray twins\"><PERSON></a>, English gangster (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_twins\" title=\"Kray twins\"><PERSON></a>, English gangster (d. 2000)", "links": [{"title": "<PERSON><PERSON> twins", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_twins"}]}, {"year": "1933", "text": "<PERSON>, English gangster (d. 1995)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_twins\" title=\"Kray twins\"><PERSON></a>, English gangster (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_twins\" title=\"Kray twins\"><PERSON></a>, English gangster (d. 1995)", "links": [{"title": "<PERSON><PERSON> twins", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_twins"}]}, {"year": "1933", "text": "<PERSON>, American author and educator", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Rush\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_Rush\" title=\"Norman Rush\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American physicist and author", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Australian golfer (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Margie_Masters\" title=\"Margie Masters\">Margie Masters</a>, Australian golfer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margie_Masters\" title=\"Margie Masters\">Margie Masters</a>, Australian golfer (d. 2022)", "links": [{"title": "<PERSON>gie Masters", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON> <PERSON>, American singer-songwriter (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American pianist, musicologist, and educator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, musicologist, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, musicologist, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Italian mobster (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mobster (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian mobster (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>one"}]}, {"year": "1935", "text": "<PERSON>, Indian-English journalist and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Estonian painter (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON>ri_<PERSON>k"}]}, {"year": "1936", "text": "<PERSON>, American singer and guitarist (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, and producer (d. 2011)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer (d. 2011)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1936", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yman"}]}, {"year": "1937", "text": "<PERSON>, Spanish composer and educator (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Coria\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Coria\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and educator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Coria"}]}, {"year": "1937", "text": "<PERSON>, American guitarist and songwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Santo_Farina\" class=\"mw-redirect\" title=\"Santo Farina\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santo_Farina\" class=\"mw-redirect\" title=\"Santo Farina\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "Santo Farina", "link": "https://wikipedia.org/wiki/Santo_Farina"}]}, {"year": "1937", "text": "<PERSON>, American baseball player (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, German chemist and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German chemist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ns"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Italian-American architect and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Italian-American architect and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Croatian general (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Petar_Stipeti%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian general (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petar_Stipeti%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian general (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petar_Stipeti%C4%87"}]}, {"year": "1938", "text": "<PERSON>, American economist and academic (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, New Zealand director and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Polish canoe racer (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Rafa%C5%82_<PERSON>sz<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish canoe racer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rafa%C5%82_<PERSON>sz<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish canoe racer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rafa%C5%82_<PERSON><PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON> of Turville, English businessman and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Turville\" title=\"<PERSON>, Baron <PERSON> of Turville\"><PERSON>, Baron <PERSON> of Turville</a>, English businessman and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Turville\" title=\"<PERSON>, Baron <PERSON> of Turville\"><PERSON>, Baron <PERSON> of Turville</a>, English businessman and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Turville", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Turville"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Israeli politician (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli politician (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American medical researcher (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical researcher (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical researcher (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Japanese archbishop (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese archbishop (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese archbishop (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Asian American activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Woo\" title=\"<PERSON><PERSON> Woo\"><PERSON><PERSON></a>, Asian American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Woo\" title=\"<PERSON><PERSON> Woo\"><PERSON><PERSON></a>, Asian American activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON>_Woo"}]}, {"year": "1942", "text": "<PERSON>, English physician and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actress (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Irish journalist and author (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Puerto Rican politician, 132nd Mayor of Ponce (d. 2004)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Rafael_Cordero_Santiago\" title=\"Rafael Cordero Santiago\"><PERSON></a>, Puerto Rican politician, 132nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ponce,_Puerto_Rico\" title=\"List of mayors of Ponce, Puerto Rico\">Mayor of Ponce</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rafael Cordero Santiago\"><PERSON></a>, Puerto Rican politician, 132nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Ponce,_Puerto_Rico\" title=\"List of mayors of Ponce, Puerto Rico\">Mayor of Ponce</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Cordero_Santiago"}, {"title": "List of mayors of Ponce, Puerto Rico", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Ponce,_Puerto_Rico"}]}, {"year": "1942", "text": "<PERSON>, Colombian biologist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian biologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian biologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Scottish-American wrestler and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian rugby player and coach (d. 1994)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Ukrainian footballer and manager (d. 2007)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian footballer and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>,  American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Canadian educator and union leader", "html": "1945 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and union leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and union leader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian drummer (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jerry <PERSON>\"><PERSON></a>, Canadian drummer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Welsh rugby player (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>me"}]}, {"year": "1949", "text": "<PERSON>, American journalist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Trinidadian volcanologist and politician, 7th Prime Minister of Trinidad and Tobago", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian volcanologist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian volcanologist and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Filipino lawyer and politician (d. 2012)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Jamaican singer-songwriter, keyboard player, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Black\" title=\"<PERSON><PERSON> Black\"><PERSON><PERSON></a>, Jamaican singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ve_Black\" title=\"<PERSON><PERSON> Black\"><PERSON><PERSON></a>, Jamaican singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ve_Black"}]}, {"year": "1950", "text": "<PERSON>, Argentinian lawyer and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Czech politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Miroslav_Sl%C3%A1dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miroslav_Sl%C3%A1dek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Sl%C3%A1dek"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Italian poet and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Si<PERSON>\"><PERSON><PERSON></a>, Italian poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sica\"><PERSON><PERSON></a>, Italian poet and author", "links": [{"title": "Gabriella <PERSON>", "link": "https://wikipedia.org/wiki/Gabriella_Sica"}]}, {"year": "1950", "text": "<PERSON>-<PERSON>, Austrian biologist, anthropologist, and ethnologist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist, anthropologist, and ethnologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist, anthropologist, and ethnologist", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American composer and conductor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian educator and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Italian priest", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dominican baseball player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1953", "text": "<PERSON>, German footballer and manager (d. 2024)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian lawyer and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Slovak singer-songwriter and bass player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEo_R%C3%A1%C5%BE\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%BEo_R%C3%A1%C5%BE\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEo_R%C3%A1%C5%BE"}]}, {"year": "1954", "text": "<PERSON>, American businessman and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>s\" title=\"Mike Rounds\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mike_Rounds\" title=\"Mike Rounds\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>s", "link": "https://wikipedia.org/wiki/Mike_Rounds"}]}, {"year": "1954", "text": "<PERSON>, American accountant, lawyer, and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accountant, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accountant, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian journalist and politician, 29th Prime Minister of Australia", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1955", "text": "<PERSON>, American soprano and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American businessman and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German-American baseball player and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Ron_<PERSON>\" title=\"Ron <PERSON>hire\"><PERSON></a>, German-American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ron_<PERSON>\" title=\"Ron Gardenhire\"><PERSON></a>, German-American baseball player and manager", "links": [{"title": "Ron Garden<PERSON>", "link": "https://wikipedia.org/wiki/Ron_Gardenhire"}]}, {"year": "1957", "text": "<PERSON>, American actor and voice actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Norwegian banker and politician, 65th Mayor of Bergen", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian banker and politician, 65th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Bergen\" title=\"List of mayors of Bergen\">Mayor of Bergen</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian banker and politician, 65th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Bergen\" title=\"List of mayors of Bergen\">Mayor of Bergen</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ke"}, {"title": "List of mayors of Bergen", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Bergen"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Japanese director and composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American lawyer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian guitarist and songwriter (d. 2009)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist and songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist and songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Danish educator and politician, Danish Minister of Social Affairs", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish educator and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Social_Affairs_(Denmark)\" title=\"Ministry of Social Affairs (Denmark)\">Danish Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish educator and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Social_Affairs_(Denmark)\" title=\"Ministry of Social Affairs (Denmark)\">Danish Minister of Social Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Social Affairs (Denmark)", "link": "https://wikipedia.org/wiki/Ministry_of_Social_Affairs_(Denmark)"}]}, {"year": "1960", "text": "<PERSON>-<PERSON>, Australian golfer and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer and sportscaster", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Colombian journalist, lawyer, and activist (d. 1999)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist, lawyer, and activist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian journalist, lawyer, and activist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jaime_G<PERSON>z%C3%B3n"}]}, {"year": "1960", "text": "<PERSON>, German race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "B<PERSON> Wong", "link": "https://wikipedia.org/wiki/B<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American gymnast and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1962", "text": "<PERSON>, Welsh rugby player and television host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1962)\" title=\"<PERSON> (rugby, born 1962)\"><PERSON></a>, Welsh rugby player and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1962)\" title=\"<PERSON> (rugby, born 1962)\"><PERSON></a>, Welsh rugby player and television host", "links": [{"title": "<PERSON> (rugby, born 1962)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1962)"}]}, {"year": "1962", "text": "<PERSON>, English bass player and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Zambian footballer (d. 2000)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zambian footballer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zambian footballer (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1963", "text": "<PERSON>, Scottish footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Spanish singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian businessman and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Russian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>-<PERSON>, American lawyer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, German-Greek journalist and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>yria<PERSON>_Velopoulos\" title=\"<PERSON>yria<PERSON> Velopoulos\"><PERSON><PERSON><PERSON><PERSON></a>, German-Greek journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yria<PERSON>_Velopoulos\" title=\"Kyria<PERSON> Velopoulos\"><PERSON><PERSON><PERSON><PERSON></a>, German-Greek journalist and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kyriakos_Velopoulos"}]}, {"year": "1966", "text": "<PERSON>, Russian businessman and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English academic and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Trinidadian cricketer and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1967)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1967)\"><PERSON></a>, Trinidadian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1967)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1967)\"><PERSON></a>, Trinidadian cricketer and sportscaster", "links": [{"title": "<PERSON> (cricketer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1967)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Samoan-New Zealand rugby player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Samoan-New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Samoan-New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English television host and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish tennis player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Francisco_Clavet\" title=\"Francisco Clavet\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Clavet\" title=\"Francisco Clavet\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Clavet"}]}, {"year": "1968", "text": "<PERSON>, American voice actor and illustrator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(story_artist)\" title=\"<PERSON> (story artist)\"><PERSON></a>, American voice actor and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(story_artist)\" title=\"<PERSON> (story artist)\"><PERSON></a>, American voice actor and illustrator", "links": [{"title": "<PERSON> (story artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(story_artist)"}]}, {"year": "1968", "text": "<PERSON>, American journalist and critic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Irish-Canadian author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English field hockey player and engineer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1971", "text": "<PERSON>, Argentina international rugby union player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentina international rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentina international rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, American academic", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r_<PERSON>ut\" title=\"Z<PERSON><PERSON><PERSON>ut\"><PERSON><PERSON><PERSON><PERSON></a>, American academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r_<PERSON>ut\" title=\"Zephyr <PERSON>ut\"><PERSON><PERSON><PERSON><PERSON></a>, American academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zephyr_Teachout"}]}, {"year": "1971", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Jamaican track and field athlete", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican track and field athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican track and field athlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American model and actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ret"}]}, {"year": "1972", "text": "<PERSON>, American football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1972", "text": "<PERSON>, English lawyer and politician, Attorney General for England and Wales", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Estonian author and academic", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American filmmaker", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, New Zealand rugby player, cricketer, and radio host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, New Zealand rugby player, cricketer, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, New Zealand rugby player, cricketer, and radio host", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/G%C3%A1bor_Babos\" title=\"Gábor Babos\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A1bor_Babos\" title=\"Gábor Babos\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "Gábor <PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A1bor_Babos"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American football coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Dominican baseball player and scout", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON>\"><PERSON><PERSON></a>, Dominican baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Guerrero\"><PERSON><PERSON></a>, Dominican baseball player and scout", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Colombian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81ngel\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81ngel\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81ngel"}]}, {"year": "1975", "text": "<PERSON>, Liberian footballer (d. 2013)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Bulgarian swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Ecuadoran footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Kaviedes\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadoran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Ka<PERSON>es\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadoran footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Kaviedes"}]}, {"year": "1978", "text": "<PERSON>, Trinidadian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian drummer and songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>as_Petravi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marijonas_Petravi%C4%8Dius"}]}, {"year": "1980", "text": "<PERSON>, Ghanaian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Australian jockey", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian jockey", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1980", "text": "<PERSON>, Spanish basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ana\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ana\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Monta%C3%B1ana"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American fashion designer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1980", "text": "<PERSON>, American actress and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Bueno\" title=\"Se<PERSON><PERSON>án Buen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Bueno\" title=\"Se<PERSON><PERSON><PERSON> Bueno\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Bueno"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian guitarist and composer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian guitarist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Singaporean-American model, actress, and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tila_Tequila\" title=\"Tila Tequila\"><PERSON><PERSON></a>, Singaporean-American model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"Tila Tequila\"><PERSON><PERSON></a>, Singaporean-American model, actress, and singer", "links": [{"title": "Tila Tequila", "link": "https://wikipedia.org/wiki/Tila_Tequila"}]}, {"year": "1981", "text": "<PERSON>, Filipino actor and politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Malaysian race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fairuz_Fauzy\" title=\"Fairuz Fauzy\"><PERSON><PERSON></a>, Malaysian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fairuz_Fauzy\" title=\"Fairuz Fauzy\"><PERSON><PERSON></a>, Malaysian race car driver", "links": [{"title": "<PERSON>uz <PERSON>au<PERSON>", "link": "https://wikipedia.org/wiki/Fairuz_Fauzy"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, dancer, and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Garin\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Garin\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_<PERSON>arin"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1983", "text": "<PERSON>, American race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Filipino singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Basabas\"><PERSON><PERSON></a>, Filipino singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Basabas\"><PERSON><PERSON></a>, Filipino singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swedish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English-Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1985", "text": "<PERSON>, Australian actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian snowboarder (d. 2014)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)\" title=\"<PERSON> (snowboarder)\"><PERSON></a>, Australian snowboarder (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)\" title=\"<PERSON> (snowboarder)\"><PERSON></a>, Australian snowboarder (d. 2014)", "links": [{"title": "<PERSON> (snowboarder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(snowboarder)"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Swedish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian rapper and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian rapper and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1986", "text": "<PERSON>-<PERSON>, English actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_football)"}]}, {"year": "1987", "text": "<PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German race walker", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Bahamian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mitchell"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eda\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eda\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1eda"}]}, {"year": "1989", "text": "<PERSON>, Brazilian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>cei%C3%A7%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Concei%C3%A7%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anderson_Concei%C3%A7%C3%A3o"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Canadian actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish YouTuber", "html": "1989 - <a href=\"https://wikipedia.org/wiki/PewDiePie\" title=\"PewDiePie\">PewDie<PERSON><PERSON></a>, Swedish YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PewDiePie\" title=\"PewDiePie\">PewDieP<PERSON></a>, Swedish YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/PewDiePie"}]}, {"year": "1989", "text": "<PERSON>, Australian actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American middle-distance runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American middle-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American middle-distance runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/%C4%B0lkay_G%C3%BCndo%C4%9Fan\" title=\"<PERSON><PERSON><PERSON> Gündoğan\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0lkay_G%C3%BCndo%C4%9Fan\" title=\"<PERSON><PERSON><PERSON> Gündoğan\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0lkay_G%C3%BCndo%C4%9Fan"}]}, {"year": "1990", "text": "<PERSON>, Saudi Arabia international footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabia international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabia international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Italian motorcycle racer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Montenegrin basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Nikola_Vu%C4%8Devi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikola_Vu%C4%8Devi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Vu%C4%8Devi%C4%87"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Andersen Aase\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Torstein Andersen Aase\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON> A<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Montenegrin basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bojan_Dubljevi%C4%87"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Filipino actor, singer, and dancer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Marrion_Go<PERSON>z\" title=\"Mar<PERSON> Gopez\"><PERSON><PERSON></a>, Filipino actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marrion_Gopez\" title=\"Marrion Gopez\"><PERSON><PERSON></a>, Filipino actor, singer, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marrion_Go<PERSON>z"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Chinese chess grandmaster", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Ding_<PERSON>\" title=\"Ding Liren\"><PERSON><PERSON></a>, Chinese chess grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ding_<PERSON>\" title=\"Ding Liren\"><PERSON><PERSON></a>, Chinese chess grandmaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ding_Liren"}]}, {"year": "1993", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American-South Korean singer, dancer, and actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-South Korean singer, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-South Korean singer, dancer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Czech tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Te<PERSON><PERSON>_<PERSON>cov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Te<PERSON><PERSON>_<PERSON>v%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tereza_Martincov%C3%A1"}]}, {"year": "1994", "text": "<PERSON>, American mixed martial artist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27M<PERSON><PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27M<PERSON><PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON> (fighter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley_(fighter)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Dominican baseball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Oc%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oc%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oc%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American gymnast", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American wrestler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Breakker\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Breakker\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ker"}]}, {"year": "1997", "text": "<PERSON>, English gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American singer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Amon-Ra_St._Brown\" title=\"Amon-Ra St. Brown\">Amon-Ra St. Brown</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>._Brown\" title=\"Amon-Ra St. Brown\">Am<PERSON>-<PERSON></a>, American football player", "links": [{"title": "Amon-Ra St. Brown", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-Ra_St._Brown"}]}], "Deaths": [{"year": "935", "text": "<PERSON>, Chinese official and chancellor", "html": "935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Tang)\" title=\"<PERSON> (Later Tang)\"><PERSON></a>, Chinese official and chancellor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Tang)\" title=\"<PERSON> (Later Tang)\"><PERSON></a>, Chinese official and chancellor", "links": [{"title": "<PERSON> (Later Tang)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Tang)"}]}, {"year": "996", "text": "<PERSON>, French king", "html": "996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French king", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1152", "text": "<PERSON><PERSON><PERSON> of Soissons, French theologian, philosopher and composer", "html": "1152 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Soissons\" title=\"<PERSON><PERSON><PERSON> of Soissons\"><PERSON><PERSON><PERSON> of Soissons</a>, French theologian, philosopher and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Soissons\" title=\"<PERSON><PERSON><PERSON> of Soissons\"><PERSON><PERSON><PERSON> of Soissons</a>, French theologian, philosopher and composer", "links": [{"title": "<PERSON><PERSON><PERSON> of Soissons", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Soissons"}]}, {"year": "1168", "text": "<PERSON>, Count of Nevers, French nobleman", "html": "1168 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nevers\" title=\"<PERSON>, Count of Nevers\"><PERSON>, Count of Nevers</a>, French nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nevers\" title=\"<PERSON>, Count of Nevers\"><PERSON>, Count of Nevers</a>, French nobleman", "links": [{"title": "<PERSON>, Count of Nevers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1260", "text": "<PERSON><PERSON><PERSON>, Egyptian sultan", "html": "1260 - <a href=\"https://wikipedia.org/wiki/Qutuz\" title=\"Qutuz\"><PERSON><PERSON><PERSON></a>, Egyptian sultan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qutuz\" title=\"Qutuz\"><PERSON><PERSON><PERSON></a>, Egyptian sultan", "links": [{"title": "Qutuz", "link": "https://wikipedia.org/wiki/Qutuz"}]}, {"year": "1375", "text": "<PERSON><PERSON><PERSON>, Danish king (b. 1320)", "html": "1375 - <a href=\"https://wikipedia.org/wiki/Valdemar_IV_of_Denmark\" title=\"Valdemar IV of Denmark\"><PERSON><PERSON><PERSON> IV</a>, Danish king (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valdemar_IV_of_Denmark\" title=\"Valdemar IV of Denmark\"><PERSON><PERSON><PERSON> IV</a>, Danish king (b. 1320)", "links": [{"title": "Valdemar IV of Denmark", "link": "https://wikipedia.org/wiki/Valdemar_IV_of_Denmark"}]}, {"year": "1537", "text": "<PERSON>, English queen and wife of <PERSON> of England (b. c. 1508)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English queen and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1508</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English queen and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1508</span>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1572", "text": "<PERSON>, 3rd Earl of Derby, English admiral and politician, Lord Lieutenant of Lancashire (b. 1508)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Derby\" title=\"<PERSON>, 3rd Earl of Derby\"><PERSON>, 3rd Earl of Derby</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Derby\" title=\"<PERSON>, 3rd Earl of Derby\"><PERSON>, 3rd Earl of Derby</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire\" title=\"Lord Lieutenant of Lancashire\">Lord Lieutenant of Lancashire</a> (b. 1508)", "links": [{"title": "<PERSON>, 3rd Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Derby"}, {"title": "Lord Lieutenant of Lancashire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Lancashire"}]}, {"year": "1601", "text": "<PERSON><PERSON>, Danish astronomer and alchemist (b. 1546)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/Ty<PERSON>_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a>, Danish astronomer and alchemist (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tycho_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON></a>, Danish astronomer and alchemist (b. 1546)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>_<PERSON>e"}]}, {"year": "1633", "text": "<PERSON>, French organist and composer (b. 1562/3)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1562/3)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1562/3)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1642", "text": "<PERSON>, 1st Earl of Lindsey, English peer and courtier (b. 1582)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Lindsey\" title=\"<PERSON>, 1st Earl of Lindsey\"><PERSON>, 1st Earl of Lindsey</a>, English peer and courtier (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Lindsey\" title=\"<PERSON>, 1st Earl of Lindsey\"><PERSON>, 1st Earl of Lindsey</a>, English peer and courtier (b. 1582)", "links": [{"title": "<PERSON>, 1st Earl of Lindsey", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Lindsey"}]}, {"year": "1655", "text": "<PERSON>, French priest, astronomer, and mathematician (b. 1592)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, astronomer, and mathematician (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, astronomer, and mathematician (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1669", "text": "<PERSON>, English lawyer and author (b. 1600)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and author (b. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English architect and scholar (b. 1611)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and scholar (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and scholar (b. 1611)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}]}, {"year": "1725", "text": "<PERSON>, Italian composer and educator (b. 1660)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Austrian violinist and composer (b. 1739)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American lawyer and politician, 10th President of the Continental Congress (b. 1740)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1824", "text": "<PERSON>, American patriot post rider during American Revolutionary War (b. 1752)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Israel_Bissell\" title=\"Israel Bissell\">Israel B<PERSON>ll</a>, American patriot <a href=\"https://wikipedia.org/wiki/Post_rider\" class=\"mw-redirect\" title=\"Post rider\">post rider</a> during <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Bissell\" title=\"Israel Bissell\">Israel Bissell</a>, American patriot <a href=\"https://wikipedia.org/wiki/Post_rider\" class=\"mw-redirect\" title=\"Post rider\">post rider</a> during <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> (b. 1752)", "links": [{"title": "Israel Bissell", "link": "https://wikipedia.org/wiki/Israel_<PERSON><PERSON>ll"}, {"title": "Post rider", "link": "https://wikipedia.org/wiki/Post_rider"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1852", "text": "<PERSON>, American lawyer and politician, 14th United States Secretary of State (b. 1782)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-Australian author and poet (b. 1817)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Australian author and poet (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Australian author and poet (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1898", "text": "<PERSON>, French painter and illustrator (b. 1824)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, French archaeologist and photographer (b. 1828)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French archaeologist and photographer (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French archaeologist and photographer (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay"}]}, {"year": "1917", "text": "<PERSON>, American painter and academic (b. 1852)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English businessman (b. 1839)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American mob boss (b. 1902)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Dutch_Schultz\" title=\"Dutch Schultz\">Dutch <PERSON></a>, American mob boss (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dutch_Schultz\" title=\"Dutch Schultz\">Dutch <PERSON></a>, American mob boss (b. 1902)", "links": [{"title": "Dutch Schultz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Swedish actor (b. 1886)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni<PERSON>_Wahlbom"}]}, {"year": "1938", "text": "<PERSON>, German sculptor and playwright (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and playwright (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and playwright (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Canadian poet and painter (b. 1912)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>u\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and painter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>u\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and painter (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Den<PERSON>_Garneau"}]}, {"year": "1944", "text": "<PERSON>, French engineer and businessman, co-founded the Renault Company (b. 1877)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, French engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Renault\" title=\"Renault\">Renault Company</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Renault_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, French engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Renault\" title=\"Renault\">Renault Company</a> (b. 1877)", "links": [{"title": "<PERSON> (industrialist)", "link": "https://wikipedia.org/wiki/Louis_Renault_(industrialist)"}, {"title": "Renault", "link": "https://wikipedia.org/wiki/Renault"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian soldier and politician, Minister President of Norway (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Minister President of Norway</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Minister President of Norway</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>ui<PERSON>ling"}, {"title": "List of heads of government of Norway", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway"}]}, {"year": "1948", "text": "<PERSON>, Austrian-Hungarian composer (b. 1870)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Austrian-Hungarian composer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1r\" title=\"<PERSON>\"><PERSON></a>, Austrian-Hungarian composer (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Leh%C3%A1r"}]}, {"year": "1948", "text": "<PERSON>, American historian and author (b. 1877)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Ukrainian playwright and publicist (b. 1902)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian playwright and publicist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian playwright and publicist (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON> <PERSON><PERSON>, English philosopher and academic (b. 1873)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English philosopher and academic (b. 1873)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, the test pilot of rocket, participant in the launch of the first artificial Earth satellite, Lenin Prize winner, Candidate of Technical Sciences (b. 1924)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the test pilot of <a href=\"https://wikipedia.org/wiki/Rocket\" title=\"Rocket\">rocket</a>, participant in the launch of the first artificial <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> <a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">satellite</a>, <a href=\"https://wikipedia.org/wiki/Lenin_Prize\" title=\"Lenin Prize\">Lenin Prize winner</a>, <a href=\"https://wikipedia.org/wiki/Kandidat\" class=\"mw-redirect\" title=\"Kandidat\">Candidate of Technical Sciences</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the test pilot of <a href=\"https://wikipedia.org/wiki/Rocket\" title=\"Rocket\">rocket</a>, participant in the launch of the first artificial <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> <a href=\"https://wikipedia.org/wiki/Sputnik_1\" title=\"Sputnik 1\">satellite</a>, <a href=\"https://wikipedia.org/wiki/Lenin_Prize\" title=\"Lenin Prize\">Lenin Prize winner</a>, <a href=\"https://wikipedia.org/wiki/Kandidat\" class=\"mw-redirect\" title=\"Kandidat\">Candidate of Technical Sciences</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Rocket", "link": "https://wikipedia.org/wiki/Rocket"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}, {"title": "Sputnik 1", "link": "https://wikipedia.org/wiki/Sputnik_1"}, {"title": "Lenin Prize", "link": "https://wikipedia.org/wiki/Lenin_Prize"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kandidat"}]}, {"year": "1964", "text": "<PERSON>, German mountaineer (b. 1931)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German chemist (b. 1879)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and historian (b. 1896)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and historian (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and historian (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish poet and politician (b. 1908)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Kemal_%C3%87a%C4%9Flar\" title=\"Behçet Kemal Çağlar\"><PERSON><PERSON>ç<PERSON></a>, Turkish poet and politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beh%C3%A7et_Kemal_%C3%87a%C4%9Flar\" title=\"Behçet Kemal Çağlar\"><PERSON><PERSON>ç<PERSON>ğ<PERSON></a>, Turkish poet and politician (b. 1908)", "links": [{"title": "Behçet Kemal Çağlar", "link": "https://wikipedia.org/wiki/Beh%C3%A7et_Kemal_%C3%87a%C4%9Flar"}]}, {"year": "1970", "text": "<PERSON>, American historian and author (b. 1916)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American composer (b. 1876)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swiss race car driver and motorcycle racer (b. 1936)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss race car driver and motorcycle racer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss race car driver and motorcycle racer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, NFL player died during a game (b. 1943)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, NFL player died during a game (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, NFL player died during a game (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and sportscaster (b. 1919)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Ukrainian violinist (b. 1908)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian violinist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and diplomat, Turkish Ambassador to France (b. 1919)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/%C4%B0smail_Erez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Turkish_Ambassador_to_France\" class=\"mw-redirect\" title=\"Turkish Ambassador to France\">Turkish Ambassador to France</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smail_Erez\" title=\"İsmail <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Turkish_Ambassador_to_France\" class=\"mw-redirect\" title=\"Turkish Ambassador to France\">Turkish Ambassador to France</a> (b. 1919)", "links": [{"title": "İ<PERSON>il <PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0smail_Erez"}, {"title": "Turkish Ambassador to France", "link": "https://wikipedia.org/wiki/Turkish_Ambassador_to_France"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish historian, author, and academic (b. 1888)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(senior)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (senior)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish historian, author, and academic (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(senior)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (senior)\"><PERSON><PERSON><PERSON><PERSON></a>, Polish historian, author, and academic (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (senior)", "link": "https://wikipedia.org/wiki/Zdzis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_(senior)"}]}, {"year": "1979", "text": "<PERSON>, Italian automobile designer and founded of <PERSON><PERSON><PERSON> (b. 1908)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automobile designer and founded of <a href=\"https://wikipedia.org/wiki/Abarth\" title=\"Abarth\"><PERSON><PERSON><PERSON></a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian automobile designer and founded of <a href=\"https://wikipedia.org/wiki/Abarth\" title=\"Abarth\"><PERSON><PERSON><PERSON></a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>th"}]}, {"year": "1983", "text": "<PERSON>, Taiwanese composer and educator (b. 1910)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Taiwanese composer and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Taiwanese composer and educator (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American race car driver (b. 1941)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian cardinal (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer (b. 1948)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish mountaineer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American captain, screenwriter, and producer, created <PERSON> Trek (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, screenwriter, and producer, created <i><a href=\"https://wikipedia.org/wiki/Star_Trek\" title=\"Star Trek\">Star Trek</a></i> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, screenwriter, and producer, created <i><a href=\"https://wikipedia.org/wiki/Star_Trek\" title=\"Star Trek\">Star Trek</a></i> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Star Trek", "link": "https://wikipedia.org/wiki/Star_Trek"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Indian author and screenwriter (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and screenwriter (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Is<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>,  American novelist and short story writer  (b. 1944)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, German footballer (b. 1930)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Greek theoretician and author (b. 1930)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek theoretician and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek theoretician and author (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hotzeas"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Puerto Rican-American actor and singer (b. 1940)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American actor and singer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American actor and singer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American voice actor and singer (b. 1926)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and singer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor and singer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Danish actress (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actress (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actress (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American actress and set designer (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and set designer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and set designer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, German author and critic (b. 1937)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and critic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and critic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wolf_R%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech director and screenwriter (b. 1935)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Jaromil_Jire%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech director and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaromil_Jire%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech director and screenwriter (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaromil_Jire%C5%A1"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American soldier and politician, 59th United States Postmaster General (b. 1921)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Blount\" title=\"<PERSON>ton M. Blount\"><PERSON><PERSON></a>, American soldier and politician, 59th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Blount\" title=\"<PERSON><PERSON> M. Blount\"><PERSON><PERSON></a>, American soldier and politician, 59th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Postmaster General", "link": "https://wikipedia.org/wiki/United_States_Postmaster_General"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Colombian footballer (b. 1969)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Gaviria\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hern%C3%A1n_Gaviria\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian footballer (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hern%C3%A1n_Gaviria"}]}, {"year": "2002", "text": "<PERSON>, English-American activist, co-founded the Mattachine Society and Radical Faeries (b. 1912)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Mattachine_Society\" title=\"Mattachine Society\">Mattachine Society</a> and <a href=\"https://wikipedia.org/wiki/Radical_Faeries\" title=\"Radical Faeries\">Radical Faeries</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Mattachine_Society\" title=\"Mattachine Society\">Mattachine Society</a> and <a href=\"https://wikipedia.org/wiki/Radical_Faeries\" title=\"Radical Faeries\">Radical Faeries</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mattachine Society", "link": "https://wikipedia.org/wiki/Mattachine_Society"}, {"title": "Radical Faeries", "link": "https://wikipedia.org/wiki/Radical_Faeries"}]}, {"year": "2002", "text": "<PERSON>, American actress and singer (b. 1918)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American engineer (b. 1954)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American race car driver and businessman (b. 1980)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American cardinal (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Estonian chess player (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ik<PERSON>\" title=\"Maaja Ranniku\"><PERSON><PERSON></a>, Estonian chess player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ranniku\"><PERSON><PERSON></a>, Estonian chess player (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nniku"}]}, {"year": "2005", "text": "<PERSON>, American soprano and actress (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Honduran businessman and politician, President of Honduras (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo\" title=\"José <PERSON> Hoyo\"><PERSON></a>, Honduran businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo\" title=\"José <PERSON> Hoyo\"><PERSON></a>, Honduran businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (b. 1926)", "links": [{"title": "José <PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Azcona_del_Hoyo"}, {"title": "President of Honduras", "link": "https://wikipedia.org/wiki/President_of_Honduras"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian painter (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Mo<PERSON>rameh_Ghanbari\" title=\"Mokarrameh Ghanbari\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian painter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>rameh_Ghanbari\" title=\"Mo<PERSON><PERSON>eh Ghanbari\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian painter (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mo<PERSON>rameh_Ghanbari"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, Chinese sinologist and scholar (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Immanuel_C._Y._Hsu\" title=\"Immanuel C. Y. Hsu\">Immanu<PERSON> <PERSON><PERSON></a>, Chinese sinologist and scholar (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Immanuel_C._Y._Hsu\" title=\"Immanuel C. Y<PERSON>\">Immanu<PERSON> <PERSON><PERSON></a>, Chinese sinologist and scholar (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Immanuel_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American civil rights activist (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Rosa_Parks\" title=\"Rosa Parks\"><PERSON></a>, American civil rights activist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosa_Parks\" title=\"Rosa Parks\"><PERSON></a>, American civil rights activist (b. 1913)", "links": [{"title": "Rosa <PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Parks"}]}, {"year": "2005", "text": "<PERSON>, English actor and screenwriter (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American educator and activist (b. 1904)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>cMillan\"><PERSON><PERSON></a>, American educator and activist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>Millan\" title=\"<PERSON><PERSON> McMillan\"><PERSON><PERSON></a>, American educator and activist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enolia_M<PERSON>Millan"}]}, {"year": "2006", "text": "<PERSON>, Scottish historian and scholar (b. 1909)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Czech organist and composer (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech organist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech organist and composer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand author (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Kyrgyzstan journalist (b. 1981)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Kyrgyzstan journalist (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Kyrgyzstan journalist (b. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English journalist and author (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American pianist and composer (b. 1943)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cotel\"><PERSON><PERSON></a>, American pianist and composer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cotel\"><PERSON><PERSON></a>, American pianist and composer (b. 1943)", "links": [{"title": "Moshe Cotel", "link": "https://wikipedia.org/wiki/<PERSON>she_Cotel"}]}, {"year": "2010", "text": "<PERSON>, American author and illustrator (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and illustrator (b. 1927)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actor, director, and producer (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American author and playwright (b. 1912)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Taiwanese composer and educator (b. 1967)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Sansan_<PERSON>en\" title=\"<PERSON><PERSON> Chi<PERSON>\"><PERSON><PERSON></a>, Taiwanese composer and educator (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese composer and educator (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}]}, {"year": "2011", "text": "<PERSON>, American computer scientist and academic, developed the Lisp programming language (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and academic, developed the <a href=\"https://wikipedia.org/wiki/Lisp_(programming_language)\" title=\"Lisp (programming language)\">Lisp programming language</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and academic, developed the <a href=\"https://wikipedia.org/wiki/Lisp_(programming_language)\" title=\"Lisp (programming language)\">Lisp programming language</a> (b. 1927)", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}, {"title": "Lisp (programming language)", "link": "https://wikipedia.org/wiki/Lisp_(programming_language)"}]}, {"year": "2012", "text": "<PERSON>, American actress (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Swedish actress (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6rk\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bj%C3%B6rk\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anita_Bj%C3%B6rk"}]}, {"year": "2012", "text": "<PERSON>, American wrestler and sportscaster (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American tennis player (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English director and producer (b. 1951)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bird\"><PERSON><PERSON></a>, English director and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English director and producer (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bird"}]}, {"year": "2013", "text": "<PERSON>, American girl with a rare genetic disorder (b. 1993)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American girl with a rare genetic disorder (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American girl with a rare genetic disorder (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Mexican model and actress (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican model and actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican model and actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American football player and coach (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>w_May<PERSON>\" title=\"<PERSON><PERSON> Mayne\"><PERSON><PERSON></a>, American football player and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_May<PERSON>\" title=\"<PERSON><PERSON> May<PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lew_Mayne"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, South African runner (b. 1980)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African runner (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African runner (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian actor, director, and producer (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"S. S<PERSON> Raj<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"S. S<PERSON> Raj<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian actor, director, and producer (b. 1928)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actress and singer (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English commander and pilot (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander and pilot (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander and pilot (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and academic (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian author and critic (b. 1960)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian author and critic (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian author and critic (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mar<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Slovak cardinal (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/J%C3%A1n_Chryzostom_Korec\" title=\"<PERSON><PERSON> Korec\"><PERSON><PERSON></a>, Slovak cardinal (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1n_Chryzostom_Korec\" title=\"<PERSON><PERSON> Korec\"><PERSON><PERSON></a>, Slovak cardinal (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1n_Chryzostom_Korec"}]}, {"year": "2015", "text": "<PERSON>, Irish-American actress and singer (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and singer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and singer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara"}]}, {"year": "2016", "text": "<PERSON>, American pop singer (b. 1943)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Uruguayan politician, former president (2000-2005) (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>b%C3%A1%C3%B1ez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan politician, former president (2000-2005) (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>b%C3%A1%C3%B1ez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Uruguayan politician, former president (2000-2005) (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ib%C3%A1%C3%B1ez"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American pianist and singer-songwriter (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Fats_Domino\" title=\"Fats Domino\"><PERSON><PERSON></a>, American pianist and singer-songwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fats_Domino\" title=\"Fats Domino\"><PERSON><PERSON></a>, American pianist and singer-songwriter (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fats_Domino"}]}, {"year": "2017", "text": "<PERSON>, American actor (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Indian classical singer (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical singer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical singer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American singer/songwriter (b. 1943)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Tony <PERSON>\"><PERSON></a>, American singer/songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Tony <PERSON>\"><PERSON></a>, American singer/songwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1962)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor, writer, and singer (b. 1955)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, writer, and singer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, writer, and singer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American basketball player and coach (b. 1981)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Moroccan footballer (b. 1989)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Barrada\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan footballer (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ada\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan footballer (b. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abd<PERSON><PERSON>z_Barrada"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American screenwriter (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}