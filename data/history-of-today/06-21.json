{"date": "June 21", "url": "https://wikipedia.org/wiki/June_21", "data": {"Events": [{"year": "533", "text": "A Byzantine expeditionary fleet under <PERSON><PERSON><PERSON><PERSON> sails from Constantinople to attack the Vandals in Africa, via Greece and Sicily.", "html": "533 - A Byzantine expeditionary fleet under <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\">Belisarios</a> sails from Constantinople to <a href=\"https://wikipedia.org/wiki/Vandalic_War\" title=\"Vandalic War\">attack the Vandals</a> in Africa, via Greece and Sicily.", "no_year_html": "A Byzantine expeditionary fleet under <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\">Belisa<PERSON><PERSON></a> sails from Constantinople to <a href=\"https://wikipedia.org/wiki/Vandalic_War\" title=\"Vandalic War\">attack the Vandals</a> in Africa, via Greece and Sicily.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Vandalic War", "link": "https://wikipedia.org/wiki/Vandalic_War"}]}, {"year": "1307", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> is enthroned as <PERSON><PERSON><PERSON> of the Mongols and <PERSON><PERSON> of the Yuan.", "html": "1307 - <a href=\"https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan\" title=\"Kü<PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is enthroned as <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> and <a href=\"https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan\" title=\"Külüg Khan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> is enthroned as <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"K<PERSON>gan\"><PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> and <a href=\"https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Khan\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>gan"}, {"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%BCl%C3%BCg_Khan"}, {"title": "Yuan dynasty", "link": "https://wikipedia.org/wiki/Yuan_dynasty"}]}, {"year": "1529", "text": "French forces are driven out of northern Italy by Spain at the Battle of Landriano during the War of the League of Cognac.", "html": "1529 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> forces are driven out of northern Italy by <a href=\"https://wikipedia.org/wiki/Habsburg_Spain\" title=\"Habsburg Spain\">Spain</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Landriano\" title=\"Battle of Landriano\">Battle of Landriano</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cognac\" title=\"War of the League of Cognac\">War of the League of Cognac</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French</a> forces are driven out of northern Italy by <a href=\"https://wikipedia.org/wiki/Habsburg_Spain\" title=\"Habsburg Spain\">Spain</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Landriano\" title=\"Battle of Landriano\">Battle of Landriano</a> during the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cognac\" title=\"War of the League of Cognac\">War of the League of Cognac</a>.", "links": [{"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Habsburg Spain", "link": "https://wikipedia.org/wiki/Habsburg_Spain"}, {"title": "Battle of Landriano", "link": "https://wikipedia.org/wiki/Battle_of_Landriano"}, {"title": "War of the League of Cognac", "link": "https://wikipedia.org/wiki/War_of_the_League_of_Cognac"}]}, {"year": "1582", "text": "Sengoku period: <PERSON><PERSON>, the most powerful of the Japanese daimyōs, is forced to commit suicide by his own general <PERSON><PERSON><PERSON>.", "html": "1582 - <a href=\"https://wikipedia.org/wiki/Sengoku_period\" title=\"Sengoku period\">Sengoku period</a>: <a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON> Nobu<PERSON>ga</a>, the most powerful of the Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Daimyō\">daimyōs</a></i>, is <a href=\"https://wikipedia.org/wiki/Honn%C5%8D-ji_Incident\" title=\"Honnō-ji Incident\">forced to commit suicide</a> by his own general <a href=\"https://wikipedia.org/wiki/Akechi_Mitsuhide\" title=\"Ake<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sengoku_period\" title=\"Sengoku period\">Sengoku period</a>: <a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON> Nobunaga</a>, the most powerful of the Japanese <i><a href=\"https://wikipedia.org/wiki/Daimy%C5%8D\" class=\"mw-redirect\" title=\"Daimyō\">daimyōs</a></i>, is <a href=\"https://wikipedia.org/wiki/Honn%C5%8D-ji_Incident\" title=\"Honnō-ji Incident\">forced to commit suicide</a> by his own general <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>hide\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Sengoku period", "link": "https://wikipedia.org/wiki/Sengoku_period"}, {"title": "Oda Nobunaga", "link": "https://wikipedia.org/wiki/Oda_<PERSON>ga"}, {"title": "Daimy<PERSON>", "link": "https://wikipedia.org/wiki/Daimy%C5%8D"}, {"title": "Honnō-ji Incident", "link": "https://wikipedia.org/wiki/Honn%C5%8D-ji_Incident"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>de"}]}, {"year": "1621", "text": "Execution of 27 Czech noblemen on the Old Town Square in Prague as a consequence of the Battle of White Mountain.", "html": "1621 - <a href=\"https://wikipedia.org/wiki/Old_Town_Square_execution\" title=\"Old Town Square execution\">Execution of 27 Czech noblemen</a> on the Old Town Square in Prague as a consequence of the <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old_Town_Square_execution\" title=\"Old Town Square execution\">Execution of 27 Czech noblemen</a> on the Old Town Square in Prague as a consequence of the <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a>.", "links": [{"title": "Old Town Square execution", "link": "https://wikipedia.org/wiki/Old_Town_Square_execution"}, {"title": "Battle of White Mountain", "link": "https://wikipedia.org/wiki/Battle_of_White_Mountain"}]}, {"year": "1734", "text": "In Montreal, New France, a  slave known by the French name of <PERSON><PERSON><PERSON> is put to death, having been convicted of setting the fire that destroyed much of the city.", "html": "1734 - In <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>, <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>, a slave known by the French name of <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9lique\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is put to death, having been convicted of setting the fire that destroyed much of the city.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>, <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>, a slave known by the French name of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lique\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is put to death, having been convicted of setting the fire that destroyed much of the city.", "links": [{"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9lique"}]}, {"year": "1749", "text": "Halifax, Nova Scotia, is founded.", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a>, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a>, is founded.", "links": [{"title": "Halifax, Nova Scotia", "link": "https://wikipedia.org/wiki/Halifax,_Nova_Scotia"}]}, {"year": "1768", "text": "<PERSON> offends the King and Parliament in a speech to the Massachusetts General Court.", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a> offends the King and Parliament in a speech to the Massachusetts General Court.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a> offends the King and Parliament in a speech to the Massachusetts General Court.", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1788", "text": "New Hampshire becomes the ninth state to ratify the Constitution of the United States.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> becomes the ninth state to ratify the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">Constitution of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> becomes the ninth state to ratify the <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">Constitution of the United States</a>.", "links": [{"title": "New Hampshire", "link": "https://wikipedia.org/wiki/New_Hampshire"}, {"title": "Constitution of the United States", "link": "https://wikipedia.org/wiki/Constitution_of_the_United_States"}]}, {"year": "1791", "text": "King <PERSON> of France and his immediate family begin the Flight to Varennes during the French Revolution.", "html": "1791 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON> of France</a> and his immediate family begin the <a href=\"https://wikipedia.org/wiki/Flight_to_Varennes\" title=\"Flight to Varennes\">Flight to Varennes</a> during the French Revolution.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_XVI_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVI of France\"><PERSON> of France</a> and his immediate family begin the <a href=\"https://wikipedia.org/wiki/Flight_to_Varennes\" title=\"Flight to Varennes\">Flight to Varennes</a> during the French Revolution.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Flight to Varennes", "link": "https://wikipedia.org/wiki/Flight_to_Varennes"}]}, {"year": "1798", "text": "Irish Rebellion of 1798: The British Army defeats Irish rebels at the Battle of Vinegar Hill.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>: The British Army defeats Irish rebels at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vinegar_Hill\" title=\"Battle of Vinegar Hill\">Battle of Vinegar Hill</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>: The British Army defeats Irish rebels at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vinegar_Hill\" title=\"Battle of Vinegar Hill\">Battle of Vinegar Hill</a>.", "links": [{"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Battle of Vinegar Hill", "link": "https://wikipedia.org/wiki/Battle_of_Vinegar_Hill"}]}, {"year": "1813", "text": "Peninsular War: <PERSON> defeats <PERSON> at the Battle of Vitoria.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: Wellington defeats <PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vitoria\" title=\"Battle of Vitoria\">Battle of Vitoria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: Wellington defeats <PERSON> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vitoria\" title=\"Battle of Vitoria\">Battle of Vitoria</a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Battle of Vitoria", "link": "https://wikipedia.org/wiki/Battle_of_Vitoria"}]}, {"year": "1824", "text": "Greek War of Independence: Egyptian forces capture Psara in the Aegean Sea.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Egyptian forces capture <a href=\"https://wikipedia.org/wiki/Psara\" title=\"<PERSON>sar<PERSON>\">Psara</a> in the Aegean Sea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a>: Egyptian forces capture <a href=\"https://wikipedia.org/wiki/Psara\" title=\"<PERSON>sara\">Psara</a> in the Aegean Sea.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sara"}]}, {"year": "1826", "text": "Maniots defeat Egyptians under <PERSON> in the Battle of Vergas.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Maniots\" title=\"Maniots\">Maniots</a> defeat Egyptians under <PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vergas\" class=\"mw-redirect\" title=\"Battle of Vergas\">Battle of Vergas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maniots\" title=\"Maniots\">Maniots</a> defeat Egyptians under <PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vergas\" class=\"mw-redirect\" title=\"Battle of Vergas\">Battle of Vergas</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maniots"}, {"title": "Battle of Vergas", "link": "https://wikipedia.org/wiki/Battle_of_Vergas"}]}, {"year": "1848", "text": "In the Wallachian Revolution, <PERSON> and <PERSON> issue the Proclamation of Islaz and create a new republican government.", "html": "1848 - In the <a href=\"https://wikipedia.org/wiki/Wallachian_Revolution_of_1848\" title=\"Wallachian Revolution of 1848\">Wallachian Revolution</a>, <a href=\"https://wikipedia.org/wiki/Ion_Heliade_R%C4%83du<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Christian_Tell\" title=\"Christian Tell\"><PERSON></a> issue the <a href=\"https://wikipedia.org/wiki/Proclamation_of_Islaz\" title=\"Proclamation of Islaz\">Proclamation of Islaz</a> and create a new republican government.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Wallachian_Revolution_of_1848\" title=\"Wallachian Revolution of 1848\">Wallachian Revolution</a>, <a href=\"https://wikipedia.org/wiki/Ion_Heliade_R%C4%83du<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Christian_Tell\" title=\"Christian Tell\"><PERSON></a> issue the <a href=\"https://wikipedia.org/wiki/Proclamation_of_Islaz\" title=\"Proclamation of Islaz\">Proclamation of Islaz</a> and create a new republican government.", "links": [{"title": "Wallachian Revolution of 1848", "link": "https://wikipedia.org/wiki/Wallachian_Revolution_of_1848"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_Heliade_R%C4%83dulescu"}, {"title": "Christian Tell", "link": "https://wikipedia.org/wiki/Christian_Tell"}, {"title": "Proclamation of Islaz", "link": "https://wikipedia.org/wiki/Proclamation_of_Islaz"}]}, {"year": "1864", "text": "American Civil War: The Battle of Jerusalem Plank Road begins.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Jerusalem_Plank_Road\" title=\"Battle of Jerusalem Plank Road\">Battle of Jerusalem Plank Road</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Jerusalem_Plank_Road\" title=\"Battle of Jerusalem Plank Road\">Battle of Jerusalem Plank Road</a> begins.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Jerusalem Plank Road", "link": "https://wikipedia.org/wiki/Battle_of_Jerusalem_Plank_Road"}]}, {"year": "1898", "text": "The United States captures Guam from Spain. The few warning shots fired by the U.S. naval vessels are misinterpreted as salutes by the Spanish garrison, which was unaware that the two nations were at war.", "html": "1898 - The United States <a href=\"https://wikipedia.org/wiki/Capture_of_Guam\" title=\"Capture of Guam\">captures Guam</a> from Spain. The few warning shots fired by the U.S. naval vessels are misinterpreted as salutes by the Spanish garrison, which was unaware that the two nations were at war.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Capture_of_Guam\" title=\"Capture of Guam\">captures Guam</a> from Spain. The few warning shots fired by the U.S. naval vessels are misinterpreted as salutes by the Spanish garrison, which was unaware that the two nations were at war.", "links": [{"title": "Capture of Guam", "link": "https://wikipedia.org/wiki/Capture_of_Guam"}]}, {"year": "1900", "text": "Boxer Rebellion: China formally declares war on the United States, Britain, Germany, France and Japan, as an edict issued from the Empress <PERSON><PERSON>.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: China formally declares war on the United States, Britain, Germany, France and Japan, as an edict issued from the <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>ager_Cixi\" title=\"Empress Dow<PERSON> Cixi\">Empress <PERSON><PERSON> Cixi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: China formally declares war on the United States, Britain, Germany, France and Japan, as an edict issued from the <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_Cixi\" title=\"Empress <PERSON><PERSON> Cixi\">Empress <PERSON><PERSON>ix<PERSON></a>.", "links": [{"title": "Boxer Rebellion", "link": "https://wikipedia.org/wiki/Boxer_Rebellion"}, {"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1915", "text": "The U.S. Supreme Court hands down its decision in <PERSON><PERSON><PERSON> v. United States 238 US 347 1915, striking down Oklahoma grandfather clause legislation which had the effect of denying the right to vote to blacks.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> hands down its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._United_States\" title=\"<PERSON><PERSON><PERSON> v. United States\"><PERSON><PERSON><PERSON> v. United States</a></i> 238 US 347 1915, striking down <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a> <a href=\"https://wikipedia.org/wiki/Grandfather_clause\" title=\"Grandfather clause\">grandfather clause</a> legislation which had the effect of denying the right to vote to blacks.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> hands down its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_v._United_States\" title=\"<PERSON><PERSON><PERSON> v. United States\"><PERSON><PERSON><PERSON> v. United States</a></i> 238 US 347 1915, striking down <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a> <a href=\"https://wikipedia.org/wiki/Grandfather_clause\" title=\"Grandfather clause\">grandfather clause</a> legislation which had the effect of denying the right to vote to blacks.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON><PERSON><PERSON> v. United States", "link": "https://wikipedia.org/wiki/Guinn_v._United_States"}, {"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}, {"title": "Grandfather clause", "link": "https://wikipedia.org/wiki/Grandfather_clause"}]}, {"year": "1919", "text": "The Royal Canadian Mounted Police fire a volley into a crowd of unemployed war veterans, killing two, during the Winnipeg general strike.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a> fire a volley into a crowd of unemployed war veterans, killing two, during the <a href=\"https://wikipedia.org/wiki/Winnipeg_general_strike\" title=\"Winnipeg general strike\">Winnipeg general strike</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Royal Canadian Mounted Police</a> fire a volley into a crowd of unemployed war veterans, killing two, during the <a href=\"https://wikipedia.org/wiki/Winnipeg_general_strike\" title=\"Winnipeg general strike\">Winnipeg general strike</a>.", "links": [{"title": "Royal Canadian Mounted Police", "link": "https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police"}, {"title": "Winnipeg general strike", "link": "https://wikipedia.org/wiki/Winnipeg_general_strike"}]}, {"year": "1919", "text": "Admiral <PERSON> scuttles the German fleet at Scapa Flow, Orkney. The nine sailors killed are the last casualties of World War I.", "html": "1919 - Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Scuttling_of_the_German_fleet_at_Scapa_Flow\" title=\"Scuttling of the German fleet at Scapa Flow\">scuttles the German fleet</a> at <a href=\"https://wikipedia.org/wiki/Scapa_Flow\" title=\"Scapa Flow\">Scapa Flow</a>, <a href=\"https://wikipedia.org/wiki/Orkney\" title=\"Orkney\">Orkney</a>. The nine sailors killed are the last casualties of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "no_year_html": "Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Scuttling_of_the_German_fleet_at_Scapa_Flow\" title=\"Scuttling of the German fleet at Scapa Flow\">scuttles the German fleet</a> at <a href=\"https://wikipedia.org/wiki/Scapa_Flow\" title=\"Scapa Flow\">Scapa Flow</a>, <a href=\"https://wikipedia.org/wiki/Orkney\" title=\"Orkney\">Orkney</a>. The nine sailors killed are the last casualties of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Scuttling of the German fleet at Scapa Flow", "link": "https://wikipedia.org/wiki/Scuttling_of_the_German_fleet_at_Scapa_Flow"}, {"title": "Scapa Flow", "link": "https://wikipedia.org/wiki/Scapa_Flow"}, {"title": "Orkney", "link": "https://wikipedia.org/wiki/Orkney"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1921", "text": "The Irish village of Knockcroghery was burned by British forces.", "html": "1921 - The Irish village of Knockcroghery <a href=\"https://wikipedia.org/wiki/Knockcroghery#Burning_of_Knockcroghery\" title=\"Knockcroghery\">was burned by British forces</a>.", "no_year_html": "The Irish village of Knockcroghery <a href=\"https://wikipedia.org/wiki/Knockcroghery#Burning_of_Knockcroghery\" title=\"Knockcroghery\">was burned by British forces</a>.", "links": [{"title": "Knockcroghery", "link": "https://wikipedia.org/wiki/Knockcroghery#Burning_of_Knockcroghery"}]}, {"year": "1929", "text": "An agreement brokered by U.S. Ambassador <PERSON> ends the Cristero War in Mexico.", "html": "1929 - An agreement brokered by U.S. Ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> ends the <a href=\"https://wikipedia.org/wiki/Cristero_War\" title=\"Cristero War\">Cristero War</a> in Mexico.", "no_year_html": "An agreement brokered by U.S. Ambassador <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> ends the <a href=\"https://wikipedia.org/wiki/Cristero_War\" title=\"Cristero War\">Cristero War</a> in Mexico.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cristero War", "link": "https://wikipedia.org/wiki/Cristero_War"}]}, {"year": "1930", "text": "One-year conscription comes into force in France.", "html": "1930 - One-year <a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">conscription</a> comes into force in France.", "no_year_html": "One-year <a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">conscription</a> comes into force in France.", "links": [{"title": "Conscription", "link": "https://wikipedia.org/wiki/Conscription"}]}, {"year": "1940", "text": "World War II: Italy begins an unsuccessful invasion of France.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Italy begins an <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_France\" title=\"Italian invasion of France\">unsuccessful invasion</a> of France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Italy begins an <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_France\" title=\"Italian invasion of France\">unsuccessful invasion</a> of France.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Italian invasion of France", "link": "https://wikipedia.org/wiki/Italian_invasion_of_France"}]}, {"year": "1942", "text": "World War II: Tobruk falls to Italian and German forces; 33,000 Allied troops are taken prisoner.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Axis_capture_of_Tobruk\" title=\"Axis capture of Tobruk\">Tobruk falls</a> to Italian and German forces; 33,000 Allied troops are taken prisoner.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Axis_capture_of_Tobruk\" title=\"Axis capture of Tobruk\">Tobruk falls</a> to Italian and German forces; 33,000 Allied troops are taken prisoner.", "links": [{"title": "Axis capture of Tobruk", "link": "https://wikipedia.org/wiki/Axis_capture_of_Tobruk"}]}, {"year": "1942", "text": "World War II: A Japanese submarine surfaces near the Columbia River in Oregon, firing 17 shells at Fort Stevens in one of only a handful of attacks by Japan against the United States mainland.", "html": "1942 - World War II: A Japanese submarine surfaces near the Columbia River in Oregon, <a href=\"https://wikipedia.org/wiki/Bombardment_of_Fort_Stevens\" title=\"Bombardment of Fort Stevens\">firing 17 shells at Fort Stevens</a> in one of only a handful of attacks by Japan against the United States mainland.", "no_year_html": "World War II: A Japanese submarine surfaces near the Columbia River in Oregon, <a href=\"https://wikipedia.org/wiki/Bombardment_of_Fort_Stevens\" title=\"Bombardment of Fort Stevens\">firing 17 shells at Fort Stevens</a> in one of only a handful of attacks by Japan against the United States mainland.", "links": [{"title": "Bombardment of Fort Stevens", "link": "https://wikipedia.org/wiki/Bombardment_of_Fort_Stevens"}]}, {"year": "1945", "text": "World War II: The Battle of Okinawa ends when the organized resistance of Imperial Japanese Army forces collapses in the Mabuni area on the southern tip of the main island.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">Battle of Okinawa</a> ends when the organized resistance of <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> forces collapses in the Mabuni area on the southern tip of the main island.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">Battle of Okinawa</a> ends when the organized resistance of <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> forces collapses in the Mabuni area on the southern tip of the main island.", "links": [{"title": "Battle of Okinawa", "link": "https://wikipedia.org/wiki/Battle_of_Okinawa"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}]}, {"year": "1952", "text": "The Philippine School of Commerce, through a republic act, is converted to Philippine College of Commerce, later to be the Polytechnic University of the Philippines.", "html": "1952 - The Philippine School of Commerce, through a republic act, is converted to Philippine College of Commerce, later to be the <a href=\"https://wikipedia.org/wiki/Polytechnic_University_of_the_Philippines\" title=\"Polytechnic University of the Philippines\">Polytechnic University of the Philippines</a>.", "no_year_html": "The Philippine School of Commerce, through a republic act, is converted to Philippine College of Commerce, later to be the <a href=\"https://wikipedia.org/wiki/Polytechnic_University_of_the_Philippines\" title=\"Polytechnic University of the Philippines\">Polytechnic University of the Philippines</a>.", "links": [{"title": "Polytechnic University of the Philippines", "link": "https://wikipedia.org/wiki/Polytechnic_University_of_the_Philippines"}]}, {"year": "1957", "text": "<PERSON> is sworn in as Canada's first female Cabinet Minister.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as Canada's first female <a href=\"https://wikipedia.org/wiki/Cabinet_of_Canada\" title=\"Cabinet of Canada\">Cabinet Minister</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as Canada's first female <a href=\"https://wikipedia.org/wiki/Cabinet_of_Canada\" title=\"Cabinet of Canada\">Cabinet Minister</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Cabinet of Canada", "link": "https://wikipedia.org/wiki/Cabinet_of_Canada"}]}, {"year": "1963", "text": "<PERSON> <PERSON> is elected as Pope <PERSON>.", "html": "1963 - Cardinal <PERSON> is <a href=\"https://wikipedia.org/wiki/1963_papal_conclave\" title=\"1963 papal conclave\">elected</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a>.", "no_year_html": "Cardinal <PERSON> is <a href=\"https://wikipedia.org/wiki/1963_papal_conclave\" title=\"1963 papal conclave\">elected</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a>.", "links": [{"title": "1963 papal conclave", "link": "https://wikipedia.org/wiki/1963_papal_conclave"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "Three civil rights workers, <PERSON>, <PERSON> and <PERSON>, are murdered in Neshoba County, Mississippi, United States, by members of the Ku Klux Klan.", "html": "1964 - Three <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights</a> workers, <a href=\"https://wikipedia.org/wiki/<PERSON>(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>\" title=\"Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>\">are murdered</a> in <a href=\"https://wikipedia.org/wiki/Neshoba_County,_Mississippi\" title=\"Neshoba County, Mississippi\">Neshoba County, Mississippi</a>, United States, by members of the <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a>.", "no_year_html": "Three <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights</a> workers, <a href=\"https://wikipedia.org/wiki/<PERSON>(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>\" title=\"Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>\">are murdered</a> in <a href=\"https://wikipedia.org/wiki/Neshoba_County,_Mississippi\" title=\"Neshoba County, Mississippi\">Neshoba County, Mississippi</a>, United States, by members of the <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a>.", "links": [{"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}, {"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_(activist)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Neshoba County, Mississippi", "link": "https://wikipedia.org/wiki/Neshoba_County,_Mississippi"}, {"title": "Ku Klux Klan", "link": "https://wikipedia.org/wiki/Ku_Klux_Klan"}]}, {"year": "1970", "text": "Penn Central declares Section 77 bankruptcy in what was the largest U.S. corporate bankruptcy to date.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Penn_Central\" class=\"mw-redirect\" title=\"Penn Central\">Penn Central</a> <a href=\"https://wikipedia.org/wiki/Bankruptcy_of_Penn_Central\" title=\"Bankruptcy of Penn Central\">declares</a> Section 77 <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a> in what was the largest U.S. corporate bankruptcy to date.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Penn_Central\" class=\"mw-redirect\" title=\"Penn Central\">Penn Central</a> <a href=\"https://wikipedia.org/wiki/Bankruptcy_of_Penn_Central\" title=\"Bankruptcy of Penn Central\">declares</a> Section 77 <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a> in what was the largest U.S. corporate bankruptcy to date.", "links": [{"title": "Penn Central", "link": "https://wikipedia.org/wiki/Penn_Central"}, {"title": "Bankruptcy of Penn Central", "link": "https://wikipedia.org/wiki/Bankruptcy_of_Penn_Central"}, {"title": "Bankruptcy", "link": "https://wikipedia.org/wiki/Bankruptcy"}]}, {"year": "1973", "text": "In its decision in <PERSON> v. California, 413 U.S. 15, the Supreme Court of the United States establishes the Miller test for determining whether something is obscene and not protected speech under the U.S. constitution.", "html": "1973 - In its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._California\" title=\"Miller v. California\"><PERSON> v. California</a></i>, 413 U.S. 15, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> establishes the <a href=\"https://wikipedia.org/wiki/Miller_test\" title=\"Miller test\">Miller test</a> for determining whether something is <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscene</a> and not protected speech under the U.S. constitution.", "no_year_html": "In its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._California\" title=\"Miller v. California\"><PERSON> v. California</a></i>, 413 U.S. 15, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> establishes the <a href=\"https://wikipedia.org/wiki/Miller_test\" title=\"Miller test\">Miller test</a> for determining whether something is <a href=\"https://wikipedia.org/wiki/Obscenity\" title=\"Obscenity\">obscene</a> and not protected speech under the U.S. constitution.", "links": [{"title": "<PERSON> v. California", "link": "https://wikipedia.org/wiki/Miller_v._California"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Miller test", "link": "https://wikipedia.org/wiki/<PERSON>_test"}, {"title": "Obscenity", "link": "https://wikipedia.org/wiki/Obscenity"}]}, {"year": "1978", "text": "The original production of <PERSON> and <PERSON>'s musical, <PERSON><PERSON><PERSON>, based on the life of <PERSON>, opens at the Prince Edward Theatre, London.", "html": "1978 - The original production of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Musical_theatre\" title=\"Musical theatre\">musical</a>, <i><a href=\"https://wikipedia.org/wiki/Evita_(musical)\" title=\"Evita (musical)\">Evita</a></i>, based on the life of <a href=\"https://wikipedia.org/wiki/Eva_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, opens at the <a href=\"https://wikipedia.org/wiki/Prince_Edward_Theatre\" title=\"Prince Edward Theatre\">Prince Edward Theatre</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The original production of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Musical_theatre\" title=\"Musical theatre\">musical</a>, <i><a href=\"https://wikipedia.org/wiki/Evita_(musical)\" title=\"<PERSON>vita (musical)\">Evita</a></i>, based on the life of <a href=\"https://wikipedia.org/wiki/Eva_Per%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, opens at the <a href=\"https://wikipedia.org/wiki/Prince_Edward_Theatre\" title=\"Prince Edward Theatre\">Prince Edward Theatre</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Musical theatre", "link": "https://wikipedia.org/wiki/Musical_theatre"}, {"title": "<PERSON><PERSON><PERSON> (musical)", "link": "https://wikipedia.org/wiki/Evita_(musical)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Per%C3%B3n"}, {"title": "Prince Edward Theatre", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1982", "text": "<PERSON> is found not guilty by reason of insanity for the attempted assassination of U.S. President <PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON></a> is found <a href=\"https://wikipedia.org/wiki/Insanity_defense\" title=\"Insanity defense\">not guilty by reason of insanity</a> for the <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>\" title=\"Attempted assassination of <PERSON>\">attempted assassination</a> of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON></a> is found <a href=\"https://wikipedia.org/wiki/Insanity_defense\" title=\"Insanity defense\">not guilty by reason of insanity</a> for the <a href=\"https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>\" title=\"Attempted assassination of <PERSON>\">attempted assassination</a> of U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Insanity defense", "link": "https://wikipedia.org/wiki/Insanity_defense"}, {"title": "Attempted assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Attempted_assassination_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "Braathens SAFE Flight 139 is hijacked on approach to Oslo Airport, Fornebu. Special forces arrest the hijacker and there are no fatalities.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Braathens_SAFE_Flight_139\" title=\"Braathens SAFE Flight 139\">Braathens SAFE Flight 139</a> is hijacked on approach to <a href=\"https://wikipedia.org/wiki/Oslo_Airport,_Fornebu\" title=\"Oslo Airport, Fornebu\">Oslo Airport, Fornebu</a>. Special forces arrest the hijacker and there are no fatalities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Braathens_SAFE_Flight_139\" title=\"Braathens SAFE Flight 139\">Braathens SAFE Flight 139</a> is hijacked on approach to <a href=\"https://wikipedia.org/wiki/Oslo_Airport,_Fornebu\" title=\"Oslo Airport, Fornebu\">Oslo Airport, Fornebu</a>. Special forces arrest the hijacker and there are no fatalities.", "links": [{"title": "Braathens SAFE Flight 139", "link": "https://wikipedia.org/wiki/B<PERSON><PERSON>ens_SAFE_Flight_139"}, {"title": "Oslo Airport, Fornebu", "link": "https://wikipedia.org/wiki/Oslo_Airport,_Fornebu"}]}, {"year": "1989", "text": "The U.S. Supreme Court rules in Texas v<PERSON>, 491 U.S. 397, that American flag-burning is a form of political protest protected by the First Amendment.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Texas_v<PERSON>_<PERSON>\" title=\"Texas v<PERSON> Johnson\">Texas v<PERSON></a></i>, 491 U.S. 397, that American flag-burning is a form of political protest protected by the First Amendment.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Texas_v<PERSON>_<PERSON>\" title=\"Texas v<PERSON> Johnson\">Texas v<PERSON></a></i>, 491 U.S. 397, that American flag-burning is a form of political protest protected by the First Amendment.", "links": [{"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}, {"title": "Texas v. <PERSON>", "link": "https://wikipedia.org/wiki/Texas_v._<PERSON>"}]}, {"year": "1993", "text": "Space Shuttle Endeavour is launched on STS-57 to retrieve the European Retrievable Carrier (EURECA) satellite. It is also the first shuttle mission to carry the Spacehab module.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-57\" title=\"STS-57\">STS-57</a> to retrieve the <a href=\"https://wikipedia.org/wiki/European_Retrievable_Carrier\" title=\"European Retrievable Carrier\">European Retrievable Carrier</a> (EURECA) satellite. It is also the first shuttle mission to carry the <a href=\"https://wikipedia.org/wiki/Astrotech_Corporation\" title=\"Astrotech Corporation\">Spacehab</a> module.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-57\" title=\"STS-57\">STS-57</a> to retrieve the <a href=\"https://wikipedia.org/wiki/European_Retrievable_Carrier\" title=\"European Retrievable Carrier\">European Retrievable Carrier</a> (EURECA) satellite. It is also the first shuttle mission to carry the <a href=\"https://wikipedia.org/wiki/Astrotech_Corporation\" title=\"Astrotech Corporation\">Spacehab</a> module.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-57", "link": "https://wikipedia.org/wiki/STS-57"}, {"title": "European Retrievable Carrier", "link": "https://wikipedia.org/wiki/European_Retrievable_Carrier"}, {"title": "Astrotech Corporation", "link": "https://wikipedia.org/wiki/Astrotech_Corporation"}]}, {"year": "2000", "text": "Section 28 (of the Local Government Act 1988), outlawing the 'promotion' of homosexuality in the United Kingdom, is repealed in Scotland with a 99 to 17 vote.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Section_28\" title=\"Section 28\">Section 28</a> (of the <a href=\"https://wikipedia.org/wiki/Local_Government_Act_1988\" title=\"Local Government Act 1988\">Local Government Act 1988</a>), outlawing the 'promotion' of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a> in the United Kingdom, is repealed in Scotland with a 99 to 17 vote.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Section_28\" title=\"Section 28\">Section 28</a> (of the <a href=\"https://wikipedia.org/wiki/Local_Government_Act_1988\" title=\"Local Government Act 1988\">Local Government Act 1988</a>), outlawing the 'promotion' of <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">homosexuality</a> in the United Kingdom, is repealed in Scotland with a 99 to 17 vote.", "links": [{"title": "Section 28", "link": "https://wikipedia.org/wiki/Section_28"}, {"title": "Local Government Act 1988", "link": "https://wikipedia.org/wiki/Local_Government_Act_1988"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "2001", "text": "A federal grand jury in Alexandria, Virginia, indicts 13 Saudis and a Lebanese in the 1996 bombing of the Khobar Towers in Saudi Arabia that killed 19 American servicemen.", "html": "2001 - A federal grand jury in <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>, indicts 13 Saudis and a Lebanese in the 1996 bombing of the <a href=\"https://wikipedia.org/wiki/Khobar_Towers_bombing\" title=\"Khobar Towers bombing\">Khobar Towers</a> in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> that killed 19 American servicemen.", "no_year_html": "A federal grand jury in <a href=\"https://wikipedia.org/wiki/Alexandria,_Virginia\" title=\"Alexandria, Virginia\">Alexandria, Virginia</a>, indicts 13 Saudis and a Lebanese in the 1996 bombing of the <a href=\"https://wikipedia.org/wiki/Khobar_Towers_bombing\" title=\"Khobar Towers bombing\">Khobar Towers</a> in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> that killed 19 American servicemen.", "links": [{"title": "Alexandria, Virginia", "link": "https://wikipedia.org/wiki/Alexandria,_Virginia"}, {"title": "Khobar Towers bombing", "link": "https://wikipedia.org/wiki/Khobar_Towers_bombing"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "2004", "text": "SpaceShipOne becomes the first privately funded spaceplane to achieve spaceflight.", "html": "2004 - <i><a href=\"https://wikipedia.org/wiki/SpaceShipOne\" title=\"SpaceShipOne\">SpaceShipOne</a></i> becomes the first privately funded <a href=\"https://wikipedia.org/wiki/Spaceplane\" title=\"Spaceplane\">spaceplane</a> to achieve <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">spaceflight</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/SpaceShipOne\" title=\"SpaceShipOne\">SpaceShipOne</a></i> becomes the first privately funded <a href=\"https://wikipedia.org/wiki/Spaceplane\" title=\"Spaceplane\">spaceplane</a> to achieve <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">spaceflight</a>.", "links": [{"title": "SpaceShipOne", "link": "https://wikipedia.org/wiki/SpaceShipOne"}, {"title": "Spaceplane", "link": "https://wikipedia.org/wiki/Spaceplane"}, {"title": "Human spaceflight", "link": "https://wikipedia.org/wiki/Human_spaceflight"}]}, {"year": "2005", "text": "<PERSON>, who had previously been unsuccessfully tried for the murders of <PERSON>, <PERSON>, and <PERSON>, is convicted of manslaughter 41 years afterwards (the case had been reopened in 2004).", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had previously been unsuccessfully tried for the murders of <PERSON>, <PERSON>, and <PERSON>, is convicted of manslaughter 41 years afterwards (the case had been reopened in 2004).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had previously been unsuccessfully tried for the murders of <PERSON>, <PERSON>, and <PERSON>, is convicted of manslaughter 41 years afterwards (the case had been reopened in 2004).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "Pluto's newly discovered moons are officially named Nix and H<PERSON>ra.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>'s newly discovered moons are officially named <a href=\"https://wikipedia.org/wiki/<PERSON>_(moon)\" title=\"<PERSON> (moon)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Hydra_(moon)\" title=\"Hydra (moon)\">Hydra</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a>'s newly discovered moons are officially named <a href=\"https://wikipedia.org/wiki/<PERSON>_(moon)\" title=\"<PERSON> (moon)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Hydra_(moon)\" title=\"Hydra (moon)\">Hydra</a>.", "links": [{"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "<PERSON> (moon)", "link": "https://wikipedia.org/wiki/<PERSON>_(moon)"}, {"title": "<PERSON><PERSON><PERSON> (moon)", "link": "https://wikipedia.org/wiki/Hydra_(moon)"}]}, {"year": "2006", "text": "A Yeti Airlines de Havilland Canada DHC-6 Twin Otter crashes at Jumla Airport in Nepal, killing nine people.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Yeti_Airlines\" title=\"Yeti Airlines\">Yeti Airlines</a> <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">de Havilland Canada DHC-6 Twin Otter</a> <a href=\"https://wikipedia.org/wiki/2006_Yeti_Airlines_Twin_Otter_crash\" title=\"2006 Yeti Airlines Twin Otter crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Jumla_Airport\" title=\"Jumla Airport\">Jumla Airport</a> in Nepal, killing nine people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Yeti_Airlines\" title=\"Yeti Airlines\">Yeti Airlines</a> <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_DHC-6_Twin_Otter\" title=\"De Havilland Canada DHC-6 Twin Otter\">de Havilland Canada DHC-6 Twin Otter</a> <a href=\"https://wikipedia.org/wiki/2006_Yeti_Airlines_Twin_Otter_crash\" title=\"2006 Yeti Airlines Twin Otter crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Jumla_Airport\" title=\"Jumla Airport\">Jumla Airport</a> in Nepal, killing nine people.", "links": [{"title": "Yeti Airlines", "link": "https://wikipedia.org/wiki/Yeti_Airlines"}, {"title": "De Havilland Canada DHC-6 Twin Otter", "link": "https://wikipedia.org/wiki/<PERSON>_Havilland_Canada_DHC-6_<PERSON>_<PERSON>tter"}, {"title": "2006 Yeti Airlines Twin Otter crash", "link": "https://wikipedia.org/wiki/2006_Yeti_Airlines_Twin_Otter_crash"}, {"title": "Jumla Airport", "link": "https://wikipedia.org/wiki/Jumla_Airport"}]}, {"year": "2009", "text": "Greenland assumes self-rule.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a> assumes <a href=\"https://wikipedia.org/wiki/Self-governance\" title=\"Self-governance\">self-rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a> assumes <a href=\"https://wikipedia.org/wiki/Self-governance\" title=\"Self-governance\">self-rule</a>.", "links": [{"title": "Greenland", "link": "https://wikipedia.org/wiki/Greenland"}, {"title": "Self-governance", "link": "https://wikipedia.org/wiki/Self-governance"}]}, {"year": "2012", "text": "A boat carrying more than 200 migrants capsizes in the Indian Ocean between the Indonesian island of Java and Christmas Island, killing 17 people and leaving 70 others missing.", "html": "2012 - A boat carrying more than 200 migrants <a href=\"https://wikipedia.org/wiki/2012_Indian_Ocean_migrant_boat_disaster\" title=\"2012 Indian Ocean migrant boat disaster\">capsizes</a> in the Indian Ocean between the Indonesian island of Java and Christmas Island, killing 17 people and leaving 70 others missing.", "no_year_html": "A boat carrying more than 200 migrants <a href=\"https://wikipedia.org/wiki/2012_Indian_Ocean_migrant_boat_disaster\" title=\"2012 Indian Ocean migrant boat disaster\">capsizes</a> in the Indian Ocean between the Indonesian island of Java and Christmas Island, killing 17 people and leaving 70 others missing.", "links": [{"title": "2012 Indian Ocean migrant boat disaster", "link": "https://wikipedia.org/wiki/2012_Indian_Ocean_migrant_boat_disaster"}]}, {"year": "2012", "text": "An Indonesian Air Force Fokker F27 Friendship crashes near Halim Perdanakusuma International Airport, killing 11.", "html": "2012 - An <a href=\"https://wikipedia.org/wiki/Indonesian_Air_Force\" title=\"Indonesian Air Force\">Indonesian Air Force</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/2012_Indonesian_Air_Force_Fokker_F27_crash\" title=\"2012 Indonesian Air Force Fokker F27 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Halim_Perdanakusuma_International_Airport\" title=\"Halim Perdanakusuma International Airport\">Halim Perdanakusuma International Airport</a>, killing 11.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Indonesian_Air_Force\" title=\"Indonesian Air Force\">Indonesian Air Force</a> <a href=\"https://wikipedia.org/wiki/Fokker_F27_Friendship\" title=\"Fokker F27 Friendship\">Fokker F27 Friendship</a> <a href=\"https://wikipedia.org/wiki/2012_Indonesian_Air_Force_Fokker_F27_crash\" title=\"2012 Indonesian Air Force Fokker F27 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Halim_Perdanakusuma_International_Airport\" title=\"Halim Perdanakusuma International Airport\">Halim <PERSON>danak<PERSON>uma International Airport</a>, killing 11.", "links": [{"title": "Indonesian Air Force", "link": "https://wikipedia.org/wiki/Indonesian_Air_Force"}, {"title": "Fokker F27 Friendship", "link": "https://wikipedia.org/wiki/Fokker_F27_Friendship"}, {"title": "2012 Indonesian Air Force Fokker F27 crash", "link": "https://wikipedia.org/wiki/2012_Indonesian_Air_Force_Fokker_F27_crash"}, {"title": "<PERSON><PERSON> International Airport", "link": "https://wikipedia.org/wiki/Halim_Perdanakusuma_International_Airport"}]}], "Births": [{"year": "906", "text": "<PERSON><PERSON> ibn <PERSON>, Saffarid emir (d. 963)", "html": "906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Saffarid emir (d. 963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Saffarid emir (d. 963)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1002", "text": "<PERSON> (d. 1054)", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1054)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1054)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1226", "text": "<PERSON><PERSON><PERSON> V the Chaste of Poland (d. 1279)", "html": "1226 - <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste\" title=\"<PERSON><PERSON><PERSON> V the Chaste\"><PERSON><PERSON><PERSON> V the Chaste</a> of Poland (d. 1279)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste\" title=\"<PERSON><PERSON><PERSON> V the Chaste\"><PERSON><PERSON><PERSON> V the Chaste</a> of Poland (d. 1279)", "links": [{"title": "<PERSON><PERSON><PERSON> V the Chaste", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_V_the_Chaste"}]}, {"year": "1521", "text": "<PERSON>, Duke of Schleswig-Holstein-Haderslev (d. 1580)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Haderslev\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Schleswig-Holstein-Haderslev\"><PERSON>, Duke of Schleswig-Holstein-Haderslev</a> (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Haderslev\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Schleswig-Holstein-Haderslev\"><PERSON>, Duke of Schleswig-Holstein-Haderslev</a> (d. 1580)", "links": [{"title": "<PERSON>, Duke of Schleswig-Holstein-Haderslev", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Haderslev"}]}, {"year": "1528", "text": "<PERSON> of Austria, Holy Roman Empress (d. 1603)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/Maria_of_Austria,_Holy_Roman_Empress\" title=\"<PERSON> of Austria, Holy Roman Empress\"><PERSON> of Austria, Holy Roman Empress</a> (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Austria,_Holy_Roman_Empress\" title=\"<PERSON> of Austria, Holy Roman Empress\"><PERSON> of Austria, Holy Roman Empress</a> (d. 1603)", "links": [{"title": "<PERSON> of Austria, Holy Roman Empress", "link": "https://wikipedia.org/wiki/Maria_of_Austria,_Holy_Roman_Empress"}]}, {"year": "1535", "text": "<PERSON><PERSON>, German physician and botanist (d. 1596)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and botanist (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and botanist (d. 1596)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, Italian philosopher and astronomer (d. 1652)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Scipione_Chiaramonti\" title=\"Sc<PERSON>ione Chiaramonti\"><PERSON><PERSON><PERSON> Chiaramonti</a>, Italian philosopher and astronomer (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scip<PERSON>_Chiaramonti\" title=\"Scipione Chiaramonti\"><PERSON><PERSON><PERSON> Chiaramont<PERSON></a>, Italian philosopher and astronomer (d. 1652)", "links": [{"title": "Scipione <PERSON>monti", "link": "https://wikipedia.org/wiki/Scipione_Chiaramonti"}]}, {"year": "1630", "text": "<PERSON>, German Jewish banker and diplomat (d. 1703)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish banker and diplomat (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish banker and diplomat (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1636", "text": "<PERSON><PERSON><PERSON>Auvergne, Duke of Bouillon, French noble (d. 1721)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_La_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Bouillon\" title=\"<PERSON><PERSON><PERSON>Auvergne, Duke of Bouillon\"><PERSON><PERSON><PERSON>'Auvergne, Duke of Bouillon</a>, French noble (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_La_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Bouillon\" title=\"<PERSON><PERSON><PERSON> d'Auvergne, Duke of Bouillon\"><PERSON><PERSON><PERSON>Auvergne, Duke of Bouillon</a>, French noble (d. 1721)", "links": [{"title": "<PERSON><PERSON><PERSON> d'Auvergne, Duke of Bouillon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Tour_d%27A<PERSON><PERSON><PERSON>,_<PERSON>_<PERSON>_Bouillon"}]}, {"year": "1639", "text": "(O.S.) <PERSON><PERSON>, American minister and author (d. 1723)", "html": "1639 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/Increase_Mather\" title=\"Increase Mather\"><PERSON><PERSON> Mather</a>, American minister and author (d. 1723)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/Increase_Mather\" title=\"Increase Mather\"><PERSON><PERSON> Mather</a>, American minister and author (d. 1723)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "Inc<PERSON> Mather", "link": "https://wikipedia.org/wiki/Inc<PERSON>_Mather"}]}, {"year": "1676", "text": "(O.S.) <PERSON>, English philosopher and author (d. 1729)", "html": "1676 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (d. 1729)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and author (d. 1729)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1706", "text": "<PERSON>, English optician and astronomer (d. 1761)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English optician and astronomer (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English optician and astronomer (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Scottish-English mathematician and optician (d. 1768)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish-English mathematician and optician (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish-English mathematician and optician (d. 1768)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)"}]}, {"year": "1712", "text": "<PERSON>, comte <PERSON>, French admiral (d. 1790)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Bou%C3%ABxic,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON>, comte <PERSON></a>, French admiral (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Bou%C3%ABxic,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON>, comte <PERSON></a>, French admiral (d. 1790)", "links": [{"title": "<PERSON>, comte <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>u%C3%ABxic,_comte_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1730", "text": "<PERSON><PERSON><PERSON>, Japanese poet and scholar (d. 1801)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Nor<PERSON>\" title=\"<PERSON><PERSON><PERSON> Nor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet and scholar (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet and scholar (d. 1801)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1732", "text": "<PERSON>, German pianist and composer (d. 1791)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "(O.S.) <PERSON><PERSON>, American general (d. 1780)", "html": "1736 - (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/Enoch_Poor\" title=\"Enoch Poor\"><PERSON><PERSON></a>, American general (d. 1780)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/Enoch_Poor\" title=\"Enoch Poor\"><PERSON><PERSON></a>, American general (d. 1780)", "links": [{"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enoch_Poor"}]}, {"year": "1741", "text": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Chablais (d. 1808)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Chablais\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Chablais\">Prince <PERSON><PERSON><PERSON>, Duke of Chablais</a> (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Chablais\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Chablais\">Prince <PERSON><PERSON><PERSON>, Duke of Chablais</a> (d. 1808)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Chablais", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Cha<PERSON>is"}]}, {"year": "1750", "text": "<PERSON><PERSON><PERSON>, French sculptor and illustrator (d. 1818)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and illustrator (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and illustrator (d. 1818)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1759", "text": "<PERSON>, American lawyer and politician, 6th United States Secretary of the Treasury (d. 1817)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1817)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(statesman)"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1763", "text": "<PERSON><PERSON><PERSON>, French philosopher and academic (d. 1845)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1845)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, English admiral and politician (d. 1840)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral and politician (d. 1840)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1774", "text": "<PERSON>, American lawyer and politician, 6th Vice President of the United States (d. 1825)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1781", "text": "<PERSON><PERSON><PERSON>, French mathematician and physicist (d. 1840)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (d. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sim%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, English opera singer and composer (d. 1849)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English opera singer and composer (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English opera singer and composer (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, German theologian and scholar (d. 1860)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Russian poet and author (d. 1846)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_K%C3%<PERSON><PERSON><PERSON>er"}]}, {"year": "1798", "text": "<PERSON> of Banchory, Scottish jurist, agriculturalist and religious activist (d. 1868)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Banchory\" title=\"<PERSON> of Banchory\"><PERSON> of Banchory</a>, Scottish jurist, agriculturalist and religious activist (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Banchory\" title=\"<PERSON> of Banchory\"><PERSON> of Banchory</a>, Scottish jurist, agriculturalist and religious activist (d. 1868)", "links": [{"title": "<PERSON> of Banchory", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Banchory"}]}, {"year": "1802", "text": "<PERSON>, German theologian (d. 1871)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, German composer and singer (d. 1841)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German composer and singer (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German composer and singer (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, American physician and geologist (d. 1880)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and geologist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and geologist (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, Polish sculptor (d. 1890)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Bryli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish sculptor (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Bryli%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish sculptor (d. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_Bryli%C5%84ski"}]}, {"year": "1814", "text": "<PERSON>, German anatomist and academic (d. 1889)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anatomist and academic (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anatomist and academic (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, French astronomer (d. 1873)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Irish economist and jurist (d. 1882)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish economist and jurist (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish economist and jurist (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, English bishop and historian (d. 1901)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and historian (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and historian (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, French geologist and academic (d. 1904)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9_Fouqu%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9_Fouqu%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Andr%C3%A9_Fouqu%C3%A9"}]}, {"year": "1828", "text": "<PERSON><PERSON>, German Catholic writer and teacher (d. 1907)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Catholic writer and teacher (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Catholic writer and teacher (d. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON><PERSON>, Flemish poet and author (d. 1878)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Fr<PERSON>_de_<PERSON>rt\" title=\"<PERSON><PERSON> de Cort\"><PERSON><PERSON></a>, Flemish poet and author (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr<PERSON>_de_<PERSON>rt\" title=\"<PERSON><PERSON> de Cort\"><PERSON><PERSON></a>, Flemish poet and author (d. 1878)", "links": [{"title": "Frans de Cort", "link": "https://wikipedia.org/wiki/Frans_de_Cort"}]}, {"year": "1836", "text": "<PERSON>, Italian theologian (d. 1906)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian theologian (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian theologian (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian author, poet, and playwright (d. 1908)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian author, poet, and playwright (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian author, poet, and playwright (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Welsh-Australian politician, 9th Premier of Queensland (d. 1920)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Australian politician, 9th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1845", "text": "<PERSON>, English astrophysicist and astronomer (d. 1894)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ra<PERSON>rd\"><PERSON></a>, English astrophysicist and astronomer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Ranyard\"><PERSON></a>, English astrophysicist and astronomer (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rd"}]}, {"year": "1846", "text": "<PERSON>-<PERSON><PERSON>, Scottish-English author and playwright (d. 1928)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English author and playwright (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English author and playwright (d. 1928)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Italian painter (d. 1911)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American author and illustrator, co-founded the Boy Scouts of America (d. 1941)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-founded the <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-founded the <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Boy Scouts of America", "link": "https://wikipedia.org/wiki/Boy_Scouts_of_America"}]}, {"year": "1857", "text": "<PERSON>, American pharmacist, founded <PERSON><PERSON>  (d. 1941)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Dr. Pepper\">Dr<PERSON></a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Dr. Pepper\"><PERSON><PERSON></a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Italian painter (d. 1924)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, Italian sculptor and educator (d. 1928)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Medar<PERSON>_<PERSON>\" title=\"Medar<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and educator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>\" title=\"Medar<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and educator (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medar<PERSON>_<PERSON>o"}]}, {"year": "1859", "text": "<PERSON>, American-French painter and illustrator (d. 1937)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter and illustrator (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter and illustrator (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, British physiologist and biochemist (d. 1931)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ib<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physiologist and biochemist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physiologist and biochemist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Halliburt<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Thai historian and author (d. 1943)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Damrong_Rajanubhab\" title=\"Damrong Rajanubhab\"><PERSON><PERSON></a>, Thai historian and author (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Damrong_Rajanubhab\" title=\"Damrong Rajanubhab\"><PERSON><PERSON></a>, Thai historian and author (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Damrong_<PERSON>b"}]}, {"year": "1863", "text": "<PERSON>, German physicist (d. 1936)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, German physicist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, German physicist (d. 1936)", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>(physicist)"}]}, {"year": "1863", "text": "<PERSON>, German astronomer and academic (d. 1932)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Wolf\"><PERSON></a>, German astronomer and academic (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Swiss historian and critic (d. 1945)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lf<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and critic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lf<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and critic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_W%C3%B6lfflin"}]}, {"year": "1866", "text": "<PERSON>, American baseball player (d. 1940)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, German-American painter and illustrator (d. 1938)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Norwegian historian of religion (d. 1953)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian of religion (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian historian of religion (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, English zoologist and anatomist (d. 1946)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and anatomist (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and anatomist (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Jewish-German chemist and academic (d. 1915)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-German chemist and academic (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jewish-German chemist and academic (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, English-Australian engineer (d. 1959)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian engineer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian engineer (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Mexican painter (d. 1907)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Estonian linguist (d. 1953)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Dutch physicist and academic (d. 1956)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American psychologist and pediatrician (d. 1961)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and pediatrician (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and pediatrician (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, 1st Baron <PERSON>, English economist and civil servant (d. 1941)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English economist and civil servant (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English economist and civil servant (d. 1941)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1881", "text": "(O.S.) <PERSON>, Russian painter, costume designer, and illustrator (d. 1962)", "html": "1881 - (<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter, costume designer, and illustrator (d. 1962)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style\" class=\"mw-redirect\" title=\"Old Style\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter, costume designer, and illustrator (d. 1962)", "links": [{"title": "Old Style", "link": "https://wikipedia.org/wiki/Old_Style"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Israeli photographer and cinematographer (d. 1968)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Ya%27acov_<PERSON>\" title=\"Ya'acov <PERSON>\">Ya'acov <PERSON></a>, Israeli photographer and cinematographer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27acov_<PERSON>\" title=\"Ya'acov <PERSON>\">Ya'acov <PERSON></a>, Israeli photographer and cinematographer (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%27acov_<PERSON>-<PERSON>v"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Spanish lawyer and politician, 123rd President of Catalonia (d. 1940)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\"><PERSON>luís Companys</a>, Spanish lawyer and politician, 123rd <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Llu%C3%ADs_Companys\" title=\"Lluís Companys\">Lluís Companys</a>, Spanish lawyer and politician, 123rd <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (d. 1940)", "links": [{"title": "Lluís Companys", "link": "https://wikipedia.org/wiki/Llu%C3%ADs_Companys"}, {"title": "President of Catalonia", "link": "https://wikipedia.org/wiki/President_of_Catalonia"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Dutch fencer and soldier (d. 1966)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch fencer and soldier (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch fencer and soldier (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, American painter and illustrator (d. 1971)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Rockwell_Kent\" title=\"Rockwell Kent\">Rockwell Kent</a>, American painter and illustrator (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rockwell_Kent\" title=\"Rockwell Kent\">Rockwell Kent</a>, American painter and illustrator (d. 1971)", "links": [{"title": "Rockwell Kent", "link": "https://wikipedia.org/wiki/Rockwell_Kent"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Russian author and educator (d. 1958)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author and educator (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English field marshal (d. 1981)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field marshal (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Canadian geologist and petrologist (d. 1956)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and petrologist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and petrologist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American sprinter and sailor (d. 1972)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and sailor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and sailor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Italian architect and engineer, co-designed the Pirelli Tower and Cathedral of Saint Mary of the Assumption (d. 1979)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Pirelli_Tower\" title=\"Pirelli Tower\">Pirelli Tower</a> and <a href=\"https://wikipedia.org/wiki/Cathedral_of_Saint_Mary_of_the_Assumption_(San_Francisco,_California)\" class=\"mw-redirect\" title=\"Cathedral of Saint Mary of the Assumption (San Francisco, California)\">Cathedral of Saint Mary of the Assumption</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Pirelli_Tower\" title=\"Pirelli Tower\">Pirelli Tower</a> and <a href=\"https://wikipedia.org/wiki/Cathedral_of_Saint_Mary_of_the_Assumption_(San_Francisco,_California)\" class=\"mw-redirect\" title=\"Cathedral of Saint Mary of the Assumption (San Francisco, California)\">Cathedral of Saint Mary of the Assumption</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pirelli Tower", "link": "https://wikipedia.org/wiki/Pirelli_Tower"}, {"title": "Cathedral of Saint Mary of the Assumption (San Francisco, California)", "link": "https://wikipedia.org/wiki/Cathedral_of_Saint_Mary_of_the_Assumption_(San_Francisco,_California)"}]}, {"year": "1891", "text": "<PERSON>, German-Swiss viola player and conductor (d. 1966)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss viola player and conductor (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss viola player and conductor (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, American theologian and academic (d. 1971)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theologian and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theologian and academic (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Czech composer and educator (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Alois_H%C3%A1ba\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and educator (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alois_H%C3%A1ba\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and educator (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alois_H%C3%A1ba"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, English journalist and civil servant (d. 1968)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and civil servant (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and civil servant (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, German mathematician and physicist (d. 1951)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and physicist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and physicist (d. 1951)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1896", "text": "<PERSON>, American admiral, invented the <PERSON><PERSON> lung (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_lung\" title=\"<PERSON><PERSON> lung\"><PERSON><PERSON> lung</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, invented the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_lung\" title=\"<PERSON><PERSON> lung\"><PERSON><PERSON> lung</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> lung", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_lung"}]}, {"year": "1899", "text": "<PERSON>, Czech composer (d. 1944)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, French economist and Islamologist (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and Islamologist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and Islamologist (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German runner and coach (d. 1984)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner and coach (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner and coach (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American caricaturist, painter and illustrator (d. 2003)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American caricaturist, painter and illustrator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American caricaturist, painter and illustrator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French journalist (d. 2000)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, French philosopher and author (d. 1980)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher and author (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, German-American pianist (d. 2005)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sultan\"><PERSON><PERSON><PERSON></a>, German-American pianist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sultan\"><PERSON><PERSON><PERSON></a>, German-American pianist (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American philosopher and academic (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Russian poet and author (d. 1971)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American producer and manager (d. 2012)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish pilot and engineer (d. 2000)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pilot and engineer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pilot and engineer (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American novelist and critic (d. 1989)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and critic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American novelist and critic (d. 1989)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1912", "text": "<PERSON>, Indian author and playwright (d. 2009)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Sri Lankan monk and scholar (d. 2003)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan monk and scholar (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan monk and scholar (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ro"}]}, {"year": "1913", "text": "<PERSON>, Filipino political activist (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino political activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino political activist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Canadian-American economist and academic, Nobel Prize laureate (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1915", "text": "<PERSON>, German soldier and astronomer (d. 1993)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and astronomer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and astronomer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English businessman, founded <PERSON><PERSON> <PERSON><PERSON>  (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"J. C. Ba<PERSON>ford\">J. C. <PERSON></a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"J. C<PERSON>\">J. C<PERSON></a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Chinese photographer (d. 2018)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Tchan_Fou-li\" title=\"Tchan Fou-li\"><PERSON><PERSON>ou-li</a>, Chinese photographer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tchan_Fou-li\" title=\"Tchan Fou-li\"><PERSON><PERSON>ou-li</a>, Chinese photographer (d. 2018)", "links": [{"title": "<PERSON><PERSON>i", "link": "https://wikipedia.org/wiki/T<PERSON>_Fou-li"}]}, {"year": "1916", "text": "<PERSON>, American physicist and astronomer (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian ice hockey player (d. 1977)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Buddy_O%27Connor"}]}, {"year": "1918", "text": "<PERSON>, Canadian engineer (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English historian, author, and academic (d. 1994)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American baseball player, coach, and manager (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, British sociologist and anthropologist (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British sociologist and anthropologist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British sociologist and anthropologist (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American mountaineer (d. 2020)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American economist and banker (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and banker (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and banker (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Hungarian mathematician and academic (d. 1955)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Tib<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"Tib<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mathematician and academic (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tib<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American engineer (d. 2017)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Italian martyr and saint (d. 1935)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian martyr and saint (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian martyr and saint (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton<PERSON>_<PERSON>a"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Canadian journalist and politician (d. 1997)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Russian chess player and coach (d. 1968)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and coach (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and coach (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Italian-American architect, designed the <PERSON><PERSON><PERSON> (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American architect, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Swiss figure skater (d. 2017)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss figure skater (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss figure skater (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress and singer (d. 1965)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress and singer (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor, producer, and production manager (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and production manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and production manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Burkinabé historian, politician and writer (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé historian, politician and writer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Burkinabé historian, politician and writer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian journalist and politician (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian journalist and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian journalist and politician (d. 2007)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Canadian_politician)"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Swedish art collector and historian (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Pont<PERSON>_Hult%C3%A9n\" title=\"Pontus Hultén\"><PERSON><PERSON></a>, Swedish art collector and historian (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pont<PERSON>_Hult%C3%A9n\" title=\"Pontus Hultén\"><PERSON><PERSON></a>, Swedish art collector and historian (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pontus_Hult%C3%A9n"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian actor (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_En<PERSON>zami\" title=\"<PERSON><PERSON><PERSON><PERSON> Entezam<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_En<PERSON>zami\" title=\"<PERSON><PERSON><PERSON><PERSON> Entezam<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian actor (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_En<PERSON>zami"}]}, {"year": "1924", "text": "<PERSON>, British-Canadian jazz clarinetist and satirical cartoonist (d. 2023)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Canadian jazz clarinetist and satirical cartoonist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Canadian jazz clarinetist and satirical cartoonist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French psychoanalyst and academic (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychoanalyst and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychoanalyst and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Russian mezzo-soprano (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mezzo-soprano (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mezzo-soprano (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>a"}]}, {"year": "1925", "text": "<PERSON>, American poet, publisher, and art dealer (d. 2024)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, publisher, and art dealer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stanley Moss\"><PERSON></a>, American poet, publisher, and art dealer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian journalist and politician, 45th Prime Minister of Italy (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American football player (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1926", "text": "<PERSON>, French-American cinematographer (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Conrad_Hall\" title=\"Conrad Hall\"><PERSON></a>, French-American cinematographer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conrad_Hall\" title=\"Conrad Hall\"><PERSON></a>, French-American cinematographer (d. 2003)", "links": [{"title": "Conrad Hall", "link": "https://wikipedia.org/wiki/Conrad_Hall"}]}, {"year": "1927", "text": "<PERSON>, American lawyer, politician, and diplomat, United States Ambassador to Seychelles (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Seychelles\" class=\"mw-redirect\" title=\"United States Ambassador to Seychelles\">United States Ambassador to Seychelles</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Seychelles\" class=\"mw-redirect\" title=\"United States Ambassador to Seychelles\">United States Ambassador to Seychelles</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Seychelles", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Seychelles"}]}, {"year": "1928", "text": "<PERSON>, German-American mathematician and academic (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Brazilian-Italian actress (d. 1983)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Italian actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Italian actress (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Hungarian actress (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Margit_Bara\" title=\"Margit Bara\"><PERSON><PERSON></a>, Hungarian actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Margit_Bara\" title=\"Margit Bara\"><PERSON><PERSON></a>, Hungarian actress (d. 2016)", "links": [{"title": "Margit Bara", "link": "https://wikipedia.org/wiki/Margit_Bara"}]}, {"year": "1930", "text": "<PERSON>, English journalist and politician, Shadow Foreign Secretary (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Foreign_Secretary\" title=\"Shadow Foreign Secretary\">Shadow Foreign Secretary</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Foreign_Secretary\" title=\"Shadow Foreign Secretary\">Shadow Foreign Secretary</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Shadow Foreign Secretary", "link": "https://wikipedia.org/wiki/Shadow_Foreign_Secretary"}]}, {"year": "1930", "text": "<PERSON>, American football player and coach (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Croatian-Canadian animator, director, and screenwriter (d. 1988)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Grgi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Canadian animator, director, and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Grgi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Canadian animator, director, and screenwriter (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zlatko_Grgi%C4%87"}]}, {"year": "1931", "text": "<PERSON>, American journalist, lawyer, and politician, 15th United States Secretary of Health and Human Services (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "1931", "text": "<PERSON>, Israeli Olympic long-jumper (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli Olympic long-jumper (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli Olympic long-jumper (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English journalist and civil servant (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and civil servant (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and civil servant (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Argentinian pianist, composer, and conductor", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian pianist, composer, and conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>rin"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, American R&B/jazz singer (d. 2001)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American R&amp;B/jazz singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American R&amp;B/jazz singer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O.<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actor and comedian", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, French author and playwright (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and playwright (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>n"}]}, {"year": "1937", "text": "<PERSON>, English cricketer and coach (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English songwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, English songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)\" title=\"<PERSON> (lyricist)\"><PERSON></a>, English songwriter", "links": [{"title": "<PERSON> (lyricist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lyricist)"}]}, {"year": "1938", "text": "<PERSON>, American historian and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German mathematician and computer scientist (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and computer scientist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and computer scientist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American actress and television personality", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian philosopher and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Indian Catholic bishop", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Catholic bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27Souza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Catholic bishop", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27Souza"}]}, {"year": "1941", "text": "<PERSON>, American-Canadian actor, producer, and screenwriter (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor, producer, and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor, producer, and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Canadian actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON> of Alverthorpe, English businessman and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Alverthorpe\" title=\"<PERSON>, Baron <PERSON> of Alverthorpe\"><PERSON>, Baron <PERSON> of Alverthorpe</a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Alverthorpe\" title=\"<PERSON>, Baron <PERSON> of Alverthorpe\"><PERSON>, Baron <PERSON> of Alverthorpe</a>, English businessman and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Alverthorpe", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Alverthorpe"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Swiss Catholic bishop", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(bishop)\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, Swiss Catholic bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(bishop)\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, Swiss Catholic bishop", "links": [{"title": "<PERSON><PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(bishop)"}]}, {"year": "1942", "text": "<PERSON>, American journalist and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author and poet", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Italian cyclist (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON>, American soldier, lawyer, and politician, 3rd United States Secretary of Veterans Affairs (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Togo_D._West_Jr.\" title=\"Togo D. West Jr.\">Togo D. West Jr.</a>, American soldier, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Veterans_Affairs\" title=\"United States Secretary of Veterans Affairs\">United States Secretary of Veterans Affairs</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Togo_D._West_Jr.\" title=\"Togo D. West Jr.\">Togo D. West Jr.</a>, American soldier, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Veterans_Affairs\" title=\"United States Secretary of Veterans Affairs\">United States Secretary of Veterans Affairs</a> (d. 2018)", "links": [{"title": "To<PERSON> <PERSON> Jr.", "link": "https://wikipedia.org/wiki/Togo_D._West_Jr."}, {"title": "United States Secretary of Veterans Affairs", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Veterans_Affairs"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Brazilian pianist, composer, and producer[citation needed]", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian pianist, composer, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian accountant and politician, Canadian Minister of Health (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Health_(Canada)\" title=\"Minister of Health (Canada)\">Canadian Minister of Health</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Health_(Canada)\" title=\"Minister of Health (Canada)\">Canadian Minister of Health</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Health (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Health_(Canada)"}]}, {"year": "1943", "text": "<PERSON>, American pole vaulter (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English drummer (d. 2018)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English-American director and producer (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English-American computer scientist and academic (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American computer scientist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American computer scientist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Polish author and poet (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and poet (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and poet (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Swedish race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Northern Irish-British academic and politician, Minister for Sport and the Olympics", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-British academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-British academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Norwegian actor, director, and screenwriter (d. 2007)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Trond_<PERSON>ag\" title=\"Trond Kirk<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actor, director, and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trond_<PERSON>\" title=\"Trond <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian actor, director, and screenwriter (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trond_<PERSON>ag"}]}, {"year": "1946", "text": "<PERSON>, Scottish lawyer and politician, Secretary of State for Scotland", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1946", "text": "<PERSON>, Baron <PERSON>, Iraqi-British businessman, founded M&C Saatchi and Saatchi & Saatchi", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Iraqi-British businessman, founded <a href=\"https://wikipedia.org/wiki/M%26C_Saatchi\" title=\"M&amp;C Saatchi\">M&amp;C Saatchi</a> and <a href=\"https://wikipedia.org/wiki/Saatchi_%26_Sa<PERSON>i\" title=\"Saatchi &amp; Saatchi\">Saatchi &amp; Saatchi</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Iraqi-British businessman, founded <a href=\"https://wikipedia.org/wiki/M%26C_Saatchi\" title=\"M&amp;C Saatchi\">M&amp;C Saatchi</a> and <a href=\"https://wikipedia.org/wiki/Saatchi_%26_Sa<PERSON>i\" title=\"Saatchi &amp; Saatchi\">Saatchi &amp; Saatchi</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}, {"title": "M&C Saatchi", "link": "https://wikipedia.org/wiki/M%26C_Saatchi"}, {"title": "Saatchi & Saatchi", "link": "https://wikipedia.org/wiki/Saatchi_%26_Saatchi"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Iranian lawyer, judge, and activist, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian lawyer, judge, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian lawyer, judge, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2025)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American football coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Spanish philosopher and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish philosopher and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Serbian footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>van_A%C4%87imovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_A%C4%87imovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jovan_A%C4%87imovi%C4%87"}]}, {"year": "1948", "text": "<PERSON>, British novelist and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Polish author and translator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and translator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French composer and conductor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Guyanese-English author, poet, and playwright", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-English author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Lord <PERSON>, Scottish lawyer and judge", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian poet and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American rock drummer and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Scottish actor and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Enn_<PERSON>\" title=\"En<PERSON>\"><PERSON><PERSON></a>, Scottish actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enn_<PERSON>\" title=\"Enn <PERSON>\"><PERSON><PERSON></a>, Scottish actor and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enn_Reitel"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Norwegian guitarist and record producer (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Thue\" title=\"<PERSON><PERSON><PERSON> Thue\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and record producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Thue\" title=\"<PERSON>g<PERSON> Thue\"><PERSON><PERSON><PERSON></a>, Norwegian guitarist and record producer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trygve_Thue"}]}, {"year": "1950", "text": "<PERSON>, Scottish-Australian singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American academic and politician, 80th Governor of Vermont", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 80th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 80th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Vermont", "link": "https://wikipedia.org/wiki/Governor_of_Vermont"}]}, {"year": "1951", "text": "<PERSON>, English lawyer and judge", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Australian anthropologist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian anthropologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian anthropologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "Mona<PERSON><PERSON>, Finnish sprinter (d. 2000)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish sprinter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish sprinter (d. 2000)", "links": [{"title": "Mona<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English singer-songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand cricketer and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English political scientist and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Japanese director and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>uffner\"><PERSON><PERSON><PERSON></a>, American artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, Prime Minister of Pakistan (d. 2007)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1953", "text": "<PERSON>, Jamaican producer and musician (d. 1999)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican producer and musician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pablo\"><PERSON></a>, Jamaican producer and musician (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Icelandic economist, former Governor of Central Bank of Iceland", "html": "1954 - <a href=\"https://wikipedia.org/wiki/M%C3%A1r_Gu%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic economist, former Governor of <a href=\"https://wikipedia.org/wiki/Central_Bank_of_Iceland\" title=\"Central Bank of Iceland\">Central Bank of Iceland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1r_Gu%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic economist, former Governor of <a href=\"https://wikipedia.org/wiki/Central_Bank_of_Iceland\" title=\"Central Bank of Iceland\">Central Bank of Iceland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1r_Gu%C3%B<PERSON><PERSON><PERSON>"}, {"title": "Central Bank of Iceland", "link": "https://wikipedia.org/wiki/Central_Bank_of_Iceland"}]}, {"year": "1954", "text": "<PERSON>, American general and politician, 16th Assistant Secretary, Bureau of Political-Military Affairs", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 16th Assistant Secretary, Bureau of Political-Military Affairs", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 16th Assistant Secretary, Bureau of Political-Military Affairs", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Austrian author and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian software developer and businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian software developer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian software developer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and broadcaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>ed, American author and illustrator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Berkeley_Breathed\" title=\"Berkeley Breathed\"><PERSON> Breathed</a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berkeley_Breathed\" title=\"Berkeley Breathed\"><PERSON> Breathed</a>, American author and illustrator", "links": [{"title": "<PERSON> Breathed", "link": "https://wikipedia.org/wiki/<PERSON>_Breathed"}]}, {"year": "1957", "text": "<PERSON>, Filipino cardinal", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Bolivian journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Russian colonel, pilot, and astronaut", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, pilot, and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English captain and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English captain and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English captain and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Marcella Detroit\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Marcella Detroit\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcella_Detroit"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American politician, 38th Governor of Oregon", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1960", "text": "<PERSON>, Slovenian politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, French singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, German keyboard player and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indonesian businessman and politician, 7th President of Indonesia", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>idodo"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American rock singer-songwriter and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON> Winger\"><PERSON><PERSON></a>, American rock singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Winger\"><PERSON><PERSON></a>, American rock singer-songwriter and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Slovenian actor and singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Iz<PERSON>_M<PERSON>ar\" title=\"<PERSON>z<PERSON> Mlakar\"><PERSON><PERSON><PERSON></a>, Slovenian actor and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iz<PERSON>_<PERSON>\" title=\"<PERSON>z<PERSON> Mlak<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian actor and singer-songwriter", "links": [{"title": "Iztok Mlakar", "link": "https://wikipedia.org/wiki/Iz<PERSON>_M<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese shogi player and theoretician ", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Sh%C5%8Dhe<PERSON>_<PERSON>ka<PERSON>\" title=\"Shōhe<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Shogi\" title=\"Shogi\">shogi</a> player and theoretician ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%8Dhe<PERSON>_<PERSON>ka<PERSON>\" title=\"Shōhe<PERSON>kada\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese <a href=\"https://wikipedia.org/wiki/Shogi\" title=\"Shogi\">shogi</a> player and theoretician ", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dhei_<PERSON><PERSON><PERSON>"}, {"title": "Shogi", "link": "https://wikipedia.org/wiki/Shogi"}]}, {"year": "1962", "text": "<PERSON>, Russian singer-songwriter and guitarist  (d. 1990)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Italian pianist and composer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actor and director", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Ukrainian chess player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Greek director and choreographer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and choreographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Welsh footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English biologist and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Chinese general, pilot, and astronaut", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Australian rugby player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American model and TV journalist, Miss America 1989", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and TV journalist, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1989</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and TV journalist, <a href=\"https://wikipedia.org/wiki/Miss_America\" title=\"Miss America\">Miss America 1989</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America", "link": "https://wikipedia.org/wiki/Miss_America"}]}, {"year": "1967", "text": "<PERSON>, American comedian, actor, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French-American businessman, founded eBay", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American businessman, founded <a href=\"https://wikipedia.org/wiki/EBay\" title=\"EBay\">eBay</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American businessman, founded <a href=\"https://wikipedia.org/wiki/EBay\" title=\"EBay\">eBay</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "EBay", "link": "https://wikipedia.org/wiki/EBay"}]}, {"year": "1967", "text": "<PERSON>, American actress, director, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Thai businesswoman and politician, 28th Prime Minister of Thailand", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Shinawatra\"><PERSON><PERSON></a>, Thai businesswoman and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Shinawatra\"><PERSON><PERSON></a>, Thai businesswoman and politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1968", "text": "<PERSON><PERSON>, English singer-songwriter and DJ", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English singer-songwriter and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English singer-songwriter and DJ", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1970", "text": "<PERSON>, American pianist and composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ty<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>ne_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sprinter and long jumper", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sprinter and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sprinter and long jumper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Northern Irish cricketer and rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish cricketer and rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish cricketer and rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, South African-New Zealand netball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-New Zealand netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-New Zealand netball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Slovak diplomat and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Val%C3%A1%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak diplomat and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Val%C3%A1%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak diplomat and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Val%C3%A1%C5%A1ek"}]}, {"year": "1973", "text": "<PERSON>, American actress and singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English guitarist, vocalist and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist, vocalist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist, vocalist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1974", "text": "<PERSON>, Australian race car driver", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Flavio_Roma\" title=\"Flavio Roma\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flavio_Roma\" title=\"Flavio Roma\"><PERSON>lav<PERSON> Roma</a>, Italian footballer", "links": [{"title": "Flavio Roma", "link": "https://wikipedia.org/wiki/Flavio_Roma"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian television host", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craft\"><PERSON></a>, Australian television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craft\"><PERSON></a>, Australian television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American guitarist and songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Irish boxer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Flemish writer (d. 2013)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish writer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish writer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American golfer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Montenegrin footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>janovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>janovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Montenegrin footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dejan_Ognjanovi%C4%87"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, French rapper", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Rim%27K\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rim%27K\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rim%27K"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Polish chess player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%81<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>y_Rleal\" title=\"Sendy Rleal\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>y_Rleal\" title=\"Sendy Rleal\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sendy_Rleal"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American pole vaulter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American pole vaulter", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1982", "text": "<PERSON>, South Korean baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Prince of Wales, heir apparent to the British throne ", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> to the British throne ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>, <a href=\"https://wikipedia.org/wiki/Heir_apparent\" title=\"Heir apparent\">heir apparent</a> to the British throne ", "links": [{"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales"}, {"title": "Heir apparent", "link": "https://wikipedia.org/wiki/Heir_apparent"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actor and singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American activist and academic", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American musician, singer and songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lana Del Rey\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lana Del Rey\"><PERSON></a>, American singer-songwriter", "links": [{"title": "Lana <PERSON>", "link": "https://wikipedia.org/wiki/Lana_Del_Rey"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ejigu\"><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>yeh<PERSON>_<PERSON>gu"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>-<PERSON>, Australian wheelchair basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/Wheelchair_basketball\" title=\"Wheelchair basketball\">wheelchair basketball</a> player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>-<PERSON>"}, {"title": "Wheelchair basketball", "link": "https://wikipedia.org/wiki/Wheelchair_basketball"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Hidea<PERSON>_Wakui\" title=\"Hideaki Wakui\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hidea<PERSON>_Wakui\" title=\"Hideaki Wakui\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "Hidea<PERSON>", "link": "https://wikipedia.org/wiki/Hideaki_Wakui"}]}, {"year": "1987", "text": "<PERSON>, Mexican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Austrian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pr%C3%B6dl\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B6dl\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sebastian_Pr%C3%B6dl"}]}, {"year": "1987", "text": "<PERSON>, Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American basketball and volleyball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball and volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball and volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American chess player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player", "links": [{"title": "<PERSON> (chess player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(chess_player)"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_Young\" title=\"Thad<PERSON><PERSON> Young\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON><PERSON>_Young\" title=\"Thad<PERSON><PERSON> Young\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Sudanese runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Kaki\"><PERSON><PERSON><PERSON></a>, Sudanese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Kaki\"><PERSON><PERSON><PERSON></a>, Sudanese runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Lithuanian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ri%C4%8Dardas_Berankis\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Berank<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ri%C4%8Dardas_Berankis\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Berank<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian tennis player", "links": [{"title": "Ričard<PERSON>", "link": "https://wikipedia.org/wiki/Ri%C4%8Dardas_<PERSON>rankis"}]}, {"year": "1990", "text": "<PERSON>, Russian chess player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Swiss footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Moubandje\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Moubandje\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Moubandje"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>tveit\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>tveit\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A5vard_Nordtveit"}]}, {"year": "1990", "text": "<PERSON>, Portuguese politician", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Kaku<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Ka<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%ABl_<PERSON><PERSON>ta"}]}, {"year": "1991", "text": "<PERSON>-young, South Korean singer-songwriter, actress, and entertainer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON>-<PERSON></a>, South Korean singer-songwriter, actress, and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON>-<PERSON></a>, South Korean singer-songwriter, actress, and entertainer", "links": [{"title": "<PERSON> (South Korean singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American singer, songwriter, actor, dancer and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">MAX</a>, American singer, songwriter, actor, dancer and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">MAX</a>, American singer, songwriter, actor, dancer and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Egyptian professional footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian professional footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian professional footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Argentine-American esports player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Hungrybox\" title=\"Hungrybox\">Hungrybox</a>, Argentine-American <a href=\"https://wikipedia.org/wiki/Esports\" title=\"Esports\">esports</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungrybox\" title=\"Hungrybox\">Hungrybox</a>, Argentine-American <a href=\"https://wikipedia.org/wiki/Esports\" title=\"Esports\">esports</a> player", "links": [{"title": "Hungrybox", "link": "https://wikipedia.org/wiki/Hungrybox"}, {"title": "Esports", "link": "https://wikipedia.org/wiki/Esports"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Turkish tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ba%C5%9Fak_Erayd%C4%B1n\" title=\"Başak Eraydın\"><PERSON><PERSON><PERSON></a>, Turkish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ba%C5%9Fak_Erayd%C4%B1n\" title=\"<PERSON>şak <PERSON>ydın\"><PERSON><PERSON><PERSON></a>, Turkish tennis player", "links": [{"title": "Başak Eraydın", "link": "https://wikipedia.org/wiki/Ba%C5%9Fak_Erayd%C4%B1n"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON>\"><PERSON> May</a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON>\"><PERSON> May</a>, Australian rugby league player", "links": [{"title": "<PERSON> May", "link": "https://wikipedia.org/wiki/Tyrone_May"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American golfer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Black\" title=\"<PERSON> Black\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, British-American freestyle skier", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American freestyle skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American freestyle skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, New Zealand rugby league player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Russian chess player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American celebrity cat (d. 2019)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Bub\"><PERSON></a>, American celebrity cat (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Bub\"><PERSON></a>, American celebrity cat (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}], "Deaths": [{"year": "532", "text": "Emperor <PERSON><PERSON><PERSON> of Northern Wei, former Northern Wei emperor", "html": "532 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON><PERSON> of Northern Wei</a>, former <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Northern_Wei\" title=\"Emperor <PERSON><PERSON><PERSON> of Northern Wei\">Emperor <PERSON><PERSON><PERSON> of Northern Wei</a>, former <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> emperor", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Northern Wei", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Northern_Wei"}, {"title": "Northern Wei", "link": "https://wikipedia.org/wiki/Northern_Wei"}]}, {"year": "866", "text": "<PERSON><PERSON>, Frankish archbishop", "html": "866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Bourges)\" title=\"<PERSON><PERSON> (archbishop of Bourges)\"><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Bourges)\" title=\"<PERSON><PERSON> (archbishop of Bourges)\"><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a>", "links": [{"title": "<PERSON><PERSON> (archbishop of Bourges)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Bourges)"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}]}, {"year": "868", "text": "<PERSON>, the tenth Imam of Shia Islam (b. 829)", "html": "868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the tenth Imam of Shia Islam (b. 829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the tenth Imam of Shia Islam (b. 829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "870", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Muslim caliph", "html": "870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Muslim <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"Caliph\">caliph</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Muslim <a href=\"https://wikipedia.org/wiki/Caliph\" class=\"mw-redirect\" title=\"Calip<PERSON>\">caliph</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Caliph", "link": "https://wikipedia.org/wiki/Caliph"}]}, {"year": "947", "text": "<PERSON>, official of the Liao Dynasty", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liao_dynasty)\" title=\"<PERSON> (Liao dynasty)\"><PERSON></a>, official of the Liao Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liao_dynasty)\" title=\"<PERSON> (Liao dynasty)\"><PERSON></a>, official of the Liao Dynasty", "links": [{"title": "<PERSON> (Liao dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liao_dynasty)"}]}, {"year": "1040", "text": "<PERSON><PERSON> <PERSON>, Count of Anjou (b. 972)", "html": "1040 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III,_Count_of_Anjou\" title=\"<PERSON><PERSON> <PERSON>, Count of Anjou\"><PERSON><PERSON> <PERSON>, Count of Anjou</a> (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON><PERSON> <PERSON>, Count of Anjou\"><PERSON><PERSON> <PERSON>, Count of Anjou</a> (b. 972)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Count of Anjou", "link": "https://wikipedia.org/wiki/Fu<PERSON>_<PERSON>,_Count_of_Anjou"}]}, {"year": "1171", "text": "<PERSON>, French-English monk (b. 1103)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English monk (b. 1103)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English monk (b. 1103)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1208", "text": "<PERSON> of Swabia (b. 1177)", "html": "1208 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a> (b. 1177)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a> (b. 1177)", "links": [{"title": "Philip of Swabia", "link": "https://wikipedia.org/wiki/Philip_of_Swabia"}]}, {"year": "1305", "text": "<PERSON><PERSON><PERSON> of Bohemia (b. 1271)", "html": "1305 - <a href=\"https://wikipedia.org/wiki/Wenceslaus_II_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> II of Bohemia\"><PERSON><PERSON><PERSON> II of Bohemia</a> (b. 1271)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wenceslaus_II_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> II of Bohemia\"><PERSON><PERSON><PERSON> II of Bohemia</a> (b. 1271)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Bohemia", "link": "https://wikipedia.org/wiki/Wenceslaus_II_of_Bohemia"}]}, {"year": "1359", "text": "<PERSON>, king of Sweden (b. 1339)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (b. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (b. 1339)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1377", "text": "<PERSON> of England (b. 1312)", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (b. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1312)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1421", "text": "<PERSON>, French general (b. 1366)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1366)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1366)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1527", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian historian and author (b. 1469)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and author (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and author (b. 1469)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1529", "text": "<PERSON>, English poet and educator (b. 1460)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and educator (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and educator (b. 1460)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1547", "text": "<PERSON><PERSON>, Italian painter and educator (b. 1485)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Piomb<PERSON>\" title=\"<PERSON><PERSON> Piombo\"><PERSON><PERSON></a>, Italian painter and educator (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Piombo\"><PERSON><PERSON></a>, Italian painter and educator (b. 1485)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON><PERSON>, Italian general (b. 1510)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian general (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian general (b. 1510)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1582", "text": "<PERSON><PERSON>, Japanese warlord (b. 1534)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON></a>, Japanese warlord (b. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oda_Nobunaga\" title=\"Oda Nobunaga\"><PERSON><PERSON></a>, Japanese warlord (b. 1534)", "links": [{"title": "Oda Nobunaga", "link": "https://wikipedia.org/wiki/Oda_<PERSON>ga"}]}, {"year": "1585", "text": "<PERSON>, 8th Earl of Northumberland (b. 1532)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Northumberland\" title=\"<PERSON>, 8th Earl of Northumberland\"><PERSON>, 8th Earl of Northumberland</a> (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Northumberland\" title=\"<PERSON>, 8th Earl of Northumberland\"><PERSON>, 8th Earl of Northumberland</a> (b. 1532)", "links": [{"title": "<PERSON>, 8th Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Earl_of_Northumberland"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON>, Italian saint (b. 1568)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint (b. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON>, French agronomist and physician (b. 1535)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French agronomist and physician (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French agronomist and physician (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, <PERSON> of Guise (b. 1575)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Cardinal_of_Guise\" title=\"<PERSON>, Cardinal of Guise\"><PERSON>, Cardinal of Guise</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Guise\" title=\"<PERSON>, Cardinal of Guise\"><PERSON>, Cardinal of Guise</a> (b. 1575)", "links": [{"title": "<PERSON>, <PERSON> of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Guise"}]}, {"year": "1621", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Czech soldier and composer (b. 1564)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/Kry%C5%A1tof_Harant\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech soldier and composer (b. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kry%C5%A1tof_Harant\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech soldier and composer (b. 1564)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kry%C5%A1tof_Harant"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, German theologian (b. 1551)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian (b. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German theologian (b. 1551)", "links": [{"title": "Sal<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON>, English admiral and explorer (b. 1580)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English admiral and explorer (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, English admiral and explorer (b. 1580)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>(explorer)"}]}, {"year": "1652", "text": "<PERSON><PERSON>, English architect, designed the Queen's House and Wilton House (b. 1573)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jones\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Queen%27s_House\" title=\"Queen's House\">Queen's House</a> and <a href=\"https://wikipedia.org/wiki/Wilton_House\" title=\"Wilton House\">Wilton House</a> (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Queen%27s_House\" title=\"Queen's House\">Queen's House</a> and <a href=\"https://wikipedia.org/wiki/Wilton_House\" title=\"Wilton House\">Wilton House</a> (b. 1573)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Queen's House", "link": "https://wikipedia.org/wiki/Queen%27s_House"}, {"title": "Wilton House", "link": "https://wikipedia.org/wiki/Wilton_House"}]}, {"year": "1661", "text": "<PERSON>, Italian painter (b. 1599)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1599)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON><PERSON><PERSON>, French author, critic, and jurist (b. 1664)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author, critic, and jurist (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author, critic, and jurist (b. 1664)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, 2nd Viscount <PERSON>, English politician, Lord Lieutenant of Ireland (b. 1674)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1674)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1765", "text": "<PERSON><PERSON><PERSON> of Horodenka, Hasidic rabbi", "html": "1765 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_of_Horodenka\" title=\"<PERSON><PERSON><PERSON> of Horodenka\"><PERSON><PERSON><PERSON> of Horodenka</a>, Hasidic rabbi", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Horodenka\" title=\"<PERSON><PERSON><PERSON> of Horodenka\"><PERSON><PERSON><PERSON> of Horodenka</a>, Hasidic rabbi", "links": [{"title": "<PERSON><PERSON><PERSON> of Horodenka", "link": "https://wikipedia.org/wiki/Nach<PERSON>_of_Horodenka"}]}, {"year": "1796", "text": "<PERSON>, American soldier and engineer (b. 1710)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, French playwright and translator (b. 1773)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and translator (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and translator (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Aignan"}]}, {"year": "1865", "text": "<PERSON>, American wife of <PERSON> (b. 1824)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Swedish physicist and astronomer (b. 1814)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and astronomer (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and astronomer (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%85ngstr%C3%B6m"}]}, {"year": "1876", "text": "<PERSON>, Mexican general and politician 8th President of Mexico (b. 1794)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON> Anna\"><PERSON></a>, Mexican general and politician 8th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON> Anna\"><PERSON></a>, Mexican general and politician 8th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_L%C3%B3<PERSON><PERSON>_<PERSON>_Santa_Anna"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, American general (b. 1804)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Theophi<PERSON>\"><PERSON><PERSON><PERSON></a>, American general (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Theophi<PERSON>\"><PERSON>phi<PERSON></a>, American general (b. 1804)", "links": [{"title": "Theophi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>phi<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American businessman and politician, 8th Governor of California (b. 1824)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1824)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>_Stanford"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian composer and educator (b. 1844)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and educator (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer and educator (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Austrian journalist and author, Nobel Prize laureate (b. 1843)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Austrian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Austrian journalist and author, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1843)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1929", "text": "<PERSON>, English sociologist, journalist, and academic (b. 1864)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sociologist, journalist, and academic (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sociologist, journalist, and academic (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American author (b. 1892)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American general, Medal of Honor recipient (b. 1881)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1868)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American astronomer (b. 1867)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, French gymnast (b. 1872)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French gymnast (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French gymnast (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Finnish politician (b. 1877)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ville_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Canadian captain and pilot (b. 1896)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Wop_May\" title=\"Wop May\">Wop May</a>, Canadian captain and pilot (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wop_May\" title=\"Wop May\">Wop May</a>, Canadian captain and pilot (b. 1896)", "links": [{"title": "Wop May", "link": "https://wikipedia.org/wiki/Wop_May"}]}, {"year": "1954", "text": "<PERSON>, Swedish-American engineer, developed the zipper (b. 1880)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer, developed the <a href=\"https://wikipedia.org/wiki/Zipper\" title=\"<PERSON>ipper\">zipper</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer, developed the <a href=\"https://wikipedia.org/wiki/Z<PERSON>per\" title=\"<PERSON>ipper\">zipper</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>per"}]}, {"year": "1957", "text": "<PERSON>, French captain and author (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French captain and author (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French captain and author (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1957", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1874)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1964", "text": "<PERSON>, American civil rights activist (b. 1943)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American civil rights activist (b. 1943)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American civil rights activist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, American civil rights activist (b. 1943)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_(activist)"}]}, {"year": "1964", "text": "<PERSON>, American civil rights activist (b. 1939)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American professor of the history of art (b. 1892)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_historian)\" title=\"<PERSON> (art historian)\"><PERSON></a>, American professor of the history of art (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_historian)\" title=\"<PERSON> (art historian)\"><PERSON></a>, American professor of the history of art (b. 1892)", "links": [{"title": "<PERSON> (art historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(art_historian)"}]}, {"year": "1968", "text": "<PERSON>, South African botanist (b. 1883)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African botanist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African botanist (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American tennis player (b. 1934)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Indonesian engineer and politician, 1st President of Indonesia (b. 1901)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a>, Indonesian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a>, Indonesian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (b. 1901)", "links": [{"title": "Sukar<PERSON>", "link": "https://wikipedia.org/wiki/Sukarno"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English race car driver (b. 1942)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Courage\" title=\"Piers Courage\"><PERSON><PERSON></a>, English race car driver (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Courage\" title=\"Piers Courage\"><PERSON><PERSON></a>, English race car driver (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>s_Courage"}]}, {"year": "1976", "text": "<PERSON>, American librarian (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German conductor and composer (b. 1923)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American illustrator and animator (b. 1909)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and animator (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian-American chef and businessman, founded <PERSON> <PERSON><PERSON><PERSON> (b. 1897)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Hector Boyardee\"><PERSON></a>, Italian-American chef and businessman, founded <a href=\"https://wikipedia.org/wiki/Chef_Boyardee\" title=\"Chef Boyardee\">Chef Boy<PERSON></a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Hector Boyardee\"><PERSON></a>, Italian-American chef and businessman, founded <a href=\"https://wikipedia.org/wiki/Chef_Boyardee\" title=\"Chef Boyardee\">Chef Boy<PERSON></a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chef <PERSON>", "link": "https://wikipedia.org/wiki/Chef_<PERSON><PERSON>ee"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Swedish lieutenant and politician, 25th Prime Minister of Sweden (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tage_E<PERSON>er\" title=\"Tage Erlander\"><PERSON><PERSON></a>, Swedish lieutenant and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tag<PERSON>_E<PERSON>er\" title=\"Tage Erlander\"><PERSON><PERSON></a>, Swedish lieutenant and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (b. 1901)", "links": [{"title": "Tage Erlander", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Lebanese singer-songwriter and producer (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese singer-songwriter and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese singer-songwriter and producer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>i"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American engineer and businessman, founded the Muntz Car Company (b. 1914)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mu<PERSON>\" title=\"Mad<PERSON> Muntz\"><PERSON><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Muntz_Car_Company\" title=\"Muntz Car Company\">Muntz Car Company</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mu<PERSON>\" title=\"Mad<PERSON> Mu<PERSON>\"><PERSON><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Muntz_Car_Company\" title=\"Muntz Car Company\">Muntz Car Company</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}, {"title": "Muntz Car Company", "link": "https://wikipedia.org/wiki/Muntz_Car_Company"}]}, {"year": "1988", "text": "<PERSON>, American football coach (b. 1908)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English journalist and author, co-founded the National Guardian (b. 1904)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author, co-founded the <i><a href=\"https://wikipedia.org/wiki/National_Guardian\" title=\"National Guardian\">National Guardian</a></i> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>frage\"><PERSON><PERSON></a>, English journalist and author, co-founded the <i><a href=\"https://wikipedia.org/wiki/National_Guardian\" title=\"National Guardian\">National Guardian</a></i> (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>frage"}, {"title": "National Guardian", "link": "https://wikipedia.org/wiki/National_Guardian"}]}, {"year": "1990", "text": "<PERSON>, American singer (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American singer (b. 1925)", "links": [{"title": "June <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian rugby league player (b. 1971)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (b. 1971)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1992", "text": "<PERSON>, Australian hobby shop proprietor (b. 1922)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hobby shop proprietor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hobby shop proprietor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Bangladeshi poet, author, and playwright (b. 1956)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi poet, author, and playwright (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi poet, author, and playwright (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Chinese captain and politician, 3rd President of the People's Republic of China (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of the People's Republic of China</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of the People's Republic of China</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ian"}, {"title": "President of the People's Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China"}]}, {"year": "1994", "text": "<PERSON>, American astronomer and astrophysicist (b. 1906)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and astrophysicist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and astrophysicist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese actor, singer, director, and producer (b. 1931)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor, singer, director, and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor, singer, director, and producer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>tar<PERSON>_Katsu"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Mexican trade union leader (b. 1900)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Fidel_Vel%C3%A1zquez_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican trade union leader (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fidel_Vel%C3%A1zquez_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican trade union leader (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fidel_Vel%C3%<PERSON><PERSON><PERSON>_S%C3%A1nchez"}]}, {"year": "1998", "text": "<PERSON>, English historian (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ana<PERSON>sio_Ballestrero\" title=\"Anastasio Ballestrero\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana<PERSON><PERSON>_Ballestrero\" title=\"<PERSON><PERSON>sio Ballestrero\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1913)", "links": [{"title": "Anastasio <PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_Ballestrero"}]}, {"year": "1998", "text": "<PERSON>, American baseball player and manager (b. 1916)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>anis\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>anis\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Campanis"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Japanese drummer (b. 1973)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese drummer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese drummer (b. 1973)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "2000", "text": "<PERSON>, Armenian-American pianist and composer (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-American pianist and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-American pianist and composer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Egyptian actress and singer (b. 1942)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Soad_Ho<PERSON>\" title=\"So<PERSON> Ho<PERSON>\"><PERSON><PERSON></a>, Egyptian actress and singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/So<PERSON>_<PERSON>\" title=\"So<PERSON> Ho<PERSON>\"><PERSON><PERSON></a>, Egyptian actress and singer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Soad_Ho<PERSON>ny"}]}, {"year": "2001", "text": "<PERSON>, American actor and producer (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carroll_O%27Connor"}]}, {"year": "2002", "text": "<PERSON>, Canadian author and playwright (b. 1930)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American soldier and author (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Brazilian engineer and politician, Governor of Rio de Janeiro (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Leonel_Brizola\" title=\"Leonel Brizola\"><PERSON><PERSON></a>, Brazilian engineer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro\" class=\"mw-redirect\" title=\"Governor of Rio de Janeiro\">Governor of Rio de Janeiro</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leonel_Brizola\" title=\"<PERSON><PERSON> Brizola\"><PERSON><PERSON></a>, Brazilian engineer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro\" class=\"mw-redirect\" title=\"Governor of Rio de Janeiro\">Governor of Rio de Janeiro</a> (b. 1922)", "links": [{"title": "Leonel Brizola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Brizola"}, {"title": "Governor of Rio de Janeiro", "link": "https://wikipedia.org/wiki/Governor_of_Rio_de_Janeiro"}]}, {"year": "2004", "text": "<PERSON>, American businesswoman (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Amonette\"><PERSON></a>, American businesswoman (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Amonette\"><PERSON></a>, American businesswoman (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Filipino cardinal (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino cardinal (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1975)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>i"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2007", "text": "<PERSON>, American businessman, founded Bob Evans Restaurants (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)\" title=\"<PERSON> (restaurateur)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants\" title=\"Bob <PERSON> Restaurants\">Bob <PERSON> Restaurants</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)\" title=\"<PERSON> (restaurateur)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants\" title=\"Bob <PERSON> Restaurants\">Bob <PERSON> Restaurants</a> (b. 1918)", "links": [{"title": "<PERSON> (restaurateur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurateur)"}, {"title": "<PERSON> Restaurants", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Restaurants"}]}, {"year": "2008", "text": "<PERSON>, American race car driver (b. 1962)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English author (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian actor and screenwriter (b. 1956)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer, journalist, and author (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/%C4%B0lhan_Sel%C3%A7uk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer, journalist, and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0lhan_Sel%C3%A7uk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer, journalist, and author (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0lhan_Sel%C3%A7uk"}]}, {"year": "2011", "text": "<PERSON>, Canadian author and poet (b. 1927)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American composer and producer (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian economist and diplomat, Indian Ambassador to the United States (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian economist and diplomat, <a href=\"https://wikipedia.org/wiki/Indian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Indian Ambassador to the United States\">Indian Ambassador to the United States</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Indian economist and diplomat, <a href=\"https://wikipedia.org/wiki/Indian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Indian Ambassador to the United States\">Indian Ambassador to the United States</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Indian Ambassador to the United States", "link": "https://wikipedia.org/wiki/Indian_Ambassador_to_the_United_States"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian photographer and journalist (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian photographer and journalist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian photographer and journalist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "2012", "text": "<PERSON>, American economist and author (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actor and screenwriter (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Japanese politician, Japanese Minister of Defense (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defense_(Japan)\" title=\"Minister of Defense (Japan)\">Japanese Minister of Defense</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Minister of Defense (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Defense_(Japan)"}]}, {"year": "2014", "text": "<PERSON>, Austrian-Liechtenstein politician, 7th Prime Minister of Liechtenstein (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Liechtenstein politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Liechtenstein politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Liechtenstein", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein"}]}, {"year": "2014", "text": "<PERSON>, Malaysian lawyer and politician (b. 1959)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian lawyer and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian lawyer and politician (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and sportscaster (b. 1964)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Finnish author and poet (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and poet (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish author and poet (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian actor, playwright, and poet (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, playwright, and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor, playwright, and poet (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German soldier and politician (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American horn player, composer, and conductor (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horn player, composer, and conductor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American horn player, composer, and conductor (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian television host and singer (b. 1941)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian television host and singer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian television host and singer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American columnist and conservative political commentator (b. 1950)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American columnist and conservative political commentator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American columnist and conservative political commentator (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Scottish politician (b. 1929)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish politician (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American essayist and literary critic (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>s\"><PERSON></a>, American essayist and literary critic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frederick_<PERSON>\" title=\"Frederick Crews\"><PERSON></a>, American essayist and literary critic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_Crews"}]}]}}