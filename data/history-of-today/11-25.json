{"date": "November 25", "url": "https://wikipedia.org/wiki/November_25", "data": {"Events": [{"year": "571 BC", "text": "<PERSON><PERSON><PERSON>, king of Rome, celebrates the first of his three triumphs for his victory over the Etruscans.", "html": "571 BC - 571 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lli<PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates the first of his three <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> for his victory over the <a href=\"https://wikipedia.org/wiki/Etruscans\" class=\"mw-redirect\" title=\"Etruscans\">Etruscans</a>.", "no_year_html": "571 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>llius\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates the first of his three <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumphs</a> for his victory over the <a href=\"https://wikipedia.org/wiki/Etruscans\" class=\"mw-redirect\" title=\"Etruscans\">Etruscans</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Etruscans", "link": "https://wikipedia.org/wiki/Etruscans"}]}, {"year": "1034", "text": "<PERSON><PERSON><PERSON>, King of Scots, dies. His grandson, <PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> of Dunkeld, inherits the throne.", "html": "1034 - <a href=\"https://wikipedia.org/wiki/M%C3%A1el_Coluim_II_of_Scotland\" class=\"mw-redirect\" title=\"Máel Coluim II of Scotland\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Scots\" class=\"mw-redirect\" title=\"King of Scots\">King of Scots</a>, dies. His grandson, <a href=\"https://wikipedia.org/wiki/Donnchad_I_of_Scotland\" class=\"mw-redirect\" title=\"Donnchad I of Scotland\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Beth%C3%B3c\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Cr%C3%ADn%C3%A1n_of_Dunkeld\" title=\"<PERSON><PERSON><PERSON><PERSON> of Dunkeld\"><PERSON><PERSON><PERSON><PERSON> of Dunkeld</a>, inherits the throne.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1el_Coluim_II_of_Scotland\" class=\"mw-redirect\" title=\"M<PERSON>el Coluim II of Scotland\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Scots\" class=\"mw-redirect\" title=\"King of Scots\">King of Scots</a>, dies. His grandson, <a href=\"https://wikipedia.org/wiki/Donnchad_I_of_Scotland\" class=\"mw-redirect\" title=\"Donnch<PERSON> I of Scotland\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Beth%C3%B3c\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Cr%C3%ADn%C3%A1n_of_Dunkeld\" title=\"<PERSON><PERSON><PERSON><PERSON> of Dunkeld\"><PERSON><PERSON><PERSON><PERSON> of Dunkeld</a>, inherits the throne.", "links": [{"title": "<PERSON><PERSON><PERSON> of Scotland", "link": "https://wikipedia.org/wiki/M%C3%A1el_Coluim_II_of_Scotland"}, {"title": "King of Scots", "link": "https://wikipedia.org/wiki/King_of_Scots"}, {"title": "<PERSON><PERSON><PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Scotland"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beth%C3%B3c"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Dunkeld", "link": "https://wikipedia.org/wiki/Cr%C3%ADn%C3%A1n_of_Dunkeld"}]}, {"year": "1120", "text": "The White Ship sinks in the English Channel, drowning <PERSON>, son and heir of <PERSON> of England.", "html": "1120 - The <i><a href=\"https://wikipedia.org/wiki/White_Ship\" class=\"mw-redirect\" title=\"White Ship\">White Ship</a></i> sinks in the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>, drowning <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son and heir of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry I of England\"><PERSON> of England</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/White_Ship\" class=\"mw-redirect\" title=\"White Ship\">White Ship</a></i> sinks in the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>, drowning <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son and heir of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry <PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "White Ship", "link": "https://wikipedia.org/wiki/White_Ship"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1177", "text": "<PERSON> IV of Jerusalem and <PERSON><PERSON><PERSON> of Châtillon defeat <PERSON><PERSON><PERSON> at the Battle of Montgisard.", "html": "1177 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Jerusalem\" title=\"<PERSON> IV of Jerusalem\"><PERSON> IV of Jerusalem</a> and <a href=\"https://wikipedia.org/wiki/Ray<PERSON>d_of_Ch%C3%A2tillon\" title=\"<PERSON><PERSON><PERSON> of Châtillon\"><PERSON><PERSON><PERSON> of Châtillon</a> defeat <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladin\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Montgisard\" title=\"Battle of Montgisard\">Battle of Montgisard</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Jerusalem\" title=\"<PERSON> IV of Jerusalem\"><PERSON> IV of Jerusalem</a> and <a href=\"https://wikipedia.org/wiki/Ray<PERSON>d_of_Ch%C3%A2tillon\" title=\"<PERSON><PERSON><PERSON> of Châtillon\"><PERSON><PERSON><PERSON> of Châtillon</a> defeat <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladi<PERSON>\"><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Montgisard\" title=\"Battle of Montgisard\">Battle of Montgisard</a>.", "links": [{"title": "<PERSON> IV of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_IV_of_Jerusalem"}, {"title": "<PERSON><PERSON><PERSON> of Châtillon", "link": "https://wikipedia.org/wiki/Raynald_of_Ch%C3%A2tillon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "Battle of Montgisard", "link": "https://wikipedia.org/wiki/Battle_of_Montgisard"}]}, {"year": "1343", "text": "A tsunami, caused by an earthquake in the Tyrrhenian Sea, devastates Naples and the Maritime Republic of Amalfi, among other places.", "html": "1343 - A <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>, caused by an <a href=\"https://wikipedia.org/wiki/Earthquake_of_1343\" class=\"mw-redirect\" title=\"Earthquake of 1343\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a>, devastates <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a> and the <a href=\"https://wikipedia.org/wiki/Amalfi\" title=\"Amalfi\">Maritime Republic of Amalfi</a>, among other places.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a>, caused by an <a href=\"https://wikipedia.org/wiki/Earthquake_of_1343\" class=\"mw-redirect\" title=\"Earthquake of 1343\">earthquake</a> in the <a href=\"https://wikipedia.org/wiki/Tyrrhenian_Sea\" title=\"Tyrrhenian Sea\">Tyrrhenian Sea</a>, devastates <a href=\"https://wikipedia.org/wiki/Naples\" title=\"Naples\">Naples</a> and the <a href=\"https://wikipedia.org/wiki/Amalfi\" title=\"Amalfi\">Maritime Republic of Amalfi</a>, among other places.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "Earthquake of 1343", "link": "https://wikipedia.org/wiki/Earthquake_of_1343"}, {"title": "Tyrrhenian Sea", "link": "https://wikipedia.org/wiki/Tyrrhenian_Sea"}, {"title": "Naples", "link": "https://wikipedia.org/wiki/Naples"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amalfi"}]}, {"year": "1400", "text": "King <PERSON><PERSON><PERSON> I becomes king of Ava.", "html": "1400 - King <a href=\"https://wikipedia.org/wiki/Minkhaung_I\" title=\"Minkhaung I\">Minkha<PERSON> I</a> becomes king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ava\" title=\"Kingdom of Ava\">Ava</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Minkhaung_I\" title=\"Minkhaung I\">Minkha<PERSON> I</a> becomes king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ava\" title=\"Kingdom of Ava\">Ava</a>.", "links": [{"title": "Minkhaung I", "link": "https://wikipedia.org/wiki/Min<PERSON><PERSON>_I"}, {"title": "Kingdom of Ava", "link": "https://wikipedia.org/wiki/Kingdom_of_Ava"}]}, {"year": "1487", "text": "<PERSON> York is crowned Queen of England.", "html": "1487 - <a href=\"https://wikipedia.org/wiki/Elizabeth_<PERSON>_York\" title=\"<PERSON> of York\"><PERSON> of York</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_consorts\" class=\"mw-redirect\" title=\"List of English consorts\">Queen of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_<PERSON>_York\" title=\"<PERSON> of York\"><PERSON> of York</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_consorts\" class=\"mw-redirect\" title=\"List of English consorts\">Queen of England</a>.", "links": [{"title": "<PERSON> of York", "link": "https://wikipedia.org/wiki/Elizabeth_of_York"}, {"title": "List of English consorts", "link": "https://wikipedia.org/wiki/List_of_English_consorts"}]}, {"year": "1491", "text": "The siege of Granada, the last Moorish stronghold in Spain, ends with the Treaty of Granada.", "html": "1491 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Granada\" class=\"mw-redirect\" title=\"Siege of Granada\">siege of Granada</a>, the last <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> stronghold in Spain, ends with the <a href=\"https://wikipedia.org/wiki/Treaty_of_Granada\" class=\"mw-redirect\" title=\"Treaty of Granada\">Treaty of Granada</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Granada\" class=\"mw-redirect\" title=\"Siege of Granada\">siege of Granada</a>, the last <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> stronghold in Spain, ends with the <a href=\"https://wikipedia.org/wiki/Treaty_of_Granada\" class=\"mw-redirect\" title=\"Treaty of Granada\">Treaty of Granada</a>.", "links": [{"title": "Siege of Granada", "link": "https://wikipedia.org/wiki/Siege_of_Granada"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}, {"title": "Treaty of Granada", "link": "https://wikipedia.org/wiki/Treaty_of_Granada"}]}, {"year": "1510", "text": "Portuguese conquest of Goa: Portuguese naval forces under the command of <PERSON><PERSON><PERSON>, and local mercenaries working for privateer <PERSON><PERSON><PERSON>, seize Goa from the Bijapur Sultanate, resulting in 451 years of Portuguese colonial rule.", "html": "1510 - <a href=\"https://wikipedia.org/wiki/Portuguese_conquest_of_Goa\" title=\"Portuguese conquest of Goa\">Portuguese conquest of Goa</a>: Portuguese naval forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and local mercenaries working for privateer <a href=\"https://wikipedia.org/wiki/Timoji\" title=\"Timoji\">Tim<PERSON>ji</a>, seize Goa from the <a href=\"https://wikipedia.org/wiki/Adil_Shahi_dynasty\" class=\"mw-redirect\" title=\"Adil Shahi dynasty\">Bijapur Sultanate</a>, resulting in 451 years of Portuguese colonial rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portuguese_conquest_of_Goa\" title=\"Portuguese conquest of Goa\">Portuguese conquest of Goa</a>: Portuguese naval forces under the command of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, and local mercenaries working for privateer <a href=\"https://wikipedia.org/wiki/Timoji\" title=\"Timoji\">Timoji</a>, seize Goa from the <a href=\"https://wikipedia.org/wiki/Adil_Shahi_dynasty\" class=\"mw-redirect\" title=\"Adil Shahi dynasty\">Bijapur Sultanate</a>, resulting in 451 years of Portuguese colonial rule.", "links": [{"title": "Portuguese conquest of Goa", "link": "https://wikipedia.org/wiki/Portuguese_conquest_of_Goa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ji"}, {"title": "<PERSON>il <PERSON> dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Shahi_dynasty"}]}, {"year": "1596", "text": "The Cudgel War begins in Finland (at the time part of Sweden), when peasants rebel against the imposition of taxes by the nobility.", "html": "1596 - The <a href=\"https://wikipedia.org/wiki/Cudgel_War\" title=\"Cudgel War\">Cudgel War</a> begins in Finland (at the time part of Sweden), when <a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasants</a> rebel against the imposition of taxes by the nobility.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cudgel_War\" title=\"Cudgel War\">Cudgel War</a> begins in Finland (at the time part of Sweden), when <a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasants</a> rebel against the imposition of taxes by the nobility.", "links": [{"title": "Cudgel War", "link": "https://wikipedia.org/wiki/Cudgel_War"}, {"title": "Peasant", "link": "https://wikipedia.org/wiki/Peasant"}]}, {"year": "1667", "text": "A deadly earthquake rocks Shemakha in the Caucasus, killing 80,000 people.", "html": "1667 - A deadly <a href=\"https://wikipedia.org/wiki/1667_Shamakhi_earthquake\" title=\"1667 Shamakhi earthquake\">earthquake</a> rocks <a href=\"https://wikipedia.org/wiki/Shemakha\" class=\"mw-redirect\" title=\"Shemakha\">Shemakha</a> in the <a href=\"https://wikipedia.org/wiki/Caucasus\" title=\"Caucasus\">Caucasus</a>, killing 80,000 people.", "no_year_html": "A deadly <a href=\"https://wikipedia.org/wiki/1667_Shamakhi_earthquake\" title=\"1667 Shamakhi earthquake\">earthquake</a> rocks <a href=\"https://wikipedia.org/wiki/Shemakha\" class=\"mw-redirect\" title=\"Shemakha\">Shemakha</a> in the <a href=\"https://wikipedia.org/wiki/Caucasus\" title=\"Caucasus\">Caucasus</a>, killing 80,000 people.", "links": [{"title": "1667 Shamakhi earthquake", "link": "https://wikipedia.org/wiki/1667_Shamakhi_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Caucasus", "link": "https://wikipedia.org/wiki/Caucasus"}]}, {"year": "1678", "text": "Trunajaya rebellion: After a long and logistically challenging march, the allied Mataram and Dutch troops successfully assaulted the rebel stronghold of Kediri.", "html": "1678 - <a href=\"https://wikipedia.org/wiki/Trunajaya_rebellion\" title=\"Trunajaya rebellion\">Trunajaya rebellion</a>: After a long and logistically challenging march, the allied <a href=\"https://wikipedia.org/wiki/Mataram_Sultanate\" title=\"Mataram Sultanate\">Mataram</a> and <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch</a> troops <a href=\"https://wikipedia.org/wiki/Kediri_campaign_(1678)\" class=\"mw-redirect\" title=\"Kediri campaign (1678)\">successfully assaulted</a> the rebel stronghold of Kediri.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trunajaya_rebellion\" title=\"Trunajaya rebellion\">Trunajaya rebellion</a>: After a long and logistically challenging march, the allied <a href=\"https://wikipedia.org/wiki/Mataram_Sultanate\" title=\"Mataram Sultanate\">Mataram</a> and <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch</a> troops <a href=\"https://wikipedia.org/wiki/Kediri_campaign_(1678)\" class=\"mw-redirect\" title=\"Kediri campaign (1678)\">successfully assaulted</a> the rebel stronghold of Kediri.", "links": [{"title": "Trun<PERSON><PERSON> rebellion", "link": "https://wikipedia.org/wiki/Trun<PERSON><PERSON>_rebellion"}, {"title": "Mataram Sultanate", "link": "https://wikipedia.org/wiki/Mataram_Sultanate"}, {"title": "Dutch East India Company", "link": "https://wikipedia.org/wiki/Dutch_East_India_Company"}, {"title": "Kediri campaign (1678)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_campaign_(1678)"}]}, {"year": "1755", "text": "King <PERSON> of Spain grants royal protection to the Beaterio de la Compañia de Jesus, now known as the Congregation of the Religious of the Virgin Mary.", "html": "1755 - King <a href=\"https://wikipedia.org/wiki/Ferdinand_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> grants royal protection to the Beaterio de la Compañia de Jesus, now known as the Congregation of the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> grants royal protection to the Beaterio de la Compañia de Jesus, now known as the Congregation of the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a>.", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Ferdinand_VI_of_Spain"}, {"title": "Religious of the Virgin Mary", "link": "https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary"}]}, {"year": "1758", "text": "French and Indian War: British forces capture Fort Duquesne from French control. Later, Fort Pitt will be built nearby and grow into modern Pittsburgh.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: British forces capture <a href=\"https://wikipedia.org/wiki/Fort_Duquesne\" title=\"Fort Duquesne\">Fort Duquesne</a> from French control. Later, <a href=\"https://wikipedia.org/wiki/Fort_Pitt_(Pennsylvania)\" title=\"Fort Pitt (Pennsylvania)\">Fort Pitt</a> will be built nearby and grow into modern <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>: British forces capture <a href=\"https://wikipedia.org/wiki/Fort_Duquesne\" title=\"Fort Duquesne\">Fort Duquesne</a> from French control. Later, <a href=\"https://wikipedia.org/wiki/Fort_Pitt_(Pennsylvania)\" title=\"Fort Pitt (Pennsylvania)\">Fort Pitt</a> will be built nearby and grow into modern <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "links": [{"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}, {"title": "Fort Duquesne", "link": "https://wikipedia.org/wiki/Fort_Duquesne"}, {"title": "Fort Pitt (Pennsylvania)", "link": "https://wikipedia.org/wiki/Fort_Pitt_(Pennsylvania)"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1759", "text": "An earthquake hits the Mediterranean destroying Beirut and Damascus and killing 30,000-40,000.", "html": "1759 - An <a href=\"https://wikipedia.org/wiki/Near_East_earthquakes_of_1759\" class=\"mw-redirect\" title=\"Near East earthquakes of 1759\">earthquake</a> hits the Mediterranean destroying <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> and <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a> and killing 30,000-40,000.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Near_East_earthquakes_of_1759\" class=\"mw-redirect\" title=\"Near East earthquakes of 1759\">earthquake</a> hits the Mediterranean destroying <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> and <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a> and killing 30,000-40,000.", "links": [{"title": "Near East earthquakes of 1759", "link": "https://wikipedia.org/wiki/Near_East_earthquakes_of_1759"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Damascus", "link": "https://wikipedia.org/wiki/Damascus"}]}, {"year": "1783", "text": "American Revolutionary War: The last British troops leave New York City three months after the signing of the Treaty of Paris.", "html": "1783 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The last British troops <a href=\"https://wikipedia.org/wiki/Evacuation_Day_(New_York)\" title=\"Evacuation Day (New York)\">leave New York City</a> three months after the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The last British troops <a href=\"https://wikipedia.org/wiki/Evacuation_Day_(New_York)\" title=\"Evacuation Day (New York)\">leave New York City</a> three months after the signing of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Paris_(1783)\" title=\"Treaty of Paris (1783)\">Treaty of Paris</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Evacuation Day (New York)", "link": "https://wikipedia.org/wiki/Evacuation_Day_(New_York)"}, {"title": "Treaty of Paris (1783)", "link": "https://wikipedia.org/wiki/Treaty_of_Paris_(1783)"}]}, {"year": "1795", "text": "Partitions of Poland: <PERSON><PERSON>, the last king of independent Poland, is forced to abdicate and is exiled to Russia.", "html": "1795 - <a href=\"https://wikipedia.org/wiki/Partitions_of_Poland\" title=\"Partitions of Poland\">Partitions of Poland</a>: <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the last king of independent Poland, is forced to <a href=\"https://wikipedia.org/wiki/Abdication\" title=\"Abdication\">abdicate</a> and is exiled to Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Partitions_of_Poland\" title=\"Partitions of Poland\">Partitions of Poland</a>: <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, the last king of independent Poland, is forced to <a href=\"https://wikipedia.org/wiki/Abdication\" title=\"Abdication\">abdicate</a> and is exiled to Russia.", "links": [{"title": "Partitions of Poland", "link": "https://wikipedia.org/wiki/Partitions_of_Poland"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_August_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Abdication", "link": "https://wikipedia.org/wiki/Abdication"}]}, {"year": "1826", "text": "The Greek frigate Hell<PERSON> arrives in Nafplion to become the first flagship of the Hellenic Navy.", "html": "1826 - The <a href=\"https://wikipedia.org/wiki/Greek_frigate_Hellas\" title=\"Greek frigate <PERSON><PERSON>\">Greek frigate <i>Hell<PERSON></i></a> arrives in <a href=\"https://wikipedia.org/wiki/Nafplion\" class=\"mw-redirect\" title=\"Nafplion\">Nafp<PERSON></a> to become the first <a href=\"https://wikipedia.org/wiki/Flagship\" title=\"Flagship\">flagship</a> of the <a href=\"https://wikipedia.org/wiki/Hellenic_Navy\" title=\"Hellenic Navy\">Hellenic Navy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greek_frigate_Hellas\" title=\"Greek frigate <PERSON><PERSON>\">Greek frigate <i>Hell<PERSON></i></a> arrives in <a href=\"https://wikipedia.org/wiki/Nafplion\" class=\"mw-redirect\" title=\"Nafplion\">Nafplion</a> to become the first <a href=\"https://wikipedia.org/wiki/Flagship\" title=\"Flagship\">flagship</a> of the <a href=\"https://wikipedia.org/wiki/Hellenic_Navy\" title=\"Hellenic Navy\">Hellenic Navy</a>.", "links": [{"title": "Greek frigate Hellas", "link": "https://wikipedia.org/wiki/Greek_frigate_Hellas"}, {"title": "Nafplion", "link": "https://wikipedia.org/wiki/Nafplion"}, {"title": "Flagship", "link": "https://wikipedia.org/wiki/Flagship"}, {"title": "Hellenic Navy", "link": "https://wikipedia.org/wiki/Hellenic_Navy"}]}, {"year": "1833", "text": "A massive undersea earthquake, estimated magnitude between 8.7 and 9.2, rocks Sumatra, producing a massive tsunami all along the Indonesian coast.", "html": "1833 - A massive undersea <a href=\"https://wikipedia.org/wiki/1833_Sumatra_earthquake\" title=\"1833 Sumatra earthquake\">earthquake</a>, estimated magnitude between 8.7 and 9.2, rocks <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, producing a massive <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> all along the Indonesian coast.", "no_year_html": "A massive undersea <a href=\"https://wikipedia.org/wiki/1833_Sumatra_earthquake\" title=\"1833 Sumatra earthquake\">earthquake</a>, estimated magnitude between 8.7 and 9.2, rocks <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, producing a massive <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> all along the Indonesian coast.", "links": [{"title": "1833 Sumatra earthquake", "link": "https://wikipedia.org/wiki/1833_Sumatra_earthquake"}, {"title": "Sumatra", "link": "https://wikipedia.org/wiki/Sumatra"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1839", "text": "A cyclone slams into south-eastern India.  An estimated 300,000 deaths resulted from the disaster.", "html": "1839 - A <a href=\"https://wikipedia.org/wiki/1839_Coringa_cyclone\" title=\"1839 Coringa cyclone\">cyclone</a> slams into south-eastern India. An estimated 300,000 deaths resulted from the disaster.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1839_Coringa_cyclone\" title=\"1839 Coringa cyclone\">cyclone</a> slams into south-eastern India. An estimated 300,000 deaths resulted from the disaster.", "links": [{"title": "1839 Coringa cyclone", "link": "https://wikipedia.org/wiki/1839_Coringa_cyclone"}]}, {"year": "1863", "text": "American Civil War: Battle of Missionary Ridge: Union forces led by General <PERSON> break the Siege of Chattanooga by routing Confederate troops under General <PERSON><PERSON><PERSON> at Missionary Ridge in Tennessee.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Missionary_Ridge\" title=\"Battle of Missionary Ridge\">Battle of Missionary Ridge</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces led by General <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\"><PERSON></a> break the <a href=\"https://wikipedia.org/wiki/Siege_of_Chattanooga\" class=\"mw-redirect\" title=\"Siege of Chattanooga\">Siege of Chattanooga</a> by routing <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under General <a href=\"https://wikipedia.org/wiki/Braxton_Bragg\" title=\"Braxton Bragg\"><PERSON><PERSON><PERSON> Bragg</a> at <a href=\"https://wikipedia.org/wiki/Missionary_Ridge\" title=\"Missionary Ridge\">Missionary Ridge</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Missionary_Ridge\" title=\"Battle of Missionary Ridge\">Battle of Missionary Ridge</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces led by General <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\"><PERSON></a> break the <a href=\"https://wikipedia.org/wiki/Siege_of_Chattanooga\" class=\"mw-redirect\" title=\"Siege of Chattanooga\">Siege of Chattanooga</a> by routing <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops under General <a href=\"https://wikipedia.org/wiki/Braxton_Bragg\" title=\"Braxton Bragg\"><PERSON><PERSON><PERSON> Bragg</a> at <a href=\"https://wikipedia.org/wiki/Missionary_Ridge\" title=\"Missionary Ridge\">Missionary Ridge</a> in <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Missionary Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Missionary_Ridge"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Siege of Chattanooga", "link": "https://wikipedia.org/wiki/Siege_of_Chattanooga"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Braxton Bragg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Missionary Ridge", "link": "https://wikipedia.org/wiki/Missionary_Ridge"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1864", "text": "American Civil War: A group of Confederate operatives calling themselves the Confederate Army of Manhattan starts fires in more than 20 locations in an unsuccessful attempt to burn down New York City.", "html": "1864 - American Civil War: A group of Confederate operatives calling themselves the <a href=\"https://wikipedia.org/wiki/Confederate_Army_of_Manhattan\" title=\"Confederate Army of Manhattan\">Confederate Army of Manhattan</a> starts fires in more than 20 locations in an unsuccessful attempt to burn down New York City.", "no_year_html": "American Civil War: A group of Confederate operatives calling themselves the <a href=\"https://wikipedia.org/wiki/Confederate_Army_of_Manhattan\" title=\"Confederate Army of Manhattan\">Confederate Army of Manhattan</a> starts fires in more than 20 locations in an unsuccessful attempt to burn down New York City.", "links": [{"title": "Confederate Army of Manhattan", "link": "https://wikipedia.org/wiki/Confederate_Army_of_Manhattan"}]}, {"year": "1874", "text": "The United States Greenback Party is established as a political party consisting primarily of farmers affected by the Panic of 1873.", "html": "1874 - The United States <a href=\"https://wikipedia.org/wiki/Greenback_Party\" title=\"Greenback Party\">Greenback Party</a> is established as a <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a> consisting primarily of farmers affected by the <a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\">Panic of 1873</a>.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Greenback_Party\" title=\"Greenback Party\">Greenback Party</a> is established as a <a href=\"https://wikipedia.org/wiki/Political_party\" title=\"Political party\">political party</a> consisting primarily of farmers affected by the <a href=\"https://wikipedia.org/wiki/Panic_of_1873\" title=\"Panic of 1873\">Panic of 1873</a>.", "links": [{"title": "Greenback Party", "link": "https://wikipedia.org/wiki/Greenback_Party"}, {"title": "Political party", "link": "https://wikipedia.org/wiki/Political_party"}, {"title": "Panic of 1873", "link": "https://wikipedia.org/wiki/Panic_of_1873"}]}, {"year": "1876", "text": "American Indian Wars: In retaliation for the American defeat at the Battle of the Little Bighorn, United States Army troops sack the sleeping village of Cheyenne Chief <PERSON> at the headwaters of the Powder River.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In retaliation for the American defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn\" title=\"Battle of the Little Bighorn\">Battle of the Little Bighorn</a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops <a href=\"https://wikipedia.org/wiki/Dull_Knife_Fight\" title=\"Dull Knife Fight\">sack the sleeping village</a> of <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON><PERSON></a> at the headwaters of the <a href=\"https://wikipedia.org/wiki/Powder_River_(Montana)\" class=\"mw-redirect\" title=\"Powder River (Montana)\">Powder River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: In retaliation for the American defeat at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn\" title=\"Battle of the Little Bighorn\">Battle of the Little Bighorn</a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> troops <a href=\"https://wikipedia.org/wiki/Dull_Knife_Fight\" title=\"Dull Knife Fight\">sack the sleeping village</a> of <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON><PERSON></a> at the headwaters of the <a href=\"https://wikipedia.org/wiki/Powder_River_(Montana)\" class=\"mw-redirect\" title=\"Powder River (Montana)\">Powder River</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Battle of the Little Bighorn", "link": "https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Dull Knife Fight", "link": "https://wikipedia.org/wiki/Du<PERSON>_K<PERSON>_Fight"}, {"title": "Cheyenne", "link": "https://wikipedia.org/wiki/Cheyenne"}, {"title": "Chief <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON><PERSON>_<PERSON>"}, {"title": "Powder River (Montana)", "link": "https://wikipedia.org/wiki/Powder_River_(Montana)"}]}, {"year": "1905", "text": "Prince <PERSON> of Denmark arrives in Norway to become King <PERSON><PERSON><PERSON> of Norway.", "html": "1905 - Prince <PERSON> of Denmark arrives in Norway to become <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">King <PERSON><PERSON><PERSON> VII of Norway</a>.", "no_year_html": "Prince <PERSON> of Denmark arrives in Norway to become <a href=\"https://wikipedia.org/wiki/Haakon_VII_of_Norway\" class=\"mw-redirect\" title=\"Haakon VII of Norway\">King <PERSON><PERSON><PERSON> VII of Norway</a>.", "links": [{"title": "Haakon VII of Norway", "link": "https://wikipedia.org/wiki/Haakon_VII_of_Norway"}]}, {"year": "1908", "text": "A fire breaks out on SS Sardinia as it leaves Malta's Grand Harbour, resulting in the ship's grounding and the deaths of at least 118 people.", "html": "1908 - A fire breaks out on <a href=\"https://wikipedia.org/wiki/SS_Sardinia_(1888)\" title=\"SS Sardinia (1888)\">SS <i>Sardinia</i></a> as it leaves Malta's <a href=\"https://wikipedia.org/wiki/Grand_Harbour\" title=\"Grand Harbour\">Grand Harbour</a>, resulting in the ship's grounding and the deaths of at least 118 people.", "no_year_html": "A fire breaks out on <a href=\"https://wikipedia.org/wiki/SS_Sardinia_(1888)\" title=\"SS Sardinia (1888)\">SS <i>Sardinia</i></a> as it leaves Malta's <a href=\"https://wikipedia.org/wiki/Grand_Harbour\" title=\"Grand Harbour\">Grand Harbour</a>, resulting in the ship's grounding and the deaths of at least 118 people.", "links": [{"title": "SS Sardinia (1888)", "link": "https://wikipedia.org/wiki/SS_Sardinia_(1888)"}, {"title": "Grand Harbour", "link": "https://wikipedia.org/wiki/Grand_Harbour"}]}, {"year": "1912", "text": "Românul de la Pind, the longest-running newspaper by and about Aromanians until World War II, ceases its publications.", "html": "1912 - <i><a href=\"https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind\" title=\"Românul de la Pind\">Românul de la Pind</a></i>, the longest-running newspaper by and about <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> until <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, ceases its publications.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind\" title=\"Românul de la Pind\">Românul de la Pind</a></i>, the longest-running newspaper by and about <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> until <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, ceases its publications.", "links": [{"title": "Românul de la Pind", "link": "https://wikipedia.org/wiki/Rom%C3%A2nul_de_la_Pind"}, {"title": "Aromanians", "link": "https://wikipedia.org/wiki/Aromanians"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1915", "text": "<PERSON> presents the field equations of general relativity to the Prussian Academy of Sciences.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Albert Einstein\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Einstein_field_equations\" title=\"Einstein field equations\">field equations</a> of <a href=\"https://wikipedia.org/wiki/Introduction_to_general_relativity\" title=\"Introduction to general relativity\">general relativity</a> to the <a href=\"https://wikipedia.org/wiki/Prussian_Academy_of_Sciences\" title=\"Prussian Academy of Sciences\">Prussian Academy of Sciences</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert Einstein\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Einstein_field_equations\" title=\"Einstein field equations\">field equations</a> of <a href=\"https://wikipedia.org/wiki/Introduction_to_general_relativity\" title=\"Introduction to general relativity\">general relativity</a> to the <a href=\"https://wikipedia.org/wiki/Prussian_Academy_of_Sciences\" title=\"Prussian Academy of Sciences\">Prussian Academy of Sciences</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Einstein field equations", "link": "https://wikipedia.org/wiki/Einstein_field_equations"}, {"title": "Introduction to general relativity", "link": "https://wikipedia.org/wiki/Introduction_to_general_relativity"}, {"title": "Prussian Academy of Sciences", "link": "https://wikipedia.org/wiki/Prussian_Academy_of_Sciences"}]}, {"year": "1917", "text": "World War I: German forces defeat Portuguese army of about 1,200 at Negomano on the border of modern-day Mozambique and Tanzania.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ngomano\" title=\"Battle of Ngomano\">defeat</a> Portuguese army of about 1,200 at <a href=\"https://wikipedia.org/wiki/Negomano\" title=\"Negomano\">Negomano</a> on the border of modern-day <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> and <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German forces <a href=\"https://wikipedia.org/wiki/Battle_of_Ngomano\" title=\"Battle of Ngomano\">defeat</a> Portuguese army of about 1,200 at <a href=\"https://wikipedia.org/wiki/Negomano\" title=\"Negomano\">Negomano</a> on the border of modern-day <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> and <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzania</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Ngomano", "link": "https://wikipedia.org/wiki/Battle_of_Ngomano"}, {"title": "Negomano", "link": "https://wikipedia.org/wiki/Negomano"}, {"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}, {"title": "Tanzania", "link": "https://wikipedia.org/wiki/Tanzania"}]}, {"year": "1918", "text": "Vojvodina, formerly Austro-Hungarian crown land, proclaims its secession from Austria-Hungary to join the Kingdom of Serbia.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Vojvodina\" title=\"Vojvodina\">Vojvodina</a>, formerly <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> crown land, proclaims its secession from Austria-Hungary to join the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vojvodina\" title=\"Vojvodina\">Vojvodina</a>, formerly <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austro-Hungarian</a> crown land, proclaims its secession from Austria-Hungary to join the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a>.", "links": [{"title": "Vojvodina", "link": "https://wikipedia.org/wiki/Vojvodina"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Kingdom of Serbia", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia"}]}, {"year": "1926", "text": "The deadliest November tornado outbreak in U.S. history kills 76 people and injures more than 400.", "html": "1926 - The deadliest November <a href=\"https://wikipedia.org/wiki/List_of_North_American_tornadoes_and_tornado_outbreaks\" title=\"List of North American tornadoes and tornado outbreaks\">tornado outbreak</a> in U.S. history kills 76 people and injures more than 400.", "no_year_html": "The deadliest November <a href=\"https://wikipedia.org/wiki/List_of_North_American_tornadoes_and_tornado_outbreaks\" title=\"List of North American tornadoes and tornado outbreaks\">tornado outbreak</a> in U.S. history kills 76 people and injures more than 400.", "links": [{"title": "List of North American tornadoes and tornado outbreaks", "link": "https://wikipedia.org/wiki/List_of_North_American_tornadoes_and_tornado_outbreaks"}]}, {"year": "1936", "text": "In Berlin, Germany and Japan sign the Anti-Comintern Pact, agreeing to consult on measures \"to safeguard their common interests\" in the case of an unprovoked attack by the Soviet Union against either nation.", "html": "1936 - In <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>, <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and Japan sign the <a href=\"https://wikipedia.org/wiki/Anti-Comintern_Pact\" title=\"Anti-Comintern Pact\">Anti-Comintern Pact</a>, agreeing to consult on measures \"to safeguard their common interests\" in the case of an unprovoked attack by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> against either nation.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a>, <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and Japan sign the <a href=\"https://wikipedia.org/wiki/Anti-Comintern_Pact\" title=\"Anti-Comintern Pact\">Anti-Comintern Pact</a>, agreeing to consult on measures \"to safeguard their common interests\" in the case of an unprovoked attack by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> against either nation.", "links": [{"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Anti-Comintern Pact", "link": "https://wikipedia.org/wiki/Anti-Comintern_Pact"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1941", "text": "HMS Barham is sunk by a German torpedo during World War II.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/HMS_Barham_(04)\" title=\"HMS Barham (04)\">HMS <i>Barham</i></a> is sunk by a German torpedo during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Barham_(04)\" title=\"HMS Barham (04)\">HMS <i>Barham</i></a> is sunk by a German torpedo during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "HMS Barham (04)", "link": "https://wikipedia.org/wiki/HMS_Barham_(04)"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1943", "text": "World War II: Statehood of Bosnia and Herzegovina is re-established at the State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina.", "html": "1943 - World War II: Statehood of <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> is re-established at the <a href=\"https://wikipedia.org/wiki/State_Anti-fascist_Council_for_the_National_Liberation_of_Bosnia_and_Herzegovina\" title=\"State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina\">State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina</a>.", "no_year_html": "World War II: Statehood of <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> is re-established at the <a href=\"https://wikipedia.org/wiki/State_Anti-fascist_Council_for_the_National_Liberation_of_Bosnia_and_Herzegovina\" title=\"State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina\">State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina</a>.", "links": [{"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "State Anti-fascist Council for the National Liberation of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/State_Anti-fascist_Council_for_the_National_Liberation_of_Bosnia_and_Herzegovina"}]}, {"year": "1947", "text": "<PERSON>are: The \"Hollywood Ten\" are blacklisted by Hollywood movie studios.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\">Red Scare</a>: The \"<a href=\"https://wikipedia.org/wiki/Hollywood_Ten\" class=\"mw-redirect\" title=\"Hollywood Ten\">Hollywood Ten</a>\" are <a href=\"https://wikipedia.org/wiki/Blacklisted\" class=\"mw-redirect\" title=\"Blacklisted\">blacklisted</a> by <a href=\"https://wikipedia.org/wiki/Cinema_of_the_United_States\" title=\"Cinema of the United States\">Hollywood movie studios</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\">Red Scare</a>: The \"<a href=\"https://wikipedia.org/wiki/Hollywood_Ten\" class=\"mw-redirect\" title=\"Hollywood Ten\">Hollywood Ten</a>\" are <a href=\"https://wikipedia.org/wiki/Blacklisted\" class=\"mw-redirect\" title=\"Blacklisted\">blacklisted</a> by <a href=\"https://wikipedia.org/wiki/Cinema_of_the_United_States\" title=\"Cinema of the United States\">Hollywood movie studios</a>.", "links": [{"title": "Second Red Scare", "link": "https://wikipedia.org/wiki/Second_Red_Scare"}, {"title": "Hollywood Ten", "link": "https://wikipedia.org/wiki/Hollywood_Ten"}, {"title": "Blacklisted", "link": "https://wikipedia.org/wiki/Blacklisted"}, {"title": "Cinema of the United States", "link": "https://wikipedia.org/wiki/Cinema_of_the_United_States"}]}, {"year": "1947", "text": "New Zealand ratifies the Statute of Westminster and thus becomes independent of legislative control by the United Kingdom.", "html": "1947 - New Zealand ratifies the <a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_1931\" title=\"Statute of Westminster 1931\">Statute of Westminster</a> and thus becomes independent of legislative control by the United Kingdom.", "no_year_html": "New Zealand ratifies the <a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_1931\" title=\"Statute of Westminster 1931\">Statute of Westminster</a> and thus becomes independent of legislative control by the United Kingdom.", "links": [{"title": "Statute of Westminster 1931", "link": "https://wikipedia.org/wiki/Statute_of_Westminster_1931"}]}, {"year": "1950", "text": "The Great Appalachian Storm of 1950 impacts 22 American states, killing 353 people, injuring over 160, and causing US$66.7 million in damages (1950 dollars).", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Great_Appalachian_Storm_of_1950\" title=\"Great Appalachian Storm of 1950\">Great Appalachian Storm of 1950</a> impacts 22 American states, killing 353 people, injuring over 160, and causing US$66.7 million in damages (1950 dollars).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Appalachian_Storm_of_1950\" title=\"Great Appalachian Storm of 1950\">Great Appalachian Storm of 1950</a> impacts 22 American states, killing 353 people, injuring over 160, and causing US$66.7 million in damages (1950 dollars).", "links": [{"title": "Great Appalachian Storm of 1950", "link": "https://wikipedia.org/wiki/Great_Appalachian_Storm_of_1950"}]}, {"year": "1952", "text": "<PERSON>'s murder-mystery play <PERSON> Mousetrap opens at the Ambassadors Theatre in London's West End after a premiere in Nottingham, UK.  It will become the longest continuously running play in history.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s murder-mystery play <i><a href=\"https://wikipedia.org/wiki/The_Mousetrap\" title=\"The Mousetrap\">The Mousetrap</a></i> opens at the <a href=\"https://wikipedia.org/wiki/Ambassadors_Theatre_(London)\" title=\"Ambassadors Theatre (London)\">Ambassadors Theatre</a> in London's West End after a premiere in Nottingham, UK. It will become the <a href=\"https://wikipedia.org/wiki/List_of_the_longest-running_West_End_shows\" title=\"List of the longest-running West End shows\">longest continuously running play</a> in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s murder-mystery play <i><a href=\"https://wikipedia.org/wiki/The_Mousetrap\" title=\"The Mousetrap\">The Mousetrap</a></i> opens at the <a href=\"https://wikipedia.org/wiki/Ambassadors_Theatre_(London)\" title=\"Ambassadors Theatre (London)\">Ambassadors Theatre</a> in London's West End after a premiere in Nottingham, UK. It will become the <a href=\"https://wikipedia.org/wiki/List_of_the_longest-running_West_End_shows\" title=\"List of the longest-running West End shows\">longest continuously running play</a> in history.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Mousetrap", "link": "https://wikipedia.org/wiki/The_Mousetrap"}, {"title": "Ambassadors Theatre (London)", "link": "https://wikipedia.org/wiki/Ambassadors_Theatre_(London)"}, {"title": "List of the longest-running West End shows", "link": "https://wikipedia.org/wiki/List_of_the_longest-running_West_End_shows"}]}, {"year": "1952", "text": "Korean War: After 42 days of fighting, the Battle of Triangle Hill ends in a Chinese victory. American and South Korean units abandon their attempt to capture the \"Iron Triangle\".", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: After 42 days of fighting, the <a href=\"https://wikipedia.org/wiki/Battle_of_Triangle_Hill\" title=\"Battle of Triangle Hill\">Battle of Triangle Hill</a> ends in a Chinese victory. American and South Korean units abandon their attempt to capture the \"<a href=\"https://wikipedia.org/wiki/Iron_Triangle_(Korea)\" title=\"Iron Triangle (Korea)\">Iron Triangle</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: After 42 days of fighting, the <a href=\"https://wikipedia.org/wiki/Battle_of_Triangle_Hill\" title=\"Battle of Triangle Hill\">Battle of Triangle Hill</a> ends in a Chinese victory. American and South Korean units abandon their attempt to capture the \"<a href=\"https://wikipedia.org/wiki/Iron_Triangle_(Korea)\" title=\"Iron Triangle (Korea)\">Iron Triangle</a>\".", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Battle of Triangle Hill", "link": "https://wikipedia.org/wiki/Battle_of_Triangle_Hill"}, {"title": "Iron Triangle (Korea)", "link": "https://wikipedia.org/wiki/Iron_Triangle_(Korea)"}]}, {"year": "1958", "text": "French Sudan gains autonomy as a self-governing member of the French Community.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a> gains <a href=\"https://wikipedia.org/wiki/Autonomous_administrative_division\" title=\"Autonomous administrative division\">autonomy</a> as a self-governing member of the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Sudan\" title=\"French Sudan\">French Sudan</a> gains <a href=\"https://wikipedia.org/wiki/Autonomous_administrative_division\" title=\"Autonomous administrative division\">autonomy</a> as a self-governing member of the <a href=\"https://wikipedia.org/wiki/French_Community\" title=\"French Community\">French Community</a>.", "links": [{"title": "French Sudan", "link": "https://wikipedia.org/wiki/French_Sudan"}, {"title": "Autonomous administrative division", "link": "https://wikipedia.org/wiki/Autonomous_administrative_division"}, {"title": "French Community", "link": "https://wikipedia.org/wiki/French_Community"}]}, {"year": "1960", "text": "The Mirabal sisters of the Dominican Republic are assassinated.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_sisters\" title=\"Mirabal sisters\">Mirabal sisters</a> of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> are assassinated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_sisters\" title=\"<PERSON>bal sisters\">Mirabal sisters</a> of the <a href=\"https://wikipedia.org/wiki/Dominican_Republic\" title=\"Dominican Republic\">Dominican Republic</a> are assassinated.", "links": [{"title": "<PERSON><PERSON> sisters", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_sisters"}, {"title": "Dominican Republic", "link": "https://wikipedia.org/wiki/Dominican_Republic"}]}, {"year": "1963", "text": "State funeral of <PERSON>; after lying in state at the United States Capitol, a Requiem Mass takes place at Cathedral of St. Matthew the Apostle and the President is buried at Arlington National Cemetery.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/State_funeral_of_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"State funeral of <PERSON>\">State funeral of <PERSON></a>; after lying in state at the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>, a Requiem Mass takes place at <a href=\"https://wikipedia.org/wiki/Cathedral_of_St._<PERSON>_the_Apostle_(Washington,_D.C.)\" class=\"mw-redirect\" title=\"Cathedral of <PERSON>. Matthew the Apostle (Washington, D.C.)\">Cathedral of St. Matthew the Apostle</a> and the President is buried at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/State_funeral_of_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"State funeral of <PERSON>\">State funeral of <PERSON></a>; after lying in state at the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>, a Requiem Mass takes place at <a href=\"https://wikipedia.org/wiki/Cathedral_of_St._<PERSON>_the_Apostle_(Washington,_D.C.)\" class=\"mw-redirect\" title=\"Cathedral of <PERSON>. Matthew the Apostle (Washington, D.C.)\">Cathedral of St. Matthew the Apostle</a> and the President is buried at <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a>.", "links": [{"title": "State funeral of <PERSON>", "link": "https://wikipedia.org/wiki/State_funeral_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}, {"title": "Cathedral of <PERSON><PERSON> Matthew the Apostle (Washington, D.C.)", "link": "https://wikipedia.org/wiki/Cathedral_of_St._Matthew_the_Apostle_(Washington,_D.C.)"}, {"title": "Arlington National Cemetery", "link": "https://wikipedia.org/wiki/Arlington_National_Cemetery"}]}, {"year": "1968", "text": "The Old Student House in Helsinki, Finland is occupied by a large group of University of Helsinki students.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Old_Student_House,_Helsinki\" title=\"Old Student House, Helsinki\">Old Student House</a> in <a href=\"https://wikipedia.org/wiki/Helsinki,_Finland\" class=\"mw-redirect\" title=\"Helsinki, Finland\">Helsinki, Finland</a> is <a href=\"https://wikipedia.org/wiki/Takeover_of_Vanha\" title=\"Takeover of Vanha\">occupied</a> by a large group of <a href=\"https://wikipedia.org/wiki/University_of_Helsinki\" title=\"University of Helsinki\">University of Helsinki</a> students.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_Student_House,_Helsinki\" title=\"Old Student House, Helsinki\">Old Student House</a> in <a href=\"https://wikipedia.org/wiki/Helsinki,_Finland\" class=\"mw-redirect\" title=\"Helsinki, Finland\">Helsinki, Finland</a> is <a href=\"https://wikipedia.org/wiki/Takeover_of_Vanha\" title=\"Takeover of Vanha\">occupied</a> by a large group of <a href=\"https://wikipedia.org/wiki/University_of_Helsinki\" title=\"University of Helsinki\">University of Helsinki</a> students.", "links": [{"title": "Old Student House, Helsinki", "link": "https://wikipedia.org/wiki/Old_Student_House,_Helsinki"}, {"title": "Helsinki, Finland", "link": "https://wikipedia.org/wiki/Helsinki,_Finland"}, {"title": "Takeover of Van<PERSON>", "link": "https://wikipedia.org/wiki/Takeover_of_<PERSON><PERSON>"}, {"title": "University of Helsinki", "link": "https://wikipedia.org/wiki/University_of_Helsinki"}]}, {"year": "1970", "text": "In Japan, author <PERSON><PERSON><PERSON> and one compatriot commit ritualistic seppuku after an unsuccessful coup attempt.", "html": "1970 - In Japan, author <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and one compatriot commit ritualistic <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">seppuku</a> after an unsuccessful coup attempt.", "no_year_html": "In Japan, author <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and one compatriot commit ritualistic <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">seppuku</a> after an unsuccessful coup attempt.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Seppuku", "link": "https://wikipedia.org/wiki/Seppuku"}]}, {"year": "1973", "text": "<PERSON><PERSON>, head of the military Regime of the Colonels in Greece, is ousted in a hardliners' coup led by Brigadier General <PERSON><PERSON>.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, head of the military <a href=\"https://wikipedia.org/wiki/Greek_junta\" title=\"Greek junta\">Regime of the Colonels</a> in Greece, is ousted in a hardliners' coup led by Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, head of the military <a href=\"https://wikipedia.org/wiki/Greek_junta\" title=\"Greek junta\">Regime of the Colonels</a> in Greece, is ousted in a hardliners' coup led by Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Greek junta", "link": "https://wikipedia.org/wiki/Greek_junta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Coup of 25 November 1975, a failed military coup d'état by Portuguese far-left activists seeking to hijack the  Portuguese transition to democracy to establishment a communist regime.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Coup_of_25_November_1975\" title=\"Coup of 25 November 1975\">Coup of 25 November 1975</a>, a failed military <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> by Portuguese far-left activists seeking to hijack the <a href=\"https://wikipedia.org/wiki/Portuguese_transition_to_democracy\" title=\"Portuguese transition to democracy\">Portuguese transition to democracy</a> to establishment a <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> regime.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coup_of_25_November_1975\" title=\"Coup of 25 November 1975\">Coup of 25 November 1975</a>, a failed military <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> by Portuguese far-left activists seeking to hijack the <a href=\"https://wikipedia.org/wiki/Portuguese_transition_to_democracy\" title=\"Portuguese transition to democracy\">Portuguese transition to democracy</a> to establishment a <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> regime.", "links": [{"title": "Coup of 25 November 1975", "link": "https://wikipedia.org/wiki/Coup_of_25_November_1975"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Portuguese transition to democracy", "link": "https://wikipedia.org/wiki/Portuguese_transition_to_democracy"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}]}, {"year": "1975", "text": "Suriname gains independence from the Netherlands.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a> gains independence from the Netherlands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suriname\" title=\"Suriname\">Suriname</a> gains independence from the Netherlands.", "links": [{"title": "Suriname", "link": "https://wikipedia.org/wiki/Suriname"}]}, {"year": "1977", "text": "Former Senator <PERSON><PERSON><PERSON>, is found guilty by the Philippine Military Commission No. 2 and is sentenced to death by firing squad. He is later assassinated in 1983.", "html": "1977 - Former Senator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON>.</a>, is found guilty by the Philippine <a href=\"https://wikipedia.org/wiki/Military_justice\" title=\"Military justice\">Military Commission No. 2</a> and is sentenced to death by <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">firing squad</a>. He is later assassinated in 1983.", "no_year_html": "Former Senator <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON> Jr.</a>, is found guilty by the Philippine <a href=\"https://wikipedia.org/wiki/Military_justice\" title=\"Military justice\">Military Commission No. 2</a> and is sentenced to death by <a href=\"https://wikipedia.org/wiki/Execution_by_firing_squad\" title=\"Execution by firing squad\">firing squad</a>. He is later assassinated in 1983.", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Military justice", "link": "https://wikipedia.org/wiki/Military_justice"}, {"title": "Execution by firing squad", "link": "https://wikipedia.org/wiki/Execution_by_firing_squad"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, president of Upper Volta, is ousted from power in a coup d'état led by Colonel <PERSON><PERSON>.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Sangoul%C3%A9_Lamizana\" title=\"Sangoulé Lamizana\"><PERSON><PERSON><PERSON></a>, president of Upper Volta, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1980_Upper_Voltan_coup_d%27%C3%A9tat\" title=\"1980 Upper Voltan coup d'état\">coup d'état</a> led by Colonel <a href=\"https://wikipedia.org/wiki/Say<PERSON>_Zerbo\" title=\"Say<PERSON> Zerbo\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sangoul%C3%A9_Lamizana\" title=\"Sangoulé Lamizana\"><PERSON><PERSON><PERSON></a>, president of Upper Volta, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1980_Upper_Voltan_coup_d%27%C3%A9tat\" title=\"1980 Upper Voltan coup d'état\">coup d'état</a> led by Colonel <a href=\"https://wikipedia.org/wiki/Saye_Zerbo\" title=\"Saye Zerbo\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sangoul%C3%A9_Lamizana"}, {"title": "1980 Upper Voltan coup d'état", "link": "https://wikipedia.org/wiki/1980_Upper_Voltan_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Pope <PERSON> appoints <PERSON> (the future Pope <PERSON>) Prefect of the Congregation for the Doctrine of the Faith.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">Pope <PERSON> II</a> appoints <PERSON> (the future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">Pope <PERSON></a>) <a href=\"https://wikipedia.org/wiki/Dicastery_for_the_Doctrine_of_the_Faith\" title=\"Dicastery for the Doctrine of the Faith\">Prefect of the Congregation for the Doctrine of the Faith</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> appoints <PERSON> (the future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\">Pope <PERSON></a>) <a href=\"https://wikipedia.org/wiki/Dicastery_for_the_Doctrine_of_the_Faith\" title=\"Dicastery for the Doctrine of the Faith\">Prefect of the Congregation for the Doctrine of the Faith</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dicastery for the Doctrine of the Faith", "link": "https://wikipedia.org/wiki/Dicastery_for_the_Doctrine_of_the_Faith"}]}, {"year": "1984", "text": "Thirty-six top musicians gather in a Notting Hill studio and record Band Aid's \"Do They Know It's Christmas?\" in order to raise money for famine relief in Ethiopia.", "html": "1984 - Thirty-six top musicians gather in a <a href=\"https://wikipedia.org/wiki/Notting_Hill\" title=\"Notting Hill\">Notting Hill</a> studio and record <a href=\"https://wikipedia.org/wiki/Band_Aid_(band)\" title=\"Band Aid (band)\">Band Aid</a>'s \"<a href=\"https://wikipedia.org/wiki/Do_They_Know_It%27s_Christmas%3F\" title=\"Do They Know It's Christmas?\">Do They Know It's Christmas?</a>\" in order to raise money for <a href=\"https://wikipedia.org/wiki/Famine_relief\" title=\"Famine relief\">famine relief</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "no_year_html": "Thirty-six top musicians gather in a <a href=\"https://wikipedia.org/wiki/Notting_Hill\" title=\"Notting Hill\">Notting Hill</a> studio and record <a href=\"https://wikipedia.org/wiki/Band_Aid_(band)\" title=\"Band Aid (band)\">Band Aid</a>'s \"<a href=\"https://wikipedia.org/wiki/Do_They_Know_It%27s_Christmas%3F\" title=\"Do They Know It's Christmas?\">Do They Know It's Christmas?</a>\" in order to raise money for <a href=\"https://wikipedia.org/wiki/Famine_relief\" title=\"Famine relief\">famine relief</a> in <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "links": [{"title": "Notting Hill", "link": "https://wikipedia.org/wiki/Notting_Hill"}, {"title": "Band Aid (band)", "link": "https://wikipedia.org/wiki/Band_Aid_(band)"}, {"title": "Do They Know It's Christmas?", "link": "https://wikipedia.org/wiki/Do_They_Know_It%27s_Christmas%3F"}, {"title": "Famine relief", "link": "https://wikipedia.org/wiki/Famine_relief"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1985", "text": "A Soviet Air Force Antonov An-12 is shot down near Menongue in Angola's Cuando Cubango Province, killing 21.", "html": "1985 - A <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> <a href=\"https://wikipedia.org/wiki/Antonov_An-12\" title=\"Antonov An-12\"><PERSON><PERSON> An-12</a> is <a href=\"https://wikipedia.org/wiki/1985_Aeroflot_Antonov_An-12_shoot-down\" title=\"1985 Aeroflot Antonov An-12 shoot-down\">shot down</a> near <a href=\"https://wikipedia.org/wiki/Menongue\" title=\"Menongue\">Menongue</a> in Angola's <a href=\"https://wikipedia.org/wiki/Cuando_Cubango_Province\" title=\"Cuando Cubango Province\">Cuando Cubango Province</a>, killing 21.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Force</a> <a href=\"https://wikipedia.org/wiki/Antonov_An-12\" title=\"Antonov An-12\"><PERSON>ov An-12</a> is <a href=\"https://wikipedia.org/wiki/1985_Aeroflot_Antonov_An-12_shoot-down\" title=\"1985 Aeroflot Antonov An-12 shoot-down\">shot down</a> near <a href=\"https://wikipedia.org/wiki/Menongue\" title=\"Menongue\">Menongue</a> in Angola's <a href=\"https://wikipedia.org/wiki/Cuando_Cubango_Province\" title=\"Cuando Cubango Province\">Cuando Cubango Province</a>, killing 21.", "links": [{"title": "Soviet Air Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Forces"}, {"title": "Antonov An-12", "link": "https://wikipedia.org/wiki/Antonov_An-12"}, {"title": "1985 Aeroflot Antonov An-12 shoot-down", "link": "https://wikipedia.org/wiki/1985_Aeroflot_Antonov_An-12_shoot-down"}, {"title": "Menongue", "link": "https://wikipedia.org/wiki/Menongue"}, {"title": "Cuando Cubango Province", "link": "https://wikipedia.org/wiki/Cuando_Cubango_Province"}]}, {"year": "1986", "text": "Iran-Contra affair: U.S. Attorney General <PERSON> announces that profits from covert weapons sales to Iran were illegally diverted to the anti-communist Contra rebels in Nicaragua.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: U.S. Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that profits from covert weapons sales to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> were illegally diverted to the anti-communist <a href=\"https://wikipedia.org/wiki/Contras\" title=\"Contras\">Contra</a> rebels in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: U.S. Attorney General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that profits from covert weapons sales to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> were illegally diverted to the anti-communist <a href=\"https://wikipedia.org/wiki/Contras\" title=\"Contras\">Contra</a> rebels in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Contras", "link": "https://wikipedia.org/wiki/Contras"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1986", "text": "The King Fahd Causeway is officially opened in the Persian Gulf.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/King_Fahd_Causeway\" title=\"King Fahd Causeway\">King Fahd Causeway</a> is officially opened in the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/King_Fah<PERSON>_Causeway\" title=\"King Fahd Causeway\">King <PERSON>ahd Causeway</a> is officially opened in the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a>.", "links": [{"title": "King Fahd Causeway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ah<PERSON>_Causeway"}, {"title": "Persian Gulf", "link": "https://wikipedia.org/wiki/Persian_Gulf"}]}, {"year": "1987", "text": "Typhoon <PERSON> pummels the Philippines with category 5 winds of 265 km/h (165 mph) and a surge that destroys entire villages. At least 1,036 deaths are attributed to the storm.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Typhoon_Nina_(1987)\" title=\"Typhoon Nina (1987)\">Typhoon Nina</a> pummels the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> with <a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale\" title=\"Saffir-Simpson scale\">category 5 winds</a> of 265 km/h (165 mph) and a surge that destroys entire villages. At least 1,036 deaths are attributed to the storm.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Nina_(1987)\" title=\"Typhoon Nina (1987)\">Typhoon Nina</a> pummels the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> with <a href=\"https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale\" title=\"Saffir-Simpson scale\">category 5 winds</a> of 265 km/h (165 mph) and a surge that destroys entire villages. At least 1,036 deaths are attributed to the storm.", "links": [{"title": "Typhoon Nina (1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1987)"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Saffir-Simpson scale", "link": "https://wikipedia.org/wiki/Saffir%E2%80%93Simpson_scale"}]}, {"year": "1992", "text": "The Federal Assembly of Czechoslovakia votes to split the country into the Czech Republic and Slovakia, with effect from January 1, 1993.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Federal_Assembly_(Czechoslovakia)\" title=\"Federal Assembly (Czechoslovakia)\">Federal Assembly of Czechoslovakia</a> votes to split the country into the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, with effect from January 1, 1993.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Assembly_(Czechoslovakia)\" title=\"Federal Assembly (Czechoslovakia)\">Federal Assembly of Czechoslovakia</a> votes to split the country into the <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a> and <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, with effect from January 1, 1993.", "links": [{"title": "Federal Assembly (Czechoslovakia)", "link": "https://wikipedia.org/wiki/Federal_Assembly_(Czechoslovakia)"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}]}, {"year": "1999", "text": "A five-year-old Cuban boy, <PERSON><PERSON>, is rescued by fishermen while floating in an inner tube off the Florida coast.", "html": "1999 - A five-year-old Cuban boy, <a href=\"https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is rescued by fishermen while floating in an inner tube off the Florida coast.", "no_year_html": "A five-year-old Cuban boy, <a href=\"https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, is rescued by fishermen while floating in an inner tube off the Florida coast.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez"}]}, {"year": "2000", "text": "The 2000 Baku earthquake, with a Richter magnitude of 7.0, leaves 26 people dead in Baku, Azerbaijan, and becomes the strongest earthquake in the region in 158 years.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/2000_Baku_earthquake\" title=\"2000 Baku earthquake\">2000 Baku earthquake</a>, with a Richter magnitude of 7.0, leaves 26 people dead in <a href=\"https://wikipedia.org/wiki/Baku\" title=\"Baku\">Baku</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, and becomes the strongest earthquake in the region in 158 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2000_Baku_earthquake\" title=\"2000 Baku earthquake\">2000 Baku earthquake</a>, with a Richter magnitude of 7.0, leaves 26 people dead in <a href=\"https://wikipedia.org/wiki/Baku\" title=\"Baku\">Baku</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, and becomes the strongest earthquake in the region in 158 years.", "links": [{"title": "2000 Baku earthquake", "link": "https://wikipedia.org/wiki/2000_Baku_earthquake"}, {"title": "Baku", "link": "https://wikipedia.org/wiki/Baku"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}]}, {"year": "2008", "text": "Cyclone <PERSON><PERSON> strikes northern Sri Lanka, killing 15 people and displacing 90,000 others while dealing the region the highest rainfall in nine decades.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Cyclone_Ni<PERSON>_(2008)\" title=\"Cyclone Nisha (2008)\">Cyclone <PERSON><PERSON></a> strikes northern <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing 15 people and displacing 90,000 others while dealing the region the highest rainfall in nine decades.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyclone_Ni<PERSON>_(2008)\" title=\"Cyclone Nisha (2008)\">Cyclone <PERSON><PERSON></a> strikes northern <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, killing 15 people and displacing 90,000 others while dealing the region the highest rainfall in nine decades.", "links": [{"title": "<PERSON> (2008)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(2008)"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "2009", "text": "Jeddah floods: Freak rains swamp the city of Jeddah, Saudi Arabia, during an ongoing Hajj pilgrimage. Three thousand cars are swept away and 122 people perish in the torrents, with 350 others missing.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/2009_Jeddah_floods\" title=\"2009 Jeddah floods\">Jeddah floods</a>: Freak rains swamp the city of <a href=\"https://wikipedia.org/wiki/Jeddah\" title=\"Jeddah\">Jeddah</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, during an ongoing Hajj pilgrimage. Three thousand cars are swept away and 122 people perish in the torrents, with 350 others missing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2009_Jeddah_floods\" title=\"2009 Jeddah floods\">Jeddah floods</a>: Freak rains swamp the city of <a href=\"https://wikipedia.org/wiki/Jeddah\" title=\"Jeddah\">Jeddah</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, during an ongoing Hajj pilgrimage. Three thousand cars are swept away and 122 people perish in the torrents, with 350 others missing.", "links": [{"title": "2009 Jeddah floods", "link": "https://wikipedia.org/wiki/2009_Jeddah_floods"}, {"title": "Jeddah", "link": "https://wikipedia.org/wiki/Jeddah"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}], "Births": [{"year": "902", "text": "Emperor <PERSON><PERSON> of Liao (d. 947)", "html": "902 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON> of Liao\">Emperor <PERSON><PERSON> of Liao</a> (d. 947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON> of Liao\">Emperor <PERSON><PERSON> of Liao</a> (d. 947)", "links": [{"title": "Emperor <PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao"}]}, {"year": "1075", "text": "Emperor <PERSON><PERSON> of Jin (d. 1135)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a> (d. 1135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a> (d. 1135)", "links": [{"title": "Emperor <PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin"}]}, {"year": "1454", "text": "<PERSON>, Queen of Cyprus (d. 1510)", "html": "1454 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of Cyprus (d. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of Cyprus (d. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1467", "text": "<PERSON>, 2nd Baron <PERSON>, Knight of Henry VIII of England (d. 1525)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Knight of Henry <PERSON> of England (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, Knight of Henry VIII of England (d. 1525)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON> of Cattaro, Dominican visionary and anchoress (d. 1565)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Cattaro\" title=\"<PERSON><PERSON><PERSON> of Cattaro\"><PERSON><PERSON><PERSON> of Cattaro</a>, Dominican visionary and anchoress (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Cattaro\" title=\"<PERSON><PERSON><PERSON> of Cattaro\"><PERSON><PERSON><PERSON> of Cattaro</a>, Dominican visionary and anchoress (d. 1565)", "links": [{"title": "<PERSON><PERSON><PERSON> of Cattaro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Cattaro"}]}, {"year": "1562", "text": "<PERSON><PERSON>, Spanish playwright and poet (d. 1635)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish playwright and poet (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish playwright and poet (d. 1635)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, English actor (d. 1630)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1577", "text": "<PERSON><PERSON>, Dutch admiral (d. 1629)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch admiral (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch admiral (d. 1629)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1587", "text": "Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet, English politician (d. 1666)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet\">Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet</a>, English politician (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet\">Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet</a>, English politician (d. 1666)", "links": [{"title": "Sir <PERSON><PERSON><PERSON><PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1609", "text": "<PERSON> of France, Queen of England, Scotland and Ireland (d. 1669)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, Queen of England, Scotland and Ireland (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, Queen of England, Scotland and Ireland (d. 1669)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1638", "text": "<PERSON> Braganza (d. 1705)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Braganza\" title=\"<PERSON> of Braganza\"><PERSON> of Braganza</a> (d. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Braganza\" title=\"<PERSON> of Braganza\"><PERSON> of Braganza</a> (d. 1705)", "links": [{"title": "Catherine of Braganza", "link": "https://wikipedia.org/wiki/Catherine_of_Braganza"}]}, {"year": "1666", "text": "<PERSON>, Italian violin maker (d. 1740)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violin maker (d. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violin maker (d. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON><PERSON><PERSON>, French astronomer and botanist (d. 1784)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_S%C3%A9guier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and botanist (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_S%C3%A9guier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and botanist (d. 1784)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois_S%C3%A9guier"}]}, {"year": "1752", "text": "<PERSON>, German composer and critic (d. 1814)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and critic (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and critic (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1753", "text": "<PERSON>, American spy (d. 1838)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(spy)\" title=\"<PERSON> (spy)\"><PERSON></a>, American spy (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(spy)\" title=\"<PERSON> (spy)\"><PERSON></a>, American spy (d. 1838)", "links": [{"title": "<PERSON> (spy)", "link": "https://wikipedia.org/wiki/<PERSON>_(spy)"}]}, {"year": "1758", "text": "<PERSON>, Jr., American general and politician, 7th United States Secretary of War (d. 1843)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and politician, 7th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general and politician, 7th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1843)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1778", "text": "<PERSON>, English author and activist (d. 1856)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (d. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, Austrian organist and composer (d. 1863)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Xaver Gruber\"><PERSON></a>, Austrian organist and composer (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Franz Xaver Gruber\"><PERSON></a>, Austrian organist and composer (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_X<PERSON>_G<PERSON>ber"}]}, {"year": "1814", "text": "<PERSON>, German physician and physicist (d. 1878)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and physicist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physician and physicist (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Canadian merchant and politician (d. 1904)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian merchant and politician (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian merchant and politician (d. 1904)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1817", "text": "<PERSON>, American lawyer and politician, United States Ambassador to France (d. 1911)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "1835", "text": "<PERSON>, Scottish-American businessman and philanthropist (d. 1919)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American businessman and philanthropist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American businessman and philanthropist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, German mathematician and academic (d. 1902)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and academic (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, German mathematician and academic (d. 1902)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hr%C3%B6<PERSON>_(mathematician)"}]}, {"year": "1843", "text": "<PERSON>, American businessman and philanthropist (d. 1919)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, German engineer and businessman, founded Mercedes-Benz (d. 1929)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_Benz\" class=\"mw-redirect\" title=\"Karl Benz\"><PERSON></a>, German engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Mercedes-Benz\" title=\"Mercedes-Benz\">Mercedes-Benz</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mercedes-Benz", "link": "https://wikipedia.org/wiki/Mercedes-Benz"}]}, {"year": "1845", "text": "<PERSON>, Portuguese-French journalist and author (d. 1900)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese-French journalist and author (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese-French journalist and author (d. 1900)", "links": [{"title": "<PERSON> Que<PERSON>ós", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s"}]}, {"year": "1846", "text": "<PERSON>, American activist (d. 1911)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Carrie_Nation\" title=\"Carrie Nation\"><PERSON></a>, American activist (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carrie_Nation\" title=\"Carrie Nation\"><PERSON></a>, American activist (d. 1911)", "links": [{"title": "Carrie <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, French journalist, author, and playwright (d. 1922)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, author, and playwright (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, author, and playwright (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON>, American pianist and composer (d. 1901)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, Swedish tug of war competitor, shot putter, and discus thrower (d. 1958)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor, shot putter, and discus thrower (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor, shot putter, and discus thrower (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gustaf_S%C3%B6derstr%C3%B6m"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1865", "text": "<PERSON>, American engineer, businesswoman, and philanthropist (d. 1933)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businesswoman, and philanthropist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businesswoman, and philanthropist (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Egyptian economist, founded the Banque Misr (d. 1941)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Tala<PERSON>_Harb\" title=\"<PERSON><PERSON><PERSON> Harb\"><PERSON><PERSON><PERSON></a>, Egyptian economist, founded the <a href=\"https://wikipedia.org/wiki/Banque_Misr\" title=\"Banque Misr\">Banque Misr</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rb\" title=\"<PERSON><PERSON><PERSON> Harb\"><PERSON><PERSON><PERSON></a>, Egyptian economist, founded the <a href=\"https://wikipedia.org/wiki/Banque_Misr\" title=\"Banque Misr\">Banque Misr</a> (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rb"}, {"title": "Banque Mi<PERSON>r", "link": "https://wikipedia.org/wiki/Banque_Misr"}]}, {"year": "1868", "text": "<PERSON>, Grand Duke of Hesse (d. 1937)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (d. 1937)", "links": [{"title": "<PERSON>, Grand Duke of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Hesse"}]}, {"year": "1869", "text": "<PERSON>, American lawyer and judge (d. 1934)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" class=\"mw-redirect\" title=\"<PERSON> (jurist)\"><PERSON></a>, American lawyer and judge (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" class=\"mw-redirect\" title=\"<PERSON> (jurist)\"><PERSON></a>, American lawyer and judge (d. 1934)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)"}]}, {"year": "1870", "text": "<PERSON><PERSON>, American director, producer, and playwright (d. 1937)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Winthrop_Ames\" title=\"Winthrop Ames\"><PERSON><PERSON></a>, American director, producer, and playwright (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winthrop_Ames\" title=\"Winthrop Ames\"><PERSON><PERSON></a>, American director, producer, and playwright (d. 1937)", "links": [{"title": "<PERSON><PERSON> Ames", "link": "https://wikipedia.org/wiki/<PERSON>throp_Ames"}]}, {"year": "1870", "text": "<PERSON>, French painter of Les Nabis movement (d. 1943)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Na<PERSON>\"><PERSON></a> movement (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> movement (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Les_Nabis"}]}, {"year": "1872", "text": "<PERSON>, American gymnast and triathlete (d. 1960)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American painter and illustrator (d. 1945)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American boxer (d. 1910)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "Princess <PERSON> of Saxe-Coburg and Gotha (d. 1936)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Saxe-Coburg_and_Gotha\" title=\"Princess <PERSON> of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Saxe-Coburg_and_Gotha\" title=\"Princess <PERSON> of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1936)", "links": [{"title": "Princess <PERSON> of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON><PERSON>_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1877", "text": "<PERSON>-<PERSON>, British actor, director and playwright (d. 1946)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Granville-Barker\"><PERSON></a>, British actor, director and playwright (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Granville-Barker\" title=\"<PERSON> Granville-Barker\"><PERSON>-<PERSON></a>, British actor, director and playwright (d. 1946)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Granville-Barker"}]}, {"year": "1880", "text": "<PERSON>, Australian minister and pilot, founded the Royal Flying Doctor Service of Australia (d. 1951)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, Australian minister and pilot, founded the <a href=\"https://wikipedia.org/wiki/Royal_Flying_Doctor_Service_of_Australia\" class=\"mw-redirect\" title=\"Royal Flying Doctor Service of Australia\">Royal Flying Doctor Service of Australia</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, Australian minister and pilot, founded the <a href=\"https://wikipedia.org/wiki/Royal_Flying_Doctor_Service_of_Australia\" class=\"mw-redirect\" title=\"Royal Flying Doctor Service of Australia\">Royal Flying Doctor Service of Australia</a> (d. 1951)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>(minister)"}, {"title": "Royal Flying Doctor Service of Australia", "link": "https://wikipedia.org/wiki/Royal_Flying_Doctor_Service_of_Australia"}]}, {"year": "1880", "text": "<PERSON>, English author (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Romanian-Israeli poet and critic (d. 1958)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli poet and critic (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli poet and critic (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1881", "text": "<PERSON> (d. 1963)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\"><PERSON> John XXIII</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\"><PERSON> John XXIII</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>III"}]}, {"year": "1883", "text": "<PERSON>, American mystic and author (d. 1939)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mystic and author (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mystic and author (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Russian botanist and geneticist (d. 1943)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian botanist and geneticist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian botanist and geneticist (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Turkish author and playwright (d. 1956)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin\" title=\"Reşat Nuri Güntekin\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin\" title=\"Reşat Nuri Güntekin\"><PERSON><PERSON><PERSON></a>, Turkish author and playwright (d. 1956)", "links": [{"title": "Reşat N<PERSON>", "link": "https://wikipedia.org/wiki/Re%C5%9Fat_Nuri_G%C3%BCntekin"}]}, {"year": "1890", "text": "<PERSON>, English soldier and poet (d. 1918)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and poet (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and poet (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 26th <PERSON><PERSON><PERSON><PERSON> (d. 1941)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D\" title=\"Ō<PERSON><PERSON> Uichirō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 26th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D\" title=\"Ō<PERSON><PERSON> Uichirō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 26th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Cnishiki_Uichir%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1893", "text": "<PERSON>, American author and critic (d. 1970)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German pianist and composer (d. 1991)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Soviet politician, Chairman of the Presidium of the Supreme Soviet of the Soviet Union (d. 1978)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Chairman</a> of the <a href=\"https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet\" title=\"Presidium of the Supreme Soviet\">Presidium of the Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Chairman</a> of the <a href=\"https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet\" title=\"Presidium of the Supreme Soviet\">Presidium of the Supreme Soviet</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}, {"title": "Presidium of the Supreme Soviet", "link": "https://wikipedia.org/wiki/Presidium_of_the_Supreme_Soviet"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1895", "text": "<PERSON>, American poet and author (d. 1986)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech general and politician, 8th President of Czechoslovakia (d. 1979)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Svoboda\" title=\"Ludvík Svoboda\"><PERSON>d<PERSON><PERSON>bo<PERSON></a>, Czech general and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Svoboda\" title=\"Ludvík Svoboda\">Lud<PERSON><PERSON> Svoboda</a>, Czech general and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (d. 1979)", "links": [{"title": "Ludvík Svoboda", "link": "https://wikipedia.org/wiki/Ludv%C3%ADk_Svoboda"}, {"title": "President of Czechoslovakia", "link": "https://wikipedia.org/wiki/President_of_Czechoslovakia"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Indonesian archbishop (d. 1963)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pranat<PERSON>\" title=\"<PERSON><PERSON>pra<PERSON>\"><PERSON><PERSON></a>, Indonesian archbishop (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pra<PERSON>\" title=\"<PERSON><PERSON>pranat<PERSON>\"><PERSON><PERSON></a>, Indonesian archbishop (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Soegijapranata"}]}, {"year": "1896", "text": "<PERSON>, American composer and critic (d. 1989)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virgil_Thomson"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Indian actor, director, and screenwriter (d. 1971)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Estonian tenor and director (d. 1989)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian tenor and director (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian tenor and director (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>ne_<PERSON>iisi<PERSON>a"}]}, {"year": "1900", "text": "<PERSON>, German SS officer (d. 1947)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ss\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ss\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rudolf_H%C3%B6ss"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1900", "text": "<PERSON>, American actress and politician (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and politician (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and politician (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German SS officer (d. 1948)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1902", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shore\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American discus thrower and shot putter (d. 1964)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Italian composer and conductor (d. 2000)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and conductor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Turkish mystic and author (d. 1993)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish mystic and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish mystic and author (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American philosopher and logician (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and logician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and logician (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English race car driver and pilot (d. 1938)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and pilot (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi playwright, author, educator, director and media personality (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Natyaguru\" class=\"mw-redirect\" title=\"Natyaguru\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Nuru<PERSON>_Momen\" title=\"<PERSON><PERSON><PERSON> Mom<PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi playwright, author, educator, director and media personality (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Natyaguru\" class=\"mw-redirect\" title=\"Natyaguru\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Nuru<PERSON>_Momen\" title=\"<PERSON><PERSON><PERSON> Mom<PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi playwright, author, educator, director and media personality (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natyaguru"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuru<PERSON>_<PERSON>en"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator (d. 1986)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"P. D<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"P. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator (d. 1986)", "links": [{"title": "P<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Dutch painter and photographer (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and photographer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and photographer (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American physician, etymologist, and educator (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, etymologist, and educator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician, etymologist, and educator (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American baseball player and coach (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Russian-French journalist (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French journalist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Zitrone"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Chilean general and politician, 30th President of Chile (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean general and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean general and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Peruvian politician, 121st Prime Minister of Peru (d. 2013)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 121st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 121st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Peru", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Peru"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American actress and screenwriter (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress and screenwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Italian cardinal (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON>, Cypriot-Turkish colonel and politician, Deputy Prime Minister of Turkey (d. 1997)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F\" title=\"Alparslan Türkeş\">Alparsla<PERSON> Türk<PERSON>ş</a>, Cypriot-Turkish colonel and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F\" title=\"Alparslan Türkeş\"><PERSON>parsla<PERSON>ü<PERSON>ş</a>, Cypriot-Turkish colonel and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a> (d. 1997)", "links": [{"title": "Alparslan Türkeş", "link": "https://wikipedia.org/wiki/Alparslan_T%C3%BCrke%C5%9F"}, {"title": "Deputy Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey"}]}, {"year": "1919", "text": "<PERSON>, American director, producer, and screenwriter (d. 1979)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, English actress (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Mexican-American actor, singer, and director (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor, singer, and director (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican-American actor, singer, and director (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Montalb%C3%A1n"}]}, {"year": "1920", "text": "<PERSON>, American actress (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Czech composer and playwright (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hu<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hu<PERSON>%C3%ADk\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and playwright (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilja_Hurn%C3%ADk"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Portuguese-American businessman and philanthropist (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Portuguese-American businessman and philanthropist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> B<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Portuguese-American businessman and philanthropist (d. 2014)", "links": [{"title": "Fe<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Finnish banker and politician, 9th President of Finland (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish banker and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish banker and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1923", "text": "<PERSON>, American golfer (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American golfer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American golfer (d. 2001)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1924", "text": "<PERSON>, American saxophonist and composer (d. 1977)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American activist, co-founded the National League of Families (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/National_League_of_Families\" class=\"mw-redirect\" title=\"National League of Families\">National League of Families</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/National_League_of_Families\" class=\"mw-redirect\" title=\"National League of Families\">National League of Families</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "National League of Families", "link": "https://wikipedia.org/wiki/National_League_of_Families"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Japanese poet, philosopher, and critic (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, philosopher, and critic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, philosopher, and critic (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American author (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and producer (d. 1969)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and jurist, 21st Chief Justice of India (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and jurist, 21st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and jurist, 21st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}]}, {"year": "1927", "text": "<PERSON>, American journalist and author (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American director and producer (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American cornet and trumpet player (d. 2000)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet and trumpet player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet and trumpet player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ley"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actress and singer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American geologist and academic (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American dancer and choreographer (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Turkish sociologist and psychologist (d. 1983)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist and psychologist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish sociologist and psychologist (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erol_G%C3%BCng%C3%B6r"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Italian actress (d. 2009)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American economist and academic (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Austrian-German physicist and astronaut (d. 1995)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-German physicist and astronaut (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-German physicist and astronaut (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American football coach and auto racing executive", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and auto racing executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and auto racing executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Mauritian politician, 3rd President of Mauritius (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritian politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius\" class=\"mw-redirect\" title=\"List of Presidents of Mauritius\">President of Mauritius</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritian politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius\" class=\"mw-redirect\" title=\"List of Presidents of Mauritius\">President of Mauritius</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of Mauritius", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Mauritius"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indian jurist and politician, 21st Governor of West Bengal", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian jurist and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian jurist and politician, 21st <a href=\"https://wikipedia.org/wiki/Governor_of_West_Bengal\" class=\"mw-redirect\" title=\"Governor of West Bengal\">Governor of West Bengal</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of West Bengal", "link": "https://wikipedia.org/wiki/Governor_of_West_Bengal"}]}, {"year": "1940", "text": "<PERSON>, American singer (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Greek pole vaulter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pole vaulter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English journalist and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Pakistani spiritual leader and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani spiritual leader and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani spiritual leader and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Greek footballer and manager (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and harmonica player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor, television personality, game show host, lawyer, and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television personality, game show host, lawyer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television personality, game show host, lawyer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Kenyan lawyer and politician, 8th Vice President of Kenya (d. 2003)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Kenya\" class=\"mw-redirect\" title=\"Vice President of Kenya\">Vice President of Kenya</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Kenya\" class=\"mw-redirect\" title=\"Vice President of Kenya\">Vice President of Kenya</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of Kenya", "link": "https://wikipedia.org/wiki/Vice_President_of_Kenya"}]}, {"year": "1945", "text": "<PERSON>, American journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American painter and illustrator (d. 1984)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American football player (d. 2007)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2007)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1946", "text": "<PERSON>, American author and illustrator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1946", "text": "<PERSON>, English footballer (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (d. 2011)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1947", "text": "<PERSON>, French-American director and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian lawyer and politician, 14th Deputy Premier of Quebec", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Deputy Premier of Quebec", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_Quebec"}]}, {"year": "1948", "text": "<PERSON>, American author (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Australian cricketer and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kerry_O%27Keeffe"}]}, {"year": "1950", "text": "<PERSON>, English-American author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Italian author, screenwriter, and actor (d. 2014)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American baseball player and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American author and poet", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter (d. 2011)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Spanish author and journalist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON>erte\" title=\"<PERSON>\"><PERSON></a>, Spanish author and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>-<PERSON>erte\" title=\"<PERSON>\"><PERSON></a>, Spanish author and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_P%C3%A9<PERSON>-<PERSON><PERSON>e"}]}, {"year": "1951", "text": "<PERSON>, Dutch footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rep\" title=\"Johnny Rep\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Johnny_Rep\" title=\"Johnny Rep\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author and educator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Crescent_Dragonwagon\" title=\"Crescent Dragonwagon\">Crescent Dragonwagon</a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crescent_Dragonwagon\" title=\"Crescent Dragonwagon\">Crescent Dragonwagon</a>, American author and educator", "links": [{"title": "Crescent Dragonwagon", "link": "https://wikipedia.org/wiki/Crescent_Dragonwagon"}]}, {"year": "1952", "text": "<PERSON>, American businessman and politician, 80th Governor of New Hampshire", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Hampshire_governor)\" title=\"<PERSON> (New Hampshire governor)\"><PERSON></a>, American businessman and politician, 80th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Hampshire_governor)\" title=\"<PERSON> (New Hampshire governor)\"><PERSON></a>, American businessman and politician, 80th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a>", "links": [{"title": "<PERSON> (New Hampshire governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Hampshire_governor)"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Oriali\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON> Oriali\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Oriali"}]}, {"year": "1953", "text": "<PERSON>, Australian rugby league player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American author, screenwriter, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lling"}]}, {"year": "1955", "text": "<PERSON>, American director and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Dutch author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian dancer and choreographer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and politician, 60th Governor of Maryland", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1958", "text": "<PERSON>, American historian of science", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of science", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of science", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Scottish journalist and politician (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English guitarist and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American lawyer, journalist, and publisher (d. 1999)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American lawyer, journalist, and publisher (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lawyer, journalist, and publisher (d. 1999)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1961", "text": "<PERSON>, English footballer (d. 2013)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_<PERSON>tive"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese videogame designer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese videogame designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese videogame designer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Japanese comedian and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese comedian and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor and director", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian singer and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter (d. 2022)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American musician, songwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American football player, coach, and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1966", "text": "<PERSON>, American R&B singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Surinamese swimmer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian comedian and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian actress and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christina_<PERSON>gate"}]}, {"year": "1971", "text": "<PERSON>, Swedish ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/G%C3%B6ksel_Demirpen%C3%A7e\" class=\"mw-redirect\" title=\"G<PERSON><PERSON><PERSON> Demirpençe\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6ks<PERSON>_Demirpen%C3%A7e\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>pen<PERSON>e\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6ks<PERSON>_Demirpen%C3%A7e"}]}, {"year": "1972", "text": "<PERSON>, American basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_N<PERSON>in\" title=\"<PERSON><PERSON><PERSON> N<PERSON>melin\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_N<PERSON>in\" title=\"<PERSON><PERSON><PERSON> N<PERSON>mel<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>melin"}]}, {"year": "1973", "text": "<PERSON>, Dutch cyclist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavio <PERSON>el\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavio <PERSON>el\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Octavio_<PERSON><PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian actor (d. 2024)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Canadian actor (d. 2024)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Moroccan-Dutch journalist and author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan-Dutch journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan-Dutch journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Northern Irish actor and DJ", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish actor and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish actor and DJ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American soccer player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Ukrainian gymnast and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Argentinian tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Guillermo_<PERSON>%C3%B1as\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guillermo_<PERSON>%C3%B1as\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillermo_Ca%C3%B1as"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese singer-songwriter and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> She<PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> She<PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American wrestler and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South African footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, German rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hier\" title=\"<PERSON><PERSON><PERSON> Thier\"><PERSON><PERSON><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hier\" title=\"St<PERSON><PERSON> Thier\"><PERSON><PERSON><PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, South Korean baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American activist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1981)\" title=\"<PERSON> (born 1981)\"><PERSON></a>, American activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1981)\" title=\"<PERSON> (born 1981)\"><PERSON></a>, American activist", "links": [{"title": "<PERSON> (born 1981)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1981)"}]}, {"year": "1981", "text": "<PERSON>, American journalist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Chevon_Troutman\" title=\"Chevon Troutman\">Chevo<PERSON> Troutman</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chevon_Troutman\" title=\"Chevon Troutman\">Chevo<PERSON> Troutman</a>, American basketball player", "links": [{"title": "Chevon Troutman", "link": "https://wikipedia.org/wiki/Chevon_Troutman"}]}, {"year": "1982", "text": "<PERSON>, Australian-English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, French actor (d. 2022)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ulliel\" title=\"<PERSON>pard Ulliel\"><PERSON><PERSON></a>, French actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ulliel\" title=\"<PERSON>pard Ulliel\"><PERSON><PERSON></a>, French actor (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ulliel"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Dutch pentathlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch pentathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Georgian luger (d. 2010)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian luger (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian luger (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Spearing"}]}, {"year": "1989", "text": "<PERSON>, Belgian singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, German ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Romanian tennis player ", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian tennis player ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dan"}]}, {"year": "1993", "text": "<PERSON>, English motorcycle racer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American basketball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Slovenian tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ri"}]}], "Deaths": [{"year": "311", "text": "<PERSON> of Alexandria", "html": "311 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>_of_Alexandria\" class=\"mw-redirect\" title=\"<PERSON> of Alexandria\">Pope <PERSON> of Alexandria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>_of_Alexandria\" class=\"mw-redirect\" title=\"<PERSON> of Alexandria\">Pope <PERSON> of Alexandria</a>", "links": [{"title": "<PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Alexandria"}]}, {"year": "734", "text": "<PERSON><PERSON><PERSON>, Turkic emperor (b. 683)", "html": "734 - <a href=\"https://wikipedia.org/wiki/Bilge_<PERSON>gan\" class=\"mw-redirect\" title=\"Bilge Khagan\"><PERSON><PERSON><PERSON></a>, Turkic emperor (b. 683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bil<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Bilge Khagan\"><PERSON><PERSON><PERSON></a>, Turkic emperor (b. 683)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bilge_<PERSON>"}]}, {"year": "1034", "text": "<PERSON> of Scotland (b. 954)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (b. 954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (b. 954)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1120", "text": "<PERSON>, son of <PERSON> of England (sinking of the White Ship) (b. 1103)", "html": "1120 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (sinking of the <i>White Ship</i>) (b. 1103)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (sinking of the <i>White Ship</i>) (b. 1103)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1185", "text": "<PERSON> (b. 1097)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_III\" title=\"Pope Lucius III\">Pope Lucius III</a> (b. 1097)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lucius_III\" title=\"<PERSON> Lucius III\">Pope Lucius III</a> (b. 1097)", "links": [{"title": "<PERSON> <PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1326", "text": "<PERSON>, Japanese shōgun (b. 1264)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shōgun (b. 1264)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Japanese shō<PERSON> (b. 1264)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1374", "text": "<PERSON>, Prince of Taranto (b. 1329)", "html": "1374 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Taranto\"><PERSON>, Prince of Taranto</a> (b. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Taranto\"><PERSON>, Prince of Taranto</a> (b. 1329)", "links": [{"title": "<PERSON>, Prince of Taranto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Taranto"}]}, {"year": "1456", "text": "<PERSON>, French merchant and banker (b. 1395)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a>, French merchant and banker (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a>, French merchant and banker (b. 1395)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_C%C5%93ur"}]}, {"year": "1560", "text": "<PERSON>, Italian admiral (b. 1466)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian admiral (b. 1466)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian admiral (b. 1466)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, Chinese general (b. 1512)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Hu_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hu_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hu_<PERSON>n"}]}, {"year": "1626", "text": "<PERSON>, English actor, founded Dulwich College (b. 1566)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, founded <a href=\"https://wikipedia.org/wiki/Dulwich_College\" title=\"Dulwich College\">Dulwich College</a> (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, founded <a href=\"https://wikipedia.org/wiki/Dulwich_College\" title=\"Dulwich College\">Dulwich College</a> (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dulwich College", "link": "https://wikipedia.org/wiki/Dulwich_College"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON>, French astronomer and mathematician (b. 1605)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and mathematician (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and mathematician (b. 1605)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus"}]}, {"year": "1700", "text": "<PERSON><PERSON>, American lawyer and politician, 10th Mayor of New York City (b. 1643)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (b. 1643)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1748", "text": "<PERSON>, English hymnwriter and theologian (b. 1674)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymnwriter and theologian (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymnwriter and theologian (b. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, German violinist and composer (b. 1687)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English poet and politician (b. 1712)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and politician (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and politician (b. 1712)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1865", "text": "<PERSON>, German explorer and scholar (b. 1821)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and scholar (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and scholar (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German chemist and academic (b. 1818)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American lawyer and politician, 21st Vice President of the United States (b. 1819)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1885", "text": "<PERSON> of Spain (b. 1857)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XII_of_Spain\" class=\"mw-redirect\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (b. 1857)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Alfonso_XII_of_Spain"}]}, {"year": "1909", "text": "<PERSON>, American lawyer and politician (b. 1839)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French-American racing driver and businessman (b. 1892)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Chevrolet\" title=\"Gaston Chevrolet\"><PERSON></a>, French-American racing driver and businessman (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaston_Chevrolet\" title=\"Gaston Chevrolet\"><PERSON></a>, French-American racing driver and businessman (b. 1892)", "links": [{"title": "Gaston Chevrolet", "link": "https://wikipedia.org/wiki/Gaston_Chevrolet"}]}, {"year": "1934", "text": "<PERSON><PERSON> <PERSON><PERSON>, English plant taxonomist and authority on succulents (b. 1849)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English plant taxonomist and authority on succulents (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English plant taxonomist and authority on succulents (b. 1849)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "Kenesaw <PERSON>is, American lawyer and judge (b. 1866)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Kenesaw_Mountain_Landis\" title=\"Kenesaw Mountain Landis\">Kenesaw Mountain Landis</a>, American lawyer and judge (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenesaw_Mountain_Landis\" title=\"Kenesaw Mountain Landis\">Kenesaw Mountain Landis</a>, American lawyer and judge (b. 1866)", "links": [{"title": "Kenesaw Mountain Landis", "link": "https://wikipedia.org/wiki/Kenesaw_Mountain_Landis"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese martial artist, founded <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1877)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Kanbun_Uechi\" title=\"Kanbun Uechi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Uechi-ry%C5%AB\" class=\"mw-redirect\" title=\"Uechi-ryū\">Uechi-ry<PERSON></a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kanbun_Uechi\" title=\"Kanbun Uechi\">Ka<PERSON><PERSON><PERSON></a>, Japanese martial artist, founded <a href=\"https://wikipedia.org/wiki/Uechi-ry%C5%AB\" class=\"mw-redirect\" title=\"Uechi-ryū\">Uechi-ryū</a> (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kanbun_Uechi"}, {"title": "Uechi-ryū", "link": "https://wikipedia.org/wiki/Uechi-ry%C5%AB"}]}, {"year": "1949", "text": "<PERSON>, American actor and dancer (b. 1878)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Chinese general (b. 1922)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Danish author and playwright, Nobel Prize laureate (b. 1873)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Finnish linguist and diplomat (b. 1873)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish linguist and diplomat (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish linguist and diplomat (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Ukrainian-Russian director, producer, and screenwriter (b. 1894)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian director, producer, and screenwriter (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian director, producer, and screenwriter (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON> of Greece and Denmark (b. 1869)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (b. 1869)", "links": [{"title": "<PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1959", "text": "<PERSON><PERSON>, French actor (b. 1922)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Belgian archer (b. 1866)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian archer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian archer (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Russian lieutenant (b. 1913)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lieutenant (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lieutenant (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English pianist and educator (b. 1890)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and educator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American novelist, critic, and essayist (b. 1878)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sinclair\" title=\"Upton Sinclair\"><PERSON><PERSON></a>, American novelist, critic, and essayist (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Upton_Sinclair\" title=\"Upton Sinclair\"><PERSON><PERSON></a>, American novelist, critic, and essayist (b. 1878)", "links": [{"title": "Upton Sinclair", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sinclair"}]}, {"year": "1968", "text": "<PERSON>, American geographer and explorer (b. 1908)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geographer and explorer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geographer and explorer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese author, actor, and director (b. 1925)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, actor, and director (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author, actor, and director (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Romanian engineer, designed the Coandă-1910 (b. 1886)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer, designed the <a href=\"https://wikipedia.org/wiki/Coand%C4%83-1910\" title=\"Coandă-1910\">Coandă-1910</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C4%83\" title=\"<PERSON>\"><PERSON></a>, Romanian engineer, designed the <a href=\"https://wikipedia.org/wiki/Coand%C4%83-1910\" title=\"Coandă-1910\">Coandă-1910</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Coand%C4%83"}, {"title": "Coandă-1910", "link": "https://wikipedia.org/wiki/Coand%C4%83-1910"}]}, {"year": "1972", "text": "<PERSON>, German architect (b. 1893)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Lithuania-born English actor (b. 1928)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuania-born English actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuania-born English actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1948)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Drake\"><PERSON></a>, English singer-songwriter and guitarist (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Burmese lawyer and diplomat, 3rd Secretary-General of the United Nations (b. 1909)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/U_Thant\" title=\"U Thant\"><PERSON></a>, Burmese lawyer and diplomat, 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U_Thant\" title=\"U Thant\"><PERSON></a>, Burmese lawyer and diplomat, 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1909)", "links": [{"title": "U Thant", "link": "https://wikipedia.org/wiki/U_Thant"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1980", "text": "<PERSON>, American tennis player (b. 1928)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lam"}]}, {"year": "1981", "text": "<PERSON>, American actor and singer (b. 1907)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON> (Pakistani singer), Pakistani Christian playback singer (b. 1932)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Pakistani_singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Pakistani singer)\"><PERSON><PERSON> (Pakistani singer)</a>, Pakistani Christian playback singer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Pakistani_singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Pakistani singer)\"><PERSON><PERSON> (Pakistani singer)</a>, Pakistani Christian playback singer (b. 1932)", "links": [{"title": "<PERSON><PERSON> (Pakistani singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(Pakistani_singer)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician, 5th Deputy Prime Minister of India (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India\" title=\"Deputy Prime Minister of India\">Deputy Prime Minister of India</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>van"}, {"title": "Deputy Prime Minister of India", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_India"}]}, {"year": "1985", "text": "<PERSON>, English poet and critic (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German pastor and theologian (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American lawyer and politician, 51st Mayor of Chicago (b. 1922)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harold Washington\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American general (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>va_<PERSON>_<PERSON>tch\" title=\"<PERSON>va R<PERSON>\"><PERSON><PERSON></a>, American general (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>va_<PERSON>_<PERSON>tch\" title=\"Alva R. <PERSON>tch\"><PERSON><PERSON></a>, American general (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>tch"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Georgian philosopher and academic (b. 1930)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian philosopher and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian philosopher and academic (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actress and voice artist (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian-French journalist (b. 1914)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French journalist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French journalist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Zitrone"}]}, {"year": "1997", "text": "<PERSON>, Malawian physician and politician, 1st President of Malawi (b. 1898)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Hastings_Banda\" title=\"Hastings Banda\"><PERSON></a>, Malawian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Malawi\" title=\"President of Malawi\">President of Malawi</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hastings_Banda\" title=\"Hastings Banda\"><PERSON></a>, Malawian physician and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Malawi\" title=\"President of Malawi\">President of Malawi</a> (b. 1898)", "links": [{"title": "Hastings Banda", "link": "https://wikipedia.org/wiki/Hastings_Banda"}, {"title": "President of Malawi", "link": "https://wikipedia.org/wiki/President_of_Malawi"}]}, {"year": "1998", "text": "<PERSON>, American philosopher and academic (b. 1906)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American comedian, actor, and screenwriter (b. 1933)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American comedian, actor, and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American comedian, actor, and screenwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flip_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Mexican union leader and politician (b. 1904)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_Campa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican union leader and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_Campa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican union leader and politician (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valent%C3%ADn_Campa"}]}, {"year": "2000", "text": "<PERSON>, American baseball player and scout (b. 1917)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout (b. 1917)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Czech-English director and producer (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-English director and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-English director and producer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American painter and academic (b. 1939)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Northern Irish footballer (b. 1946)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"George Best\"><PERSON></a>, Northern Irish footballer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George Best\"><PERSON></a>, Northern Irish footballer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English rally driver (b. 1971)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rally driver (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rally driver (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Italian author and illustrator (b. 1931)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Mexican singer-songwriter (b. 1979)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer-songwriter (b. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valent%C3%ADn_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actress and publisher, co-founded Beginner Books (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Beginner_Books\" title=\"Beginner Books\">Beginner Books</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Beginner_Books\" title=\"Beginner Books\">Beginner Books</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Beginner Books", "link": "https://wikipedia.org/wiki/Beginner_Books"}]}, {"year": "2006", "text": "<PERSON>, American lieutenant and pilot (b. 1919)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American philosopher and academic (b. 1954)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, British protozoologist (b. 1915) ", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British protozoologist (b. 1915) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British protozoologist (b. 1915) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American journalist and author (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English keyboard player, songwriter, and director (b. 1955)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and director (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and director (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American anthropologist and academic (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and academic (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English businessman, founded Bernard <PERSON> Farms (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Farms\" class=\"mw-redirect\" title=\"Bernard <PERSON> Farms\"><PERSON>s</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Farms\" class=\"mw-redirect\" title=\"Bernard <PERSON> Farms\"><PERSON>s</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Farms", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Farms"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Russian weightlifter and coach (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian weightlifter and coach (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian weightlifter and coach (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1947)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Coco_<PERSON>aux"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Pakistani poet and writer (b. 1959)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and writer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani poet and writer (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON>war"}]}, {"year": "2012", "text": "<PERSON>, Swedish mathematician and educator (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rmander\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and educator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rmander\" title=\"<PERSON>\"><PERSON></a>, Swedish mathematician and educator (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_H%C3%B6<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English footballer and manager (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, English actress (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American football player and businessman (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and businessman (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>mp\"><PERSON></a>, American football player and businessman (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Argentinian businessman (b. 1968)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ricardo_Fort\" title=\"Ricardo Fort\"><PERSON> Fort</a>, Argentinian businessman (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_Fort\" title=\"Ricardo Fort\"><PERSON></a>, Argentinian businessman (b. 1968)", "links": [{"title": "Ricardo Fort", "link": "https://wikipedia.org/wiki/Ricardo_Fort"}]}, {"year": "2013", "text": "<PERSON>, English footballer and manager (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American drummer and bandleader (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and bandleader (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hamilton\"><PERSON></a>, American drummer and bandleader (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chico_Hamilton"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Czech journalist and politician (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Egon_L%C3%A1nsk%C3%BD\" title=\"Egon Lánský\"><PERSON><PERSON></a>, Czech journalist and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egon_L%C3%A1nsk%C3%BD\" title=\"Egon <PERSON>\"><PERSON><PERSON></a>, Czech journalist and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Egon_L%C3%A1nsk%C3%BD"}]}, {"year": "2013", "text": "<PERSON>, American author and illustrator (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Al_Plastino\" title=\"Al <PERSON>lastino\"><PERSON></a>, American author and illustrator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Plastino\" title=\"<PERSON>last<PERSON>\"><PERSON></a>, American author and illustrator (b. 1921)", "links": [{"title": "Al Plastino", "link": "https://wikipedia.org/wiki/Al_Plastino"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American publisher and philanthropist (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher and philanthropist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American publisher and philanthropist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian dancer, and choreographer (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Indian dancer, and choreographer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Indian dancer, and choreographer (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitara_Devi"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Czech composer and conductor (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and conductor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and conductor (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American biogerontologist and academic (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biogerontologist and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biogerontologist and academic (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Jamaican boxer (b. 1974)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/O%27N<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican boxer (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O%27N<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican boxer (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O%27N<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English admiral (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral (b. 1932)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Norwegian drummer and composer (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer and composer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian drummer and composer (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Svein_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Swedish author and translator (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lennar<PERSON> Hellsing\"><PERSON><PERSON><PERSON></a>, Swedish author and translator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lennar<PERSON> Hellsing\"><PERSON><PERSON><PERSON></a>, Swedish author and translator (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lennart_Hellsing"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American director, producer, and editor (b. 1913)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and editor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and editor (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Communist leader of Cuba, and revolutionary (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Communist leader of Cuba, and revolutionary (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Communist leader of Cuba, and revolutionary (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1945)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Glass\"><PERSON></a>, American actor (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Argentinian football player (b. 1960)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Diego_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian football player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_<PERSON>\" title=\"Diego <PERSON>\"><PERSON></a>, Argentinian football player (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, English football player and manager (b.1943)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b.1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and manager (b.1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>enables"}]}, {"year": "2024", "text": "<PERSON>, American actor (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American evangelist and Christian writer (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and Christian writer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and Christian writer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}