{"date": "March 29", "url": "https://wikipedia.org/wiki/March_29", "data": {"Events": [{"year": "1430", "text": "The Ottoman Empire under <PERSON><PERSON> captures Thessalonica from the Republic of Venice.", "html": "1430 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> under <a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\">Murad II</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Thessalonica_(1422%E2%80%931430)\" title=\"Siege of Thessalonica (1422-1430)\">captures</a> <a href=\"https://wikipedia.org/wiki/Thessalonica\" class=\"mw-redirect\" title=\"Thessalonica\">Thessalonica</a> from the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> under <a href=\"https://wikipedia.org/wiki/Murad_II\" title=\"Murad II\">Murad II</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Thessalonica_(1422%E2%80%931430)\" title=\"Siege of Thessalonica (1422-1430)\">captures</a> <a href=\"https://wikipedia.org/wiki/Thessalonica\" class=\"mw-redirect\" title=\"Thessalonica\">Thessalonica</a> from the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Murad II", "link": "https://wikipedia.org/wiki/Murad_II"}, {"title": "Siege of Thessalonica (1422-1430)", "link": "https://wikipedia.org/wiki/Siege_of_Thessalonica_(1422%E2%80%931430)"}, {"title": "Thessalonica", "link": "https://wikipedia.org/wiki/Thessalonica"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1461", "text": "Battle of Towton: <PERSON> of York defeats Queen <PERSON> to become King <PERSON> of England, bringing a temporary stop to the Wars of the Roses.", "html": "1461 - <a href=\"https://wikipedia.org/wiki/Battle_of_Towton\" title=\"Battle of Towton\">Battle of Towton</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> of York</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\">Queen <PERSON></a> to become King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> of England</a>, bringing a temporary stop to the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Towton\" title=\"Battle of Towton\">Battle of Towton</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> of York</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anjou\" title=\"<PERSON> of Anjou\">Queen <PERSON></a> to become King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV of England</a>, bringing a temporary stop to the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>.", "links": [{"title": "Battle of Towton", "link": "https://wikipedia.org/wiki/Battle_of_Towton"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Wars of the Roses", "link": "https://wikipedia.org/wiki/Wars_of_the_Roses"}]}, {"year": "1549", "text": "The city of Salvador, Bahia, the first capital of Brazil, is founded.", "html": "1549 - The city of <a href=\"https://wikipedia.org/wiki/Salvador,_Bahia\" title=\"Salvador, Bahia\">Salvador, Bahia</a>, the first capital of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, is founded.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Salvador,_Bahia\" title=\"Salvador, Bahia\">Salvador, Bahia</a>, the first capital of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, is founded.", "links": [{"title": "Salvador, Bahia", "link": "https://wikipedia.org/wiki/Salvador,_Bahia"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}]}, {"year": "1632", "text": "Treaty of Saint-Germain is signed returning Quebec to French control after the English had seized it in 1629.", "html": "1632 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1632)\" title=\"Treaty of Saint-Germain-en-Laye (1632)\">Treaty of Saint-Germain</a> is signed returning <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> to French control after the English had seized it in <a href=\"https://wikipedia.org/wiki/1629\" title=\"1629\">1629</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1632)\" title=\"Treaty of Saint-Germain-en-Laye (1632)\">Treaty of Saint-Germain</a> is signed returning <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a> to French control after the English had seized it in <a href=\"https://wikipedia.org/wiki/1629\" title=\"1629\">1629</a>.", "links": [{"title": "Treaty of Saint-Germain-en-Laye (1632)", "link": "https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1632)"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "1629", "link": "https://wikipedia.org/wiki/1629"}]}, {"year": "1792", "text": "King <PERSON> of Sweden dies after being shot in the back at a midnight masquerade ball at Stockholm's Royal Opera 13 days earlier.", "html": "1792 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"Gustav III\"><PERSON> III</a> of Sweden dies after being shot in the back at a midnight <a href=\"https://wikipedia.org/wiki/Masquerade_ball\" title=\"Masquerade ball\">masquerade ball</a> at <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>'s <a href=\"https://wikipedia.org/wiki/Royal_Swedish_Opera\" title=\"Royal Swedish Opera\">Royal Opera</a> 13 days earlier.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"Gustav III\"><PERSON></a> of Sweden dies after being shot in the back at a midnight <a href=\"https://wikipedia.org/wiki/Masquerade_ball\" title=\"Masquerade ball\">masquerade ball</a> at <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>'s <a href=\"https://wikipedia.org/wiki/Royal_Swedish_Opera\" title=\"Royal Swedish Opera\">Royal Opera</a> 13 days earlier.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Masquerade ball", "link": "https://wikipedia.org/wiki/Masquerade_ball"}, {"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}, {"title": "Royal Swedish Opera", "link": "https://wikipedia.org/wiki/Royal_Swedish_Opera"}]}, {"year": "1806", "text": "Construction is authorized of the Great National Pike, better known as the Cumberland Road, becoming the first United States federal highway.", "html": "1806 - Construction is authorized of the Great National Pike, better known as the <a href=\"https://wikipedia.org/wiki/Cumberland_Road\" class=\"mw-redirect\" title=\"Cumberland Road\">Cumberland Road</a>, becoming the first United States federal <a href=\"https://wikipedia.org/wiki/Highway#United_States\" title=\"Highway\">highway</a>.", "no_year_html": "Construction is authorized of the Great National Pike, better known as the <a href=\"https://wikipedia.org/wiki/Cumberland_Road\" class=\"mw-redirect\" title=\"Cumberland Road\">Cumberland Road</a>, becoming the first United States federal <a href=\"https://wikipedia.org/wiki/Highway#United_States\" title=\"Highway\">highway</a>.", "links": [{"title": "Cumberland Road", "link": "https://wikipedia.org/wiki/Cumberland_Road"}, {"title": "Highway", "link": "https://wikipedia.org/wiki/Highway#United_States"}]}, {"year": "1809", "text": "King <PERSON> of Sweden abdicates after a coup d'état.", "html": "1809 - King <a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV Adolf of Sweden</a> <a href=\"https://wikipedia.org/wiki/Abdication\" title=\"Abdication\">abdicates</a> after a <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Gustav_IV_Adolf_of_Sweden\" class=\"mw-redirect\" title=\"Gustav IV Adolf of Sweden\"><PERSON> IV <PERSON> of Sweden</a> <a href=\"https://wikipedia.org/wiki/Abdication\" title=\"Abdication\">abdicates</a> after a <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden"}, {"title": "Abdication", "link": "https://wikipedia.org/wiki/Abdication"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "1809", "text": "At the Diet of Porvoo, Finland's four Estates pledge allegiance to <PERSON> of Russia, commencing the secession of the Grand Duchy of Finland from Sweden.", "html": "1809 - At the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Porvoo\" title=\"Diet of Porvoo\">Diet of Porvoo</a>, Finland's <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">four Estates</a> pledge allegiance to <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Russia\" title=\"Alexander I of Russia\"><PERSON> of Russia</a>, commencing the secession of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a> from <a href=\"https://wikipedia.org/wiki/Lands_of_Sweden\" title=\"Lands of Sweden\">Sweden</a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Porvoo\" title=\"Diet of Porvoo\">Diet of Porvoo</a>, Finland's <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">four Estates</a> pledge allegiance to <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Russia\" title=\"<PERSON> I of Russia\"><PERSON> of Russia</a>, commencing the secession of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a> from <a href=\"https://wikipedia.org/wiki/Lands_of_Sweden\" title=\"Lands of Sweden\">Sweden</a>.", "links": [{"title": "Diet of Porvoo", "link": "https://wikipedia.org/wiki/<PERSON>_of_Porvoo"}, {"title": "Riksdag of the Estates", "link": "https://wikipedia.org/wiki/Riksdag_of_the_Estates"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}, {"title": "Lands of Sweden", "link": "https://wikipedia.org/wiki/Lands_of_Sweden"}]}, {"year": "1847", "text": "Mexican-American War: United States forces led by General <PERSON> take Veracruz after a siege.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: United States forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take <a href=\"https://wikipedia.org/wiki/Veracruz_(city)\" title=\"Veracruz (city)\">Veracruz</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Veracruz\" title=\"Siege of Veracruz\">siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: United States forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take <a href=\"https://wikipedia.org/wiki/Veracruz_(city)\" title=\"Veracruz (city)\">Veracruz</a> after a <a href=\"https://wikipedia.org/wiki/Siege_of_Veracruz\" title=\"Siege of Veracruz\">siege</a>.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Veracruz (city)", "link": "https://wikipedia.org/wiki/Veracruz_(city)"}, {"title": "Siege of Veracruz", "link": "https://wikipedia.org/wiki/Siege_of_Veracruz"}]}, {"year": "1849", "text": "The United Kingdom annexes the Punjab.", "html": "1849 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a> annexes the <a href=\"https://wikipedia.org/wiki/Punjab_Province_(British_India)\" title=\"Punjab Province (British India)\">Punjab</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom</a> annexes the <a href=\"https://wikipedia.org/wiki/Punjab_Province_(British_India)\" title=\"Punjab Province (British India)\">Punjab</a>.", "links": [{"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Punjab Province (British India)", "link": "https://wikipedia.org/wiki/Punjab_Province_(British_India)"}]}, {"year": "1857", "text": "<PERSON><PERSON> of the 34th Regiment, Bengal Native Infantry mutinies against the East India Company's rule in India and inspires the protracted Indian Rebellion of 1857, also known as the Sepoy Mutiny.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">Sepoy</a> <a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\">Mangal Pandey</a> of the 34th <a href=\"https://wikipedia.org/wiki/Regiment\" title=\"Regiment\">Regiment</a>, <a href=\"https://wikipedia.org/wiki/Bengal_Native_Infantry\" title=\"Bengal Native Infantry\">Bengal Native Infantry</a> mutinies against the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a>'s <a href=\"https://wikipedia.org/wiki/Company_rule_in_India\" title=\"Company rule in India\">rule in India</a> and inspires the protracted <a href=\"https://wikipedia.org/wiki/Indian_Rebellion_of_1857\" title=\"Indian Rebellion of 1857\">Indian Rebellion of 1857</a>, also known as the Sepoy Mutiny.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">Sepoy</a> <a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\">Mangal Pandey</a> of the 34th <a href=\"https://wikipedia.org/wiki/Regiment\" title=\"Regiment\">Regiment</a>, <a href=\"https://wikipedia.org/wiki/Bengal_Native_Infantry\" title=\"Bengal Native Infantry\">Bengal Native Infantry</a> mutinies against the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a>'s <a href=\"https://wikipedia.org/wiki/Company_rule_in_India\" title=\"Company rule in India\">rule in India</a> and inspires the protracted <a href=\"https://wikipedia.org/wiki/Indian_Rebellion_of_1857\" title=\"Indian Rebellion of 1857\">Indian Rebellion of 1857</a>, also known as the Sepoy Mutiny.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oy"}, {"title": "Mangal <PERSON>", "link": "https://wikipedia.org/wiki/Mangal_Pandey"}, {"title": "Regiment", "link": "https://wikipedia.org/wiki/Regiment"}, {"title": "Bengal Native Infantry", "link": "https://wikipedia.org/wiki/Bengal_Native_Infantry"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "Company rule in India", "link": "https://wikipedia.org/wiki/Company_rule_in_India"}, {"title": "Indian Rebellion of 1857", "link": "https://wikipedia.org/wiki/Indian_Rebellion_of_1857"}]}, {"year": "1867", "text": "Queen <PERSON> gives Royal Assent to the British North America Act which establishes Canada on July 1.", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen <PERSON>\">Queen <PERSON></a> gives <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> to the <a href=\"https://wikipedia.org/wiki/British_North_America_Act,_1867\" class=\"mw-redirect\" title=\"British North America Act, 1867\">British North America Act</a> which establishes Canada on <a href=\"https://wikipedia.org/wiki/July_1\" title=\"July 1\">July 1</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a> gives <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> to the <a href=\"https://wikipedia.org/wiki/British_North_America_Act,_1867\" class=\"mw-redirect\" title=\"British North America Act, 1867\">British North America Act</a> which establishes Canada on <a href=\"https://wikipedia.org/wiki/July_1\" title=\"July 1\">July 1</a>.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "Royal Assent", "link": "https://wikipedia.org/wiki/Royal_Assent"}, {"title": "British North America Act, 1867", "link": "https://wikipedia.org/wiki/British_North_America_Act,_1867"}, {"title": "July 1", "link": "https://wikipedia.org/wiki/July_1"}]}, {"year": "1871", "text": "Royal Albert Hall is opened by Queen Victoria.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Royal_Albert_Hall\" title=\"Royal Albert Hall\">Royal Albert Hall</a> is opened by <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Royal_Albert_Hall\" title=\"Royal Albert Hall\">Royal Albert Hall</a> is opened by <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a>.", "links": [{"title": "Royal Albert Hall", "link": "https://wikipedia.org/wiki/Royal_Albert_Hall"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1879", "text": "Anglo-Zulu War: Battle of Kambula: British forces defeat 20,000 Zulus.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Kambula\" title=\"Battle of Kambula\">Battle of Kambula</a>: British forces defeat 20,000 <a href=\"https://wikipedia.org/wiki/Zulus\" class=\"mw-redirect\" title=\"Zulus\">Zulus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Kambula\" title=\"Battle of Kambula\">Battle of Kambula</a>: British forces defeat 20,000 <a href=\"https://wikipedia.org/wiki/Zulus\" class=\"mw-redirect\" title=\"Zulus\">Zulus</a>.", "links": [{"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}, {"title": "Battle of Kambula", "link": "https://wikipedia.org/wiki/Battle_of_Kambula"}, {"title": "Zulus", "link": "https://wikipedia.org/wiki/Zulus"}]}, {"year": "1882", "text": "The Knights of Columbus is established.", "html": "1882 - The <a href=\"https://wikipedia.org/wiki/Knights_of_Columbus\" title=\"Knights of Columbus\">Knights of Columbus</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Knights_of_Columbus\" title=\"Knights of Columbus\">Knights of Columbus</a> is established.", "links": [{"title": "Knights of Columbus", "link": "https://wikipedia.org/wiki/Knights_of_Columbus"}]}, {"year": "1927", "text": "Sunbeam 1000hp breaks the land speed record at Daytona Beach, Florida.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Sunbeam_1000hp\" class=\"mw-redirect\" title=\"Sunbeam 1000hp\">Sunbeam 1000hp</a> breaks the land speed record at Daytona Beach, Florida.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sunbeam_1000hp\" class=\"mw-redirect\" title=\"Sunbeam 1000hp\">Sunbeam 1000hp</a> breaks the land speed record at Daytona Beach, Florida.", "links": [{"title": "Sunbeam 1000hp", "link": "https://wikipedia.org/wiki/Sunbeam_1000hp"}]}, {"year": "1936", "text": "The 1936 German parliamentary election and referendum seeks approval for the recent remilitarization of the Rhineland.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/1936_German_parliamentary_election_and_referendum\" title=\"1936 German parliamentary election and referendum\">1936 German parliamentary election and referendum</a> seeks approval for the recent remilitarization of the Rhineland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1936_German_parliamentary_election_and_referendum\" title=\"1936 German parliamentary election and referendum\">1936 German parliamentary election and referendum</a> seeks approval for the recent remilitarization of the Rhineland.", "links": [{"title": "1936 German parliamentary election and referendum", "link": "https://wikipedia.org/wiki/1936_German_parliamentary_election_and_referendum"}]}, {"year": "1941", "text": "The North American Regional Broadcasting Agreement goes into effect at 03:00 local time.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/North_American_Regional_Broadcasting_Agreement\" title=\"North American Regional Broadcasting Agreement\">North American Regional Broadcasting Agreement</a> goes into effect at 03:00 local time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_American_Regional_Broadcasting_Agreement\" title=\"North American Regional Broadcasting Agreement\">North American Regional Broadcasting Agreement</a> goes into effect at 03:00 local time.", "links": [{"title": "North American Regional Broadcasting Agreement", "link": "https://wikipedia.org/wiki/North_American_Regional_Broadcasting_Agreement"}]}, {"year": "1941", "text": "World War II: British Royal Navy and Royal Australian Navy forces defeat those of the Italian Regia Marina off the Peloponnesian coast of Greece in the Battle of Cape Matapan.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and <a href=\"https://wikipedia.org/wiki/Royal_Australian_Navy\" title=\"Royal Australian Navy\">Royal Australian Navy</a> forces defeat those of the Italian <i><a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a></i> off the <a href=\"https://wikipedia.org/wiki/Peloponnese\" title=\"Peloponnese\">Peloponnesian</a> coast of Greece in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Matapan\" title=\"Battle of Cape Matapan\">Battle of Cape Matapan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and <a href=\"https://wikipedia.org/wiki/Royal_Australian_Navy\" title=\"Royal Australian Navy\">Royal Australian Navy</a> forces defeat those of the Italian <i><a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a></i> off the <a href=\"https://wikipedia.org/wiki/Peloponnese\" title=\"Peloponnese\">Peloponnesian</a> coast of Greece in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Matapan\" title=\"Battle of Cape Matapan\">Battle of Cape Matapan</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Royal Australian Navy", "link": "https://wikipedia.org/wiki/Royal_Australian_Navy"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "Peloponnese", "link": "https://wikipedia.org/wiki/Peloponnese"}, {"title": "Battle of Cape Matapan", "link": "https://wikipedia.org/wiki/Battle_of_Cape_Matapan"}]}, {"year": "1942", "text": "The Bombing of Lübeck in World War II is the first major success for the RAF Bomber Command against Germany and a German city.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/Bombing_of_L%C3%BCbeck_in_World_War_II\" title=\"Bombing of Lübeck in World War II\">Bombing of Lübeck in World War II</a> is the first major success for the <a href=\"https://wikipedia.org/wiki/RAF_Bomber_Command\" title=\"RAF Bomber Command\">RAF Bomber Command</a> against Germany and a German city.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bombing_of_L%C3%BCbeck_in_World_War_II\" title=\"Bombing of Lübeck in World War II\">Bombing of Lübeck in World War II</a> is the first major success for the <a href=\"https://wikipedia.org/wiki/RAF_Bomber_Command\" title=\"RAF Bomber Command\">RAF Bomber Command</a> against Germany and a German city.", "links": [{"title": "Bombing of Lübeck in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_L%C3%BCbeck_in_World_War_II"}, {"title": "RAF Bomber Command", "link": "https://wikipedia.org/wiki/RAF_Bomber_Command"}]}, {"year": "1947", "text": "The Malagasy Uprising against French colonial rule begins in Madagascar.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Malagasy_Uprising\" title=\"Malagasy Uprising\">Malagasy Uprising</a> against French colonial rule begins in <a href=\"https://wikipedia.org/wiki/French_Madagascar\" title=\"French Madagascar\">Madagascar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Malagasy_Uprising\" title=\"Malagasy Uprising\">Malagasy Uprising</a> against French colonial rule begins in <a href=\"https://wikipedia.org/wiki/French_Madagascar\" title=\"French Madagascar\">Madagascar</a>.", "links": [{"title": "Malagasy Uprising", "link": "https://wikipedia.org/wiki/Malagasy_Uprising"}, {"title": "French Madagascar", "link": "https://wikipedia.org/wiki/French_Madagascar"}]}, {"year": "1951", "text": "<PERSON> and <PERSON> are convicted of conspiracy to commit espionage.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are convicted of conspiracy to commit <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> are convicted of conspiracy to commit <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1951", "text": "Hypnosis murders in Copenhagen.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Hypnosis_murders\" class=\"mw-redirect\" title=\"Hypnosis murders\">Hypnosis murders</a> in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hypnosis_murders\" class=\"mw-redirect\" title=\"Hypnosis murders\">Hypnosis murders</a> in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>.", "links": [{"title": "Hypnosis murders", "link": "https://wikipedia.org/wiki/Hypnosis_murders"}, {"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}]}, {"year": "1957", "text": "The New York, Ontario and Western Railway makes its final run, the first major U.S. railroad to be abandoned in its entirety.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/New_York,_Ontario_and_Western_Railway\" title=\"New York, Ontario and Western Railway\">New York, Ontario and Western Railway</a> makes its final run, the first major U.S. railroad to be abandoned in its entirety.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York,_Ontario_and_Western_Railway\" title=\"New York, Ontario and Western Railway\">New York, Ontario and Western Railway</a> makes its final run, the first major U.S. railroad to be abandoned in its entirety.", "links": [{"title": "New York, Ontario and Western Railway", "link": "https://wikipedia.org/wiki/New_York,_Ontario_and_Western_Railway"}]}, {"year": "1961", "text": "The Twenty-third Amendment to the United States Constitution is ratified, allowing residents of Washington, D.C., to vote in presidential elections.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Twenty-third_Amendment_to_the_United_States_Constitution\" title=\"Twenty-third Amendment to the United States Constitution\">Twenty-third Amendment to the United States Constitution</a> is ratified, allowing residents of <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, to vote in presidential elections.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Twenty-third_Amendment_to_the_United_States_Constitution\" title=\"Twenty-third Amendment to the United States Constitution\">Twenty-third Amendment to the United States Constitution</a> is ratified, allowing residents of <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, to vote in presidential elections.", "links": [{"title": "Twenty-third Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-third_Amendment_to_the_United_States_Constitution"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1962", "text": "<PERSON>, the president of Argentina, is overthrown in a military coup by Argentina's armed forces, ending an 11.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1⁄2 day constitutional crisis.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the president of <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, is overthrown in a military <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup</a> by Argentina's <a href=\"https://wikipedia.org/wiki/Armed_forces_of_Argentina\" class=\"mw-redirect\" title=\"Armed forces of Argentina\">armed forces</a>, ending an 11<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> day constitutional crisis.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the president of <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, is overthrown in a military <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup</a> by Argentina's <a href=\"https://wikipedia.org/wiki/Armed_forces_of_Argentina\" class=\"mw-redirect\" title=\"Armed forces of Argentina\">armed forces</a>, ending an 11<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> day constitutional crisis.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ondiz<PERSON>"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Armed forces of Argentina", "link": "https://wikipedia.org/wiki/Armed_forces_of_Argentina"}]}, {"year": "1968", "text": "The funeral of <PERSON>, the first man in space, started in Moscow, with thousands of people in attendance.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Funeral_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Funeral of <PERSON> and <PERSON>\">funeral of <PERSON></a>, the first man in space, started in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, with thousands of people in attendance.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Funeral_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Funeral of <PERSON> and <PERSON>\">funeral of <PERSON></a>, the first man in space, started in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, with thousands of people in attendance.", "links": [{"title": "Funeral of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Funeral_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1971", "text": "My Lai massacre: Lieutenant <PERSON> is convicted of premeditated murder and sentenced to life in prison.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/My_<PERSON>_massacre\" title=\"My Lai massacre\">My Lai massacre</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of premeditated murder and sentenced to life in prison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/My_<PERSON>_massacre\" title=\"My Lai massacre\">My Lai massacre</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of premeditated murder and sentenced to life in prison.", "links": [{"title": "My Lai massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_massacre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "Vietnam War: The last United States combat soldiers leave South Vietnam.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The last United States combat soldiers leave <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The last United States combat soldiers leave <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1973", "text": "Operation Barrel Roll, a covert American bombing campaign in Laos to stop communist infiltration of South Vietnam, ends.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Operation_Barrel_Roll\" title=\"Operation Barrel Roll\">Operation Barrel Roll</a>, a covert American bombing campaign in <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> to stop <a href=\"https://wikipedia.org/wiki/PAVN\" class=\"mw-redirect\" title=\"PAVN\">communist infiltration</a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>, ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Barrel_Roll\" title=\"Operation Barrel Roll\">Operation Barrel Roll</a>, a covert American bombing campaign in <a href=\"https://wikipedia.org/wiki/Laos\" title=\"Laos\">Laos</a> to stop <a href=\"https://wikipedia.org/wiki/PAVN\" class=\"mw-redirect\" title=\"PAVN\">communist infiltration</a> of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>, ends.", "links": [{"title": "Operation Barrel Roll", "link": "https://wikipedia.org/wiki/Operation_Barrel_Roll"}, {"title": "Laos", "link": "https://wikipedia.org/wiki/Laos"}, {"title": "PAVN", "link": "https://wikipedia.org/wiki/PAVN"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1974", "text": "NASA's Mariner 10 becomes the first space probe to fly by Mercury.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mariner_10\" title=\"Mariner 10\">Mariner 10</a> becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to fly by <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mariner_10\" title=\"Mariner 10\">Mariner 10</a> becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to fly by <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mariner 10", "link": "https://wikipedia.org/wiki/Mariner_10"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}, {"title": "Mercury (planet)", "link": "https://wikipedia.org/wiki/Mercury_(planet)"}]}, {"year": "1974", "text": "Terracotta Army was discovered in Shaanxi province, China.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Terracotta_Army\" title=\"Terracotta Army\">Terracotta Army</a> was discovered in <a href=\"https://wikipedia.org/wiki/Shaanxi\" title=\"Shaanxi\">Shaanxi province</a>, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terracotta_Army\" title=\"Terracotta Army\">Terracotta Army</a> was discovered in <a href=\"https://wikipedia.org/wiki/Shaanxi\" title=\"Shaanxi\">Shaanxi province</a>, China.", "links": [{"title": "Terracotta Army", "link": "https://wikipedia.org/wiki/Terracotta_Army"}, {"title": "Shaanxi", "link": "https://wikipedia.org/wiki/Shaanxi"}]}, {"year": "1982", "text": "The Canada Act 1982 receives the Royal Assent from Queen <PERSON>, setting the stage for the Queen of Canada to proclaim the Constitution Act, 1982.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/Canada_Act_1982\" title=\"Canada Act 1982\">Canada Act 1982</a> receives the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> from Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>, setting the stage for the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Canada\" title=\"Monarchy of Canada\">Queen of Canada</a> to proclaim the <a href=\"https://wikipedia.org/wiki/Constitution_Act,_1982\" title=\"Constitution Act, 1982\">Constitution Act, 1982</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canada_Act_1982\" title=\"Canada Act 1982\">Canada Act 1982</a> receives the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a> from Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>, setting the stage for the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Canada\" title=\"Monarchy of Canada\">Queen of Canada</a> to proclaim the <a href=\"https://wikipedia.org/wiki/Constitution_Act,_1982\" title=\"Constitution Act, 1982\">Constitution Act, 1982</a>.", "links": [{"title": "Canada Act 1982", "link": "https://wikipedia.org/wiki/Canada_Act_1982"}, {"title": "Royal Assent", "link": "https://wikipedia.org/wiki/Royal_Assent"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Monarchy of Canada", "link": "https://wikipedia.org/wiki/Monarchy_of_Canada"}, {"title": "Constitution Act, 1982", "link": "https://wikipedia.org/wiki/Constitution_Act,_1982"}]}, {"year": "1984", "text": "The Baltimore Colts load its possessions onto fifteen Mayflower moving trucks in the early morning hours and transfer its operations to Indianapolis.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Indianapolis_Colts\" title=\"Indianapolis Colts\">Baltimore Colts</a> load its possessions onto fifteen <a href=\"https://wikipedia.org/wiki/Mayflower_Transit\" title=\"Mayflower Transit\">Mayflower</a> moving trucks in the early morning hours and <a href=\"https://wikipedia.org/wiki/Baltimore_Colts_relocation_to_Indianapolis\" title=\"Baltimore Colts relocation to Indianapolis\">transfer its operations</a> to <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indianapolis_Colts\" title=\"Indianapolis Colts\">Baltimore Colts</a> load its possessions onto fifteen <a href=\"https://wikipedia.org/wiki/Mayflower_Transit\" title=\"Mayflower Transit\">Mayflower</a> moving trucks in the early morning hours and <a href=\"https://wikipedia.org/wiki/Baltimore_Colts_relocation_to_Indianapolis\" title=\"Baltimore Colts relocation to Indianapolis\">transfer its operations</a> to <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>.", "links": [{"title": "Indianapolis Colts", "link": "https://wikipedia.org/wiki/Indianapolis_Colts"}, {"title": "Mayflower Transit", "link": "https://wikipedia.org/wiki/Mayflower_Transit"}, {"title": "Baltimore Colts relocation to Indianapolis", "link": "https://wikipedia.org/wiki/Baltimore_Colts_relocation_to_Indianapolis"}, {"title": "Indianapolis", "link": "https://wikipedia.org/wiki/Indianapolis"}]}, {"year": "1990", "text": "The Czechoslovak parliament is unable to reach an agreement on what to call the country after the fall of Communism, sparking the so-called Hyphen War.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovak</a> parliament is unable to reach an agreement on what to call the country after the fall of <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communism</a>, sparking the so-called <a href=\"https://wikipedia.org/wiki/Hyphen_War\" title=\"Hyphen War\">Hyphen War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovak</a> parliament is unable to reach an agreement on what to call the country after the fall of <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communism</a>, sparking the so-called <a href=\"https://wikipedia.org/wiki/Hyphen_War\" title=\"Hyphen War\">Hyphen War</a>.", "links": [{"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "Hyphen War", "link": "https://wikipedia.org/wiki/Hyphen_War"}]}, {"year": "1999", "text": "The Dow Jones Industrial Average closes above the 10,000 mark (10,006.78) for the first time, during the height of the dot-com bubble.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> closes above the 10,000 mark (10,006.78) for the first time, during the height of the <a href=\"https://wikipedia.org/wiki/Dot-com_bubble\" title=\"Dot-com bubble\">dot-com bubble</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> closes above the 10,000 mark (10,006.78) for the first time, during the height of the <a href=\"https://wikipedia.org/wiki/Dot-com_bubble\" title=\"Dot-com bubble\">dot-com bubble</a>.", "links": [{"title": "Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/<PERSON>_Jones_Industrial_Average"}, {"title": "Dot-com bubble", "link": "https://wikipedia.org/wiki/Dot-com_bubble"}]}, {"year": "1999", "text": "A magnitude 6.8 earthquake in India strikes the Chamoli district in Uttar Pradesh, killing 103.", "html": "1999 - A <a href=\"https://wikipedia.org/wiki/1999_Chamoli_earthquake\" title=\"1999 Chamoli earthquake\">magnitude 6.8 earthquake in India</a> strikes the <a href=\"https://wikipedia.org/wiki/Chamoli_district\" title=\"Chamoli district\">Chamoli district</a> in <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh\" title=\"Uttar Pradesh\">Uttar Pradesh</a>, killing 103.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1999_Chamoli_earthquake\" title=\"1999 Chamoli earthquake\">magnitude 6.8 earthquake in India</a> strikes the <a href=\"https://wikipedia.org/wiki/Chamoli_district\" title=\"Chamoli district\">Chamoli district</a> in <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh\" title=\"Uttar Pradesh\">Uttar Pradesh</a>, killing 103.", "links": [{"title": "1999 Chamoli earthquake", "link": "https://wikipedia.org/wiki/1999_Chamoli_earthquake"}, {"title": "Chamoli district", "link": "https://wikipedia.org/wiki/Chamoli_district"}, {"title": "Uttar Pradesh", "link": "https://wikipedia.org/wiki/Uttar_Pradesh"}]}, {"year": "2001", "text": "A Gulfstream III crashes on approach to Aspen/Pitkin County Airport in Aspen, Colorado. All 18 people on board are killed.", "html": "2001 - A <a href=\"https://wikipedia.org/wiki/Gulfstream_III\" title=\"Gulfstream III\">Gulfstream III</a> <a href=\"https://wikipedia.org/wiki/2001_Avjet_Gulfstream_III_crash\" title=\"2001 Avjet Gulfstream III crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Aspen/Pitkin_County_Airport\" title=\"Aspen/Pitkin County Airport\">Aspen/Pitkin County Airport</a> in <a href=\"https://wikipedia.org/wiki/Aspen,_Colorado\" title=\"Aspen, Colorado\">Aspen, Colorado</a>. All 18 people on board are killed.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Gulfstream_III\" title=\"Gulfstream III\">Gulfstream III</a> <a href=\"https://wikipedia.org/wiki/2001_Avjet_Gulfstream_III_crash\" title=\"2001 Avjet Gulfstream III crash\">crashes</a> on approach to <a href=\"https://wikipedia.org/wiki/Aspen/Pitkin_County_Airport\" title=\"Aspen/Pitkin County Airport\">Aspen/Pitkin County Airport</a> in <a href=\"https://wikipedia.org/wiki/Aspen,_Colorado\" title=\"Aspen, Colorado\">Aspen, Colorado</a>. All 18 people on board are killed.", "links": [{"title": "Gulfstream III", "link": "https://wikipedia.org/wiki/Gulfstream_III"}, {"title": "2001 Avjet Gulfstream III crash", "link": "https://wikipedia.org/wiki/2001_Avjet_Gulfstream_III_crash"}, {"title": "Aspen/Pitkin County Airport", "link": "https://wikipedia.org/wiki/Aspen/Pitkin_County_Airport"}, {"title": "Aspen, Colorado", "link": "https://wikipedia.org/wiki/Aspen,_Colorado"}]}, {"year": "2002", "text": "In reaction to the Passover massacre two days prior, Israel launches Operation Defensive Shield against Palestinian militants, its largest military operation in the West Bank since the 1967 Six-Day War.", "html": "2002 - In reaction to the <a href=\"https://wikipedia.org/wiki/Passover_massacre\" title=\"Passover massacre\">Passover massacre</a> two days prior, <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches <a href=\"https://wikipedia.org/wiki/Operation_Defensive_Shield\" title=\"Operation Defensive Shield\">Operation Defensive Shield</a> against <a href=\"https://wikipedia.org/wiki/Palestinian_political_violence\" title=\"Palestinian political violence\">Palestinian militants</a>, its largest military operation in the <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> since the 1967 <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>.", "no_year_html": "In reaction to the <a href=\"https://wikipedia.org/wiki/Passover_massacre\" title=\"Passover massacre\">Passover massacre</a> two days prior, <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches <a href=\"https://wikipedia.org/wiki/Operation_Defensive_Shield\" title=\"Operation Defensive Shield\">Operation Defensive Shield</a> against <a href=\"https://wikipedia.org/wiki/Palestinian_political_violence\" title=\"Palestinian political violence\">Palestinian militants</a>, its largest military operation in the <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> since the 1967 <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>.", "links": [{"title": "Passover massacre", "link": "https://wikipedia.org/wiki/Passover_massacre"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Operation Defensive Shield", "link": "https://wikipedia.org/wiki/Operation_Defensive_Shield"}, {"title": "Palestinian political violence", "link": "https://wikipedia.org/wiki/Palestinian_political_violence"}, {"title": "West Bank", "link": "https://wikipedia.org/wiki/West_Bank"}, {"title": "Six-Day War", "link": "https://wikipedia.org/wiki/Six-Day_War"}]}, {"year": "2004", "text": "Bulgaria, Estonia, Latvia, Lithuania, Romania, Slovakia, and Slovenia join NATO as full members.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> as full members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> as full members.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "2004", "text": "The Council on Tall Buildings and Urban Habitat certifies Taipei 101 as the world's tallest building, based on the building having been topped out on 1 July  2003, even though the building was not completed until 31 December 2004.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Council_on_Tall_Buildings_and_Urban_Habitat\" title=\"Council on Tall Buildings and Urban Habitat\">Council on Tall Buildings and Urban Habitat</a> certifies <a href=\"https://wikipedia.org/wiki/Taipei_101\" title=\"Taipei 101\">Taipei 101</a> as the world's tallest building, based on the building having been topped out on 1 July 2003, even though the building was not completed until 31 December 2004.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_on_Tall_Buildings_and_Urban_Habitat\" title=\"Council on Tall Buildings and Urban Habitat\">Council on Tall Buildings and Urban Habitat</a> certifies <a href=\"https://wikipedia.org/wiki/Taipei_101\" title=\"Taipei 101\">Taipei 101</a> as the world's tallest building, based on the building having been topped out on 1 July 2003, even though the building was not completed until 31 December 2004.", "links": [{"title": "Council on Tall Buildings and Urban Habitat", "link": "https://wikipedia.org/wiki/Council_on_Tall_Buildings_and_Urban_Habitat"}, {"title": "Taipei 101", "link": "https://wikipedia.org/wiki/Taipei_101"}]}, {"year": "2010", "text": "Two suicide bombers hit the Moscow Metro system at the peak of the morning rush hour, killing 40.", "html": "2010 - Two suicide bombers <a href=\"https://wikipedia.org/wiki/2010_Moscow_Metro_bombings\" title=\"2010 Moscow Metro bombings\">hit the Moscow Metro system</a> at the peak of the morning rush hour, killing 40.", "no_year_html": "Two suicide bombers <a href=\"https://wikipedia.org/wiki/2010_Moscow_Metro_bombings\" title=\"2010 Moscow Metro bombings\">hit the Moscow Metro system</a> at the peak of the morning rush hour, killing 40.", "links": [{"title": "2010 Moscow Metro bombings", "link": "https://wikipedia.org/wiki/2010_Moscow_Metro_bombings"}]}, {"year": "2013", "text": "At least 36 people are killed when a 16-floor building collapses in the commercial capital Dar es Salaam, Tanzania.", "html": "2013 - At least 36 people are killed when a 16-floor <a href=\"https://wikipedia.org/wiki/2013_Dar_es_Salaam_building_collapse\" title=\"2013 Dar es Salaam building collapse\">building collapses</a> in the commercial capital <a href=\"https://wikipedia.org/wiki/Dar_es_Salaam\" title=\"Dar es Salaam\">Dar es Salaam</a>, Tanzania.", "no_year_html": "At least 36 people are killed when a 16-floor <a href=\"https://wikipedia.org/wiki/2013_Dar_es_Salaam_building_collapse\" title=\"2013 Dar es Salaam building collapse\">building collapses</a> in the commercial capital <a href=\"https://wikipedia.org/wiki/Dar_es_Salaam\" title=\"Dar es Salaam\">Dar es Salaam</a>, Tanzania.", "links": [{"title": "2013 Dar es Salaam building collapse", "link": "https://wikipedia.org/wiki/2013_Dar_<PERSON>_<PERSON><PERSON>_building_collapse"}, {"title": "Dar es Salaam", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "The first same-sex marriages in England and Wales are performed.", "html": "2014 - The first <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_the_United_Kingdom\" title=\"Same-sex marriage in the United Kingdom\">same-sex marriages</a> in <a href=\"https://wikipedia.org/wiki/England_and_Wales\" title=\"England and Wales\">England and Wales</a> are performed.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_the_United_Kingdom\" title=\"Same-sex marriage in the United Kingdom\">same-sex marriages</a> in <a href=\"https://wikipedia.org/wiki/England_and_Wales\" title=\"England and Wales\">England and Wales</a> are performed.", "links": [{"title": "Same-sex marriage in the United Kingdom", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_the_United_Kingdom"}, {"title": "England and Wales", "link": "https://wikipedia.org/wiki/England_and_Wales"}]}, {"year": "2015", "text": "Air Canada Flight 624 skids off the runway at Halifax Stanfield International Airport, after arriving from Toronto shortly past midnight. All 133 passengers and five crews on board survive, with 23 treated for minor injuries.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_624\" title=\"Air Canada Flight 624\">Air Canada Flight 624</a> skids off the runway at <a href=\"https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport\" title=\"Halifax Stanfield International Airport\">Halifax Stanfield International Airport</a>, after arriving from <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a> shortly past midnight. All 133 passengers and five crews on board survive, with 23 treated for minor injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_624\" title=\"Air Canada Flight 624\">Air Canada Flight 624</a> skids off the runway at <a href=\"https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport\" title=\"Halifax Stanfield International Airport\">Halifax Stanfield International Airport</a>, after arriving from <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a> shortly past midnight. All 133 passengers and five crews on board survive, with 23 treated for minor injuries.", "links": [{"title": "Air Canada Flight 624", "link": "https://wikipedia.org/wiki/Air_Canada_Flight_624"}, {"title": "Halifax Stanfield International Airport", "link": "https://wikipedia.org/wiki/Halifax_Stanfield_International_Airport"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}]}, {"year": "2016", "text": "A United States Air Force F-16 crashes during takeoff from Bagram Airfield in Afghanistan.", "html": "2016 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon\" title=\"General Dynamics F-16 Fighting Falcon\">F-16</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bagram_Airfield\" title=\"Bagram Airfield\">Bagram Airfield</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "General Dynamics F-16 Fighting Falcon", "link": "https://wikipedia.org/wiki/General_Dynamics_F-16_Fighting_Falcon"}, {"title": "Bagram Airfield", "link": "https://wikipedia.org/wiki/Bagram_Airfield"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2017", "text": "Prime Minister <PERSON> invokes Article 50 of the Treaty on European Union, formally beginning the United Kingdom's withdrawal from the European Union.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a> <a href=\"https://wikipedia.org/wiki/United_Kingdom_invocation_of_Article_50_of_the_Treaty_on_European_Union\" title=\"United Kingdom invocation of Article 50 of the Treaty on European Union\">invokes Article 50</a> of the <a href=\"https://wikipedia.org/wiki/Treaty_on_European_Union\" title=\"Treaty on European Union\">Treaty on European Union</a>, formally beginning <a href=\"https://wikipedia.org/wiki/Brexit\" title=\"Brexit\">the United Kingdom's withdrawal</a> from the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a> <a href=\"https://wikipedia.org/wiki/United_Kingdom_invocation_of_Article_50_of_the_Treaty_on_European_Union\" title=\"United Kingdom invocation of Article 50 of the Treaty on European Union\">invokes Article 50</a> of the <a href=\"https://wikipedia.org/wiki/Treaty_on_European_Union\" title=\"Treaty on European Union\">Treaty on European Union</a>, formally beginning <a href=\"https://wikipedia.org/wiki/Brexit\" title=\"Brexit\">the United Kingdom's withdrawal</a> from the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}, {"title": "United Kingdom invocation of Article 50 of the Treaty on European Union", "link": "https://wikipedia.org/wiki/United_Kingdom_invocation_of_Article_50_of_the_Treaty_on_European_Union"}, {"title": "Treaty on European Union", "link": "https://wikipedia.org/wiki/Treaty_on_European_Union"}, {"title": "Brexit", "link": "https://wikipedia.org/wiki/Brexit"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2021", "text": "The ship <PERSON> was dislodged from the Suez Canal.", "html": "2021 - The ship <a href=\"https://wikipedia.org/wiki/Ever_Given\" title=\"Ever Given\">Ever Given</a> was <a href=\"https://wikipedia.org/wiki/2021_Suez_Canal_obstruction#Salvage_and_refloating\" title=\"2021 Suez Canal obstruction\">dislodged</a> from the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "no_year_html": "The ship <a href=\"https://wikipedia.org/wiki/Ever_Given\" title=\"Ever Given\">Ever Given</a> was <a href=\"https://wikipedia.org/wiki/2021_Suez_Canal_obstruction#Salvage_and_refloating\" title=\"2021 Suez Canal obstruction\">dislodged</a> from the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>.", "links": [{"title": "<PERSON> Given", "link": "https://wikipedia.org/wiki/Ever_Given"}, {"title": "2021 Suez Canal obstruction", "link": "https://wikipedia.org/wiki/2021_Suez_Canal_obstruction#Salvage_and_refloating"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}], "Births": [{"year": "1187", "text": "<PERSON>, Duke of Brittany, grandson of King <PERSON> of England (d. 1203)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a>, grandson of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1203)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a>, grandson of King <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1203)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_England"}]}, {"year": "1561", "text": "<PERSON><PERSON>, Italian biologist (d. 1636)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/Santorio_Santorio\" title=\"Santorio Santorio\"><PERSON><PERSON></a>, Italian biologist (d. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santorio_Santorio\" title=\"Santorio Santorio\"><PERSON><PERSON></a>, Italian biologist (d. 1636)", "links": [{"title": "Santorio Santorio", "link": "https://wikipedia.org/wiki/Santorio_Santorio"}]}, {"year": "1584", "text": "<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron, English general and politician (d. 1648)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron\" title=\"<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron\"><PERSON><PERSON>, 2nd Lord <PERSON> of Cameron</a>, English general and politician (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron\" title=\"<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron\"><PERSON><PERSON>, 2nd Lord <PERSON> of Cameron</a>, English general and politician (d. 1648)", "links": [{"title": "<PERSON><PERSON>, 2nd Lord <PERSON> of Cameron", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_2nd_Lord_<PERSON>_of_Cameron"}]}, {"year": "1602", "text": "<PERSON>, English priest, scholar, and academic (d. 1675)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, scholar, and academic (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, scholar, and academic (d. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, Irish politician (d. 1789)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish politician (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish politician (d. 1789)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1735", "text": "<PERSON>, German author (d. 1787)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A4us\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Mu<PERSON>%C3%A4us"}]}, {"year": "1747", "text": "<PERSON>, German pianist and composer (d. 1822)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4ssler"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French general and politician, 12th Prime Minister of France (d. 1851)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON>, Danish adventurer (d. 1841)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_J%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish adventurer (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_J%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish adventurer (d. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8rgen_J%C3%B8<PERSON><PERSON>"}]}, {"year": "1790", "text": "<PERSON>, American lawyer and politician, 10th President of the United States (d. 1862)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1799", "text": "<PERSON>, 14th Earl of Derby, English politician, Prime Minister of the United Kingdom (d. 1869)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_<PERSON>_Derby\" title=\"<PERSON>, 14th Earl of Derby\"><PERSON>, 14th Earl of Derby</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_14th_Earl_of_Derby\" title=\"<PERSON>, 14th Earl of Derby\"><PERSON>, 14th Earl of Derby</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1869)", "links": [{"title": "<PERSON>, 14th Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Earl_of_Derby"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1802", "text": "<PERSON>, German landscape painter (d. 1858)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German landscape painter (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German landscape painter (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, German physiologist, physician, and philosopher (d. 1899)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist, physician, and philosopher (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist, physician, and philosopher (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON>ner"}]}, {"year": "1826", "text": "<PERSON>, German journalist and politician (d. 1900)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, English-American engineer and inventor (d. 1937)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American engineer and inventor (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American engineer and inventor (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thomson"}]}, {"year": "1860", "text": "<PERSON>, New Zealand zoologist (d. 1950)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, New Zealand zoologist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(zoologist)\" title=\"<PERSON> (zoologist)\"><PERSON></a>, New Zealand zoologist (d. 1950)", "links": [{"title": "<PERSON> (zoologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(zoologist)"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Swiss-American painter (d. 1947)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller-Ury\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American painter (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss-American painter (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_M%C3%BCller-Ury"}]}, {"year": "1863", "text": "<PERSON>, Australian politician, 5th Premier of Western Australia (d. 1943)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1943)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1867", "text": "<PERSON>, American baseball player and manager (d. 1955)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, British architect (d. 1944)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British architect (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British architect (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, English cricketer (d. 1939)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English-Australian politician, 12th Premier of Western Australia (d. 1953)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hal_<PERSON>ch"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian mathematician and academic (d. 1941)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mathematician and academic (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American philanthropist and geologist, 33rd First Lady of the United States (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and geologist, 33rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and geologist, 33rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1883", "text": "<PERSON>, Dutch-American biochemist (d. 1971)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American biochemist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American biochemist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author and poet (d. 1936)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Kosztol%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Kosztol%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON><PERSON>sztolányi\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dezs%C5%91_Kosztol%C3%A1nyi"}]}, {"year": "1889", "text": "<PERSON>, American actor (d. 1951)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Baxter\"><PERSON></a>, American actor (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Baxter"}]}, {"year": "1889", "text": "<PERSON>, American producer, playwright, librettist, director and actor (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer, playwright, librettist, director and actor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer, playwright, librettist, director and actor (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English astronomer (d. 1960)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON>, French-German poet and playwright (d. 1950)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-German poet and playwright (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-German poet and playwright (d. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian cardinal (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty"}]}, {"year": "1895", "text": "<PERSON>, German philosopher and author (d. 1998)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_J%C3%BCnger"}]}, {"year": "1896", "text": "<PERSON>, German mathematician (d. 1962)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Georgian-Russian general and politician (d. 1953)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian-Russian general and politician (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, Georgian-Russian general and politician (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavrentiy_Beria"}]}, {"year": "1900", "text": "<PERSON>, Australian farmer and politician, 18th Prime Minister of Australia (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1900", "text": "<PERSON>, English zoologist and animal ecologist (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and animal ecologist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and animal ecologist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French author, playwright, and screenwriter (d. 1967)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author, playwright, and screenwriter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French author, playwright, and screenwriter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Aym%C3%A9"}]}, {"year": "1902", "text": "<PERSON>, English composer (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Canadian colonel and politician, Canadian Minister of National Defence (d. 1999)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)\" title=\"Minister of National Defence (Canada)\">Canadian Minister of National Defence</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ness"}, {"title": "Minister of National Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_National_Defence_(Canada)"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian singer-songwriter and producer (d. 2006)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and producer (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(composer)"}]}, {"year": "1908", "text": "<PERSON>, American actor (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_O%27Connell"}]}, {"year": "1908", "text": "<PERSON>, American actor and screenwriter (d. 1968)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Keefe\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Keefe"}]}, {"year": "1909", "text": "<PERSON>, American singer-songwriter and pianist (d. 1967)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mullican\" title=\"<PERSON> Mullican\"><PERSON></a>, American singer-songwriter and pianist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mullican\" title=\"Moon Mullican\"><PERSON></a>, American singer-songwriter and pianist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Moon_<PERSON>can"}]}, {"year": "1912", "text": "<PERSON>, German soldier and pilot (d. 1979)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, British trade union leader, General Secretary of the Transport and General Workers' Union (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, British trade union leader, General Secretary of the <a href=\"https://wikipedia.org/wiki/Transport_and_General_Workers%27_Union\" title=\"Transport and General Workers' Union\">Transport and General Workers' Union</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, British trade union leader, General Secretary of the <a href=\"https://wikipedia.org/wiki/Transport_and_General_Workers%27_Union\" title=\"Transport and General Workers' Union\">Transport and General Workers' Union</a> (d. 2009)", "links": [{"title": "<PERSON> (trade unionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)"}, {"title": "Transport and General Workers' Union", "link": "https://wikipedia.org/wiki/Transport_and_General_Workers%27_Union"}]}, {"year": "1914", "text": "<PERSON>, Indian-English historian, journalist, and author (d. 2014)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pincher\"><PERSON></a>, Indian-English historian, journalist, and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pincher\"><PERSON></a>, Indian-English historian, journalist, and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1916", "text": "<PERSON>, English philosopher and academic (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American poet and politician (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Welsh scientist and nuclear researcher (d. 1988)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>euan_<PERSON>\" title=\"Ieuan <PERSON>dock\"><PERSON><PERSON><PERSON></a>, Welsh scientist and nuclear researcher (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>euan_<PERSON>\" title=\"Ieuan Maddock\"><PERSON><PERSON><PERSON></a>, Welsh scientist and nuclear researcher (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ieuan_<PERSON>dock"}]}, {"year": "1918", "text": "<PERSON>, American actress and singer (d. 1990)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Vietnamese mathematician and academic (d. 1991)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Thi%C3%AAm\" title=\"Lê Văn <PERSON>ê<PERSON>\"><PERSON><PERSON></a>, Vietnamese mathematician and academic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Thi%C3%AAm\" title=\"Lê Văn <PERSON>hiê<PERSON>\"><PERSON><PERSON></a>, Vietnamese mathematician and academic (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%AA_V%C4%83n_Thi%C3%AAm"}]}, {"year": "1918", "text": "<PERSON>, American businessman, founded Walmart and Sam's Club (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Walmart\" title=\"Walmart\">Walmart</a> and <a href=\"https://wikipedia.org/wiki/Sam%27s_Club\" title=\"Sam's Club\">Sam's Club</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Walmart\" title=\"Walmart\">Walmart</a> and <a href=\"https://wikipedia.org/wiki/Sam%27s_Club\" title=\"Sam's Club\">Sam's Club</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Walmart", "link": "https://wikipedia.org/wiki/Walmart"}, {"title": "Sam's Club", "link": "https://wikipedia.org/wiki/Sam%27s_Club"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American businessman and politician (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American-Canadian geneticist and academic (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian geneticist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian geneticist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French author (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and judge (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian cricketer, footballer, and politician (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, footballer, and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English-Manx motorcycle racer (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Manx motorcycle racer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Manx motorcycle racer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American lawyer and judge (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Russian physicist (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, British chemist (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British chemist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American journalist and producer (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, American journalist and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, American journalist and producer (d. 2016)", "links": [{"title": "<PERSON> (host)", "link": "https://wikipedia.org/wiki/<PERSON>_(host)"}]}, {"year": "1927", "text": "<PERSON>, English pharmacologist and academic, Nobel Prize laureate (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Pakistani-Indian politician, 13th Foreign Secretary of India (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian politician, 13th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-Indian politician, 13th <a href=\"https://wikipedia.org/wiki/Foreign_Secretary_(India)\" title=\"Foreign Secretary (India)\">Foreign Secretary of India</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ari"}, {"title": "Foreign Secretary (India)", "link": "https://wikipedia.org/wiki/Foreign_Secretary_(India)"}]}, {"year": "1928", "text": "<PERSON>, Belgian-American journalist, author, and academic (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American journalist, author, and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American journalist, author, and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American boxer and mobster (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and mobster (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and mobster (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English activist, author, and academic (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, author, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, author, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American biologist, geneticist, and academic (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Estonian director and politician, 2nd President of Estonia (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri"}, {"title": "President of Estonia", "link": "https://wikipedia.org/wiki/President_of_Estonia"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian actor, director and playwright (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>t<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director and playwright (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>t<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor, director and playwright (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Utpal_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Mauritian lawyer and politician, 4th President of Mauritius (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mauritian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Mauritius\" title=\"President of Mauritius\">President of Mauritius</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mauritian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Mauritius\" title=\"President of Mauritius\">President of Mauritius</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anerood_<PERSON>"}, {"title": "President of Mauritius", "link": "https://wikipedia.org/wiki/President_of_Mauritius"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Russian general, pilot and cosmonaut (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Aleksei_Gubarev\" title=\"Aleksei Gubarev\">Alek<PERSON> Gubarev</a>, Russian general, pilot and cosmonaut (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksei_Gubarev\" title=\"Aleksei Gubarev\"><PERSON><PERSON><PERSON>uba<PERSON></a>, Russian general, pilot and cosmonaut (d. 2015)", "links": [{"title": "Aleksei Gubarev", "link": "https://wikipedia.org/wiki/Aleksei_Gubarev"}]}, {"year": "1931", "text": "<PERSON>, English journalist and politician, Chancellor of the Duchy of Lancaster", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1935", "text": "<PERSON>, Northern Irish singer (d. 1996)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish singer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Murray"}]}, {"year": "1936", "text": "<PERSON>, English-American composer and educator (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and educator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American composer and educator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 48th Governor of Missouri (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Missouri\" class=\"mw-redirect\" title=\"Governor of Missouri\">Governor of Missouri</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Missouri\" class=\"mw-redirect\" title=\"Governor of Missouri\">Governor of Missouri</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Missouri", "link": "https://wikipedia.org/wiki/Governor_of_Missouri"}]}, {"year": "1937", "text": "<PERSON>, Filipino painter and sculptor (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter and sculptor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino painter and sculptor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Haitian businessman and politician, 6th Prime Minister of Haiti (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "1937", "text": "<PERSON>, English footballer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French-American businessman and diplomat, 63rd United States Ambassador to the Netherlands (d. 2008)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-American businessman and diplomat, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands\" class=\"mw-redirect\" title=\"United States Ambassador to the Netherlands\">United States Ambassador to the Netherlands</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French-American businessman and diplomat, 63rd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands\" class=\"mw-redirect\" title=\"United States Ambassador to the Netherlands\">United States Ambassador to the Netherlands</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the Netherlands", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 2006)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American bass singer (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass singer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass singer (d. 2005)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON>, American actor (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1943", "text": "<PERSON>, English banker and politician, Prime Minister of the United Kingdom", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Major\"><PERSON></a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Greek keyboard player and songwriter (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek keyboard player and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek keyboard player and songwriter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor, comedian, musician and writer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, musician and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian, musician and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, English singer-songwriter, keyboard player, and producer (d. 2002)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Speedy_Keen\" title=\"Speedy Keen\"><PERSON><PERSON></a>, English singer-songwriter, keyboard player, and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Speedy_Keen\" title=\"Speedy Keen\"><PERSON><PERSON></a>, English singer-songwriter, keyboard player, and producer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Speedy_Keen"}]}, {"year": "1946", "text": "<PERSON>, English-Australian singer-songwriter, guitarist, and producer (d. 2007)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter, guitarist, and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter, guitarist, and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American academic (d. 2007)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer and actor (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and actor (d. 2022)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1948", "text": "<PERSON>, American author and educator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American saxophonist and composer (d. 2007)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and writer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Israeli archaeologist and professor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Israel_Finkelstein\" title=\"Israel Finkelstein\"><PERSON></a>, Israeli archaeologist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Finkelstein\" title=\"Israel Finkelstein\"><PERSON></a>, Israeli archaeologist and professor", "links": [{"title": "Israel Finkelstein", "link": "https://wikipedia.org/wiki/Israel_Finkelstein"}]}, {"year": "1949", "text": "<PERSON>, English musician (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian social worker and politician, 30th Premier of Quebec", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian social worker and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian social worker and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1949", "text": "<PERSON>, American murderer (d. 1979)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Guinean vocalist (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guinean vocalist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guinean vocalist (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Kant%C3%A9"}]}, {"year": "1951", "text": "<PERSON>, Canadian computer scientist, mathematician and businessman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian computer scientist, mathematician and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian computer scientist, mathematician and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American harmonica player (d. 1996)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American harmonica player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American harmonica player (d. 1996)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1951", "text": "<PERSON>, American economist and professor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and professor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Vietnamese-American photographer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban boxer and engineer (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Te%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban boxer and engineer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Te%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban boxer and engineer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te%C3%B3fi<PERSON>_Stevenson"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Nigerian politician, President-elect of Nigeria", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian politician, <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President-elect of Nigeria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian politician, <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President-elect of Nigeria</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}]}, {"year": "1952", "text": "<PERSON>, American writer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American football player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American historian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, American legal scholar", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_She<PERSON>\" title=\"<PERSON><PERSON><PERSON> She<PERSON>\"><PERSON><PERSON><PERSON></a>, American legal scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_She<PERSON>\" title=\"<PERSON><PERSON><PERSON> She<PERSON>\"><PERSON><PERSON><PERSON></a>, American legal scholar", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American writer and editor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and editor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and editor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American football player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American poet", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Irish actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, British-American actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Sirtis"}]}, {"year": "1956", "text": "<PERSON>, American singer (d. 1996)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American writer and inventor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and inventor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American gymnast (d. 2020)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(gymnast)\" title=\"<PERSON> (gymnast)\"><PERSON></a>, American gymnast (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(gymnast)\" title=\"<PERSON> (gymnast)\"><PERSON></a>, American gymnast (d. 2020)", "links": [{"title": "<PERSON> (gymnast)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(gymnast)"}]}, {"year": "1957", "text": "<PERSON>, American author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth Hand\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth Hand\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, British writer, journalist and art critic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, British writer, journalist and art critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, British writer, journalist and art critic", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1957", "text": "<PERSON>, American-French actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American theologian", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American businessman and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Iranian-American economic consultant, economist and writer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American economic consultant, economist and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American economic consultant, economist and writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2011)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Norwegian writer, musician and football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Norwegian writer, musician and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Norwegian writer, musician and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo_Nesb%C3%B8"}]}, {"year": "1961", "text": "<PERSON>, American economist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian poet and novelist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress and comedian", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English director and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Ukrainian-American theoretical physicist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American theoretical physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American theoretical physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American writer, historian and educator", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American writer, historian and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American writer, historian and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American attorney and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian model and actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American poet and critic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American writer and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ida<PERSON>\"><PERSON><PERSON></a>, American writer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Halliday"}]}, {"year": "1965", "text": "<PERSON>, American novelist, screenwriter and illustrator", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American journalist and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Bradford_Tatum\" title=\"<PERSON> Tatum\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bradford_Tatum\" title=\"Bradford Tatum\"><PERSON></a>, American actor", "links": [{"title": "Bradford Tatum", "link": "https://wikipedia.org/wiki/Bradford_Tatum"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American football player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Bolivian writer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>d%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bolivian writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Sold%C3%A1n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bolivian writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edmund<PERSON>_Paz_Sold%C3%A1n"}]}, {"year": "1968", "text": "<PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American politician and AFRC colonel", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and <a href=\"https://wikipedia.org/wiki/Air_Force_Reserve_Command\" title=\"Air Force Reserve Command\">AFRC</a> colonel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and <a href=\"https://wikipedia.org/wiki/Air_Force_Reserve_Command\" title=\"Air Force Reserve Command\">AFRC</a> colonel", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Air Force Reserve Command", "link": "https://wikipedia.org/wiki/Air_Force_Reserve_Command"}]}, {"year": "1969", "text": "<PERSON>, American football player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1971", "text": "<PERSON>, American political adviser, 28th White House Press Secretary", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political adviser, 28th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political adviser, 28th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1971", "text": "<PERSON>, South African television and radio journalist and war correspondent", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African television and radio journalist and war correspondent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African television and radio journalist and war correspondent", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1972", "text": "<PERSON>, American novelist, poet and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/St<PERSON>_<PERSON>t\" title=\"<PERSON><PERSON>t\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St<PERSON>_Le<PERSON>t\" title=\"St<PERSON> Leicht\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, British Indian politician, Secretary of State for the Home Department", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British Indian politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Home_Department\" class=\"mw-redirect\" title=\"Secretary of State for the Home Department\">Secretary of State for the Home Department</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British Indian politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Home_Department\" class=\"mw-redirect\" title=\"Secretary of State for the Home Department\">Secretary of State for the Home Department</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for the Home Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Home_Department"}]}, {"year": "1973", "text": "<PERSON>, Dutch footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Cuban-Canadian singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Alex_Cuba\" title=\"Alex Cuba\">Alex Cuba</a>, Cuban-Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alex_Cuba\" title=\"Alex Cuba\"><PERSON></a>, Cuban-Canadian singer-songwriter", "links": [{"title": "Alex <PERSON>", "link": "https://wikipedia.org/wiki/Alex_Cuba"}]}, {"year": "1976", "text": "<PERSON>, American tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American writer and poet (d. 2017)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and poet (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Zimbabwean writer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Holding\"><PERSON></a>, Zimbabwean writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Cuban boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cuban_boxer)\" title=\"<PERSON> (Cuban boxer)\"><PERSON></a>, Cuban boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cuban_boxer)\" title=\"<PERSON> (Cuban boxer)\"><PERSON></a>, Cuban boxer", "links": [{"title": "<PERSON> (Cuban boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cuban_boxer)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON> bin <PERSON>, Jordanian prince", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Jordanian prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Jordanian prince", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American poet and writer (d. 2020)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and writer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and writer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American stand-up comedian, actor and writer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Elia\" title=\"<PERSON>\"><PERSON></a>, American stand-up comedian, actor and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Elia\" title=\"<PERSON>\"><PERSON></a>, American stand-up comedian, actor and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chris_<PERSON>%27Elia"}]}, {"year": "1980", "text": "<PERSON>, American skier", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American attorney and politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lty"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American musician, singer, songwriter and record producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer, songwriter and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Trinidadian footballer (d. 2018)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian footballer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian footballer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek-Cypriot footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Efstathi<PERSON>_<PERSON>ftis\" title=\"<PERSON>fstathi<PERSON>ft<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek-Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Efstathi<PERSON>_<PERSON>ftis\" title=\"<PERSON>fstathi<PERSON> Aloneft<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek-Cypriot footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efstathios_Aloneftis"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American attorney, activist and politician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>_<PERSON>tar_Lumum<PERSON>\" title=\"Chok<PERSON> Antar <PERSON>\"><PERSON><PERSON><PERSON></a>, American attorney, activist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>_<PERSON>tar_<PERSON>\" title=\"Chokwe Antar Lu<PERSON>\"><PERSON><PERSON><PERSON></a>, American attorney, activist and politician", "links": [{"title": "Chokwe Antar <PERSON>", "link": "https://wikipedia.org/wiki/Chokwe_Antar_Lumumba"}]}, {"year": "1985", "text": "<PERSON>, Venezuelan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>-<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>yl<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Sylvan_Ebanks-Blake"}]}, {"year": "1986", "text": "<PERSON>, American actor and director", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean idol, actress and television host", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean <a href=\"https://wikipedia.org/wiki/Korean_pop_idol\" class=\"mw-redirect\" title=\"Korean pop idol\">idol</a>, actress and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South Korean <a href=\"https://wikipedia.org/wiki/Korean_pop_idol\" class=\"mw-redirect\" title=\"Korean pop idol\">idol</a>, actress and television host", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}, {"title": "Korean pop idol", "link": "https://wikipedia.org/wiki/Korean_pop_idol"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/N%27Golo_Kant%C3%A9\" title=\"<PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%27Golo_Kant%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%27Golo_Kant%C3%A9"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hazard\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hazard\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zard"}]}, {"year": "1994", "text": "<PERSON>-won, South Korean rapper, singer-songwriter, and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/One_(rapper)\" title=\"One (rapper)\"><PERSON></a>, South Korean rapper, singer-songwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/One_(rapper)\" title=\"One (rapper)\"><PERSON></a>, South Korean rapper, singer-songwriter, and actor", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, South Korean footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>an\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>an\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>an"}]}], "Deaths": [{"year": "500", "text": "<PERSON><PERSON><PERSON><PERSON>, Welsh king and religious figure", "html": "500 - <a href=\"https://wikipedia.org/wiki/AD_500\" title=\"AD 500\">500</a> - <a href=\"https://wikipedia.org/wiki/Gwynllyw\" title=\"Gwynllyw\"><PERSON><PERSON><PERSON><PERSON></a>, Welsh king and religious figure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AD_500\" title=\"AD 500\">500</a> - <a href=\"https://wikipedia.org/wiki/Gwynllyw\" title=\"Gwynlly<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Welsh king and religious figure", "links": [{"title": "AD 500", "link": "https://wikipedia.org/wiki/AD_500"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gwynllyw"}]}, {"year": "1058", "text": "<PERSON> (b. 1020)", "html": "1058 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON> IX</a> (b. 1020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> IX</a> (b. 1020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1461", "text": "<PERSON>, 3rd Earl of Northumberland, English politician (b. 1421)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland\" title=\"<PERSON>, 3rd Earl of Northumberland\"><PERSON>, 3rd Earl of Northumberland</a>, English politician (b. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland\" title=\"<PERSON>, 3rd Earl of Northumberland\"><PERSON>, 3rd Earl of Northumberland</a>, English politician (b. 1421)", "links": [{"title": "<PERSON>, 3rd Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Northumberland"}]}, {"year": "1461", "text": "<PERSON>, 6th Baron <PERSON> (c. 1406)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a> (c. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a> (c. 1406)", "links": [{"title": "<PERSON>, 6th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>"}]}, {"year": "1628", "text": "<PERSON>, English archbishop and academic (b. 1546)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop and academic (b. 1546)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON>, Dutch painter and engraver (b. 1565)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, Dutch painter and engraver (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>, Dutch painter and engraver (b. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON><PERSON>, Danish-German organist, violinist, and composer (b. 1665)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-German organist, violinist, and composer (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-German organist, violinist, and composer (b. 1665)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, Margrave of Brandenburg-Ansbach, (b. 1678)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON>, Margrave of Brandenburg-Ansbach</a>, (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach\" class=\"mw-redirect\" title=\"<PERSON>, Margrave of Brandenburg-Ansbach\"><PERSON>, Margrave of Brandenburg-Ansbach</a>, (b. 1678)", "links": [{"title": "<PERSON>, Margrave of Brandenburg-Ansbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Ansbach"}]}, {"year": "1751", "text": "<PERSON>, English captain and philanthropist, founded Foundling Hospital (b. 1668)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Foundling_Hospital\" title=\"Foundling Hospital\">Foundling Hospital</a> (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Foundling_Hospital\" title=\"Foundling Hospital\">Foundling Hospital</a> (b. 1668)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foundling Hospital", "link": "https://wikipedia.org/wiki/Foundling_Hospital"}]}, {"year": "1772", "text": "<PERSON>, Swedish astronomer, philosopher, and theologian (b. 1688)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, philosopher, and theologian (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish astronomer, philosopher, and theologian (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emanuel_<PERSON>borg"}]}, {"year": "1777", "text": "<PERSON>, Prussian physician and chemist (b. 1692)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian physician and chemist (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian physician and chemist (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, English missionary and poet (b. 1707)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and poet (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and poet (b. 1707)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Swedish king (b. 1746)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Swedish king (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"Gustav III\"><PERSON></a>, Swedish king (b. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, marquis <PERSON>, French general and engineer (b. 1714)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, marquis de Montale<PERSON>\"><PERSON>, marquis de <PERSON></a>, French general and engineer (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>\" title=\"<PERSON>, marquis de Montale<PERSON>t\"><PERSON>, marquis de <PERSON></a>, French general and engineer (b. 1714)", "links": [{"title": "<PERSON>, marquis de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9,_marquis_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON>, Dutch-Austrian librarian and diplomat (b. 1733)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-Austrian librarian and diplomat (b. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-Austrian librarian and diplomat (b. 1733)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, German pianist and composer (b. 1747)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (b. 1747)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4ssler"}]}, {"year": "1824", "text": "<PERSON>, Norwegian lay minister, social reformer and author (b. 1771)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian lay minister, social reformer and author (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian lay minister, social reformer and author (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, German poet, translator and academic (b. 1751)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, translator and academic (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet, translator and academic (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, English geographer, historian and oceanography pioneer (b. 1742)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer, historian and oceanography pioneer (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geographer, historian and oceanography pioneer (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, German-American businessman (b. 1763)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, English priest and poet (b. 1792)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and poet (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and poet (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON><PERSON>, French pianist and composer (b. 1813)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (b. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, French painter (b. 1859)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American business executive (b. 1839)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American business executive (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American business executive (b. 1839)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Croatian painter (b. 1878)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Slava_Ra%C5%A1kaj\" title=\"<PERSON><PERSON> Raškaj\"><PERSON><PERSON></a>, Croatian painter (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slava_Ra%C5%A1kaj\" title=\"<PERSON><PERSON> Raškaj\"><PERSON><PERSON></a>, Croatian painter (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slava_Ra%C5%A1kaj"}]}, {"year": "1911", "text": "<PERSON>, French organist and composer (b. 1837)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Scottish lieutenant and explorer (b. 1883)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lieutenant and explorer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lieutenant and explorer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English lieutenant and explorer (b. 1868)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and explorer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and explorer (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English physician and explorer (b. 1872)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physician and explorer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physician and explorer (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American illustrator and caricaturist (b. 1856)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator and caricaturist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American illustrator and caricaturist (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American naturalist and nature essayist (b. 1837)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naturalist and nature essayist (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American naturalist and nature essayist (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Irish composer and conductor (b. 1852)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German-American banker and philanthropist (b. 1867)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American banker and philanthropist (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American banker and philanthropist (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Polish pianist and composer (b. 1882)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Russian-English rugby player and soldier (b. 1916)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English rugby player and soldier (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-English rugby player and soldier (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English parapsychologist and author (b. 1881)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English parapsychologist and author (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English parapsychologist and author (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish politician (b. 1882)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Ki<PERSON><PERSON>\" title=\"<PERSON>ä<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON><PERSON><PERSON>\" title=\"<PERSON>ä<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON><PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Anglo-Irish novelist (b. 1888)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish novelist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish novelist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, African priest and politician, 1st Prime Minister of the Central African Republic (b. 1910)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_Boganda\" title=\"Bart<PERSON><PERSON><PERSON>y Boganda\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, African priest and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire\" class=\"mw-redirect\" title=\"List of heads of state of the Central African Republic and Central African Empire\">Prime Minister of the Central African Republic</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_Boganda\" title=\"Bart<PERSON>é<PERSON>y Boganda\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, African priest and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire\" class=\"mw-redirect\" title=\"List of heads of state of the Central African Republic and Central African Empire\">Prime Minister of the Central African Republic</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barth%C3%A9lemy_Boganda"}, {"title": "List of heads of state of the Central African Republic and Central African Empire", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Central_African_Republic_and_Central_African_Empire"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Canadian dentist and politician, 19th Lieutenant Governor of Quebec (b. 1898)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dentist and politician, 19th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian dentist and politician, 19th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fauteux"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1963", "text": "<PERSON>, American author and librarian (b. 1872)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and librarian (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and librarian (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek Army officer and Prime Minister of Greece (b. 1876)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Stylianos_Gonatas\" title=\"Stylianos Gonatas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Army officer and Prime Minister of Greece (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stylianos_Gonatas\" title=\"Stylianos Gonatas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Army officer and Prime Minister of Greece (b. 1876)", "links": [{"title": "Styl<PERSON>s <PERSON>", "link": "https://wikipedia.org/wiki/Stylianos_Gonatas"}]}, {"year": "1970", "text": "<PERSON>, American journalist and author (b. 1885)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Pakistani lawyer and politician (b. 1886)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani lawyer and politician (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English businessman, founded Rank Organisation (b. 1888)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rank_Organisation\" class=\"mw-redirect\" title=\"Rank Organisation\">Rank Organisation</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Rank_Organisation\" class=\"mw-redirect\" title=\"Rank Organisation\">Rank Organisation</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Rank Organisation", "link": "https://wikipedia.org/wiki/Rank_Organisation"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Greece footballer (b. 1927)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greece footballer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greece footballer (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Trinidadian historian and politician, 1st Prime Minister of Trinidad and Tobago (b. 1911)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian historian and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago"}]}, {"year": "1982", "text": "<PERSON>, German academic and politician, 1st President of the European Commission (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>stein"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1982", "text": "<PERSON>, British organic chemist (b. 1897)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British organic chemist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British organic chemist (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German composer and educator (b. 1895)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American general (b. 1897)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Twining\"><PERSON></a>, American general (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ing"}]}, {"year": "1985", "text": "<PERSON>, American physician and academic, 9th Surgeon General of the United States (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 9th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 9th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "1985", "text": "<PERSON>, British geologist (b. 1923)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian composer and conductor (b. 1914)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian composer and conductor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Canadian composer and conductor (b. 1914)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1988", "text": "<PERSON>, American baseball player and coach (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French photographer (b. 1928)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1908)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English actor, director, and screenwriter (b. 1922)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American illustrator (b. 1916)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mort_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American baseball player and coach (b. 1912)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (b. 1912)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player (b. 1944)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, British biochemist and virologist (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biochemist and virologist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biochemist and virologist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American jazz singer (b. 1918)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)\" title=\"<PERSON> (jazz singer)\"><PERSON></a>, American jazz singer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)\" title=\"<PERSON> (jazz singer)\"><PERSON></a>, American jazz singer (b. 1918)", "links": [{"title": "<PERSON> (jazz singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Norwegian lawyer, academic, and explorer (b. 1899)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer, academic, and explorer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer, academic, and explorer (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American pianist and composer (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer (b. 1920)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pianist)"}]}, {"year": "2003", "text": "<PERSON>, Italian physician and microbiologist (b. 1956)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and microbiologist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and microbiologist (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Mauritian-born SOE agent (b. 1905)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Lise_de_Baissac\" title=\"Li<PERSON> de Baissac\"><PERSON><PERSON> Baissac</a>, <PERSON><PERSON><PERSON>-born <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lise_de_Baissac\" title=\"<PERSON><PERSON> Baissac\"><PERSON><PERSON> Baissac</a>, <PERSON><PERSON>tian-born <a href=\"https://wikipedia.org/wiki/Special_Operations_Executive\" title=\"Special Operations Executive\">SOE</a> agent (b. 1905)", "links": [{"title": "Lise de Baissac", "link": "https://wikipedia.org/wiki/Lise_de_Baissac"}, {"title": "Special Operations Executive", "link": "https://wikipedia.org/wiki/Special_Operations_Executive"}]}, {"year": "2004", "text": "<PERSON>, American philosopher and academic (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Mexican author and poet (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Salvador_Elizondo\" title=\"<PERSON> Eli<PERSON>do\"><PERSON></a>, Mexican author and poet (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Elizondo\" title=\"Salvador Eli<PERSON>do\"><PERSON></a>, Mexican author and poet (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Elizondo"}]}, {"year": "2007", "text": "<PERSON>, English rugby player and soldier (b. 1934)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estrange\" title=\"<PERSON>\"><PERSON></a>, English rugby player and soldier (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estrange\" title=\"<PERSON>\"><PERSON></a>, English rugby player and soldier (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estrange"}]}, {"year": "2009", "text": "<PERSON>, Russian footballer and manager (b. 1943)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>v"}]}, {"year": "2009", "text": "<PERSON>, American actor and singer (b. 1975)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Portuguese painter and sculptor (b. 1938)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/%C3%82ngelo_de_Sousa\" title=\"<PERSON><PERSON><PERSON> de Sousa\"><PERSON><PERSON><PERSON> Sousa</a>, Portuguese painter and sculptor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%82ngelo_de_Sousa\" title=\"<PERSON><PERSON><PERSON> de Sousa\"><PERSON><PERSON><PERSON> Sousa</a>, Portuguese painter and sculptor (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON> de Sousa", "link": "https://wikipedia.org/wiki/%C3%82nge<PERSON>_de_Sousa"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek author, poet, playwright, and screenwriter (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek author, poet, playwright, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek author, poet, playwright, and screenwriter (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Gambian lawyer and politician, 8th Attorney General of the Gambia (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Gambian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_the_Gambia\" title=\"Attorney General of the Gambia\">Attorney General of the Gambia</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Gambian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Attorney_General_of_the_Gambia\" title=\"Attorney General of the Gambia\">Attorney General of the Gambia</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}, {"title": "Attorney General of the Gambia", "link": "https://wikipedia.org/wiki/Attorney_General_of_the_Gambia"}]}, {"year": "2012", "text": "<PERSON>, American race car driver and engineer (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)\" title=\"<PERSON> (drag racer)\"><PERSON></a>, American race car driver and engineer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)\" title=\"<PERSON> (drag racer)\"><PERSON></a>, American race car driver and engineer (b. 1930)", "links": [{"title": "<PERSON> (drag racer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drag_racer)"}]}, {"year": "2013", "text": "<PERSON>, Irish-French painter (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Irish-French painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Irish-French painter (b. 1930)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(artist)"}]}, {"year": "2013", "text": "<PERSON>, English-Canadian journalist and actor (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian journalist and politician, 12th Premier of Alberta (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "2013", "text": "<PERSON>, Canadian businessman and politician, 32nd Mayor of Vancouver (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Vancouver\" class=\"mw-redirect\" title=\"Mayor of Vancouver\">Mayor of Vancouver</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Vancouver\" class=\"mw-redirect\" title=\"Mayor of Vancouver\">Mayor of Vancouver</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Phillips"}, {"title": "Mayor of Vancouver", "link": "https://wikipedia.org/wiki/Mayor_of_Vancouver"}]}, {"year": "2014", "text": "<PERSON>, American actor and dancer (b. 1913)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American actor and dancer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American actor and dancer (b. 1913)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}]}, {"year": "2015", "text": "<PERSON>, Australian-English painter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English painter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English painter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actress (b. 1946)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Duke\"><PERSON></a>, American actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Duke\"><PERSON></a>, American actress (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Russian physicist, 2003 Nobel laureate in Physics (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Russian physicist, 2003 <a href=\"https://wikipedia.org/wiki/List_of_Nobel_laureates_in_Physics\" title=\"List of Nobel laureates in Physics\">Nobel laureate in Physics</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, Russian physicist, 2003 <a href=\"https://wikipedia.org/wiki/List_of_Nobel_laureates_in_Physics\" title=\"List of Nobel laureates in Physics\">Nobel laureate in Physics</a> (b. 1928)", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physicist)"}, {"title": "List of Nobel laureates in Physics", "link": "https://wikipedia.org/wiki/List_of_Nobel_laureates_in_Physics"}]}, {"year": "2018", "text": "<PERSON>, American author (b. 1946)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>eve\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>eve\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hreve"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, French film director (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Agn%C3%A8s_Varda\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French film director (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agn%C3%A8s_Varda\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French film director (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agn%C3%A8s_Varda"}]}, {"year": "2020", "text": "<PERSON>, American country music singer (b. 1958)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American musician (b. 1951)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish composer and conductor (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish composer and conductor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish composer and conductor (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Albanian politician, 29th Prime Minister of Albania (b. 1962)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (b. 1962)", "links": [{"title": "Bash<PERSON>", "link": "https://wikipedia.org/wiki/Bash<PERSON>_<PERSON>o"}, {"title": "Prime Minister of Albania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Albania"}]}, {"year": "2021", "text": "<PERSON>, Kenyan educator and philanthropist (b. 1921)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan educator and philanthropist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan educator and philanthropist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, British botanist (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, British botanist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, British botanist (b. 1934)", "links": [{"title": "<PERSON> (botanist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(botanist)"}]}, {"year": "2022", "text": "<PERSON>, English actress (b. 1932)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1932)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "2023", "text": "<PERSON>, Australian politician (b. 1937)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Indian contemporary artist (b. 1943)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Vivan_Sundaram\" title=\"Vivan Sundaram\"><PERSON><PERSON></a>, Indian contemporary artist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vivan_Sundaram\" title=\"Vivan Sundaram\"><PERSON><PERSON></a>, Indian contemporary artist (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vivan_Sundaram"}]}, {"year": "2024", "text": "<PERSON>, English folk and rock drummer/percussionist (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Folk_music\" title=\"Folk music\">folk</a> and rock drummer/percussionist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Folk_music\" title=\"Folk music\">folk</a> and rock drummer/percussionist (b. 1947)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}, {"title": "Folk music", "link": "https://wikipedia.org/wiki/Folk_music"}]}, {"year": "2024", "text": "<PERSON>, American actor (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American actor (b. 1936)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}]}}