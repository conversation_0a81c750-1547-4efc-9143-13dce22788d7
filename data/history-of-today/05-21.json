{"date": "May 21", "url": "https://wikipedia.org/wiki/May_21", "data": {"Events": [{"year": "293", "text": "Roman Emperors <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> appoint <PERSON><PERSON> as Caesar to <PERSON><PERSON><PERSON><PERSON>, beginning the period of four rulers known as the Tetrarchy.", "html": "293 - Roman Emperors <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> appoint <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a></i> to <PERSON><PERSON><PERSON><PERSON>, beginning the period of four rulers known as the <a href=\"https://wikipedia.org/wiki/Tetrarchy\" title=\"Tetrarchy\">Tetrarchy</a>.", "no_year_html": "Roman Emperors <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> appoint <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a></i> to <PERSON><PERSON><PERSON><PERSON>, beginning the period of four rulers known as the <a href=\"https://wikipedia.org/wiki/Tetrarchy\" title=\"Tetrarchy\">Tetrarchy</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rius"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}, {"title": "Tetrarchy", "link": "https://wikipedia.org/wiki/Tetrarchy"}]}, {"year": "878", "text": "Syracuse, Sicily, is captured by the Muslim Aghlabids after a nine-month siege.", "html": "878 - <a href=\"https://wikipedia.org/wiki/Syracuse,_Sicily\" title=\"Syracuse, Sicily\">Syracuse, Sicily</a>, is <a href=\"https://wikipedia.org/wiki/Siege_of_Syracuse_(877%E2%80%93878)\" title=\"Siege of Syracuse (877-878)\">captured</a> by the Muslim <a href=\"https://wikipedia.org/wiki/Aghlabids\" class=\"mw-redirect\" title=\"Aghlabids\">Aghlabids</a> after a nine-month siege.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syracuse,_Sicily\" title=\"Syracuse, Sicily\">Syracuse, Sicily</a>, is <a href=\"https://wikipedia.org/wiki/Siege_of_Syracuse_(877%E2%80%93878)\" title=\"Siege of Syracuse (877-878)\">captured</a> by the Muslim <a href=\"https://wikipedia.org/wiki/Aghlabids\" class=\"mw-redirect\" title=\"Aghlabids\">Aghlabids</a> after a nine-month siege.", "links": [{"title": "Syracuse, Sicily", "link": "https://wikipedia.org/wiki/Syracuse,_Sicily"}, {"title": "Siege of Syracuse (877-878)", "link": "https://wikipedia.org/wiki/Siege_of_Syracuse_(877%E2%80%93878)"}, {"title": "Aghlabids", "link": "https://wikipedia.org/wiki/Aghlabids"}]}, {"year": "879", "text": "Pope <PERSON> gives blessings to <PERSON><PERSON><PERSON> of Croatia and to the Croatian people, considered to be international recognition of the Croatian state.", "html": "879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John <PERSON>\">Pope <PERSON></a> gives blessings to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Croatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Croatia\"><PERSON><PERSON><PERSON> of Croatia</a> and to the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> people, considered to be international recognition of the Croatian state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> John <PERSON>\">Pope <PERSON> VIII</a> gives blessings to <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_of_Croatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Croatia\"><PERSON><PERSON><PERSON> of Croatia</a> and to the <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatian</a> people, considered to be international recognition of the Croatian state.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "B<PERSON>mir of Croatia", "link": "https://wikipedia.org/wiki/Branimir_of_Croatia"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}]}, {"year": "996", "text": "Sixteen-year-old <PERSON> is crowned Holy Roman Emperor.", "html": "996 - Sixteen-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> is crowned <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "no_year_html": "Sixteen-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> is crowned <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}]}, {"year": "1349", "text": "<PERSON><PERSON><PERSON>'s Code, the constitution of the Serbian Empire, is enacted by <PERSON><PERSON><PERSON> the Mighty.", "html": "1349 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an%27s_Code\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Code\"><PERSON><PERSON><PERSON>'s Code</a>, the constitution of the <a href=\"https://wikipedia.org/wiki/Serbian_Empire\" title=\"Serbian Empire\">Serbian Empire</a>, is enacted by <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_the_Mighty\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Mighty\"><PERSON><PERSON><PERSON> the Mighty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an%27s_Code\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Code\"><PERSON><PERSON><PERSON>'s Code</a>, the constitution of the <a href=\"https://wikipedia.org/wiki/Serbian_Empire\" title=\"Serbian Empire\">Serbian Empire</a>, is enacted by <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_the_Mighty\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> the Mighty\"><PERSON><PERSON><PERSON> the Mighty</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>'s Code", "link": "https://wikipedia.org/wiki/Du%C5%A1an%27s_Code"}, {"title": "Serbian Empire", "link": "https://wikipedia.org/wiki/Serbian_Empire"}, {"title": "<PERSON><PERSON><PERSON> the Mighty", "link": "https://wikipedia.org/wiki/Du%C5%A1an_the_Mighty"}]}, {"year": "1403", "text": "<PERSON> of Castile sends <PERSON><PERSON> as ambassador to Timur to discuss the possibility of an alliance between Timur and Castile against the Ottoman Empire.", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> sends <a href=\"https://wikipedia.org/wiki/Ruy_<PERSON>nz%C3%A1<PERSON>z_de_Clavijo\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Timurid_relations_with_Europe\" title=\"Timurid relations with Europe\">ambassador to Timur</a> to discuss the possibility of an alliance between Timur and Castile against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> sends <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>%C3%A1lez_de_Clavijo\" title=\"<PERSON><PERSON>lav<PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Timurid_relations_with_Europe\" title=\"Timurid relations with Europe\">ambassador to Timur</a> to discuss the possibility of an alliance between Timur and Castile against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruy_Gonz%C3%<PERSON>lez_de_Clavijo"}, {"title": "Timurid relations with Europe", "link": "https://wikipedia.org/wiki/Timurid_relations_with_Europe"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1554", "text": "Queen <PERSON> grants a royal charter to Derby School, as a grammar school for boys in Derby, England.", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Mary I of England\">Queen <PERSON> I</a> grants a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> to <a href=\"https://wikipedia.org/wiki/Derby_School\" title=\"Derby School\">Derby School</a>, as a grammar school for boys in <a href=\"https://wikipedia.org/wiki/Derby\" title=\"Derby\">Derby</a>, England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\">Queen <PERSON> I</a> grants a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> to <a href=\"https://wikipedia.org/wiki/Derby_School\" title=\"Derby School\">Derby School</a>, as a grammar school for boys in <a href=\"https://wikipedia.org/wiki/Derby\" title=\"Derby\">Derby</a>, England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "Derby School", "link": "https://wikipedia.org/wiki/Derby_School"}, {"title": "Derby", "link": "https://wikipedia.org/wiki/Derby"}]}, {"year": "1659", "text": "In the Concert of The Hague, the Dutch Republic, the Commonwealth of England and the Kingdom of France set out their views on how the Second Northern War should end.", "html": "1659 - In the <a href=\"https://wikipedia.org/wiki/Concert_of_The_Hague_(1659)\" title=\"Concert of The Hague (1659)\">Concert of The Hague</a>, the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>, the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a> set out their views on how the <a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a> should end.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Concert_of_The_Hague_(1659)\" title=\"Concert of The Hague (1659)\">Concert of The Hague</a>, the <a href=\"https://wikipedia.org/wiki/Dutch_Republic\" title=\"Dutch Republic\">Dutch Republic</a>, the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a> set out their views on how the <a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a> should end.", "links": [{"title": "Concert of The Hague (1659)", "link": "https://wikipedia.org/wiki/Concert_of_The_Hague_(1659)"}, {"title": "Dutch Republic", "link": "https://wikipedia.org/wiki/Dutch_Republic"}, {"title": "Commonwealth of England", "link": "https://wikipedia.org/wiki/Commonwealth_of_England"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Second Northern War", "link": "https://wikipedia.org/wiki/Second_Northern_War"}]}, {"year": "1660", "text": "The Battle of Long Sault concludes after five days in which French colonial militia, with their Huron and Algonquin allies, are defeated by the Iroquois Confederacy.", "html": "1660 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Sault\" title=\"Battle of Long Sault\">Battle of Long Sault</a> concludes after five days in which <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">French</a> <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">colonial</a> <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a>, with their <a href=\"https://wikipedia.org/wiki/Wyandot_people\" title=\"Wyandot people\">Huron</a> and <a href=\"https://wikipedia.org/wiki/Algonquin_people\" title=\"Algonquin people\">Algonquin</a> allies, are defeated by the <a href=\"https://wikipedia.org/wiki/Iroquois_Confederacy\" class=\"mw-redirect\" title=\"Iroquois Confederacy\">Iroquois Confederacy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Sault\" title=\"Battle of Long Sault\">Battle of Long Sault</a> concludes after five days in which <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">French</a> <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">colonial</a> <a href=\"https://wikipedia.org/wiki/Militia\" title=\"Militia\">militia</a>, with their <a href=\"https://wikipedia.org/wiki/Wyandot_people\" title=\"Wyandot people\">Huron</a> and <a href=\"https://wikipedia.org/wiki/Algonquin_people\" title=\"Algonquin people\">Algonquin</a> allies, are defeated by the <a href=\"https://wikipedia.org/wiki/Iroquois_Confederacy\" class=\"mw-redirect\" title=\"Iroquois Confederacy\">Iroquois Confederacy</a>.", "links": [{"title": "Battle of Long Sault", "link": "https://wikipedia.org/wiki/Battle_of_Long_Sault"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}, {"title": "Militia", "link": "https://wikipedia.org/wiki/Militia"}, {"title": "Wyandot people", "link": "https://wikipedia.org/wiki/Wyandot_people"}, {"title": "Algonquin people", "link": "https://wikipedia.org/wiki/Algonquin_people"}, {"title": "Iroquois Confederacy", "link": "https://wikipedia.org/wiki/Iroquois_Confederacy"}]}, {"year": "1674", "text": "The nobility elect <PERSON> King of Poland and Grand Duke of Lithuania.", "html": "1674 - The <a href=\"https://wikipedia.org/wiki/Szlachta\" title=\"Szlachta\">nobility</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> King of Poland and Grand Duke of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Szlachta\" title=\"Szlachta\">nobility</a> elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> King of Poland and Grand Duke of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "Szlachta", "link": "https://wikipedia.org/wiki/Szlachta"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1703", "text": "<PERSON> is imprisoned on charges of seditious libel.", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is imprisoned on charges of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is imprisoned on charges of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seditious libel", "link": "https://wikipedia.org/wiki/Seditious_libel"}]}, {"year": "1725", "text": "The Order of <PERSON> is instituted in Russia by Empress <PERSON> It would later be discontinued and then reinstated by the Soviet government in 1942 as the Order of <PERSON>.", "html": "1725 - The <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Order of St<PERSON> Alexander <PERSON>\">Order of St<PERSON></a> is instituted in Russia by Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>. It would later be discontinued and then reinstated by the Soviet government in <a href=\"https://wikipedia.org/wiki/1942\" title=\"1942\">1942</a> as the <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON>\" title=\"Order of <PERSON>\">Order of Alexander <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Order of St<PERSON>\">Order of <PERSON><PERSON></a> is instituted in Russia by Empress <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>. It would later be discontinued and then reinstated by the Soviet government in <a href=\"https://wikipedia.org/wiki/1942\" title=\"1942\">1942</a> as the <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON>\" title=\"Order of <PERSON>\">Order of <PERSON></a>.", "links": [{"title": "Order of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1942", "link": "https://wikipedia.org/wiki/1942"}, {"title": "Order of <PERSON>", "link": "https://wikipedia.org/wiki/Order_of_<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "Ten-year-old <PERSON> is abducted in Pennsylvania by <PERSON><PERSON> during the French and Indian War. She is returned six and a half years later.", "html": "1758 - Ten-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_settler)\" title=\"<PERSON> (colonial settler)\"><PERSON></a> is abducted in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> by <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> during the <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>. She is returned six and a half years later.", "no_year_html": "Ten-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_settler)\" title=\"<PERSON> (colonial settler)\"><PERSON></a> is abducted in <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> by <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a> during the <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>. She is returned six and a half years later.", "links": [{"title": "<PERSON> (colonial settler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_settler)"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Lenape", "link": "https://wikipedia.org/wiki/Lenape"}, {"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}]}, {"year": "1792", "text": "A lava dome collapses on Mount Unzen, near the city of Shimbara on the Japanese island of Kyūshū, creating a deadly tsunami that killed nearly 15,000 people.", "html": "1792 - A <a href=\"https://wikipedia.org/wiki/Lava_dome\" title=\"Lava dome\">lava dome</a> collapses on <a href=\"https://wikipedia.org/wiki/Mount_Unzen\" title=\"Mount Unzen\">Mount Unzen</a>, near the city of <a href=\"https://wikipedia.org/wiki/Shimabara,_Nagasaki\" title=\"Shimabara, Nagasaki\">Shimbara</a> on the <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> island of <a href=\"https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB\" class=\"mw-redirect\" title=\"Kyūshū\">Kyūshū</a>, <a href=\"https://wikipedia.org/wiki/1792_Unzen_earthquake_and_tsunami\" class=\"mw-redirect\" title=\"1792 Unzen earthquake and tsunami\">creating a deadly tsunami</a> that killed nearly 15,000 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Lava_dome\" title=\"Lava dome\">lava dome</a> collapses on <a href=\"https://wikipedia.org/wiki/Mount_Unzen\" title=\"Mount Unzen\">Mount Unzen</a>, near the city of <a href=\"https://wikipedia.org/wiki/Shimabara,_Nagasaki\" title=\"Shimabara, Nagasaki\">Shimbara</a> on the <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> island of <a href=\"https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB\" class=\"mw-redirect\" title=\"Kyūshū\">Kyūshū</a>, <a href=\"https://wikipedia.org/wiki/1792_Unzen_earthquake_and_tsunami\" class=\"mw-redirect\" title=\"1792 Unzen earthquake and tsunami\">creating a deadly tsunami</a> that killed nearly 15,000 people.", "links": [{"title": "Lava dome", "link": "https://wikipedia.org/wiki/Lava_dome"}, {"title": "Mount Unzen", "link": "https://wikipedia.org/wiki/Mount_Unzen"}, {"title": "Shimabara, Nagasaki", "link": "https://wikipedia.org/wiki/Shimabara,_Nagasaki"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "Kyūshū", "link": "https://wikipedia.org/wiki/Ky%C5%ABsh%C5%AB"}, {"title": "1792 Unzen earthquake and tsunami", "link": "https://wikipedia.org/wiki/1792_Unzen_earthquake_and_tsunami"}]}, {"year": "1799", "text": "The end of the Siege of Acre (1799): <PERSON> abandons his siege of the Ottoman city of Acre after two months. This was the turning point of <PERSON>'s Egyptian campaign and one of the first major defeats he suffered in his military career.", "html": "1799 - The end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Acre_(1799)\" title=\"Siege of Acre (1799)\">Siege of Acre (1799)</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Bonaparte\"><PERSON></a> abandons his siege of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> city of <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a> after two months. This was the turning point of <PERSON>'s Egyptian campaign and one of the first major defeats he suffered in his military career.", "no_year_html": "The end of the <a href=\"https://wikipedia.org/wiki/Siege_of_Acre_(1799)\" title=\"Siege of Acre (1799)\">Siege of Acre (1799)</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> abandons his siege of the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a> city of <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a> after two months. This was the turning point of <PERSON>'s Egyptian campaign and one of the first major defeats he suffered in his military career.", "links": [{"title": "Siege of Acre (1799)", "link": "https://wikipedia.org/wiki/Siege_of_Acre_(1799)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Acre, Israel", "link": "https://wikipedia.org/wiki/Acre,_Israel"}]}, {"year": "1809", "text": "The first day of the Battle of Aspern-Essling between the Austrian army led by <PERSON><PERSON><PERSON> and the French army led by <PERSON> of France sees the French attack across the Danube held.", "html": "1809 - The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Aspern-Essling\" title=\"Battle of Aspern-Essling\">Battle of Aspern-Essling</a> between the Austrian army led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_<PERSON>_Teschen\" title=\"Arch<PERSON><PERSON>, Duke of Teschen\">Archduke <PERSON></a> and the French army led by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON> I of France</a> sees the French attack across the <a href=\"https://wikipedia.org/wiki/Danube\" title=\"Danube\">Danube</a> held.", "no_year_html": "The first day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Aspern-Essling\" title=\"Battle of Aspern-Essling\">Battle of Aspern-Essling</a> between the Austrian army led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen\" title=\"Arch<PERSON><PERSON>, Duke of Teschen\">Arch<PERSON>ke <PERSON></a> and the French army led by <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON> I of France</a> sees the French attack across the <a href=\"https://wikipedia.org/wiki/Danube\" title=\"Danube\">Danube</a> held.", "links": [{"title": "Battle of Aspern-Essling", "link": "https://wikipedia.org/wiki/Battle_of_Aspern-Essling"}, {"title": "<PERSON><PERSON><PERSON>, Duke of Teschen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Teschen"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Danube", "link": "https://wikipedia.org/wiki/Danube"}]}, {"year": "1851", "text": "Slavery in Colombia is abolished.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Slavery_in_Colombia\" title=\"Slavery in Colombia\">Slavery in Colombia</a> is abolished.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slavery_in_Colombia\" title=\"Slavery in Colombia\">Slavery in Colombia</a> is abolished.", "links": [{"title": "Slavery in Colombia", "link": "https://wikipedia.org/wiki/Slavery_in_Colombia"}]}, {"year": "1856", "text": "Lawrence, Kansas is captured and burned by pro-slavery forces.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Lawrence,_Kansas\" title=\"Lawrence, Kansas\">Lawrence, Kansas</a> is <a href=\"https://wikipedia.org/wiki/Sacking_of_Lawrence\" title=\"Sacking of Lawrence\">captured and burned</a> by pro-<a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawrence,_Kansas\" title=\"Lawrence, Kansas\">Lawrence, Kansas</a> is <a href=\"https://wikipedia.org/wiki/Sacking_of_<PERSON>\" title=\"Sacking of Lawrence\">captured and burned</a> by pro-<a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> forces.", "links": [{"title": "Lawrence, Kansas", "link": "https://wikipedia.org/wiki/Lawrence,_Kansas"}, {"title": "Sacking of <PERSON>", "link": "https://wikipedia.org/wiki/Sacking_of_<PERSON>"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1863", "text": "American Civil War: The Union Army succeeds in closing off the last escape route from Port Hudson, Louisiana, in preparation for the coming siege.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Plains_Store\" title=\"Battle of Plains Store\">succeeds</a> in closing off the last escape route from <a href=\"https://wikipedia.org/wiki/Port_Hudson,_Louisiana\" title=\"Port Hudson, Louisiana\">Port Hudson, Louisiana</a>, in preparation for the coming <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Plains_Store\" title=\"Battle of Plains Store\">succeeds</a> in closing off the last escape route from <a href=\"https://wikipedia.org/wiki/Port_Hudson,_Louisiana\" title=\"Port Hudson, Louisiana\">Port Hudson, Louisiana</a>, in preparation for the coming <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">siege</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Battle of Plains Store", "link": "https://wikipedia.org/wiki/Battle_of_Plains_Store"}, {"title": "Port Hudson, Louisiana", "link": "https://wikipedia.org/wiki/Port_Hudson,_Louisiana"}, {"title": "Siege of Port Hudson", "link": "https://wikipedia.org/wiki/Siege_of_Port_Hudson"}]}, {"year": "1864", "text": "Russia declares an end to the Russo-Circassian War and many Circassians are forced into exile. The day is designated the Circassian Day of Mourning.", "html": "1864 - Russia declares an end to the <a href=\"https://wikipedia.org/wiki/Russo-Circassian_War\" title=\"Russo-Circassian War\">Russo-Circassian War</a> and many <a href=\"https://wikipedia.org/wiki/Circassians\" title=\"Circassians\">Circassians</a> are forced into exile. The day is designated the <a href=\"https://wikipedia.org/wiki/Circassian_Day_of_Mourning\" title=\"Circassian Day of Mourning\">Circassian Day of Mourning</a>.", "no_year_html": "Russia declares an end to the <a href=\"https://wikipedia.org/wiki/Russo-Circassian_War\" title=\"Russo-Circassian War\">Russo-Circassian War</a> and many <a href=\"https://wikipedia.org/wiki/Circassians\" title=\"Circassians\">Circassians</a> are forced into exile. The day is designated the <a href=\"https://wikipedia.org/wiki/Circassian_Day_of_Mourning\" title=\"Circassian Day of Mourning\">Circassian Day of Mourning</a>.", "links": [{"title": "Russo-Circassian War", "link": "https://wikipedia.org/wiki/Russo-Circassian_War"}, {"title": "Circassians", "link": "https://wikipedia.org/wiki/Circassians"}, {"title": "Circassian Day of Mourning", "link": "https://wikipedia.org/wiki/Circassian_Day_of_Mourning"}]}, {"year": "1864", "text": "American Civil War: The Battle of Spotsylvania Court House ends.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House\" title=\"Battle of Spotsylvania Court House\">Battle of Spotsylvania Court House</a> ends.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House\" title=\"Battle of Spotsylvania Court House\">Battle of Spotsylvania Court House</a> ends.", "links": [{"title": "Battle of Spotsylvania Court House", "link": "https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House"}]}, {"year": "1864", "text": "The Ionian Islands reunite with Greece.", "html": "1864 - The <a href=\"https://wikipedia.org/wiki/Ionian_Islands\" title=\"Ionian Islands\">Ionian Islands</a> reunite with Greece.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ionian_Islands\" title=\"Ionian Islands\">Ionian Islands</a> reunite with Greece.", "links": [{"title": "Ionian Islands", "link": "https://wikipedia.org/wiki/Ionian_Islands"}]}, {"year": "1871", "text": "French troops invade the Paris Commune and engage its residents in street fighting. By the close of \"Bloody Week\", some 20,000 communards have been killed and 38,000 arrested.", "html": "1871 - French troops invade the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a> and engage its residents in street fighting. By the close of \"<a href=\"https://wikipedia.org/wiki/Bloody_Week\" class=\"mw-redirect\" title=\"Bloody Week\">Bloody Week</a>\", some 20,000 <a href=\"https://wikipedia.org/wiki/Communard\" class=\"mw-redirect\" title=\"Communard\">communards</a> have been killed and 38,000 arrested.", "no_year_html": "French troops invade the <a href=\"https://wikipedia.org/wiki/Paris_Commune\" title=\"Paris Commune\">Paris Commune</a> and engage its residents in street fighting. By the close of \"<a href=\"https://wikipedia.org/wiki/Bloody_Week\" class=\"mw-redirect\" title=\"Bloody Week\">Bloody Week</a>\", some 20,000 <a href=\"https://wikipedia.org/wiki/Communard\" class=\"mw-redirect\" title=\"Communard\">communards</a> have been killed and 38,000 arrested.", "links": [{"title": "Paris Commune", "link": "https://wikipedia.org/wiki/Paris_Commune"}, {"title": "Bloody Week", "link": "https://wikipedia.org/wiki/Bloody_Week"}, {"title": "Communard", "link": "https://wikipedia.org/wiki/Communard"}]}, {"year": "1871", "text": "Opening of the first rack railway in Europe, the Rigi Bahnen on Mount Rigi.", "html": "1871 - Opening of the first <a href=\"https://wikipedia.org/wiki/Rack_railway\" title=\"Rack railway\">rack railway</a> in Europe, the <a href=\"https://wikipedia.org/wiki/Rigi_Bahnen\" class=\"mw-redirect\" title=\"Rigi Bahnen\"><PERSON>igi <PERSON></a> on <a href=\"https://wikipedia.org/wiki/Mount_Rigi\" class=\"mw-redirect\" title=\"Mount Rigi\">Mount Rigi</a>.", "no_year_html": "Opening of the first <a href=\"https://wikipedia.org/wiki/Rack_railway\" title=\"Rack railway\">rack railway</a> in Europe, the <a href=\"https://wikipedia.org/wiki/Rigi_Bahnen\" class=\"mw-redirect\" title=\"Rigi Bahnen\"><PERSON><PERSON></a> on <a href=\"https://wikipedia.org/wiki/Mount_Rigi\" class=\"mw-redirect\" title=\"Mount Rigi\">Mount Rigi</a>.", "links": [{"title": "Rack railway", "link": "https://wikipedia.org/wiki/Rack_railway"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON>en"}, {"title": "Mount Rigi", "link": "https://wikipedia.org/wiki/Mount_Rigi"}]}, {"year": "1879", "text": "War of the Pacific: Two Chilean ships blocking the harbor of Iquique (then belonging to Peru) battle two Peruvian vessels in the Battle of Iquique.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Two <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> ships blocking the harbor of <a href=\"https://wikipedia.org/wiki/Iquique\" title=\"Iquique\">Iquique</a> (then belonging to <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>) battle two Peruvian vessels in the <a href=\"https://wikipedia.org/wiki/Battle_of_Iquique\" title=\"Battle of Iquique\">Battle of Iquique</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Two <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chilean</a> ships blocking the harbor of <a href=\"https://wikipedia.org/wiki/Iquique\" title=\"Iquique\">Iquique</a> (then belonging to <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>) battle two Peruvian vessels in the <a href=\"https://wikipedia.org/wiki/Battle_of_Iquique\" title=\"Battle of Iquique\">Battle of Iquique</a>.", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Iquique", "link": "https://wikipedia.org/wiki/Iquique"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Battle of Iquique", "link": "https://wikipedia.org/wiki/Battle_of_Iquique"}]}, {"year": "1881", "text": "The American Red Cross is established by <PERSON> in Washington, D.C.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Washington, D.C.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Washington, D.C.", "links": [{"title": "American Red Cross", "link": "https://wikipedia.org/wiki/American_Red_Cross"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "The Manchester Ship Canal in the United Kingdom is officially opened by Queen <PERSON>, who later knights its designer Sir <PERSON>.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/Manchester_Ship_Canal\" title=\"Manchester Ship Canal\">Manchester Ship Canal</a> in the United Kingdom is officially opened by <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a>, who later <a href=\"https://wikipedia.org/wiki/Knight\" title=\"Knight\">knights</a> its designer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Manchester_Ship_Canal\" title=\"Manchester Ship Canal\">Manchester Ship Canal</a> in the United Kingdom is officially opened by <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a>, who later <a href=\"https://wikipedia.org/wiki/Knight\" title=\"Knight\">knights</a> its designer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>.", "links": [{"title": "Manchester Ship Canal", "link": "https://wikipedia.org/wiki/Manchester_Ship_Canal"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Knight"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "The Fédération Internationale de Football Association (FIFA) is founded in Paris.", "html": "1904 - The <a href=\"https://wikipedia.org/wiki/FIFA\" title=\"FIFA\">Fédération Internationale de Football Association</a> (FIFA) is founded in Paris.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/FIFA\" title=\"FIFA\">Fédération Internationale de Football Association</a> (FIFA) is founded in Paris.", "links": [{"title": "FIFA", "link": "https://wikipedia.org/wiki/FIFA"}]}, {"year": "1911", "text": "President of Mexico <PERSON><PERSON><PERSON><PERSON> and the revolutionary <PERSON> sign the Treaty of Ciudad Juárez to put an end to the fighting between the forces of both men, concluding the initial phase of the Mexican Revolution.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> <a href=\"https://wikipedia.org/wiki/Porfirio_D%C3%ADaz\" title=\"Porfiri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and the revolutionary <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" class=\"mw-redirect\" title=\"Francisco <PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Ciudad_Ju%C3%A1rez\" title=\"Treaty of Ciudad Juárez\">Treaty of Ciudad Juárez</a> to put an end to the fighting between the forces of both men, concluding the initial phase of the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> <a href=\"https://wikipedia.org/wiki/Porfirio_D%C3%ADaz\" title=\"Porfiri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and the revolutionary <a href=\"https://wikipedia.org/wiki/Francisco_Madero\" class=\"mw-redirect\" title=\"Francisco Made<PERSON>\"><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Ciudad_Ju%C3%A1rez\" title=\"Treaty of Ciudad Juárez\">Treaty of Ciudad Juárez</a> to put an end to the fighting between the forces of both men, concluding the initial phase of the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>.", "links": [{"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Porfirio_D%C3%ADaz"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Treaty of Ciudad Juárez", "link": "https://wikipedia.org/wiki/Treaty_of_Ciudad_Ju%C3%A1rez"}, {"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}]}, {"year": "1917", "text": "The Imperial War Graves Commission is established through royal charter to mark, record, and maintain the graves and places of commemoration of the British Empire's military forces.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission\" title=\"Commonwealth War Graves Commission\">Imperial War Graves Commission</a> is established through <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> to mark, record, and maintain the graves and places of commemoration of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">British Empire's</a> military forces.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission\" title=\"Commonwealth War Graves Commission\">Imperial War Graves Commission</a> is established through <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a> to mark, record, and maintain the graves and places of commemoration of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">British Empire's</a> military forces.", "links": [{"title": "Commonwealth War Graves Commission", "link": "https://wikipedia.org/wiki/Commonwealth_War_Graves_Commission"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1917", "text": "The Great Atlanta fire of 1917 causes $5.5 million in damages, destroying some 300 acres including 2,000 homes, businesses and churches, displacing about 10,000 people but leading to only one fatality (due to heart attack).", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Great_Atlanta_fire_of_1917\" title=\"Great Atlanta fire of 1917\">Great Atlanta fire of 1917</a> causes $5.5 million in damages, destroying some 300 acres including 2,000 homes, businesses and churches, displacing about 10,000 people but leading to only one fatality (due to heart attack).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Atlanta_fire_of_1917\" title=\"Great Atlanta fire of 1917\">Great Atlanta fire of 1917</a> causes $5.5 million in damages, destroying some 300 acres including 2,000 homes, businesses and churches, displacing about 10,000 people but leading to only one fatality (due to heart attack).", "links": [{"title": "Great Atlanta fire of 1917", "link": "https://wikipedia.org/wiki/Great_Atlanta_fire_of_1917"}]}, {"year": "1924", "text": "University of Chicago students <PERSON> and <PERSON>, Jr. murder 14-year-old <PERSON> in a \"thrill killing\".", "html": "1924 - <a href=\"https://wikipedia.org/wiki/University_of_Chicago\" title=\"University of Chicago\">University of Chicago</a> students <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON>, Jr.</a> murder 14-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> in a \"<a href=\"https://wikipedia.org/wiki/Thrill_killing\" title=\"Thrill killing\">thrill killing</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/University_of_Chicago\" title=\"University of Chicago\">University of Chicago</a> students <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON> and <PERSON><PERSON>\"><PERSON> and <PERSON>, Jr.</a> murder 14-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> in a \"<a href=\"https://wikipedia.org/wiki/Thrill_killing\" title=\"Thrill killing\">thrill killing</a>\".", "links": [{"title": "University of Chicago", "link": "https://wikipedia.org/wiki/University_of_Chicago"}, {"title": "<PERSON> and Lo<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thrill killing", "link": "https://wikipedia.org/wiki/Thrill_killing"}]}, {"year": "1925", "text": "The opera <PERSON><PERSON><PERSON>, unfinished when composer <PERSON><PERSON><PERSON><PERSON> died, is premiered in Dresden.", "html": "1925 - The opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Faust\"><PERSON><PERSON><PERSON></a></i>, unfinished when composer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> died, is premiered <a href=\"https://wikipedia.org/wiki/Semperoper\" title=\"Semperoper\">in Dresden</a>.", "no_year_html": "The opera <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Faust\"><PERSON><PERSON><PERSON></a></i>, unfinished when composer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> died, is premiered <a href=\"https://wikipedia.org/wiki/Semperoper\" title=\"Semperoper\">in Dresden</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON> touches down at Le Bourget Field in Paris, completing the world's first solo nonstop flight across the Atlantic Ocean.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> touches down at <a href=\"https://wikipedia.org/wiki/Le_Bourget_Field\" class=\"mw-redirect\" title=\"Le Bourget Field\">Le Bourget Field</a> in Paris, completing the world's first solo nonstop flight across the Atlantic Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> touches down at <a href=\"https://wikipedia.org/wiki/Le_Bo<PERSON>_Field\" class=\"mw-redirect\" title=\"Le Bourget Field\">Le Bourget Field</a> in Paris, completing the world's first solo nonstop flight across the Atlantic Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Le Bourget Field", "link": "https://wikipedia.org/wiki/Le_Bourget_Field"}]}, {"year": "1932", "text": "Bad weather forces <PERSON> to land in a pasture in Derry, Northern Ireland, and she thereby becomes the first woman to fly solo across the Atlantic Ocean.", "html": "1932 - Bad weather forces <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to land in a pasture in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, and she thereby becomes the first woman to fly solo across the Atlantic Ocean.", "no_year_html": "Bad weather forces <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to land in a pasture in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, and she thereby becomes the first woman to fly solo across the Atlantic Ocean.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Derry", "link": "https://wikipedia.org/wiki/Derry"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1934", "text": "Oskaloosa, Iowa, becomes the first municipality in the United States to fingerprint all of its citizens.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Oskaloosa,_Iowa\" title=\"Oskaloosa, Iowa\">Oskaloosa, Iowa</a>, becomes the first municipality in the United States to fingerprint all of its citizens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oskaloosa,_Iowa\" title=\"Oskaloosa, Iowa\">Oskaloosa, Iowa</a>, becomes the first municipality in the United States to fingerprint all of its citizens.", "links": [{"title": "Oskaloosa, Iowa", "link": "https://wikipedia.org/wiki/Oskaloosa,_Iowa"}]}, {"year": "1936", "text": "<PERSON><PERSON> is arrested after wandering the streets of Tokyo for days with her dead lover's severed genitals in her handbag. Her story soon becomes one of Japan's most notorious scandals.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is arrested after wandering the streets of Tokyo for days with her dead lover's severed genitals in her handbag. Her story soon becomes one of Japan's most notorious scandals.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is arrested after wandering the streets of Tokyo for days with her dead lover's severed genitals in her handbag. Her story soon becomes one of Japan's most notorious scandals.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "A Soviet station, North Pole-1, becomes the first scientific research settlement to operate on the drift ice of the Arctic Ocean.", "html": "1937 - A <a href=\"https://wikipedia.org/wiki/Soviet_and_Russian_manned_drifting_ice_stations\" class=\"mw-redirect\" title=\"Soviet and Russian manned drifting ice stations\">Soviet station</a>, <a href=\"https://wikipedia.org/wiki/North_Pole-1\" title=\"North Pole-1\">North Pole-1</a>, becomes the first scientific research settlement to operate on the <a href=\"https://wikipedia.org/wiki/Drift_ice\" title=\"Drift ice\">drift ice</a> of the Arctic Ocean.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Soviet_and_Russian_manned_drifting_ice_stations\" class=\"mw-redirect\" title=\"Soviet and Russian manned drifting ice stations\">Soviet station</a>, <a href=\"https://wikipedia.org/wiki/North_Pole-1\" title=\"North Pole-1\">North Pole-1</a>, becomes the first scientific research settlement to operate on the <a href=\"https://wikipedia.org/wiki/Drift_ice\" title=\"Drift ice\">drift ice</a> of the Arctic Ocean.", "links": [{"title": "Soviet and Russian manned drifting ice stations", "link": "https://wikipedia.org/wiki/Soviet_and_Russian_manned_drifting_ice_stations"}, {"title": "North Pole-1", "link": "https://wikipedia.org/wiki/North_Pole-1"}, {"title": "Drift ice", "link": "https://wikipedia.org/wiki/Drift_ice"}]}, {"year": "1939", "text": "The Canadian National War Memorial is unveiled by <PERSON> and <PERSON> in Ottawa, Ontario, Canada.", "html": "1939 - The Canadian <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Canada)\" title=\"National War Memorial (Canada)\">National War Memorial</a> is unveiled by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George VI\">King <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen Elizabeth The Queen Mother\">Queen <PERSON></a> in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, Ontario, Canada.", "no_year_html": "The Canadian <a href=\"https://wikipedia.org/wiki/National_War_Memorial_(Canada)\" title=\"National War Memorial (Canada)\">National War Memorial</a> is unveiled by <a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" title=\"George VI\">King <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen Elizabeth The Queen Mother\">Queen <PERSON></a> in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>, Ontario, Canada.", "links": [{"title": "National War Memorial (Canada)", "link": "https://wikipedia.org/wiki/National_War_Memorial_(Canada)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Queen <PERSON> The Queen Mother", "link": "https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}]}, {"year": "1946", "text": "Physicist <PERSON> is fatally irradiated in a criticality incident during an experiment with the demon core at Los Alamos National Laboratory.", "html": "1946 - Physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is fatally irradiated in a <a href=\"https://wikipedia.org/wiki/Criticality_incident\" class=\"mw-redirect\" title=\"Criticality incident\">criticality incident</a> during an experiment with the <a href=\"https://wikipedia.org/wiki/Demon_core\" title=\"Demon core\">demon core</a> at <a href=\"https://wikipedia.org/wiki/Los_Alamos_National_Laboratory\" title=\"Los Alamos National Laboratory\">Los Alamos National Laboratory</a>.", "no_year_html": "Physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is fatally irradiated in a <a href=\"https://wikipedia.org/wiki/Criticality_incident\" class=\"mw-redirect\" title=\"Criticality incident\">criticality incident</a> during an experiment with the <a href=\"https://wikipedia.org/wiki/Demon_core\" title=\"Demon core\">demon core</a> at <a href=\"https://wikipedia.org/wiki/Los_Alamos_National_Laboratory\" title=\"Los Alamos National Laboratory\">Los Alamos National Laboratory</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Criticality incident", "link": "https://wikipedia.org/wiki/Criticality_incident"}, {"title": "Demon core", "link": "https://wikipedia.org/wiki/Demon_core"}, {"title": "Los Alamos National Laboratory", "link": "https://wikipedia.org/wiki/Los_Alamos_National_Laboratory"}]}, {"year": "1951", "text": "The opening of the Ninth Street Show, otherwise known as the 9th Street Art Exhibition: A gathering of a number of notable artists, and the stepping-out of the post war New York avant-garde, collectively known as the New York School.", "html": "1951 - The opening of the <a href=\"https://wikipedia.org/wiki/Ninth_Street_Show\" class=\"mw-redirect\" title=\"Ninth Street Show\">Ninth Street Show</a>, otherwise known as the <a href=\"https://wikipedia.org/wiki/9th_Street_Art_Exhibition\" title=\"9th Street Art Exhibition\">9th Street Art Exhibition</a>: A gathering of a number of notable artists, and the stepping-out of the post war New York <a href=\"https://wikipedia.org/wiki/Avant-garde\" title=\"Avant-garde\">avant-garde</a>, collectively known as the <a href=\"https://wikipedia.org/wiki/New_York_School_(art)\" title=\"New York School (art)\">New York School</a>.", "no_year_html": "The opening of the <a href=\"https://wikipedia.org/wiki/Ninth_Street_Show\" class=\"mw-redirect\" title=\"Ninth Street Show\">Ninth Street Show</a>, otherwise known as the <a href=\"https://wikipedia.org/wiki/9th_Street_Art_Exhibition\" title=\"9th Street Art Exhibition\">9th Street Art Exhibition</a>: A gathering of a number of notable artists, and the stepping-out of the post war New York <a href=\"https://wikipedia.org/wiki/Avant-garde\" title=\"Avant-garde\">avant-garde</a>, collectively known as the <a href=\"https://wikipedia.org/wiki/New_York_School_(art)\" title=\"New York School (art)\">New York School</a>.", "links": [{"title": "Ninth Street Show", "link": "https://wikipedia.org/wiki/Ninth_Street_Show"}, {"title": "9th Street Art Exhibition", "link": "https://wikipedia.org/wiki/9th_Street_Art_Exhibition"}, {"title": "Avant-garde", "link": "https://wikipedia.org/wiki/Avant-garde"}, {"title": "New York School (art)", "link": "https://wikipedia.org/wiki/New_York_School_(art)"}]}, {"year": "1961", "text": "American civil rights movement: Alabama Governor <PERSON> declares martial law in an attempt to restore order after race riots break out.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in an attempt to restore order after <a href=\"https://wikipedia.org/wiki/Race_riot\" class=\"mw-redirect\" title=\"Race riot\">race riots</a> break out.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> in an attempt to restore order after <a href=\"https://wikipedia.org/wiki/Race_riot\" class=\"mw-redirect\" title=\"Race riot\">race riots</a> break out.", "links": [{"title": "American civil rights movement", "link": "https://wikipedia.org/wiki/American_civil_rights_movement"}, {"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}, {"title": "Race riot", "link": "https://wikipedia.org/wiki/Race_riot"}]}, {"year": "1966", "text": "The Ulster Volunteer Force declares war on the Irish Republican Army in Northern Ireland.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> declares war on the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%9369)\" class=\"mw-redirect\" title=\"Irish Republican Army (1922-69)\">Irish Republican Army</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ulster_Volunteer_Force\" title=\"Ulster Volunteer Force\">Ulster Volunteer Force</a> declares war on the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%9369)\" class=\"mw-redirect\" title=\"Irish Republican Army (1922-69)\">Irish Republican Army</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "Ulster Volunteer Force", "link": "https://wikipedia.org/wiki/Ulster_Volunteer_Force"}, {"title": "Irish Republican Army (1922-69)", "link": "https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%9369)"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1969", "text": "Civil unrest in Rosario, Argentina, known as Rosariazo, following the death of a 15-year-old student.", "html": "1969 - Civil unrest in <a href=\"https://wikipedia.org/wiki/Rosario\" title=\"Rosario\">Rosario</a>, Argentina, known as <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i>, following the death of a 15-year-old student.", "no_year_html": "Civil unrest in <a href=\"https://wikipedia.org/wiki/Rosario\" title=\"Rosario\">Rosario</a>, Argentina, known as <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i>, following the death of a 15-year-old student.", "links": [{"title": "Rosario", "link": "https://wikipedia.org/wiki/Rosario"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>'s Pietà in St. Peter's Basilica in Rome is damaged by a vandal, the mentally disturbed Hungarian geologist <PERSON><PERSON><PERSON>.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Piet%C3%A0_(<PERSON><PERSON>)\" title=\"<PERSON><PERSON><PERSON> (<PERSON><PERSON>)\"><PERSON><PERSON><PERSON></a></i> in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> in Rome is damaged by a <a href=\"https://wikipedia.org/wiki/Vandalism\" title=\"Vandalism\">vandal</a>, the mentally disturbed Hungarian geologist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>th\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Piet%C3%A0_(<PERSON><PERSON>)\" title=\"<PERSON><PERSON><PERSON> (<PERSON><PERSON>)\"><PERSON><PERSON><PERSON></a></i> in <a href=\"https://wikipedia.org/wiki/St._Peter%27s_Basilica\" title=\"St. Peter's Basilica\">St. Peter's Basilica</a> in Rome is damaged by a <a href=\"https://wikipedia.org/wiki/Vandalism\" title=\"Vandalism\">vandal</a>, the mentally disturbed Hungarian geologist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>th\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Pietà (<PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Piet%C3%A0_(<PERSON><PERSON>)"}, {"title": "St. Peter's Basilica", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_Basilica"}, {"title": "Vandalism", "link": "https://wikipedia.org/wiki/Vandalism"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "Twenty-nine people are killed in the Yuba City bus disaster in Martinez, California.", "html": "1976 - Twenty-nine people are killed in the <a href=\"https://wikipedia.org/wiki/Yuba_City_bus_disaster\" title=\"Yuba City bus disaster\">Yuba City bus disaster</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>,_California\" title=\"Martinez, California\">Martinez, California</a>.", "no_year_html": "Twenty-nine people are killed in the <a href=\"https://wikipedia.org/wiki/Yuba_City_bus_disaster\" title=\"Yuba City bus disaster\">Yuba City bus disaster</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>,_California\" title=\"Martinez, California\">Martinez, California</a>.", "links": [{"title": "Yuba City bus disaster", "link": "https://wikipedia.org/wiki/Yuba_City_bus_disaster"}, {"title": "Martinez, California", "link": "https://wikipedia.org/wiki/<PERSON>,_California"}]}, {"year": "1979", "text": "White Night riots in San Francisco following the manslaughter conviction of <PERSON> for the assassinations of <PERSON> and <PERSON>.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/White_Night_riots\" title=\"White Night riots\">White Night riots</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> following the <a href=\"https://wikipedia.org/wiki/Manslaughter\" title=\"Manslaughter\">manslaughter</a> conviction of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> White\"><PERSON></a> for the assassinations of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/White_Night_riots\" title=\"White Night riots\">White Night riots</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a> following the <a href=\"https://wikipedia.org/wiki/Manslaughter\" title=\"Manslaughter\">manslaughter</a> conviction of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dan White\"><PERSON></a> for the assassinations of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>.", "links": [{"title": "White Night riots", "link": "https://wikipedia.org/wiki/White_Night_riots"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Manslaughter", "link": "https://wikipedia.org/wiki/Manslaughter"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "The Italian government releases the membership list of Propaganda Due, an illegal pseudo-Masonic lodge that was implicated in numerous Italian crimes and mysteries.", "html": "1981 - The Italian government releases the membership list of <a href=\"https://wikipedia.org/wiki/Propaganda_Due\" title=\"Propaganda Due\">Propaganda Due</a>, an illegal pseudo-<a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic lodge</a> that was implicated in numerous Italian crimes and mysteries.", "no_year_html": "The Italian government releases the membership list of <a href=\"https://wikipedia.org/wiki/Propaganda_Due\" title=\"Propaganda Due\">Propaganda Due</a>, an illegal pseudo-<a href=\"https://wikipedia.org/wiki/Freemasonry\" title=\"Freemasonry\">Masonic lodge</a> that was implicated in numerous Italian crimes and mysteries.", "links": [{"title": "Propaganda Due", "link": "https://wikipedia.org/wiki/Propaganda_Due"}, {"title": "Freemasonry", "link": "https://wikipedia.org/wiki/Freemasonry"}]}, {"year": "1981", "text": "Transamerica Corporation agrees to sell United Artists to Metro-Goldwyn-Mayer for $380 million after the box office failure of the 1980 film Heaven's Gate.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Transamerica_Corporation\" title=\"Transamerica Corporation\">Transamerica Corporation</a> agrees to sell <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> to <a href=\"https://wikipedia.org/wiki/Metro-Goldwyn-Mayer\" title=\"Metro-Goldwyn-Mayer\">Metro-Goldwyn-Mayer</a> for $380 million after the box office failure of the 1980 film <i><a href=\"https://wikipedia.org/wiki/Heaven%27s_Gate_(film)\" title=\"Heaven's Gate (film)\">Heaven's Gate</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transamerica_Corporation\" title=\"Transamerica Corporation\">Transamerica Corporation</a> agrees to sell <a href=\"https://wikipedia.org/wiki/United_Artists\" title=\"United Artists\">United Artists</a> to <a href=\"https://wikipedia.org/wiki/Metro-Goldwyn-Mayer\" title=\"Metro-Goldwyn-Mayer\">Metro-Goldwyn-Mayer</a> for $380 million after the box office failure of the 1980 film <i><a href=\"https://wikipedia.org/wiki/Heaven%27s_Gate_(film)\" title=\"Heaven's Gate (film)\">Heaven's Gate</a></i>.", "links": [{"title": "Transamerica Corporation", "link": "https://wikipedia.org/wiki/Transamerica_Corporation"}, {"title": "United Artists", "link": "https://wikipedia.org/wiki/United_Artists"}, {"title": "Metro-Goldwyn-Mayer", "link": "https://wikipedia.org/wiki/Metro-Goldwyn-Mayer"}, {"title": "Heaven's Gate (film)", "link": "https://wikipedia.org/wiki/Heaven%27s_Gate_(film)"}]}, {"year": "1982", "text": "Falklands War: A British amphibious assault during Operation Sutton leads to the Battle of San Carlos.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: A British amphibious assault during <a href=\"https://wikipedia.org/wiki/Operation_Sutton\" title=\"Operation Sutton\">Operation Sutton</a> leads to the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Carlos_(1982)\" title=\"Battle of San Carlos (1982)\">Battle of San Carlos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: A British amphibious assault during <a href=\"https://wikipedia.org/wiki/Operation_Sutton\" title=\"Operation Sutton\">Operation Sutton</a> leads to the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Carlos_(1982)\" title=\"Battle of San Carlos (1982)\">Battle of San Carlos</a>.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "Operation Sutton", "link": "https://wikipedia.org/wiki/Operation_Sutton"}, {"title": "Battle of San Carlos (1982)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON>_(1982)"}]}, {"year": "1988", "text": "<PERSON> holds her controversial <PERSON><PERSON> on the Mound before the General Assembly of the Church of Scotland.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> holds her controversial <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_on_the_Mound\" title=\"<PERSON><PERSON> on the Mound\"><PERSON><PERSON> on the Mound</a> before the <a href=\"https://wikipedia.org/wiki/General_Assembly_of_the_Church_of_Scotland\" title=\"General Assembly of the Church of Scotland\">General Assembly of the Church of Scotland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> holds her controversial <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_on_the_Mound\" title=\"<PERSON><PERSON> on the Mound\"><PERSON><PERSON> on the Mound</a> before the <a href=\"https://wikipedia.org/wiki/General_Assembly_of_the_Church_of_Scotland\" title=\"General Assembly of the Church of Scotland\">General Assembly of the Church of Scotland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sermon on the Mound", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_on_the_Mound"}, {"title": "General Assembly of the Church of Scotland", "link": "https://wikipedia.org/wiki/General_Assembly_of_the_Church_of_Scotland"}]}, {"year": "1991", "text": "Former Indian Prime Minister <PERSON><PERSON> is assassinated by a female suicide bomber near Madras.", "html": "1991 - Former Indian <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> by a female suicide bomber near <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Madras</a>.", "no_year_html": "Former Indian <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON>\">assassinated</a> by a female suicide bomber near <a href=\"https://wikipedia.org/wiki/Chennai\" title=\"Chennai\">Madras</a>.", "links": [{"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Chennai", "link": "https://wikipedia.org/wiki/Chennai"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, president of the People's Democratic Republic of Ethiopia, flees Ethiopia, effectively bringing the Ethiopian Civil War to an end.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Mengistu_Haile_Mariam\" title=\"Mengistu Haile Maria<PERSON>\"><PERSON><PERSON><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Republic_of_Ethiopia\" title=\"People's Democratic Republic of Ethiopia\">People's Democratic Republic of Ethiopia</a>, flees <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, effectively bringing the <a href=\"https://wikipedia.org/wiki/Ethiopian_Civil_War\" title=\"Ethiopian Civil War\">Ethiopian Civil War</a> to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mengis<PERSON>_Haile_Mariam\" title=\"Mengistu Haile <PERSON>\"><PERSON><PERSON><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/People%27s_Democratic_Republic_of_Ethiopia\" title=\"People's Democratic Republic of Ethiopia\">People's Democratic Republic of Ethiopia</a>, flees <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, effectively bringing the <a href=\"https://wikipedia.org/wiki/Ethiopian_Civil_War\" title=\"Ethiopian Civil War\">Ethiopian Civil War</a> to an end.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "People's Democratic Republic of Ethiopia", "link": "https://wikipedia.org/wiki/People%27s_Democratic_Republic_of_Ethiopia"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Ethiopian Civil War", "link": "https://wikipedia.org/wiki/Ethiopian_Civil_War"}]}, {"year": "1992", "text": "After 30 seasons <PERSON> hosted his penultimate episode and last featuring guests (<PERSON> and <PERSON><PERSON>) of The Tonight Show.", "html": "1992 - After 30 seasons <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hosted his penultimate episode and last featuring guests (<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Midler\"><PERSON><PERSON></a>) of <i><a href=\"https://wikipedia.org/wiki/The_Tonight_Show\" title=\"The Tonight Show\">The Tonight Show</a></i>.", "no_year_html": "After 30 seasons <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> hosted his penultimate episode and last featuring guests (<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Midler\"><PERSON><PERSON></a>) of <i><a href=\"https://wikipedia.org/wiki/The_Tonight_Show\" title=\"The Tonight Show\">The Tonight Show</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "The Tonight Show", "link": "https://wikipedia.org/wiki/The_Tonight_Show"}]}, {"year": "1994", "text": "The Democratic Republic of Yemen unsuccessfully attempts to secede from the Republic of Yemen; a war breaks out.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Yemen\" title=\"Democratic Republic of Yemen\">Democratic Republic of Yemen</a> unsuccessfully attempts to secede from the <a href=\"https://wikipedia.org/wiki/Republic_of_Yemen\" class=\"mw-redirect\" title=\"Republic of Yemen\">Republic of Yemen</a>; a <a href=\"https://wikipedia.org/wiki/Yemeni_Civil_War_(1994)\" class=\"mw-redirect\" title=\"Yemeni Civil War (1994)\">war</a> breaks out.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Yemen\" title=\"Democratic Republic of Yemen\">Democratic Republic of Yemen</a> unsuccessfully attempts to secede from the <a href=\"https://wikipedia.org/wiki/Republic_of_Yemen\" class=\"mw-redirect\" title=\"Republic of Yemen\">Republic of Yemen</a>; a <a href=\"https://wikipedia.org/wiki/Yemeni_Civil_War_(1994)\" class=\"mw-redirect\" title=\"Yemeni Civil War (1994)\">war</a> breaks out.", "links": [{"title": "Democratic Republic of Yemen", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Yemen"}, {"title": "Republic of Yemen", "link": "https://wikipedia.org/wiki/Republic_of_Yemen"}, {"title": "Yemeni Civil War (1994)", "link": "https://wikipedia.org/wiki/Yemeni_Civil_War_(1994)"}]}, {"year": "1996", "text": "The ferry MV Bukoba sinks in Tanzanian waters on Lake Victoria, killing nearly 1,000.", "html": "1996 - The ferry <a href=\"https://wikipedia.org/wiki/MV_Bukoba\" title=\"MV Bukoba\">MV <i>Bukoba</i></a> sinks in <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzanian</a> waters on <a href=\"https://wikipedia.org/wiki/Lake_Victoria\" title=\"Lake Victoria\">Lake Victoria</a>, killing nearly 1,000.", "no_year_html": "The ferry <a href=\"https://wikipedia.org/wiki/MV_Bukoba\" title=\"MV Bukoba\">MV <i>Bukoba</i></a> sinks in <a href=\"https://wikipedia.org/wiki/Tanzania\" title=\"Tanzania\">Tanzanian</a> waters on <a href=\"https://wikipedia.org/wiki/Lake_Victoria\" title=\"Lake Victoria\">Lake Victoria</a>, killing nearly 1,000.", "links": [{"title": "MV Bukoba", "link": "https://wikipedia.org/wiki/MV_Bukoba"}, {"title": "Tanzania", "link": "https://wikipedia.org/wiki/Tanzania"}, {"title": "Lake Victoria", "link": "https://wikipedia.org/wiki/Lake_Victoria"}]}, {"year": "1996", "text": "The seven Trappist monks of Tibhirine that were abducted on March 27 are killed under uncertain circumstances.", "html": "1996 - The seven <a href=\"https://wikipedia.org/wiki/Trappists\" title=\"Trappists\">Trappist</a> monks of <a href=\"https://wikipedia.org/wiki/Abbey_of_Our_Lady_of_Atlas\" title=\"Abbey of Our Lady of Atlas\">T<PERSON><PERSON><PERSON></a> that were abducted on March 27 <a href=\"https://wikipedia.org/wiki/Murder_of_the_monks_of_Tibhirine\" title=\"Murder of the monks of Tibhirine\">are killed</a> under uncertain circumstances.", "no_year_html": "The seven <a href=\"https://wikipedia.org/wiki/Trappists\" title=\"Trappists\">Trappist</a> monks of <a href=\"https://wikipedia.org/wiki/Abbey_of_Our_Lady_of_Atlas\" title=\"Abbey of Our Lady of Atlas\">T<PERSON><PERSON><PERSON></a> that were abducted on March 27 <a href=\"https://wikipedia.org/wiki/Murder_of_the_monks_of_Tibhirine\" title=\"Murder of the monks of Tibhirine\">are killed</a> under uncertain circumstances.", "links": [{"title": "Trappists", "link": "https://wikipedia.org/wiki/Trappists"}, {"title": "Abbey of Our Lady of Atlas", "link": "https://wikipedia.org/wiki/Abbey_of_Our_Lady_of_Atlas"}, {"title": "Murder of the monks of Tibhirine", "link": "https://wikipedia.org/wiki/Murder_of_the_monks_of_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1998", "text": "In Miami, five abortion clinics are attacked by a butyric acid attacker.", "html": "1998 - In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, five <a href=\"https://wikipedia.org/wiki/Abortion_clinic\" title=\"Abortion clinic\">abortion clinics</a> are attacked by a <a href=\"https://wikipedia.org/wiki/Butyric_acid\" title=\"Butyric acid\">butyric acid</a> attacker.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, five <a href=\"https://wikipedia.org/wiki/Abortion_clinic\" title=\"Abortion clinic\">abortion clinics</a> are attacked by a <a href=\"https://wikipedia.org/wiki/Butyric_acid\" title=\"Butyric acid\">butyric acid</a> attacker.", "links": [{"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}, {"title": "Abortion clinic", "link": "https://wikipedia.org/wiki/Abortion_clinic"}, {"title": "Butyric acid", "link": "https://wikipedia.org/wiki/Butyric_acid"}]}, {"year": "1998", "text": "President <PERSON><PERSON><PERSON> of Indonesia resigns following the killing of students from Trisakti University earlier that week by security forces and growing mass protests in Jakarta against his ongoing corrupt rule.", "html": "1998 - President <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suharto\">Suharto</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> resigns following the killing of students from <a href=\"https://wikipedia.org/wiki/Trisakti_shootings\" title=\"Trisakti shootings\">Trisakti University</a> earlier that week by security forces and growing mass protests in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a> against his ongoing corrupt rule.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suhart<PERSON>\">Suharto</a> of <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a> resigns following the killing of students from <a href=\"https://wikipedia.org/wiki/Trisakti_shootings\" title=\"Trisakti shootings\">Trisakti University</a> earlier that week by security forces and growing mass protests in <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a> against his ongoing corrupt rule.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Su<PERSON>o"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Trisakti shootings", "link": "https://wikipedia.org/wiki/Trisakti_shootings"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}]}, {"year": "2000", "text": "Nineteen people are killed in a plane crash in Wilkes-Barre, Pennsylvania.", "html": "2000 - Nineteen people are killed in a <a href=\"https://wikipedia.org/wiki/2000_East_Coast_Aviation_Services_British_Aerospace_Jetstream_crash\" title=\"2000 East Coast Aviation Services British Aerospace Jetstream crash\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Wilkes-Barre,_Pennsylvania\" title=\"Wilkes-Barre, Pennsylvania\">Wilkes-Barre, Pennsylvania</a>.", "no_year_html": "Nineteen people are killed in a <a href=\"https://wikipedia.org/wiki/2000_East_Coast_Aviation_Services_British_Aerospace_Jetstream_crash\" title=\"2000 East Coast Aviation Services British Aerospace Jetstream crash\">plane crash</a> in <a href=\"https://wikipedia.org/wiki/Wilkes-<PERSON>,_Pennsylvania\" title=\"Wilkes-Barre, Pennsylvania\">Wilkes-Barre, Pennsylvania</a>.", "links": [{"title": "2000 East Coast Aviation Services British Aerospace Jetstream crash", "link": "https://wikipedia.org/wiki/2000_East_Coast_Aviation_Services_British_Aerospace_Jetstream_crash"}, {"title": "Wilkes-Barre, Pennsylvania", "link": "https://wikipedia.org/wiki/Wilkes-Barre,_Pennsylvania"}]}, {"year": "2001", "text": "French Taubira law is enacted, officially recognizing the Atlantic slave trade and slavery as crimes against humanity.", "html": "2001 - French <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Christiane Taubira\">Taubira law</a> is enacted, officially recognizing the <a href=\"https://wikipedia.org/wiki/Atlantic_slave_trade\" title=\"Atlantic slave trade\">Atlantic slave trade</a> and <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> as <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Christian<PERSON> Taubir<PERSON>\">Taubira law</a> is enacted, officially recognizing the <a href=\"https://wikipedia.org/wiki/Atlantic_slave_trade\" title=\"Atlantic slave trade\">Atlantic slave trade</a> and <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> as <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Atlantic slave trade", "link": "https://wikipedia.org/wiki/Atlantic_slave_trade"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}]}, {"year": "2003", "text": "The 6.8 Mw  Boumerdès earthquake shakes northern Algeria with a maximum Mercalli intensity of X (Extreme). More than 2,200 people were killed and a moderate tsunami sank boats at the Balearic Islands.", "html": "2003 - The 6.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2003_Boumerd%C3%A8s_earthquake\" title=\"2003 Boumerdès earthquake\">Boumerdès earthquake</a> shakes northern <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>). More than 2,200 people were killed and a moderate tsunami sank boats at the <a href=\"https://wikipedia.org/wiki/Balearic_Islands\" title=\"Balearic Islands\">Balearic Islands</a>.", "no_year_html": "The 6.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2003_Boumerd%C3%A8s_earthquake\" title=\"2003 Boumerdès earthquake\">Boumerdès earthquake</a> shakes northern <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>). More than 2,200 people were killed and a moderate tsunami sank boats at the <a href=\"https://wikipedia.org/wiki/Balearic_Islands\" title=\"Balearic Islands\">Balearic Islands</a>.", "links": [{"title": "2003 Boumerdès earthquake", "link": "https://wikipedia.org/wiki/2003_Boumerd%C3%A8s_earthquake"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}, {"title": "Balearic Islands", "link": "https://wikipedia.org/wiki/Balearic_Islands"}]}, {"year": "2005", "text": "The tallest roller coaster in the world, Kingda Ka opens at Six Flags Great Adventure in Jackson Township, New Jersey.", "html": "2005 - The tallest <a href=\"https://wikipedia.org/wiki/Roller_coaster\" title=\"Roller coaster\">roller coaster</a> in the world, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ka\" title=\"Kingda Ka\"><PERSON><PERSON> Ka</a> opens at <a href=\"https://wikipedia.org/wiki/Six_Flags_Great_Adventure\" title=\"Six Flags Great Adventure\">Six Flags Great Adventure</a> in <a href=\"https://wikipedia.org/wiki/Jackson_Township,_New_Jersey\" title=\"Jackson Township, New Jersey\">Jackson Township, New Jersey</a>.", "no_year_html": "The tallest <a href=\"https://wikipedia.org/wiki/Roller_coaster\" title=\"Roller coaster\">roller coaster</a> in the world, <a href=\"https://wikipedia.org/wiki/King<PERSON>_Ka\" title=\"Kingda Ka\"><PERSON><PERSON> Ka</a> opens at <a href=\"https://wikipedia.org/wiki/Six_Flags_Great_Adventure\" title=\"Six Flags Great Adventure\">Six Flags Great Adventure</a> in <a href=\"https://wikipedia.org/wiki/Jackson_Township,_New_Jersey\" title=\"Jackson Township, New Jersey\">Jackson Township, New Jersey</a>.", "links": [{"title": "Roller coaster", "link": "https://wikipedia.org/wiki/Roller_coaster"}, {"title": "<PERSON><PERSON> Ka", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Six Flags Great Adventure", "link": "https://wikipedia.org/wiki/Six_Flags_Great_Adventure"}, {"title": "Jackson Township, New Jersey", "link": "https://wikipedia.org/wiki/Jackson_Township,_New_Jersey"}]}, {"year": "2006", "text": "The Republic of Montenegro holds a referendum proposing independence from the State Union of Serbia and Montenegro; 55% of Montenegrins vote for independence.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Montenegro\" class=\"mw-redirect\" title=\"Republic of Montenegro\">Republic of Montenegro</a> holds a <a href=\"https://wikipedia.org/wiki/Montenegrin_independence_referendum,_2006\" class=\"mw-redirect\" title=\"Montenegrin independence referendum, 2006\">referendum</a> proposing independence from the <a href=\"https://wikipedia.org/wiki/State_Union_of_Serbia_and_Montenegro\" class=\"mw-redirect\" title=\"State Union of Serbia and Montenegro\">State Union of Serbia and Montenegro</a>; 55% of Montenegrins vote for independence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Montenegro\" class=\"mw-redirect\" title=\"Republic of Montenegro\">Republic of Montenegro</a> holds a <a href=\"https://wikipedia.org/wiki/Montenegrin_independence_referendum,_2006\" class=\"mw-redirect\" title=\"Montenegrin independence referendum, 2006\">referendum</a> proposing independence from the <a href=\"https://wikipedia.org/wiki/State_Union_of_Serbia_and_Montenegro\" class=\"mw-redirect\" title=\"State Union of Serbia and Montenegro\">State Union of Serbia and Montenegro</a>; 55% of Montenegrins vote for independence.", "links": [{"title": "Republic of Montenegro", "link": "https://wikipedia.org/wiki/Republic_of_Montenegro"}, {"title": "Montenegrin independence referendum, 2006", "link": "https://wikipedia.org/wiki/Montenegrin_independence_referendum,_2006"}, {"title": "State Union of Serbia and Montenegro", "link": "https://wikipedia.org/wiki/State_Union_of_Serbia_and_Montenegro"}]}, {"year": "2010", "text": "JAXA, the Japan Aerospace Exploration Agency, launches the solar-sail spacecraft IKAROS aboard an H-IIA rocket. The vessel would make a Venus flyby late in the year.", "html": "2010 - JAXA, the <a href=\"https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency\" class=\"mw-redirect\" title=\"Japan Aerospace Exploration Agency\">Japan Aerospace Exploration Agency</a>, launches the <a href=\"https://wikipedia.org/wiki/Solar-sail\" class=\"mw-redirect\" title=\"Solar-sail\">solar-sail</a> spacecraft <a href=\"https://wikipedia.org/wiki/IKAROS\" title=\"IKAROS\">IKAROS</a> aboard an <a href=\"https://wikipedia.org/wiki/H-IIA\" title=\"H-IIA\">H-IIA</a> rocket. The vessel would make a Venus flyby late in the year.", "no_year_html": "JAXA, the <a href=\"https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency\" class=\"mw-redirect\" title=\"Japan Aerospace Exploration Agency\">Japan Aerospace Exploration Agency</a>, launches the <a href=\"https://wikipedia.org/wiki/Solar-sail\" class=\"mw-redirect\" title=\"Solar-sail\">solar-sail</a> spacecraft <a href=\"https://wikipedia.org/wiki/IKAROS\" title=\"IKAROS\">IKAROS</a> aboard an <a href=\"https://wikipedia.org/wiki/H-IIA\" title=\"H-IIA\">H-IIA</a> rocket. The vessel would make a Venus flyby late in the year.", "links": [{"title": "Japan Aerospace Exploration Agency", "link": "https://wikipedia.org/wiki/Japan_Aerospace_Exploration_Agency"}, {"title": "Solar-sail", "link": "https://wikipedia.org/wiki/Solar-sail"}, {"title": "IKAROS", "link": "https://wikipedia.org/wiki/IKAROS"}, {"title": "H-IIA", "link": "https://wikipedia.org/wiki/H-IIA"}]}, {"year": "2011", "text": "Radio broadcaster <PERSON> predicted that the world would end on this date.", "html": "2011 - Radio broadcaster <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ing\"><PERSON></a> predicted that the <a href=\"https://wikipedia.org/wiki/2011_end_times_prediction\" title=\"2011 end times prediction\">world would end</a> on this date.", "no_year_html": "Radio broadcaster <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> predicted that the <a href=\"https://wikipedia.org/wiki/2011_end_times_prediction\" title=\"2011 end times prediction\">world would end</a> on this date.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2011 end times prediction", "link": "https://wikipedia.org/wiki/2011_end_times_prediction"}]}, {"year": "2012", "text": "A bus accident near Himara, Albania kills 13 people and injures 21 others.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Qafa_e_Vish%C3%ABs_bus_accident\" title=\"Qafa e Vishë<PERSON> bus accident\">bus accident</a> near <a href=\"https://wikipedia.org/wiki/Himara\" class=\"mw-redirect\" title=\"Himara\">Himara, Albania</a> kills 13 people and injures 21 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Qafa_e_Vish%C3%ABs_bus_accident\" title=\"Qafa e Vishës bus accident\">bus accident</a> near <a href=\"https://wikipedia.org/wiki/Himara\" class=\"mw-redirect\" title=\"Himara\">Himara, Albania</a> kills 13 people and injures 21 others.", "links": [{"title": "Q<PERSON><PERSON> e Vishës bus accident", "link": "https://wikipedia.org/wiki/Qafa_e_Vish%C3%ABs_bus_accident"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Himara"}]}, {"year": "2012", "text": "A suicide bombing kills more than 120 people in Sana'a, Yemen.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/2012_Sana%27a_bombing\" class=\"mw-redirect\" title=\"2012 Sana'a bombing\">A suicide bombing</a> kills more than 120 people in <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a, Yemen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2012_Sana%27a_bombing\" class=\"mw-redirect\" title=\"2012 Sana'a bombing\">A suicide bombing</a> kills more than 120 people in <a href=\"https://wikipedia.org/wiki/Sana%27a\" class=\"mw-redirect\" title=\"Sana'a\">Sana'a, Yemen</a>.", "links": [{"title": "2012 Sana'a bombing", "link": "https://wikipedia.org/wiki/2012_Sana%27a_bombing"}, {"title": "Sana'a", "link": "https://wikipedia.org/wiki/Sana%27a"}]}, {"year": "2014", "text": "Random killings occurred on the Bannan Line of the Taipei MRT, killing four and injuring 24.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/2014_Taipei_Metro_attack\" title=\"2014 Taipei Metro attack\">Random killings</a> occurred on the <a href=\"https://wikipedia.org/wiki/Bannan_Line\" class=\"mw-redirect\" title=\"Bannan Line\">Bannan Line</a> of the <a href=\"https://wikipedia.org/wiki/Taipei_MRT\" class=\"mw-redirect\" title=\"Taipei MRT\">Taipei MRT</a>, killing four and injuring 24.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2014_Taipei_Metro_attack\" title=\"2014 Taipei Metro attack\">Random killings</a> occurred on the <a href=\"https://wikipedia.org/wiki/Bannan_Line\" class=\"mw-redirect\" title=\"Bannan Line\">Bannan Line</a> of the <a href=\"https://wikipedia.org/wiki/Taipei_MRT\" class=\"mw-redirect\" title=\"Taipei MRT\">Taipei MRT</a>, killing four and injuring 24.", "links": [{"title": "2014 Taipei Metro attack", "link": "https://wikipedia.org/wiki/2014_Taipei_Metro_attack"}, {"title": "Bannan Line", "link": "https://wikipedia.org/wiki/Bannan_Line"}, {"title": "Taipei MRT", "link": "https://wikipedia.org/wiki/Taipei_MRT"}]}, {"year": "2017", "text": "Ringling Bros. and Barnum & Bailey Circus performed their final show at Nassau Veterans Memorial Coliseum.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_Circus\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> performed their final show at Nassau Veterans Memorial Coliseum.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_Circus\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> performed their final show at Nassau Veterans Memorial Coliseum.", "links": [{"title": "Ringling Bros. and Barnum & Bailey Circus", "link": "https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>"}]}], "Births": [{"year": "1471", "text": "<PERSON><PERSON>, German painter, engraver, and mathematician (d. 1528)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%BCrer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter, engraver, and mathematician (d. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%BCrer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter, engraver, and mathematician (d. 1528)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Albrecht_D%C3%BCrer"}]}, {"year": "1497", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Muslim jurist (d. 1547)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Al<PERSON>Hat<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Muslim jurist (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Al<PERSON>Hat<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Muslim jurist (d. 1547)", "links": [{"title": "Al<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1527", "text": "<PERSON> of Spain (d. 1598)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (d. 1598)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1653", "text": "<PERSON><PERSON><PERSON> of Austria, Queen of Poland (d. 1697)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Austria,_Queen_of_Poland\" title=\"<PERSON><PERSON><PERSON> of Austria, Queen of Poland\"><PERSON><PERSON><PERSON> of Austria, Queen of Poland</a> (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Austria,_Queen_of_Poland\" title=\"<PERSON><PERSON><PERSON> of Austria, Queen of Poland\"><PERSON><PERSON><PERSON> of Austria, Queen of Poland</a> (d. 1697)", "links": [{"title": "<PERSON><PERSON><PERSON> of Austria, Queen of Poland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Austria,_Queen_of_Poland"}]}, {"year": "1688", "text": "(O.S.) <PERSON>, English poet, essayist, and translator (d. 1744)", "html": "1688 - (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, essayist, and translator (d. 1744)", "no_year_html": "(<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>) <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, essayist, and translator (d. 1744)", "links": [{"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, American lawyer and judge (d. 1810)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, Irish-born, English physician and mineralogist (d. 1833)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Irish-born, English <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> and <a href=\"https://wikipedia.org/wiki/Mineralogy\" title=\"Mineralogy\">mineralogist</a> (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Irish-born, English <a href=\"https://wikipedia.org/wiki/Physician\" title=\"Physician\">physician</a> and <a href=\"https://wikipedia.org/wiki/Mineralogy\" title=\"Mineralogy\">mineralogist</a> (d. 1833)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}, {"title": "Physician", "link": "https://wikipedia.org/wiki/Physician"}, {"title": "Mineralogy", "link": "https://wikipedia.org/wiki/Mineralogy"}]}, {"year": "1759", "text": "<PERSON>, French lawyer and politician (d. 1820)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1775", "text": "<PERSON>, French soldier and politician (d. 1840)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON>, English prison reformer, philanthropist and Quaker (d. 1845)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prison reformer, philanthropist and Quaker (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prison reformer, philanthropist and Quaker (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, 6th Duke of Devonshire, English politician, Lord Chamberlain of the Household (d. 1858)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Duke_of_Devonshire\" title=\"<PERSON>, 6th Duke of Devonshire\"><PERSON>, 6th Duke of Devonshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the Household</a> (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Devonshire\" title=\"<PERSON>, 6th Duke of Devonshire\"><PERSON>, 6th Duke of Devonshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Chamberlain\" title=\"Lord Chamberlain\">Lord Chamberlain of the Household</a> (d. 1858)", "links": [{"title": "<PERSON>, 6th Duke of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Duke_of_Devonshire"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French mathematician and engineer (d. 1843)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Coriolis\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Coriolis\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French mathematician and engineer (d. 1843)", "links": [{"title": "Gaspard<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, English paleontologist (d. 1847)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paleontologist (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paleontologist (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "Princess <PERSON> of Sweden, Swedish princess (d. 1865)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a>, Swedish princess (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a>, Swedish princess (d. 1865)", "links": [{"title": "Princess <PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Sweden"}]}, {"year": "1806", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Duchess of Sutherland, English duchess (d. 1868)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Duchess_of_Sutherland\" title=\"<PERSON>, Duchess of Sutherland\"><PERSON>, Duchess of Sutherland</a>, English duchess (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>-<PERSON>,_Duchess_of_Sutherland\" title=\"<PERSON>, Duchess of Sutherland\"><PERSON>, Duchess of Sutherland</a>, English duchess (d. 1868)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Duchess of Sutherland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>-<PERSON>,_Duchess_of_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, Dutch Talmudist (d. 1890)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Dutch Talmudist (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Dutch Talmudist (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, American banker and politician (d. 1899)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gue\" title=\"<PERSON>\"><PERSON></a>, American banker and politician (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gue\" title=\"<PERSON>\"><PERSON></a>, American banker and politician (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Swiss painter (d. 1905)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech-Austrian physician and academic (d. 1884)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek\" title=\"František Chvostek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-Austrian physician and academic (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek\" title=\"František Chvostek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-Austrian physician and academic (d. 1884)", "links": [{"title": "František Chvostek", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Chvostek"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON>, Japanese soldier and politician (d. 1919)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Itaga<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Swiss lawyer and politician, and Nobel Prize laureate (d. 1914)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1843", "text": "<PERSON>, French jurist, educator, and Nobel Prize laureate (d. 1918)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, French jurist, educator, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, French jurist, educator, and <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1918)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jurist)"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1844", "text": "<PERSON>, French painter (d. 1910)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1849", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter (d. 1928)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Italian priest and volcanologist (d. 1914)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and volcanologist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and volcanologist (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, French police officer and politician, 64th Prime Minister of France, Nobel Prize laureate (d. 1925)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French police officer and politician, 64th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French police officer and politician, 64th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1853", "text": "<PERSON>, French politician (d. 1905)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American telegraphist (d. 1937)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American telegraphist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American telegraphist (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Uruguayan journalist and politician, President of Uruguay (d. 1929)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_y_Ord%C3%B3%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_y_Ord%C3%B3%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_y_Ord%C3%B3%C3%B1ez"}, {"title": "President of Uruguay", "link": "https://wikipedia.org/wiki/President_of_Uruguay"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, French mathematician (d. 1936)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_Goursat\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89douard_Goursat\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_Goursat"}]}, {"year": "1860", "text": "<PERSON>, Indonesian-Dutch  physician, physiologist, and academic, Nobel Prize laureate (d. 1927)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-Dutch physician, physiologist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian-Dutch physician, physiologist, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willem_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1861", "text": "<PERSON>, Argentinian physician and academic (d. 1918)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physician and academic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian physician and academic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON> of Austria (d. 1954)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_of_Austria\" title=\"<PERSON><PERSON><PERSON> of Austria\">Archdu<PERSON> of Austria</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_of_Austria\" title=\"<PERSON><PERSON><PERSON> of Austria\">Arch<PERSON><PERSON> of Austria</a> (d. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_of_Austria"}]}, {"year": "1864", "text": "Princess <PERSON><PERSON><PERSON><PERSON> of Belgium (d. 1945)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>%C3%A9phanie_of_Belgium\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Belgium\">Princess <PERSON><PERSON><PERSON><PERSON> of Belgium</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>%C3%A9<PERSON><PERSON>_of_Belgium\" title=\"Princess <PERSON><PERSON><PERSON><PERSON> of Belgium\">Princess <PERSON><PERSON><PERSON><PERSON> of Belgium</a> (d. 1945)", "links": [{"title": "Princess <PERSON><PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Princess_St%C3%A9<PERSON><PERSON>_of_Belgium"}]}, {"year": "1867", "text": "<PERSON>, American physician (d. 1939)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, German neurologist and academic (d. 1941)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American cyclist and engineer (d. 1930)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist and engineer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist and engineer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Romanian journalist, author, and poet (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Tudor_Arghezi\" title=\"Tudor Arghezi\"><PERSON></a>, Romanian journalist, author, and poet (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tudor_Arghezi\" title=\"Tudor Arghezi\"><PERSON></a>, Romanian journalist, author, and poet (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tudor_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Uruguayan poet and publisher (d. 1920)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez_y_<PERSON><PERSON>s\" title=\"<PERSON>\"><PERSON></a>, Uruguayan poet and publisher (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_y_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan poet and publisher (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_P%C3%A9rez_y_<PERSON><PERSON>s"}]}, {"year": "1885", "text": "Princess <PERSON> of Albania, (Princess <PERSON> of Schönburg-Waldenburg) (d. 1936)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Albania\" class=\"mw-redirect\" title=\"Princess <PERSON> of Albania\">Princess <PERSON> of Albania</a>, (Princess <PERSON> of Schönburg-Waldenburg) (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Albania\" class=\"mw-redirect\" title=\"Princess <PERSON> of Albania\">Princess <PERSON> of Albania</a>, (Princess <PERSON> of Schönburg-Waldenburg) (d. 1936)", "links": [{"title": "Princess <PERSON> of Albania", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Albania"}]}, {"year": "1893", "text": "<PERSON>, English cricketer (d. 1963)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1963)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1893", "text": "<PERSON>, Australian public servant (d. 1969)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican general, president (1934-1940) and father of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/L%C3%A1zaro_C%C3%A1rdenas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general, president (1934-1940) and father of <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas\" title=\"Cuauhtémoc Cárdenas\">Cuauhtémoc <PERSON></a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1zaro_C%C3%A1rdenas\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general, president (1934-1940) and father of <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas\" title=\"Cuauhtémoc Cárdenas\"><PERSON><PERSON>uhtémoc <PERSON></a> (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1zaro_C%C3%A1rdenas"}, {"title": "Cuauhtémoc <PERSON>as", "link": "https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas"}]}, {"year": "1898", "text": "<PERSON>, American physician and businessman, founded Occidental Petroleum (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and businessman, founded <a href=\"https://wikipedia.org/wiki/Occidental_Petroleum\" title=\"Occidental Petroleum\">Occidental Petroleum</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and businessman, founded <a href=\"https://wikipedia.org/wiki/Occidental_Petroleum\" title=\"Occidental Petroleum\">Occidental Petroleum</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Occidental Petroleum", "link": "https://wikipedia.org/wiki/Occidental_Petroleum"}]}, {"year": "1898", "text": "<PERSON>, Luxembourgian lawyer and judge (d. 1967)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian lawyer and judge (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Luxembourgian lawyer and judge (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_L%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American long jumper (d. 1932)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American long jumper (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American long jumper (d. 1932)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1898", "text": "<PERSON>, American painter and translator (d. 1976)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and translator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and translator (d. 1976)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1901", "text": "<PERSON>, Multiracial playwright and librarian (d. 1993)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Multiracial playwright and librarian (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Multiracial playwright and librarian (d. 1993)", "links": [{"title": "Regina <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American pianist, bandleader, and radio host (d. 1986)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, bandleader, and radio host (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, bandleader, and radio host (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American film producer and agent (d. 2000)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American film producer and agent (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American film producer and agent (d. 2000)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1901", "text": "<PERSON>, Belgian author and playwright (d. 1992)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and playwright (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and playwright (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American baseball player (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Hungarian-American architect and academic, designed the Ameritrust Tower (d. 1981)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Ameritrust_Tower\" class=\"mw-redirect\" title=\"Ameritrust Tower\">Ameritrust Tower</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Ameritrust_Tower\" class=\"mw-redirect\" title=\"Ameritrust Tower\">Ameritrust Tower</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ameritrust Tower", "link": "https://wikipedia.org/wiki/Ameritrust_Tower"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American director, producer, and screenwriter (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Litvak\" title=\"<PERSON><PERSON><PERSON> Litvak\"><PERSON><PERSON><PERSON></a>, Ukrainian-American director, producer, and screenwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tvak\" title=\"<PERSON><PERSON><PERSON> Litvak\"><PERSON><PERSON><PERSON></a>, Ukrainian-American director, producer, and screenwriter (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anatole_Litvak"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American author (d. 1986)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actor and director (d. 1981)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1981)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (d. 1943)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Waller\" title=\"<PERSON><PERSON> Waller\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Waller\" title=\"<PERSON><PERSON> Waller\"><PERSON><PERSON></a>, American singer-songwriter and pianist (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Waller"}]}, {"year": "1907", "text": "<PERSON>, American roller coaster designer (d. 1979)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American roller coaster designer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American roller coaster designer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Chinese painter and calligrapher (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and calligrapher (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and calligrapher (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American psychologist and academic (d. 1986)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American baseball player and coach (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Greek pianist and composer (d. 1976)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist and composer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek pianist and composer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, French novelist, diplomat, film director, aviator (d. 1980)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, diplomat, film director, aviator (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, diplomat, film director, aviator (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Indian Civil Service Officer and former Under Secretary-General of the UN (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian Civil Service Officer and former Under Secretary-General of the UN (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian Civil Service Officer and former Under Secretary-General of the UN (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American singer and actor (d. 1988)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dennis Day\"><PERSON></a>, American singer and actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dennis Day\"><PERSON></a>, American singer and actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Dutch sprinter and police officer (d. 2002)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter and police officer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sprinter and police officer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American author and screenwriter (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian-American actor and director (d. 1993)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American tuba player and educator (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American tuba player and educator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American tuba player and educator (d. 2007)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1920", "text": "<PERSON>, American businessman, co-founded the Music Man Company  (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Music_Man_(company)\" title=\"Music Man (company)\">Music Man Company</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Music_Man_(company)\" title=\"Music Man (company)\">Music Man Company</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Music Man (company)", "link": "https://wikipedia.org/wiki/Music_Man_(company)"}]}, {"year": "1921", "text": "<PERSON>, English computer scientist and academic, designed OXO (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic, designed <i><a href=\"https://wikipedia.org/wiki/OXO_(video_game)\" title=\"OXO (video game)\">OXO</a></i> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and academic, designed <i><a href=\"https://wikipedia.org/wiki/OXO_(video_game)\" title=\"OXO (video game)\">OXO</a></i> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "OXO (video game)", "link": "https://wikipedia.org/wiki/OXO_(video_game)"}]}, {"year": "1921", "text": "<PERSON>, Russian physicist and academic, Nobel Prize laureate (d. 1989)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1923", "text": "<PERSON>, American photographer (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American photographer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American photographer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Swiss-American mathematician and academic (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American mathematician and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American mathematician and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American football player and coach (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Australian feminist poet, novelist and playwright (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian feminist poet, novelist and playwright (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian feminist poet, novelist and playwright (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actress (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress, comedian, and game show panelist (d. 1999)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and game show panelist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and game show panelist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American novelist, essayist, and poet (d. 2005)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actress and comedian (d. 1959)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and comedian (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Hungarian businessman and diplomat (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/P%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian businessman and diplomat (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian businessman and diplomat (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_Zwack"}]}, {"year": "1928", "text": "<PERSON>, American radio host and producer (d. 1975)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, American radio host and producer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)\" title=\"<PERSON> (DJ)\"><PERSON></a>, American radio host and producer (d. 1975)", "links": [{"title": "<PERSON> (DJ)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(DJ)"}]}, {"year": "1928", "text": "<PERSON>, American actress (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American drummer (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English silversmith and industrial designer (d. 2000)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English silversmith and industrial designer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, English silversmith and industrial designer (d. 2000)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>(designer)"}]}, {"year": "1930", "text": "<PERSON>, American bassist (d. 1982)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, New Zealand rugby player (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player (d. 2019)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1930", "text": "<PERSON>, Australian politician, 22nd Prime Minister of Australia (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Latvian javelin thrower and surgeon (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Inese_Jaunzeme\" title=\"Inese Jaunzeme\"><PERSON><PERSON></a>, Latvian javelin thrower and surgeon (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inese_Jaunzeme\" title=\"Inese Jaunzeme\">In<PERSON></a>, Latvian javelin thrower and surgeon (d. 2011)", "links": [{"title": "Inese <PERSON>", "link": "https://wikipedia.org/wiki/Inese_Jaunzeme"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Greek admiral and intelligence chief (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral and intelligence chief (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral and intelligence chief (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French trumpet player (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French trumpet player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French trumpet player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Russian weightlifter (d. 1993)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian weightlifter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian weightlifter (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Chinese-English journalist and author (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Innes\" title=\"<PERSON><PERSON><PERSON> Innes\"><PERSON><PERSON><PERSON></a>, Chinese-English journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Innes\" title=\"<PERSON><PERSON><PERSON> Innes\"><PERSON><PERSON><PERSON></a>, Chinese-English journalist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jocasta_Innes"}]}, {"year": "1934", "text": "<PERSON>, American horn player and bandleader (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bob Northern\"><PERSON></a>, American horn player and bandleader (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bob_<PERSON>\" title=\"Bob Northern\"><PERSON></a>, American horn player and bandleader (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON> <PERSON>, Swedish biochemist and academic, Nobel Prize laureate (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1935", "text": "<PERSON>, English clarinet player and bandleader (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and bandleader (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English clarinet player and bandleader (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>foot"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Polish-American biologist and academic, Nobel Prize laureate (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnter_Blobel\" title=\"Günter Blobel\"><PERSON><PERSON><PERSON></a>, Polish-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCnter_Blobel\" title=\"Günter Blobel\"><PERSON><PERSON><PERSON>lo<PERSON></a>, Polish-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnter_Blobel"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1938", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Shot%22_<PERSON>\" title='<PERSON> \"Shot\" Williams'><PERSON> \"Shot\" <PERSON></a>, American singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Shot%22_<PERSON>\" title='<PERSON> \"Shot\" Williams'><PERSON> \"Shot\" <PERSON></a>, American singer (d. 2011)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Shot%22_Williams"}]}, {"year": "1939", "text": "<PERSON>, Swiss oboist, composer, and conductor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss oboist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss oboist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, 4th Baron <PERSON>, English photographer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English photographer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English photographer and politician", "links": [{"title": "<PERSON>, 4th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Baron <PERSON> of Wirral, English politician, Secretary of State for Wales", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Wirral\" title=\"<PERSON>, Baron <PERSON> of Wirral\"><PERSON>, Baron <PERSON> of Wirral</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Wirral\" title=\"<PERSON>, Baron <PERSON> of Wirral\"><PERSON>, Baron <PERSON> of Wirral</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Wirral", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Wirral"}, {"title": "Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Wales"}]}, {"year": "1942", "text": "<PERSON>, Australian swimmer (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American race car driver (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English pianist and composer (d. 1989)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English bass player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1943", "text": "<PERSON>, English guitarist and songwriter (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Valentine\"><PERSON></a>, English guitarist and songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Baroness <PERSON>, Iranian-English academic and politician (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Iranian-English academic and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON>, Baroness <PERSON></a>, Iranian-English academic and politician (d. 2022)", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author and entrepreneur (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and entrepreneur (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and entrepreneur (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Irish lawyer and politician, President of Ireland", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1945", "text": "<PERSON>, German physicist and astronaut", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>sch<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor, writer, and producer (d. 2017)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, writer, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, writer, and producer (d. 2017)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1946", "text": "<PERSON>, English-American screenwriter and producer (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American screenwriter and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian equestrian rider and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian equestrian rider and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian equestrian rider and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American physician and academic (d. 1992)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Turkish historian and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/%C4%B0lber_Ortayl%C4%B1\" title=\"<PERSON><PERSON>ber Ortaylı\"><PERSON><PERSON><PERSON></a>, Turkish historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0lber_Ortayl%C4%B1\" title=\"<PERSON>lber Ortaylı\"><PERSON><PERSON><PERSON></a>, Turkish historian and academic", "links": [{"title": "İlber Ortaylı", "link": "https://wikipedia.org/wiki/%C4%B0lber_Ortayl%C4%B1"}]}, {"year": "1948", "text": "<PERSON>, English author and critic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Maltese-Australian singer-songwriter and saxophonist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-Australian singer-songwriter and saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-Australian singer-songwriter and saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian-English actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish journalist and politician, UK Minister of State for Europe", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, UK <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, UK <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1948", "text": "<PERSON>, English-Australian singer-songwriter and musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Scottish journalist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, British police officer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(police_officer)\" title=\"<PERSON> (police officer)\"><PERSON></a>, British police officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(police_officer)\" title=\"<PERSON> (police officer)\"><PERSON></a>, British police officer", "links": [{"title": "<PERSON> (police officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(police_officer)"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English soprano", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English economist and journalist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor, screenwriter, and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Franken"}]}, {"year": "1951", "text": "<PERSON>, Irish lawyer and judge (d. 2016)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and judge (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and judge (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "Mr. T, American actor and wrestler", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Mr._T\" title=\"Mr. T\">Mr. T</a>, American actor and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr._T\" title=\"Mr. T\">Mr. T</a>, American actor and wrestler", "links": [{"title": "Mr. T", "link": "https://wikipedia.org/wiki/Mr._T"}]}, {"year": "1953", "text": "<PERSON>, Filipino actress and recording artist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and recording artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and recording artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, British politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American guitarist and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English field hockey player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_hockey)\" title=\"<PERSON> (field hockey)\"><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(field_hockey)\" title=\"<PERSON> (field hockey)\"><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON> (field hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(field_hockey)"}]}, {"year": "1955", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, English nurse and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Nadine Dorries\"><PERSON><PERSON></a>, English nurse and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Nadine Dorries\"><PERSON><PERSON></a>, English nurse and politician", "links": [{"title": "Nadine Dorries", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "Judge <PERSON>, American actor and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Judge_Reinhold\" title=\"Judge Reinhold\">Judge <PERSON><PERSON><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judge_Reinhold\" title=\"Judge Reinhold\">Judge <PERSON><PERSON><PERSON></a>, American actor and producer", "links": [{"title": "Judge <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judge_<PERSON><PERSON>hold"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Dutch actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>ndi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>ndi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_Soutendijk"}]}, {"year": "1958", "text": "<PERSON>, French fashion designer (d. 2015)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Christian_Audigier\" title=\"Christian Audigier\"><PERSON></a>, French fashion designer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Audigier\" title=\"Christian Audigier\"><PERSON></a>, French fashion designer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Audigier"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Canadian-Scottish computer scientist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Scottish computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-Scottish computer scientist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Indian-American fashion designer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Maldivian politician, 6th President of the Maldives", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maldivian politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Maldives\" title=\"President of the Maldives\">President of the Maldives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maldivian politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_Maldives\" title=\"President of the Maldives\">President of the Maldives</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}, {"title": "President of the Maldives", "link": "https://wikipedia.org/wiki/President_of_the_Maldives"}]}, {"year": "1960", "text": "<PERSON>, American serial killer (d. 1994)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Kent_Hrbek\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kent_Hrbek\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kent_Hrbek"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Indian actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian cricketer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian swimmer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Salnikov"}]}, {"year": "1962", "text": "<PERSON>, American composer and educator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American screenwriter and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American musician and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American musician and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American musician and producer", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1963", "text": "<PERSON>, English actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Salvadoran-American drummer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress and playwright", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Belarusian hurdler", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian professional wrestler (d. 2007)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional wrestler (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Belgian politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Estonian director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German-Australian rower", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Filipino actress and singer (d. 1985)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and singer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and singer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Georgian-Ukrainian journalist and director (d. 2000)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Ukrainian journalist and director (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Ukrainian journalist and director (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Rhodesian born English footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Rhodesian born English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Rhodesian born English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Slovenian hurdler", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Dorsey_Levens\" title=\"Dorsey Levens\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dorsey_Levens\" title=\"Dorsey Levens\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "Do<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Levens"}]}, {"year": "1970", "text": "<PERSON>, Australian surfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "The Notorious B<PERSON>I.G<PERSON>, American rapper (d. 1997)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Notorious_B.I.G.\" title=\"The Notorious B.I.G.\">The Notorious B.I.G.</a>, American rapper (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Notorious_B.I.G.\" title=\"The Notorious B.I.G.\">The Notorious B.I.G.</a>, American rapper (d. 1997)", "links": [{"title": "The Notorious B.I.G.", "link": "https://wikipedia.org/wiki/The_Notorious_B.I.G."}]}, {"year": "1973", "text": "<PERSON>, American golfer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English comedian, musician and television presenter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, musician and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, musician and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lk\" title=\"<PERSON><PERSON> Balk\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lk\" title=\"<PERSON><PERSON> Balk\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Fairuza Balk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lk"}]}, {"year": "1974", "text": "Havoc, American rapper and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Havoc_(musician)\" title=\"Havoc (musician)\">Havoc</a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Havoc_(musician)\" title=\"Havoc (musician)\">Havoc</a>, American rapper and producer", "links": [{"title": "Havoc (musician)", "link": "https://wikipedia.org/wiki/Havoc_(musician)"}]}, {"year": "1975", "text": "<PERSON>, Australian rugby league player and boxer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English snooker player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Moroccan runner (d. 2013)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan runner (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan runner (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, South African international footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ton_Fortune\" title=\"Quinton Fortune\"><PERSON><PERSON><PERSON></a>, South African international footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ton_<PERSON>\" title=\"Quinton Fortune\"><PERSON><PERSON><PERSON></a>, South African international footballer and coach", "links": [{"title": "<PERSON><PERSON>ton Fortune", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, German footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American rapper and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Max_B\" title=\"Max B\"><PERSON></a>, American rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_B\" title=\"Max B\"><PERSON></a>, American rapper and songwriter", "links": [{"title": "Max B", "link": "https://wikipedia.org/wiki/<PERSON>_B"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German-American porn actress and model", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American porn actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American porn actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Canadian basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Argentinian-Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Dami%C3%A1n_<PERSON>_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dami%C3%A1n_<PERSON>_%C3%81l<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian-Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dami%C3%A1n_Ariel_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish politician, Minister for Sport, Health Improvement and Mental Health", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport,_Health_Improvement_and_Mental_Health\" class=\"mw-redirect\" title=\"Minister for Sport, Health Improvement and Mental Health\">Minister for Sport, Health Improvement and Mental Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport,_Health_Improvement_and_Mental_Health\" class=\"mw-redirect\" title=\"Minister for Sport, Health Improvement and Mental Health\">Minister for Sport, Health Improvement and Mental Health</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Sport, Health Improvement and Mental Health", "link": "https://wikipedia.org/wiki/Minister_for_Sport,_Health_Improvement_and_Mental_Health"}]}, {"year": "1979", "text": "<PERSON>, Australian author and academic", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American mixed martial artist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fighter)\" title=\"<PERSON> (fighter)\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON> (fighter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fighter)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Czech musician/composer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech musician/composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech musician/composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Belgian-Australian singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Got<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Got<PERSON>\" title=\"Got<PERSON>\"><PERSON><PERSON></a>, Belgian-Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ye"}]}, {"year": "1981", "text": "<PERSON>, American ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American soccer player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Buddle\"><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Buddle\"><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Polish pole vaulter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Latvian tennis player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/L%C4%ABga_Dekmeijere\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C4%ABga_Dekmeijere\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C4%ABga_Dekmeijere"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>%C3%BAjo_Maia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>%C3%BAjo_Maia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON><PERSON>_Ara%C3%<PERSON><PERSON>_Maia"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, German volleyball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goller\"><PERSON></a>, German volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Goller\"><PERSON></a>, German volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ller"}]}, {"year": "1985", "text": "<PERSON>, Manx cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Norwegian swimmer (d. 2012)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian swimmer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian swimmer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English cricketer and sportscaster", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_H<PERSON>ck%C3%A1"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English rapper, producer, and actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, English rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, English rapper, producer, and actor", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Slovak footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Kuciak\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Ku<PERSON>k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1an_Ku<PERSON>k"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_L%27Estrange\" title=\"Heath L'Estrange\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_L%27Estrange\" title=\"Heath L'Estrange\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heath_L%27Estrange"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON>, Croatian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEuki%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEuki%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Mand%C5%BEuki%C4%87"}]}, {"year": "1986", "text": "<PERSON>, American singer and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Mexican race walker", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Eder_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eder_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican race walker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eder_S%C3%A1nchez"}]}, {"year": "1986", "text": "<PERSON>, South Korean singer-songwriter and dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Park_Sojin\" class=\"mw-redirect\" title=\"Park Sojin\"><PERSON></a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Park_Sojin\" class=\"mw-redirect\" title=\"Park Sojin\"><PERSON></a>, South Korean singer-songwriter and dancer", "links": [{"title": "Park Sojin", "link": "https://wikipedia.org/wiki/Park_Sojin"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beau Falloon\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Beau Falloon\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Beau_Falloon"}]}, {"year": "1988", "text": "<PERSON>, English Paralympic swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Paralympic swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Paralympic swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South Korean singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Park_Gyu-ri\" title=\"Park Gyu-ri\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Park_Gyu-ri\" title=\"<PERSON> Gyu-ri\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/Park_Gyu-ri"}]}, {"year": "1988", "text": "<PERSON><PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Estonian triple jumper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian triple jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, New Zealand actress and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Welsh footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Barbadian athlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Slovenian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>me_Costa_Marques\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Costa Marques\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>me_Costa_Marques\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Costa Marques\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>me_Costa_Marques"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dano\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1992", "text": "<PERSON>, Scottish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, German footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCneberg\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCneberg\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philipp_Gr%C3%BCneberg"}]}, {"year": "1992", "text": "<PERSON>, American singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Estonian biathlete", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aim\" title=\"<PERSON><PERSON><PERSON> Gaim\"><PERSON><PERSON><PERSON></a>, Estonian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aim\" title=\"<PERSON><PERSON><PERSON> Gaim\"><PERSON><PERSON><PERSON></a>, Estonian biathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aim"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American soccer player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" class=\"mw-redirect\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" class=\"mw-redirect\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1994", "text": "<PERSON>, English diver", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Filipino actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lo<PERSON>\"><PERSON></a>, Filipino actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lo<PERSON>\"><PERSON></a>, Filipino actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Dutch tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Indy_de_Vroome\" title=\"Indy de Vroome\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indy_de_Vroome\" title=\"Indy de Vroome\"><PERSON></a>, Dutch tennis player", "links": [{"title": "Indy de Vroome", "link": "https://wikipedia.org/wiki/Indy_de_Vroome"}]}, {"year": "1996", "text": "<PERSON>, Russian tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Italian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Ukrainian singer-songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viktoria_Petryk"}]}, {"year": "1997", "text": "<PERSON>, American actor and singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2002", "text": "<PERSON>, Spanish cancer activist and influencer (d. 2023)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2002\" title=\"2002\">2002</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cancer activist and influencer (d. <a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2002\" title=\"2002\">2002</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cancer activist and influencer (d. <a href=\"https://wikipedia.org/wiki/2023\" title=\"2023\">2023</a>)", "links": [{"title": "2002", "link": "https://wikipedia.org/wiki/2002"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2023", "link": "https://wikipedia.org/wiki/2023"}]}], "Deaths": [{"year": "252", "text": "<PERSON>, Chinese emperor of Eastern Wu (b. 182)", "html": "252 - <a href=\"https://wikipedia.org/wiki/Sun_Quan\" title=\"Sun Quan\"><PERSON></a>, Chinese emperor of <a href=\"https://wikipedia.org/wiki/Eastern_Wu\" title=\"Eastern Wu\">Eastern Wu</a> (b. 182)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Quan\" title=\"Sun Quan\"><PERSON></a>, Chinese emperor of <a href=\"https://wikipedia.org/wiki/Eastern_Wu\" title=\"Eastern Wu\">Eastern Wu</a> (b. 182)", "links": [{"title": "Sun <PERSON>", "link": "https://wikipedia.org/wiki/Sun_Quan"}, {"title": "Eastern Wu", "link": "https://wikipedia.org/wiki/Eastern_Wu"}]}, {"year": "954", "text": "<PERSON>, Chinese prince and chancellor (b. 882)", "html": "954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince and chancellor (b. 882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince and chancellor (b. 882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "987", "text": "<PERSON>, king of West Francia (b. c. 966)", "html": "987 - <a href=\"https://wikipedia.org/wiki/Louis_V_of_France\" title=\"Louis V of France\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (b. c. 966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_V_of_France\" title=\"Louis V of France\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (b. c. 966)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_V_of_France"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}]}, {"year": "1075", "text": "<PERSON><PERSON><PERSON> of Poland, queen of Hungary (b. 1013)", "html": "1075 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Hungary\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Hungary\"><PERSON><PERSON><PERSON> of Poland</a>, queen of Hungary (b. 1013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Hungary\" title=\"<PERSON><PERSON><PERSON> of Poland, Queen of Hungary\"><PERSON><PERSON><PERSON> of Poland</a>, queen of Hungary (b. 1013)", "links": [{"title": "<PERSON><PERSON><PERSON> of Poland, Queen of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Poland,_Queen_of_Hungary"}]}, {"year": "1086", "text": "<PERSON>, Chinese statesman and poet (b. 1021)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese statesman and poet (b. 1021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese statesman and poet (b. 1021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1237", "text": "<PERSON> the <PERSON>, Manx son of <PERSON><PERSON>", "html": "1237 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>\" title=\"<PERSON> the Black\"><PERSON> the <PERSON></a>, <PERSON>x son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Black\" title=\"<PERSON> the Black\"><PERSON> the Black</a>, <PERSON>x son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "links": [{"title": "<PERSON> the Black", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1254", "text": "<PERSON>, king of Germany (b. 1228)", "html": "1254 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"<PERSON> IV of Germany\"><PERSON> IV</a>, king of Germany (b. 1228)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"<PERSON> IV of Germany\"><PERSON> IV</a>, king of Germany (b. 1228)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/Conrad_IV_of_Germany"}]}, {"year": "1416", "text": "<PERSON> Celje, queen consort of Poland (b. 1386)", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Celje\" class=\"mw-redirect\" title=\"<PERSON> of Celje\"><PERSON> of Celje</a>, queen consort of Poland (b. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ce<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> of Celje\"><PERSON> of Celje</a>, queen consort of Poland (b. 1386)", "links": [{"title": "<PERSON> of Celje", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1471", "text": "<PERSON>, king of England (b. 1421)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England (b. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, king of England (b. 1421)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1481", "text": "<PERSON>, king of Denmark (b. 1426)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/Christian_I_of_Denmark\" title=\"Christian I of Denmark\"><PERSON></a>, king of Denmark (b. 1426)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_I_of_Denmark\" title=\"Christian I of Denmark\"><PERSON></a>, king of Denmark (b. 1426)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_I_of_Denmark"}]}, {"year": "1512", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian ruler (b. 1452)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1452)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1524", "text": "<PERSON>, 2nd Duke of Norfolk, English soldier and politician, Lord High Treasurer (b. 1443)", "html": "1524 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Norfolk\" title=\"<PERSON>, 2nd Duke of Norfolk\"><PERSON>, 2nd Duke of Norfolk</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Norfolk\" title=\"<PERSON>, 2nd Duke of Norfolk\"><PERSON>, 2nd Duke of Norfolk</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1443)", "links": [{"title": "<PERSON>, 2nd Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Norfolk"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1542", "text": "<PERSON><PERSON><PERSON>, Spanish-American explorer (b. 1496)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American explorer (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American explorer (b. 1496)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1563", "text": "<PERSON><PERSON>, Lithuanian writer (b. 1510)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/Martynas_Ma%C5%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian writer (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martynas_Ma%C5%BE<PERSON><PERSON>\" title=\"<PERSON><PERSON> Maž<PERSON>\"><PERSON><PERSON></a>, Lithuanian writer (b. 1510)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martynas_Ma%C5%BEvydas"}]}, {"year": "1607", "text": "<PERSON>, English scholar and academic (b. 1549)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1549)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON>, Spanish admiral and nobleman (b. c. 1556)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Spanish_Navy_officer)\" title=\"<PERSON> (Spanish Navy officer)\"><PERSON></a>, Spanish admiral and nobleman (b. <abbr title=\"circa\">c.</abbr> 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Spanish_Navy_officer)\" title=\"<PERSON> (Spanish Navy officer)\"><PERSON></a>, Spanish admiral and nobleman (b. <abbr title=\"circa\">c.</abbr> 1556)", "links": [{"title": "<PERSON> (Spanish Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Spanish_Navy_officer)"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian anatomist (b. 1537)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian anatomist (b. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian anatomist (b. 1537)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON><PERSON><PERSON>, Italian astrologer, theologian, and poet (b. 1568)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>o_Campanella\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astrologer, theologian, and poet (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lla\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian astrologer, theologian, and poet (b. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lla"}]}, {"year": "1647", "text": "<PERSON>, Dutch poet and playwright (b. 1581)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and playwright (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and playwright (b. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1650", "text": "<PERSON>, 1st Marquess of Montrose, Scottish general and politician (b. 1612)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montrose\" title=\"<PERSON>, 1st Marquess of Montrose\"><PERSON>, 1st Marquess of Montrose</a>, Scottish general and politician (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Montrose\" title=\"<PERSON>, 1st Marquess of Montrose\"><PERSON>, 1st Marquess of Montrose</a>, Scottish general and politician (b. 1612)", "links": [{"title": "<PERSON>, 1st Marquess of Montrose", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montrose"}]}, {"year": "1664", "text": "<PERSON>, English settler, founded Taunton, Massachusetts (b. 1588)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English settler, founded <a href=\"https://wikipedia.org/wiki/Taunton,_Massachusetts\" title=\"Taunton, Massachusetts\">Taunton, Massachusetts</a> (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English settler, founded <a href=\"https://wikipedia.org/wiki/Taunton,_Massachusetts\" title=\"Taunton, Massachusetts\">Taunton, Massachusetts</a> (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Taunton, Massachusetts", "link": "https://wikipedia.org/wiki/Taunton,_Massachusetts"}]}, {"year": "1670", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian astronomer and physicist (b. 1586)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astronomer and physicist (b. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astronomer and physicist (b. 1586)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1686", "text": "<PERSON>, German physicist and inventor of the Magdeburg Hemispheres (b. 1602)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and inventor of the <a href=\"https://wikipedia.org/wiki/Magdeburg_Hemispheres\" class=\"mw-redirect\" title=\"Magdeburg Hemispheres\">Magdeburg Hemispheres</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and inventor of the <a href=\"https://wikipedia.org/wiki/Magdeburg_Hemispheres\" class=\"mw-redirect\" title=\"Magdeburg Hemispheres\">Magdeburg Hemispheres</a> (b. 1602)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Magdeburg Hemispheres", "link": "https://wikipedia.org/wiki/Magdeburg_Hemispheres"}]}, {"year": "1690", "text": "<PERSON>, English-American minister and missionary (b. 1604)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, English-American minister and missionary (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, English-American minister and missionary (b. 1604)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>(missionary)"}]}, {"year": "1719", "text": "<PERSON>, French mystic and philosopher (b. 1646)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic and philosopher (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, 1st Earl of Oxford and Earl <PERSON>, English politician, Chancellor of the Exchequer (b. 1661)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_Earl_<PERSON>\" title=\"<PERSON>, 1st Earl of Oxford and Earl <PERSON>\"><PERSON>, 1st Earl of Oxford and Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of Oxford and Earl <PERSON>\"><PERSON>, 1st Earl of Oxford and <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1661)", "links": [{"title": "<PERSON>, 1st Earl of Oxford and Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Oxford_and_<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1742", "text": "<PERSON>, Swedish physician and academic (b. 1664)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and academic (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physician and academic (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Polish and Saxon general (b. 1695)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish and Saxon general (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish and Saxon general (b. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, English actor, playwright, and poet (b. 1722)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and poet (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and poet (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, German-Swedish chemist and pharmacist (b. 1742)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swedish chemist and pharmacist (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swedish chemist and pharmacist (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, English poet and critic (b. 1728)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, French diplomat and spy (b. 1728)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Chevalier_<PERSON>%27Eon\" class=\"mw-redirect\" title=\"Chevalier d'Eon\"><PERSON></a>, French diplomat and spy (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chevalier_<PERSON>%27Eon\" class=\"mw-redirect\" title=\"Chevalier d'Eon\"><PERSON></a>, French diplomat and spy (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chevalier_d%27Eon"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, 3rd <PERSON><PERSON> (b. 1768)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, 3rd <PERSON><PERSON></a> (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, 3rd <PERSON><PERSON></a> (b. 1768)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Italian priest and composer (b. 1775)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (b. 1783)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero\" title=\"<PERSON>güero\"><PERSON></a>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero\" title=\"<PERSON> Agüero\"><PERSON></a>, Peruvian soldier and politician, 1st President of Peru and 2nd President of North Peru (b. 1783)", "links": [{"title": "<PERSON> la Riva Agüero", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_la_Riva_Ag%C3%BCero"}]}, {"year": "1862", "text": "<PERSON>, Irish-American actor and manager (b. 1827)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish-American actor and manager (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Irish-American actor and manager (b. 1827)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1879", "text": "<PERSON>, Chilean lawyer and commander (b. 1848)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and commander (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean lawyer and commander (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, French anarchist (b. 1872)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>_(anarchist)\" title=\"<PERSON><PERSON> (anarchist)\"><PERSON><PERSON></a>, French anarchist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>_(anarchist)\" title=\"<PERSON><PERSON> (anarchist)\"><PERSON><PERSON></a>, French anarchist (b. 1872)", "links": [{"title": "<PERSON><PERSON> (anarchist)", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON>_(anarchist)"}]}, {"year": "1894", "text": "<PERSON>, German physicist and academic (b. 1839)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/August_Kundt\" title=\"August Kundt\">August <PERSON></a>, German physicist and academic (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Kundt\" title=\"August Kundt\">August <PERSON></a>, German physicist and academic (b. 1839)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_Kundt"}]}, {"year": "1895", "text": "<PERSON>, Austrian composer and conductor (b. 1819)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1901", "text": "<PERSON>, French rugby player (b. 1874)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, French rugby player (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, French rugby player (b. 1874)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>(rugby_union)"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Scottish-American astronomer and academic (b. 1857)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American astronomer and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American astronomer and academic (b. 1857)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Russian general and engineer (b. 1875)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and engineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and engineer (b. 1875)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ato"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian mathematician, crystallographer, and mineralogist (b. 1853)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>\" title=\"Evgra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician, crystallographer, and mineralogist (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>\" title=\"Evgra<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician, crystallographer, and mineralogist (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgra<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Mexican politician, 54th President of Mexico (b. 1859)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese agriculturalist, guardian of <PERSON><PERSON><PERSON><PERSON> (b. 1871)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Hidesabur%C5%8D_Ueno\" title=\"Hidesaburō Ueno\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese agriculturalist, guardian of <a href=\"https://wikipedia.org/wiki/Hachik%C5%8D\" title=\"Hachik<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hidesabur%C5%8D_Ueno\" title=\"Hidesaburō Ueno\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese agriculturalist, guardian of <a href=\"https://wikipedia.org/wiki/Hachik%C5%8D\" title=\"Hachik<PERSON>\">Ha<PERSON>k<PERSON></a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hidesabur%C5%8D_Ueno"}, {"title": "Hachikō", "link": "https://wikipedia.org/wiki/Hachik%C5%8D"}]}, {"year": "1926", "text": "<PERSON>, English-Italian author (b. 1886)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian author (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian author (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, 5th Earl of Rosebery, English politician, Prime Minister of the United Kingdom (b. 1847)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Rose<PERSON>\" title=\"<PERSON>, 5th Earl of Rosebery\"><PERSON>, 5th Earl of Rosebery</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Rose<PERSON>\" title=\"<PERSON>, 5th Earl of Rosebery\"><PERSON>, 5th Earl of Rosebery</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1847)", "links": [{"title": "<PERSON>, 5th Earl of Rosebery", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1932", "text": "<PERSON>, French fencer and author (b. 1873)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fencer and author (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fencer and author (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American activist and author, co-founded Hull House, Nobel Prize laureate (b. 1860)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author, co-founded <a href=\"https://wikipedia.org/wiki/Hull_House\" title=\"Hull House\">Hull House</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author, co-founded <a href=\"https://wikipedia.org/wiki/Hull_House\" title=\"Hull House\">Hull House</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hull House", "link": "https://wikipedia.org/wiki/Hull_House"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1935", "text": "<PERSON>, Dutch botanist and geneticist (b. 1848)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and geneticist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist and geneticist (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English footballer and manager (b. 1888)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, German-American novelist, playwright, and critic (b. 1906)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American novelist, playwright, and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American novelist, playwright, and critic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor (b. 1913)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English businessman and adventurer (b. 1877)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and adventurer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and adventurer (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Ukrainian-Russian singer-songwriter, actor, and poet (b. 1889)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian singer-songwriter, actor, and poet (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian singer-songwriter, actor, and poet (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (b. 1882)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1965", "text": "<PERSON>, French chef (b. 1898)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, French chef (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English pilot and engineer, designed the de Havilland Mosquito (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Havilland\"><PERSON></a>, English pilot and engineer, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Havilland_Mosquito\" title=\"De Havilland Mosquito\">de Havilland Mosquito</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Havilland\"><PERSON></a>, English pilot and engineer, designed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Havilland_Mosquito\" title=\"De Havilland Mosquito\">de Havilland Mosquito</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "De Havilland Mosquito", "link": "https://wikipedia.org/wiki/De_Havilland_Mosquito"}]}, {"year": "1968", "text": "<PERSON>, English actress (b. 1896)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-Australian biologist and author (b. 1885)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Australian biologist and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Australian biologist and author (b. 1885)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer, trumpet player, bandleader, and actor (b. 1911)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, trumpet player, bandleader, and actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Monroe\"><PERSON></a>, American singer, trumpet player, bandleader, and actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Soviet Marshal and general (b. 1897)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Marshal and general (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Marshal and general (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, PIRA volunteer and Hunger Striker (b. 1957)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA volunteer</a> and Hunger Striker (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA volunteer</a> and Hunger Striker (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PIRA", "link": "https://wikipedia.org/wiki/PIRA"}]}, {"year": "1981", "text": "<PERSON><PERSON>, INLA volunteer and Hunger Striker (b. 1957)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_National_Liberation_Army\" title=\"Irish National Liberation Army\">INLA volunteer</a> and Hunger Striker (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Hara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_National_Liberation_Army\" title=\"Irish National Liberation Army\">INLA volunteer</a> and Hunger Striker (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patsy_O%27Hara"}, {"title": "Irish National Liberation Army", "link": "https://wikipedia.org/wiki/Irish_National_Liberation_Army"}]}, {"year": "1983", "text": "<PERSON>, English historian and author (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress (b. 1891)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor and dancer (b. 1900)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and dancer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and dancer (b. 1900)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1991", "text": "<PERSON><PERSON>, Indian politician, 6th Prime Minister of India (b. 1944)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1995", "text": "<PERSON>, American captain and politician, 18th United States Secretary of Defense (b. 1938)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter and producer (b. 1957)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actor and producer (b. 1917)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>h_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>h_<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian art historian, art critic and conservator (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian art historian, art critic and conservator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian art historian, art critic and conservator (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and director (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English author (b. 1901)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English actor (b. 1904)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American businessman, founded Herbalife (b. 1956)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Herbalife\" title=\"Herbalife\">Herbalife</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Herbalife\" title=\"Herbalife\">Herbalife</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Herbalife", "link": "https://wikipedia.org/wiki/Herbalife"}]}, {"year": "2002", "text": "<PERSON><PERSON> <PERSON>, French-American sculptor and painter (b. 1930)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Niki_de_Saint_Phalle\" title=\"Niki de Saint Phalle\"><PERSON><PERSON> <PERSON> Phalle</a>, French-American sculptor and painter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niki_de_Saint_Phalle\" title=\"Niki de Saint Phalle\"><PERSON><PERSON> <PERSON> Saint Phalle</a>, French-American sculptor and painter (b. 1930)", "links": [{"title": "Niki de Saint Phalle", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>_Saint_Phalle"}]}, {"year": "2003", "text": "<PERSON>, Argentinian-Italian race car driver and businessman, founded <PERSON> (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American captain, banker, and politician, 41st Governor of Arkansas (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, banker, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, banker, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "2005", "text": "<PERSON>, American outsider artist (b. 1956)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Outsider_art\" title=\"Outsider art\">outsider artist</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Outsider_art\" title=\"Outsider art\">outsider artist</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Outsider art", "link": "https://wikipedia.org/wiki/Outsider_art"}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2005", "text": "<PERSON>, American actor and director (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American actor and director (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American race car driver (b. 1987)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1987)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2006", "text": "<PERSON>, American dancer, choreographer, and author (b. 1909)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer, choreographer, and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer, choreographer, and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Thai director, producer, and screenwriter (b. 1931)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>erd_<PERSON>ri\" title=\"Cherd Songsri\"><PERSON><PERSON></a>, Thai director, producer, and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Cherd Songsri\"><PERSON><PERSON></a>, Thai director, producer, and screenwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cherd_<PERSON>ri"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1929)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American butler and preacher, survivor of the Tulsa race riot (b. 1903)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American butler and preacher, survivor of the <a href=\"https://wikipedia.org/wiki/Tulsa_race_riot\" class=\"mw-redirect\" title=\"Tulsa race riot\">Tulsa race riot</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American butler and preacher, survivor of the <a href=\"https://wikipedia.org/wiki/Tulsa_race_riot\" class=\"mw-redirect\" title=\"Tulsa race riot\">Tulsa race riot</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tulsa race riot", "link": "https://wikipedia.org/wiki/Tulsa_race_riot"}]}, {"year": "2012", "text": "<PERSON> of Irinoupolis, Metropolitan of Irinoupolis and Primate of the Ukrainian Orthodox Church of the USA (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Constantine_of_Irinoupolis\" title=\"<PERSON> of Irinoupolis\"><PERSON> of Irinoupolis</a>, Metropolitan of Irinoupolis and Primate of the Ukrainian Orthodox Church of the USA (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_of_Irinoupolis\" title=\"<PERSON> of Irinoupolis\"><PERSON> of Irinoupolis</a>, Metropolitan of Irinoupolis and Primate of the Ukrainian Orthodox Church of the USA (b. 1936)", "links": [{"title": "Constantine of Irinoupolis", "link": "https://wikipedia.org/wiki/Constantine_of_Irinoupolis"}]}, {"year": "2012", "text": "<PERSON>, Georgian commander (b. 1964)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Roman_Dumba<PERSON>ze\" title=\"Roman Dumbadze\"><PERSON></a>, Georgian commander (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Dumbadze\" title=\"Roman Dumbadze\"><PERSON></a>, Georgian commander (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Du<PERSON>dze"}]}, {"year": "2012", "text": "<PERSON>, Cuban boxer (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Cuban boxer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Cuban boxer (b. 1950)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/Douglas_Rodr%C3%<PERSON><PERSON><PERSON>_(boxer)"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1952)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2012", "text": "<PERSON>, Australian anthropologist and academic (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian anthropologist and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian anthropologist and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "Count <PERSON> of Rosenborg, member of the Danish royal family (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_of_Rosenborg\" title=\"Count <PERSON> of Rosenborg\">Count <PERSON> of Rosenborg</a>, member of the Danish royal family (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_of_Rosenborg\" title=\"Count <PERSON> of Rosenborg\">Count <PERSON> of Rosenborg</a>, member of the Danish royal family (b. 1942)", "links": [{"title": "Count <PERSON> of Rosenborg", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_Rosenborg"}]}, {"year": "2013", "text": "<PERSON>, American trombonist, composer, and conductor (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and conductor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer, and conductor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_<PERSON>mstock"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American baseball player and coach (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Cot_Deal\" title=\"Cot Deal\"><PERSON><PERSON></a>, American baseball player and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cot_Deal\" title=\"Cot Deal\"><PERSON><PERSON></a>, American baseball player and coach (b. 1923)", "links": [{"title": "Cot Deal", "link": "https://wikipedia.org/wiki/Cot_Deal"}]}, {"year": "2013", "text": "<PERSON>, American businessman, co-founded <PERSON><PERSON><PERSON> (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Snapple\" title=\"Snapple\">Snap<PERSON></a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Snapple\" title=\"Snapple\">Snap<PERSON></a> (b. 1933)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)"}, {"title": "Snapple", "link": "https://wikipedia.org/wiki/Snapple"}]}, {"year": "2013", "text": "<PERSON>, American pianist and composer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (b. 1924)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON>, French journalist and historian (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and historian (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and historian (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Malaysian son of <PERSON><PERSON><PERSON> of Kedah (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Tunku_Annuar\" title=\"Tunku Annuar\"><PERSON><PERSON><PERSON></a>, Malaysian son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Kedah\" title=\"<PERSON><PERSON><PERSON> of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tunku_Annuar\" title=\"Tunku Annuar\"><PERSON><PERSON><PERSON></a>, Malaysian son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Kedah\" title=\"<PERSON><PERSON><PERSON> of Kedah\"><PERSON><PERSON><PERSON> of Kedah</a> (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nk<PERSON>_Annuar"}, {"title": "Badlishah of Kedah", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Kedah"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1926)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2014", "text": "<PERSON>, Venezuelan physician and politician, President of Venezuela (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan physician and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan physician and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Iranian wrestler (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian wrestler (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian wrestler (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Italian race walker (b. 1969)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sid<PERSON>\" title=\"<PERSON><PERSON> Sid<PERSON>\"><PERSON><PERSON></a>, Italian race walker (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sid<PERSON>\" title=\"<PERSON>rita Sidoti\"><PERSON><PERSON></a>, Italian race walker (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Sidoti"}]}, {"year": "2015", "text": "<PERSON><PERSON>, English singer-songwriter (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter (b. 1948)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Kuwaiti businessman and politician, 8th Kuwaiti Speaker of the National Assembly (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kuwaiti businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_Kuwait_National_Assembly\" class=\"mw-redirect\" title=\"List of Speakers of Kuwait National Assembly\">Kuwaiti Speaker of the National Assembly</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kuwaiti businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_Kuwait_National_Assembly\" class=\"mw-redirect\" title=\"List of Speakers of Kuwait National Assembly\">Kuwaiti Speaker of the National Assembly</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Speakers of Kuwait National Assembly", "link": "https://wikipedia.org/wiki/List_of_Speakers_of_Kuwait_National_Assembly"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and coach (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American bass player and producer (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, American bass player and producer (b. 1955)", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>_(bassist)"}]}, {"year": "2016", "text": "<PERSON>, American drummer and songwriter (b. 1964)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Belgian film director (b. 1925)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian film director (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian film director (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rik_<PERSON>s"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan writer (b. 1971)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Binyavanga_Wainaina\" title=\"Binyavanga Wainaina\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan writer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yavanga_Wainaina\" title=\"Binyavanga Wainaina\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan writer (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Binyavanga_Wainaina"}]}, {"year": "2020", "text": "<PERSON>, fifth President of George Mason University (b. 1941) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/University_President\" class=\"mw-redirect\" title=\"University President\">President</a> of <a href=\"https://wikipedia.org/wiki/George_Mason_University\" title=\"George Mason University\">George Mason University</a> (b. 1941) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/University_President\" class=\"mw-redirect\" title=\"University President\">President</a> of <a href=\"https://wikipedia.org/wiki/George_Mason_University\" title=\"George Mason University\">George Mason University</a> (b. 1941) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "University President", "link": "https://wikipedia.org/wiki/University_President"}, {"title": "George <PERSON> University", "link": "https://wikipedia.org/wiki/<PERSON>_Mason_University"}]}, {"year": "2024", "text": "<PERSON> <PERSON><PERSON> <PERSON>, Polish composer (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON> <PERSON><PERSON></a>, Polish composer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON>\"><PERSON> <PERSON><PERSON></a>, Polish composer (b. 1953)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jan_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}]}}