{"date": "September 1", "url": "https://wikipedia.org/wiki/September_1", "data": {"Events": [{"year": "1145", "text": "The main altar of Lund Cathedral, at the time the seat of the archiepiscopal see of all the Nordic countries, is consecrated.", "html": "1145 - The main altar of <a href=\"https://wikipedia.org/wiki/Lund_Cathedral\" title=\"Lund Cathedral\">Lund Cathedral</a>, at the time the seat of the <a href=\"https://wikipedia.org/wiki/Archiepiscopal_see\" class=\"mw-redirect\" title=\"Archiepiscopal see\">archiepiscopal see</a> of all the <a href=\"https://wikipedia.org/wiki/Nordic_countries\" title=\"Nordic countries\">Nordic countries</a>, is consecrated.", "no_year_html": "The main altar of <a href=\"https://wikipedia.org/wiki/Lund_Cathedral\" title=\"Lund Cathedral\">Lund Cathedral</a>, at the time the seat of the <a href=\"https://wikipedia.org/wiki/Archiepiscopal_see\" class=\"mw-redirect\" title=\"Archiepiscopal see\">archiepiscopal see</a> of all the <a href=\"https://wikipedia.org/wiki/Nordic_countries\" title=\"Nordic countries\">Nordic countries</a>, is consecrated.", "links": [{"title": "Lund Cathedral", "link": "https://wikipedia.org/wiki/Lund_Cathedral"}, {"title": "Archiepiscopal see", "link": "https://wikipedia.org/wiki/Archiepiscopal_see"}, {"title": "Nordic countries", "link": "https://wikipedia.org/wiki/Nordic_countries"}]}, {"year": "1173", "text": "The widow <PERSON><PERSON><PERSON> sacrifices herself in order to raise the siege of Ancona by the forces of Emperor <PERSON>.", "html": "1173 - The widow <a href=\"https://wikipedia.org/wiki/Stamira\" title=\"Stamira\">St<PERSON><PERSON></a> sacrifices herself in order to raise the siege of <a href=\"https://wikipedia.org/wiki/Ancona\" title=\"Ancona\">Ancona</a> by the forces of Emperor <a href=\"https://wikipedia.org/wiki/Frederick<PERSON>Bar<PERSON>\" title=\"Frederick Barbar<PERSON>\"><PERSON></a>.", "no_year_html": "The widow <a href=\"https://wikipedia.org/wiki/Stamira\" title=\"Stamira\">St<PERSON><PERSON></a> sacrifices herself in order to raise the siege of <a href=\"https://wikipedia.org/wiki/Ancona\" title=\"Ancona\">Ancona</a> by the forces of Emperor <a href=\"https://wikipedia.org/wiki/Frederick_Barbarossa\" title=\"Frederick Barbarossa\"><PERSON></a>.", "links": [{"title": "Stamira", "link": "https://wikipedia.org/wiki/Stamira"}, {"title": "Ancona", "link": "https://wikipedia.org/wiki/Ancona"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frederick_Barbarossa"}]}, {"year": "1355", "text": "King <PERSON><PERSON><PERSON><PERSON> of Bosnia writes In castro nostro Vizoka vocatum from the Old town of Visoki.", "html": "1355 - King <a href=\"https://wikipedia.org/wiki/Tvrtko_I_of_Bosnia\" title=\"Tvrtko I of Bosnia\">Tvrtko I of Bosnia</a> writes <i>In castro nostro Vizoka vocatum</i> from the <a href=\"https://wikipedia.org/wiki/Old_town_of_Visoki\" title=\"Old town of Visoki\">Old town of Visoki</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Tvrtko_I_of_Bosnia\" title=\"Tvrtko I of Bosnia\">Tvrtko I of Bosnia</a> writes <i>In castro nostro Vizoka vocatum</i> from the <a href=\"https://wikipedia.org/wiki/Old_town_of_Visoki\" title=\"Old town of Visoki\">Old town of Visoki</a>.", "links": [{"title": "Tvrtko I of Bosnia", "link": "https://wikipedia.org/wiki/Tvrtko_I_of_Bosnia"}, {"title": "Old town of Visoki", "link": "https://wikipedia.org/wiki/Old_town_of_Visoki"}]}, {"year": "1449", "text": "Tumu Crisis: The Mongols capture the Emperor of China.", "html": "1449 - <a href=\"https://wikipedia.org/wiki/Tumu_Crisis\" title=\"Tumu Crisis\">Tumu Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> capture the <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tumu_Crisis\" title=\"Tumu Crisis\">Tumu Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongols</a> capture the <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor of China</a>.", "links": [{"title": "Tumu Crisis", "link": "https://wikipedia.org/wiki/Tumu_Crisis"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}]}, {"year": "1529", "text": "The Spanish fort of Sancti Spiritu, the first one built in modern Argentina, is destroyed by indigenous people.", "html": "1529 - The Spanish fort of <a href=\"https://wikipedia.org/wiki/Sancti_Spiritu_(Argentina)\" title=\"Sancti Spiritu (Argentina)\">Sancti Spiritu</a>, the first one built in modern <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, is destroyed by indigenous people.", "no_year_html": "The Spanish fort of <a href=\"https://wikipedia.org/wiki/Sancti_Spiritu_(Argentina)\" title=\"Sancti Spiritu (Argentina)\">Sancti Spiritu</a>, the first one built in modern <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, is destroyed by indigenous people.", "links": [{"title": "<PERSON><PERSON><PERSON> (Argentina)", "link": "https://wikipedia.org/wiki/Sancti_Spiritu_(Argentina)"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1532", "text": "Lady <PERSON> is made Marquess of Pembroke by her fiancé, King <PERSON> of England.", "html": "1532 - Lady <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is made <a href=\"https://wikipedia.org/wiki/Marquess_of_Pembroke\" title=\"Marquess of Pembroke\">Marquess of Pembroke</a> by her <a href=\"https://wikipedia.org/wiki/Engagement\" title=\"Engagement\">fiancé</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a>.", "no_year_html": "Lady <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is made <a href=\"https://wikipedia.org/wiki/Marquess_of_Pembroke\" title=\"Marquess of Pembroke\">Marquess of Pembroke</a> by her <a href=\"https://wikipedia.org/wiki/Engagement\" title=\"Engagement\">fiancé</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marquess of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>_of_Pembroke"}, {"title": "Engagement", "link": "https://wikipedia.org/wiki/Engagement"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1604", "text": "<PERSON><PERSON>, now known as <PERSON>, the holy scripture of Sikhs, is first installed at Harmandir Sahib.", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, now known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Guru <PERSON>\">Guru <PERSON></a>, the holy scripture of <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikhs</a>, is first installed at <a href=\"https://wikipedia.org/wiki/Harman<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, now known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guru <PERSON>\">Guru <PERSON></a>, the holy scripture of <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikhs</a>, is first installed at <a href=\"https://wikipedia.org/wiki/Harman<PERSON>_Sahib\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>h"}, {"title": "Guru <PERSON> Sahib", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sikh", "link": "https://wikipedia.org/wiki/Sikh"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1610", "text": "<PERSON>'s musical work <PERSON><PERSON><PERSON><PERSON> della Beata Vergine (Vespers for the Blessed Virgin) is first published, printed in Venice and dedicated to <PERSON>.", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s musical work <i><a href=\"https://wikipedia.org/wiki/Vespro_della_Beata_Vergine\" title=\"Vespro della Beata Vergine\">Vespro della Beata Vergine</a></i> (<i><a href=\"https://wikipedia.org/wiki/Vespers\" title=\"Vespers\">Vespers</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>,_mother_of_Jesus\" title=\"<PERSON>, mother of Jesus\">the Blessed Virgin</a></i>) is first published, printed in Venice and dedicated to <a href=\"https://wikipedia.org/wiki/Pope_Paul_V\" title=\"Pope Paul V\">Pope <PERSON> V</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s musical work <i><a href=\"https://wikipedia.org/wiki/Vespro_della_Beata_Vergine\" title=\"Vespro della Beata Vergine\">Vespro della Beata Vergine</a></i> (<i><a href=\"https://wikipedia.org/wiki/Vespers\" title=\"Vespers\">Vespers</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>,_mother_of_Jesus\" title=\"Mary, mother of Jesus\">the Blessed Virgin</a></i>) is first published, printed in Venice and dedicated to <a href=\"https://wikipedia.org/wiki/Pope_Paul_V\" title=\"Pope Paul V\">Pope <PERSON> V</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vespro della Beata Vergine", "link": "https://wikipedia.org/wiki/Ves<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_Vergine"}, {"title": "Vespers", "link": "https://wikipedia.org/wiki/Vespers"}, {"title": "<PERSON>, mother of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_mother_of_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "Battle of Tippermuir: <PERSON>, 1st Marquess of Montrose defeats the Earl of Wemyss's Covenanters, reviving the Royalist cause.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Battle_of_Tippermuir\" title=\"Battle of Tippermuir\">Battle of Tippermuir</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Montrose\" title=\"<PERSON>, 1st Marquess of Montrose\"><PERSON>, 1st Marquess of Montrose</a> defeats the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_We<PERSON>ss\" title=\"<PERSON>, 1st Earl of Wemyss\">Earl <PERSON> Wemyss</a>'s <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanters</a>, reviving the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> cause.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Tippermuir\" title=\"Battle of Tippermuir\">Battle of Tippermuir</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Montrose\" title=\"<PERSON>, 1st Marquess of Montrose\"><PERSON>, 1st Marquess of Montrose</a> defeats the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Wemyss\" title=\"<PERSON>, 1st Earl of Wemyss\">Earl of Wemyss</a>'s <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanters</a>, reviving the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Royalist</a> cause.", "links": [{"title": "Battle of Tippermuir", "link": "https://wikipedia.org/wiki/Battle_of_Tippermuir"}, {"title": "<PERSON>, 1st Marquess of Montrose", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montrose"}, {"title": "<PERSON>, 1st Earl of Wemyss", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>"}, {"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}]}, {"year": "1645", "text": "English Civil War. Scottish Covenanter forces abandon their month-long Siege of Hereford, a Cavalier stronghold, on news of Royalist victories in Scotland.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>. <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> forces abandon their month-long <a href=\"https://wikipedia.org/wiki/Siege_of_Hereford\" title=\"Siege of Hereford\">Siege of Hereford</a>, a <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Cavalier</a> stronghold, on news of Royalist victories in Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>. <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scottish</a> <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanter</a> forces abandon their month-long <a href=\"https://wikipedia.org/wiki/Siege_of_Hereford\" title=\"Siege of Hereford\">Siege of Hereford</a>, a <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Cavalier</a> stronghold, on news of Royalist victories in Scotland.", "links": [{"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}, {"title": "Siege of Hereford", "link": "https://wikipedia.org/wiki/Siege_of_Hereford"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}]}, {"year": "1715", "text": "At the age of five, <PERSON> becomes king of France in succession to his great-grandfather, King <PERSON>.", "html": "1715 - At the age of five, <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a> becomes king of France in succession to his great-grandfather, King <PERSON>.", "no_year_html": "At the age of five, <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON></a> becomes king of France in succession to his great-grandfather, King <PERSON>.", "links": [{"title": "<PERSON> XV of France", "link": "https://wikipedia.org/wiki/Louis_XV_of_France"}]}, {"year": "1763", "text": "<PERSON> of Russia endorses <PERSON>'s plans for a Foundling Home in Moscow.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"Catherine II of Russia\"><PERSON> of Russia</a> endorses <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s plans for a <a href=\"https://wikipedia.org/wiki/Foundling_Home_in_Moscow\" class=\"mw-redirect\" title=\"Foundling Home in Moscow\">Foundling Home in Moscow</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" class=\"mw-redirect\" title=\"Catherine II of Russia\"><PERSON> of Russia</a> endorses <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s plans for a <a href=\"https://wikipedia.org/wiki/Foundling_Home_in_Moscow\" class=\"mw-redirect\" title=\"Foundling Home in Moscow\">Foundling Home in Moscow</a>.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Foundling Home in Moscow", "link": "https://wikipedia.org/wiki/Foundling_Home_in_Moscow"}]}, {"year": "1772", "text": "The Mission San Luis Obispo de Tolosa is founded in San Luis Obispo, California.", "html": "1772 - The <a href=\"https://wikipedia.org/wiki/Mission_San_Luis_Obispo_de_Tolosa\" title=\"Mission San Luis Obispo de Tolosa\">Mission San Luis Obispo de Tolosa</a> is founded in <a href=\"https://wikipedia.org/wiki/San_Luis_Obispo,_California\" title=\"San Luis Obispo, California\">San Luis Obispo, California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mission_San_Luis_Obispo_de_Tolosa\" title=\"Mission San Luis Obispo de Tolosa\">Mission San Luis Obispo de Tolosa</a> is founded in <a href=\"https://wikipedia.org/wiki/San_Luis_Obispo,_California\" title=\"San Luis Obispo, California\">San Luis Obispo, California</a>.", "links": [{"title": "Mission San Luis Obispo de Tolosa", "link": "https://wikipedia.org/wiki/Mission_San_Luis_<PERSON>_<PERSON>_<PERSON>losa"}, {"title": "San Luis Obispo, California", "link": "https://wikipedia.org/wiki/San_<PERSON>_<PERSON>,_California"}]}, {"year": "1774", "text": "Massachusetts Bay colonists rise up in the bloodless Powder Alarm.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Massachusetts Bay</a> colonists rise up in the bloodless <a href=\"https://wikipedia.org/wiki/Powder_Alarm\" title=\"Powder Alarm\">Powder Alarm</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Province_of_Massachusetts_Bay\" title=\"Province of Massachusetts Bay\">Massachusetts Bay</a> colonists rise up in the bloodless <a href=\"https://wikipedia.org/wiki/Powder_Alarm\" title=\"Powder Alarm\">Powder Alarm</a>.", "links": [{"title": "Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Province_of_Massachusetts_Bay"}, {"title": "Powder Alarm", "link": "https://wikipedia.org/wiki/Powder_Alarm"}]}, {"year": "1804", "text": "3 <PERSON>, one of the largest asteroids in the Main Belt, is discovered by the German astronomer <PERSON>.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/3_Juno\" title=\"3 Juno\">3 Juno</a>, one of the largest <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroids</a> in the <a href=\"https://wikipedia.org/wiki/Main_Belt\" class=\"mw-redirect\" title=\"Main Belt\">Main Belt</a>, is discovered by the German astronomer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/3_Juno\" title=\"3 Juno\">3 Juno</a>, one of the largest <a href=\"https://wikipedia.org/wiki/Asteroid\" title=\"Asteroid\">asteroids</a> in the <a href=\"https://wikipedia.org/wiki/Main_Belt\" class=\"mw-redirect\" title=\"Main Belt\">Main Belt</a>, is discovered by the German astronomer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "3 Juno", "link": "https://wikipedia.org/wiki/3_<PERSON>"}, {"title": "Asteroid", "link": "https://wikipedia.org/wiki/Asteroid"}, {"title": "Main Belt", "link": "https://wikipedia.org/wiki/Main_Belt"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "The Order of <PERSON><PERSON> Gregory the Great is established by <PERSON> <PERSON> of the Vatican State to recognize high support for the Vatican or for the Pope, by a man or a woman, and not necessarily a Roman Catholic.", "html": "1831 - The <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_Gregory_the_Great\" title=\"Order of <PERSON><PERSON> Gregory the Great\">Order of <PERSON><PERSON> Gregory the Great</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_Gregory_<PERSON>\" title=\"<PERSON> Gregory XVI\">Pope <PERSON> XVI</a> of the <a href=\"https://wikipedia.org/wiki/Vatican_State\" class=\"mw-redirect\" title=\"Vatican State\">Vatican State</a> to recognize high support for the Vatican or for the Pope, by a man or a woman, and not necessarily a <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_Gregory_the_Great\" title=\"Order of <PERSON><PERSON> Gregory the Great\">Order of <PERSON><PERSON> Gregory the Great</a> is established by <a href=\"https://wikipedia.org/wiki/<PERSON>_Gregory_<PERSON>\" title=\"<PERSON> Gregory <PERSON>\"><PERSON> XVI</a> of the <a href=\"https://wikipedia.org/wiki/Vatican_State\" class=\"mw-redirect\" title=\"Vatican State\">Vatican State</a> to recognize high support for the Vatican or for the Pope, by a man or a woman, and not necessarily a <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a>.", "links": [{"title": "Order of <PERSON><PERSON> Gregory the Great", "link": "https://wikipedia.org/wiki/Order_of_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vatican State", "link": "https://wikipedia.org/wiki/Vatican_State"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, one of the first English-speaking white women to settle west of the Rocky Mountains, arrives at Walla Walla, Washington.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, one of the first English-speaking white women to settle west of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>, arrives at <a href=\"https://wikipedia.org/wiki/Walla_Walla,_Washington\" title=\"Walla Walla, Washington\">Walla Walla, Washington</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, one of the first English-speaking white women to settle west of the <a href=\"https://wikipedia.org/wiki/Rocky_Mountains\" title=\"Rocky Mountains\">Rocky Mountains</a>, arrives at <a href=\"https://wikipedia.org/wiki/Walla_Walla,_Washington\" title=\"Walla Walla, Washington\">Walla Walla, Washington</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>"}, {"title": "Rocky Mountains", "link": "https://wikipedia.org/wiki/Rocky_Mountains"}, {"title": "Walla Walla, Washington", "link": "https://wikipedia.org/wiki/Walla_Walla,_Washington"}]}, {"year": "1838", "text": "<PERSON>'s Scots School, the oldest school of British origin in South America, is established.", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Andrew%27s_Scots_School\" class=\"mw-redirect\" title=\"Saint Andrew's Scots School\">Saint Andrew's Scots School</a>, the oldest school of British origin in South America, is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Andrew%27s_Scots_School\" class=\"mw-redirect\" title=\"Saint Andrew's Scots School\">Saint <PERSON>'s Scots School</a>, the oldest school of British origin in South America, is established.", "links": [{"title": "Saint Andrew's Scots School", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Scots_School"}]}, {"year": "1862", "text": "American Civil War: Battle of Chantilly: Confederate Army troops defeat a group of retreating Union Army troops in Chantilly, Virginia.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chantilly\" title=\"Battle of Chantilly\">Battle of Chantilly</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate Army troops</a> defeat a group of retreating <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">Union Army troops</a> in <a href=\"https://wikipedia.org/wiki/Chantilly,_Virginia\" title=\"Chantilly, Virginia\">Chantilly, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chantilly\" title=\"Battle of Chantilly\">Battle of Chantilly</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate Army troops</a> defeat a group of retreating <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">Union Army troops</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>tilly,_Virginia\" title=\"Chantilly, Virginia\">Chantilly, Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Chantilly", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON>ly"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Chantilly, Virginia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Virginia"}]}, {"year": "1864", "text": "American Civil War: The Confederate Army General <PERSON> orders the evacuation of Atlanta, ending a four-month siege by General <PERSON>.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the evacuation of <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>, ending a four-month <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the evacuation of <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>, ending a four-month <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Confederate Army", "link": "https://wikipedia.org/wiki/Confederate_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}, {"title": "Siege", "link": "https://wikipedia.org/wiki/Siege"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "Franco-Prussian War: The Battle of Sedan is fought, resulting in a decisive Prussian victory.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sedan\" title=\"Battle of Sedan\">Battle of Sedan</a> is fought, resulting in a decisive <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sedan\" title=\"Battle of Sedan\">Battle of Sedan</a> is fought, resulting in a decisive <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Battle of Sedan", "link": "https://wikipedia.org/wiki/Battle_of_Sedan"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON> ascends to the throne as king of the Zulu nation following the death of his father <PERSON><PERSON><PERSON>.", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\">Ce<PERSON><PERSON><PERSON></a> ascends to the throne as king of the <a href=\"https://wikipedia.org/wiki/Zulu_Kingdom\" title=\"Zulu Kingdom\">Z<PERSON></a> nation following the death of his father <a href=\"https://wikipedia.org/wiki/Mpande\" class=\"mw-redirect\" title=\"Mpande\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\">Ce<PERSON><PERSON><PERSON></a> ascends to the throne as king of the <a href=\"https://wikipedia.org/wiki/Zulu_Kingdom\" title=\"Zulu Kingdom\">Zulu</a> nation following the death of his father <a href=\"https://wikipedia.org/wiki/Mpande\" class=\"mw-redirect\" title=\"Mpande\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Cetshwayo", "link": "https://wikipedia.org/wiki/Cetshwayo"}, {"title": "Zulu Kingdom", "link": "https://wikipedia.org/wiki/Zulu_Kingdom"}, {"title": "Mpande", "link": "https://wikipedia.org/wiki/Mpande"}]}, {"year": "1878", "text": "<PERSON> becomes the world's first female telephone operator when she is recruited by <PERSON> to the Boston Telephone Dispatch Company.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the world's first female telephone operator when she is recruited by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the Boston Telephone Dispatch Company.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the world's first female telephone operator when she is recruited by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the Boston Telephone Dispatch Company.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "The army of <PERSON> is routed by the British at the Battle of Kandahar, ending the Second Anglo-Afghan War.", "html": "1880 - The army of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Emir_of_Afghanistan)\" title=\"<PERSON><PERSON><PERSON> (Emir of Afghanistan)\"><PERSON></a> is routed by the British at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kandahar_(1880)\" title=\"Battle of Kandahar (1880)\">Battle of Kandahar</a>, ending the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Afghan_War\" title=\"Second Anglo-Afghan War\">Second Anglo-Afghan War</a>.", "no_year_html": "The army of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Emir_of_Afghanistan)\" title=\"<PERSON><PERSON><PERSON> (Emir of Afghanistan)\"><PERSON></a> is routed by the British at the <a href=\"https://wikipedia.org/wiki/Battle_of_Kandahar_(1880)\" title=\"Battle of Kandahar (1880)\">Battle of Kandahar</a>, ending the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Afghan_War\" title=\"Second Anglo-Afghan War\">Second Anglo-Afghan War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (Emir of Afghanistan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(Emir_of_Afghanistan)"}, {"title": "Battle of Kandahar (1880)", "link": "https://wikipedia.org/wiki/Battle_of_Kandahar_(1880)"}, {"title": "Second Anglo-Afghan War", "link": "https://wikipedia.org/wiki/Second_Anglo-Afghan_War"}]}, {"year": "1894", "text": "Over 400 people die in the Great Hinckley Fire, a forest fire in Hinckley, Minnesota.", "html": "1894 - Over 400 people die in the <a href=\"https://wikipedia.org/wiki/Great_Hinckley_Fire\" title=\"Great Hinckley Fire\">Great Hinckley Fire</a>, a <a href=\"https://wikipedia.org/wiki/Forest_fire\" class=\"mw-redirect\" title=\"Forest fire\">forest fire</a> in <a href=\"https://wikipedia.org/wiki/Hinckley,_Minnesota\" title=\"Hinckley, Minnesota\">Hinckley, Minnesota</a>.", "no_year_html": "Over 400 people die in the <a href=\"https://wikipedia.org/wiki/Great_Hinckley_Fire\" title=\"Great Hinckley Fire\">Great Hinckley Fire</a>, a <a href=\"https://wikipedia.org/wiki/Forest_fire\" class=\"mw-redirect\" title=\"Forest fire\">forest fire</a> in <a href=\"https://wikipedia.org/wiki/Hinckley,_Minnesota\" title=\"Hinckley, Minnesota\">Hinckley, Minnesota</a>.", "links": [{"title": "Great Hinckley Fire", "link": "https://wikipedia.org/wiki/Great_Hinckley_Fire"}, {"title": "Forest fire", "link": "https://wikipedia.org/wiki/Forest_fire"}, {"title": "Hinckley, Minnesota", "link": "https://wikipedia.org/wiki/Hinckley,_Minnesota"}]}, {"year": "1897", "text": "The Tremont Street Subway in Boston opens, becoming the first underground rapid transit system in North America.", "html": "1897 - The <a href=\"https://wikipedia.org/wiki/Tremont_Street_Subway\" class=\"mw-redirect\" title=\"Tremont Street Subway\">Tremont Street Subway</a> in Boston opens, becoming the first underground <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit</a> system in North America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tremont_Street_Subway\" class=\"mw-redirect\" title=\"Tremont Street Subway\">Tremont Street Subway</a> in Boston opens, becoming the first underground <a href=\"https://wikipedia.org/wiki/Rapid_transit\" title=\"Rapid transit\">rapid transit</a> system in North America.", "links": [{"title": "Tremont Street Subway", "link": "https://wikipedia.org/wiki/Tremont_Street_Subway"}, {"title": "Rapid transit", "link": "https://wikipedia.org/wiki/Rapid_transit"}]}, {"year": "1923", "text": "The Great Kantō earthquake devastates Tokyo and Yokohama, killing about 105,000 people.", "html": "1923 - The <a href=\"https://wikipedia.org/wiki/1923_Great_Kant%C5%8D_earthquake\" title=\"1923 Great Kantō earthquake\">Great Kantō earthquake</a> devastates Tokyo and <a href=\"https://wikipedia.org/wiki/Yokohama\" title=\"Yokohama\">Yokohama</a>, killing about 105,000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1923_Great_Kant%C5%8D_earthquake\" title=\"1923 Great Kantō earthquake\">Great Kantō earthquake</a> devastates Tokyo and <a href=\"https://wikipedia.org/wiki/Yokohama\" title=\"Yokohama\">Yokohama</a>, killing about 105,000 people.", "links": [{"title": "1923 Great Kantō earthquake", "link": "https://wikipedia.org/wiki/1923_Great_Kant%C5%8D_earthquake"}, {"title": "Yokohama", "link": "https://wikipedia.org/wiki/Yokohama"}]}, {"year": "1939", "text": "World War II: Germany and Slovakia invade Poland, beginning the European phase of World War II.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Republic (1939-45)\">Slovakia</a> <a href=\"https://wikipedia.org/wiki/Invasion_of_Poland\" title=\"Invasion of Poland\">invade Poland</a>, beginning the European phase of World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"Slovak Republic (1939-45)\">Slovakia</a> <a href=\"https://wikipedia.org/wiki/Invasion_of_Poland\" title=\"Invasion of Poland\">invade Poland</a>, beginning the European phase of World War II.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Slovak Republic (1939-45)", "link": "https://wikipedia.org/wiki/Slovak_Republic_(1939%E2%80%9345)"}, {"title": "Invasion of Poland", "link": "https://wikipedia.org/wiki/Invasion_of_Poland"}]}, {"year": "1939", "text": "<PERSON><PERSON> <PERSON> and his student <PERSON><PERSON> publish the <PERSON><PERSON><PERSON> model, proving for the first time in contemporary physics how black holes could develop.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and his student <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> publish the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%E2%80%93Snyder_model\" title=\"<PERSON><PERSON><PERSON><PERSON> model\"><PERSON><PERSON><PERSON>-<PERSON> model</a>, proving for the first time in contemporary physics how <a href=\"https://wikipedia.org/wiki/Black_hole\" title=\"Black hole\">black holes</a> could develop.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and his student <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> publish the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%E2%80%93Snyder_model\" title=\"<PERSON><PERSON><PERSON><PERSON> model\"><PERSON><PERSON><PERSON>-<PERSON> model</a>, proving for the first time in contemporary physics how <a href=\"https://wikipedia.org/wiki/Black_hole\" title=\"Black hole\">black holes</a> could develop.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> model", "link": "https://wikipedia.org/wiki/Oppenheimer%E2%80%93Snyder_model"}, {"title": "Black hole", "link": "https://wikipedia.org/wiki/Black_hole"}]}, {"year": "1944", "text": "World War II: Launch of Operation Ratweek, complicating German retreat. ", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Launch of <a href=\"https://wikipedia.org/wiki/Operation_Ratweek_(1944)\" title=\"Operation Ratweek (1944)\">Operation Ratweek</a>, complicating German retreat. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Launch of <a href=\"https://wikipedia.org/wiki/Operation_Ratweek_(1944)\" title=\"Operation Ratweek (1944)\">Operation Ratweek</a>, complicating German retreat. ", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Ratweek (1944)", "link": "https://wikipedia.org/wiki/Operation_Ratweek_(1944)"}]}, {"year": "1961", "text": "TWA Flight 529 crashed shortly after takeoff from Midway Airport in Chicago, killing all 78 people on board. At the time, it was the deadliest single plane disaster in U.S. history.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/TWA_Flight_529\" title=\"TWA Flight 529\">TWA Flight 529</a> crashed shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Midway_International_Airport\" title=\"Midway International Airport\">Midway Airport</a> in Chicago, killing all 78 people on board. At the time, it was the deadliest single plane disaster in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TWA_Flight_529\" title=\"TWA Flight 529\">TWA Flight 529</a> crashed shortly after takeoff from <a href=\"https://wikipedia.org/wiki/Midway_International_Airport\" title=\"Midway International Airport\">Midway Airport</a> in Chicago, killing all 78 people on board. At the time, it was the deadliest single plane disaster in U.S. history.", "links": [{"title": "TWA Flight 529", "link": "https://wikipedia.org/wiki/TWA_Flight_529"}, {"title": "Midway International Airport", "link": "https://wikipedia.org/wiki/Midway_International_Airport"}]}, {"year": "1967", "text": "Six-Day War: The Khartoum Resolution is issued at the Arab Summit, and eight countries adopt the \"three 'no's against Israel\".", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>: The <a href=\"https://wikipedia.org/wiki/Khartoum_Resolution\" title=\"Khartoum Resolution\">Khartoum Resolution</a> is issued at the Arab Summit, and eight countries adopt the \"three 'no's against Israel\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a>: The <a href=\"https://wikipedia.org/wiki/Khartoum_Resolution\" title=\"Khartoum Resolution\">Khartoum Resolution</a> is issued at the Arab Summit, and eight countries adopt the \"three 'no's against Israel\".", "links": [{"title": "Six-Day War", "link": "https://wikipedia.org/wiki/Six-Day_War"}, {"title": "Khartoum Resolution", "link": "https://wikipedia.org/wiki/Khartoum_Resolution"}]}, {"year": "1969", "text": "A coup in Libya brings <PERSON><PERSON><PERSON> to power.", "html": "1969 - A <a href=\"https://wikipedia.org/wiki/1969_Libyan_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1969 Libyan coup d'état\">coup</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> brings <a href=\"https://wikipedia.org/wiki/Muammar_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to power.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1969_Libyan_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1969 Libyan coup d'état\">coup</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> brings <a href=\"https://wikipedia.org/wiki/Muammar_<PERSON>ad<PERSON>\" title=\"Mu<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to power.", "links": [{"title": "1969 Libyan coup d'état", "link": "https://wikipedia.org/wiki/1969_Libyan_coup_d%27%C3%A9tat"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "A 76-hour multinational rescue effort in the Celtic Sea resulted in the Rescue of <PERSON> and <PERSON>.", "html": "1973 - A 76-hour multinational rescue effort in the <a href=\"https://wikipedia.org/wiki/Celtic_Sea\" title=\"Celtic Sea\">Celtic Sea</a> resulted in the <a href=\"https://wikipedia.org/wiki/Rescue_of_<PERSON>_and_<PERSON>\" title=\"Rescue of <PERSON> and <PERSON>\">Rescue of <PERSON> and <PERSON></a>.", "no_year_html": "A 76-hour multinational rescue effort in the <a href=\"https://wikipedia.org/wiki/Celtic_Sea\" title=\"Celtic Sea\">Celtic Sea</a> resulted in the <a href=\"https://wikipedia.org/wiki/Rescue_of_<PERSON>_<PERSON>_and_<PERSON>\" title=\"Rescue of <PERSON> and <PERSON>\">Rescue of <PERSON> and <PERSON></a>.", "links": [{"title": "Celtic Sea", "link": "https://wikipedia.org/wiki/Celtic_Sea"}, {"title": "Rescue of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Rescue_of_<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "The SR-71 Blackbird sets (and holds) the record for flying from New York to London in the time of one hour, 54 minutes and 56.4 seconds at a speed of 1,435.587 miles per hour (2,310.353 km/h).", "html": "1974 - The <i><a href=\"https://wikipedia.org/wiki/SR-71_Blackbird\" class=\"mw-redirect\" title=\"SR-71 Blackbird\">SR-71 Blackbird</a></i> sets (and holds) the record for flying from New York to London in the time of one hour, 54 minutes and 56.4 seconds at a speed of 1,435.587 miles per hour (2,310.353 km/h).", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/SR-71_Blackbird\" class=\"mw-redirect\" title=\"SR-71 Blackbird\">SR-71 Blackbird</a></i> sets (and holds) the record for flying from New York to London in the time of one hour, 54 minutes and 56.4 seconds at a speed of 1,435.587 miles per hour (2,310.353 km/h).", "links": [{"title": "SR-71 Blackbird", "link": "https://wikipedia.org/wiki/SR-71_Blackbird"}]}, {"year": "1981", "text": "Central African President <PERSON> is ousted from power in a bloodless military coup led by General <PERSON>.", "html": "1981 - Central African President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/1981_Central_African_Republic_coup_d%27%C3%A9tat\" title=\"1981 Central African Republic coup d'état\">bloodless military coup</a> led by General <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Central African President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/1981_Central_African_Republic_coup_d%27%C3%A9tat\" title=\"1981 Central African Republic coup d'état\">bloodless military coup</a> led by General <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Koling<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1981 Central African Republic coup d'état", "link": "https://wikipedia.org/wiki/1981_Central_African_Republic_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ba"}]}, {"year": "1982", "text": "The United States Air Force Space Command is founded.", "html": "1982 - The United States <a href=\"https://wikipedia.org/wiki/Air_Force_Space_Command\" class=\"mw-redirect\" title=\"Air Force Space Command\">Air Force Space Command</a> is founded.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Air_Force_Space_Command\" class=\"mw-redirect\" title=\"Air Force Space Command\">Air Force Space Command</a> is founded.", "links": [{"title": "Air Force Space Command", "link": "https://wikipedia.org/wiki/Air_Force_Space_Command"}]}, {"year": "1983", "text": "Cold War: Korean Air Lines Flight 007 is shot down by a Soviet jet fighter after the commercial aircraft strayed into Soviet airspace, killing all 269 on board, including Congressman <PERSON>.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a> is shot down by a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> jet fighter after the commercial aircraft strayed into Soviet airspace, killing all 269 on board, including <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congressman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a> is shot down by a <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> jet fighter after the commercial aircraft strayed into Soviet airspace, killing all 269 on board, including <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congressman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Korean Air Lines Flight 007", "link": "https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "The wreck of the Titanic is discovered by an American-French expedition led by <PERSON> and <PERSON><PERSON><PERSON>.", "html": "1985 - The wreck of the <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">Titanic</a> is discovered by an American-French expedition led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON><PERSON><PERSON>.", "no_year_html": "The wreck of the <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">Titanic</a> is discovered by an American-French expedition led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <PERSON><PERSON><PERSON>.", "links": [{"title": "Titanic", "link": "https://wikipedia.org/wiki/Titanic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "The Beslan school siege begins when armed terrorists take schoolchildren and school staff hostage in North Ossetia, Russia; by the end of the siege, three days later, more than 385 people are dead (including hostages, other civilians, security personnel and terrorists).", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Beslan_school_siege\" title=\"Beslan school siege\">Beslan school siege</a> begins when armed terrorists take schoolchildren and school staff hostage in <a href=\"https://wikipedia.org/wiki/North_Ossetia\" class=\"mw-redirect\" title=\"North Ossetia\">North Ossetia</a>, Russia; by the end of the siege, three days later, more than 385 people are dead (including hostages, other civilians, security personnel and terrorists).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Beslan_school_siege\" title=\"Beslan school siege\">Beslan school siege</a> begins when armed terrorists take schoolchildren and school staff hostage in <a href=\"https://wikipedia.org/wiki/North_Ossetia\" class=\"mw-redirect\" title=\"North Ossetia\">North Ossetia</a>, Russia; by the end of the siege, three days later, more than 385 people are dead (including hostages, other civilians, security personnel and terrorists).", "links": [{"title": "Beslan school siege", "link": "https://wikipedia.org/wiki/Beslan_school_siege"}, {"title": "North Ossetia", "link": "https://wikipedia.org/wiki/North_Ossetia"}]}, {"year": "2008", "text": "Iraq War: The United States Armed Forces transfers control of Anbar Province to the Iraqi Armed Forces.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a> transfers control of <a href=\"https://wikipedia.org/wiki/Al_Anbar_Governorate\" title=\"Al Anbar Governorate\">Anbar Province</a> to the <a href=\"https://wikipedia.org/wiki/Iraqi_Armed_Forces\" title=\"Iraqi Armed Forces\">Iraqi Armed Forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a> transfers control of <a href=\"https://wikipedia.org/wiki/Al_Anbar_Governorate\" title=\"Al Anbar Governorate\">Anbar Province</a> to the <a href=\"https://wikipedia.org/wiki/Iraqi_Armed_Forces\" title=\"Iraqi Armed Forces\">Iraqi Armed Forces</a>.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}, {"title": "Al Anbar Governorate", "link": "https://wikipedia.org/wiki/Al_Anbar_Governorate"}, {"title": "Iraqi Armed Forces", "link": "https://wikipedia.org/wiki/Iraqi_Armed_Forces"}]}, {"year": "2022", "text": "Physicians at AdventHealth Central Florida Division developed a new five hour test for brain-eating amoebas.", "html": "2022 - Physicians at <a href=\"https://wikipedia.org/wiki/AdventHealth#Brain-eating_amoebas\" title=\"AdventHealth\">AdventHealth Central Florida Division</a> developed a new five hour test for brain-eating <a href=\"https://wikipedia.org/wiki/Amoeba\" title=\"Amoeba\">amoebas</a>.", "no_year_html": "Physicians at <a href=\"https://wikipedia.org/wiki/AdventHealth#Brain-eating_amoebas\" title=\"AdventHealth\">AdventHealth Central Florida Division</a> developed a new five hour test for brain-eating <a href=\"https://wikipedia.org/wiki/Amoeba\" title=\"Amoeba\">amoebas</a>.", "links": [{"title": "AdventHealth", "link": "https://wikipedia.org/wiki/AdventHealth#Brain-eating_amoebas"}, {"title": "Amoeba", "link": "https://wikipedia.org/wiki/Amoeba"}]}], "Births": [{"year": "948", "text": "<PERSON>, emperor of the Liao Dynasty (d. 982)", "html": "948 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON> of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (d. 982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON> of Liao\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liao_dynasty\" title=\"Liao dynasty\">Liao Dynasty</a> (d. 982)", "links": [{"title": "Emperor <PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Liao"}, {"title": "Liao dynasty", "link": "https://wikipedia.org/wiki/Liao_dynasty"}]}, {"year": "1145", "text": "<PERSON>, Arab geographer and poet (d. 1217)", "html": "1145 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab geographer and poet (d. 1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab geographer and poet (d. 1217)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1288", "text": "<PERSON> of Poland (d. 1335)", "html": "1288 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Poland\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a> (d. 1335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Poland\" title=\"<PERSON> of Poland\"><PERSON> of Poland</a> (d. 1335)", "links": [{"title": "<PERSON> of Poland", "link": "https://wikipedia.org/wiki/Elizabeth_<PERSON>_of_Poland"}]}, {"year": "1341", "text": "<PERSON> the <PERSON>, King of Sicily (d. 1377)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_the_Simple\" class=\"mw-redirect\" title=\"<PERSON> III the Simple\"><PERSON> III the Simple</a>, King of Sicily (d. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_the_Simple\" class=\"mw-redirect\" title=\"<PERSON> III the Simple\"><PERSON> III the Simple</a>, King of Sicily (d. 1377)", "links": [{"title": "<PERSON> the Simple", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Simple"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON>, Spanish general (d. 1515)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish general (d. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish general (d. 1515)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba"}]}, {"year": "1477", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mercenary (d. 1525)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Fanfulla\" title=\"<PERSON><PERSON><PERSON><PERSON> Fanfulla\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mercenary (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Fanfulla\" title=\"Bart<PERSON><PERSON><PERSON> Fanfulla\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mercenary (d. 1525)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Fanfulla"}]}, {"year": "1561", "text": "<PERSON><PERSON><PERSON><PERSON>, English murderer (d. 1615)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English murderer (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English murderer (d. 1615)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1566", "text": "<PERSON>, English actor and major figure of the Elizabethan theatre; founder of Dulwich College and Alleyn's School (d. 1626)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and major figure of the Elizabethan theatre; founder of <a href=\"https://wikipedia.org/wiki/Dulwich_College\" title=\"Dulwich College\">Dulwich College</a> and <a href=\"https://wikipedia.org/wiki/Alleyn%27s_School\" title=\"Alleyn's School\">Alleyn's School</a> (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and major figure of the Elizabethan theatre; founder of <a href=\"https://wikipedia.org/wiki/Dulwich_College\" title=\"Dulwich College\">Dulwich College</a> and <a href=\"https://wikipedia.org/wiki/Alleyn%27s_School\" title=\"Alleyn's School\">Alleyn's School</a> (d. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dulwich College", "link": "https://wikipedia.org/wiki/Dulwich_College"}, {"title": "Alleyn's School", "link": "https://wikipedia.org/wiki/Alleyn%27s_School"}]}, {"year": "1577", "text": "<PERSON><PERSON><PERSON>, Italian cardinal and art collector (d. 1633)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/Scipione_Borghese\" title=\"Scipione Borghese\"><PERSON><PERSON><PERSON> Borg<PERSON></a>, Italian cardinal and art collector (d. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sc<PERSON><PERSON>_Borghese\" title=\"Scipione Borghese\"><PERSON><PERSON><PERSON> Borg<PERSON></a>, Italian cardinal and art collector (d. 1633)", "links": [{"title": "Scipione Borghese", "link": "https://wikipedia.org/wiki/Scipione_Borghese"}]}, {"year": "1579", "text": "<PERSON> of Holstein-Gottorp, Prince-Bishop, Roman Catholic archbishop (d. 1634)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Holstein-Gottorp,_Prince-Bishop\" class=\"mw-redirect\" title=\"<PERSON> of Holstein-Gottorp, Prince-Bishop\"><PERSON> of Holstein-Gottorp, Prince-Bishop</a>, Roman Catholic archbishop (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Holstein-Gottorp,_Prince-Bishop\" class=\"mw-redirect\" title=\"<PERSON> of Holstein-Gottorp, Prince-Bishop\"><PERSON> of Holstein-Gottorp, Prince-Bishop</a>, Roman Catholic archbishop (d. 1634)", "links": [{"title": "<PERSON> of Holstein-Gottorp, Prince-Bishop", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Holstein-<PERSON>,_Prince-Bishop"}]}, {"year": "1588", "text": "<PERSON>, Prince of Condé (d. 1646)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1588%E2%80%931646)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1588-1646)\"><PERSON>, Prince of Condé</a> (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1588%E2%80%931646)\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Condé (1588-1646)\"><PERSON>, Prince of Condé</a> (d. 1646)", "links": [{"title": "<PERSON>, Prince of Condé (1588-1646)", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Cond%C3%A9_(1588%E2%80%931646)"}]}, {"year": "1592", "text": "<PERSON>, Spanish mystic and saint (d. 1665)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mystic and saint (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish mystic and saint (d. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, English politician (d. 1643)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, Italian stage designer, engineer, and architect (d. 1678)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian stage designer, engineer, and architect (d. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian stage designer, engineer, and architect (d. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "Princess <PERSON> of Denmark, daughter of King <PERSON> of Denmark (d. 1717)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Denmark\" title=\"Princess <PERSON> of Denmark\">Princess <PERSON> of Denmark</a>, daughter of King <PERSON> of Denmark (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Denmark\" title=\"Princess <PERSON> of Denmark\">Princess <PERSON> of Denmark</a>, daughter of King <PERSON> of Denmark (d. 1717)", "links": [{"title": "Princess <PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Denmark"}]}, {"year": "1653", "text": "<PERSON>, German organist, composer, and educator (d. 1706)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and educator (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and educator (d. 1706)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1689", "text": "<PERSON><PERSON>, Bohemian architect, designed Ss. Cyril and Methodius Cathedral (d. 1751)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Kilian <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bohemian architect, designed <a href=\"https://wikipedia.org/wiki/Ss._<PERSON>_and_Methodius_Cathedral\" class=\"mw-redirect\" title=\"Ss. Cyril and Methodius Cathedral\">Ss. Cyril and Methodius Cathedral</a> (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"Kilian <PERSON>\"><PERSON><PERSON><PERSON></a>, Bohemian architect, designed <a href=\"https://wikipedia.org/wiki/Ss._<PERSON>_and_Methodius_Cathedral\" class=\"mw-redirect\" title=\"Ss. Cyril and Methodius Cathedral\">Ss. Cyril and Methodius Cathedral</a> (d. 1751)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ss. Cyril and Methodius Cathedral", "link": "https://wikipedia.org/wiki/Ss._<PERSON>_and_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Prince of Orange (d. 1751)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> (d. 1751)", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Orange"}]}, {"year": "1726", "text": "<PERSON>, German organist, composer, and educator (d. 1803)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, German organist, composer, and educator (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, German organist, composer, and educator (d. 1803)", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>(organist)"}]}, {"year": "1795", "text": "<PERSON>, American publisher, founded the New York Herald (d. 1872)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> (d. 1872)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "New York Herald", "link": "https://wikipedia.org/wiki/New_York_Herald"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian commander and politician (d. 1868)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian commander and politician (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian commander and politician (d. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Gyu<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Leader and important benefactor of his home town of Brentford, England (d. 1883)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Leader and important benefactor of his home town of <a href=\"https://wikipedia.org/wiki/Brentford\" title=\"Brentford\">Brentford</a>, England (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Leader and important benefactor of his home town of <a href=\"https://wikipedia.org/wiki/Brentford\" title=\"Brentford\">Brentford</a>, England (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Brentford", "link": "https://wikipedia.org/wiki/Brentford"}]}, {"year": "1818", "text": "<PERSON>, Costa Rican lawyer and politician, 1st President of Costa Rica (d. 1892)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>_Madriz\" title=\"<PERSON>\"><PERSON></a>, Costa Rican lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Costa_Rica\" title=\"President of Costa Rica\">President of Costa Rica</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Castro_Madriz\" title=\"<PERSON>\"><PERSON></a>, Costa Rican lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Costa_Rica\" title=\"President of Costa Rica\">President of Costa Rica</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Castro_Madriz"}, {"title": "President of Costa Rica", "link": "https://wikipedia.org/wiki/President_of_Costa_Rica"}]}, {"year": "1848", "text": "<PERSON>, Swiss myrmecologist, neuroanatomist, and psychiatrist (d. 1931)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss myrmecologist, neuroanatomist, and psychiatrist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss myrmecologist, neuroanatomist, and psychiatrist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, Hungarian anatomist (d. 1910)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian anatomist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian anatomist (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American baseball player and manager (d. 1919)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rour<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rour<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 1919)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Rourke_(baseball)"}]}, {"year": "1851", "text": "<PERSON>, American journalist and agent (d. 1932)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and agent (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and agent (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON>, Russian general (d. 1926)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Aleksei_Brusilov\" title=\"Aleksei Brusilov\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksei_Brusilov\" title=\"Aleksei Brusilov\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1926)", "links": [{"title": "Aleksei Brusilov", "link": "https://wikipedia.org/wiki/Aleksei_Brusilov"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, German playwright and composer (d. 1921)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, German playwright and composer (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, German playwright and composer (d. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON>, Russian poet and critic (d. 1909)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>oken<PERSON>_<PERSON>\" title=\"Innoken<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and critic (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Innoken<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and critic (d. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Ukrainian-Russian microbiologist and ecologist (d. 1953)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian microbiologist and ecologist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian microbiologist and ecologist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON>, Japanese general (d. 1919)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American boxer (d. 1933)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, 1st Baron <PERSON>, English sailor and politician (d. 1947)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English sailor and politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English sailor and politician (d. 1947)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Canadian publisher and politician (d. 1952)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher and politician (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher and politician (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON> <PERSON>, American lawyer, civil servant, and religious leader (d. 1961)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, civil servant, and religious leader (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, civil servant, and religious leader (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON> the founder of Gaf<PERSON>ha da Nazaré (d. 1925)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Ferreira_Sardo\" title=\"<PERSON>\"><PERSON></a> the founder of <PERSON><PERSON><PERSON><PERSON> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Ferreira_Sardo\" title=\"<PERSON>\"><PERSON></a> the founder of <PERSON><PERSON><PERSON><PERSON> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_Sardo"}]}, {"year": "1875", "text": "<PERSON>, American author (d. 1950)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English journalist and activist (d. 1961)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English chemist and physicist, Nobel Prize laureate (d. 1945)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1877", "text": "<PERSON>, American author, playwright, and water polo player (d. 1949)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Rex_Beach\" title=\"Rex Beach\"><PERSON></a>, American author, playwright, and water polo player (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rex_Beach\" title=\"Rex Beach\"><PERSON></a>, American author, playwright, and water polo player (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rex_Beach"}]}, {"year": "1878", "text": "Princess <PERSON> of Saxe-Coburg and Gotha (d. 1942)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha\" title=\"Princess Alexandra of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha\" title=\"Princess Alexandra of Saxe-Coburg and Gotha\">Princess <PERSON> of Saxe-Coburg and Gotha</a> (d. 1942)", "links": [{"title": "Princess <PERSON> of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/Princess_Alexandra_of_Saxe-Coburg_and_Gotha"}]}, {"year": "1878", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English general and historian (d. 1966)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English general and historian (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English general and historian (d. 1966)", "links": [{"title": "J. F. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Italian conductor and director (d. 1968)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian conductor and director (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian conductor and director (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Canadian ice hockey player (d. 1934)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pit<PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player (d. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>re"}]}, {"year": "1884", "text": "<PERSON>, Australian artist (d. 1961)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian artist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian artist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Swedish actor and director (d. 1947)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor and director (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor and director (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigurd_Wall%C3%A9n"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Swiss composer and conductor (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss composer and conductor (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss composer and conductor (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Shi<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Shi<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Swiss author and poet (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Croatian physician (d. 1958)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%A0tampar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian physician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%A0tampar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian physician (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andri<PERSON>_%C5%A0tampar"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, American lieutenant and politician, 55th Governor of Massachusetts (d. 1979)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Leverett_Saltonstall\" title=\"Leverett Saltonstall\"><PERSON><PERSON><PERSON></a>, American lieutenant and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leverett_Saltonstall\" title=\"Leverett Saltonstall\"><PERSON><PERSON><PERSON></a>, American lieutenant and politician, 55th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1979)", "links": [{"title": "Leverett <PERSON>tall", "link": "https://wikipedia.org/wiki/Leverett_Saltonstall"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Japanese-American painter and photographer (d. 1953)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American painter and photographer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American painter and photographer (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, German engineer and designer, invented the Human-powered aircraft (d. 1955)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer and designer, invented the <a href=\"https://wikipedia.org/wiki/Human-powered_aircraft\" title=\"Human-powered aircraft\">Human-powered aircraft</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer and designer, invented the <a href=\"https://wikipedia.org/wiki/Human-powered_aircraft\" title=\"Human-powered aircraft\">Human-powered aircraft</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Human-powered aircraft", "link": "https://wikipedia.org/wiki/Human-powered_aircraft"}]}, {"year": "1896", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian religious leader, founded the International Society for Krishna Consciousness (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/A._C._<PERSON><PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>\" title=\"A. C. Bhakti<PERSON><PERSON>a Swami <PERSON>\"><PERSON>. C<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness\" title=\"International Society for Krishna Consciousness\">International Society for Krishna Consciousness</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._C._<PERSON><PERSON><PERSON><PERSON><PERSON>_Swami_<PERSON>\" title=\"A. C. Bhaktivedanta Swami <PERSON>\"><PERSON>. C. <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader, founded the <a href=\"https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness\" title=\"International Society for Krishna Consciousness\">International Society for Krishna Consciousness</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>a"}, {"title": "International Society for Krishna Consciousness", "link": "https://wikipedia.org/wiki/International_Society_for_Krishna_Consciousness"}]}, {"year": "1897", "text": "<PERSON>, Irish footballer (d. 1963)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1897)\" title=\"<PERSON> (footballer, born 1897)\"><PERSON></a>, Irish footballer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1897)\" title=\"<PERSON> (footballer, born 1897)\"><PERSON></a>, Irish footballer (d. 1963)", "links": [{"title": "<PERSON> (footballer, born 1897)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1897)"}]}, {"year": "1898", "text": "<PERSON>, English actress and singer (d. 1983)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carson\"><PERSON></a>, English actress and singer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American actor (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish psychiatrist and psychologist (d. 1980)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Kazimierz_D%C4%85bro<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish psychiatrist and psychologist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kazimierz_D%C4%85bro<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish psychiatrist and psychologist (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazimierz_D%C4%85<PERSON>wski"}]}, {"year": "1904", "text": "<PERSON>, American football player and actor (d. 1974)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Dutch arachnologist (d. 1972)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Father_<PERSON><PERSON><PERSON><PERSON>\" title=\"Father <PERSON><PERSON><PERSON>\">Father <PERSON><PERSON></a>, Dutch arachnologist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Father_<PERSON><PERSON><PERSON><PERSON>\" title=\"Father <PERSON><PERSON><PERSON>\">Father <PERSON><PERSON><PERSON><PERSON></a>, Dutch arachnologist (d. 1972)", "links": [{"title": "Father <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican lawyer and politician, 49th President of the Dominican Republic (d. 2002)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>%C3%ADn_Balaguer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%ADn_Balaguer\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican lawyer and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_the_Dominican_Republic\" title=\"President of the Dominican Republic\">President of the Dominican Republic</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_Balaguer"}, {"title": "President of the Dominican Republic", "link": "https://wikipedia.org/wiki/President_of_the_Dominican_Republic"}]}, {"year": "1906", "text": "<PERSON>, German composer and educator (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English author (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English footballer and manager (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Filipino businessman and politician (d. 1981)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino businessman and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino businessman and politician (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American labor union leader and president of the United Auto Workers (d. 1970) ", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor union leader and president of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (d. <a href=\"https://wikipedia.org/wiki/1970\" title=\"1970\">1970</a>) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor union leader and president of the <a href=\"https://wikipedia.org/wiki/United_Auto_Workers\" title=\"United Auto Workers\">United Auto Workers</a> (d. <a href=\"https://wikipedia.org/wiki/1970\" title=\"1970\">1970</a>) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Auto Workers", "link": "https://wikipedia.org/wiki/United_Auto_Workers"}, {"title": "1970", "link": "https://wikipedia.org/wiki/1970"}]}, {"year": "1908", "text": "<PERSON>, Pakistani cricketer (d. 1980)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, English social activist (d. 2012)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social activist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social activist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Canadian historian and diplomat (d. 1957)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian and diplomat (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Canadian historian and diplomat (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Austrian painter and illustrator (d. 1979)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and illustrator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and illustrator (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American jockey (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, American jockey (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, American jockey (d. 1995)", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(jockey)"}]}, {"year": "1916", "text": "<PERSON>, American tennis player (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, South African cricketer (d. 2008)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German physicist and academic (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4nchen\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4nchen\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hilda_H%C3%A4nchen"}]}, {"year": "1920", "text": "<PERSON>, American journalist, author and activist (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Uruguayan lawyer and journalist (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan lawyer and journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan lawyer and journalist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and stuntman (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Dutch author, poet and playwright (d. 1995)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet and playwright (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, poet and playwright (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian-American actress and singer (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Italian actor, director and screenwriter (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor, director and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor, director and screenwriter (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American boxer (d. 1969)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, 2nd Baron <PERSON> of Fleet, Canadian businessman and art collector (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\"><PERSON>, 2nd Baron <PERSON> of Fleet</a>, Canadian businessman and art collector (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\"><PERSON>, 2nd Baron <PERSON> of Fleet</a>, Canadian businessman and art collector (d. 2006)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON> of Fleet", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Fleet"}]}, {"year": "1924", "text": "<PERSON>, American voice actor (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, American activist (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American activist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American activist (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American saxophonist, clarinet player and composer (d. 1982)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Art_Pepper\" title=\"Art Pepper\"><PERSON></a>, American saxophonist, clarinet player and composer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Pepper\" title=\"Art Pepper\"><PERSON></a>, American saxophonist, clarinet player and composer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Pepper"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Bangladeshi banker and politician, 10th President of Bangladesh (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi banker and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi banker and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1926", "text": "<PERSON>, American illustrator (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Colan"}]}, {"year": "1926", "text": "<PERSON>, Australian ice hockey player and coach (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Australian ice hockey player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Australian ice hockey player and coach (d. 2012)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Austrian painter (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Afroyim\" title=\"<PERSON><PERSON><PERSON> Afroyim\"><PERSON><PERSON><PERSON></a>, Austrian painter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yi<PERSON>\" title=\"<PERSON>shan<PERSON> Afroyim\"><PERSON><PERSON><PERSON></a>, Austrian painter (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American author and screenwriter (d. 1978)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor, singer, and artist (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and artist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and artist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American baseball player (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian wrestler (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and civil servant (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Turgut_%C3%96zakman\" title=\"Turgut <PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and civil servant (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turgut_%C3%96zakman\" title=\"Turgut <PERSON>zak<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and civil servant (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Turgut_%C3%96zakman"}]}, {"year": "1930", "text": "<PERSON>, Dutch composer and theorist (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and theorist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch composer and theorist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Indian architect (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian architect (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian architect (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Indian theologian and scholar (d. 2012)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian theologian and scholar (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian theologian and scholar (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American journalist and sportscaster (d. 2012)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and sportscaster (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cook\"><PERSON><PERSON></a>, American journalist and sportscaster (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English accountant and politician, Secretary of State for Transport (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Transport\" title=\"Secretary of State for Transport\">Secretary of State for Transport</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Transport", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Transport"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 1999)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Willie\" title=\"<PERSON>car Willie\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Willie\" title=\"Boxcar Willie\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "links": [{"title": "Boxcar Willie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Willie"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Nauruan politician, 23rd President of Nauru (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>og_G<PERSON>\" title=\"Derog Gioura\"><PERSON><PERSON></a>, Nauruan politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>\" title=\"Derog Gioura\"><PERSON><PERSON></a>, Nauruan politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (d. 2008)", "links": [{"title": "Derog Gioura", "link": "https://wikipedia.org/wiki/Derog_G<PERSON>ura"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1933", "text": "<PERSON>, American bass player and songwriter  (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American educator and politician, 45th Governor of Texas (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Sri Lankan politician (d. 1982)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON><PERSON><PERSON>\">T<PERSON> <PERSON><PERSON></a>, Sri Lankan politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Sri Lankan politician (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1993)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Twitty\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Twitty\"><PERSON></a>, American singer-songwriter and guitarist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>witty"}]}, {"year": "1935", "text": "<PERSON>, English cartoonist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Japanese conductor and director (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese conductor and director (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese conductor and director (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American basketball player (d. 2001)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Soviet inorganic chemist, chief of the commission investigating the Chernobyl disaster (d. 1988)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet inorganic chemist, chief of the commission investigating the Chernobyl disaster (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet inorganic chemist, chief of the commission investigating the Chernobyl disaster (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Danish painter, sculptor and poet (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter, sculptor and poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter, sculptor and poet (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Dominican baseball player (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Car<PERSON>\"><PERSON></a>, Dominican baseball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rico_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress, comedian, screenwriter and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Turkish general (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Ya%C5%9Far_B%C3%BCy%C3%BCkan%C4%B1t\" title=\"<PERSON><PERSON><PERSON>ü<PERSON>ükanıt\"><PERSON><PERSON><PERSON></a>, Turkish general (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%C5%9Far_B%C3%BCy%C3%BCkan%C4%B1t\" title=\"<PERSON><PERSON><PERSON> Bü<PERSON>ükanıt\"><PERSON><PERSON><PERSON></a>, Turkish general (d. 2019)", "links": [{"title": "Yaşar Büyükanıt", "link": "https://wikipedia.org/wiki/Ya%C5%9Far_B%C3%BCy%C3%BCkan%C4%B1t"}]}, {"year": "1940", "text": "<PERSON>, French author, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and educator", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>\" title=\"C. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and educator", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>roud"}]}, {"year": "1944", "text": "<PERSON>, American soul singer-songwriter and musician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American soul singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American soul singer-songwriter and musician", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1944", "text": "<PERSON>, American conductor and composer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Yemeni general and politician, 2nd President of Yemen", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Yemeni general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Yemen\" title=\"President of Yemen\">President of Yemen</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Yemeni general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Yemen\" title=\"President of Yemen\">President of Yemen</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Yemen", "link": "https://wikipedia.org/wiki/President_of_Yemen"}]}, {"year": "1946", "text": "<PERSON>, Manx-English singer-songwriter and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx-English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Israeli rock singer, lyricist and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rock singer, lyricist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rock singer, lyricist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>och"}]}, {"year": "1946", "text": "<PERSON><PERSON>, South Korean soldier and politician, 9th President of South Korea (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>yun\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Korea\" class=\"mw-redirect\" title=\"List of Presidents of South Korea\">President of South Korea</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean soldier and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Korea\" class=\"mw-redirect\" title=\"List of Presidents of South Korea\">President of South Korea</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>yun"}, {"title": "List of Presidents of South Korea", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_South_Korea"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 11th Speaker of the Lok Sabha (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Sang<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1948", "text": "<PERSON>, American drummer and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Polish archbishop and philosopher (d. 2011)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_%C5%BByci%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop and philosopher (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3zef_%C5%BByci%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop and philosopher (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_%C5%BByci%C5%84ski"}]}, {"year": "1948", "text": "<PERSON>, American drummer and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish physician and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish physician and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Russian politician, 36th Prime Minister of Russia", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician, 36th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "1950", "text": "<PERSON>, American football player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American psychologist, author and talk show host", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist, author and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_McGraw"}]}, {"year": "1951", "text": "<PERSON>, English cricketer and sportscaster (d. 1998)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor (d. 2016)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Spanish golfer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ero\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1ero\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_Pi%C3%B1ero"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, transportation journalist, founder of Transportation Communications Newsletter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, transportation journalist, founder of <a href=\"https://wikipedia.org/wiki/Transportation_Communications_Newsletter\" title=\"Transportation Communications Newsletter\">Transportation Communications Newsletter</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, transportation journalist, founder of <a href=\"https://wikipedia.org/wiki/Transportation_Communications_Newsletter\" title=\"Transportation Communications Newsletter\">Transportation Communications Newsletter</a>", "links": [{"title": "Bernie <PERSON>", "link": "https://wikipedia.org/wiki/Bernie_Wagenblast"}, {"title": "Transportation Communications Newsletter", "link": "https://wikipedia.org/wiki/Transportation_Communications_Newsletter"}]}, {"year": "1957", "text": "<PERSON>, Australian linguist[better source needed]", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian linguist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian linguist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Cuban-American singer-songwriter and actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Montenegrin basketball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1ko_Ivanovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1ko_Ivanovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Montenegrin basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1ko_Ivanovi%C4%87"}]}, {"year": "1959", "text": "<PERSON>, English footballer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9Fem\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9Fem\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra<PERSON>_<PERSON>%C3%9Fem"}]}, {"year": "1960", "text": "<PERSON>, American football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Karl_Mecklenburg\" title=\"Karl Mecklenburg\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_Mecklenburg\" title=\"Karl Mecklenburg\"><PERSON></a>, American football player", "links": [{"title": "Karl <PERSON>", "link": "https://wikipedia.org/wiki/Karl_Mecklenburg"}]}, {"year": "1961", "text": "<PERSON>, British academic and educator; director of the Wellcome Trust", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator; director of the <a href=\"https://wikipedia.org/wiki/Wellcome_Trust\" title=\"Wellcome Trust\">Wellcome Trust</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator; director of the <a href=\"https://wikipedia.org/wiki/Wellcome_Trust\" title=\"Wellcome Trust\">Wellcome Trust</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Wellcome Trust", "link": "https://wikipedia.org/wiki/Wellcome_Trust"}]}, {"year": "1961", "text": "<PERSON>, American captain, pilot and astronaut", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American saxophonist, composer and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English-Irish footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Ru<PERSON>_<PERSON>ullit\" title=\"<PERSON><PERSON><PERSON>ull<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ru<PERSON>_<PERSON>ullit\" title=\"<PERSON><PERSON><PERSON>ull<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ru<PERSON>_<PERSON>ullit"}]}, {"year": "1963", "text": "<PERSON>, Australian footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American musician and actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American musician and actor", "links": [{"title": "Grant<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian ice hockey player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2023)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian actor and singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (d. 2002)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English actor, screenwriter and director", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian engineer and politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Egyptian terrorist (d. 2001)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian terrorist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian terrorist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Norwegian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby league player, coach and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, South Korean actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-min"}]}, {"year": "1970", "text": "<PERSON>, Indian-American actress and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese bass player and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese bass player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese bass player and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Turkish footballer and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Hakan_%C5%9E%C3%BCk%C3%BCr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hakan_%C5%9E%C3%BCk%C3%BCr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hakan_%C5%9E%C3%BCk%C3%BCr"}]}, {"year": "1971", "text": "<PERSON>, American fashion designer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/J.D._Fortune\" title=\"J.D. Fortune\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J.D._Fortune\" title=\"J.D. Fortune\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "J.D. <PERSON>", "link": "https://wikipedia.org/wiki/J.D._Fortune"}]}, {"year": "1973", "text": "<PERSON>, English rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Indian actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American-born English actor and musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Go<PERSON>\" title=\"<PERSON> Gorman\"><PERSON></a>, American-born English actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gorman\"><PERSON></a>, American-born English actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rman"}]}, {"year": "1974", "text": "<PERSON>, American football player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Japanese director and producer, founder of Ordet Animation Studio", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and producer, founder of <a href=\"https://wikipedia.org/wiki/Ordet_(company)\" class=\"mw-redirect\" title=\"Ordet (company)\">Ordet Animation Studio</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and producer, founder of <a href=\"https://wikipedia.org/wiki/Ordet_(company)\" class=\"mw-redirect\" title=\"Ordet (company)\">Ordet Animation Studio</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ordet (company)", "link": "https://wikipedia.org/wiki/Ordet_(company)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American writer, director, cartoonist and comic illustrator", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American writer, director, cartoonist and comic illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American writer, director, cartoonist and comic illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American anti-government militant ", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>undy\" title=\"<PERSON>mon Bundy\"><PERSON><PERSON></a>, American anti-government militant ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>undy\" title=\"<PERSON><PERSON> Bundy\"><PERSON><PERSON></a>, American anti-government militant ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mobley\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mobley\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English-Canadian actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, English-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, English-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Babydaddy\" title=\"Babydaddy\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babydaddy\" title=\"Babydaddy\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Babydaddy"}]}, {"year": "1976", "text": "<PERSON>, Australian racing driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Mexican boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Rozental\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Rozental\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Rozental"}]}, {"year": "1977", "text": "<PERSON>, Spanish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian racing driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American lawyer and author", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian-Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ieri"}]}, {"year": "1980", "text": "<PERSON>, Ghanaian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clinton_<PERSON>is"}]}, {"year": "1981", "text": "<PERSON>, Australian basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Quick\" title=\"Adam Quick\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Quick\" title=\"Adam Quick\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian figure skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian racing driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>-<PERSON>, American actress and director", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Spanish cyclist (d. 2012)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I%C3%B1<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Spanish footballer (d. 2019)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ka"}]}, {"year": "1984", "text": "<PERSON>, Swedish film composer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ludwig_G%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish film composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_G%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish film composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_G%C3%B<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_K%C3%B6teles\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_K%C3%B6teles\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_K%C3%B6teles"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter, guitarist and producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>,  French tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Monfils\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%ABl_Monfils\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%ABl_Monfils"}]}, {"year": "1986", "text": "<PERSON>, Kenyan-Norwegian singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Norwegian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Cuban decathlete", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Leonel_Su%C3%A1rez\" title=\"<PERSON><PERSON> Suárez\"><PERSON><PERSON></a>, Cuban decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leonel_Su%C3%A1rez\" title=\"<PERSON><PERSON> Suárez\"><PERSON><PERSON></a>, Cuban decathlete", "links": [{"title": "Leonel Suárez", "link": "https://wikipedia.org/wiki/Leonel_Su%C3%A1rez"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Norwegian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Swiss racing driver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miles_Plumlee"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American rapper-songwriter and model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Chanel_West_Coast\" title=\"Chanel West Coast\"><PERSON><PERSON></a>, American rapper-songwriter and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chanel_West_Coast\" title=\"Chanel West Coast\"><PERSON><PERSON></a>, American rapper-songwriter and model", "links": [{"title": "Chanel West Coast", "link": "https://wikipedia.org/wiki/Chanel_West_Coast"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Italian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German singer and songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ecuadorian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jefferson_<PERSON>ro"}]}, {"year": "1989", "text": "<PERSON>, Swedish ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Aisling_Loftus\" title=\"Aisling Loftus\"><PERSON><PERSON><PERSON> Loft<PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aisling_Loftus\" title=\"Aisling Loftus\"><PERSON><PERSON><PERSON> Loft<PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aisling_Loftus"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Czech footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)\" title=\"<PERSON> (footballer, born 1991)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1991)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1991)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Grenadian sprinter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grenadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Grenadian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Nosek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Nosek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Nosek"}]}, {"year": "1992", "text": "<PERSON><PERSON>im, South Korean singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Woo_Hye-lim\" class=\"mw-redirect\" title=\"Woo Hye-lim\"><PERSON><PERSON>ye-lim</a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woo_Hye-lim\" class=\"mw-redirect\" title=\"Woo Hye-lim\"><PERSON><PERSON> <PERSON>ye-lim</a>, South Korean singer-songwriter", "links": [{"title": "<PERSON><PERSON>-lim", "link": "https://wikipedia.org/wiki/Woo_<PERSON>ye-lim"}]}, {"year": "1993", "text": "<PERSON>, Gabonese footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Gabonese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Russian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Spanish racing driver ", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Spanish racing driver ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, Spanish racing driver ", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Zendaya\" title=\"Zenday<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zendaya\" title=\"Zenday<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>daya"}]}, {"year": "1997", "text": "<PERSON><PERSON>, South Korean singer, songwriter and record producer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer, songwriter and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Spanish motorcycle racer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Nigerian-American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Cam_<PERSON>\" title=\"Cam <PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cam <PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cam_Reddish"}]}, {"year": "2000", "text": "<PERSON>, Israeli Olympic cyclist", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli Olympic cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli Olympic cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Indian Cricketer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>al\"><PERSON><PERSON><PERSON></a>, Indian Cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>al"}]}, {"year": "2002", "text": "<PERSON>, French tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, South Korean singer and actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON> Yu-jin\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON> Yu-jin\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}], "Deaths": [{"year": "870", "text": "<PERSON>, Persian scholar (b. 810)", "html": "870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian scholar (b. 810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian scholar (b. 810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1081", "text": "Bishop <PERSON><PERSON><PERSON> of Angers", "html": "1081 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Angers\" title=\"<PERSON><PERSON><PERSON> of Angers\"><PERSON><PERSON><PERSON> of Angers</a>", "no_year_html": "Bishop <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Angers\" title=\"<PERSON><PERSON><PERSON> of Angers\"><PERSON><PERSON><PERSON> of Angers</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Angers", "link": "https://wikipedia.org/wiki/Eusebius_of_Angers"}]}, {"year": "1159", "text": "<PERSON> (b. 1100)", "html": "1159 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON> Adrian IV\">Pope <PERSON></a> (b. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Adrian <PERSON>\"><PERSON></a> (b. 1100)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1198", "text": "<PERSON><PERSON><PERSON>, Queen of Portugal (b. 1160)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON>, Queen of Portugal</a> (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon\" title=\"<PERSON><PERSON><PERSON> of Aragon\"><PERSON><PERSON><PERSON>, Queen of Portugal</a> (b. 1160)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Aragon"}]}, {"year": "1215", "text": "<PERSON>, bishop of Utrecht", "html": "1215 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Utrecht)\" title=\"<PERSON> (bishop of Utrecht)\"><PERSON></a>, bishop of Utrecht", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Utrecht)\" title=\"<PERSON> (bishop of Utrecht)\"><PERSON></a>, bishop of Utrecht", "links": [{"title": "<PERSON> (bishop of Utrecht)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Utrecht)"}]}, {"year": "1256", "text": "<PERSON><PERSON>, Japanese shōgun (b. 1218)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsune\" title=\"<PERSON><PERSON> Yoritsune\"><PERSON><PERSON></a>, Japanese shōgun (b. 1218)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuj%C5%8D_Yoritsune\" title=\"<PERSON><PERSON> Yoritsune\"><PERSON><PERSON> Yoritsune</a>, Japanese shōgun (b. 1218)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kuj%C5%8D_Yo<PERSON>une"}]}, {"year": "1327", "text": "<PERSON><PERSON><PERSON><PERSON>, Grand Master of the Knights Hospitaller", "html": "1327 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Villaret\" title=\"<PERSON><PERSON><PERSON><PERSON> de Villaret\"><PERSON><PERSON><PERSON><PERSON></a>, Grand Master of the Knights Hospitaller", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Villaret\" title=\"<PERSON>oul<PERSON> de Villaret\"><PERSON><PERSON><PERSON><PERSON></a>, Grand Master of the Knights Hospitaller", "links": [{"title": "Foulques de Villaret", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1339", "text": "<PERSON>, Duke of Bavaria (b. 1305)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1305)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1376", "text": "<PERSON> Valois, Duke of Orléans (b. 1336)", "html": "1376 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois,_Duke_of_Orl%C3%A9ans\" class=\"mw-redirect\" title=\"<PERSON> of Valois, Duke of Orléans\"><PERSON> of Valois, Duke of Orléans</a> (b. 1336)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois,_Duke_of_Orl%C3%A9ans\" class=\"mw-redirect\" title=\"<PERSON> of Valois, Duke of Orléans\"><PERSON> of Valois, Duke of Orléans</a> (b. 1336)", "links": [{"title": "<PERSON> Valois, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1414", "text": "<PERSON>, 6th Baron <PERSON>, English politician, Lord High Treasurer (b. 1369)", "html": "1414 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (b. 1369)", "links": [{"title": "<PERSON>, 6th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>_<PERSON>"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1480", "text": "<PERSON>, Count of Württemberg (b. 1413)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%BCrttemberg\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a> (b. 1413)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%BCrttemberg\" title=\"<PERSON>, Count of Württemberg\"><PERSON>, Count of Württemberg</a> (b. 1413)", "links": [{"title": "<PERSON>, Count of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_W%C3%<PERSON>rt<PERSON><PERSON>"}]}, {"year": "1557", "text": "<PERSON>, French navigator and explorer (b. 1491)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French navigator and explorer (b. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French navigator and explorer (b. 1491)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, Sikh 4th of the Ten Gurus of Sikhism (b. 1534)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikh</a> 4th of the <a href=\"https://wikipedia.org/wiki/Ten_Gurus_of_Sikhism\" class=\"mw-redirect\" title=\"Ten Gurus of Sikhism\">Ten Gurus of Sikhism</a> (b. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikh</a> 4th of the <a href=\"https://wikipedia.org/wiki/Ten_Gurus_of_Sikhism\" class=\"mw-redirect\" title=\"Ten Gurus of Sikhism\">Ten Gurus of Sikhism</a> (b. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sikh", "link": "https://wikipedia.org/wiki/Sikh"}, {"title": "Ten Gurus of Sikhism", "link": "https://wikipedia.org/wiki/Ten_Gurus_of_Sikhism"}]}, {"year": "1599", "text": "<PERSON><PERSON><PERSON>, Dutch explorer (b. 1565)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch explorer (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch explorer (b. 1565)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1615", "text": "<PERSON>, French lawyer and jurist (b. 1529)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and jurist (b. 1529)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>er"}]}, {"year": "1646", "text": "<PERSON>, English statesman (b. 1582)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman (b. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, French mathematician, theologian, and philosopher (b. 1588)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, theologian, and philosopher (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, theologian, and philosopher (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON> the Younger, Flemish painter (b. 1601)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the Younger</a>, Flemish painter (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the Younger</a>, Flemish painter (b. 1601)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1685", "text": "<PERSON><PERSON>, Welsh lawyer, jurist, and politician, Secretary of State for the Northern Department (b. 1625)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh lawyer, jurist, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department\" title=\"Secretary of State for the Northern Department\">Secretary of State for the Northern Department</a> (b. 1625)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Secretary of State for the Northern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Northern_Department"}]}, {"year": "1687", "text": "<PERSON>, English priest and philosopher (b. 1614)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and philosopher (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> More\"><PERSON></a>, English priest and philosopher (b. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1621)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_<PERSON>\" title=\"Corne<PERSON> de Man\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_<PERSON>\" title=\"Corne<PERSON> de Man\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French sculptor (b. 1628)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Girardon\" title=\"<PERSON>\"><PERSON></a>, French sculptor (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Girardon\" title=\"<PERSON>\"><PERSON></a>, French sculptor (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON> of France (b. 1638)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"<PERSON> XIV of France\"><PERSON> of France</a> (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a> (b. 1638)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}]}, {"year": "1838", "text": "<PERSON>, American soldier, explorer, and politician, 4th Governor of Missouri Territory (b. 1770)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, explorer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri Territory</a> (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, explorer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri Territory</a> (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Governors of Missouri", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Missouri"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, Hungarian theologian and educator (b. 1786)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian theologian and educator (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian theologian and educator (b. 1786)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, Hungarian-Austrian commander and politician (b. 1799)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian commander and politician (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Austrian commander and politician (b. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Gyu<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, last known passenger pigeon (h. 1885)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(passenger_pigeon)\" title=\"<PERSON> (passenger pigeon)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"End<PERSON>\">last known</a> <a href=\"https://wikipedia.org/wiki/Passenger_pigeon\" title=\"Passenger pigeon\">passenger pigeon</a> (h. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(passenger_pigeon)\" title=\"<PERSON> (passenger pigeon)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Endling\" title=\"Endling\">last known</a> <a href=\"https://wikipedia.org/wiki/Passenger_pigeon\" title=\"Passenger pigeon\">passenger pigeon</a> (h. 1885)", "links": [{"title": "<PERSON> (passenger pigeon)", "link": "https://wikipedia.org/wiki/<PERSON>_(passenger_pigeon)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endling"}, {"title": "Passenger pigeon", "link": "https://wikipedia.org/wiki/Passenger_pigeon"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Hungarian architect and academic (b. 1854)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian architect and academic (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>z\"><PERSON><PERSON></a>, Hungarian architect and academic (b. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Samu_<PERSON>z"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Georgian Social Democrat politician (b. 1883)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hom<PERSON>\"><PERSON><PERSON></a>, Georgian Social Democrat politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hom<PERSON>\"><PERSON><PERSON></a>, Georgian Social Democrat politician (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Estonian scientist and politician, 1st Estonian Minister of Education (b. 1878)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_P%C3%B5ld\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian scientist and politician, 1st <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_P%C3%B5ld\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian scientist and politician, 1st <a href=\"https://wikipedia.org/wiki/Estonian_Minister_of_Education\" class=\"mw-redirect\" title=\"Estonian Minister of Education\">Estonian Minister of Education</a> (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peeter_P%C3%B5ld"}, {"title": "Estonian Minister of Education", "link": "https://wikipedia.org/wiki/Estonian_Minister_of_Education"}]}, {"year": "1943", "text": "<PERSON>, Cameroonian ruler (b. 1880)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian ruler (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian ruler (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soldier and adventurer (b. 1861)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and adventurer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and adventurer (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Canadian author and suffragist (b. 1873)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and suffragist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author and suffragist (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, English soldier and writer (b. 1886)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English soldier and writer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English soldier and writer (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author (b. 1897)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1897)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1970", "text": "<PERSON>, French novelist, poet, and playwright, Nobel Prize laureate (b. 1885)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON>ois_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1971", "text": "<PERSON>, English soldier (b. 1909)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier (b. 1909)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Estonian dancer, dance teacher, and choreographer (b. 1891)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_<PERSON>go\" title=\"Gerd Neggo\"><PERSON><PERSON></a>, Estonian dancer, dance teacher, and choreographer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>go\" title=\"G<PERSON> Neggo\"><PERSON><PERSON></a>, Estonian dancer, dance teacher, and choreographer (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>erd_<PERSON>eggo"}]}, {"year": "1977", "text": "<PERSON>, American singer and actress (b. 1896)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress (b. 1901)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German architect and author (b. 1905)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and author (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American mathematician and academic (b. 1900)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curry\"><PERSON><PERSON></a>, American mathematician and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curry\"><PERSON><PERSON></a>, American mathematician and academic (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish activist and politician (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Gomu%C5%82ka\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish activist and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Gomu%C5%82ka\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish activist and politician (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Gomu%C5%82ka"}]}, {"year": "1982", "text": "<PERSON>, Brazilian student; honored by the Catholicism (b. 1962)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian student; honored by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholicism</a> (b. <a href=\"https://wikipedia.org/wiki/1962\" title=\"1962\">1962</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian student; honored by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholicism</a> (b. <a href=\"https://wikipedia.org/wiki/1962\" title=\"1962\">1962</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "1962", "link": "https://wikipedia.org/wiki/1962"}]}, {"year": "1983", "text": "<PERSON>, American lawyer and politician (b. 1912)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American physician and politician (b. 1935)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Duchess of Parma (b. 1898)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Parma (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Duchess of Parma (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Bus<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German racing driver (b. 1957)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hamilton\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hamilton\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1911)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American businessman and academic (b. 1938)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer (b. 1947)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Polish-American engineer (b. 1894)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Tadeusz_Sendzimir\" title=\"Tadeusz Sendzimir\"><PERSON><PERSON><PERSON></a>, Polish-American engineer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tadeusz_Sendzimir\" title=\"Tadeusz Send<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American engineer (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_Sendzimir"}]}, {"year": "1990", "text": "<PERSON>, American scholar and diplomat (b. 1910)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and diplomat (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and diplomat (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, German graphic designer and typographer (b. 1922)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German graphic designer and typographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German graphic designer and typographer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>cher"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer (b. 1929)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Czibor\" title=\"<PERSON><PERSON>án Czibor\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Czibor\" title=\"<PERSON><PERSON><PERSON> Czibor\"><PERSON><PERSON><PERSON></a>, Hungarian footballer (b. 1929)", "links": [{"title": "Zoltán C<PERSON>bor", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Czibor"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Polish poet and author (b. 1930)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>rupi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>i%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and author (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Krupi%C5%84ski"}]}, {"year": "1998", "text": "<PERSON>, American golfer and sportscaster (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cary_Middlecoff"}]}, {"year": "1998", "text": "<PERSON><PERSON> <PERSON>, Turkish director, producer, and screenwriter (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>sman <PERSON>\"><PERSON><PERSON> <PERSON></a>, Turkish director, producer, and screenwriter (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Zambian computer scientist and author (b. 1951)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Zambian computer scientist and author (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Zambian computer scientist and author (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor and producer (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brooks\"><PERSON></a>, American actor and producer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Brooks"}]}, {"year": "2003", "text": "<PERSON>, English painter and academic (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Syrian religious leader, Grand <PERSON> of Syria (b. 1915)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian religious leader, <a href=\"https://wikipedia.org/wiki/Grand_Mufti_of_Syria\" title=\"Grand Mufti of Syria\">Grand Mufti of Syria</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian religious leader, <a href=\"https://wikipedia.org/wiki/Grand_Mu<PERSON><PERSON>_of_Syria\" title=\"Grand Mufti of Syria\">Grand Mufti of Syria</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Grand Mufti of Syria", "link": "https://wikipedia.org/wiki/Grand_Mufti_of_Syria"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, South African businessman (b. 1938)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African businessman (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African businessman (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ast<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and guitarist (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1926)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian author and poet (b. 1910)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Faludy\" title=\"György Faludy\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Faludy\" title=\"György Faludy\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian author and poet (b. 1910)", "links": [{"title": "György Faludy", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Faludy"}]}, {"year": "2006", "text": "<PERSON>, American journalist (b. 1934)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American businessman and politician, 57th Mayor of Pittsburgh (b. 1944)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(mayor)\" title=\"<PERSON> (mayor)\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>_(mayor)\" title=\"<PERSON> (mayor)\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/Mayor_of_Pittsburgh\" class=\"mw-redirect\" title=\"Mayor of Pittsburgh\">Mayor of Pittsburgh</a> (b. 1944)", "links": [{"title": "<PERSON> (mayor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(mayor)"}, {"title": "Mayor of Pittsburgh", "link": "https://wikipedia.org/wiki/Mayor_of_Pittsburgh"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Welsh painter and educator (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh painter and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh painter and educator (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand horse racer and philanthropist (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand horse racer and philanthropist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand horse racer and philanthropist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Czech-Canadian businessman (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian businessman (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1937)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 45th <PERSON><PERSON><PERSON><PERSON> (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_I\" title=\"Wakanohana Kanji I\"><PERSON><PERSON><PERSON><PERSON> Kanji I</a>, Japanese sumo wrestler, the 45th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_I\" title=\"Wakanohana Kanji I\"><PERSON><PERSON><PERSON><PERSON> Kanji I</a>, Japanese sumo wrestler, the 45th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Kanji I", "link": "https://wikipedia.org/wiki/Waka<PERSON><PERSON>_Kanji_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "2012", "text": "<PERSON>, South African saxophonist, flute player, and composer (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African saxophonist, flute player, and composer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African saxophonist, flute player, and composer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American songwriter and composer (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Haitian businessman and politician, 6th Prime Minister of Haiti (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "2012", "text": "<PERSON>, Swedish politician (b. 1988)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ll\" title=\"<PERSON>\"><PERSON></a>, Swedish politician (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ll\" title=\"<PERSON>\"><PERSON></a>, Swedish politician (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z%C3%A4ll"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Italian illustrator (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian illustrator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian illustrator (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arnal<PERSON>_<PERSON>zu"}]}, {"year": "2013", "text": "<PERSON>, Spanish footballer and manager (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian soldier (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American linguist and academic (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American linguist and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English commander and pilot (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English commander and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Somali militant leader (b. 1977)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali militant leader (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Somali militant leader (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American chemist and academic, developed spandex (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, developed <a href=\"https://wikipedia.org/wiki/Spandex\" title=\"Spandex\">spandex</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, developed <a href=\"https://wikipedia.org/wiki/Spandex\" title=\"Spandex\">spandex</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Spandex", "link": "https://wikipedia.org/wiki/Spandex"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Armenian general (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian general (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian general (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actor and singer (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1931)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2015", "text": "<PERSON>, American historian and author (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sergeant and pilot (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American jazz pianist and composer (b. 1926)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American disc jockey and music producer (b. 1971)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American disc jockey and music producer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American disc jockey and music producer (b. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American writer and journalist (b. 1941)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and journalist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and journalist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Chinese major general (b. 1919)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Major_general\" title=\"Major general\">major general</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Major_general\" title=\"Major general\">major general</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Major general", "link": "https://wikipedia.org/wiki/Major_general"}]}, {"year": "2023", "text": "<PERSON>, American singer-songwriter, musician, author and businessman (b. 1946)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American singer-songwriter, musician, author and businessman (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American singer-songwriter, musician, author and businessman (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American journalist (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}