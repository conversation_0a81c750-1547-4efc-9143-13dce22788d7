{"date": "September 27", "url": "https://wikipedia.org/wiki/September_27", "data": {"Events": [{"year": "1066", "text": "<PERSON> the Conqueror and his army set sail from the mouth of the Somme river, beginning the Norman conquest of England.", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> and his army set sail from the mouth of the Somme river, beginning the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a> and his army set sail from the mouth of the Somme river, beginning the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest of England</a>.", "links": [{"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Norman conquest of England", "link": "https://wikipedia.org/wiki/Norman_conquest_of_England"}]}, {"year": "1331", "text": "The Battle of Płowce is fought, between the Kingdom of Poland and the Teutonic Order. The Poles are defeated but their leaders escape capture.", "html": "1331 - The <a href=\"https://wikipedia.org/wiki/Battle_of_P%C5%82owce\" title=\"Battle of Płowce\">Battle of Płowce</a> is fought, between the Kingdom of Poland and the Teutonic Order. The Poles are defeated but their leaders escape capture.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_P%C5%82owce\" title=\"Battle of Płowce\">Battle of Płowce</a> is fought, between the Kingdom of Poland and the Teutonic Order. The Poles are defeated but their leaders escape capture.", "links": [{"title": "Battle of Płowce", "link": "https://wikipedia.org/wiki/Battle_of_P%C5%82owce"}]}, {"year": "1422", "text": "After the brief Gollub War, the Teutonic Knights sign the Treaty of Melno with Poland and Lithuania.", "html": "1422 - After the brief <a href=\"https://wikipedia.org/wiki/Gollub_War\" class=\"mw-redirect\" title=\"Gollub War\">Gollub War</a>, the Teutonic Knights sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Melno\" title=\"Treaty of Melno\">Treaty of Melno</a> with Poland and Lithuania.", "no_year_html": "After the brief <a href=\"https://wikipedia.org/wiki/Gollub_War\" class=\"mw-redirect\" title=\"Gollub War\">Gollub War</a>, the Teutonic Knights sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Melno\" title=\"Treaty of Melno\">Treaty of Melno</a> with Poland and Lithuania.", "links": [{"title": "Gollub War", "link": "https://wikipedia.org/wiki/Gollub_War"}, {"title": "Treaty of Melno", "link": "https://wikipedia.org/wiki/Treaty_of_Melno"}]}, {"year": "1529", "text": "The Siege of Vienna begins when Suleiman I attacks the city.", "html": "1529 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Vienna_(1529)\" title=\"Siege of Vienna (1529)\">Siege of Vienna</a> begins when Suleiman I attacks the city.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Vienna_(1529)\" title=\"Siege of Vienna (1529)\">Siege of Vienna</a> begins when Suleiman I attacks the city.", "links": [{"title": "Siege of Vienna (1529)", "link": "https://wikipedia.org/wiki/Siege_of_Vienna_(1529)"}]}, {"year": "1540", "text": "The Society of Jesus (Jesuits) receives its charter from <PERSON>.", "html": "1540 - The <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> (Jesuits) receives its charter from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> (Jesuits) receives its charter from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul III\"><PERSON></a>.", "links": [{"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "The armies of Sweden are defeated by the Polish-Lithuanian Commonwealth in the Battle of Kircholm.", "html": "1605 - The armies of Sweden are defeated by the Polish-Lithuanian Commonwealth in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kircholm\" title=\"Battle of Kircholm\">Battle of Kircholm</a>.", "no_year_html": "The armies of Sweden are defeated by the Polish-Lithuanian Commonwealth in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kircholm\" title=\"Battle of Kircholm\">Battle of Kircholm</a>.", "links": [{"title": "Battle of Kircholm", "link": "https://wikipedia.org/wiki/Battle_of_Kircholm"}]}, {"year": "1669", "text": "The Venetians surrender the fortress of Candia to the Ottomans, thus ending the 21-year-long Siege of Candia.", "html": "1669 - The Venetians surrender the fortress of Candia to the Ottomans, thus ending the 21-year-long <a href=\"https://wikipedia.org/wiki/Siege_of_Candia\" title=\"Siege of Candia\">Siege of Candia</a>.", "no_year_html": "The Venetians surrender the fortress of Candia to the Ottomans, thus ending the 21-year-long <a href=\"https://wikipedia.org/wiki/Siege_of_Candia\" title=\"Siege of Candia\">Siege of Candia</a>.", "links": [{"title": "Siege of Candia", "link": "https://wikipedia.org/wiki/Siege_of_Candia"}]}, {"year": "1777", "text": "American Revolution: Lancaster, Pennsylvania becomes the capital of the United States for one day after Congress evacuates Philadelphia.", "html": "1777 - American Revolution: <a href=\"https://wikipedia.org/wiki/Lancaster,_Pennsylvania\" title=\"Lancaster, Pennsylvania\">Lancaster, Pennsylvania</a> becomes the capital of the United States for one day after Congress evacuates Philadelphia.", "no_year_html": "American Revolution: <a href=\"https://wikipedia.org/wiki/Lancaster,_Pennsylvania\" title=\"Lancaster, Pennsylvania\">Lancaster, Pennsylvania</a> becomes the capital of the United States for one day after Congress evacuates Philadelphia.", "links": [{"title": "Lancaster, Pennsylvania", "link": "https://wikipedia.org/wiki/Lancaster,_Pennsylvania"}]}, {"year": "1791", "text": "The National Assembly of France votes to award full citizenship to Jews.", "html": "1791 - The National Assembly of France votes to award full citizenship to <a href=\"https://wikipedia.org/wiki/History_of_the_Jews_in_France\" title=\"History of the Jews in France\">Jews</a>.", "no_year_html": "The National Assembly of France votes to award full citizenship to <a href=\"https://wikipedia.org/wiki/History_of_the_Jews_in_France\" title=\"History of the Jews in France\">Jews</a>.", "links": [{"title": "History of the Jews in France", "link": "https://wikipedia.org/wiki/History_of_the_Jews_in_France"}]}, {"year": "1821", "text": "The Army of the Three Guarantees triumphantly enters Mexico City, led by <PERSON><PERSON><PERSON><PERSON>. The following day Mexico is declared independent.", "html": "1821 - The <a href=\"https://wikipedia.org/wiki/Army_of_the_Three_Guarantees\" title=\"Army of the Three Guarantees\">Army of the Three Guarantees</a> triumphantly enters <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>, led by <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"Agustín de Iturbide\"><PERSON><PERSON><PERSON><PERSON> de Iturbide</a>. The following day Mexico is declared independent.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Army_of_the_Three_Guarantees\" title=\"Army of the Three Guarantees\">Army of the Three Guarantees</a> triumphantly enters <a href=\"https://wikipedia.org/wiki/Mexico_City\" title=\"Mexico City\">Mexico City</a>, led by <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"Agustín de Iturbide\"><PERSON><PERSON><PERSON><PERSON> de Iturbide</a>. The following day Mexico is declared independent.", "links": [{"title": "Army of the Three Guarantees", "link": "https://wikipedia.org/wiki/Army_of_the_Three_Guarantees"}, {"title": "Mexico City", "link": "https://wikipedia.org/wiki/Mexico_City"}, {"title": "Agustín de Iturbide", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON> officially informs the Académie des Inscriptions et Belles Lettres in France that he has deciphered the Rosetta Stone.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_Champo<PERSON>on\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> officially informs the Académie des Inscriptions et Belles Lettres in France that he has deciphered the <a href=\"https://wikipedia.org/wiki/Rose<PERSON>_Stone\" title=\"Rosetta Stone\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_Champo<PERSON>on\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> officially informs the Académie des Inscriptions et Belles Lettres in France that he has deciphered the <a href=\"https://wikipedia.org/wiki/Rose<PERSON>_Stone\" title=\"Rosetta Stone\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>on"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rose<PERSON>_Stone"}]}, {"year": "1825", "text": "The world's first public railway to use steam locomotives, the Stockton and Darlington Railway, is ceremonially opened with the engine Locomotion pulling wagons with coal and passengers from Shildon to Darlington to Stockton.", "html": "1825 - The world's first public railway to use steam locomotives, the <a href=\"https://wikipedia.org/wiki/Stockton_and_Darlington_Railway\" title=\"Stockton and Darlington Railway\">Stockton and Darlington Railway</a>, is ceremonially opened with the engine <a href=\"https://wikipedia.org/wiki/Locomotion_No._1\" title=\"Locomotion No. 1\">Locomotion</a> pulling wagons with coal and passengers from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Darlington\" title=\"Darlington\">Darlington</a> to <a href=\"https://wikipedia.org/wiki/Stockton-on-Tees\" title=\"Stockton-on-Tees\">Stockton</a>.", "no_year_html": "The world's first public railway to use steam locomotives, the <a href=\"https://wikipedia.org/wiki/Stockton_and_Darlington_Railway\" title=\"Stockton and Darlington Railway\">Stockton and Darlington Railway</a>, is ceremonially opened with the engine <a href=\"https://wikipedia.org/wiki/Locomotion_No._1\" title=\"Locomotion No. 1\">Locomotion</a> pulling wagons with coal and passengers from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> to <a href=\"https://wikipedia.org/wiki/Darlington\" title=\"Darlington\">Darlington</a> to <a href=\"https://wikipedia.org/wiki/Stockton-on-Tees\" title=\"Stockton-on-Tees\">Stockton</a>.", "links": [{"title": "Stockton and Darlington Railway", "link": "https://wikipedia.org/wiki/Stockton_and_Darlington_Railway"}, {"title": "Locomotion No. 1", "link": "https://wikipedia.org/wiki/Locomotion_No._1"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Darlington", "link": "https://wikipedia.org/wiki/Darlington"}, {"title": "Stockton-on-Tees", "link": "https://wikipedia.org/wiki/Stockton-on-Tees"}]}, {"year": "1854", "text": "The paddle steamer SS Arctic, owned by the Collins Line of New York, sinks off the coast of Newfoundland, following a collision with a smaller vessel, the SS Vesta. Only 88 of over 300 people on board survive. About a dozen of the occupants of the Vesta are killed when their lifeboat is hit by the Arctic.", "html": "1854 - The paddle steamer <a href=\"https://wikipedia.org/wiki/SS_Arctic\" title=\"SS Arctic\">SS <i>Arctic</i></a>, owned by the Collins Line of New York, <a href=\"https://wikipedia.org/wiki/SS_Arctic_disaster\" title=\"SS Arctic disaster\">sinks</a> off the coast of Newfoundland, following a collision with a smaller vessel, the <i><a href=\"https://wikipedia.org/wiki/SS_Vesta\" title=\"SS Vesta\">SS Vesta</a></i>. Only 88 of over 300 people on board survive. About a dozen of the occupants of the <i>Vesta</i> are killed when their lifeboat is hit by the <i>Arctic</i>.", "no_year_html": "The paddle steamer <a href=\"https://wikipedia.org/wiki/SS_Arctic\" title=\"SS Arctic\">SS <i>Arctic</i></a>, owned by the Collins Line of New York, <a href=\"https://wikipedia.org/wiki/SS_Arctic_disaster\" title=\"SS Arctic disaster\">sinks</a> off the coast of Newfoundland, following a collision with a smaller vessel, the <i><a href=\"https://wikipedia.org/wiki/SS_Vesta\" title=\"SS Vesta\">SS Vesta</a></i>. Only 88 of over 300 people on board survive. About a dozen of the occupants of the <i>Vesta</i> are killed when their lifeboat is hit by the <i>Arctic</i>.", "links": [{"title": "SS Arctic", "link": "https://wikipedia.org/wiki/SS_Arctic"}, {"title": "SS Arctic disaster", "link": "https://wikipedia.org/wiki/SS_Arctic_disaster"}, {"title": "SS Vesta", "link": "https://wikipedia.org/wiki/SS_Vesta"}]}, {"year": "1875", "text": "The merchant sailing ship <PERSON> is wrecked in a storm at Liverpool.", "html": "1875 - The merchant sailing ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> is wrecked in a storm at Liverpool.", "no_year_html": "The merchant sailing ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> is wrecked in a storm at Liverpool.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "\"Wreck of the Old 97\": an American rail disaster, in which 11 people are killed; it later becomes the subject of a popular ballad.", "html": "1903 - \"<a href=\"https://wikipedia.org/wiki/Wreck_of_the_Old_97\" title=\"Wreck of the Old 97\">Wreck of the Old 97</a>\": an American rail disaster, in which 11 people are killed; it later becomes the subject of a popular ballad.", "no_year_html": "\"<a href=\"https://wikipedia.org/wiki/Wreck_of_the_Old_97\" title=\"Wreck of the Old 97\">Wreck of the Old 97</a>\": an American rail disaster, in which 11 people are killed; it later becomes the subject of a popular ballad.", "links": [{"title": "Wreck of the Old 97", "link": "https://wikipedia.org/wiki/Wreck_of_the_Old_97"}]}, {"year": "1908", "text": "Production of the Model T automobile begins at the Ford Piquette Avenue Plant in Detroit.", "html": "1908 - Production of the Model T automobile begins at the <a href=\"https://wikipedia.org/wiki/Ford_Piquette_Avenue_Plant\" title=\"Ford Piquette Avenue Plant\">Ford Piquette Avenue Plant</a> in Detroit.", "no_year_html": "Production of the Model T automobile begins at the <a href=\"https://wikipedia.org/wiki/Ford_Piquette_Avenue_Plant\" title=\"Ford Piquette Avenue Plant\">Ford Piquette Avenue Plant</a> in Detroit.", "links": [{"title": "Ford Piquette Avenue Plant", "link": "https://wikipedia.org/wiki/Ford_Piquette_Avenue_Plant"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON> is proclaimed deposed as ruler of Ethiopia in a palace coup in favor of his aunt <PERSON><PERSON><PERSON><PERSON>.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Iyasu_V\" class=\"mw-redirect\" title=\"Iyasu V\"><PERSON>yas<PERSON> V</a> is proclaimed deposed as ruler of Ethiopia in a palace coup in favor of his aunt <a href=\"https://wikipedia.org/wiki/Zewditu\" title=\"Zewditu\">Zewditu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iyasu_V\" class=\"mw-redirect\" title=\"Iyasu V\"><PERSON><PERSON><PERSON> V</a> is proclaimed deposed as ruler of Ethiopia in a palace coup in favor of his aunt <a href=\"https://wikipedia.org/wiki/Zewditu\" title=\"Zewditu\">Zewditu</a>.", "links": [{"title": "Iyasu V", "link": "https://wikipedia.org/wiki/Iyasu_V"}, {"title": "Zewditu", "link": "https://wikipedia.org/wiki/Zewditu"}]}, {"year": "1922", "text": "King <PERSON> of Greece abdicates his throne in favor of his eldest son, <PERSON>.", "html": "1922 - King <a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"<PERSON> I of Greece\"><PERSON> I of Greece</a> abdicates his throne in favor of his eldest son, <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Greece\" title=\"<PERSON> II of Greece\"><PERSON> II</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Constantine_I_of_Greece\" title=\"<PERSON> I of Greece\"><PERSON> of Greece</a> abdicates his throne in favor of his eldest son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> II of Greece\"><PERSON> II</a>.", "links": [{"title": "Constantine I of Greece", "link": "https://wikipedia.org/wiki/Constantine_I_of_Greece"}, {"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Greece"}]}, {"year": "1928", "text": "The Republic of China is recognized by the United States.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> is recognized by the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> is recognized by the United States.", "links": [{"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}]}, {"year": "1930", "text": "<PERSON> wins the (pre-Masters) Grand Slam of golf.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a> wins the (pre-Masters) <a href=\"https://wikipedia.org/wiki/Grand_Slam_(golf)\" title=\"Grand Slam (golf)\">Grand Slam of golf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a> wins the (pre-Masters) <a href=\"https://wikipedia.org/wiki/Grand_Slam_(golf)\" title=\"Grand Slam (golf)\">Grand Slam of golf</a>.", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}, {"title": "Grand Slam (golf)", "link": "https://wikipedia.org/wiki/Grand_Slam_(golf)"}]}, {"year": "1938", "text": "The ocean liner Queen Elizabeth is launched in Glasgow.", "html": "1938 - The ocean liner <i><a href=\"https://wikipedia.org/wiki/RMS_Queen_<PERSON>\" title=\"RMS Queen <PERSON>\">Queen <PERSON></a></i> is launched in Glasgow.", "no_year_html": "The ocean liner <i><a href=\"https://wikipedia.org/wiki/RMS_Queen_<PERSON>\" title=\"RMS Queen <PERSON>\">Queen <PERSON></a></i> is launched in Glasgow.", "links": [{"title": "RMS Queen <PERSON>", "link": "https://wikipedia.org/wiki/RMS_Queen_<PERSON>"}]}, {"year": "1940", "text": "World War II: The Tripartite Pact is signed in Berlin by Germany, Japan and Italy.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Tripartite_Pact\" title=\"Tripartite Pact\">Tripartite Pact</a> is signed in Berlin by Germany, Japan and Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Tripartite_Pact\" title=\"Tripartite Pact\">Tripartite Pact</a> is signed in Berlin by Germany, Japan and Italy.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Tripartite Pact", "link": "https://wikipedia.org/wiki/Tripartite_Pact"}]}, {"year": "1941", "text": "World War II: The Greek National Liberation Front is established with <PERSON><PERSON> as acting leader.", "html": "1941 - World War II: The Greek National Liberation Front is established with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as acting leader.", "no_year_html": "World War II: The Greek National Liberation Front is established with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as acting leader.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Siantos"}]}, {"year": "1941", "text": "The SS Patrick Henry is launched, becoming the first of more than 2,700 Liberty ships.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/SS_<PERSON>_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> is launched, becoming the first of more than 2,700 Liberty ships.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SS_<PERSON>_<PERSON>\" title=\"SS <PERSON>\">SS <i><PERSON></i></a> is launched, becoming the first of more than 2,700 Liberty ships.", "links": [{"title": "SS <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: Last day of the Matanikau action on Guadalcanal as United States Marines barely escape after being surrounded by Japanese forces.", "html": "1942 - World War II: Last day of the <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">Matanikau action</a> on Guadalcanal as United States Marines barely escape after being surrounded by Japanese forces.", "no_year_html": "World War II: Last day of the <a href=\"https://wikipedia.org/wiki/Actions_along_the_Matanikau\" title=\"Actions along the Matanikau\">Matanikau action</a> on Guadalcanal as United States Marines barely escape after being surrounded by Japanese forces.", "links": [{"title": "Actions along the Matanikau", "link": "https://wikipedia.org/wiki/Actions_along_the_Matanikau"}]}, {"year": "1944", "text": "World War II: The Kassel Mission results in the largest loss by a USAAF group on any mission during the war.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Kassel_Mission\" title=\"Kassel Mission\">Kassel Mission</a> results in the largest loss by a USAAF group on any mission during the war.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Kassel_Mission\" title=\"Kassel Mission\">Kassel Mission</a> results in the largest loss by a USAAF group on any mission during the war.", "links": [{"title": "Kassel Mission", "link": "https://wikipedia.org/wiki/Kassel_Mission"}]}, {"year": "1949", "text": "<PERSON><PERSON>'s design is chosen as the flag of the People's Republic of China.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Zeng_<PERSON>nsong\" title=\"Zeng Liansong\"><PERSON><PERSON>nsong</a>'s design is chosen as the flag of the People's Republic of China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeng_<PERSON>nsong\" title=\"Zeng Liansong\"><PERSON><PERSON>nsong</a>'s design is chosen as the flag of the People's Republic of China.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeng_<PERSON>nsong"}]}, {"year": "1956", "text": "USAF Captain <PERSON><PERSON><PERSON> becomes the first person to exceed Mach 3. Shortly thereafter, the Bell X-2 goes out of control and Captain <PERSON> is killed.", "html": "1956 - USAF Captain <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G._Apt\" title=\"Milburn G. Apt\"><PERSON><PERSON><PERSON> G. Apt</a> becomes the first person to exceed Mach 3. Shortly thereafter, the <a href=\"https://wikipedia.org/wiki/Bell_X-2\" title=\"Bell X-2\">Bell X-2</a> goes out of control and Captain <PERSON> is killed.", "no_year_html": "USAF Captain <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G._Apt\" title=\"Milburn G. Apt\"><PERSON><PERSON><PERSON> G. Apt</a> becomes the first person to exceed Mach 3. Shortly thereafter, the <a href=\"https://wikipedia.org/wiki/Bell_X-2\" title=\"Bell X-2\">Bell X-2</a> goes out of control and Captain <PERSON> is killed.", "links": [{"title": "Milburn G. Apt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G._Apt"}, {"title": "Bell X-2", "link": "https://wikipedia.org/wiki/Bell_X-2"}]}, {"year": "1959", "text": "Typhoon <PERSON> kills nearly 5,000 people in Japan.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Typhoon_Vera\" title=\"Typhoon Vera\">Typhoon <PERSON></a> kills nearly 5,000 people in Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Vera\" title=\"Typhoon Vera\">Typhoon <PERSON></a> kills nearly 5,000 people in Japan.", "links": [{"title": "Typhoon Vera", "link": "https://wikipedia.org/wiki/Typhoon_Vera"}]}, {"year": "1962", "text": "The Yemen Arab Republic is established.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Yemen_Arab_Republic\" title=\"Yemen Arab Republic\">Yemen Arab Republic</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Yemen_Arab_Republic\" title=\"Yemen Arab Republic\">Yemen Arab Republic</a> is established.", "links": [{"title": "Yemen Arab Republic", "link": "https://wikipedia.org/wiki/Yemen_Arab_Republic"}]}, {"year": "1962", "text": "<PERSON>'s book Silent Spring is published, inspiring an environmental movement and the creation of the U.S. Environmental Protection Agency.", "html": "1962 - <PERSON>'s book <i><a href=\"https://wikipedia.org/wiki/Silent_Spring\" title=\"Silent Spring\">Silent Spring</a></i> is published, inspiring an environmental movement and the creation of the U.S. Environmental Protection Agency.", "no_year_html": "<PERSON>'s book <i><a href=\"https://wikipedia.org/wiki/Silent_Spring\" title=\"Silent Spring\">Silent Spring</a></i> is published, inspiring an environmental movement and the creation of the U.S. Environmental Protection Agency.", "links": [{"title": "Silent Spring", "link": "https://wikipedia.org/wiki/Silent_Spring"}]}, {"year": "1964", "text": "The British TSR-2 aircraft XR219 makes its maiden flight.", "html": "1964 - The British <a href=\"https://wikipedia.org/wiki/BAC_TSR-2\" title=\"BAC TSR-2\">TSR-2</a> aircraft XR219 makes its maiden flight.", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/BAC_TSR-2\" title=\"BAC TSR-2\">TSR-2</a> aircraft XR219 makes its maiden flight.", "links": [{"title": "BAC TSR-2", "link": "https://wikipedia.org/wiki/BAC_TSR-2"}]}, {"year": "1973", "text": "Texas International Airlines Flight 655 crashes into the Black Fork Mountain Wilderness near Mena, Arkansas, killing all 11 people on board.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Texas_International_Airlines_Flight_655\" title=\"Texas International Airlines Flight 655\">Texas International Airlines Flight 655</a> crashes into the <a href=\"https://wikipedia.org/wiki/Black_Fork_Mountain_Wilderness\" title=\"Black Fork Mountain Wilderness\">Black Fork Mountain Wilderness</a> near <a href=\"https://wikipedia.org/wiki/Mena,_Arkansas\" title=\"Mena, Arkansas\">Mena, Arkansas</a>, killing all 11 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_International_Airlines_Flight_655\" title=\"Texas International Airlines Flight 655\">Texas International Airlines Flight 655</a> crashes into the <a href=\"https://wikipedia.org/wiki/Black_Fork_Mountain_Wilderness\" title=\"Black Fork Mountain Wilderness\">Black Fork Mountain Wilderness</a> near <a href=\"https://wikipedia.org/wiki/Mena,_Arkansas\" title=\"Mena, Arkansas\">Mena, Arkansas</a>, killing all 11 people on board.", "links": [{"title": "Texas International Airlines Flight 655", "link": "https://wikipedia.org/wiki/Texas_International_Airlines_Flight_655"}, {"title": "Black Fork Mountain Wilderness", "link": "https://wikipedia.org/wiki/Black_Fork_Mountain_Wilderness"}, {"title": "Mena, Arkansas", "link": "https://wikipedia.org/wiki/Mena,_Arkansas"}]}, {"year": "1975", "text": "The last use of capital punishment in Spain sparks worldwide protests.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Last_use_of_capital_punishment_in_Spain\" title=\"Last use of capital punishment in Spain\">last use of capital punishment in Spain</a> sparks worldwide protests.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Last_use_of_capital_punishment_in_Spain\" title=\"Last use of capital punishment in Spain\">last use of capital punishment in Spain</a> sparks worldwide protests.", "links": [{"title": "Last use of capital punishment in Spain", "link": "https://wikipedia.org/wiki/Last_use_of_capital_punishment_in_Spain"}]}, {"year": "1977", "text": "Japan Airlines Flight 715 crashes on approach to Sultan <PERSON> Airport in Subang, Malaysia, killing 34 of the 79 people on board.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_715\" class=\"mw-redirect\" title=\"Japan Airlines Flight 715\">Japan Airlines Flight 715</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Airport\" title=\"Sultan <PERSON> Airport\">Sultan <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/Subang,_Selangor\" title=\"Subang, Selangor\">Subang</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, killing 34 of the 79 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_715\" class=\"mw-redirect\" title=\"Japan Airlines Flight 715\">Japan Airlines Flight 715</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Airport\" title=\"Sultan <PERSON> Airport\">Sultan <PERSON> Airport</a> in <a href=\"https://wikipedia.org/wiki/Subang,_Selangor\" title=\"Subang, Selangor\">Subang</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, killing 34 of the 79 people on board.", "links": [{"title": "Japan Airlines Flight 715", "link": "https://wikipedia.org/wiki/Japan_Airlines_Flight_715"}, {"title": "<PERSON> Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Shah_Airport"}, {"title": "Subang, Selangor", "link": "https://wikipedia.org/wiki/Subang,_Selangor"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "1988", "text": "The National League for Democracy is formed by <PERSON><PERSON> and others to fight dictatorship in Myanmar.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a> is formed by <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> and others to fight dictatorship in Myanmar.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_League_for_Democracy\" title=\"National League for Democracy\">National League for Democracy</a> is formed by <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> and others to fight dictatorship in Myanmar.", "links": [{"title": "National League for Democracy", "link": "https://wikipedia.org/wiki/National_League_for_Democracy"}, {"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "1993", "text": "The Sukhumi massacre takes place in Abkhazia.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Sukhumi_massacre\" class=\"mw-redirect\" title=\"Sukhumi massacre\">Sukhumi massacre</a> takes place in Abkhazia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sukhumi_massacre\" class=\"mw-redirect\" title=\"Sukhumi massacre\">Sukhumi massacre</a> takes place in Abkhazia.", "links": [{"title": "Sukhumi massacre", "link": "https://wikipedia.org/wiki/<PERSON>kh<PERSON>_massacre"}]}, {"year": "1996", "text": "The Battle of Kabul ends in a Taliban victory; an Islamic Emirate of Afghanistan is established.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Kabul_(1992%E2%80%931996)\" title=\"Battle of Kabul (1992-1996)\">Battle of Kabul</a> ends in a Taliban victory; an <a href=\"https://wikipedia.org/wiki/Islamic_Emirate_of_Afghanistan_(1996%E2%80%932001)\" title=\"Islamic Emirate of Afghanistan (1996-2001)\">Islamic Emirate of Afghanistan</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Kabul_(1992%E2%80%931996)\" title=\"Battle of Kabul (1992-1996)\">Battle of Kabul</a> ends in a Taliban victory; an <a href=\"https://wikipedia.org/wiki/Islamic_Emirate_of_Afghanistan_(1996%E2%80%932001)\" title=\"Islamic Emirate of Afghanistan (1996-2001)\">Islamic Emirate of Afghanistan</a> is established.", "links": [{"title": "Battle of Kabul (1992-1996)", "link": "https://wikipedia.org/wiki/Battle_of_Kabul_(1992%E2%80%931996)"}, {"title": "Islamic Emirate of Afghanistan (1996-2001)", "link": "https://wikipedia.org/wiki/Islamic_Emirate_of_Afghanistan_(1996%E2%80%932001)"}]}, {"year": "1996", "text": "Confusion on a tanker ship results in the Julie N. oil spill in Portland, Maine.", "html": "1996 - Confusion on a tanker ship results in the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_oil_spill\" title=\"Julie <PERSON> oil spill\">Julie <PERSON> oil spill</a> in Portland, Maine.", "no_year_html": "Confusion on a tanker ship results in the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_oil_spill\" title=\"Julie <PERSON> oil spill\">Julie <PERSON> oil spill</a> in Portland, Maine.", "links": [{"title": "Julie <PERSON> oil spill", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_oil_spill"}]}, {"year": "1998", "text": "The Google internet search engine retroactively claims this date as its birthday.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a> internet search engine retroactively claims this date as its birthday.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google</a> internet search engine retroactively claims this date as its birthday.", "links": [{"title": "Google", "link": "https://wikipedia.org/wiki/Google"}]}, {"year": "2001", "text": "In Switzerland, a gunman shoots 18 citizens, killing 14 and then himself.", "html": "2001 - In Switzerland, a gunman <a href=\"https://wikipedia.org/wiki/Zug_massacre\" title=\"Zug massacre\">shoots</a> 18 citizens, killing 14 and then himself.", "no_year_html": "In Switzerland, a gunman <a href=\"https://wikipedia.org/wiki/Zug_massacre\" title=\"Zug massacre\">shoots</a> 18 citizens, killing 14 and then himself.", "links": [{"title": "Zug massacre", "link": "https://wikipedia.org/wiki/Zug_massacre"}]}, {"year": "2003", "text": "The SMART-1 satellite is launched.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/SMART-1\" title=\"SMART-1\">SMART-1</a> satellite is launched.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SMART-1\" title=\"SMART-1\">SMART-1</a> satellite is launched.", "links": [{"title": "SMART-1", "link": "https://wikipedia.org/wiki/SMART-1"}]}, {"year": "2007", "text": "NASA launches the Dawn probe to the asteroid belt.", "html": "2007 - NASA launches the <i><a href=\"https://wikipedia.org/wiki/Dawn_(spacecraft)\" title=\"Dawn (spacecraft)\">Dawn</a></i> probe to the asteroid belt.", "no_year_html": "NASA launches the <i><a href=\"https://wikipedia.org/wiki/Dawn_(spacecraft)\" title=\"Dawn (spacecraft)\">Dawn</a></i> probe to the asteroid belt.", "links": [{"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/Dawn_(spacecraft)"}]}, {"year": "2008", "text": "CNSA astronaut <PERSON><PERSON> becomes the first Chinese person to perform a spacewalk.", "html": "2008 - CNSA astronaut <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gan<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first Chinese person to perform a spacewalk.", "no_year_html": "CNSA astronaut <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gan<PERSON>\" title=\"<PERSON><PERSON>gan<PERSON>\"><PERSON><PERSON></a> becomes the first Chinese person to perform a spacewalk.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gang"}]}, {"year": "2012", "text": "In Minneapolis, a gunman shoots seven citizens, killing five and then himself.", "html": "2012 - In Minneapolis, a gunman <a href=\"https://wikipedia.org/wiki/Minneapolis_firm_shooting\" title=\"Minneapolis firm shooting\">shoots seven citizens</a>, killing five and then himself.", "no_year_html": "In Minneapolis, a gunman <a href=\"https://wikipedia.org/wiki/Minneapolis_firm_shooting\" title=\"Minneapolis firm shooting\">shoots seven citizens</a>, killing five and then himself.", "links": [{"title": "Minneapolis firm shooting", "link": "https://wikipedia.org/wiki/Minneapolis_firm_shooting"}]}, {"year": "2014", "text": "The eruption of Mount Ontake in Japan occurs.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/2014_eruption_of_Mount_Ontake\" class=\"mw-redirect\" title=\"2014 eruption of Mount Ontake\">eruption of Mount Ontake</a> in Japan occurs.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2014_eruption_of_Mount_Ontake\" class=\"mw-redirect\" title=\"2014 eruption of Mount Ontake\">eruption of Mount Ontake</a> in Japan occurs.", "links": [{"title": "2014 eruption of Mount Ontake", "link": "https://wikipedia.org/wiki/2014_eruption_of_Mount_Ontake"}]}, {"year": "2019", "text": "Over two million people participated in worldwide strikes to protest climate change across 2,400 locations worldwide.", "html": "2019 - Over two million people participated in <a href=\"https://wikipedia.org/wiki/September_2019_climate_strikes\" title=\"September 2019 climate strikes\">worldwide strikes to protest climate change</a> across 2,400 locations worldwide.", "no_year_html": "Over two million people participated in <a href=\"https://wikipedia.org/wiki/September_2019_climate_strikes\" title=\"September 2019 climate strikes\">worldwide strikes to protest climate change</a> across 2,400 locations worldwide.", "links": [{"title": "September 2019 climate strikes", "link": "https://wikipedia.org/wiki/September_2019_climate_strikes"}]}, {"year": "2020", "text": "Second Nagorno-Karabakh War: Azerbaijan launched an offensive against the self-proclaimed Republic of Artsakh, inhabited predominantly by ethnic Armenians.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> launched an offensive against the self-proclaimed <a href=\"https://wikipedia.org/wiki/Republic_of_Artsakh\" title=\"Republic of Artsakh\">Republic of Artsakh</a>, inhabited predominantly by <a href=\"https://wikipedia.org/wiki/Ethnic_Armenians\" class=\"mw-redirect\" title=\"Ethnic Armenians\">ethnic Armenians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> launched an offensive against the self-proclaimed <a href=\"https://wikipedia.org/wiki/Republic_of_Artsakh\" title=\"Republic of Artsakh\">Republic of Artsakh</a>, inhabited predominantly by <a href=\"https://wikipedia.org/wiki/Ethnic_Armenians\" class=\"mw-redirect\" title=\"Ethnic Armenians\">ethnic Armenians</a>.", "links": [{"title": "Second Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Republic of Artsakh", "link": "https://wikipedia.org/wiki/Republic_of_Artsakh"}, {"title": "Ethnic Armenians", "link": "https://wikipedia.org/wiki/Ethnic_Armenians"}]}], "Births": [{"year": "808", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (d. 850)", "html": "808 - <a href=\"https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 850)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D"}]}, {"year": "830", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans, Queen of the Franks (probable year; d. 869)", "html": "830 - <a href=\"https://wikipedia.org/wiki/Erment<PERSON>e_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans</a>, Queen of the Franks (probable year; d. 869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erment<PERSON><PERSON>_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans</a>, Queen of the Franks (probable year; d. 869)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Orléans", "link": "https://wikipedia.org/wiki/Ermentrude_of_Orl%C3%A9ans"}]}, {"year": "1271", "text": "<PERSON><PERSON><PERSON> of Bohemia, King of Bohemia and Poland (d. 1305)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/Wen<PERSON>laus_II_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> II of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a>, King of Bohemia and Poland (d. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wen<PERSON>laus_II_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> II of Bohemia\"><PERSON><PERSON><PERSON> of Bohemia</a>, King of Bohemia and Poland (d. 1305)", "links": [{"title": "<PERSON><PERSON><PERSON> II of Bohemia", "link": "https://wikipedia.org/wiki/Wenceslaus_II_of_Bohemia"}]}, {"year": "1275", "text": "<PERSON> <PERSON>, Duke of Brabant (d. 1312)", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (d. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brabant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a> (d. 1312)", "links": [{"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1300", "text": "<PERSON>, Count <PERSON><PERSON> of the Rhine (d. 1327)", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine\" title=\"<PERSON>, Count <PERSON> of the Rhine\"><PERSON>, Count <PERSON> of the Rhine</a> (d. 1327)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine\" title=\"<PERSON>, Count <PERSON> of the Rhine\"><PERSON>, Count <PERSON><PERSON> of the Rhine</a> (d. 1327)", "links": [{"title": "<PERSON>, Count <PERSON> of the Rhine", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON><PERSON>_of_the_Rhine"}]}, {"year": "1389", "text": "<PERSON><PERSON><PERSON>, ruler of Florence (d. 1464)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, ruler of Florence (d. 1464)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, ruler of Florence (d. 1464)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cosimo_de%27_Medici"}]}, {"year": "1433", "text": "<PERSON><PERSON>, Polish canon regular and saint (d. 1489)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Ka<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish canon regular and saint (d. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Ka<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish canon regular and saint (d. 1489)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Ka<PERSON><PERSON><PERSON><PERSON>k"}]}, {"year": "1442", "text": "<PERSON>, 2nd Duke of Suffolk (d. 1491)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Duke_of_Suffolk\" title=\"<PERSON>, 2nd Duke of Suffolk\"><PERSON>, 2nd Duke of Suffolk</a> (d. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Duke_of_Suffolk\" title=\"<PERSON>, 2nd Duke of Suffolk\"><PERSON>, 2nd Duke of Suffolk</a> (d. 1491)", "links": [{"title": "<PERSON>, 2nd Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Duke_of_Suffolk"}]}, {"year": "1496", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish diplomat (d. 1542)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/Hieron<PERSON>us_%C5%81aski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish diplomat (d. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hieron<PERSON><PERSON>_%C5%81aski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish diplomat (d. 1542)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hieronymus_%C5%81aski"}]}, {"year": "1507", "text": "<PERSON>, French physician (d. 1566)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician (d. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON>, King of Poland (d. 1586)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, King of Poland (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, King of Poland (d. 1586)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1544", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (d. 1579)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Take<PERSON><PERSON>_Shi<PERSON>haru\" title=\"<PERSON><PERSON><PERSON> Shi<PERSON>har<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>haru\" title=\"<PERSON><PERSON><PERSON> Shigeharu\"><PERSON><PERSON><PERSON></a>, Japanese samurai (d. 1579)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takena<PERSON>_<PERSON>geharu"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian playwright and stage actor (d. 1624)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and stage actor (d. 1624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and stage actor (d. 1624)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flaminio_Scala"}]}, {"year": "1598", "text": "<PERSON>, English admiral (d. 1657)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>(admiral)\" title=\"<PERSON> (admiral)\"><PERSON></a>, English admiral (d. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(admiral)\" title=\"<PERSON> (admiral)\"><PERSON></a>, English admiral (d. 1657)", "links": [{"title": "<PERSON> (admiral)", "link": "https://wikipedia.org/wiki/<PERSON>(admiral)"}]}, {"year": "1601", "text": "<PERSON> of France (d. 1643)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/Louis_XIII_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XIII_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1643)", "links": [{"title": "Louis <PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIII_of_France"}]}, {"year": "1627", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French bishop and theologian (d. 1704)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop and theologian (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French bishop and theologian (d. 1704)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jacques-B%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, American pastor and librarian (d. 1729)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/Solomon_Stoddard\" title=\"<PERSON> Stoddard\"><PERSON></a>, American pastor and librarian (d. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_Stoddard\" title=\"Solomon Stoddard\"><PERSON></a>, American pastor and librarian (d. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_Stoddard"}]}, {"year": "1657", "text": "<PERSON> of Russia (d. 1704)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/Sofia_Alekseyevna_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofia_Alekseyevna_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1704)", "links": [{"title": "<PERSON> Alekseyevna of Russia", "link": "https://wikipedia.org/wiki/Sofia_Alekseyevna_of_Russia"}]}, {"year": "1677", "text": "<PERSON>, Italian violinist and composer (d. 1754)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Giovanni Carlo <PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Giovanni Carlo <PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1754)", "links": [{"title": "Giovanni <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1696", "text": "<PERSON><PERSON><PERSON>, Italian bishop and saint (d. 1787)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_Liguori\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_Li<PERSON>ori\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (d. 1787)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON><PERSON><PERSON>"}]}, {"year": "1719", "text": "<PERSON>, German mathematician and epigrammatist (d. 1800)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German mathematician and epigrammatist (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German mathematician and epigrammatist (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4stner"}]}, {"year": "1722", "text": "<PERSON>, American philosopher and politician, fourth Governor of Massachusetts (d. 1803)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and politician, fourth <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and politician, fourth <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1729", "text": "<PERSON>, Austrian lepidopterist, author, and poet (d. 1800)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lepidopterist, author, and poet (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lepidopterist, author, and poet (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON>, Marquess of Tavistock, Irish politician (d. 1767)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_Tavistock\" title=\"<PERSON>, Marquess of Tavistock\"><PERSON>, Marquess of Tavistock</a>, Irish politician (d. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_Tavistock\" title=\"<PERSON>, Marquess of Tavistock\"><PERSON>, Marquess of Tavistock</a>, Irish politician (d. 1767)", "links": [{"title": "<PERSON>, Marquess of Tavistock", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquess_of_<PERSON>"}]}, {"year": "1765", "text": "<PERSON>, French general (d. 1794)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Tr%C3%A9moille\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Tr%C3%A9moille\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Tr%C3%A9moille"}]}, {"year": "1772", "text": "<PERSON>, daughter of <PERSON> who had twelve children (d. 1836)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> who had twelve children (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> who had twelve children (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican royalist turned insurgent; first emperor of Mexico (d. 1824)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"<PERSON><PERSON><PERSON><PERSON> de Iturbide\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican royalist turned insurgent; first emperor of Mexico (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"<PERSON><PERSON><PERSON><PERSON> de Iturbide\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican royalist turned insurgent; first emperor of Mexico (d. 1824)", "links": [{"title": "Agustín de Iturbide", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide"}]}, {"year": "1803", "text": "<PERSON>, American admiral (d. 1865)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, German-English evangelist and missionary, founded the Ashley Down Orphanage (d. 1898)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German-English evangelist and missionary, founded the <a href=\"https://wikipedia.org/wiki/New_Orphan_Houses,_Ashley_Down,_Bristol\" class=\"mw-redirect\" title=\"New Orphan Houses, Ashley Down, Bristol\">Ashley Down Orphanage</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German-English evangelist and missionary, founded the <a href=\"https://wikipedia.org/wiki/New_Orphan_Houses,_Ashley_Down,_Bristol\" class=\"mw-redirect\" title=\"New Orphan Houses, Ashley Down, Bristol\">Ashley Down Orphanage</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_M%C3%BCller"}, {"title": "New Orphan Houses, Ashley Down, Bristol", "link": "https://wikipedia.org/wiki/New_Orphan_Houses,_Ashley_Down,_Bristol"}]}, {"year": "1818", "text": "<PERSON>, German chemist and academic (d. 1884)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss philosopher, poet, and critic (d. 1881)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss philosopher, poet, and critic (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss philosopher, poet, and critic (d. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Henri-<PERSON>%C3%A9d%C3%A9ric_Amiel"}]}, {"year": "1824", "text": "<PERSON> \"<PERSON>\" <PERSON>, American general (d. 1862)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Bull%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American general (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Bull%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American general (d. 1862)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Bull%22_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, American general (d. 1887)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American general and politician, 19th Governor of Texas (d. 1898)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "1840", "text": "<PERSON>, American captain and historian (d. 1914)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and historian (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and historian (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, German-American cartoonist (d. 1902)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American cartoonist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American cartoonist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, Belgian geologist and petrographer (d. 1903)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON>\">Alphon<PERSON></a>, Belgian geologist and petrographer (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Alphon<PERSON>\"><PERSON>phon<PERSON></a>, Belgian geologist and petrographer (d. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%A<PERSON>ois_Renard"}]}, {"year": "1843", "text": "<PERSON>, French mathematician and academic (d. 1913)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, American poet and author (d. 1933)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and author (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American poet and author (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Slovak priest and politician (d. 1938)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest and politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest and politician (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Italian saint (d. 1932)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Eurosia_Fabris\" title=\"Eurosia Fabris\"><PERSON><PERSON>ab<PERSON></a>, Italian saint (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eurosia_Fabris\" title=\"Eurosia Fabris\"><PERSON><PERSON> Fab<PERSON></a>, Italian saint (d. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eurosia_Fabris"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Italian novelist and poet, Nobel Prize laureate (d. 1936)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Grazia_Deledda\" title=\"Grazia Del<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grazia_Del<PERSON>\" title=\"Graz<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grazia_<PERSON>a"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,  Indian legislator and political leader (d. 1933)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian legislator and political leader (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian legislator and political leader (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Austrian mathematician and philosopher (d. 1934)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Austrian mathematician and philosopher (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Austrian mathematician and philosopher (d. 1934)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "1879", "text": "<PERSON>, American hurdler and coach (d. 1962)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, English poet and composer (d. 1970)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and composer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and composer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>-<PERSON>, English figure skater and tennis player (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater and tennis player (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater and tennis player (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Sr., American magician (d. 1965)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American magician (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American magician (d. 1965)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr."}]}, {"year": "1885", "text": "<PERSON>, Canadian businessman and politician (d. 1964)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, English diplomat (d. 1943)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, German lieutenant and pilot (d. 1922)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Richthofen\"><PERSON><PERSON></a>, German lieutenant and pilot (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Richthofen\"><PERSON><PERSON></a>, German lieutenant and pilot (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English cricketer (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American soldier and politician (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American composer and producer (d. 1946)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Slovenian poet and politician (d. 1981)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian poet and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian poet and politician (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German footballer and manager (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English poet and critic (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American author and screenwriter (d. 1977)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and screenwriter (d. 1977)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1906", "text": "<PERSON>, Russian art collector and author (d. 1980)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian art collector and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian art collector and author (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English actor, director, producer, and screenwriter (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and screenwriter (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Indian socialist revolutionary (disputed with 28 September) (d. 1931)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian socialist <a href=\"https://wikipedia.org/wiki/Revolutionary\" title=\"Revolutionary\">revolutionary</a> (disputed with 28 September) (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian socialist <a href=\"https://wikipedia.org/wiki/Revolutionary\" title=\"Revolutionary\">revolutionary</a> (disputed with 28 September) (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Revolutionary", "link": "https://wikipedia.org/wiki/Revolutionary"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American-Mexican photographer (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Mexican photographer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Mexican photographer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American psychologist and author (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Israeli academic and politician (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/S._Yizhar\" title=\"S. Yizhar\"><PERSON><PERSON></a>, Israeli academic and politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Yizhar\" title=\"S. <PERSON>\"><PERSON><PERSON></a>, Israeli academic and politician (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._Yi<PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American novelist and essayist (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and essayist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American magician and actor (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actor and producer (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American microbiologist (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English astronomer and author, Nobel Prize laureate (d. 1984)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1918", "text": "<PERSON>, 2nd Baron <PERSON> (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON></a>, 2nd Baron <PERSON> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON></a>, 2nd <PERSON> (d. 2001)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Soviet military leader (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet military leader (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet military leader (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actress and author (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American lieutenant and politician (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American mathematician and computer scientist (d. 1986)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor, director, and producer (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian director and screenwriter (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian director and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian director and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Jancs%C3%B3"}]}, {"year": "1921", "text": "<PERSON>, American screenwriter and producer, co-founded Amicus Productions (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Amicus_Productions\" title=\"Amicus Productions\">Amicus Productions</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Amicus_Productions\" title=\"Amicus Productions\">Amicus Productions</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amicus Productions", "link": "https://wikipedia.org/wiki/Amicus_Productions"}]}, {"year": "1921", "text": "<PERSON>, American author and illustrator (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American pianist and bandleader (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American director and producer (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Penn\"><PERSON></a>, American director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Penn\"><PERSON></a>, American director and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_Penn"}]}, {"year": "1924", "text": "<PERSON>, American-Canadian anthropologist, author, and academic (d. 1974)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian anthropologist, author, and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian anthropologist, author, and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American pianist and composer (d. 1966)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Austrian-American physicist and academic (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Czech-Canadian author and publisher (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian author and publisher (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian author and publisher (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD"}]}, {"year": "1925", "text": "<PERSON>, English physiologist and academic, Nobel Prize laureate (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, English physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, English physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON> (physiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physiologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1925", "text": "<PERSON>, American author (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Canadian businessman and philanthropist (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "Chrysostomos I of Cyprus (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Chrysostomos_I_of_Cyprus\" title=\"Chrysostomos I of Cyprus\">Chrysostomos I of Cyprus</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chrysostomos_I_of_Cyprus\" title=\"Chrysostomos I of Cyprus\">Chrysostomos I of Cyprus</a> (d. 2007)", "links": [{"title": "Chrysostomos I of Cyprus", "link": "https://wikipedia.org/wiki/Chrysostomos_I_of_Cyprus"}]}, {"year": "1927", "text": "<PERSON>, American trumpet player (d. 1994)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rodney\"><PERSON></a>, American trumpet player (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Italian author and illustrator (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American actress (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English archaeologist and historian (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret Rule\"><PERSON></a>, English archaeologist and historian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Margaret Rule\"><PERSON></a>, English archaeologist and historian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American pianist, composer, and educator (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist, composer, and educator (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist, composer, and educator (d. 2004)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1929", "text": "<PERSON>, Estonian race walker (d. 1995)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian race walker (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian race walker (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English actress (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Austrian-Canadian businessman, founded Olympia and York (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Olympia_and_York\" title=\"Olympia and York\">Olympia and York</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Olympia_and_York\" title=\"Olympia and York\">Olympia and York</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Olympia and York", "link": "https://wikipedia.org/wiki/Olympia_and_York"}]}, {"year": "1931", "text": "<PERSON>, Austrian singer, guitarist, and actor", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian singer, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian singer, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English footballer (d. 1958)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English captain and politician (d. 2000)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1932", "text": "<PERSON>, American geophysicist", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Danish-English physicist and neuroscientist (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-English physicist and neuroscientist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-English physicist and neuroscientist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actor (d. 1996)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, American actor (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>ley\" title=\"<PERSON><PERSON><PERSON> Brimley\"><PERSON><PERSON><PERSON></a>, American actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON> Brimley\"><PERSON><PERSON><PERSON></a>, American actor (d. 2020)", "links": [{"title": "Wilford Brimley", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>rimley"}]}, {"year": "1934", "text": "<PERSON>, Jr., American actor and producer (d. 2025)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and producer (d. 2025)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1934", "text": "<PERSON>, American sportscaster and author (d. 2001)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American television host and producer (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English actor, playwright, and author (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Ukrainian politician and diplomat, eighth Prime Minister of Ukraine", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian politician and diplomat, eighth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ukraine\" title=\"Prime Minister of Ukraine\">Prime Minister of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian politician and diplomat, eighth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Ukraine\" title=\"Prime Minister of Ukraine\">Prime Minister of Ukraine</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Ukraine", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Ukraine"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, French journalist, songwriter, and screenwriter (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist, songwriter, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist, songwriter, and screenwriter (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English interior designer and author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English interior designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English interior designer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American author, poet, and playwright", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American golfer (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English soprano and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Belgian cyclist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1941", "text": "<PERSON>, English footballer and coach (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian lawyer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nard\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nard\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Serge_M%C3%A9nard"}]}, {"year": "1941", "text": "<PERSON>, American saxophonist, songwriter, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Cambodian photographer and journalist (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>th <PERSON>ran\"><PERSON><PERSON></a>, Cambodian photographer and journalist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>th <PERSON>ran\"><PERSON><PERSON></a>, Cambodian photographer and journalist (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dith_<PERSON>ran"}]}, {"year": "1942", "text": "<PERSON>, English singer and actor (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Stardust\"><PERSON></a>, English singer and actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alvin Stardust\"><PERSON></a>, English singer and actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alvin_Stardust"}]}, {"year": "1943", "text": "Prince <PERSON><PERSON><PERSON>, Duke of Aosta (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Aosta_(b._1943)\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Aosta (b. 1943)\">Prince <PERSON><PERSON><PERSON>, Duke of Aosta</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Aosta_(b._1943)\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Aosta (b. 1943)\">Prince <PERSON><PERSON><PERSON>, Duke of Aosta</a> (d. 2021)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Aosta (b. 1943)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>,_Duke_of_Aosta_(b._1943)"}]}, {"year": "1943", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American-born Mexican singer-songwriter and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Ang%C3%A9lica_Mar%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-born Mexican singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ang%C3%A9lica_Mar%C3%ADa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-born Mexican singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ang%C3%A9lica_Mar%C3%ADa"}]}, {"year": "1944", "text": "<PERSON>, American baseball player and scout", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian-American painter (d. 2003)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American painter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Cypriot lawyer and politician, seventh President of Cyprus", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot lawyer and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot lawyer and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicos_Anastasiades"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1946", "text": "<PERSON><PERSON> <PERSON><PERSON>, American painter and sculptor (d. 1978)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. C<PERSON> Cannon\"><PERSON><PERSON> <PERSON><PERSON></a>, American painter and sculptor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._C<PERSON>_<PERSON>\" title=\"T. C<PERSON> Cannon\"><PERSON><PERSON> <PERSON><PERSON></a>, American painter and sculptor (d. 1978)", "links": [{"title": "T<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Dutch football manager and former player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch football manager and former player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch football manager and former player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>vocaa<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian politician, 26th Premier of Western Australia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1947", "text": "<PERSON>, Scottish singer-songwriter and actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish actor, director, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON> Loaf, American singer-songwriter, producer, and actor (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Meat_Loaf\" title=\"Meat Loaf\">Meat Loaf</a>, American singer-songwriter, producer, and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meat_Loaf\" title=\"Meat Loaf\">Meat Loaf</a>, American singer-songwriter, producer, and actor (d. 2022)", "links": [{"title": "Meat Loaf", "link": "https://wikipedia.org/wiki/Meat_Loaf"}]}, {"year": "1947", "text": "<PERSON>, Puerto Rican-American actress and comedian", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian actor, director, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Rhodesian-Zimbabwean cricketer and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian-Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian-Zimbabwean cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Australian journalist and politician, 39th Australian Minister for Health", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Australia)\" class=\"mw-redirect\" title=\"Minister for Health (Australia)\">Australian Minister for Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 39th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Australia)\" class=\"mw-redirect\" title=\"Minister for Health (Australia)\">Australian Minister for Health</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Health (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Health_(Australia)"}]}, {"year": "1949", "text": "<PERSON>, American baseball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Norwegian singer-songwriter and guitarist (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter and guitarist (d. 2020)", "links": [{"title": "Jahn <PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>igen"}]}, {"year": "1950", "text": "<PERSON>, Australian writer (d. 2024)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Australian writer (d. 2024)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese-American actor and martial artist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American actor and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American actor and martial artist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian politician, 27th Premier of Western Australia", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1951", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1951", "text": "<PERSON>, American author and illustrator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shooter\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim Shooter\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1952", "text": "<PERSON>, English author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Romanian pilot, engineer and cosmonaut", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian pilot, engineer and cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian pilot, engineer and cosmonaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English journalist and politician, Shadow Secretary of State for International Development", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Shadow Secretary of State for International Development\">Shadow Secretary of State for International Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Shadow Secretary of State for International Development\">Shadow Secretary of State for International Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Secretary of State for International Development", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_International_Development"}]}, {"year": "1953", "text": "<PERSON>, Indian guru and saint", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Mata_Am<PERSON>damay<PERSON>\" title=\"Mata Amritanandamayi\"><PERSON></a>, Indian guru and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mata_Am<PERSON>ayi\" title=\"Mata Amritanandamayi\"><PERSON></a>, Indian guru and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mata_Amritanandamayi"}]}, {"year": "1953", "text": "<PERSON>, Italian footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tile"}]}, {"year": "1953", "text": "<PERSON>, Australian keyboard player, saxophonist and songwriter (d. 2012)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian keyboard player, saxophonist and songwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian keyboard player, saxophonist and songwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian radio host and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio host and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio host and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Russian violinist and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American computer programmer and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish footballer and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English cricketer, footballer, and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, footballer, and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, footballer, and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, singer, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish author and playwright", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Irvine_Welsh\" title=\"Irvine Welsh\">Irvine Welsh</a>, Scottish author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irvine_Welsh\" title=\"Irvine Welsh\">Irvine Welsh</a>, Scottish author and playwright", "links": [{"title": "Irvine Welsh", "link": "https://wikipedia.org/wiki/Irvine_Welsh"}]}, {"year": "1959", "text": "<PERSON>, American speed skater and cyclist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater and cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, German-American actor, director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, New Zealand cricketer and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American comedian, actor, and radio host", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Serbian footballer (d. 2012)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Predrag_Brzakovi%C4%87\" title=\"Predra<PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Predrag_Brzakovi%C4%87\" title=\"Predra<PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Predrag_Brzakovi%C4%87"}]}, {"year": "1964", "text": "<PERSON>, American computer scientist and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tracy Camp\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tracy Camp\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "Tracy Camp", "link": "https://wikipedia.org/wiki/Tracy_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, South African boxer (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player, coach and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian lawyer and politician, 30th Premier of New Brunswick", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1965", "text": "<PERSON>, Canadian lawyer and politician, 50th Canadian Minister of Justice", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1965", "text": "<PERSON>, American radio and television host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American engineer and astronaut", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Italian singer-songwriter and rapper", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON></a>, Italian singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON></a>, Italian singer-songwriter and rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uk<PERSON>\" title=\"<PERSON><PERSON> Okechukwu\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Okechukwu\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ukwu"}]}, {"year": "1968", "text": "<PERSON>, Finnish politician, 41st Prime Minister of Finland", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese chess player and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese chess player and author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Mexican illustrator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian cyclist and speed skater", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist and speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cyclist and speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American actress, blogger, and businesswoman", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Gwyneth_Paltrow\" title=\"Gwyneth Paltrow\"><PERSON><PERSON><PERSON></a>, American actress, blogger, and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gwyneth_Paltrow\" title=\"Gwyneth Paltrow\"><PERSON><PERSON><PERSON></a>, American actress, blogger, and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gwyneth_Paltrow"}]}, {"year": "1972", "text": "<PERSON>, American politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Russian fencer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian fencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, British actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Indira_Varma\" title=\"Indira Varma\">In<PERSON> Varma</a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indira_Varma\" title=\"Indira Varma\">In<PERSON> Varma</a>, British actress", "links": [{"title": "Indira Varma", "link": "https://wikipedia.org/wiki/Indira_Varma"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American video game designer and dancer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(catcher)"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Estonian javelin thrower", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Andrus_V%C3%A4rnik\" title=\"Andrus Värnik\"><PERSON><PERSON></a>, Estonian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andrus_V%C3%A4rnik\" title=\"Andrus Värnik\"><PERSON><PERSON></a>, Estonian javelin thrower", "links": [{"title": "Andrus Värnik", "link": "https://wikipedia.org/wiki/Andrus_V%C3%A4rnik"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Romanian pianist (d. 2012)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian pianist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian pianist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Hungarian actress and model", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6r%C3%B6g\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6r%C3%B6g\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zita_G%C3%B6r%C3%B6g"}]}, {"year": "1979", "text": "<PERSON>, Australian race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian sumo wrestler, the 68th Yokozuna", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Asash%C5%8Dry%C5%AB_Akinori\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler, the 68th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\"><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asash%C5%8Dry%C5%AB_Akinori\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler, the 68th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\">Yoko<PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Asash%C5%8Dry%C5%AB_Akinori"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author and illustrator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, New Zealand cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ji\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anna Camp\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anna Camp\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1982", "text": "<PERSON>, Swedish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American rapper, producer, and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wayne\" title=\"<PERSON> Wayne\"><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wayne\" title=\"<PERSON> Wayne\"><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_White\" title=\"<PERSON> White\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American football player (d. 2007)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, South Korean actress and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ye-bin\" title=\"<PERSON><PERSON> Hye-bin\"><PERSON><PERSON>-<PERSON></a>, South Korean actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ye-bin\" title=\"<PERSON><PERSON> Hye-bin\"><PERSON><PERSON>-<PERSON></a>, South Korean actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-bin"}]}, {"year": "1983", "text": "<PERSON>, American basketball player and coach", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter, actress, and fashion designer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter, actress, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter, actress, and fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Av<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (d. 2011)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Wouter_<PERSON>\" title=\"Wouter <PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wouter_<PERSON>\" title=\"Wout<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Canadian decathlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian decathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Czech footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Ivorian footballer (d. 2014)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Ivorian footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ibrahim_<PERSON>%C3%A9_(footballer,_born_1985)\" title=\"<PERSON> (footballer, born 1985)\"><PERSON></a>, Ivorian footballer (d. 2014)", "links": [{"title": "<PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/Ibrahim_Tour%C3%A9_(footballer,_born_1985)"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Argentinian race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON> American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1m_Bogd%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1m_Bogd%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81d%C3%A1m_Bogd%C3%A1n"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Austin_Carlile\" title=\"Austin Carlile\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Carlile\" title=\"Austin Carlile\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Carlile"}]}, {"year": "1987", "text": "<PERSON>, French figure skater", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Russian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German pole vaulter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South Korean swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, South Korean swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, South Korean swimmer", "links": [{"title": "<PERSON>wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-hwan"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Guinean footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guinean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guinean footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Romanian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Anete <PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Anete <PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anete_Paulus"}]}, {"year": "1991", "text": "<PERSON>, Japanese model and actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Rio_Uchida\" title=\"Rio Uchida\"><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rio_Uchida\" title=\"Rio Uchida\"><PERSON></a>, Japanese model and actress", "links": [{"title": "Rio Uchida", "link": "https://wikipedia.org/wiki/Rio_Uchida"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_September_1992)\" title=\"<PERSON> (footballer, born September 1992)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_September_1992)\" title=\"<PERSON> (footballer, born September 1992)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born September 1992)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_September_1992)"}]}, {"year": "1992", "text": "<PERSON>, North Korean footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ryong\" title=\"<PERSON>-ryong\"><PERSON>yon<PERSON></a>, North Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ryong\" title=\"<PERSON>-ryong\"><PERSON></a>, North Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ryong"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Irish singer-songwriter and actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Shaughnessy\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ryan_O%27Shaughnessy"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Granit_Xhaka\" title=\"Granit Xhaka\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Granit_Xhaka\" title=\"Granit Xhaka\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "Granit <PERSON>a", "link": "https://wikipedia.org/wiki/Granit_Xhaka"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1n"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Puerto Rican-American tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean singer and musical actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bi\" title=\"<PERSON><PERSON>-bi\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and musical actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bi\" title=\"<PERSON><PERSON>-bi\"><PERSON><PERSON>-<PERSON></a>, South Korean singer and musical actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-bi"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON> Animations, American YouTuber and animator", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jaiden_Animations\" title=\"Jaiden Animations\"><PERSON><PERSON> Animations</a>, American YouTuber and animator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaiden_Animations\" title=\"Jaiden Animations\"><PERSON><PERSON> Animations</a>, American YouTuber and animator", "links": [{"title": "Jaiden Animations", "link": "https://wikipedia.org/wiki/Jaiden_Animations"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Romanian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>c%C4%83\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>c%C4%83\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ioana_Minc%C4%83"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American race car driver", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "765", "text": "<PERSON><PERSON><PERSON>, Chinese general during the Tang dynasty", "html": "765 - <a href=\"https://wikipedia.org/wiki/Pug<PERSON>_<PERSON>%27en\" title=\"<PERSON>ug<PERSON>en\"><PERSON><PERSON><PERSON>en</a>, Chinese general during the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ug<PERSON>_<PERSON>%27en\" title=\"Pug<PERSON>en\"><PERSON><PERSON><PERSON>en</a>, Chinese general during the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pugu_Huai%27en"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "936", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Later Baekje (b. 867)", "html": "936 - <a href=\"https://wikipedia.org/wiki/Ky%C5%8Fn_Hw%C5%8Fn\" title=\"Kyŏn Hwŏn\"><PERSON><PERSON><PERSON><PERSON> Hwŏn</a>, king of <a href=\"https://wikipedia.org/wiki/Later_Baekje\" title=\"Later Baekje\">Later Baekje</a> (b. 867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ky%C5%8Fn_Hw%C5%8Fn\" title=\"Kyŏn Hwŏn\"><PERSON><PERSON>ŏ<PERSON> Hwŏn</a>, king of <a href=\"https://wikipedia.org/wiki/Later_Baekje\" title=\"Later Baekje\">Later Baekje</a> (b. 867)", "links": [{"title": "Kyŏn Hwŏn", "link": "https://wikipedia.org/wiki/Ky%C5%8Fn_Hw%C5%8Fn"}, {"title": "Later Baekje", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ekje"}]}, {"year": "1111", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian Benedictine abbess", "html": "1111 - <a href=\"https://wikipedia.org/wiki/Vekenega\" title=\"Vekenega\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian Benedictine abbess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vekenega\" title=\"Vekenega\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian Benedictine abbess", "links": [{"title": "Vekenega", "link": "https://wikipedia.org/wiki/Vekenega"}]}, {"year": "1115", "text": " <PERSON><PERSON><PERSON>, Italian saint and bishop of Foligno (b.c. 1040)", "html": "1115 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint and <a href=\"https://wikipedia.org/wiki/Bishop_of_Foligno\" class=\"mw-redirect\" title=\"Bishop of Foligno\">bishop of Foligno</a> (b.c. <a href=\"https://wikipedia.org/wiki/1040\" title=\"1040\">1040</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint and <a href=\"https://wikipedia.org/wiki/Bishop_of_Foligno\" class=\"mw-redirect\" title=\"Bishop of Foligno\">bishop of Foligno</a> (b.c. <a href=\"https://wikipedia.org/wiki/1040\" title=\"1040\">1040</a>)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Bishop of Foligno", "link": "https://wikipedia.org/wiki/<PERSON>_of_Foligno"}, {"title": "1040", "link": "https://wikipedia.org/wiki/1040"}]}, {"year": "1125", "text": "<PERSON><PERSON><PERSON> of Berg, Duchess of Bohemia (b.c. 1095)", "html": "1125 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> of Berg\"><PERSON><PERSON><PERSON> of Berg</a>, Duchess of Bohemia (b.c. <a href=\"https://wikipedia.org/wiki/1095\" title=\"1095\">1095</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Berg\" title=\"<PERSON><PERSON><PERSON> of Berg\"><PERSON><PERSON><PERSON> of Berg</a>, Duchess of Bohemia (b.c. <a href=\"https://wikipedia.org/wiki/1095\" title=\"1095\">1095</a>)", "links": [{"title": "Richeza of Berg", "link": "https://wikipedia.org/wiki/Richez<PERSON>_of_Berg"}, {"title": "1095", "link": "https://wikipedia.org/wiki/1095"}]}, {"year": "1194", "text": "<PERSON><PERSON>, Anglo-Norman nobleman (b. 1125)", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anglo-Norman nobleman (b. 1125)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Anglo-Norman nobleman (b. 1125)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1249", "text": "<PERSON>, Count of Toulouse (b. 1197)", "html": "1249 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Toulouse\" title=\"<PERSON>, Count of Toulouse\"><PERSON>, Count of Toulouse</a> (b. 1197)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Toulouse\" title=\"<PERSON>, Count of Toulouse\"><PERSON>, Count of Toulouse</a> (b. 1197)", "links": [{"title": "<PERSON>, Count of Toulouse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Toulouse"}]}, {"year": "1404", "text": "<PERSON> of Wykeham, English bishop (b. 1320)", "html": "1404 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Wykeham\" title=\"<PERSON> of Wykeham\"><PERSON> of Wykeham</a>, English bishop (b. 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wykeham\" title=\"<PERSON> of Wykeham\"><PERSON> of Wykeham</a>, English bishop (b. 1320)", "links": [{"title": "<PERSON> of Wykeham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1536", "text": "<PERSON><PERSON>, illegitimate daughter of <PERSON> <PERSON> (b. 1483)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, illegitimate daughter of <PERSON> <PERSON> (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, illegitimate daughter of <PERSON> (b. 1483)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON><PERSON><PERSON>, Polish Jesuit and polemicist (b. 1536)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish Jesuit and polemicist (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish Jesuit and polemicist (b. 1536)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>otr_<PERSON>ga"}]}, {"year": "1637", "text": "<PERSON>, Filipino saint (b. c.1600)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino saint (b. c.1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino saint (b. c.1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1657", "text": "<PERSON><PERSON><PERSON>, Roman noble (b. 1591)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/Olimpia_Maidalchini\" title=\"Olimpia Maidalchini\"><PERSON>lim<PERSON> Maidalchini</a>, Roman noble (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olimpia_Maidalchini\" title=\"Olimpia Maidalchini\">Olim<PERSON> Maidalchini</a>, Roman noble (b. 1591)", "links": [{"title": "Olimpia <PERSON>", "link": "https://wikipedia.org/wiki/Olimpia_Maidalchini"}]}, {"year": "1557", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1497)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Na<PERSON>\" title=\"Emperor <PERSON>-Nara\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Nara\" title=\"Emperor Go-Nara\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1497)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Na<PERSON>"}]}, {"year": "1590", "text": "<PERSON> (b. 1521)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_VII\" title=\"Pope Urban VII\"><PERSON> Urban VII</a> (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_VII\" title=\"Pope Urban VII\"><PERSON></a> (b. 1521)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Urban_VII"}]}, {"year": "1623", "text": "<PERSON>, Count of Nassau-Siegen (b. 1561)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a> (b. 1561)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1651", "text": "<PERSON>, Elector of Bavaria (b. 1573)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria\" title=\"<PERSON>, Elector of Bavaria\"><PERSON>, Elector of Bavaria</a> (b. 1573)", "links": [{"title": "<PERSON>, Elector of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Bavaria"}]}, {"year": "1660", "text": "<PERSON>, French priest and saint (b. 1581)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and saint (b. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON>, French writer (b. 1589)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Andilly\" title=\"<PERSON>\"><PERSON></a>, French writer (b. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Andilly\" title=\"<PERSON>\"><PERSON></a>, French writer (b. 1589)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_d%27Andilly"}]}, {"year": "1700", "text": "<PERSON> (b. 1615)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_XII\" title=\"Pope Innocent XII\">Pope Innocent <PERSON></a> (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_XII\" title=\"Pope Innocent XII\">Pope <PERSON></a> (b. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_XII"}]}, {"year": "1719", "text": "<PERSON>, English bishop (b. 1662)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, English poet and author (b. 1688)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, Swedish ichthyologist and zoologist (b. 1705)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ichthyologist and zoologist (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ichthyologist and zoologist (b. 1705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, Irish archbishop (b. 1672)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish archbishop (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, French mathematician and theorist (b. 1730)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (b. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_B%C3%A9zout"}]}, {"year": "1832", "text": "<PERSON>, German philosopher and author (b. 1781)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Karl <PERSON>\"><PERSON></a>, German philosopher and author (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, Indian humanitarian and reformer (b. 1772)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/Raja_<PERSON>_<PERSON>_<PERSON>\" title=\"Raja <PERSON>\">Raja <PERSON></a>, Indian humanitarian and reformer (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raja_<PERSON>_<PERSON>_<PERSON>\" title=\"Raja <PERSON>\">Raja <PERSON></a>, Indian humanitarian and reformer (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raja_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French chemist and pharmacist (b. 1777)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and pharmacist (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and pharmacist (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, American general (b. 1817)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Braxton Bragg\"><PERSON><PERSON><PERSON></a>, American general (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Brax<PERSON> Bragg\"><PERSON><PERSON><PERSON></a>, American general (b. 1817)", "links": [{"title": "Braxton Bragg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American journalist and politician (b. 1804)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Russian author and critic (b. 1812)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Australian politician, 12th Premier of Queensland (b. 1860)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1911", "text": "<PERSON>, French geologist and academic (b. 1844)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French geologist and academic (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9vy"}]}, {"year": "1915", "text": "<PERSON><PERSON>, French novelist, poet, and critic (b. 1858)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, poet, and critic (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, poet, and critic (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French painter and sculptor (b. 1834)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Italian-French opera singer (b. 1843)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French <a href=\"https://wikipedia.org/wiki/Opera\" title=\"Opera\">opera</a> singer (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French <a href=\"https://wikipedia.org/wiki/Opera\" title=\"Opera\">opera</a> singer (b. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Opera", "link": "https://wikipedia.org/wiki/Opera"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, German composer and educator (b. 1854)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, German composer and educator (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, German composer and educator (b. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1934", "text": "<PERSON>, English horticulturalist (b. 1858)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English composer and organist (b. 1855)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and organist (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and organist (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, German philosopher and critic (b. 1892)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and critic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and critic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Austrian physician and neuroscientist, Nobel Prize laureate (b. 1857)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and neuroscientist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and neuroscientist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1942", "text": "<PERSON>, United States Coast Guard signalman, posthumously awarded Medal of Honor, (b. 1919)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Coast Guard signalman, posthumously awarded <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>, (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Coast Guard signalman, posthumously awarded <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>, (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Canadian-American evangelist, founded the International Church of the Foursquare Gospel (b. 1890)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American evangelist, founded the <a href=\"https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel\" class=\"mw-redirect\" title=\"International Church of the Foursquare Gospel\">International Church of the Foursquare Gospel</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American evangelist, founded the <a href=\"https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel\" class=\"mw-redirect\" title=\"International Church of the Foursquare Gospel\">International Church of the Foursquare Gospel</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "International Church of the Foursquare Gospel", "link": "https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel"}]}, {"year": "1956", "text": "<PERSON>, English composer and educator (b. 1901)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American basketball player and golfer (b. 1911)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American basketball player and golfer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and golfer (b. 1911)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English activist (b. 1882)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, American poet, novelist, and memoirist (b. 1886)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/H.D.\" title=\"H.D.\"><PERSON><PERSON><PERSON>.</a>, American poet, novelist, and memoirist (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H.D.\" title=\"H.D.\"><PERSON><PERSON><PERSON>.</a>, American poet, novelist, and memoirist (b. 1886)", "links": [{"title": "H.D.", "link": "https://wikipedia.org/wiki/H.D."}]}, {"year": "1965", "text": "<PERSON>, American actress (b. 1905)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bow\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English engineer, co-designed the London, Midland and Scottish Railway (b. 1876)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, co-designed the <a href=\"https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway\" title=\"London, Midland and Scottish Railway\">London, Midland and Scottish Railway</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, co-designed the <a href=\"https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway\" title=\"London, Midland and Scottish Railway\">London, Midland and Scottish Railway</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "London, Midland and Scottish Railway", "link": "https://wikipedia.org/wiki/London,_Midland_and_Scottish_Railway"}]}, {"year": "1967", "text": "<PERSON>, Russian husband of Princess <PERSON><PERSON> of Russia (b. 1887)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian husband of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Russia\" title=\"Princess <PERSON><PERSON> of Russia\">Princess <PERSON><PERSON> of Russia</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian husband of <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Russia\" title=\"Princess <PERSON><PERSON> of Russia\">Princess <PERSON><PERSON> of Russia</a> (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Princess <PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_Alex<PERSON>rovna_of_Russia"}]}, {"year": "1972", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian mathematician, librarian, and academic (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian mathematician, librarian, and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian mathematician, librarian, and academic (b. 1892)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Argentinian lawyer and academic (b. 1907)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian lawyer and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian lawyer and academic (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1975", "text": "<PERSON>, Australian lawyer and politician, 23rd Premier of New South Wales (b. 1876)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1876)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1979", "text": "<PERSON>, English actress and singer (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fields\" title=\"<PERSON> Fields\"><PERSON></a>, English actress and singer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gracie_Fields\" title=\"<PERSON> Fields\"><PERSON></a>, English actress and singer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish singer-songwriter and guitarist (b. 1953)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, singer, director, and producer (b. 1904)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, director, and producer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, director, and producer (b. 1904)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Australian journalist and author (b. 1911)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist and author (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Greek actor (b. 1932)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Chron<PERSON>_<PERSON>hak<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chron<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek actor (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chronis_Exarhakos"}]}, {"year": "1985", "text": "<PERSON>, American actor (b. 1902)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American bass player and songwriter  (b. 1962)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Cliff_Burton\" title=\"Cliff Burton\"><PERSON></a>, American bass player and songwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cliff_Burton\" title=\"Cliff Burton\"><PERSON></a>, American bass player and songwriter (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cliff_Burton"}]}, {"year": "1991", "text": "<PERSON>, English footballer and cricketer (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and cricketer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Chinese comic artist (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese comic artist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese comic artist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American general, Medal of Honor recipient (b. 1896)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1993", "text": "<PERSON>, Canadian saxophonist and educator (b. 1928)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saxophonist and educator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saxophonist and educator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fraser_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Afghan physician and politician, seventh President of Afghanistan (b. 1947)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan physician and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan physician and politician, seventh <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Afghanistan", "link": "https://wikipedia.org/wiki/President_of_Afghanistan"}]}, {"year": "1997", "text": "<PERSON>, American viola player and educator (b. 1915)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American viola player and educator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American viola player and educator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American football player (b. 1927)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French race car driver (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, French race car driver (b. 1927)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2003", "text": "<PERSON>, American actor, singer, and dancer (b. 1925)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor"}]}, {"year": "2004", "text": "<PERSON>, American psychiatrist and author (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Brazilian comedian and actor (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian comedian and actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian comedian and actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American novelist, essayist, and memoirist (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and memoirist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and memoirist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, German chemist and soldier (b. 1910)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and soldier (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and soldier (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Japanese photographer and journalist (b. 1957)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese photographer and journalist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese photographer and journalist (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gai"}]}, {"year": "2008", "text": "<PERSON>, American director and producer (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Russian director and screenwriter (b. 1947)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and screenwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American physician and mountaineer (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and mountaineer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and mountaineer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American author and journalist (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and journalist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American football player (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Singaporean neurosurgeon and politician, Minister of Foreign Affairs for Singapore (b. 1955)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean neurosurgeon and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Singapore)\" title=\"Ministry of Foreign Affairs (Singapore)\">Minister of Foreign Affairs for Singapore</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Singaporean neurosurgeon and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Singapore)\" title=\"Ministry of Foreign Affairs (Singapore)\">Minister of Foreign Affairs for Singapore</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Singapore)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Singapore)"}]}, {"year": "2010", "text": "<PERSON>, English race car driver (b. 1936)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (b. 1936)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "2011", "text": "<PERSON>, English director, producer, and screenwriter (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_producer)\" title=\"<PERSON> (TV producer)\"><PERSON></a>, English director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_producer)\" title=\"<PERSON> (TV producer)\"><PERSON></a>, English director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON> (TV producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(TV_producer)"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Hungarian architect (b. 1935)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian architect (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian architect (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter  (b. 1933)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Country%22_Math<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"Country\" Mathis'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Country%22_Math<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"Country\" Mathis'><PERSON> \"Country\" <PERSON></a>, American singer-songwriter (b. 1933)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Country%22_Mathis"}]}, {"year": "2012", "text": "<PERSON>, American trombonist (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bert\"><PERSON></a>, American trombonist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Czech-English actor (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American academic and politician (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian director and screenwriter (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director and screenwriter (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American songwriter and producer (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American songwriter and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American songwriter and producer (b. 1940)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON>, Brazilian-American guitarist, composer, and conductor (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American guitarist, composer, and conductor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American guitarist, composer, and conductor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish sherry maker and a conservationist (b. 1923) ", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1<PERSON><PERSON>-<PERSON>_y_D%C3%ADez\" title=\"<PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON> y <PERSON></a>, Spanish sherry maker and a conservationist (b. 1923) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez-<PERSON>_y_D%C3%ADez\" title=\"<PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON> y <PERSON></a>, Spanish sherry maker and a conservationist (b. 1923) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C3%A1lez-Gordon_y_D%C3%ADez"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Turkish actor, director, and screenwriter (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor, director, and screenwriter (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English rugby player (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, French fashion designer, founded <PERSON><PERSON><PERSON> (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Chlo%C3%A9\" title=\"Chlo<PERSON>\">Ch<PERSON><PERSON></a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Chlo%C3%A9\" title=\"Chlo<PERSON>\">Ch<PERSON><PERSON></a> (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ghion"}, {"title": "Chloé", "link": "https://wikipedia.org/wiki/Chlo%C3%A9"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American journalist and author (b. 1971)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American architect and educator, designed Haydon Burns Library and Friendship Fountain Park (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and educator, designed <a href=\"https://wikipedia.org/wiki/Haydon_Burns_Library\" class=\"mw-redirect\" title=\"Haydon Burns Library\">Haydon Burns Library</a> and <a href=\"https://wikipedia.org/wiki/Friendship_Fountain_Park\" class=\"mw-redirect\" title=\"Friendship Fountain Park\">Friendship Fountain Park</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>wick\"><PERSON></a>, American architect and educator, designed <a href=\"https://wikipedia.org/wiki/Haydon_Burns_Library\" class=\"mw-redirect\" title=\"Haydon Burns Library\">Haydon Burns Library</a> and <a href=\"https://wikipedia.org/wiki/Friendship_Fountain_Park\" class=\"mw-redirect\" title=\"Friendship Fountain Park\">Friendship Fountain Park</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Haydon Burns Library", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Library"}, {"title": "Friendship Fountain Park", "link": "https://wikipedia.org/wiki/Friendship_Fountain_Park"}]}, {"year": "2014", "text": "<PERSON>, Canadian ice hockey player (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Tunisian actor and director (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Abdel<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian actor and director (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdelma<PERSON>_<PERSON>l\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tunisian actor and director (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdelmajid_Lakhal"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and politician (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nt"}]}, {"year": "2015", "text": "<PERSON>, Indian author and politician, 16th Governor of Manipur (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian author and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Manipur\" class=\"mw-redirect\" title=\"Governor of Manipur\">Governor of Manipur</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Indian author and politician, 16th <a href=\"https://wikipedia.org/wiki/Governor_of_Manipur\" class=\"mw-redirect\" title=\"Governor of Manipur\">Governor of Manipur</a> (b. 1945)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Governor of Manipur", "link": "https://wikipedia.org/wiki/Governor_of_Manipur"}]}, {"year": "2015", "text": "<PERSON>, Italian journalist and politician (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian activist and author (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>\" title=\"Ka<PERSON> Pokkudan\"><PERSON><PERSON></a>, Indian activist and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>\" title=\"Ka<PERSON> Pokkudan\"><PERSON><PERSON></a>, Indian activist and author (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-Australian cricketer, coach, and journalist (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer, coach, and journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer, coach, and journalist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American Boy Scout famous for attempting to build a nuclear reactor in a shed in his backyard (b. 1976)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Boy Scout famous for attempting to build a nuclear reactor in a shed in his backyard (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Boy Scout famous for attempting to build a nuclear reactor in a shed in his backyard (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American publisher, founder of Playboy Enterprises (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founder of <a href=\"https://wikipedia.org/wiki/Playboy_Enterprises\" class=\"mw-redirect\" title=\"Playboy Enterprises\">Playboy Enterprises</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founder of <a href=\"https://wikipedia.org/wiki/Playboy_Enterprises\" class=\"mw-redirect\" title=\"Playboy Enterprises\">Playboy Enterprises</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Playboy Enterprises", "link": "https://wikipedia.org/wiki/Playboy_Enterprises"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Indian author and translator (b. 1967)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and translator (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author and translator (b. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American football quarterback  (b. 1970)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football quarterback (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football quarterback (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian nobleman and politician (b. 1935)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>radyu<PERSON>ji\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>radyu<PERSON>h<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian nobleman and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>radyu<PERSON>ji\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Pradyumansinhji\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian nobleman and politician (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manoh<PERSON>in<PERSON>ji_Pradyu<PERSON>ji"}]}, {"year": "2018", "text": "<PERSON>, American singer, co-founder of the band Jefferson Airplane (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, co-founder of the band Jefferson Airplane (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, co-founder of the band Jefferson Airplane (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Irish-English actor (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Lebanese politician, 3rd Secretary-General of Hezbollah (b. 1960)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_Hezbollah\" title=\"Secretary-General of Hezbollah\">Secretary-General of Hezbollah</a> (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese politician, 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_Hezbollah\" title=\"Secretary-General of Hezbollah\">Secretary-General of Hezbollah</a> (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary-General of Hezbollah", "link": "https://wikipedia.org/wiki/Secretary-General_of_Hezbollah"}]}, {"year": "2024", "text": "<PERSON>, English actress (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}