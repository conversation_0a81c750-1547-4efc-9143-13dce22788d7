{"date": "June 25", "url": "https://wikipedia.org/wiki/June_25", "data": {"Events": [{"year": "524", "text": "The Franks are defeated by the Burgundians in the Battle of Vézeronce.", "html": "524 - The <a href=\"https://wikipedia.org/wiki/Franks\" title=\"Franks\">Franks</a> are defeated by the <a href=\"https://wikipedia.org/wiki/Burgundians\" title=\"Burgundians\">Burgundians</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_V%C3%A9zeronce\" title=\"Battle of Vézeronce\">Battle of Vézeronce</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Franks\" title=\"Franks\">Franks</a> are defeated by the <a href=\"https://wikipedia.org/wiki/Burgundians\" title=\"Burgundians\">Burgundians</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_V%C3%A9zeronce\" title=\"Battle of Vézeronce\">Battle of Vézeronce</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}, {"title": "Burgundians", "link": "https://wikipedia.org/wiki/Burgundians"}, {"title": "Battle of Vézeronce", "link": "https://wikipedia.org/wiki/Battle_of_V%C3%A9zeronce"}]}, {"year": "841", "text": "In the Battle of Fontenay-en-Puisaye, forces led by <PERSON> the <PERSON>ld and <PERSON> the German defeat the armies of Lothair I of Italy and <PERSON><PERSON><PERSON> II of Aquitaine.", "html": "841 - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Fontenoy_(841)\" title=\"Battle of Fontenoy (841)\">Battle of Fontenay-en-Puisaye</a>, forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bald\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> and <a href=\"https://wikipedia.org/wiki/Louis_the_German\" title=\"<PERSON> the German\"><PERSON> the German</a> defeat the armies of <a href=\"https://wikipedia.org/wiki/Lothair_I\" title=\"Lothair I\"><PERSON>hair I</a> of Italy and <a href=\"https://wikipedia.org/wiki/Pepin_II_of_Aquitaine\" title=\"Pepin II of Aquitaine\">Pepin II of Aquitaine</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Battle_of_Fontenoy_(841)\" title=\"Battle of Fontenoy (841)\">Battle of Fontenay-en-Puisaye</a>, forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bald\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> and <a href=\"https://wikipedia.org/wiki/Louis_the_German\" title=\"<PERSON> the German\"><PERSON> the German</a> defeat the armies of <a href=\"https://wikipedia.org/wiki/Lothair_I\" title=\"Lothair I\"><PERSON>hair I</a> of Italy and <a href=\"https://wikipedia.org/wiki/Pepin_II_of_Aquitaine\" title=\"Pepin II of Aquitaine\">Pepin II of Aquitaine</a>.", "links": [{"title": "Battle of Fontenoy (841)", "link": "https://wikipedia.org/wiki/Battle_of_Fontenoy_(841)"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> the German", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_German"}, {"title": "<PERSON><PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Pepin II of Aquitaine", "link": "https://wikipedia.org/wiki/Pepin_II_of_Aquitaine"}]}, {"year": "1258", "text": "War of Saint Sabas: In the Battle of Acre, the Venetians defeat a larger Genoese fleet sailing to relieve Acre.", "html": "1258 - <a href=\"https://wikipedia.org/wiki/War_of_Saint_Sabas\" title=\"War of Saint Sabas\">War of Saint Sabas</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Acre_(1258)\" title=\"Battle of Acre (1258)\">Battle of Acre</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> defeat a larger <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet sailing to relieve <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_Saint_Sabas\" title=\"War of Saint Sabas\">War of Saint Sabas</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Acre_(1258)\" title=\"Battle of Acre (1258)\">Battle of Acre</a>, the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetians</a> defeat a larger <a href=\"https://wikipedia.org/wiki/Republic_of_Genoa\" title=\"Republic of Genoa\">Genoese</a> fleet sailing to relieve <a href=\"https://wikipedia.org/wiki/Acre,_Israel\" title=\"Acre, Israel\">Acre</a>.", "links": [{"title": "War of Saint Sabas", "link": "https://wikipedia.org/wiki/War_of_Saint_Sabas"}, {"title": "Battle of Acre (1258)", "link": "https://wikipedia.org/wiki/Battle_of_Acre_(1258)"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Republic of Genoa", "link": "https://wikipedia.org/wiki/Republic_of_Genoa"}, {"title": "Acre, Israel", "link": "https://wikipedia.org/wiki/Acre,_Israel"}]}, {"year": "1530", "text": "At the Diet of Augsburg the Augsburg Confession is presented to the Holy Roman Emperor by the Lutheran princes and Electors of Germany.", "html": "1530 - At the <a href=\"https://wikipedia.org/wiki/Diet_of_Augsburg\" title=\"Diet of Augsburg\"><PERSON> of Augsburg</a> the <a href=\"https://wikipedia.org/wiki/Augsburg_Confession\" title=\"Augsburg Confession\">Augsburg Confession</a> is presented to the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> by the <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> princes and <a href=\"https://wikipedia.org/wiki/Prince-elector\" title=\"Prince-elector\">Electors</a> of Germany.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Augsburg\" title=\"Diet of Augsburg\">Diet of Augsburg</a> the <a href=\"https://wikipedia.org/wiki/Augsburg_Confession\" title=\"Augsburg Confession\">Augsburg Confession</a> is presented to the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> by the <a href=\"https://wikipedia.org/wiki/Lutheran\" class=\"mw-redirect\" title=\"Lutheran\">Lutheran</a> princes and <a href=\"https://wikipedia.org/wiki/Prince-elector\" title=\"Prince-elector\">Electors</a> of Germany.", "links": [{"title": "Diet of Augsburg", "link": "https://wikipedia.org/wiki/Diet_of_Augsburg"}, {"title": "Augsburg Confession", "link": "https://wikipedia.org/wiki/Augsburg_Confession"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "Lutheran", "link": "https://wikipedia.org/wiki/Lutheran"}, {"title": "Prince-elector", "link": "https://wikipedia.org/wiki/Prince-elector"}]}, {"year": "1658", "text": "Spanish forces fail to retake Jamaica at the Battle of Rio Nuevo during the Anglo-Spanish War.", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces fail to retake <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rio_Nuevo\" title=\"Battle of Rio Nuevo\">Battle of Rio Nuevo</a> during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish</a> forces fail to retake <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rio_Nuevo\" title=\"Battle of Rio Nuevo\">Battle of Rio Nuevo</a> during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "links": [{"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}, {"title": "Battle of Rio Nuevo", "link": "https://wikipedia.org/wiki/Battle_of_Rio_Nuevo"}, {"title": "Anglo-Spanish War (1654-60)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)"}]}, {"year": "1678", "text": "Venetian <PERSON> is the first woman awarded a doctorate of philosophy when she graduates from the University of Padua.", "html": "1678 - <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first woman awarded a <a href=\"https://wikipedia.org/wiki/Doctor_of_Philosophy\" title=\"Doctor of Philosophy\">doctorate of philosophy</a> when she graduates from the <a href=\"https://wikipedia.org/wiki/University_of_Padua\" title=\"University of Padua\">University of Padua</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venetian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first woman awarded a <a href=\"https://wikipedia.org/wiki/Doctor_of_Philosophy\" title=\"Doctor of Philosophy\">doctorate of philosophy</a> when she graduates from the <a href=\"https://wikipedia.org/wiki/University_of_Padua\" title=\"University of Padua\">University of Padua</a>.", "links": [{"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Piscopia"}, {"title": "Doctor of Philosophy", "link": "https://wikipedia.org/wiki/Doctor_of_Philosophy"}, {"title": "University of Padua", "link": "https://wikipedia.org/wiki/University_of_Padua"}]}, {"year": "1741", "text": "<PERSON> is crowned Queen of Hungary.", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Hungary\" class=\"mw-redirect\" title=\"List of rulers of Hungary\">Queen of Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Hungary\" class=\"mw-redirect\" title=\"List of rulers of Hungary\">Queen of Hungary</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of rulers of Hungary", "link": "https://wikipedia.org/wiki/List_of_rulers_of_Hungary"}]}, {"year": "1786", "text": "<PERSON><PERSON><PERSON><PERSON> discovers St. George Island of the Pribilof Islands in the Bering Sea.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/St._George_Island_(Alaska)\" class=\"mw-redirect\" title=\"St. George Island (Alaska)\">St. George Island</a> of the <a href=\"https://wikipedia.org/wiki/Pribilof_Islands\" title=\"Pribilof Islands\">Pribilof Islands</a> in the <a href=\"https://wikipedia.org/wiki/Bering_Sea\" title=\"Bering Sea\">Bering Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/St._George_Island_(Alaska)\" class=\"mw-redirect\" title=\"St. George Island (Alaska)\">St. George Island</a> of the <a href=\"https://wikipedia.org/wiki/Pribilof_Islands\" title=\"Pribilof Islands\">Pribilof Islands</a> in the <a href=\"https://wikipedia.org/wiki/Bering_Sea\" title=\"Bering Sea\">Bering Sea</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "St. George Island (Alaska)", "link": "https://wikipedia.org/wiki/St._George_Island_(Alaska)"}, {"title": "Pribilof Islands", "link": "https://wikipedia.org/wiki/Pribilof_Islands"}, {"title": "Bering Sea", "link": "https://wikipedia.org/wiki/Bering_Sea"}]}, {"year": "1788", "text": "Virginia becomes the tenth state to ratify the United States Constitution.", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> becomes the tenth <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a> becomes the tenth <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> to ratify the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a>.", "links": [{"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}]}, {"year": "1848", "text": "A photograph of the June Days uprising becomes the first known instance of photojournalism.", "html": "1848 - A photograph of the <a href=\"https://wikipedia.org/wiki/June_Days_uprising\" title=\"June Days uprising\">June Days uprising</a> becomes the first known instance of <a href=\"https://wikipedia.org/wiki/Photojournalism\" title=\"Photojournalism\">photojournalism</a>.", "no_year_html": "A photograph of the <a href=\"https://wikipedia.org/wiki/June_Days_uprising\" title=\"June Days uprising\">June Days uprising</a> becomes the first known instance of <a href=\"https://wikipedia.org/wiki/Photojournalism\" title=\"Photojournalism\">photojournalism</a>.", "links": [{"title": "June Days uprising", "link": "https://wikipedia.org/wiki/June_Days_uprising"}, {"title": "Photojournalism", "link": "https://wikipedia.org/wiki/Photojournalism"}]}, {"year": "1876", "text": "American Indian Wars: Battle of the Little Bighorn: 300 men of the U.S. 7th Cavalry Regiment under Lieutenant Colonel <PERSON> are wiped out by 5,000 Lakota, Cheyenne and Arapaho, led by <PERSON> and <PERSON> Horse.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn\" title=\"Battle of the Little Bighorn\">Battle of the Little Bighorn</a>: 300 men of the <a href=\"https://wikipedia.org/wiki/U.S._7th_Cavalry_Regiment\" class=\"mw-redirect\" title=\"U.S. 7th Cavalry Regiment\">U.S. 7th Cavalry Regiment</a> under <a href=\"https://wikipedia.org/wiki/Lieutenant_colonel\" title=\"Lieutenant colonel\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are wiped out by 5,000 <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a>, <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> and <a href=\"https://wikipedia.org/wiki/Arapaho\" title=\"Arapaho\"><PERSON><PERSON><PERSON></a>, led by <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a> and <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn\" title=\"Battle of the Little Bighorn\">Battle of the Little Bighorn</a>: 300 men of the <a href=\"https://wikipedia.org/wiki/U.S._7th_Cavalry_Regiment\" class=\"mw-redirect\" title=\"U.S. 7th Cavalry Regiment\">U.S. 7th Cavalry Regiment</a> under <a href=\"https://wikipedia.org/wiki/Lieutenant_colonel\" title=\"Lieutenant colonel\">Lieutenant Colonel</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are wiped out by 5,000 <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a>, <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> and <a href=\"https://wikipedia.org/wiki/Arapaho\" title=\"Arapaho\"><PERSON><PERSON><PERSON></a>, led by <a href=\"https://wikipedia.org/wiki/Sitting_Bull\" title=\"Sitting Bull\">Sitting Bull</a> and <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Battle of the Little Bighorn", "link": "https://wikipedia.org/wiki/Battle_of_the_Little_Bighorn"}, {"title": "U.S. 7th Cavalry Regiment", "link": "https://wikipedia.org/wiki/U.S._7th_Cavalry_Regiment"}, {"title": "Lieutenant colonel", "link": "https://wikipedia.org/wiki/Lieutenant_colonel"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "Cheyenne", "link": "https://wikipedia.org/wiki/Cheyenne"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arapaho"}, {"title": "Sitting Bull", "link": "https://wikipedia.org/wiki/Sitting_Bull"}, {"title": "Crazy Horse", "link": "https://wikipedia.org/wiki/Crazy_Horse"}]}, {"year": "1900", "text": "The Taoist monk <PERSON> discovers the Dunhuang manuscripts, a cache of ancient texts that are of great historical and religious significance, in the Mogao Caves of Dunhuang, China.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Taoism\" title=\"Taoism\">Taoist</a> monk <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Dunhuang_manuscripts\" title=\"Dunhuang manuscripts\">Dunhuang manuscripts</a>, a cache of ancient texts that are of great historical and religious significance, in the <a href=\"https://wikipedia.org/wiki/Mogao_Caves\" title=\"Mogao Caves\">Mogao Caves</a> of <a href=\"https://wikipedia.org/wiki/Dunhuang\" title=\"Dunhuang\">Dunhuang</a>, China.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Taoism\" title=\"Taoism\">Taoist</a> monk <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the <a href=\"https://wikipedia.org/wiki/Dunhuang_manuscripts\" title=\"Dunhuang manuscripts\">Dunhuang manuscripts</a>, a cache of ancient texts that are of great historical and religious significance, in the <a href=\"https://wikipedia.org/wiki/Mogao_Caves\" title=\"Mogao Caves\">Mogao Caves</a> of <a href=\"https://wikipedia.org/wiki/Dunhuang\" title=\"Dunhuang\">Dunhuang</a>, China.", "links": [{"title": "Taoism", "link": "https://wikipedia.org/wiki/Taoism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dunhuang manuscripts", "link": "https://wikipedia.org/wiki/Dunhuang_manuscripts"}, {"title": "Mogao Caves", "link": "https://wikipedia.org/wiki/Mogao_Caves"}, {"title": "Dunhuang", "link": "https://wikipedia.org/wiki/Dunhuang"}]}, {"year": "1906", "text": "Pittsburgh, Pennsylvania millionaire <PERSON> shoots and kills prominent architect <PERSON>.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a> millionaire <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> shoots and kills prominent architect <a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"Stanford White\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a> millionaire <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> shoots and kills prominent architect <a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"Stanford White\"><PERSON></a>.", "links": [{"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stanford_White"}]}, {"year": "1910", "text": "The United States Congress passes the Mann Act, which prohibits interstate transport of women or girls for \"immoral purposes\"; the ambiguous language would be used to selectively prosecute people for years to come.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Mann_Act\" title=\"Mann Act\">Mann Act</a>, which prohibits interstate transport of women or girls for \"immoral purposes\"; the ambiguous language would be used to selectively prosecute people for years to come.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Mann_Act\" title=\"Mann Act\">Mann Act</a>, which prohibits interstate transport of women or girls for \"immoral purposes\"; the ambiguous language would be used to selectively prosecute people for years to come.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Mann Act", "link": "https://wikipedia.org/wiki/Mann_Act"}]}, {"year": "1910", "text": "<PERSON>'s ballet The Firebird is premiered in Paris, bringing him to prominence as a composer.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s ballet <i><a href=\"https://wikipedia.org/wiki/The_Firebird\" title=\"The Firebird\">The Firebird</a></i> is premiered in Paris, bringing him to prominence as a composer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s ballet <i><a href=\"https://wikipedia.org/wiki/The_Firebird\" title=\"The Firebird\">The Firebird</a></i> is premiered in Paris, bringing him to prominence as a composer.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Firebird", "link": "https://wikipedia.org/wiki/The_Firebird"}]}, {"year": "1913", "text": "American Civil War veterans begin arriving at the Great Reunion of 1913.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> veterans begin arriving at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> veterans begin arriving at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Great Reunion of 1913", "link": "https://wikipedia.org/wiki/Great_Reunion_of_1913"}]}, {"year": "1935", "text": "Colombia-Soviet Union relations are established.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Colombia%E2%80%93Russia_relations\" title=\"Colombia-Russia relations\">Colombia-Soviet Union relations</a> are established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colombia%E2%80%93Russia_relations\" title=\"Colombia-Russia relations\">Colombia-Soviet Union relations</a> are established.", "links": [{"title": "Colombia-Russia relations", "link": "https://wikipedia.org/wiki/Colombia%E2%80%93Russia_relations"}]}, {"year": "1938", "text": "Dr. <PERSON> is inaugurated as the first President of Ireland.", "html": "1938 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the first <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the first <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1940", "text": "World War II: The French armistice with Nazi Germany comes into effect.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The French <a href=\"https://wikipedia.org/wiki/Armistice_of_22_June_1940\" title=\"Armistice of 22 June 1940\">armistice</a> with Nazi Germany comes into effect.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The French <a href=\"https://wikipedia.org/wiki/Armistice_of_22_June_1940\" title=\"Armistice of 22 June 1940\">armistice</a> with Nazi Germany comes into effect.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Armistice of 22 June 1940", "link": "https://wikipedia.org/wiki/Armistice_of_22_June_1940"}]}, {"year": "1941", "text": "World War II: The Continuation War between the Soviet Union and Finland, supported by Nazi Germany, began.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a> between the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, supported by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, began.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Continuation_War\" title=\"Continuation War\">Continuation War</a> between the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, supported by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, began.", "links": [{"title": "Continuation War", "link": "https://wikipedia.org/wiki/Continuation_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1943", "text": "The Holocaust and World War II: Jews in the Częstochowa Ghetto in Poland stage an uprising against the Nazis.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a> and World War II: Jews in the <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto\" title=\"Częstochowa Ghetto\">Częstochowa Ghetto</a> in Poland stage <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto_Uprising\" class=\"mw-redirect\" title=\"Częstochowa Ghetto Uprising\">an uprising</a> against the Nazis.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a> and World War II: Jews in the <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto\" title=\"Częstochowa Ghetto\">Częstochowa Ghetto</a> in Poland stage <a href=\"https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto_Uprising\" class=\"mw-redirect\" title=\"Częstochowa Ghetto Uprising\">an uprising</a> against the Nazis.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Częstochowa Ghetto", "link": "https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto"}, {"title": "Częstochowa Ghetto Uprising", "link": "https://wikipedia.org/wiki/Cz%C4%99stochowa_Ghetto_Uprising"}]}, {"year": "1943", "text": "The left-wing German Jewish exile <PERSON> is murdered in Auschwitz.", "html": "1943 - The left-wing German Jewish exile <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is murdered in <a href=\"https://wikipedia.org/wiki/Auschwitz\" class=\"mw-redirect\" title=\"Auschwitz\">Auschwitz</a>.", "no_year_html": "The left-wing German Jewish exile <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is murdered in <a href=\"https://wikipedia.org/wiki/Auschwitz\" class=\"mw-redirect\" title=\"Auschwitz\">Auschwitz</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Auschwitz", "link": "https://wikipedia.org/wiki/Auschwitz"}]}, {"year": "1944", "text": "World War II: The Battle of Tali-Ihantala, the largest battle ever fought in the Nordic countries, begins.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tali-Ihantala\" class=\"mw-redirect\" title=\"Battle of Tali-Ihantala\">Battle of Tali-Ihantala</a>, the largest battle ever fought in the <a href=\"https://wikipedia.org/wiki/Nordic_countries\" title=\"Nordic countries\">Nordic countries</a>, begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tali-Ihantala\" class=\"mw-redirect\" title=\"Battle of Tali-Ihantala\">Battle of Tali-Ihantala</a>, the largest battle ever fought in the <a href=\"https://wikipedia.org/wiki/Nordic_countries\" title=\"Nordic countries\">Nordic countries</a>, begins.", "links": [{"title": "Battle of Tali-Ihantala", "link": "https://wikipedia.org/wiki/Battle_of_Tali-<PERSON>a"}, {"title": "Nordic countries", "link": "https://wikipedia.org/wiki/Nordic_countries"}]}, {"year": "1944", "text": "World War II: United States Navy and British Royal Navy ships bombard Cherbourg to support United States Army units engaged in the Battle of Cherbourg.", "html": "1944 - World War II: United States Navy and British Royal Navy ships <a href=\"https://wikipedia.org/wiki/Bombardment_of_Cherbourg\" title=\"Bombardment of Cherbourg\">bombard Cherbourg</a> to support <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> units engaged in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cherbourg\" title=\"Battle of Cherbourg\">Battle of Cherbourg</a>.", "no_year_html": "World War II: United States Navy and British Royal Navy ships <a href=\"https://wikipedia.org/wiki/Bombardment_of_Cherbourg\" title=\"Bombardment of Cherbourg\">bombard Cherbourg</a> to support <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> units engaged in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cherbourg\" title=\"Battle of Cherbourg\">Battle of Cherbourg</a>.", "links": [{"title": "Bombardment of Cherbourg", "link": "https://wikipedia.org/wiki/Bombardment_of_Cherbourg"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Battle of Cherbourg", "link": "https://wikipedia.org/wiki/Battle_of_Cherbourg"}]}, {"year": "1944", "text": "The final page of the comic <PERSON><PERSON><PERSON> Kat is published, exactly two months after its author <PERSON> died.", "html": "1944 - The final page of the comic <i><a href=\"https://wikipedia.org/wiki/Krazy_Kat\" title=\"Krazy Kat\">Krazy Kat</a></i> is published, exactly two months after its author <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> died.", "no_year_html": "The final page of the comic <i><a href=\"https://wikipedia.org/wiki/Krazy_Kat\" title=\"Krazy Kat\">Krazy Kat</a></i> is published, exactly two months after its author <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> died.", "links": [{"title": "Krazy Kat", "link": "https://wikipedia.org/wiki/Krazy_Kat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "The Diary of a Young Girl (better known as The Diary of <PERSON>) is published.", "html": "1947 - <i><a href=\"https://wikipedia.org/wiki/The_Diary_of_a_Young_Girl\" title=\"The Diary of a Young Girl\">The Diary of a Young Girl</a></i> (better known as <i>The Diary of <PERSON></i>) is published.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Diary_of_a_Young_Girl\" title=\"The Diary of a Young Girl\">The Diary of a Young Girl</a></i> (better known as <i>The Diary of <PERSON></i>) is published.", "links": [{"title": "The Diary of a Young Girl", "link": "https://wikipedia.org/wiki/The_Diary_of_a_Young_Girl"}]}, {"year": "1948", "text": "The United States Congress passes the Displaced Persons Act to allow World War II refugees to immigrate to the United States above quota restrictions.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Displaced_Persons_Act\" title=\"Displaced Persons Act\">Displaced Persons Act</a> to allow <a href=\"https://wikipedia.org/wiki/World_War_II_refugees\" class=\"mw-redirect\" title=\"World War II refugees\">World War II refugees</a> to <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrate to the United States</a> above quota restrictions.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Displaced_Persons_Act\" title=\"Displaced Persons Act\">Displaced Persons Act</a> to allow <a href=\"https://wikipedia.org/wiki/World_War_II_refugees\" class=\"mw-redirect\" title=\"World War II refugees\">World War II refugees</a> to <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrate to the United States</a> above quota restrictions.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Displaced Persons Act", "link": "https://wikipedia.org/wiki/Displaced_Persons_Act"}, {"title": "World War II refugees", "link": "https://wikipedia.org/wiki/World_War_II_refugees"}, {"title": "Immigration to the United States", "link": "https://wikipedia.org/wiki/Immigration_to_the_United_States"}]}, {"year": "1950", "text": "The Korean War begins with the invasion of South Korea by North Korea.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> begins with the invasion of <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">South Korea</a> by <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a> begins with the invasion of <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">South Korea</a> by <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "First Republic of Korea", "link": "https://wikipedia.org/wiki/First_Republic_of_Korea"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "1960", "text": "Cold War: Two cryptographers working for the United States National Security Agency left for vacation to Mexico, and from there defected to the Soviet Union.", "html": "1960 - Cold War: Two <a href=\"https://wikipedia.org/wiki/Cryptography\" title=\"Cryptography\">cryptographers</a> working for the United States <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a> left for vacation to Mexico, and from there <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_defection\" title=\"<PERSON> defection\">defected to the Soviet Union</a>.", "no_year_html": "Cold War: Two <a href=\"https://wikipedia.org/wiki/Cryptography\" title=\"Cryptography\">cryptographers</a> working for the United States <a href=\"https://wikipedia.org/wiki/National_Security_Agency\" title=\"National Security Agency\">National Security Agency</a> left for vacation to Mexico, and from there <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_defection\" title=\"<PERSON> defection\">defected to the Soviet Union</a>.", "links": [{"title": "Cryptography", "link": "https://wikipedia.org/wiki/Cryptography"}, {"title": "National Security Agency", "link": "https://wikipedia.org/wiki/National_Security_Agency"}, {"title": "<PERSON> and <PERSON> defection", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_defection"}]}, {"year": "1975", "text": "Mozambique achieves independence from Portugal.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> achieves independence from <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a> achieves independence from <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "links": [{"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "1975", "text": "Prime Minister <PERSON><PERSON> declares a state of internal emergency in India.", "html": "1975 - Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>Gandhi\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON> Gandhi</a> declares a <a href=\"https://wikipedia.org/wiki/The_Emergency_(India)\" title=\"The Emergency (India)\">state of internal emergency</a> in India.", "no_year_html": "Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>Gandhi\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON> Gandhi</a> declares a <a href=\"https://wikipedia.org/wiki/The_Emergency_(India)\" title=\"The Emergency (India)\">state of internal emergency</a> in India.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}, {"title": "The Emergency (India)", "link": "https://wikipedia.org/wiki/The_Emergency_(India)"}]}, {"year": "1976", "text": "Missouri Governor <PERSON> issues an executive order rescinding the Extermination Order, formally apologizing on behalf of the state of Missouri for the suffering it had caused to members of the Church of Jesus Christ of Latter-day Saints.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an executive order rescinding the <a href=\"https://wikipedia.org/wiki/Missouri_Executive_Order_44\" title=\"Missouri Executive Order 44\">Extermination Order</a>, formally apologizing on behalf of the state of Missouri for the suffering it had caused to members of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Missouri\" title=\"Missouri\">Missouri</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues an executive order rescinding the <a href=\"https://wikipedia.org/wiki/Missouri_Executive_Order_44\" title=\"Missouri Executive Order 44\">Extermination Order</a>, formally apologizing on behalf of the state of Missouri for the suffering it had caused to members of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a>.", "links": [{"title": "Missouri", "link": "https://wikipedia.org/wiki/Missouri"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Missouri Executive Order 44", "link": "https://wikipedia.org/wiki/Missouri_Executive_Order_44"}, {"title": "The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1978", "text": "The rainbow flag representing gay pride is flown for the first time during the San Francisco Gay Freedom Day Parade.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Rainbow_flag_(LGBT_movement)\" class=\"mw-redirect\" title=\"Rainbow flag (LGBT movement)\">rainbow flag</a> representing <a href=\"https://wikipedia.org/wiki/Gay_pride\" class=\"mw-redirect\" title=\"Gay pride\">gay pride</a> is flown for the first time during the San Francisco <a href=\"https://wikipedia.org/wiki/San_Francisco_Pride\" title=\"San Francisco Pride\">Gay Freedom Day Parade</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rainbow_flag_(LGBT_movement)\" class=\"mw-redirect\" title=\"Rainbow flag (LGBT movement)\">rainbow flag</a> representing <a href=\"https://wikipedia.org/wiki/Gay_pride\" class=\"mw-redirect\" title=\"Gay pride\">gay pride</a> is flown for the first time during the San Francisco <a href=\"https://wikipedia.org/wiki/San_Francisco_Pride\" title=\"San Francisco Pride\">Gay Freedom Day Parade</a>.", "links": [{"title": "Rainbow flag (LGBT movement)", "link": "https://wikipedia.org/wiki/Rainbow_flag_(LGBT_movement)"}, {"title": "Gay pride", "link": "https://wikipedia.org/wiki/Gay_pride"}, {"title": "San Francisco Pride", "link": "https://wikipedia.org/wiki/San_Francisco_Pride"}]}, {"year": "1981", "text": "Microsoft is restructured to become an incorporated business in its home state of Washington.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is restructured to become an incorporated business in its home state of <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> is restructured to become an incorporated business in its home state of <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a>.", "links": [{"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}]}, {"year": "1991", "text": "The breakup of Yugoslavia begins when Slovenia and Croatia declare their independence from Yugoslavia.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Breakup_of_Yugoslavia\" title=\"Breakup of Yugoslavia\">breakup of Yugoslavia</a> begins when <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> and <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> declare their independence from <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Breakup_of_Yugoslavia\" title=\"Breakup of Yugoslavia\">breakup of Yugoslavia</a> begins when <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> and <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> declare their independence from <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a>.", "links": [{"title": "Breakup of Yugoslavia", "link": "https://wikipedia.org/wiki/Breakup_of_Yugoslavia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1992", "text": "Space Shuttle Columbia launches on STS-50, the first shuttle mission to carry Extended Duration Orbiter hardware.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-50\" title=\"STS-50\">STS-50</a>, the first shuttle mission to carry <a href=\"https://wikipedia.org/wiki/Extended_Duration_Orbiter\" title=\"Extended Duration Orbiter\">Extended Duration Orbiter</a> hardware.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-50\" title=\"STS-50\">STS-50</a>, the first shuttle mission to carry <a href=\"https://wikipedia.org/wiki/Extended_Duration_Orbiter\" title=\"Extended Duration Orbiter\">Extended Duration Orbiter</a> hardware.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-50", "link": "https://wikipedia.org/wiki/STS-50"}, {"title": "Extended Duration Orbiter", "link": "https://wikipedia.org/wiki/Extended_Duration_Orbiter"}]}, {"year": "1993", "text": "<PERSON> is sworn in as the first female Prime Minister of Canada.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first female <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Canada\" class=\"mw-redirect\" title=\"List of Prime Ministers of Canada\">Prime Minister of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first female <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Canada\" class=\"mw-redirect\" title=\"List of Prime Ministers of Canada\">Prime Minister of Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Canada", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Canada"}]}, {"year": "1996", "text": "The Khobar Towers bombing in Saudi Arabia kills 19 U.S. servicemen.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Khobar_Towers_bombing\" title=\"Khobar Towers bombing\">Khobar Towers bombing</a> in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> kills 19 <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">U.S. servicemen</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Khobar_Towers_bombing\" title=\"Khobar Towers bombing\">Khobar Towers bombing</a> in <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> kills 19 <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">U.S. servicemen</a>.", "links": [{"title": "Khobar Towers bombing", "link": "https://wikipedia.org/wiki/Khobar_Towers_bombing"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}]}, {"year": "1996", "text": "American rapper <PERSON><PERSON><PERSON> releases his debut album, Reasonable Doubt. ", "html": "1996 - American rapper <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> releases his debut album, <a href=\"https://wikipedia.org/wiki/Reasonable_Doubt_(album)\" title=\"Reasonable Doubt (album)\">Reasonable Doubt</a>. ", "no_year_html": "American rapper <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> releases his debut album, <a href=\"https://wikipedia.org/wiki/Reasonable_Doubt_(album)\" title=\"Reasonable Doubt (album)\">Reasonable Doubt</a>. ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}, {"title": "Reasonable Doubt (album)", "link": "https://wikipedia.org/wiki/Reasonable_Doubt_(album)"}]}, {"year": "1997", "text": "An uncrewed Progress spacecraft collides with the Russian space station Mir.", "html": "1997 - An uncrewed <a href=\"https://wikipedia.org/wiki/Progress_M-34\" title=\"Progress M-34\">Progress spacecraft</a> collides with the Russian <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "no_year_html": "An uncrewed <a href=\"https://wikipedia.org/wiki/Progress_M-34\" title=\"Progress M-34\">Progress spacecraft</a> collides with the Russian <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "links": [{"title": "Progress M-34", "link": "https://wikipedia.org/wiki/Progress_M-34"}, {"title": "Space station", "link": "https://wikipedia.org/wiki/Space_station"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "1997", "text": "The National Hockey League approved expansion franchises for Nashville (1998), Atlanta (1999), Columbus (2000), and Minneapolis-Saint Paul (2000).", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> approved expansion franchises for <a href=\"https://wikipedia.org/wiki/Nashville_Predators\" title=\"Nashville Predators\">Nashville</a> (1998), <a href=\"https://wikipedia.org/wiki/Atlanta_Thrashers\" title=\"Atlanta Thrashers\">Atlanta</a> (1999), <a href=\"https://wikipedia.org/wiki/Columbus_Blue_Jackets\" title=\"Columbus Blue Jackets\">Columbus</a> (2000), and <a href=\"https://wikipedia.org/wiki/Minnesota_Wild\" title=\"Minnesota Wild\">Minneapolis-Saint Paul</a> (2000).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> approved expansion franchises for <a href=\"https://wikipedia.org/wiki/Nashville_Predators\" title=\"Nashville Predators\">Nashville</a> (1998), <a href=\"https://wikipedia.org/wiki/Atlanta_Thrashers\" title=\"Atlanta Thrashers\">Atlanta</a> (1999), <a href=\"https://wikipedia.org/wiki/Columbus_Blue_Jackets\" title=\"Columbus Blue Jackets\">Columbus</a> (2000), and <a href=\"https://wikipedia.org/wiki/Minnesota_Wild\" title=\"Minnesota Wild\">Minneapolis-Saint Paul</a> (2000).", "links": [{"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "Nashville Predators", "link": "https://wikipedia.org/wiki/Nashville_Predators"}, {"title": "Atlanta Thrashers", "link": "https://wikipedia.org/wiki/Atlanta_Thrashers"}, {"title": "Columbus Blue Jackets", "link": "https://wikipedia.org/wiki/Columbus_Blue_Jackets"}, {"title": "Minnesota Wild", "link": "https://wikipedia.org/wiki/Minnesota_Wild"}]}, {"year": "1998", "text": "In <PERSON> v. City of New York, the United States Supreme Court decides that the Line Item Veto Act of 1996 is unconstitutional.", "html": "1998 - In <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._City_of_New_York\" title=\"<PERSON> v. City of New York\"><PERSON> v. City of New York</a></i>, the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> decides that the <a href=\"https://wikipedia.org/wiki/Line_Item_Veto_Act_of_1996\" title=\"Line Item Veto Act of 1996\">Line Item Veto Act of 1996</a> is <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">unconstitutional</a>.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._City_of_New_York\" title=\"<PERSON> v. City of New York\"><PERSON> v. City of New York</a></i>, the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> decides that the <a href=\"https://wikipedia.org/wiki/Line_Item_Veto_Act_of_1996\" title=\"Line Item Veto Act of 1996\">Line Item Veto Act of 1996</a> is <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">unconstitutional</a>.", "links": [{"title": "<PERSON> v. City of New York", "link": "https://wikipedia.org/wiki/<PERSON>_v._City_of_New_York"}, {"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "Line Item Veto Act of 1996", "link": "https://wikipedia.org/wiki/Line_Item_Veto_Act_of_1996"}, {"title": "Constitution of the United States", "link": "https://wikipedia.org/wiki/Constitution_of_the_United_States"}]}, {"year": "2007", "text": "PMTair Flight 241 crashes in the Dâmrei Mountains in Kampot Province, Cambodia, killing all 22 people on board.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/PMTair_Flight_241\" title=\"PMTair Flight 241\">PMTair Flight 241</a> crashes in the <a href=\"https://wikipedia.org/wiki/D%C3%A2mrei_Mountains\" title=\"Dâmrei Mountains\">Dâmrei Mountains</a> in <a href=\"https://wikipedia.org/wiki/Kampot_Province,_Cambodia\" class=\"mw-redirect\" title=\"Kampot Province, Cambodia\">Kampot Province, Cambodia</a>, killing all 22 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/PMTair_Flight_241\" title=\"PMTair Flight 241\">PMTair Flight 241</a> crashes in the <a href=\"https://wikipedia.org/wiki/D%C3%A2mrei_Mountains\" title=\"Dâmrei Mountains\">Dâmrei Mountains</a> in <a href=\"https://wikipedia.org/wiki/Kampot_Province,_Cambodia\" class=\"mw-redirect\" title=\"Kampot Province, Cambodia\">Kampot Province, Cambodia</a>, killing all 22 people on board.", "links": [{"title": "PMTair Flight 241", "link": "https://wikipedia.org/wiki/PMTair_Flight_241"}, {"title": "Dâmrei Mountains", "link": "https://wikipedia.org/wiki/D%C3%A2mrei_Mountains"}, {"title": "Kampot Province, Cambodia", "link": "https://wikipedia.org/wiki/Kampot_Province,_Cambodia"}]}, {"year": "2022", "text": "The prime minister of Bangladesh, Sheikh <PERSON><PERSON> inaugurates the longest bridge of Bangladesh, Padma Bridge.", "html": "2022 - The prime minister of Bangladesh, <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a> inaugurates the longest bridge of Bangladesh, <a href=\"https://wikipedia.org/wiki/Padma_Bridge\" title=\"Padma Bridge\">Padma Bridge</a>.", "no_year_html": "The prime minister of Bangladesh, <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a> inaugurates the longest bridge of Bangladesh, <a href=\"https://wikipedia.org/wiki/Padma_Bridge\" title=\"Padma Bridge\">Padma Bridge</a>.", "links": [{"title": "Sheikh <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Padma Bridge", "link": "https://wikipedia.org/wiki/Padma_Bridge"}]}, {"year": "2022", "text": "Russo-Ukrainian War: The Battle of Sievierodonetsk ends after weeks of heavy fighting with the Russian capture of the city, leading to the Battle of Lysychansk.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sievierodonetsk_(2022)\" title=\"Battle of Sievierodonetsk (2022)\">Battle of Sievierodonetsk</a> ends after weeks of heavy fighting with the Russian capture of the city, leading to the <a href=\"https://wikipedia.org/wiki/Battle_of_Lysychansk\" title=\"Battle of Lysychansk\">Battle of Lysychansk</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sievierodonetsk_(2022)\" title=\"Battle of Sievierodonetsk (2022)\">Battle of Sievierodonetsk</a> ends after weeks of heavy fighting with the Russian capture of the city, leading to the <a href=\"https://wikipedia.org/wiki/Battle_of_Lysychansk\" title=\"Battle of Lysychansk\">Battle of Lysychansk</a>.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "Battle of Sievierodonetsk (2022)", "link": "https://wikipedia.org/wiki/Battle_of_Sievierodonetsk_(2022)"}, {"title": "Battle of Lysychansk", "link": "https://wikipedia.org/wiki/Battle_of_Lysychansk"}]}, {"year": "2022", "text": "Two people are killed and 21 more injured after a gunman opens fire at three sites in Oslo in a suspected Islamist anti-LGBTQ+ attack.", "html": "2022 - Two people are killed and 21 more injured after a gunman opens fire at three sites in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a> in a <a href=\"https://wikipedia.org/wiki/2022_Oslo_shooting\" title=\"2022 Oslo shooting\">suspected Islamist anti-LGBTQ+ attack</a>.", "no_year_html": "Two people are killed and 21 more injured after a gunman opens fire at three sites in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Oslo</a> in a <a href=\"https://wikipedia.org/wiki/2022_Oslo_shooting\" title=\"2022 Oslo shooting\">suspected Islamist anti-LGBTQ+ attack</a>.", "links": [{"title": "Oslo", "link": "https://wikipedia.org/wiki/Oslo"}, {"title": "2022 Oslo shooting", "link": "https://wikipedia.org/wiki/2022_Oslo_shooting"}]}, {"year": "2024", "text": "Thousands of people storm Kenya's Parliament Buildings protesting the passing of the government's 2024/25 Finance Bill.", "html": "2024 - Thousands of people <a href=\"https://wikipedia.org/wiki/2024_storming_of_the_Parliament_of_Kenya\" class=\"mw-redirect\" title=\"2024 storming of the Parliament of Kenya\">storm Kenya's Parliament Buildings</a> protesting the passing of the government's 2024/25 Finance Bill.", "no_year_html": "Thousands of people <a href=\"https://wikipedia.org/wiki/2024_storming_of_the_Parliament_of_Kenya\" class=\"mw-redirect\" title=\"2024 storming of the Parliament of Kenya\">storm Kenya's Parliament Buildings</a> protesting the passing of the government's 2024/25 Finance Bill.", "links": [{"title": "2024 storming of the Parliament of Kenya", "link": "https://wikipedia.org/wiki/2024_storming_of_the_Parliament_of_Kenya"}]}], "Births": [{"year": "1242", "text": "<PERSON> of England (d. 1275)", "html": "1242 - <a href=\"https://wikipedia.org/wiki/Beatrice_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1275)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beatrice_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1275)", "links": [{"title": "Beatrice of England", "link": "https://wikipedia.org/wiki/Beatrice_of_England"}]}, {"year": "1328", "text": "<PERSON>, 2nd Earl of Salisbury, English commander (d. 1397)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl <PERSON> Salisbury\"><PERSON>, 2nd Earl of Salisbury</a>, English commander (d. 1397)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Salisbury\"><PERSON>, 2nd Earl of Salisbury</a>, English commander (d. 1397)", "links": [{"title": "<PERSON>, 2nd Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Salisbury"}]}, {"year": "1371", "text": "<PERSON> of Naples (d. 1435)", "html": "1371 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1435)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/Joanna_II_of_Naples"}]}, {"year": "1484", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, German banker (d. 1561)", "html": "1484 - <a href=\"https://wikipedia.org/wiki/Bartholomeus_<PERSON>._<PERSON><PERSON>\" title=\"Bartholomeus V<PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, German banker (d. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartholomeus_<PERSON><PERSON>_<PERSON>\" title=\"Bartholomeus <PERSON>\">Bartholome<PERSON> <PERSON><PERSON></a>, German banker (d. 1561)", "links": [{"title": "Barthol<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartholome<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1526", "text": "<PERSON>, Marchioness of Northampton (d. 1565)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Northampton\" class=\"mw-redirect\" title=\"<PERSON>, Marchioness of Northampton\"><PERSON>, Marchioness of Northampton</a> (d. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Northampton\" class=\"mw-redirect\" title=\"<PERSON>, Marchioness of Northampton\"><PERSON>, Marchioness of Northampton</a> (d. 1565)", "links": [{"title": "<PERSON>, Marchioness of Northampton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marchioness_of_Northampton"}]}, {"year": "1560", "text": "<PERSON>, German surgeon (d. 1634)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon (d. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "<PERSON><PERSON>, Queen of Sweden (d. 1597)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/Gun<PERSON>_Bielke\" title=\"Gunilla Bielke\"><PERSON><PERSON></a>, Queen of Sweden (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gun<PERSON>_Bielke\" title=\"Gunilla Bielke\"><PERSON><PERSON></a>, Queen of Sweden (d. 1597)", "links": [{"title": "Gunilla Bielke", "link": "https://wikipedia.org/wiki/Gunilla_Bielke"}]}, {"year": "1612", "text": "<PERSON>, Polish cardinal (d. 1634)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish cardinal (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish cardinal (d. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1632", "text": "<PERSON><PERSON><PERSON>, Venetian statesman and military commander (d. 1690)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/Girolamo_Corner\" title=\"Girolamo Corner\"><PERSON><PERSON><PERSON></a>, Venetian statesman and military commander (d. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Corner\" title=\"Girolamo Corner\"><PERSON><PERSON><PERSON></a>, Venetian statesman and military commander (d. 1690)", "links": [{"title": "Girolamo Corner", "link": "https://wikipedia.org/wiki/Girolamo_Corner"}]}, {"year": "1709", "text": "<PERSON>, Italian composer (d. 1762)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, French soldier and politician, Controller-General of Finances (d. 1789)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9"}, {"title": "Controller-General of Finances", "link": "https://wikipedia.org/wiki/Controller-General_of_Finances"}]}, {"year": "1755", "text": "<PERSON> of Russia (d. 1776)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" class=\"mw-redirect\" title=\"<PERSON> of Russia\"><PERSON> of Russia</a> (d. 1776)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1799", "text": "<PERSON>, Scottish-English botanist and explorer (d. 1834)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, Scottish-English botanist and explorer (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, Scottish-English botanist and explorer (d. 1834)", "links": [{"title": "<PERSON> (botanist)", "link": "https://wikipedia.org/wiki/<PERSON>(botanist)"}]}, {"year": "1814", "text": "<PERSON>, French geologist and engineer (d. 1896)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French geologist and engineer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French geologist and engineer (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1825", "text": "<PERSON>, Australian politician, 8th Premier of New South Wales (d. 1888)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1852", "text": "<PERSON><PERSON>, Spanish architect, designed the <PERSON> G<PERSON>ell (d. 1926)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aud%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish architect, designed the <a href=\"https://wikipedia.org/wiki/Park_G%C3%BCell\" title=\"<PERSON>\"><PERSON></a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aud%C3%AD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish architect, designed the <a href=\"https://wikipedia.org/wiki/Park_G%C3%BCell\" title=\"<PERSON>\"><PERSON></a> (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_Gaud%C3%AD"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Park_G%C3%BCell"}]}, {"year": "1858", "text": "<PERSON>, French author and playwright (d. 1929)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON>, French composer and conductor (d. 1956)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer and conductor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French composer and conductor (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON>, Belgian soldier and diplomat (d. 1935)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian soldier and diplomat (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian soldier and diplomat (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, German chemist and physicist, Nobel Prize laureate (d. 1941)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ernst"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean doctor and Chile's first female physician (d. 1950)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Elo%C3%ADsa_D%C3%ADaz\" title=\"<PERSON>o<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean doctor and Chile's first female physician (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elo%C3%ADsa_D%C3%ADaz\" title=\"<PERSON>o<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean doctor and Chile's first female physician (d. 1950)", "links": [{"title": "Eloísa Díaz", "link": "https://wikipedia.org/wiki/Elo%C3%ADsa_D%C3%ADaz"}]}, {"year": "1874", "text": "<PERSON>,  American cartoonist, illustrator, artist, and writer (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neill\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a>, illustrator, artist, and writer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neill\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a>, illustrator, artist, and writer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_O%27Neill"}, {"title": "Cartoonist", "link": "https://wikipedia.org/wiki/Cartoonist"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier and poet (d. 1917)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni\" title=\"<PERSON><PERSON><PERSON> Gyóni\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and poet (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni\" title=\"<PERSON><PERSON><PERSON> Gyóni\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and poet (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, German-French art collector and historian (d. 1979)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-French art collector and historian (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-French art collector and historian (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American general (d. 1950)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American director, producer, and screenwriter (d. 1995)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Hungarian author, poet, and journalist (d. 1938)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, poet, and journalist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, poet, and journalist (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Japanese microbiologist and general (d. 1959)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Shir%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese microbiologist and general (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shir%C5%8D_<PERSON>hi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese microbiologist and general (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shir%C5%8D_Ishii"}]}, {"year": "1894", "text": "<PERSON>, Romanian-German physicist and engineer (d. 1989)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German physicist and engineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German physicist and engineer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American painter and poet (d. 1963)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Italian actress (d. 1988)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Ukrainian/Soviet astronomer (d. 1969)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian/Soviet astronomer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian/Soviet astronomer (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1900", "text": "<PERSON>, American silent film actress and real estate investor (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Georgia_Hale\" title=\"Georgia Hale\"><PERSON></a>, American silent film actress and real estate investor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Hale\" title=\"Georgia Hale\"><PERSON></a>, American silent film actress and real estate investor (d. 1985)", "links": [{"title": "Georgia Hale", "link": "https://wikipedia.org/wiki/Georgia_Hale"}]}, {"year": "1900", "text": "<PERSON>, 1st Earl <PERSON> of Burma, English admiral and politician, 44th Governor-General of India (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\"><PERSON>, 1st <PERSON> of Burma</a>, English admiral and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\"><PERSON>, 1st <PERSON> of Burma</a>, English admiral and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (d. 1979)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> of Burma", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1901", "text": "<PERSON>, American businessman and politician, 47th Mayor of Kansas City (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Kansas_City,_Missouri\" class=\"mw-redirect\" title=\"List of mayors of Kansas City, Missouri\">Mayor of Kansas City</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Kansas_City,_Missouri\" class=\"mw-redirect\" title=\"List of mayors of Kansas City, Missouri\">Mayor of Kansas City</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Kansas City, Missouri", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Kansas_City,_Missouri"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON> of Japan (d. 1953)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, <PERSON></a> of Japan (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Prince_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, <PERSON></a> of Japan (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1903", "text": "<PERSON>, British novelist, essayist, and critic (d. 1950)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, essayist, and critic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, essayist, and critic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American actress (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German-American astronomer and academic (d. 1976)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American astronomer and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American astronomer and academic (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON> <PERSON>, German physicist and academic, Nobel Prize laureate (d. 1973)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1908", "text": "<PERSON>, American philosopher and academic (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American chemist and biologist, Nobel Prize laureate (d. 1980)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1912", "text": "<PERSON>, American lawyer and politician, 46th Governor of New Jersey (d. 1996)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1913", "text": "<PERSON>, English actor and screenwriter (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Canadian-American wrestler and trainer (d. 1990)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>\" title=\"Whipper <PERSON>\">Whipper <PERSON></a>, Canadian-American wrestler and trainer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>\" title=\"Whipper <PERSON>\">Whipper <PERSON></a>, Canadian-American wrestler and trainer (d. 1990)", "links": [{"title": "Whip<PERSON>", "link": "https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Swedish skier (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish skier (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French author (d. 2018)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON>, English soldier and author (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English soldier and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English soldier and author (d. 1997)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American actress (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English-Canadian ballerina and choreographer, founded the National Ballet of Canada (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ballerina and choreographer, founded the <a href=\"https://wikipedia.org/wiki/National_Ballet_of_Canada\" title=\"National Ballet of Canada\">National Ballet of Canada</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ballerina and choreographer, founded the <a href=\"https://wikipedia.org/wiki/National_Ballet_of_Canada\" title=\"National Ballet of Canada\">National Ballet of Canada</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Ballet of Canada", "link": "https://wikipedia.org/wiki/National_Ballet_of_Canada"}]}, {"year": "1922", "text": "<PERSON>, American guitarist and songwriter (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier and painter (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, 43rd Prime Minister of Iran (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Jam<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 43rd Prime Minister of Iran (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jam<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 43rd Prime Minister of Iran (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American director, producer, and screenwriter (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Bulgarian football player", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Iraqi-Indian composer and director (d. 1975)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Iraqi-Indian composer and director (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Iraqi-Indian composer and director (d. 1975)", "links": [{"title": "<PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1924", "text": "<PERSON>, American lawyer and judge (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress", "html": "1925 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American architect and academic (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress and businesswoman (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Virginia_Patton\" title=\"<PERSON> Patton\"><PERSON></a>, American actress and businesswoman (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Patton\" title=\"<PERSON> Patton\"><PERSON></a>, American actress and businesswoman (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Patton"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Austrian author and poet (d. 1973)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Ingeborg_Bachmann\" title=\"Ingeborg Bachmann\"><PERSON><PERSON><PERSON></a>, Austrian author and poet (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_Bachmann\" title=\"Ingeborg Bachmann\"><PERSON><PERSON><PERSON></a>, Austrian author and poet (d. 1973)", "links": [{"title": "Ingeborg Bachmann", "link": "https://wikipedia.org/wiki/Ingeborg_Bachmann"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Australian lawyer, judge, and politician, 23rd Attorney-General for Australia (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Kep_Enderby\" title=\"Kep Enderby\"><PERSON><PERSON></a>, Australian lawyer, judge, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kep_Enderby\" title=\"Kep Enderby\"><PERSON><PERSON></a>, Australian lawyer, judge, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Attorney-General_for_Australia\" class=\"mw-redirect\" title=\"Attorney-General for Australia\">Attorney-General for Australia</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ke<PERSON>_Enderby"}, {"title": "Attorney-General for Australia", "link": "https://wikipedia.org/wiki/Attorney-General_for_Australia"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Swedish Alpine skier (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Stig_Sollander\" title=\"Stig Sollander\"><PERSON><PERSON></a>, Swedish Alpine skier (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stig_Sollander\" title=\"Stig Sollander\"><PERSON><PERSON></a>, Swedish Alpine skier (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stig_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Hungarian runner (d. 1970)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Antal_R%C3%B3ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian runner (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antal_R%C3%B3ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian runner (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antal_R%C3%B3ka"}]}, {"year": "1927", "text": "<PERSON>, English astronomer and academic (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Russian-American physicist and academic, Nobel Prize laureate (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1928", "text": "<PERSON>, Canadian director, producer, and screenwriter (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Belgian author and illustrator, created <PERSON> Smurfs (d. 1992)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>ey<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/The_Smurfs\" title=\"The Smurfs\">The Smurfs</a></i> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ey<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/The_Smurfs\" title=\"The Smurfs\">The Smurfs</a></i> (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eyo"}, {"title": "The Smurfs", "link": "https://wikipedia.org/wiki/The_Smurfs"}]}, {"year": "1929", "text": "<PERSON>, American author and illustrator (d. 2021) ", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2021) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2021) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Italian cardinal (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 7th Prime Minister of India (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2008)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1932", "text": "<PERSON>, English painter and illustrator", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1932", "text": "<PERSON>, French-Dutch director, producer, and screenwriter (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Dutch director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Dutch director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Portuguese architect, designed the Porto School of Architecture", "html": "1933 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese architect, designed the <a href=\"https://wikipedia.org/wiki/Porto_School_of_Architecture\" title=\"Porto School of Architecture\">Porto School of Architecture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese architect, designed the <a href=\"https://wikipedia.org/wiki/Porto_School_of_Architecture\" title=\"Porto School of Architecture\">Porto School of Architecture</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Porto School of Architecture", "link": "https://wikipedia.org/wiki/Porto_School_of_Architecture"}]}, {"year": "1934", "text": "<PERSON>, American baseball player (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American minister and author (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Mexican actress and director (d. 2006)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and director (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and director (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English television producer and director (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television producer and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television producer and director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Nigerian Army Officer (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian Army Officer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian Army Officer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Indonesian poet and activist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian poet and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian poet and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American author, playwright, and activist, co-founded Gay Men's Health Crisis (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and activist, co-founded <a href=\"https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis\" class=\"mw-redirect\" title=\"Gay Men's Health Crisis\">Gay Men's Health Crisis</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Gay Men's Health Crisis", "link": "https://wikipedia.org/wiki/Gay_Men%27s_Health_Crisis"}]}, {"year": "1935", "text": "<PERSON>, American professional baseball player (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional baseball player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English racing driver (d. 2004)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American artistic gymnast", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artistic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English-American mathematician, physicist, and author (d. 2002)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, English-American mathematician, physicist, and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, English-American mathematician, physicist, and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indonesian engineer and politician, 3rd President of Indonesia (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1936", "text": "<PERSON>, German biologist and entomologist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lldobler\" title=\"<PERSON>\"><PERSON></a>, German biologist and entomologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lldobler\" title=\"<PERSON>\"><PERSON></a>, German biologist and entomologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bert_H%C3%B6lldobler"}]}, {"year": "1937", "text": "<PERSON>, American R&B/soul singer-songwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/soul singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Floyd\"><PERSON></a>, American R&amp;B/soul singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, <PERSON> of Bishop Auckland, English politician (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Bishop_Auckland\" title=\"<PERSON>, <PERSON> of Bishop Auckland\"><PERSON>, Baron <PERSON> of Bishop Auckland</a>, English politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Bishop_Auckland\" title=\"<PERSON>, Baron <PERSON> of Bishop Auckland\"><PERSON>, Baron <PERSON> of Bishop Auckland</a>, English politician (d. 2019)", "links": [{"title": "<PERSON>, <PERSON> of Bishop Auckland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Bishop_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English ballerina and actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English ballerina and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English ballerina and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American tennis player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian runner", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actress and singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-Maltese author (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Maltese author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Maltese author (d. 2005)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English bass player (d. 2004)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Canadian director, producer, and screenwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Arcand\"><PERSON><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Arcand\"><PERSON><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denys_Arcand"}]}, {"year": "1941", "text": "<PERSON>, Scottish academic and ecologist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and ecologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish academic and ecologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English actress (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek academic and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Nikif<PERSON><PERSON>_<PERSON>amando<PERSON>s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>aman<PERSON>s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikif<PERSON>s_Diamandouros"}]}, {"year": "1942", "text": "<PERSON>, American basketball player, coach, and manager (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, coach, and manager (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian author and playwright", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American screenwriter and producer (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Nigerian politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Baba_<PERSON>ana_Kingibe\" title=\"Baba Gana Kingibe\"><PERSON></a>, Nigerian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baba_<PERSON>ana_Kingibe\" title=\"Baba Gana Kingibe\"><PERSON></a>, Nigerian politician", "links": [{"title": "Baba Gana Kingibe", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>ibe"}]}, {"year": "1945", "text": "<PERSON>, American singer (d. 1974)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch-Canadian general and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Rom%C3%A9<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Canadian general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Canadian general and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rom%C3%A9o_<PERSON><PERSON>e"}]}, {"year": "1946", "text": "<PERSON>, American guitarist and songwriter (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English guitarist and saxophonist (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and saxophonist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English guitarist and saxophonist (d. 2022)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, American discus thrower (d. 2022)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower (d. 2022)", "links": [{"title": "<PERSON> (discus thrower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Irish archbishop", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Irish archbishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1949", "text": "<PERSON>, French racing driver (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, South Korean actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-sang\" title=\"<PERSON><PERSON>-sang\"><PERSON><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-sang\" title=\"<PERSON><PERSON>-sang\"><PERSON><PERSON>-<PERSON></a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>-sang", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-sang"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Italian author and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian author and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1951", "text": "<PERSON>, Swiss mathematician and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Hungarian cardinal", "html": "1952 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Erd%C5%91\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Erd%C5%91\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian cardinal", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_Erd%C5%91"}]}, {"year": "1952", "text": "<PERSON>, New Zealand singer-songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Finn\"><PERSON></a>, New Zealand singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, German singer-songwriter and keyboard player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Swedish artist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French-American cardiologist and educator (d. 2013)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American cardiologist and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American cardiologist and educator (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian cricketer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1955", "text": "<PERSON>, English cricketer and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Marks"}]}, {"year": "1956", "text": "<PERSON>, American chef and author (d. 2018)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Macedonian politician, 2nd President of the Republic of Macedonia (d. 2004)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Macedonian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Macedonian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia\" class=\"mw-redirect\" title=\"President of the Republic of Macedonia\">President of the Republic of Macedonia</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the Republic of Macedonia", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Macedonia"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, German long jumper and educator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German long jumper and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German long jumper and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Finnish ski jumper", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ski jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Australian astrophysicist and astronomer (d. 1996)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ail<PERSON>\" title=\"<PERSON><PERSON> Vaile\"><PERSON><PERSON></a>, Australian astrophysicist and astronomer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ail<PERSON>\" title=\"<PERSON><PERSON> Vaile\"><PERSON><PERSON></a>, Australian astrophysicist and astronomer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON> of Crionaich, English-Scottish journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Crionaich\" title=\"<PERSON><PERSON><PERSON> of Crionaich\"><PERSON><PERSON><PERSON> of Crionaich</a>, English-Scottish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Crionaich\" title=\"<PERSON><PERSON><PERSON> of Crionaich\"><PERSON><PERSON><PERSON> of Crionaich</a>, English-Scottish journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON> of Crionaich", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Crionaic<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, South African-Australian footballer and photographer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian footballer and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian footballer and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Kazakh director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakh director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakh director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English comedian, actor, director, producer and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, director, producer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, director, producer and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Spanish-born Canadian author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-born Canadian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-born Canadian author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter and producer (d. 2016)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English racing driver and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and musician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, French politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Napole_Polutele\" title=\"Napole Polutele\"><PERSON><PERSON></a>, French politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napole_Polutele\" title=\"Napole Polutele\"><PERSON><PERSON></a>, French politician", "links": [{"title": "Na<PERSON>", "link": "https://wikipedia.org/wiki/Napole_Polutele"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Australian beach volleyball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian beach volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian beach volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Congolese-American basketball player (d. 2024)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mutom<PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese-American basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mutombo\"><PERSON><PERSON><PERSON></a>, Congolese-American basketball player (d. 2024)", "links": [{"title": "Dikembe Mutombo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Australian journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>picer\"><PERSON><PERSON></a>, Australian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Spicer\"><PERSON><PERSON></a>, Australian journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1968", "text": "<PERSON>, Zimbabwean-South African rugby player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ai<PERSON>_<PERSON>is"}]}, {"year": "1969", "text": "<PERSON>, American football coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Finnish guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oop<PERSON>_<PERSON>a"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Estonian decathlete and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>ol\"><PERSON><PERSON><PERSON></a>, Estonian decathlete and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ol\"><PERSON><PERSON><PERSON></a>, Estonian decathlete and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>ol"}]}, {"year": "1971", "text": "<PERSON>, English cyclist and author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cyclist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian-English cricketer and educator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English cricketer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Northern Irish-Scottish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish-Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1972", "text": "<PERSON>, Puerto Rican baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Libyan engineer and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Islam_Gaddafi\" title=\"<PERSON><PERSON> al-Islam Gaddafi\"><PERSON><PERSON> <PERSON><PERSON></a>, Libyan engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Islam_Gaddafi\" title=\"<PERSON>f al-Islam Gaddafi\"><PERSON><PERSON> <PERSON><PERSON></a>, Libyan engineer and politician", "links": [{"title": "<PERSON><PERSON>adda<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_al-Islam_Gaddafi"}]}, {"year": "1973", "text": "<PERSON>, Czech ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Milan_Hnili%C4%8Dka\" title=\"Milan Hnilička\">Milan Hnilička</a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Hnili%C4%8Dka\" title=\"Milan Hnilička\"><PERSON>lič<PERSON></a>, Czech ice hockey player", "links": [{"title": "Milan Hnilička", "link": "https://wikipedia.org/wiki/Milan_Hnili%C4%8Dka"}]}, {"year": "1973", "text": "<PERSON>, English footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian director, producer, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> G<PERSON>tra\"><PERSON><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> G<PERSON>tra\"><PERSON><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>tra"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Glen_Metropolit\" title=\"Glen Metropolit\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_Metropolit\" title=\"Glen Metropolit\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Glen Metropolit", "link": "https://wikipedia.org/wiki/Glen_Metropolit"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian journalist and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Spanish tennis player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Russian chess player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>nik"}]}, {"year": "1975", "text": "<PERSON>, American model and television host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Uruguayan footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>a"}]}, {"year": "1976", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1976", "text": "<PERSON>, American swimmer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_(swimmer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ram%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ram%C3%ADrez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aramis_Ram%C3%ADrez"}]}, {"year": "1979", "text": "<PERSON>, Scottish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_Philipps"}]}, {"year": "1981", "text": "<PERSON>, Swiss ski jumper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, South Korean singer and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entertainer)\" title=\"Rain (entertainer)\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rain_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_(entertainer)"}]}, {"year": "1982", "text": "<PERSON>, Russian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Austrian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Algerian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Swedish ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Enroth"}]}, {"year": "1988", "text": "<PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAn\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAn"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Norwegian cross-country skier", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian cross-country skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Filipino actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Brazilian-American race car driver", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Australian swimmer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Swedish professional ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish professional ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish professional ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boone\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "635", "text": "<PERSON>, Chinese emperor (b. 566)", "html": "635 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON></a>, Chinese emperor (b. 566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON></a>, Chinese emperor (b. 566)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_<PERSON>_Tang"}]}, {"year": "841", "text": "<PERSON> of Auvergne, Frankish nobleman", "html": "841 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Auvergne\" title=\"<PERSON>, Count of Auvergne\"><PERSON> of Auvergne</a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Auvergne\" title=\"<PERSON>, Count of Auvergne\"><PERSON> of Auvergne</a>, Frankish nobleman", "links": [{"title": "<PERSON>, Count of Auvergne", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Auvergne"}]}, {"year": "841", "text": "<PERSON><PERSON><PERSON> of Nantes, Frankish nobleman", "html": "841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nantes\" title=\"<PERSON><PERSON><PERSON> of Nantes\"><PERSON><PERSON><PERSON> of Nantes</a>, Frankish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nantes\" title=\"<PERSON><PERSON><PERSON> of Nantes\"><PERSON><PERSON><PERSON> of Nantes</a>, Frankish nobleman", "links": [{"title": "<PERSON><PERSON><PERSON> of Nantes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nantes"}]}, {"year": "891", "text": "<PERSON><PERSON><PERSON>, German archbishop", "html": "891 - <a href=\"https://wikipedia.org/wiki/Sunderolt\" title=\"Sunder<PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sunderolt\" title=\"Sunder<PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>derolt"}]}, {"year": "931", "text": "<PERSON>, Chinese general", "html": "931 - <a href=\"https://wikipedia.org/wiki/An_Chonghui\" title=\"An Chonghui\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An_Chonghui\" title=\"An Chonghui\"><PERSON></a>, Chinese general", "links": [{"title": "An Chonghui", "link": "https://wikipedia.org/wiki/An_Chonghui"}]}, {"year": "1014", "text": "<PERSON><PERSON><PERSON><PERSON>, son of <PERSON><PERSON><PERSON><PERSON> the Unready", "html": "1014 - <a href=\"https://wikipedia.org/wiki/%C3%86thelstan_%C3%86theling\" title=\"Æthelstan Ætheling\"><PERSON><PERSON><PERSON><PERSON>the<PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/%C3%86thelred_the_Unready\" title=\"<PERSON><PERSON><PERSON><PERSON> the Unready\"><PERSON><PERSON><PERSON><PERSON> the Unready</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86thelstan_%C3%86theling\" title=\"Æthelstan Ætheling\"><PERSON><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/%C3%86thelred_the_Unready\" title=\"<PERSON><PERSON><PERSON><PERSON> the Unready\"><PERSON><PERSON><PERSON><PERSON> the Unready</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%86thelstan_%C3%86theling"}, {"title": "<PERSON><PERSON><PERSON><PERSON> the Unready", "link": "https://wikipedia.org/wiki/%C3%86thelred_the_Unready"}]}, {"year": "1031", "text": "<PERSON><PERSON>, Chinese emperor (b. 972)", "html": "1031 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (b. 972)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao"}]}, {"year": "1134", "text": "<PERSON><PERSON>, king of Denmark (b. 1065)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Denmark\" title=\"<PERSON><PERSON>, King of Denmark\"><PERSON><PERSON></a>, king of Denmark (b. 1065)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Denmark\" title=\"<PERSON><PERSON>, King of Denmark\"><PERSON><PERSON></a>, king of Denmark (b. 1065)", "links": [{"title": "<PERSON><PERSON>, King of Denmark", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Denmark"}]}, {"year": "1218", "text": "<PERSON>, 5th Earl of Leicester, French politician, Lord <PERSON> (b. 1160)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Leicester\" title=\"<PERSON>, 5th Earl of Leicester\"><PERSON>, 5th Earl of Leicester</a>, French politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward</a> (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Leicester\" title=\"<PERSON>, 5th Earl of Leicester\"><PERSON>, 5th Earl of Leicester</a>, French politician, <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_Steward\" title=\"Lord <PERSON> Steward\">Lord <PERSON> Steward</a> (b. 1160)", "links": [{"title": "<PERSON>, 5th Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Leicester"}, {"title": "Lord High Steward", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Steward"}]}, {"year": "1291", "text": "<PERSON> of Provence, queen of England (b. 1223)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/Eleanor_of_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a>, queen of England (b. 1223)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleanor_of_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a>, queen of England (b. 1223)", "links": [{"title": "<PERSON> of Provence", "link": "https://wikipedia.org/wiki/Eleanor_of_Provence"}]}, {"year": "1337", "text": "<PERSON>, king of Sicily (b. 1272)", "html": "1337 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> III</a>, king of Sicily (b. 1272)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> III</a>, king of Sicily (b. 1272)", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}]}, {"year": "1394", "text": "<PERSON> of Montau, German hermitess (b. 1347)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Montau\"><PERSON> of <PERSON></a>, German hermitess (b. 1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Montau\"><PERSON> of <PERSON></a>, German hermitess (b. 1347)", "links": [{"title": "<PERSON> of Montau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1483", "text": "<PERSON>, 2nd Earl <PERSON>, English courtier and translator (b. 1440)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_<PERSON>_<PERSON>\" title=\"<PERSON>, 2nd <PERSON>\"><PERSON>, 2nd <PERSON></a>, English courtier and translator (b. 1440)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_<PERSON>_<PERSON>\" title=\"<PERSON>, 2nd <PERSON>\"><PERSON>, 2nd <PERSON></a>, English courtier and translator (b. 1440)", "links": [{"title": "<PERSON>, 2nd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_<PERSON>_<PERSON>"}]}, {"year": "1483", "text": "<PERSON>, half brother of <PERSON> of England (b. 1458)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, half brother of <a href=\"https://wikipedia.org/wiki/Edward_V_of_England\" class=\"mw-redirect\" title=\"<PERSON> V of England\"><PERSON> of England</a> (b. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, half brother of <a href=\"https://wikipedia.org/wiki/Edward_V_of_England\" class=\"mw-redirect\" title=\"<PERSON> V of England\"><PERSON> of England</a> (b. 1458)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Edward_V_of_England"}]}, {"year": "1522", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and theorist (b. 1451)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>fu<PERSON>\" title=\"<PERSON>an<PERSON><PERSON> Gaffurius\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>an<PERSON><PERSON> Gaffurius\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1451)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1533", "text": "<PERSON>, queen of France (b. 1496)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON></a>, queen of France (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France\" title=\"<PERSON>, Queen of France\"><PERSON></a>, queen of France (b. 1496)", "links": [{"title": "<PERSON>, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_France"}]}, {"year": "1579", "text": "<PERSON><PERSON>, Japanese warlord (b. 1541)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Hatano_Hideharu\" title=\"Hatano Hideharu\"><PERSON><PERSON></a>, Japanese warlord (b. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hatano_Hideharu\" title=\"Hatano Hideharu\"><PERSON><PERSON></a>, Japanese warlord (b. 1541)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hat<PERSON>_Hideharu"}]}, {"year": "1593", "text": "<PERSON>, Italian physician and archaeologist (b. 1541)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and archaeologist (b. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and archaeologist (b. 1541)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1634", "text": "<PERSON>, English poet and playwright (b. 1576)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English poet and playwright (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English poet and playwright (b. 1576)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "1638", "text": "<PERSON>, Spanish author, poet, and playwright (b. 1602)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_de_Montalb%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Spanish author, poet, and playwright (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_de_Montalb%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Spanish author, poet, and playwright (b. 1602)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_P%C3%A9<PERSON>_de_Montalb%C3%A1n"}]}, {"year": "1665", "text": "<PERSON><PERSON><PERSON>, archduke of Austria (b. 1630)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON><PERSON><PERSON>, Archduke of Austria\"><PERSON><PERSON><PERSON></a>, archduke of Austria (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON><PERSON><PERSON>, Archduke of Austria\"><PERSON><PERSON><PERSON></a>, archduke of Austria (b. 1630)", "links": [{"title": "<PERSON><PERSON><PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>ke_of_Austria"}]}, {"year": "1669", "text": "<PERSON>, duke of Beaufort (b. 1616)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_de_Vend%C3%B4me,_Duke_<PERSON>_Beaufort\" class=\"mw-redirect\" title=\"<PERSON>, Duke <PERSON> Beaufort\"><PERSON></a>, duke of Beaufort (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_Vend%C3%B4me,_Duke_<PERSON>_Beaufort\" class=\"mw-redirect\" title=\"<PERSON>, Duke <PERSON>\"><PERSON></a>, duke of Beaufort (b. 1616)", "links": [{"title": "<PERSON>, Duke of Beaufort", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_de_Vend%C3%B4me,_<PERSON>_of_Beaufort"}]}, {"year": "1671", "text": "<PERSON>, Italian priest and astronomer (b. 1598)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and astronomer (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and astronomer (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON><PERSON><PERSON><PERSON>, French captain (b. 1611)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Castelmore_d%27Artagnan\" class=\"mw-redirect\" title=\"<PERSON>more d'Artagnan\"><PERSON>'Artagnan</a>, French captain (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Castelmore_d%27Artagnan\" class=\"mw-redirect\" title=\"<PERSON>more d'Artagnan\"><PERSON>'Artagnan</a>, French captain (b. 1611)", "links": [{"title": "<PERSON>Castelmore d'Artagnan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Castelmore_d%27Artagnan"}]}, {"year": "1686", "text": "<PERSON>, Russian painter and educator (b. 1626)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1715", "text": "<PERSON><PERSON><PERSON>, French admiral and politician (b. 1646)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral and politician (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French admiral and politician (b. 1646)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, German composer and theorist (b. 1681)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (b. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, English cartographer, painter, and architect (b. 1721)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartographer, painter, and architect (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartographer, painter, and architect (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, German composer, critic, and jurist (b. 1776)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, German composer, critic, and jurist (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, German composer, critic, and jurist (b. 1776)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, American educator (b. 1746)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator (b. 1746)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French general and engineer (b. 1774)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and engineer (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French general and engineer (b. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>-<PERSON>%C3%AEt_Haxo"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman sultan (b. 1823)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Abd%C3%BClmecid_I\" title=\"<PERSON><PERSON><PERSON>ec<PERSON> I\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abd%C3%BClmecid_I\" title=\"<PERSON><PERSON><PERSON>ec<PERSON> I\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1823)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abd%C3%BClmecid_I"}]}, {"year": "1866", "text": "<PERSON>, Finnish biologist and paleontologist (b. 1803)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish biologist and paleontologist (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish biologist and paleontologist (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Italian physicist and neurophysiologist (b. 1811)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and neurophysiologist (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and neurophysiologist (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American lawyer and politician (b. 1823)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, French sculptor (b. 1796)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor (b. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American lieutenant (b. 1845)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, American lieutenant (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, American lieutenant (b. 1845)", "links": [{"title": "<PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)"}]}, {"year": "1876", "text": "<PERSON>, American civilian army contractor (b. 1848)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Boston_Custer\" title=\"Boston Custer\">Boston Custer</a>, American civilian army contractor (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boston_Custer\" title=\"Boston Custer\">Boston Custer</a>, American civilian army contractor (b. 1848)", "links": [{"title": "Boston Custer", "link": "https://wikipedia.org/wiki/Boston_Custer"}]}, {"year": "1876", "text": "<PERSON>, American general (b. 1839)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American officer, Medal of Honor recipient (b. 1845)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American officer, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American officer, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Irish-American officer (b. 1840)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American officer (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American officer (b. 1840)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1882", "text": "<PERSON>, French sculptor (b. 1806)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Austrian organist and composer (b. 1858)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, Canadian businessman and politician, 11th Mayor of Montreal (b. 1809)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician, 11th <a href=\"https://wikipedia.org/wiki/Mayor_of_Montreal\" title=\"Mayor of Montreal\">Mayor of Montreal</a> (b. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Mayor of Montreal", "link": "https://wikipedia.org/wiki/Mayor_of_Montreal"}]}, {"year": "1894", "text": "<PERSON>, French engineer and politician, 5th President of France (b. 1837)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>not"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1906", "text": "<PERSON>, American architect, designed the Washington Square Arch (b. 1853)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"Stanford White\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Washington_Square_Arch\" title=\"Washington Square Arch\">Washington Square Arch</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"Stanford White\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Washington_Square_Arch\" title=\"Washington Square Arch\">Washington Square Arch</a> (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stanford_White"}, {"title": "Washington Square Arch", "link": "https://wikipedia.org/wiki/Washington_Square_Arch"}]}, {"year": "1912", "text": "<PERSON>, Dutch-British painter (b. 1836)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-British painter (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-British painter (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American painter, photographer, and sculptor (b. 1844)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, photographer, and sculptor (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, photographer, and sculptor (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier and poet (b. 1884)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni\" title=\"<PERSON><PERSON><PERSON> Gyóni\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and poet (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni\" title=\"<PERSON><PERSON><PERSON> Gyóni\"><PERSON><PERSON><PERSON></a>, Hungarian soldier and poet (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9za_Gy%C3%B3ni"}]}, {"year": "1918", "text": "<PERSON>, American baseball player and coach (b. 1867)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and author (b. 1882)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, British actor (b. 1900)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English race car driver (b. 1913)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German Jewish left-wing activist (c. 1887)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish left-wing activist (c. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Jewish left-wing activist (c. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Hungarian jurist and politician, 18th Prime Minister of Hungary (b. 1871)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/D%C3%A9nes_Be<PERSON>key\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9nes_Be<PERSON>key\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian jurist and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9nes_Berinkey"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Mexican singer and actress (b. 1906)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Mexican_singer)\" title=\"<PERSON><PERSON> (Mexican singer)\"><PERSON><PERSON></a>, Mexican singer and actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Mexican_singer)\" title=\"<PERSON><PERSON> (Mexican singer)\"><PERSON><PERSON></a>, Mexican singer and actress (b. 1906)", "links": [{"title": "<PERSON><PERSON> (Mexican singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Mexican_singer)"}]}, {"year": "1947", "text": "<PERSON>, American boxer (b. 1924)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American general (b. 1895)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American baseball player (b. 1871)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American water polo player (b. 1876)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)\" title=\"<PERSON> (water polo)\"><PERSON></a>, American water polo player (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)\" title=\"<PERSON> (water polo)\"><PERSON></a>, American water polo player (b. 1876)", "links": [{"title": "<PERSON> (water polo)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(water_polo)"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Irish police officer and author (b. 1904)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>is_%C3%93_S%C3%BAilleabh%C3%A1in\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish police officer and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C3%93_S%C3%BAilleabh%C3%A1in\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish police officer and author (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Muiris_%C3%93_S%C3%BAilleabh%C3%A1in"}]}, {"year": "1958", "text": "<PERSON>, English author, poet, and playwright (b. 1880)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American spree killer (b. 1938)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American spree killer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American spree killer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and manager (b. 1869)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1869)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1968", "text": "<PERSON>, English comedian and actor (b. 1924)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, 1st Baron <PERSON>, Scottish physician, biologist, and politician, Nobel Prize laureate (b. 1880)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish physician, biologist, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Scottish physician, biologist, and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1880)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>-<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1972", "text": "<PERSON>, Czech-American painter and illustrator (b. 1890)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American painter and illustrator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American painter and illustrator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Hungarian mathematician and physicist (b. 1893)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian mathematician and physicist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian mathematician and physicist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter, co-founded Capitol Records (b. 1909)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, co-founded <a href=\"https://wikipedia.org/wiki/Capitol_Records\" title=\"Capitol Records\">Capitol Records</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, co-founded <a href=\"https://wikipedia.org/wiki/Capitol_Records\" title=\"Capitol Records\">Capitol Records</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Capitol Records", "link": "https://wikipedia.org/wiki/Capitol_Records"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, British Girl Guiding and Girl Scouting leader (b. 1889)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Girl_Guiding_and_Girl_Scouting\" class=\"mw-redirect\" title=\"Girl Guiding and Girl Scouting\">Girl Guiding and Girl Scouting</a> leader (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Girl_Guiding_and_Girl_Scouting\" class=\"mw-redirect\" title=\"Girl Guiding and Girl Scouting\">Girl Guiding and Girl Scouting</a> leader (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>-Powell", "link": "https://wikipedia.org/wiki/<PERSON>lave_Baden-Powell"}, {"title": "Girl Guiding and Girl Scouting", "link": "https://wikipedia.org/wiki/Girl_Guiding_and_Girl_Scouting"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Hungarian pianist and composer (b. 1911)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Endre_Szerv%C3%A1nszky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian pianist and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Endre_Szerv%C3%A1nszky\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian pianist and composer (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Endre_Szerv%C3%A1nszky"}]}, {"year": "1979", "text": "<PERSON>, American animator, director, and producer (b. 1894)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Latvian-American photographer (b. 1906)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American photographer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American photographer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Peruvian painter and political activist (b. 1888)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADo_del_Pomar\" title=\"<PERSON>ss<PERSON> del Pomar\"><PERSON></a>, Peruvian painter and political activist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD<PERSON>_del_Pomar\" title=\"<PERSON> Pomar\"><PERSON></a>, Peruvian painter and political activist (b. 1888)", "links": [{"title": "Felipe <PERSON>", "link": "https://wikipedia.org/wiki/Felipe_Coss%C3%<PERSON><PERSON>_del_Pomar"}]}, {"year": "1983", "text": "<PERSON>, Argentinian pianist and composer (b. 1916)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French historian and philosopher (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and philosopher (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Israeli-American guitarist and songwriter (b. 1962)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hillel Slovak\"><PERSON><PERSON></a>, Israeli-American guitarist and songwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Hillel Slovak\"><PERSON><PERSON></a>, Israeli-American guitarist and songwriter (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Slovak"}]}, {"year": "1990", "text": "<PERSON>, American sergeant and murderer (b. 1940)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and murderer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and murderer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player (b. 1965)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Fifteenth Chief Justice of the United States (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fifteenth Chief Justice of the United States (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Fifteenth Chief Justice of the United States (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Irish physicist and academic, Nobel Prize laureate (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1996", "text": "<PERSON>, English civil servant and diplomat, British Ambassador to South Africa (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to South Africa\">British Ambassador to South Africa</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to South Africa\">British Ambassador to South Africa</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ing"}, {"title": "List of High Commissioners of the United Kingdom to South Africa", "link": "https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa"}]}, {"year": "1997", "text": "<PERSON>, French oceanographer and explorer (b. 1910)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French oceanographer and explorer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French oceanographer and explorer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American real estate developer and businessman (b. 1905)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate developer and businessman (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real estate developer and businessman (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian politician, 29th Canadian Minister of Labour (b. 1934)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "2003", "text": "<PERSON>, American businessman and politician, 75th Governor of Georgia (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "2004", "text": "<PERSON>, New Zealand inventor (b. 1904)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Co<PERSON>s\"><PERSON></a>, New Zealand inventor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand inventor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor and voice artist (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish singer-songwriter and activist (b. 1971)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Koyuncu\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Koyuncu\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and activist (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Koyuncu\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Koyuncu\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and activist (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A2z%C4%B1m_Koyuncu"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Dutch-American humanitarian (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_<PERSON>at\" title=\"<PERSON>aa<PERSON>ra<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American humanitarian (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>_<PERSON>\" title=\"<PERSON>aa<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch-American humanitarian (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaap_<PERSON>at"}]}, {"year": "2007", "text": "<PERSON><PERSON> <PERSON>, American journalist and educator (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and educator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist and educator (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Indian director, cinematographer, and screenwriter (b. 1963)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Indian director, cinematographer, and screenwriter (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Indian director, cinematographer, and screenwriter (b. 1963)", "links": [{"title": "<PERSON><PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(director)"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, South African anthropologist and ethologist (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African anthropologist and ethologist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African anthropologist and ethologist (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American actress and producer (b. 1947)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Farrah_Faw<PERSON>tt\" title=\"<PERSON><PERSON> Fawcett\"><PERSON><PERSON></a>, American actress and producer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Far<PERSON>_Faw<PERSON>tt\" title=\"<PERSON><PERSON> Fawcett\"><PERSON><PERSON></a>, American actress and producer (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Far<PERSON>_<PERSON>tt"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter, producer, dancer, and actor (b. 1958)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, dancer, and actor (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, dancer, and actor (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter (b. 1937)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Sky_Saxon\" title=\"Sky Saxon\"><PERSON></a>, American singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sky_Saxon\" title=\"Sky Saxon\"><PERSON></a>, American singer-songwriter (b. 1937)", "links": [{"title": "Sky Saxon", "link": "https://wikipedia.org/wiki/Sky_Saxon"}]}, {"year": "2010", "text": "<PERSON>, English playwright and screenwriter (b. 1935)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American businessman and philanthropist (b. 1915)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American computer scientist and mathematician (b. 1933)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and mathematician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, English composer and conductor (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English composer and conductor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English composer and conductor (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English actress (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese academic and jurist (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Shigemitsu_Dand%C5%8D\" title=\"Shige<PERSON><PERSON> Dandō\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese academic and jurist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shige<PERSON>su_Dand%C5%8D\" title=\"Shige<PERSON><PERSON> Dandō\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese academic and jurist (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shigemitsu_Dand%C5%8D"}]}, {"year": "2012", "text": "<PERSON>, Scottish jockey (b. 1990)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Campbell Gillies\"><PERSON></a>, Scottish jockey (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Campbell Gillies\"><PERSON></a>, Scottish jockey (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Campbell_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Jr., American businessman (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman (b. 1927)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "2012", "text": "<PERSON><PERSON>, American baseball player (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American boxer (b. 1949)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1949)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "2013", "text": "<PERSON>, American screenwriter and producer (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer (b. 1923)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2013", "text": "<PERSON>, Scottish swimmer (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish swimmer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish swimmer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American photographer and journalist (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American rower and coach (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, American rower and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, American rower and coach (b. 1935)", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>(rower)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American journalist (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soldier and judge (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Green_Wix_Unthank\" title=\"Green Wix Unthank\">Green Wix Unthank</a>, American soldier and judge (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Green_Wix_Unthank\" title=\"Green Wix Unthank\">Green Wix Unthank</a>, American soldier and judge (b. 1923)", "links": [{"title": "Green Wix Unthank", "link": "https://wikipedia.org/wiki/Green_Wix_Unthank"}]}, {"year": "2014", "text": "<PERSON>, English journalist, author, and screenwriter (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Spanish author and academic (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Matute\" title=\"<PERSON>\"><PERSON></a>, Spanish author and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%AD<PERSON>_<PERSON>ute\" title=\"<PERSON>\"><PERSON></a>, Spanish author and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Mar%C3%AD<PERSON>_Matute"}]}, {"year": "2014", "text": "<PERSON>, Ukrainian agronomist and politician (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian agronomist and politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian agronomist and politician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English actor (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Egyptian-Armenian patriarch (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_Tarmouni\" title=\"Nerses Bedros XIX Tarmouni\"><PERSON><PERSON><PERSON> XI<PERSON></a>, Egyptian-Armenian patriarch (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_Tarmouni\" title=\"<PERSON><PERSON>s Bedros XIX Tarmouni\"><PERSON><PERSON><PERSON></a>, Egyptian-Armenian patriarch (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON> XIX Tarmouni", "link": "https://wikipedia.org/wiki/N<PERSON><PERSON>_Bedros_XIX_<PERSON>ni"}]}, {"year": "2016", "text": "<PERSON>, South African writer of apartheid-period (b. 1936)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, South African writer of apartheid-period (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, South African writer of apartheid-period (b. 1936)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2023", "text": "<PERSON>, Australian trade union leader and politician (b. 1949)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian trade union leader and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian trade union leader and politician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American Samoan professional wrestler (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oa%E2%80%98i\" class=\"mw-redirect\" title=\"<PERSON><PERSON>‘i\"><PERSON><PERSON>i</a>, American Samoan professional wrestler (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oa%E2%80%98i\" class=\"mw-redirect\" title=\"<PERSON><PERSON>‘i\"><PERSON><PERSON>i</a>, American Samoan professional wrestler (b. 1945)", "links": [{"title": "Sika Anoa‘i", "link": "https://wikipedia.org/wiki/Sika_Anoa%E2%80%98i"}]}, {"year": "2024", "text": "<PERSON>, American actor (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}