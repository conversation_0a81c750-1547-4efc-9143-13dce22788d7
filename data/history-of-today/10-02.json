{"date": "October 2", "url": "https://wikipedia.org/wiki/October_2", "data": {"Events": [{"year": "829", "text": "<PERSON><PERSON><PERSON> succeeds his father <PERSON> as Byzantine Emperor.", "html": "829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON><PERSON> (emperor)\"><PERSON><PERSON><PERSON></a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(emperor)\" title=\"<PERSON><PERSON><PERSON> (emperor)\"><PERSON><PERSON><PERSON></a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a> as <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Emperor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(emperor)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "939", "text": "Battle of Andernach: <PERSON>, Holy Roman Emperor, crushes a rebellion against his rule, by a coalition of <PERSON><PERSON><PERSON> of Franconia and other Frankish dukes.", "html": "939 - <a href=\"https://wikipedia.org/wiki/Battle_of_Andernach\" title=\"Battle of Andernach\">Battle of Andernach</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, crushes a rebellion against his rule, by a coalition of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Franconia\" title=\"<PERSON><PERSON><PERSON> of Franconia\"><PERSON><PERSON><PERSON> of Franconia</a> and other Frankish dukes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Andernach\" title=\"Battle of Andernach\">Battle of Andernach</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, crushes a rebellion against his rule, by a coalition of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Franconia\" title=\"<PERSON><PERSON><PERSON> of Franconia\"><PERSON><PERSON><PERSON> of Franconia</a> and other Frankish dukes.", "links": [{"title": "Battle of Andernach", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>ernach"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON><PERSON><PERSON> of Franconia", "link": "https://wikipedia.org/wiki/Eberhard_of_Franconia"}]}, {"year": "1263", "text": "The Battle of Largs is fought between Norwegians and Scots.", "html": "1263 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Largs\" title=\"Battle of Largs\">Battle of Largs</a> is fought between Norwegians and Scots.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Largs\" title=\"Battle of Largs\">Battle of Largs</a> is fought between Norwegians and Scots.", "links": [{"title": "Battle of Largs", "link": "https://wikipedia.org/wiki/Battle_of_Largs"}]}, {"year": "1470", "text": "The <PERSON> of Warwick's rebellion forces King <PERSON> of England to flee to the Netherlands, restoring <PERSON> to the throne.", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\">The Earl of Warwick</a>'s rebellion forces King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> of England</a> to flee to the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, restoring <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a> to the throne.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\">The Earl of Warwick</a>'s rebellion forces King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> of England</a> to flee to the <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Netherlands</a>, restoring <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI</a> to the throne.", "links": [{"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1552", "text": "Russo-Kazan Wars: Russian troops enter Kazan.", "html": "1552 - Russo-Kazan Wars: Russian troops <a href=\"https://wikipedia.org/wiki/Siege_of_Kazan\" title=\"Siege of Kazan\">enter Kazan</a>.", "no_year_html": "Russo-Kazan Wars: Russian troops <a href=\"https://wikipedia.org/wiki/Siege_of_Kazan\" title=\"Siege of Kazan\">enter Kazan</a>.", "links": [{"title": "Siege of Kazan", "link": "https://wikipedia.org/wiki/Siege_of_Kazan"}]}, {"year": "1766", "text": "The Nottingham Cheese Riot breaks out at the Goose Fair in Nottingham, UK, in response to the excessive cost of cheese.", "html": "1766 - The <a href=\"https://wikipedia.org/wiki/Nottingham_cheese_riot\" title=\"Nottingham cheese riot\">Nottingham Cheese Riot</a> breaks out at the <a href=\"https://wikipedia.org/wiki/Nottingham_Goose_Fair\" title=\"Nottingham Goose Fair\">Goose Fair</a> in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, UK, in response to the excessive cost of cheese.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nottingham_cheese_riot\" title=\"Nottingham cheese riot\">Nottingham Cheese Riot</a> breaks out at the <a href=\"https://wikipedia.org/wiki/Nottingham_Goose_Fair\" title=\"Nottingham Goose Fair\">Goose Fair</a> in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, UK, in response to the excessive cost of cheese.", "links": [{"title": "Nottingham cheese riot", "link": "https://wikipedia.org/wiki/Nottingham_cheese_riot"}, {"title": "Nottingham Goose Fair", "link": "https://wikipedia.org/wiki/Nottingham_Goose_Fair"}, {"title": "Nottingham", "link": "https://wikipedia.org/wiki/Nottingham"}]}, {"year": "1780", "text": "American Revolutionary War: <PERSON>, a British Army officer, is hanged as a spy by the Continental Army.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, a British Army officer, is hanged as a spy by the Continental Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, a British Army officer, is hanged as a spy by the Continental Army.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9"}]}, {"year": "1789", "text": "The United States Bill of Rights is sent to the various States for ratification.", "html": "1789 - The <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">United States Bill of Rights</a> is sent to the various States for ratification.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Bill_of_Rights\" title=\"United States Bill of Rights\">United States Bill of Rights</a> is sent to the various States for ratification.", "links": [{"title": "United States Bill of Rights", "link": "https://wikipedia.org/wiki/United_States_Bill_of_Rights"}]}, {"year": "1835", "text": "Texas Revolution: Mexican troops attempt to disarm the people of Gonzales, but encounter stiff resistance from a hastily assembled militia.", "html": "1835 - Texas Revolution: Mexican troops attempt to <a href=\"https://wikipedia.org/wiki/Battle_of_Gonzales\" title=\"Battle of Gonzales\">disarm the people of Gonzales</a>, but encounter stiff resistance from a hastily assembled militia.", "no_year_html": "Texas Revolution: Mexican troops attempt to <a href=\"https://wikipedia.org/wiki/Battle_of_Gonzales\" title=\"Battle of Gonzales\">disarm the people of Gonzales</a>, but encounter stiff resistance from a hastily assembled militia.", "links": [{"title": "Battle of Gonzales", "link": "https://wikipedia.org/wiki/Battle_of_Gonzales"}]}, {"year": "1864", "text": "American Civil War: Confederates defeat a Union attack on Saltville, Virginia. A massacre of wounded Union prisoners ensues.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederates <a href=\"https://wikipedia.org/wiki/First_Battle_of_Saltville\" title=\"First Battle of Saltville\">defeat</a> a Union attack on Saltville, Virginia. A massacre of wounded Union prisoners ensues.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Confederates <a href=\"https://wikipedia.org/wiki/First_Battle_of_Saltville\" title=\"First Battle of Saltville\">defeat</a> a Union attack on Saltville, Virginia. A massacre of wounded Union prisoners ensues.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "First Battle of Saltville", "link": "https://wikipedia.org/wiki/First_Battle_of_Saltville"}]}, {"year": "1870", "text": "By plebiscite, the citizens of the Papal States accept annexation by the Kingdom of Italy.", "html": "1870 - By <a href=\"https://wikipedia.org/wiki/Plebiscite\" class=\"mw-redirect\" title=\"Plebiscite\">plebiscite</a>, the citizens of the Papal States accept <a href=\"https://wikipedia.org/wiki/Capture_of_Rome\" title=\"Capture of Rome\">annexation by the Kingdom of Italy</a>.", "no_year_html": "By <a href=\"https://wikipedia.org/wiki/Plebiscite\" class=\"mw-redirect\" title=\"Plebiscite\">plebiscite</a>, the citizens of the Papal States accept <a href=\"https://wikipedia.org/wiki/Capture_of_Rome\" title=\"Capture of Rome\">annexation by the Kingdom of Italy</a>.", "links": [{"title": "Plebiscite", "link": "https://wikipedia.org/wiki/Plebiscite"}, {"title": "Capture of Rome", "link": "https://wikipedia.org/wiki/Capture_of_Rome"}]}, {"year": "1919", "text": "Seven days after suffering a \"physical collapse\" following a speech in Pueblo, Colorado, U.S. President <PERSON> has a catastrophic stroke at the White House, leaving him physically and mentally incapacitated for the remainder of his presidency.", "html": "1919 - Seven days after suffering a \"<a href=\"https://wikipedia.org/wiki/Stroke#Signs_and_symptoms\" title=\"Stroke\">physical collapse</a>\" following a <a href=\"https://wikipedia.org/wiki/Pueblo_speech\" title=\"Pueblo speech\">speech in Pueblo, Colorado</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> has a catastrophic <a href=\"https://wikipedia.org/wiki/Stroke#Ischemic\" title=\"Stroke\">stroke</a> at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>, leaving him physically and mentally incapacitated for the remainder of his presidency.", "no_year_html": "Seven days after suffering a \"<a href=\"https://wikipedia.org/wiki/Stroke#Signs_and_symptoms\" title=\"Stroke\">physical collapse</a>\" following a <a href=\"https://wikipedia.org/wiki/Pueblo_speech\" title=\"Pueblo speech\">speech in Pueblo, Colorado</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> has a catastrophic <a href=\"https://wikipedia.org/wiki/Stroke#Ischemic\" title=\"Stroke\">stroke</a> at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>, leaving him physically and mentally incapacitated for the remainder of his presidency.", "links": [{"title": "Stroke", "link": "https://wikipedia.org/wiki/Stroke#Signs_and_symptoms"}, {"title": "Pueblo speech", "link": "https://wikipedia.org/wiki/Pueblo_speech"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Stroke", "link": "https://wikipedia.org/wiki/Stroke#Ischemic"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1920", "text": "Ukrainian War of Independence: <PERSON> orders the Red Army to immediately cease hostilities with the Revolutionary Insurgent Army of Ukraine.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> to immediately cease hostilities with the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> to immediately cease hostilities with the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}]}, {"year": "1928", "text": "The \"Prelature of the Holy Cross and the Work of God\", commonly known as Opus Dei, is founded.", "html": "1928 - The \"Prelature of the Holy Cross and the Work of God\", commonly known as <i><a href=\"https://wikipedia.org/wiki/Opus_Dei\" title=\"Opus Dei\">Opus Dei</a></i>, is founded.", "no_year_html": "The \"Prelature of the Holy Cross and the Work of God\", commonly known as <i><a href=\"https://wikipedia.org/wiki/Opus_Dei\" title=\"Opus Dei\">Opus Dei</a></i>, is founded.", "links": [{"title": "Opus Dei", "link": "https://wikipedia.org/wiki/Opus_Dei"}]}, {"year": "1937", "text": "<PERSON> orders the execution of Haitians living in the border region of the Dominican Republic.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Parsley_massacre\" title=\"Parsley massacre\">execution of Haitians</a> living in the border region of the Dominican Republic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Parsley_massacre\" title=\"Parsley massacre\">execution of Haitians</a> living in the border region of the Dominican Republic.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Parsley massacre", "link": "https://wikipedia.org/wiki/Pa<PERSON><PERSON>_massacre"}]}, {"year": "1942", "text": "World War II: Ocean Liner RMS Queen Mary accidentally rams and sinks HMS Curacoa, killing over 300 crewmen aboard Curacoa.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Ocean Liner <a href=\"https://wikipedia.org/wiki/RMS_Queen_<PERSON>\" title=\"RMS Queen Mary\">RMS <i>Queen <PERSON></i></a> accidentally rams and sinks <a href=\"https://wikipedia.org/wiki/HMS_Curacoa_(D41)\" title=\"HMS Curacoa (D41)\">HMS <i>Curacoa</i></a>, killing over 300 crewmen aboard <i>Curacoa</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Ocean Liner <a href=\"https://wikipedia.org/wiki/RMS_Queen_<PERSON>\" title=\"RMS Queen Mary\">R<PERSON> <i>Queen <PERSON></i></a> accidentally rams and sinks <a href=\"https://wikipedia.org/wiki/HMS_Curacoa_(D41)\" title=\"HMS Curacoa (D41)\">HMS <i>Curacoa</i></a>, killing over 300 crewmen aboard <i>Curacoa</i>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "RMS Queen Mary", "link": "https://wikipedia.org/wiki/RMS_Queen_<PERSON>"}, {"title": "HMS Curacoa (D41)", "link": "https://wikipedia.org/wiki/HMS_Curacoa_(D41)"}]}, {"year": "1944", "text": "World War II: German troops end the Warsaw Uprising.", "html": "1944 - World War II: German troops end the <a href=\"https://wikipedia.org/wiki/Warsaw_Uprising\" title=\"Warsaw Uprising\">Warsaw Uprising</a>.", "no_year_html": "World War II: German troops end the <a href=\"https://wikipedia.org/wiki/Warsaw_Uprising\" title=\"Warsaw Uprising\">Warsaw Uprising</a>.", "links": [{"title": "Warsaw Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Uprising"}]}, {"year": "1958", "text": "Guinea declares its independence from France.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> declares its independence from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinea</a> declares its independence from France.", "links": [{"title": "Guinea", "link": "https://wikipedia.org/wiki/Guinea"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON> is sworn in as the first African-American justice of the United States Supreme Court.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sworn in as the first African-American justice of the United States Supreme Court.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is sworn in as the first African-American justice of the United States Supreme Court.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Mexican President <PERSON> orders soldiers to suppress a demonstration of unarmed students, ten days before the start of the 1968 Summer Olympics.", "html": "1968 - Mexican President <a href=\"https://wikipedia.org/wiki/Gustavo_D%C3%ADaz_Ordaz\" title=\"<PERSON>\"><PERSON></a> orders soldiers to <a href=\"https://wikipedia.org/wiki/Tlatelolco_massacre\" title=\"Tlatelolco massacre\">suppress a demonstration of unarmed students</a>, ten days before the start of the <a href=\"https://wikipedia.org/wiki/1968_Summer_Olympics\" title=\"1968 Summer Olympics\">1968 Summer Olympics</a>.", "no_year_html": "Mexican President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz_Ordaz\" title=\"<PERSON>\"><PERSON></a> orders soldiers to <a href=\"https://wikipedia.org/wiki/Tlatelolco_massacre\" title=\"Tlatelolco massacre\">suppress a demonstration of unarmed students</a>, ten days before the start of the <a href=\"https://wikipedia.org/wiki/1968_Summer_Olympics\" title=\"1968 Summer Olympics\">1968 Summer Olympics</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustavo_D%C3%ADaz_Ordaz"}, {"title": "Tlatelolco massacre", "link": "https://wikipedia.org/wiki/Tlatelolco_massacre"}, {"title": "1968 Summer Olympics", "link": "https://wikipedia.org/wiki/1968_Summer_Olympics"}]}, {"year": "1970", "text": "An aircraft carrying the Wichita State University football team, administrators, and supporters crashes in Colorado, killing 31 people.", "html": "1970 - An aircraft carrying the Wichita State University football team, administrators, and supporters <a href=\"https://wikipedia.org/wiki/Wichita_State_University_football_team_plane_crash\" title=\"Wichita State University football team plane crash\">crashes</a> in Colorado, killing 31 people.", "no_year_html": "An aircraft carrying the Wichita State University football team, administrators, and supporters <a href=\"https://wikipedia.org/wiki/Wichita_State_University_football_team_plane_crash\" title=\"Wichita State University football team plane crash\">crashes</a> in Colorado, killing 31 people.", "links": [{"title": "Wichita State University football team plane crash", "link": "https://wikipedia.org/wiki/Wichita_State_University_football_team_plane_crash"}]}, {"year": "1971", "text": "South Vietnamese President <PERSON><PERSON><PERSON> is re-elected in a one-man election.", "html": "1971 - South Vietnamese President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1971_South_Vietnamese_presidential_election\" title=\"1971 South Vietnamese presidential election\">re-elected</a> in a one-man election.", "no_year_html": "South Vietnamese President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1971_South_Vietnamese_presidential_election\" title=\"1971 South Vietnamese presidential election\">re-elected</a> in a one-man election.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "1971 South Vietnamese presidential election", "link": "https://wikipedia.org/wiki/1971_South_Vietnamese_presidential_election"}]}, {"year": "1971", "text": "British European Airways Flight 706 crashes near Aarsele, Belgium, killing 63.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/British_European_Airways_Flight_706\" title=\"British European Airways Flight 706\">British European Airways Flight 706</a> crashes near <a href=\"https://wikipedia.org/wiki/Aarsele\" title=\"Aars<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgium, killing 63.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/British_European_Airways_Flight_706\" title=\"British European Airways Flight 706\">British European Airways Flight 706</a> crashes near <a href=\"https://wikipedia.org/wiki/Aarsele\" title=\"Aars<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgium, killing 63.", "links": [{"title": "British European Airways Flight 706", "link": "https://wikipedia.org/wiki/British_European_Airways_Flight_706"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>ele"}]}, {"year": "1980", "text": "<PERSON> becomes the first member of either chamber of Congress to be expelled since the Civil War.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Pennsylvania_politician)\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a> becomes the first member of either chamber of Congress to be <a href=\"https://wikipedia.org/wiki/Expulsion_from_the_United_States_Congress\" title=\"Expulsion from the United States Congress\">expelled</a> since the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Pennsylvania_politician)\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a> becomes the first member of either chamber of Congress to be <a href=\"https://wikipedia.org/wiki/Expulsion_from_the_United_States_Congress\" title=\"Expulsion from the United States Congress\">expelled</a> since the <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">Civil War</a>.", "links": [{"title": "<PERSON> (Pennsylvania politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Pennsylvania_politician)"}, {"title": "Expulsion from the United States Congress", "link": "https://wikipedia.org/wiki/Expulsion_from_the_United_States_Congress"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}]}, {"year": "1990", "text": "Xiamen Airlines Flight 8301 is hijacked and lands at Guangzhou, where it crashes into two other airliners on the ground, killing 132.", "html": "1990 - Xiamen Airlines Flight 8301 is hijacked and lands at Guangzhou, where it <a href=\"https://wikipedia.org/wiki/1990_Guangzhou_Baiyun_airport_collisions\" title=\"1990 Guangzhou Baiyun airport collisions\">crashes</a> into two other airliners on the ground, killing 132.", "no_year_html": "Xiamen Airlines Flight 8301 is hijacked and lands at Guangzhou, where it <a href=\"https://wikipedia.org/wiki/1990_Guangzhou_Baiyun_airport_collisions\" title=\"1990 Guangzhou Baiyun airport collisions\">crashes</a> into two other airliners on the ground, killing 132.", "links": [{"title": "1990 Guangzhou Baiyun airport collisions", "link": "https://wikipedia.org/wiki/1990_Guangzhou_Baiyun_airport_collisions"}]}, {"year": "1992", "text": "Military police storm the Carandiru Penitentiary in São Paulo, Brazil during a prison riot. The resulting massacre leaves 111 prisoners dead.", "html": "1992 - Military police storm the Carandiru Penitentiary in São Paulo, Brazil during a prison riot. The resulting <a href=\"https://wikipedia.org/wiki/Carandiru_Massacre\" class=\"mw-redirect\" title=\"Carandiru Massacre\">massacre</a> leaves 111 prisoners dead.", "no_year_html": "Military police storm the Carandiru Penitentiary in São Paulo, Brazil during a prison riot. The resulting <a href=\"https://wikipedia.org/wiki/Carandiru_Massacre\" class=\"mw-redirect\" title=\"Carandiru Massacre\">massacre</a> leaves 111 prisoners dead.", "links": [{"title": "Carandiru Massacre", "link": "https://wikipedia.org/wiki/Carandiru_Massacre"}]}, {"year": "1996", "text": "Aeroperú Flight 603 crashes into the ocean near Peru, killing all 70 people on board.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Aeroper%C3%BA_Flight_603\" title=\"Aeroperú Flight 603\">Aeroperú Flight 603</a> crashes into the ocean near Peru, killing all 70 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroper%C3%BA_Flight_603\" title=\"Aeroperú Flight 603\">Aeroperú Flight 603</a> crashes into the ocean near Peru, killing all 70 people on board.", "links": [{"title": "Aeroperú Flight 603", "link": "https://wikipedia.org/wiki/Aeroper%C3%BA_Flight_603"}]}, {"year": "1996", "text": "The Electronic Freedom of Information Act Amendments are signed by U.S. President <PERSON>.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Electronic Freedom of Information Act Amendments</a> are signed by U.S. President <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Electronic Freedom of Information Act Amendments</a> are signed by U.S. President <PERSON>.", "links": [{"title": "Freedom of Information Act (United States)", "link": "https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)"}]}, {"year": "2002", "text": "The Beltway sniper attacks begin in Washington, D.C., extending over three weeks and killing 10 people.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Beltway_sniper_attacks\" class=\"mw-redirect\" title=\"Beltway sniper attacks\">Beltway sniper attacks</a> begin in Washington, D.C., extending over three weeks and killing 10 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Beltway_sniper_attacks\" class=\"mw-redirect\" title=\"Beltway sniper attacks\">Beltway sniper attacks</a> begin in Washington, D.C., extending over three weeks and killing 10 people.", "links": [{"title": "Beltway sniper attacks", "link": "https://wikipedia.org/wiki/Beltway_sniper_attacks"}]}, {"year": "2004", "text": "The first parkrun, then known as the Bushy Park Time Trial, takes place in Bushy Park, London, UK.", "html": "2004 - The first <a href=\"https://wikipedia.org/wiki/Parkrun\" title=\"Parkrun\">parkrun</a>, then known as the Bushy Park Time Trial, takes place in <a href=\"https://wikipedia.org/wiki/Bushy_Park\" title=\"Bushy Park\">Bushy Park</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, UK.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Parkrun\" title=\"Parkrun\">parkrun</a>, then known as the Bushy Park Time Trial, takes place in <a href=\"https://wikipedia.org/wiki/Bushy_Park\" title=\"Bushy Park\">Bushy Park</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, UK.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Parkrun"}, {"title": "Bushy Park", "link": "https://wikipedia.org/wiki/Bushy_Park"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "2006", "text": "Five Amish girls are murdered in a shooting at a school in Pennsylvania, United States.", "html": "2006 - Five Amish girls are murdered in a <a href=\"https://wikipedia.org/wiki/West_Nickel_Mines_School_shooting\" title=\"West Nickel Mines School shooting\">shooting at a school</a> in Pennsylvania, United States.", "no_year_html": "Five Amish girls are murdered in a <a href=\"https://wikipedia.org/wiki/West_Nickel_Mines_School_shooting\" title=\"West Nickel Mines School shooting\">shooting at a school</a> in Pennsylvania, United States.", "links": [{"title": "West Nickel Mines School shooting", "link": "https://wikipedia.org/wiki/West_Nickel_Mines_School_shooting"}]}, {"year": "2007", "text": "President <PERSON><PERSON> of South Korea goes to North Korea for an Inter-Korean summit with North Korean leader <PERSON>.", "html": "2007 - President <PERSON><PERSON> of South Korea goes to North Korea for an <a href=\"https://wikipedia.org/wiki/Inter-Korean_summit\" class=\"mw-redirect\" title=\"Inter-Korean summit\">Inter-Korean summit</a> with North Korean leader <PERSON>.", "no_year_html": "President <PERSON><PERSON> of South Korea goes to North Korea for an <a href=\"https://wikipedia.org/wiki/Inter-Korean_summit\" class=\"mw-redirect\" title=\"Inter-Korean summit\">Inter-Korean summit</a> with North Korean leader <PERSON>.", "links": [{"title": "Inter-Korean summit", "link": "https://wikipedia.org/wiki/Inter-Korean_summit"}]}, {"year": "2016", "text": "Ethiopian protests break out during a festival in the Oromia region, killing dozens of people.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/2016_Ethiopian_protests\" class=\"mw-redirect\" title=\"2016 Ethiopian protests\">Ethiopian protests</a> break out during a festival in the Oromia region, killing dozens of people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2016_Ethiopian_protests\" class=\"mw-redirect\" title=\"2016 Ethiopian protests\">Ethiopian protests</a> break out during a festival in the Oromia region, killing dozens of people.", "links": [{"title": "2016 Ethiopian protests", "link": "https://wikipedia.org/wiki/2016_Ethiopian_protests"}]}, {"year": "2018", "text": "The Washington Post journalist <PERSON> is assassinated in the Saudi consulate in Istanbul, Turkey.", "html": "2018 - <i><a href=\"https://wikipedia.org/wiki/The_Washington_Post\" title=\"The Washington Post\">The Washington Post</a></i> journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> in the Saudi consulate in Istanbul, Turkey.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Washington_Post\" title=\"The Washington Post\">The Washington Post</a></i> journalist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> in the Saudi consulate in Istanbul, Turkey.", "links": [{"title": "The Washington Post", "link": "https://wikipedia.org/wiki/The_Washington_Post"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "A privately-owned Boeing B-17 Flying Fortress conducting a living history exhibition flight crashes shortly after takeoff from Windsor Locks, Connecticut, killing seven.", "html": "2019 - A privately-owned <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a> conducting a <a href=\"https://wikipedia.org/wiki/Living_history\" title=\"Living history\">living history</a> exhibition flight <a href=\"https://wikipedia.org/wiki/2019_Boeing_B-17_Flying_Fortress_crash\" title=\"2019 Boeing B-17 Flying Fortress crash\">crashes shortly after takeoff</a> from <a href=\"https://wikipedia.org/wiki/Windsor_Locks,_Connecticut\" title=\"Windsor Locks, Connecticut\">Windsor Locks, Connecticut</a>, killing seven.", "no_year_html": "A privately-owned <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a> conducting a <a href=\"https://wikipedia.org/wiki/Living_history\" title=\"Living history\">living history</a> exhibition flight <a href=\"https://wikipedia.org/wiki/2019_Boeing_B-17_Flying_Fortress_crash\" title=\"2019 Boeing B-17 Flying Fortress crash\">crashes shortly after takeoff</a> from <a href=\"https://wikipedia.org/wiki/Windsor_Locks,_Connecticut\" title=\"Windsor Locks, Connecticut\">Windsor Locks, Connecticut</a>, killing seven.", "links": [{"title": "Boeing B-17 Flying Fortress", "link": "https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress"}, {"title": "Living history", "link": "https://wikipedia.org/wiki/Living_history"}, {"title": "2019 Boeing B-17 Flying Fortress crash", "link": "https://wikipedia.org/wiki/2019_Boeing_B-17_Flying_Fortress_crash"}, {"title": "Windsor Locks, Connecticut", "link": "https://wikipedia.org/wiki/Windsor_Locks,_Connecticut"}]}], "Births": [{"year": "1452", "text": "<PERSON> of England (d. 1485)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (d. 1485)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1470", "text": "<PERSON> of Aragon, Queen of Portugal, Daughter of <PERSON> of Castile and <PERSON> of Aragon (d. 1498)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON> of Aragon, Queen of Portugal\"><PERSON> of Aragon, Queen of Portugal</a>, Daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> and <a href=\"https://wikipedia.org/wiki/Ferdinand_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1498)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Portugal\" title=\"<PERSON> of Aragon, Queen of Portugal\"><PERSON> of Aragon, Queen of Portugal</a>, Daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1498)", "links": [{"title": "<PERSON> of Aragon, Queen of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_Portugal"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}]}, {"year": "1527", "text": "<PERSON>, English politician (d. 1579)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1538", "text": "<PERSON>, Italian cardinal and saint (d. 1584)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (d. 1584)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, English author and critic (d. 1800)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, 1st Viscount <PERSON>, English general and politician (d. 1854)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English general and politician (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English general and politician (d. 1854)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, King of Sardinia (1831-49) (d. 1849)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sardinia\" title=\"<PERSON> of Sardinia\"><PERSON>, King of Sardinia (1831-49)</a> (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sardinia\" title=\"<PERSON> of Sardinia\"><PERSON>, King of Sardinia (1831-49)</a> (d. 1849)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sardinia"}]}, {"year": "1800", "text": "<PERSON>, American slave and uprising leader (d. 1831)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American slave and uprising leader (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American slave and uprising leader (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Irish-Australian politician, Premier of Tasmania (d. 1901)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1821", "text": "<PERSON>, American general (d. 1908)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, French lawyer and politician, Prime Minister of France (d. 1896)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1832", "text": "<PERSON>, English anthropologist (d. 1917)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthropologist (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, German field marshal and politician, 2nd President of Germany (d. 1934)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)\" title=\"President of Germany (1919-1945)\">President of Germany</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)\" title=\"President of Germany (1919-1945)\">President of Germany</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Germany (1919-1945)", "link": "https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)"}]}, {"year": "1851", "text": "<PERSON>, French field marshal (d. 1929)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French field marshal (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French field marshal (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Scottish chemist and academic, Nobel Prize laureate (d. 1916)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1854", "text": "<PERSON>, Scottish biologist, sociologist, geographer, and philanthropist (d. 1932)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biologist, sociologist, geographer, and philanthropist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biologist, sociologist, geographer, and philanthropist (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Indian mystic and philosopher (d. 1939)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian mystic and philosopher (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian mystic and philosopher (d. 1939)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Indian freedom fighter, activist and philosopher (d. 1948)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian freedom fighter, activist and philosopher (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian freedom fighter, activist and philosopher (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, American politician, United States Secretary of State, Nobel Prize laureate (d. 1955)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ll_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1871", "text": "<PERSON>, American landscaper and author (d. 1959)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American landscaper and author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American landscaper and author (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American lawyer and politician (d. 1924)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, English cricketer and manager (d. 1963)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Warner\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Warner\"><PERSON><PERSON><PERSON></a>, English cricketer and manager (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ham_Warner\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Warner\"><PERSON><PERSON><PERSON></a>, English cricketer and manager (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON> Warner", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ham_Warner"}]}, {"year": "1875", "text": "<PERSON><PERSON>, American suffragist (d. 1935)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American suffragist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American suffragist (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American poet (d. 1955)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Russian colonel (d. 1945)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian geologist and engineer (d. 1963)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and engineer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and engineer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, American comedian and actor (d. 1977)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American composer and conductor (d. 1962)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shield\"><PERSON></a>, American composer and conductor (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American colonel (d. 1990)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Indian freedom fighter, social reformer and politician (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian freedom fighter, social reformer and politician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian freedom fighter, social reformer and politician (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Austrian politician, Chancellor of Austria (d. 1965)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Figl"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1904", "text": "<PERSON>, English novelist, playwright, and critic (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, playwright, and critic (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Indian and politician, Prime Minister of India (d. 1966)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Croatian cardinal (d. 1981)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Fran<PERSON>_%C5%A0eper\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian cardinal (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%A0eper\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian cardinal (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franjo_%C5%A0eper"}]}, {"year": "1906", "text": "<PERSON>, Australian politician, Premier of Victoria (d. 1971)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Bolivian politician, President of Bolivia (d. 2001)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_Estenssoro\" title=\"<PERSON><PERSON><PERSON>sso<PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian politician, <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_Estenssoro\" title=\"<PERSON><PERSON><PERSON>stensso<PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian politician, <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1907", "text": "<PERSON>, Scottish biochemist and academic, Nobel Prize laureate (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1909", "text": "<PERSON>, American cartoonist, creator of <PERSON> (d. 1956)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, creator of <PERSON> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, creator of <PERSON> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American engineer and painter (d. 1981)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and painter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and painter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American chemist, occultist, and engineer (d. 1952)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)\" class=\"mw-redirect\" title=\"<PERSON> (rocket engineer)\"><PERSON></a>, American chemist, occultist, and engineer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)\" class=\"mw-redirect\" title=\"<PERSON> (rocket engineer)\"><PERSON></a>, American chemist, occultist, and engineer (d. 1952)", "links": [{"title": "<PERSON> (rocket engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, English organist, conductor, and historian (d. 1998)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Bern<PERSON>r_<PERSON>\" title=\"Bernarr Rainbow\"><PERSON><PERSON><PERSON></a>, English organist, conductor, and historian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>r_<PERSON>\" title=\"Bernarr Rainbow\"><PERSON><PERSON><PERSON></a>, English organist, conductor, and historian (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author and businessman, founded Williams Sonoma (d. 2015)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and businessman, founded <a href=\"https://wikipedia.org/wiki/Williams_Sonoma_(brand)\" class=\"mw-redirect\" title=\"Williams Sonoma (brand)\">Williams Sonoma</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and businessman, founded <a href=\"https://wikipedia.org/wiki/Williams_Sonoma_(brand)\" class=\"mw-redirect\" title=\"Williams Sonoma (brand)\">Williams Sonoma</a> (d. 2015)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}, {"title": "Williams Sonoma (brand)", "link": "https://wikipedia.org/wiki/Williams_Sonoma_(brand)"}]}, {"year": "1917", "text": "<PERSON>, Belgian cytologist and biochemist, Nobel Prize laureate (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cytologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cytologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1917", "text": "<PERSON>, American actor (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1919", "text": "<PERSON>, English guitarist and composer (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English writer and composer (d. 1978)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and composer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and composer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American pilot and engineer (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English archbishop (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Welsh historian and author (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 1993)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Polish-German theologian and academic (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German theologian and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pan<PERSON>\"><PERSON><PERSON></a>, Polish-German theologian and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian businessman (d. 1996)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actor (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gunn\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian social worker and politician, 26th Premier of British Columbia (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian social worker and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian social worker and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English biologist and academic, Nobel Prize laureate", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1933", "text": "<PERSON>, Canadian singer (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Baron <PERSON> of Foscote, English lawyer and judge", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Foscote\" title=\"<PERSON>, Baron <PERSON> of Foscote\"><PERSON>, Baron <PERSON> of Foscote</a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Foscote\" title=\"<PERSON>, Baron <PERSON> of Foscote\"><PERSON>, Baron <PERSON> of Foscote</a>, English lawyer and judge", "links": [{"title": "<PERSON>, Baron <PERSON> of Foscote", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Foscote"}]}, {"year": "1934", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1935", "text": "<PERSON>, Italian-Argentine footballer and manager (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Omar_S%C3%ADvori\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentine footballer and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Omar_S%C3%ADvori\" title=\"<PERSON>\"><PERSON></a>, Italian-Argentine footballer and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Omar_S%C3%ADvori"}]}, {"year": "1936", "text": "<PERSON>, American basketball player", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American basketball player (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American lawyer (d. 2005)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Pakistani actor, producer, and screenwriter (d. 1983)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani actor, producer, and screenwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani actor, producer, and screenwriter (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wah<PERSON>_<PERSON>rad"}]}, {"year": "1938", "text": "<PERSON>, American film critic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Indian cricketer (d. 2006)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an"}]}, {"year": "1941", "text": "<PERSON>, English poet and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American rock bass player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American director and producer, co-founded NFL Films (d. 2012)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/NFL_Films\" title=\"NFL Films\">NFL Films</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NFL Films", "link": "https://wikipedia.org/wiki/NFL_Films"}]}, {"year": "1943", "text": "<PERSON>, English journalist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American author (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Vernor_Vinge\" title=\"Vernor Vinge\"><PERSON><PERSON><PERSON></a>, American author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vernor_Vinge\" title=\"Vernor Vinge\"><PERSON><PERSON><PERSON></a>, American author (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vernor_Vinge"}]}, {"year": "1945", "text": "<PERSON>, American cryptographer and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cryptographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cryptographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Thai general and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gli<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gli<PERSON>\" title=\"<PERSON><PERSON>gli<PERSON>\"><PERSON><PERSON></a>, Thai general and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sonthi_Boonyaratglin"}]}, {"year": "1947", "text": "<PERSON>, American author and activist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American fashion designer, founded DKNY", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/DKNY\" title=\"DKNY\">DKNY</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/DKNY\" title=\"DKNY\">DKNY</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DKNY", "link": "https://wikipedia.org/wiki/DKNY"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Estonian politician, Prime Minister of Estonia", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Indian model and actress, (d. 1998)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian model and actress, (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian model and actress, (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Per<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Richard Hell\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Richard Hell\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American photographer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English singer-songwriter and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Polish classical pianist and actor (d. 2024)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish classical pianist and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish classical pianist and actor (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Riker\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American basketball player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Lorraine_Bracco\" title=\"<PERSON> Bracco\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lorraine_Bracco\" title=\"<PERSON> Bracco\"><PERSON></a>, American actress", "links": [{"title": "Lorraine Bracco", "link": "https://wikipedia.org/wiki/Lorraine_Bracco"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American soul singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1957", "text": "<PERSON>, English rugby player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> N<PERSON>il\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nevil\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, English musician and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English musician and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English musician and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Maltese-American journalist and cartoonist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-American journalist and cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-American journalist and cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Dereck_Whittenburg\" title=\"Dereck Whittenburg\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dereck_Whittenburg\" title=\"Dereck Whittenburg\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "Dereck Whittenburg", "link": "https://wikipedia.org/wiki/Der<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian-American football player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1963", "text": "<PERSON>, Filipino-American journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German field hockey player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian tennis player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>, Turkish-Austrian pianists", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_%C3%96nder\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON></a>, Turkish-Austrian pianists", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_%C3%96nder\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON></a>, Turkish-Austrian pianists", "links": [{"title": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_and_<PERSON><PERSON><PERSON>_%C3%96nder"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, American wrestler (d. 2000)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON><PERSON></a>, American wrestler (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(wrestler)"}]}, {"year": "1967", "text": "<PERSON>, Namibian sprinter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Austrian tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Czech tennis player and sportscaster (d. 2017)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player and sportscaster (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player and sportscaster (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jana_Novotn%C3%A1"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wesley\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American country music singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON><PERSON>, English musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Badly_Drawn_Boy\" title=\"Badly Drawn Boy\"><PERSON><PERSON> Draw<PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Badly_Drawn_Boy\" title=\"Badly Drawn Boy\"><PERSON><PERSON> Drawn <PERSON></a>, English musician", "links": [{"title": "Badly Drawn Boy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Drawn_Boy"}]}, {"year": "1970", "text": "<PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian soprano", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patricia_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress and talk show host", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Spanish actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Verd%C3%BA\" title=\"Mari<PERSON> Verdú\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Verd%C3%BA\" title=\"Maribel Verdú\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maribel_Verd%C3%BA"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American guitarist and songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American comic book artist, writer, animator and creator of The Loud House", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book artist, writer, animator and creator of <a href=\"https://wikipedia.org/wiki/The_Loud_House\" title=\"The Loud House\">The Loud House</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book artist, writer, animator and creator of <a href=\"https://wikipedia.org/wiki/The_Loud_House\" title=\"The Loud House\">The Loud House</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Loud House", "link": "https://wikipedia.org/wiki/The_Loud_House"}]}, {"year": "1972", "text": "<PERSON>, American basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>-<PERSON>, American journalist, author, and educator", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and educator", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Norwegian singer, songwriter, and musician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Lene_Nystr%C3%B8m\" title=\"<PERSON><PERSON> Nystrøm\"><PERSON><PERSON></a>, Norwegian singer, songwriter, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lene_Nystr%C3%B8m\" title=\"<PERSON><PERSON> Nystrøm\"><PERSON><PERSON></a>, Norwegian singer, songwriter, and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lene_Nystr%C3%B8m"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Danish architect", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gels\" title=\"<PERSON><PERSON><PERSON> Ingels\"><PERSON><PERSON><PERSON></a>, Danish architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ingels\"><PERSON><PERSON><PERSON></a>, Danish architect", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian singer-songwriter and musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, Canadian singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)\" title=\"<PERSON> (singer-songwriter)\"><PERSON></a>, Canadian singer-songwriter and musician", "links": [{"title": "<PERSON> (singer-songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer-songwriter)"}]}, {"year": "1974", "text": "<PERSON>, American motorcycle designer, co-founded Orange County Choppers", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American motorcycle designer, co-founded <a href=\"https://wikipedia.org/wiki/Orange_County_Choppers\" title=\"Orange County Choppers\">Orange County Choppers</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American motorcycle designer, co-founded <a href=\"https://wikipedia.org/wiki/Orange_County_Choppers\" title=\"Orange County Choppers\">Orange County Choppers</a>", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Orange County Choppers", "link": "https://wikipedia.org/wiki/Orange_County_Choppers"}]}, {"year": "1976", "text": "<PERSON>, English cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Swiss skier", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%A9fago\" title=\"Did<PERSON> Défago\"><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%A9fago\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Didier_D%C3%A9fago"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese singer, songwriter, actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, songwriter, actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, songwriter, actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Primo%C5%BE_Brezec\" title=\"<PERSON><PERSON><PERSON>ž Brezec\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Primo%C5%BE_Brezec\" title=\"<PERSON><PERSON><PERSON><PERSON> Brezec\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Primo%C5%BE_<PERSON><PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lk"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Luke_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luke_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "Luke <PERSON>", "link": "https://wikipedia.org/wiki/Luke_<PERSON>hire"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish volleyball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Esra_G%C3%BCm%C3%BC%C5%9F\" title=\"<PERSON><PERSON><PERSON> Gümüş\"><PERSON><PERSON><PERSON></a>, Turkish volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esra_G%C3%BCm%C3%BC%C5%9F\" title=\"E<PERSON><PERSON> Gümüş\"><PERSON><PERSON><PERSON></a>, Turkish volleyball player", "links": [{"title": "Esra Gü<PERSON>üş", "link": "https://wikipedia.org/wiki/Esra_G%C3%BCm%C3%BC%C5%9F"}]}, {"year": "1984", "text": "<PERSON>, French tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Flar_Birinci\" title=\"Çağlar Birinci\"><PERSON><PERSON><PERSON><PERSON> Birinci</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Flar_Birinci\" title=\"Çağlar Birinci\"><PERSON>ağ<PERSON> Birinci</a>, Turkish footballer", "links": [{"title": "Çağlar Birinci", "link": "https://wikipedia.org/wiki/%C3%87a%C4%9Flar_<PERSON><PERSON>nci"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Belle\" title=\"Camilla Belle\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Belle\" title=\"Camilla Belle\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Camilla Belle", "link": "https://wikipedia.org/wiki/Camilla_Belle"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, South Korean-American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean-American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>., American race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese model and adult video actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and adult video actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and adult video actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brittany_Howard"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Danish ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English rower", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rower)"}]}, {"year": "1990", "text": "<PERSON>, Manx actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American-Turkish basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Turkish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Austrian rhythmic gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rup<PERSON>\"><PERSON><PERSON></a>, Austrian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rup<PERSON>\"><PERSON><PERSON></a>, Austrian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>., American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American baseball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1993", "text": "<PERSON>, American actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Lithuanian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>idukonyt%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Eiduk<PERSON>t%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joana_Eidukonyt%C4%97"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Cook Islands rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cook Islands rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cook Islands rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Moe<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American rapper", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and YouTuber", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Quadeca\" title=\"Quadeca\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quadeca\" title=\"Quadeca\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and YouTuber", "links": [{"title": "Quadeca", "link": "https://wikipedia.org/wiki/Quadeca"}]}, {"year": "2002", "text": "<PERSON>, American social media personality and singer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social media personality and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Australian cricketer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "534", "text": "<PERSON><PERSON><PERSON>, king of the Ostrogoths in Italy", "html": "534 - <a href=\"https://wikipedia.org/wiki/Athalaric\" title=\"At<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of the Ostrogoths in Italy", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Athalaric\" title=\"At<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of the Ostrogoths in Italy", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Athalaric"}]}, {"year": "829", "text": "<PERSON>, Byzantine emperor", "html": "829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, Byzantine emperor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "939", "text": "<PERSON><PERSON><PERSON> of Franconia", "html": "939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Franconia\" title=\"<PERSON><PERSON><PERSON> of Franconia\"><PERSON><PERSON><PERSON> of Franconia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Franconia\" title=\"<PERSON><PERSON><PERSON> of Franconia\"><PERSON><PERSON><PERSON> of Franconia</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Franconia", "link": "https://wikipedia.org/wiki/Eberhard_of_Franconia"}]}, {"year": "939", "text": "<PERSON>, Duke of Lorraine", "html": "939 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a>", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1264", "text": "<PERSON>", "html": "1264 - <a href=\"https://wikipedia.org/wiki/Pope_Urban_IV\" title=\"Pope Urban IV\">Pope Urban IV</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Urban_IV\" title=\"Pope Urban IV\"><PERSON> Urban IV</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Urban_IV"}]}, {"year": "1559", "text": "<PERSON><PERSON><PERSON> <PERSON> Mantua, French-Italian composer (b. 1483)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Mantua\" title=\"<PERSON><PERSON><PERSON> of Mantua\"><PERSON><PERSON><PERSON> <PERSON> Mantua</a>, French-Italian composer (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Mantua\" title=\"<PERSON><PERSON><PERSON> of Mantua\"><PERSON><PERSON><PERSON> <PERSON> Mantua</a>, French-Italian composer (b. 1483)", "links": [{"title": "J<PERSON>quet of Mantua", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Mantua"}]}, {"year": "1626", "text": "<PERSON>, 1st Count of Gondomar, Spanish academic and diplomat (b. 1567)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Diego_Sarmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar\" title=\"<PERSON>, 1st Count of Gondomar\"><PERSON>, 1st Count of Gondomar</a>, Spanish academic and diplomat (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar\" title=\"<PERSON>, 1st Count of Gondomar\"><PERSON>, 1st Count of Gondomar</a>, Spanish academic and diplomat (b. 1567)", "links": [{"title": "<PERSON>, 1st Count of Gondomar", "link": "https://wikipedia.org/wiki/Diego_Sarmiento_de_Acu%C3%B1a,_1st_Count_of_Gondomar"}]}, {"year": "1629", "text": "<PERSON>, Italian composer (b. 1584)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1584)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON>, French cardinal and theologian (b. 1575)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and theologian (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ru<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and theologian (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rulle"}]}, {"year": "1674", "text": "<PERSON> of Nassau-Siegen, officer in the Dutch Army (b. 1606)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Siegen\" title=\"<PERSON> of Nassau-Siegen\"><PERSON> of Nassau-Siegen</a>, officer in the Dutch Army (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Siegen\" title=\"<PERSON> of Nassau-Siegen\"><PERSON> of Nassau-Siegen</a>, officer in the Dutch Army (b. 1606)", "links": [{"title": "<PERSON> of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Nassau-Siegen"}]}, {"year": "1678", "text": "<PERSON>, Qing Chinese general (b. 1612)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Qing Chinese general (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wu <PERSON>\"><PERSON></a>, Qing Chinese general (b. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON>, French general (b. 1650)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, Ukrainian diplomat (b. 1639)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian diplomat (b. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian diplomat (b. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French historian and author (b. 1644)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Timol%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Timol%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French historian and author (b. 1644)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois-Timol%C3%A9on_de_<PERSON><PERSON>"}]}, {"year": "1746", "text": "<PERSON>, English admiral and politician (b. 1666)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, 4th Duke of Devonshire, English politician, Prime Minister of the United Kingdom (b. 1720)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Devonshire\" title=\"<PERSON>, 4th Duke of Devonshire\"><PERSON>, 4th Duke of Devonshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Devonshire\" title=\"<PERSON>, 4th Duke of Devonshire\"><PERSON>, 4th Duke of Devonshire</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1720)", "links": [{"title": "<PERSON>, 4th Duke of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Devonshire"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1780", "text": "<PERSON>, English soldier (b. 1750)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9"}]}, {"year": "1782", "text": "<PERSON>, English-born American general (b. 1732)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, English-born American general (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, English-born American general (b. 1732)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(general)"}]}, {"year": "1786", "text": "<PERSON>, 1st Viscount <PERSON>, English admiral and politician (b. 1725)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (b. 1725)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, American politician, Governor of Massachusetts (b. 1722)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1804", "text": "<PERSON><PERSON><PERSON>, French engineer (b. 1725)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer (b. 1725)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON>, Bulgarian educator, merchant and writer (b. 1789)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian educator, merchant and writer (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian educator, merchant and writer (b. 1789)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>ov"}]}, {"year": "1850", "text": "<PERSON>, English painter (b. 1784)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English painter (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, French mathematician, physicist, astronomer, and politician (b. 1786)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, astronomer, and politician (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, astronomer, and politician (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Arago"}]}, {"year": "1920", "text": "<PERSON>, German composer and conductor (b. 1838)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, German composer and conductor (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>ch\"><PERSON></a>, German composer and conductor (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Swedish physicist and chemist, Nobel Prize laureate (b. 1859)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hen<PERSON>\" title=\"<PERSON><PERSON><PERSON> A<PERSON>hen<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Romanian military leader and politician, 24th Prime Minister of Romania (b. 1859)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian military leader and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">Prime Minister of Romania</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian military leader and politician, 24th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">Prime Minister of Romania</a> (b. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Romania", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania"}]}, {"year": "1943", "text": "<PERSON>, English-Australian politician, 21st Premier of Tasmania (b. 1855)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1855)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1953", "text": "<PERSON>, American painter (b. 1870)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, French anarcho-syndicalist, sewed the first Flag of Algeria (b. 1901)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/%C3%89milie_Busquant\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French anarcho-syndicalist, sewed the first <a href=\"https://wikipedia.org/wiki/Flag_of_Algeria\" title=\"Flag of Algeria\">Flag of Algeria</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89milie_Busquant\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French anarcho-syndicalist, sewed the first <a href=\"https://wikipedia.org/wiki/Flag_of_Algeria\" title=\"Flag of Algeria\">Flag of Algeria</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89milie_Busquant"}, {"title": "Flag of Algeria", "link": "https://wikipedia.org/wiki/Flag_of_Algeria"}]}, {"year": "1955", "text": "<PERSON>, American swimmer and water polo player (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, French painter and sculptor (b. 1887)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American painter (b. 1883)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1883)", "links": [{"title": "<PERSON>ke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor and dancer (b. 1904)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Finnish runner (b. 1897)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>av<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian actor, director, and screenwriter (b. 1929)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor, director, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actor, director, and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Indian lawyer and politician (b. 1903)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American journalist and author (b. 1902)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Trinidadian-American activist, actress, and musician (b. 1920)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American activist, actress, and musician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American activist, actress, and musician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor (b. 1925)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Rock_Hudson\" title=\"Rock Hudson\"><PERSON></a>, American actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rock_Hudson\" title=\"Rock Hudson\"><PERSON></a>, American actor (b. 1925)", "links": [{"title": "Rock Hudson", "link": "https://wikipedia.org/wiki/Rock_Hudson"}]}, {"year": "1987", "text": "<PERSON>, English actress (b. 1906)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian-English biologist and zoologist, Nobel Prize laureate (b. 1915)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-English biologist and zoologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-English biologist and zoologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1988", "text": "<PERSON>, English car designer, designed the Mini (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer, designed the <a href=\"https://wikipedia.org/wiki/Mini\" title=\"Mini\">Mini</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer, designed the <a href=\"https://wikipedia.org/wiki/Mini\" title=\"Mini\">Mini</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mini", "link": "https://wikipedia.org/wiki/Mini"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indonesian politician, Vice President of Indonesia (b. 1912)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Hamengkubuwono_IX\" title=\"Hamengkubuwono IX\">Hamengkubuwono IX</a>, Indonesian politician, <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Vice Presidents of Indonesia\">Vice President of Indonesia</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamengkubuwono_IX\" title=\"Hamengkubuwono IX\">Hamengkubuwono IX</a>, Indonesian politician, <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia\" class=\"mw-redirect\" title=\"List of Vice Presidents of Indonesia\">Vice President of Indonesia</a> (b. 1912)", "links": [{"title": "Hamengkubuwono IX", "link": "https://wikipedia.org/wiki/Hamengkubuwono_IX"}, {"title": "List of Vice Presidents of Indonesia", "link": "https://wikipedia.org/wiki/List_of_Vice_Presidents_of_Indonesia"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian politician (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Hazen_Argue\" title=\"Hazen Argue\"><PERSON><PERSON></a>, Canadian politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hazen_Argue\" title=\"Hazen Argue\"><PERSON><PERSON></a>, Canadian politician (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rgue"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON> of Constantinople (b. 1914)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Demetrios_I_of_Constantinople\" title=\"Demetrios I of Constantinople\">Demetrios I of Constantinople</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Demetrios_I_of_Constantinople\" title=\"Demetrios I of Constantinople\">De<PERSON><PERSON>s I of Constantinople</a> (b. 1914)", "links": [{"title": "Demetrios I of Constantinople", "link": "https://wikipedia.org/wiki/Demetrios_I_of_Constantinople"}]}, {"year": "1996", "text": "<PERSON>, Canadian lawyer and politician, Premier of Quebec (b. 1933)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Bulgarian politician, 40th Prime Minister of Bulgaria (b. 1938)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian politician, 40th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian politician, 40th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a> (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria"}]}, {"year": "1998", "text": "<PERSON>, American actor, singer, and guitarist (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and guitarist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and guitarist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Autry"}]}, {"year": "1999", "text": "<PERSON>, German journalist and author (b. 1921)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian politician, Premier of South Australia (b. 1929)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "2001", "text": "<PERSON>, German composer and academic (b. 1906)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Austrian-American physicist and philosopher (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and philosopher (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and philosopher (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American scholar and politician, United States Secretary of Labor (b. 1914)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and politician, <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, American comedian and actor (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American author and playwright (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American author and playwright (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American author and playwright (b. 1945)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, American politician (b. 1938)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1938)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ge"}]}, {"year": "2006", "text": "<PERSON>, Hungarian-American mathematician (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American football player (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tex_Coulter"}]}, {"year": "2007", "text": "<PERSON>, American actor (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Irish Republican Army volunteer (b. 1902)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> volunteer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> volunteer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Singaporean lawyer and scholar (b. 1920)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Kwa Geok <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Singaporean lawyer and scholar (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Kwa Geok <PERSON>\"><PERSON><PERSON> <PERSON></a>, Singaporean lawyer and scholar (b. 1920)", "links": [{"title": "Kwa Geok <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa_<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese-American poet and activist (b. 1939)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%AD_Thi%E1%BB%87n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese-American poet and activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%AD_Thi%E1%BB%87n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese-American poet and activist (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%AD_Thi%E1%BB%87n"}]}, {"year": "2012", "text": "<PERSON>, Trinidadian-Canadian lawyer and activist (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Canadian lawyer and activist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-Canadian lawyer and activist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON>, English-Canadian psychologist, theorist, academic (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian psychologist, theorist, academic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English-Canadian psychologist, theorist, academic (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American mathematician and academic (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian footballer (b. 1955)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Irish author, playwright, and director (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, playwright, and director (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author, playwright, and director (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Jamaican-English bassist and composer (b. 1914)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Coleridge_Goode\" title=\"Coleridge Goode\"><PERSON><PERSON></a>, Jamaican-English bassist and composer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coleridge_Goode\" title=\"Coleridge Goode\"><PERSON><PERSON></a>, Jamaican-English bassist and composer (b. 1914)", "links": [{"title": "<PERSON>ridge Goode", "link": "https://wikipedia.org/wiki/Coleridge_Goode"}]}, {"year": "2015", "text": "<PERSON>, Scottish footballer and coach (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, British conductor (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British conductor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American musician (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Saudi journalist (b. 1958)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi journalist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, British lawyer (b. 1957)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British lawyer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British lawyer (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American bowler (b. 1940)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bowler (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American actress, model and activist for Native American civil rights (b. 1946)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Sacheen_Littlefeather\" title=\"Sache<PERSON> Littlefeather\"><PERSON><PERSON><PERSON></a>, American actress, model and activist for Native American civil rights (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>che<PERSON>_Littlefeather\" title=\"<PERSON>che<PERSON> Littlefeather\"><PERSON><PERSON><PERSON></a>, American actress, model and activist for Native American civil rights (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Littlefeather"}]}, {"year": "2023", "text": "<PERSON>, English footballer (b. 1944)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer (b. 1944)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "2024", "text": "<PERSON>, American professional golfer (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional golfer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional golfer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Indonesian politician (b. 1962)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian politician (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}