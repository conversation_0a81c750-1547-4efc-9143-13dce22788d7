{"date": "August 24", "url": "https://wikipedia.org/wiki/August_24", "data": {"Events": [{"year": "367", "text": "<PERSON><PERSON><PERSON>, son of Roman Emperor <PERSON><PERSON><PERSON>, is named co-Augustus at the age of eight by his father.", "html": "367 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\">Valentinian I</a>, is named co-<a href=\"https://wikipedia.org/wiki/<PERSON>_(honorific)\" class=\"mw-redirect\" title=\"Augustus (honorific)\"><PERSON></a> at the age of eight by his father.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\">Valentinian I</a>, is named co-<a href=\"https://wikipedia.org/wiki/Augustus_(honorific)\" class=\"mw-redirect\" title=\"Augustus (honorific)\"><PERSON></a> at the age of eight by his father.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ian"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "Valentinian I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "<PERSON> (honorific)", "link": "https://wikipedia.org/wiki/<PERSON>_(honorific)"}]}, {"year": "394", "text": "The Graffito of Esmet-Akhom, the latest known inscription in Egyptian hieroglyphs, is written.", "html": "394 - The <a href=\"https://wikipedia.org/wiki/Graf<PERSON><PERSON>_of_Esmet-Akhom\" title=\"<PERSON><PERSON><PERSON> of Esmet-Akhom\"><PERSON><PERSON><PERSON> of Esmet-Akhom</a>, the latest known inscription in <a href=\"https://wikipedia.org/wiki/Egyptian_hieroglyphs\" title=\"Egyptian hieroglyphs\">Egyptian hieroglyphs</a>, is written.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Esmet-Akhom\" title=\"<PERSON><PERSON><PERSON> of Esmet-Akhom\"><PERSON><PERSON><PERSON> of Esmet-Akhom</a>, the latest known inscription in <a href=\"https://wikipedia.org/wiki/Egyptian_hieroglyphs\" title=\"Egyptian hieroglyphs\">Egyptian hieroglyphs</a>, is written.", "links": [{"title": "Graffito of Esmet-Akhom", "link": "https://wikipedia.org/wiki/Graffito_of_Esm<PERSON>-<PERSON><PERSON>"}, {"title": "Egyptian hieroglyphs", "link": "https://wikipedia.org/wiki/Egyptian_hieroglyphs"}]}, {"year": "410", "text": "The Visigoths under King <PERSON><PERSON><PERSON> I begin to pillage Rome.", "html": "410 - The <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> under King <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\">Alaric I</a> begin to <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(410)\" title=\"Sack of Rome (410)\">pillage Rome</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> under King <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\">Alaric I</a> begin to <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(410)\" title=\"Sack of Rome (410)\">pillage Rome</a>.", "links": [{"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "Alaric I", "link": "https://wikipedia.org/wiki/Alaric_I"}, {"title": "Sack of Rome (410)", "link": "https://wikipedia.org/wiki/Sack_of_Rome_(410)"}]}, {"year": "1185", "text": "Sack of Thessalonica by the Normans.", "html": "1185 - <a href=\"https://wikipedia.org/wiki/Sack_of_Thessalonica_(1185)\" title=\"Sack of Thessalonica (1185)\">Sack of Thessalonica</a> by the Normans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Thessalonica_(1185)\" title=\"Sack of Thessalonica (1185)\">Sack of Thessalonica</a> by the Normans.", "links": [{"title": "Sack of Thessalonica (1185)", "link": "https://wikipedia.org/wiki/Sack_of_Thessalonica_(1185)"}]}, {"year": "1200", "text": "King <PERSON> of England, signer of the first Magna Carta, marries <PERSON> of Angoulême in Angoulême Cathedral.", "html": "1200 - King <a href=\"https://wikipedia.org/wiki/John_of_England\" class=\"mw-redirect\" title=\"John of England\"><PERSON> of England</a>, signer of the first <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\"><PERSON><PERSON> Carta</a>, marries <a href=\"https://wikipedia.org/wiki/Isabella_of_Angoul%C3%AAme\" title=\"<PERSON> of Angoulême\"><PERSON> of Angoulême</a> in <a href=\"https://wikipedia.org/wiki/Angoul%C3%AAme_Cathedral\" title=\"Angoulême Cathedral\">Angoulême Cathedral</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/John_<PERSON>_England\" class=\"mw-redirect\" title=\"John of England\"><PERSON> of England</a>, signer of the first <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\"><PERSON><PERSON> Carta</a>, marries <a href=\"https://wikipedia.org/wiki/Isabella_of_Angoul%C3%AAme\" title=\"<PERSON> of Angoulême\"><PERSON> of Angoulême</a> in <a href=\"https://wikipedia.org/wiki/Angoul%C3%AAme_Cathedral\" title=\"Angoulême Cathedral\">Angoulême Cathedral</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_of_England"}, {"title": "Magna Carta", "link": "https://wikipedia.org/wiki/Magna_Carta"}, {"title": "<PERSON> of Angoulême", "link": "https://wikipedia.org/wiki/<PERSON>_of_Angoul%C3%AAme"}, {"title": "Angoulême Cathedral", "link": "https://wikipedia.org/wiki/Angoul%C3%AAme_Cathedral"}]}, {"year": "1215", "text": "<PERSON> issues a bull declaring Magna Carta invalid.", "html": "1215 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope Innocent III</a> issues a bull declaring <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a> invalid.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_III\" title=\"Pope Innocent III\">Pope <PERSON> III</a> issues a bull declaring <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a> invalid.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}, {"title": "Magna Carta", "link": "https://wikipedia.org/wiki/Magna_Carta"}]}, {"year": "1349", "text": "Six thousand Jews are killed in Mainz after being blamed for the bubonic plague.", "html": "1349 - Six thousand Jews are killed in <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a> after being blamed for the <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a>.", "no_year_html": "Six thousand Jews are killed in <a href=\"https://wikipedia.org/wiki/Mainz\" title=\"Mainz\">Mainz</a> after being blamed for the <a href=\"https://wikipedia.org/wiki/Bubonic_plague\" title=\"Bubonic plague\">bubonic plague</a>.", "links": [{"title": "Mainz", "link": "https://wikipedia.org/wiki/Mainz"}, {"title": "Bubonic plague", "link": "https://wikipedia.org/wiki/Bubonic_plague"}]}, {"year": "1482", "text": "The town and castle of Berwick-upon-Tweed is captured from Scotland by an English army.", "html": "1482 - The town and castle of <a href=\"https://wikipedia.org/wiki/Capture_of_Berwick_(1482)\" class=\"mw-redirect\" title=\"Capture of Berwick (1482)\">Berwick-upon-Tweed is captured</a> from Scotland by an English army.", "no_year_html": "The town and castle of <a href=\"https://wikipedia.org/wiki/Capture_of_Berwick_(1482)\" class=\"mw-redirect\" title=\"Capture of Berwick (1482)\">Berwick-upon-Tweed is captured</a> from Scotland by an English army.", "links": [{"title": "Capture of Berwick (1482)", "link": "https://wikipedia.org/wiki/Capture_of_Berwick_(1482)"}]}, {"year": "1516", "text": "The Ottoman Empire under <PERSON><PERSON> defeats the Mamluk Sultanate and captures present-day Syria at the Battle of Marj Dabiq.", "html": "1516 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> under <a href=\"https://wikipedia.org/wiki/Selim_I\" title=\"Selim I\">Selim I</a> defeats the <a href=\"https://wikipedia.org/wiki/Mamluk_Sultanate_(Cairo)\" class=\"mw-redirect\" title=\"Mamluk Sultanate (Cairo)\">Mamluk Sultanate</a> and captures present-day Syria at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Dabiq\" title=\"Battle of Marj Dabiq\">Battle of Marj Dabiq</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> under <a href=\"https://wikipedia.org/wiki/Selim_I\" title=\"Selim I\"><PERSON><PERSON> I</a> defeats the <a href=\"https://wikipedia.org/wiki/Mamluk_Sultanate_(Cairo)\" class=\"mw-redirect\" title=\"Mamluk Sultanate (Cairo)\">Mamluk Sultanate</a> and captures present-day Syria at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marj_Dabiq\" title=\"Battle of Marj Dabiq\">Battle of Marj Dabiq</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I"}, {"title": "Mamluk Sultanate (Cairo)", "link": "https://wikipedia.org/wiki/Mamluk_Sultanate_(Cairo)"}, {"title": "Battle of Marj Dabiq", "link": "https://wikipedia.org/wiki/Battle_of_Marj_Dabiq"}]}, {"year": "1561", "text": "<PERSON> of Orange marries duchess <PERSON> of Saxony.", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> of Orange</a> marries duchess <a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"Anna of Saxony\"><PERSON> of Saxony</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Silent\"><PERSON> of Orange</a> marries duchess <a href=\"https://wikipedia.org/wiki/Anna_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Silent"}, {"title": "Anna of Saxony", "link": "https://wikipedia.org/wiki/Anna_of_Saxony"}]}, {"year": "1608", "text": "The first official English representative to India lands in Surat.", "html": "1608 - The first official English representative to India lands in <a href=\"https://wikipedia.org/wiki/Surat\" title=\"Surat\">Surat</a>.", "no_year_html": "The first official English representative to India lands in <a href=\"https://wikipedia.org/wiki/Surat\" title=\"Surat\">Surat</a>.", "links": [{"title": "Surat", "link": "https://wikipedia.org/wiki/Surat"}]}, {"year": "1643", "text": "A Dutch fleet establishes a new colony in the ruins of Valdivia in southern Chile.", "html": "1643 - A Dutch fleet <a href=\"https://wikipedia.org/wiki/Dutch_expedition_to_Valdivia\" title=\"Dutch expedition to Valdivia\">establishes a new colony</a> in the ruins of <a href=\"https://wikipedia.org/wiki/Valdivia\" title=\"Valdivia\">Valdivia</a> in <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">southern Chile</a>.", "no_year_html": "A Dutch fleet <a href=\"https://wikipedia.org/wiki/Dutch_expedition_to_Valdivia\" title=\"Dutch expedition to Valdivia\">establishes a new colony</a> in the ruins of <a href=\"https://wikipedia.org/wiki/Valdivia\" title=\"Valdivia\">Valdivia</a> in <a href=\"https://wikipedia.org/wiki/Zona_Sur\" title=\"Zona Sur\">southern Chile</a>.", "links": [{"title": "Dutch expedition to Valdivia", "link": "https://wikipedia.org/wiki/Dutch_expedition_to_Valdivia"}, {"title": "Valdivia", "link": "https://wikipedia.org/wiki/Valdivia"}, {"title": "Zona Sur", "link": "https://wikipedia.org/wiki/Zona_Sur"}]}, {"year": "1662", "text": "The 1662 Book of Common Prayer is legally enforced as the liturgy of the Church of England, precipitating the Great Ejection of Dissenter ministers from their benefices.", "html": "1662 - The <a href=\"https://wikipedia.org/wiki/Book_of_Common_Prayer_(1662)\" title=\"Book of Common Prayer (1662)\">1662 <i>Book of Common Prayer</i></a> is <a href=\"https://wikipedia.org/wiki/Act_of_Uniformity_1662\" title=\"Act of Uniformity 1662\">legally enforced</a> as the liturgy of the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>, precipitating the <a href=\"https://wikipedia.org/wiki/Great_Ejection\" title=\"Great Ejection\">Great Ejection</a> of <a href=\"https://wikipedia.org/wiki/English_Dissenters\" title=\"English Dissenters\">Dissenter</a> ministers from their <a href=\"https://wikipedia.org/wiki/Benefice\" title=\"Benefice\">benefices</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Book_of_Common_Prayer_(1662)\" title=\"Book of Common Prayer (1662)\">1662 <i>Book of Common Prayer</i></a> is <a href=\"https://wikipedia.org/wiki/Act_of_Uniformity_1662\" title=\"Act of Uniformity 1662\">legally enforced</a> as the liturgy of the <a href=\"https://wikipedia.org/wiki/Church_of_England\" title=\"Church of England\">Church of England</a>, precipitating the <a href=\"https://wikipedia.org/wiki/Great_Ejection\" title=\"Great Ejection\">Great Ejection</a> of <a href=\"https://wikipedia.org/wiki/English_Dissenters\" title=\"English Dissenters\">Dissenter</a> ministers from their <a href=\"https://wikipedia.org/wiki/Benefice\" title=\"Benefice\">benefices</a>.", "links": [{"title": "Book of Common Prayer (1662)", "link": "https://wikipedia.org/wiki/Book_of_Common_Prayer_(1662)"}, {"title": "Act of Uniformity 1662", "link": "https://wikipedia.org/wiki/Act_of_Uniformity_1662"}, {"title": "Church of England", "link": "https://wikipedia.org/wiki/Church_of_England"}, {"title": "Great Ejection", "link": "https://wikipedia.org/wiki/Great_Ejection"}, {"title": "English Dissenters", "link": "https://wikipedia.org/wiki/English_Dissenters"}, {"title": "Benefice", "link": "https://wikipedia.org/wiki/Benefice"}]}, {"year": "1682", "text": "<PERSON> receives the area that is now the state of Delaware, and adds it to his colony of Pennsylvania.", "html": "1682 - <a href=\"https://wikipedia.org/wiki/William_Penn\" title=\"William Penn\"><PERSON></a> receives the area that is now the state of <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a>, and adds it to his <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> of <a href=\"https://wikipedia.org/wiki/Province_of_Pennsylvania\" title=\"Province of Pennsylvania\">Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Penn\" title=\"William Penn\"><PERSON></a> receives the area that is now the state of <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a>, and adds it to his <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> of <a href=\"https://wikipedia.org/wiki/Province_of_Pennsylvania\" title=\"Province of Pennsylvania\">Pennsylvania</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Delaware", "link": "https://wikipedia.org/wiki/Delaware"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}, {"title": "Province of Pennsylvania", "link": "https://wikipedia.org/wiki/Province_of_Pennsylvania"}]}, {"year": "1690", "text": "<PERSON> of the East India Company establishes a factory in Calcutta, an event formerly considered the founding of the city (in 2003 the Calcutta High Court ruled that the city's foundation date is unknown).", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cha<PERSON>\" title=\"<PERSON> Charnock\"><PERSON> Charnock</a> of the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> establishes a <a href=\"https://wikipedia.org/wiki/Factory_(trading_post)\" title=\"Factory (trading post)\">factory</a> in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, an event formerly considered the founding of the city (in 2003 the <a href=\"https://wikipedia.org/wiki/Calcutta_High_Court\" title=\"Calcutta High Court\">Calcutta High Court</a> ruled that the city's foundation date is unknown).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Charnock\"><PERSON>ck</a> of the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> establishes a <a href=\"https://wikipedia.org/wiki/Factory_(trading_post)\" title=\"Factory (trading post)\">factory</a> in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, an event formerly considered the founding of the city (in 2003 the <a href=\"https://wikipedia.org/wiki/Calcutta_High_Court\" title=\"Calcutta High Court\">Calcutta High Court</a> ruled that the city's foundation date is unknown).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "Factory (trading post)", "link": "https://wikipedia.org/wiki/Factory_(trading_post)"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}, {"title": "Calcutta High Court", "link": "https://wikipedia.org/wiki/Calcutta_High_Court"}]}, {"year": "1743", "text": "The War of the Hats: The Swedish army surrenders to the Russians in Helsinki, ending the war and starting Lesser Wrath.", "html": "1743 - <a href=\"https://wikipedia.org/wiki/The_War_of_the_Hats\" class=\"mw-redirect\" title=\"The War of the Hats\">The War of the Hats</a>: The <a href=\"https://wikipedia.org/wiki/Swedish_army\" class=\"mw-redirect\" title=\"Swedish army\">Swedish army</a> surrenders to the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russians</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, ending the war and starting <i>Lesser Wrath</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_War_of_the_Hats\" class=\"mw-redirect\" title=\"The War of the Hats\">The War of the Hats</a>: The <a href=\"https://wikipedia.org/wiki/Swedish_army\" class=\"mw-redirect\" title=\"Swedish army\">Swedish army</a> surrenders to the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russians</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, ending the war and starting <i>Lesser Wrath</i>.", "links": [{"title": "The War of the Hats", "link": "https://wikipedia.org/wiki/The_War_of_the_Hats"}, {"title": "Swedish army", "link": "https://wikipedia.org/wiki/Swedish_army"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1781", "text": "American Revolutionary War: A small force of Pennsylvania militia is ambushed and overwhelmed by an American Indian group, which forces <PERSON> to abandon his attempt to attack Detroit.", "html": "1781 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A small force of <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> <a href=\"https://wikipedia.org/wiki/Militia_(United_States)\" title=\"Militia (United States)\">militia</a> is <a href=\"https://wikipedia.org/wiki/<PERSON>ry%27s_Defeat\" title=\"<PERSON><PERSON>'s Defeat\">ambushed and overwhelmed</a> by an American Indian group, which forces <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to abandon his attempt to attack <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A small force of <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> <a href=\"https://wikipedia.org/wiki/Militia_(United_States)\" title=\"Militia (United States)\">militia</a> is <a href=\"https://wikipedia.org/wiki/Lochry%27s_Defeat\" title=\"<PERSON><PERSON>'s Defeat\">ambushed and overwhelmed</a> by an American Indian group, which forces <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to abandon his attempt to attack <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Militia (United States)", "link": "https://wikipedia.org/wiki/Militia_(United_States)"}, {"title": "<PERSON><PERSON>'s Defeat", "link": "https://wikipedia.org/wiki/Lochry%27s_Defeat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1789", "text": "The first naval battle of the Svensksund began in the Gulf of Finland.", "html": "1789 - The first <a href=\"https://wikipedia.org/wiki/Battle_of_Svensksund_(1789)\" title=\"Battle of Svensksund (1789)\">naval battle of the Svensksund</a> began in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Finland\" title=\"Gulf of Finland\">Gulf of Finland</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Battle_of_Svensksund_(1789)\" title=\"Battle of Svensksund (1789)\">naval battle of the Svensksund</a> began in the <a href=\"https://wikipedia.org/wiki/Gulf_of_Finland\" title=\"Gulf of Finland\">Gulf of Finland</a>.", "links": [{"title": "Battle of Svensksund (1789)", "link": "https://wikipedia.org/wiki/Battle_of_Svensksund_(1789)"}, {"title": "Gulf of Finland", "link": "https://wikipedia.org/wiki/Gulf_of_Finland"}]}, {"year": "1812", "text": "Peninsular War: A coalition of Spanish, British, and Portuguese forces succeed in lifting the two-and-a-half-year-long Siege of Cádiz.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: A coalition of Spanish, <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> forces succeed in lifting the two-and-a-half-year-long <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: A coalition of Spanish, <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> forces succeed in lifting the two-and-a-half-year-long <a href=\"https://wikipedia.org/wiki/Siege_of_C%C3%A1diz\" title=\"Siege of Cádiz\">Siege of Cádiz</a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}, {"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}, {"title": "Siege of Cádiz", "link": "https://wikipedia.org/wiki/Siege_of_C%C3%A1diz"}]}, {"year": "1814", "text": "British troops capture Washington, D.C. and set the Presidential Mansion, Capitol, Navy Yard and many other public buildings ablaze.", "html": "1814 - British troops <a href=\"https://wikipedia.org/wiki/Burning_of_Washington\" title=\"Burning of Washington\">capture Washington, D.C.</a> and set the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">Presidential Mansion</a>, <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">Capitol</a>, <a href=\"https://wikipedia.org/wiki/Washington_Navy_Yard\" title=\"Washington Navy Yard\">Navy Yard</a> and many other public buildings ablaze.", "no_year_html": "British troops <a href=\"https://wikipedia.org/wiki/Burning_of_Washington\" title=\"Burning of Washington\">capture Washington, D.C.</a> and set the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">Presidential Mansion</a>, <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">Capitol</a>, <a href=\"https://wikipedia.org/wiki/Washington_Navy_Yard\" title=\"Washington Navy Yard\">Navy Yard</a> and many other public buildings ablaze.", "links": [{"title": "Burning of Washington", "link": "https://wikipedia.org/wiki/Burning_of_Washington"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}, {"title": "Washington Navy Yard", "link": "https://wikipedia.org/wiki/Washington_Navy_Yard"}]}, {"year": "1815", "text": "The modern Constitution of the Netherlands is signed.", "html": "1815 - The modern <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Netherlands\" title=\"Constitution of the Netherlands\">Constitution of the Netherlands</a> is signed.", "no_year_html": "The modern <a href=\"https://wikipedia.org/wiki/Constitution_of_the_Netherlands\" title=\"Constitution of the Netherlands\">Constitution of the Netherlands</a> is signed.", "links": [{"title": "Constitution of the Netherlands", "link": "https://wikipedia.org/wiki/Constitution_of_the_Netherlands"}]}, {"year": "1816", "text": "The Treaty of St. Louis is signed in St. Louis, Missouri.", "html": "1816 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_St._Louis_(1816)\" title=\"Treaty of St. Louis (1816)\">Treaty of St. Louis</a> is signed in <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_St._Louis_(1816)\" title=\"Treaty of St. Louis (1816)\">Treaty of St. Louis</a> is signed in <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>.", "links": [{"title": "Treaty of St. Louis (1816)", "link": "https://wikipedia.org/wiki/Treaty_of_St._Louis_(1816)"}, {"title": "St. Louis, Missouri", "link": "https://wikipedia.org/wiki/St._Louis,_Missouri"}]}, {"year": "1820", "text": "Constitutionalist insurrection at Oporto, Portugal.", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Constitutionalism\" title=\"Constitutionalism\">Constitutionalist</a> <a href=\"https://wikipedia.org/wiki/History_of_Portugal#Crises_of_the_nineteenth_century\" title=\"History of Portugal\">insurrection</a> at <a href=\"https://wikipedia.org/wiki/Porto\" title=\"Porto\">Oporto</a>, Portugal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constitutionalism\" title=\"Constitutionalism\">Constitutionalist</a> <a href=\"https://wikipedia.org/wiki/History_of_Portugal#Crises_of_the_nineteenth_century\" title=\"History of Portugal\">insurrection</a> at <a href=\"https://wikipedia.org/wiki/Porto\" title=\"Porto\">Oporto</a>, Portugal.", "links": [{"title": "Constitutionalism", "link": "https://wikipedia.org/wiki/Constitutionalism"}, {"title": "History of Portugal", "link": "https://wikipedia.org/wiki/History_of_Portugal#Crises_of_the_nineteenth_century"}, {"title": "Porto", "link": "https://wikipedia.org/wiki/Porto"}]}, {"year": "1821", "text": "The Treaty of Córdoba is signed in Córdoba, now in Veracruz, Mexico, concluding the Mexican War of Independence from Spain.", "html": "1821 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_C%C3%B3rdoba\" title=\"Treaty of Córdoba\">Treaty of Córdoba</a> is signed in <a href=\"https://wikipedia.org/wiki/C%C3%B3rdoba,_Veracruz\" title=\"Córdoba, Veracruz\">Córdoba</a>, now in <a href=\"https://wikipedia.org/wiki/Veracruz_(state)\" class=\"mw-redirect\" title=\"Veracruz (state)\">Veracruz</a>, Mexico, concluding the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a> from Spain.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_C%C3%B3rdoba\" title=\"Treaty of Córdoba\">Treaty of Córdoba</a> is signed in <a href=\"https://wikipedia.org/wiki/C%C3%B3rdoba,_Veracruz\" title=\"Córdoba, Veracruz\">Córdoba</a>, now in <a href=\"https://wikipedia.org/wiki/Veracruz_(state)\" class=\"mw-redirect\" title=\"Veracruz (state)\">Veracruz</a>, Mexico, concluding the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a> from Spain.", "links": [{"title": "Treaty of Córdoba", "link": "https://wikipedia.org/wiki/Treaty_of_C%C3%B3rdoba"}, {"title": "Córdoba, Veracruz", "link": "https://wikipedia.org/wiki/C%C3%B3rdoba,_Veracruz"}, {"title": "Veracruz (state)", "link": "https://wikipedia.org/wiki/Veracruz_(state)"}, {"title": "Mexican War of Independence", "link": "https://wikipedia.org/wiki/Mexican_War_of_Independence"}]}, {"year": "1857", "text": "The Panic of 1857 begins, setting off one of the most severe economic crises in United States history.", "html": "1857 - The <a href=\"https://wikipedia.org/wiki/Panic_of_1857\" title=\"Panic of 1857\">Panic of 1857</a> begins, setting off one of the most severe economic crises in United States history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Panic_of_1857\" title=\"Panic of 1857\">Panic of 1857</a> begins, setting off one of the most severe economic crises in United States history.", "links": [{"title": "Panic of 1857", "link": "https://wikipedia.org/wiki/Panic_of_1857"}]}, {"year": "1870", "text": "The <PERSON><PERSON><PERSON> expedition reaches Manitoba to end the Red River Rebellion.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/Wols<PERSON>_expedition\" title=\"Wolseley expedition\">Wolseley expedition</a> reaches <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> to end the <a href=\"https://wikipedia.org/wiki/Red_River_Rebellion\" title=\"Red River Rebellion\">Red River Rebellion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wols<PERSON>_expedition\" title=\"Wolseley expedition\">Wolseley expedition</a> reaches <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> to end the <a href=\"https://wikipedia.org/wiki/Red_River_Rebellion\" title=\"Red River Rebellion\">Red River Rebellion</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> expedition", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_expedition"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}, {"title": "Red River Rebellion", "link": "https://wikipedia.org/wiki/Red_River_Rebellion"}]}, {"year": "1898", "text": "Count <PERSON>, Foreign Minister of Russia presents a rescript that convoked the First Hague Peace Conference.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Count <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Russia\" class=\"mw-redirect\" title=\"Foreign Minister of Russia\">Foreign Minister of Russia</a> presents a <i>rescript</i> that convoked the <a href=\"https://wikipedia.org/wiki/First_Hague_Peace_Conference\" class=\"mw-redirect\" title=\"First Hague Peace Conference\">First Hague Peace Conference</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Count <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_Russia\" class=\"mw-redirect\" title=\"Foreign Minister of Russia\">Foreign Minister of Russia</a> presents a <i>rescript</i> that convoked the <a href=\"https://wikipedia.org/wiki/First_Hague_Peace_Conference\" class=\"mw-redirect\" title=\"First Hague Peace Conference\">First Hague Peace Conference</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Foreign Minister of Russia", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_Russia"}, {"title": "First Hague Peace Conference", "link": "https://wikipedia.org/wiki/First_Hague_Peace_Conference"}]}, {"year": "1909", "text": "Workers start pouring concrete for the Panama Canal.", "html": "1909 - Workers start pouring concrete for the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "no_year_html": "Workers start pouring concrete for the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "links": [{"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}]}, {"year": "1911", "text": "<PERSON> is elected and sworn in as the first President of Portugal.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected and sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected and sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1914", "text": "World War I: German troops capture Namur.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German troops <a href=\"https://wikipedia.org/wiki/Siege_of_Namur_(1914)\" title=\"Siege of Namur (1914)\">capture Namur</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: German troops <a href=\"https://wikipedia.org/wiki/Siege_of_Namur_(1914)\" title=\"Siege of Namur (1914)\">capture Namur</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Siege of Namur (1914)", "link": "https://wikipedia.org/wiki/Siege_of_Namur_(1914)"}]}, {"year": "1914", "text": "World War I: The Battle of Cer ends as the first Allied victory in the war.", "html": "1914 - World War I: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cer\" title=\"Battle of Cer\">Battle of Cer</a> ends as the first Allied victory in the war.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Battle_of_Cer\" title=\"Battle of Cer\">Battle of Cer</a> ends as the first Allied victory in the war.", "links": [{"title": "Battle of Cer", "link": "https://wikipedia.org/wiki/Battle_of_Cer"}]}, {"year": "1929", "text": "Second day of two-day Hebron massacre during the 1929 Palestine riots: Arab attacks on the Jewish community in Hebron in the British Mandate of Palestine, result in the death of 65-68 Jews; the remaining Jews are forced to flee the city.", "html": "1929 - Second day of two-day <a href=\"https://wikipedia.org/wiki/1929_Hebron_massacre\" title=\"1929 Hebron massacre\">Hebron massacre</a> during the <a href=\"https://wikipedia.org/wiki/1929_Palestine_riots\" title=\"1929 Palestine riots\">1929 Palestine riots</a>: <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Arab</a> attacks on the Jewish community in <a href=\"https://wikipedia.org/wiki/Hebron\" title=\"Hebron\">Hebron</a> in the <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">British Mandate of Palestine</a>, result in the death of 65-68 Jews; the remaining Jews are forced to flee the city.", "no_year_html": "Second day of two-day <a href=\"https://wikipedia.org/wiki/1929_Hebron_massacre\" title=\"1929 Hebron massacre\">Hebron massacre</a> during the <a href=\"https://wikipedia.org/wiki/1929_Palestine_riots\" title=\"1929 Palestine riots\">1929 Palestine riots</a>: <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Arab</a> attacks on the Jewish community in <a href=\"https://wikipedia.org/wiki/Hebron\" title=\"Hebron\">Hebron</a> in the <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">British Mandate of Palestine</a>, result in the death of 65-68 Jews; the remaining Jews are forced to flee the city.", "links": [{"title": "1929 Hebron massacre", "link": "https://wikipedia.org/wiki/1929_Hebron_massacre"}, {"title": "1929 Palestine riots", "link": "https://wikipedia.org/wiki/1929_Palestine_riots"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}, {"title": "Hebron", "link": "https://wikipedia.org/wiki/Hebron"}, {"title": "Mandatory Palestine", "link": "https://wikipedia.org/wiki/Mandatory_Palestine"}]}, {"year": "1931", "text": "Resignation of the United Kingdom's Second Labour Government. Formation of the UK National Government.", "html": "1931 - Resignation of the United Kingdom's <a href=\"https://wikipedia.org/wiki/Second_Labour_Government\" class=\"mw-redirect\" title=\"Second Labour Government\">Second Labour Government</a>. Formation of the <a href=\"https://wikipedia.org/wiki/UK_National_Government\" class=\"mw-redirect\" title=\"UK National Government\">UK National Government</a>.", "no_year_html": "Resignation of the United Kingdom's <a href=\"https://wikipedia.org/wiki/Second_Labour_Government\" class=\"mw-redirect\" title=\"Second Labour Government\">Second Labour Government</a>. Formation of the <a href=\"https://wikipedia.org/wiki/UK_National_Government\" class=\"mw-redirect\" title=\"UK National Government\">UK National Government</a>.", "links": [{"title": "Second Labour Government", "link": "https://wikipedia.org/wiki/Second_Labour_Government"}, {"title": "UK National Government", "link": "https://wikipedia.org/wiki/UK_National_Government"}]}, {"year": "1932", "text": "<PERSON> becomes the first woman to fly across the United States non-stop (from Los Angeles to Newark, New Jersey).", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to fly across the United States non-stop (from Los Angeles to <a href=\"https://wikipedia.org/wiki/Newark,_New_Jersey\" title=\"Newark, New Jersey\">Newark, New Jersey</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to fly across the United States non-stop (from Los Angeles to <a href=\"https://wikipedia.org/wiki/Newark,_New_Jersey\" title=\"Newark, New Jersey\">Newark, New Jersey</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newark, New Jersey", "link": "https://wikipedia.org/wiki/Newark,_New_Jersey"}]}, {"year": "1933", "text": "The Crescent Limited train derails in Washington, D.C., after the bridge it is crossing is washed out by the 1933 Chesapeake-Potomac hurricane.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Crescent_(train)\" title=\"Crescent (train)\">Crescent Limited</a> train <a href=\"https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge#Wreck_of_the_Crescent_Limited\" title=\"Amtrak Railroad Anacostia Bridge\">derails</a> in Washington, D.C., after the <a href=\"https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge\" title=\"Amtrak Railroad Anacostia Bridge\">bridge</a> it is crossing is washed out by the <a href=\"https://wikipedia.org/wiki/1933_Chesapeake%E2%80%93Potomac_hurricane\" title=\"1933 Chesapeake-Potomac hurricane\">1933 Chesapeake-Potomac hurricane</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Crescent_(train)\" title=\"Crescent (train)\">Crescent Limited</a> train <a href=\"https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge#Wreck_of_the_Crescent_Limited\" title=\"Amtrak Railroad Anacostia Bridge\">derails</a> in Washington, D.C., after the <a href=\"https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge\" title=\"Amtrak Railroad Anacostia Bridge\">bridge</a> it is crossing is washed out by the <a href=\"https://wikipedia.org/wiki/1933_Chesapeake%E2%80%93Potomac_hurricane\" title=\"1933 Chesapeake-Potomac hurricane\">1933 Chesapeake-Potomac hurricane</a>.", "links": [{"title": "Crescent (train)", "link": "https://wikipedia.org/wiki/Crescent_(train)"}, {"title": "Amtrak Railroad Anacostia Bridge", "link": "https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge#Wreck_of_the_Crescent_Limited"}, {"title": "Amtrak Railroad Anacostia Bridge", "link": "https://wikipedia.org/wiki/Amtrak_Railroad_Anacostia_Bridge"}, {"title": "1933 Chesapeake-Potomac hurricane", "link": "https://wikipedia.org/wiki/1933_Chesapeake%E2%80%93Potomac_hurricane"}]}, {"year": "1936", "text": "The Australian Antarctic Territory is created.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Australian_Antarctic_Territory\" title=\"Australian Antarctic Territory\">Australian Antarctic Territory</a> is created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australian_Antarctic_Territory\" title=\"Australian Antarctic Territory\">Australian Antarctic Territory</a> is created.", "links": [{"title": "Australian Antarctic Territory", "link": "https://wikipedia.org/wiki/Australian_Antarctic_Territory"}]}, {"year": "1937", "text": "Spanish Civil War: the Basque Army surrenders to the Italian Corpo Truppe Volontarie following the Santoña Agreement.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: the <a href=\"https://wikipedia.org/wiki/Basque_Army\" class=\"mw-redirect\" title=\"Basque Army\">Basque Army</a> surrenders to the Italian <a href=\"https://wikipedia.org/wiki/Corpo_Truppe_Volontarie\" title=\"Corpo Truppe Volontarie\">Corpo Truppe Volontarie</a> following the <a href=\"https://wikipedia.org/wiki/Santo%C3%B1a_Agreement\" title=\"Santoña Agreement\">Santoña Agreement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: the <a href=\"https://wikipedia.org/wiki/Basque_Army\" class=\"mw-redirect\" title=\"Basque Army\">Basque Army</a> surrenders to the Italian <a href=\"https://wikipedia.org/wiki/Corpo_Truppe_Volontarie\" title=\"Corpo Truppe Volontarie\">Corpo Truppe Volontarie</a> following the <a href=\"https://wikipedia.org/wiki/Santo%C3%B1a_Agreement\" title=\"Santoña Agreement\">Santoña Agreement</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Basque Army", "link": "https://wikipedia.org/wiki/Basque_Army"}, {"title": "Corpo Truppe Volontarie", "link": "https://wikipedia.org/wiki/Corpo_Truppe_Volontarie"}, {"title": "Santoña Agreement", "link": "https://wikipedia.org/wiki/Santo%C3%B1a_Agreement"}]}, {"year": "1937", "text": "Spanish Civil War: Sovereign Council of Asturias and León is proclaimed in Gijón.", "html": "1937 - Spanish Civil War: <a href=\"https://wikipedia.org/wiki/Sovereign_Council_of_Asturias_and_Le%C3%B3n\" title=\"Sovereign Council of Asturias and León\">Sovereign Council of Asturias and León</a> is proclaimed in <a href=\"https://wikipedia.org/wiki/Gij%C3%B3n\" title=\"Gij<PERSON>\">Gijón</a>.", "no_year_html": "Spanish Civil War: <a href=\"https://wikipedia.org/wiki/Sovereign_Council_of_Asturias_and_Le%C3%B3n\" title=\"Sovereign Council of Asturias and León\">Sovereign Council of Asturias and León</a> is proclaimed in <a href=\"https://wikipedia.org/wiki/Gij%C3%B3n\" title=\"Gij<PERSON>\">Gijón</a>.", "links": [{"title": "Sovereign Council of Asturias and León", "link": "https://wikipedia.org/wiki/Sovereign_Council_of_Asturias_and_Le%C3%B3n"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gij%C3%B3n"}]}, {"year": "1938", "text": "Kweilin incident: A Japanese warplane shoots down the Kweilin, a Chinese civilian airliner, killing 14. It is the first recorded instance of a civilian airliner being shot down.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Kweilin_incident\" title=\"Kweilin incident\">Kweilin incident</a>: A Japanese warplane shoots down the <i>Kweilin</i>, a Chinese civilian airliner, killing 14. It is the <a href=\"https://wikipedia.org/wiki/List_of_airliner_shootdown_incidents\" title=\"List of airliner shootdown incidents\">first recorded instance</a> of a civilian airliner being shot down.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kweilin_incident\" title=\"Kweilin incident\">Kweilin incident</a>: A Japanese warplane shoots down the <i>Kweilin</i>, a Chinese civilian airliner, killing 14. It is the <a href=\"https://wikipedia.org/wiki/List_of_airliner_shootdown_incidents\" title=\"List of airliner shootdown incidents\">first recorded instance</a> of a civilian airliner being shot down.", "links": [{"title": "Kweilin incident", "link": "https://wikipedia.org/wiki/Kweilin_incident"}, {"title": "List of airliner shootdown incidents", "link": "https://wikipedia.org/wiki/List_of_airliner_shootdown_incidents"}]}, {"year": "1941", "text": "The Holocaust: <PERSON> orders the cessation of Nazi Germany's systematic T4 euthanasia program of the mentally ill and the handicapped due to protests, although killings continue for the remainder of the war.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> orders the cessation of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>'s systematic <a href=\"https://wikipedia.org/wiki/Action_T4\" class=\"mw-redirect\" title=\"Action T4\">T4 euthanasia program</a> of the <a href=\"https://wikipedia.org/wiki/Mental_illness\" class=\"mw-redirect\" title=\"Mental illness\">mentally ill</a> and the <a href=\"https://wikipedia.org/wiki/Disability\" title=\"Disability\">handicapped</a> due to protests, although killings continue for the remainder of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> orders the cessation of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>'s systematic <a href=\"https://wikipedia.org/wiki/Action_T4\" class=\"mw-redirect\" title=\"Action T4\">T4 euthanasia program</a> of the <a href=\"https://wikipedia.org/wiki/Mental_illness\" class=\"mw-redirect\" title=\"Mental illness\">mentally ill</a> and the <a href=\"https://wikipedia.org/wiki/Disability\" title=\"Disability\">handicapped</a> due to protests, although killings continue for the remainder of the war.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Action T4", "link": "https://wikipedia.org/wiki/Action_T4"}, {"title": "Mental illness", "link": "https://wikipedia.org/wiki/Mental_illness"}, {"title": "Disability", "link": "https://wikipedia.org/wiki/Disability"}]}, {"year": "1942", "text": "World War II: The Battle of the Eastern Solomons. Japanese aircraft carrier Ryūjō is sunk, with the loss of seven officers and 113 crewmen. The US carrier USS Enterprise is heavily damaged.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons\" title=\"Battle of the Eastern Solomons\">Battle of the Eastern Solomons</a>. Japanese <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <i><a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Ry%C5%ABj%C5%8D\" title=\"Japanese aircraft carrier R<PERSON>ūjō\">R<PERSON><PERSON><PERSON></a></i> is sunk, with the loss of seven officers and 113 crewmen. The US carrier <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CV-6)\" title=\"USS Enterprise (CV-6)\">USS <i>Enterprise</i></a> is heavily damaged.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons\" title=\"Battle of the Eastern Solomons\">Battle of the Eastern Solomons</a>. Japanese <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <i><a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Ry%C5%ABj%C5%8D\" title=\"Japanese aircraft carrier <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a></i> is sunk, with the loss of seven officers and 113 crewmen. The US carrier <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CV-6)\" title=\"USS Enterprise (CV-6)\">USS <i>Enterprise</i></a> is heavily damaged.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Eastern Solomons", "link": "https://wikipedia.org/wiki/Battle_of_the_Eastern_Solomons"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "Japanese aircraft carrier Ryūjō", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_Ry%C5%ABj%C5%8D"}, {"title": "USS Enterprise (CV-6)", "link": "https://wikipedia.org/wiki/USS_Enterprise_(CV-6)"}]}, {"year": "1944", "text": "World War II: Allied troops begin the attack on Paris.", "html": "1944 - World War II: Allied troops begin <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">the attack on Paris</a>.", "no_year_html": "World War II: Allied troops begin <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">the attack on Paris</a>.", "links": [{"title": "Liberation of Paris", "link": "https://wikipedia.org/wiki/Liberation_of_Paris"}]}, {"year": "1949", "text": "The treaty creating the North Atlantic Treaty Organization goes into effect.", "html": "1949 - The treaty creating the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">North Atlantic Treaty Organization</a> goes into effect.", "no_year_html": "The treaty creating the <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">North Atlantic Treaty Organization</a> goes into effect.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1950", "text": "<PERSON> becomes the first black U.S. delegate to the United Nations.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first black U.S. delegate to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first black U.S. delegate to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1951", "text": "United Air Lines Flight 615 crashes near Decoto, California, killing 50 people.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_615\" title=\"United Air Lines Flight 615\">United Air Lines Flight 615</a> crashes near <a href=\"https://wikipedia.org/wiki/Decoto,_California\" title=\"Decoto, California\">Decoto, California</a>, killing 50 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Air_Lines_Flight_615\" title=\"United Air Lines Flight 615\">United Air Lines Flight 615</a> crashes near <a href=\"https://wikipedia.org/wiki/Decoto,_California\" title=\"Decoto, California\">Decoto, California</a>, killing 50 people.", "links": [{"title": "United Air Lines Flight 615", "link": "https://wikipedia.org/wiki/United_Air_Lines_Flight_615"}, {"title": "Decoto, California", "link": "https://wikipedia.org/wiki/Decoto,_California"}]}, {"year": "1954", "text": "The Communist Control Act goes into effect, outlawing the Communist Party in the United States.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/Communist_Control_Act\" class=\"mw-redirect\" title=\"Communist Control Act\">Communist Control Act</a> goes into effect, outlawing the <a href=\"https://wikipedia.org/wiki/Communist_Party_USA\" title=\"Communist Party USA\">Communist Party in the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Communist_Control_Act\" class=\"mw-redirect\" title=\"Communist Control Act\">Communist Control Act</a> goes into effect, outlawing the <a href=\"https://wikipedia.org/wiki/Communist_Party_USA\" title=\"Communist Party USA\">Communist Party in the United States</a>.", "links": [{"title": "Communist Control Act", "link": "https://wikipedia.org/wiki/Communist_Control_Act"}, {"title": "Communist Party USA", "link": "https://wikipedia.org/wiki/Communist_Party_USA"}]}, {"year": "1954", "text": "Vice president <PERSON> takes office as president of Brazil, following the suicide of <PERSON><PERSON><PERSON>.", "html": "1954 - Vice president <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Caf%C3%A9_Filho\" class=\"mw-redirect\" title=\"João Café Filho\"><PERSON></a> takes office as president of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, following the suicide of <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Vice president <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Caf%C3%A9_Fil<PERSON>\" class=\"mw-redirect\" title=\"João Café Filho\"><PERSON></a> takes office as president of <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a>, following the suicide of <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "João Café Filho", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Caf%C3%A9_Filho"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Get%C3%BAlio_Vargas"}]}, {"year": "1963", "text": "Buddhist crisis: As a result of the Xá Lợi Pagoda raids, the US State Department cables the United States Embassy, Saigon to encourage Army of the Republic of Vietnam generals to launch a coup against President <PERSON><PERSON> if he did not remove his brother <PERSON><PERSON>.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: As a result of the <a href=\"https://wikipedia.org/wiki/X%C3%A1_L%E1%BB%A3i_Pagoda_raids\" title=\"Xá Lợi Pagoda raids\">Xá Lợi Pagoda raids</a>, the <a href=\"https://wikipedia.org/wiki/US_State_Department\" class=\"mw-redirect\" title=\"US State Department\">US State Department</a> <a href=\"https://wikipedia.org/wiki/Cable_243\" title=\"Cable 243\">cables</a> the <a href=\"https://wikipedia.org/wiki/United_States_Embassy,_Saigon\" class=\"mw-redirect\" title=\"United States Embassy, Saigon\">United States Embassy, Saigon</a> to encourage <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a> generals to <a href=\"https://wikipedia.org/wiki/1963_South_Vietnamese_coup\" class=\"mw-redirect\" title=\"1963 South Vietnamese coup\">launch a coup</a> against President <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"Ngô Đình Diệm\">Ngô Đình Diệm</a> if he did not remove his brother <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu\" title=\"Ngô Đình Nhu\">Ngô Đình Nhu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: As a result of the <a href=\"https://wikipedia.org/wiki/X%C3%A1_L%E1%BB%A3i_Pagoda_raids\" title=\"Xá Lợi Pagoda raids\">Xá Lợi Pagoda raids</a>, the <a href=\"https://wikipedia.org/wiki/US_State_Department\" class=\"mw-redirect\" title=\"US State Department\">US State Department</a> <a href=\"https://wikipedia.org/wiki/Cable_243\" title=\"Cable 243\">cables</a> the <a href=\"https://wikipedia.org/wiki/United_States_Embassy,_Saigon\" class=\"mw-redirect\" title=\"United States Embassy, Saigon\">United States Embassy, Saigon</a> to encourage <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">Army of the Republic of Vietnam</a> generals to <a href=\"https://wikipedia.org/wiki/1963_South_Vietnamese_coup\" class=\"mw-redirect\" title=\"1963 South Vietnamese coup\">launch a coup</a> against President <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"Ngô Đình Diệm\">Ngô Đình Diệm</a> if he did not remove his brother <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu\" title=\"Ngô Đình Nhu\">Ngô Đình Nhu</a>.", "links": [{"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}, {"title": "Xá Lợi Pagoda raids", "link": "https://wikipedia.org/wiki/X%C3%A1_L%E1%BB%A3i_Pagoda_raids"}, {"title": "US State Department", "link": "https://wikipedia.org/wiki/US_State_Department"}, {"title": "Cable 243", "link": "https://wikipedia.org/wiki/Cable_243"}, {"title": "United States Embassy, Saigon", "link": "https://wikipedia.org/wiki/United_States_Embassy,_Saigon"}, {"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "1963 South Vietnamese coup", "link": "https://wikipedia.org/wiki/1963_South_Vietnamese_coup"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu"}]}, {"year": "1967", "text": "Led by <PERSON><PERSON><PERSON>, the Youth International Party temporarily disrupts trading at the New York Stock Exchange by throwing dollar bills from the viewing gallery, causing trading to cease as brokers scramble to grab them.", "html": "1967 - Led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Youth_International_Party\" title=\"Youth International Party\">Youth International Party</a> temporarily disrupts trading at the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> by throwing dollar bills from the viewing gallery, causing trading to cease as brokers scramble to grab them.", "no_year_html": "Led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Youth_International_Party\" title=\"Youth International Party\">Youth International Party</a> temporarily disrupts trading at the <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> by throwing dollar bills from the viewing gallery, causing trading to cease as brokers scramble to grab them.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}, {"title": "Youth International Party", "link": "https://wikipedia.org/wiki/Youth_International_Party"}, {"title": "New York Stock Exchange", "link": "https://wikipedia.org/wiki/New_York_Stock_Exchange"}]}, {"year": "1970", "text": "Vietnam War protesters bomb Sterling Hall at the University of Wisconsin-Madison, leading to an international manhunt for the perpetrators.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> protesters <a href=\"https://wikipedia.org/wiki/Sterling_Hall_bombing\" title=\"Sterling Hall bombing\">bomb</a> Sterling Hall at the <a href=\"https://wikipedia.org/wiki/University_of_Wisconsin%E2%80%93Madison\" title=\"University of Wisconsin-Madison\">University of Wisconsin-Madison</a>, leading to an international manhunt for the perpetrators.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a> protesters <a href=\"https://wikipedia.org/wiki/Sterling_Hall_bombing\" title=\"Sterling Hall bombing\">bomb</a> Sterling Hall at the <a href=\"https://wikipedia.org/wiki/University_of_Wisconsin%E2%80%93Madison\" title=\"University of Wisconsin-Madison\">University of Wisconsin-Madison</a>, leading to an international manhunt for the perpetrators.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Sterling Hall bombing", "link": "https://wikipedia.org/wiki/Sterling_Hall_bombing"}, {"title": "University of Wisconsin-Madison", "link": "https://wikipedia.org/wiki/University_of_Wisconsin%E2%80%93Madison"}]}, {"year": "1981", "text": "<PERSON> is sentenced to 20 years to life in prison for murdering <PERSON>.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 20 years to life in prison for <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\">murdering <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 20 years to life in prison for <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\">murdering <PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Colombian drug barons declare \"total war\" on the Colombian government.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombian</a> <a href=\"https://wikipedia.org/wiki/Drug_baron\" class=\"mw-redirect\" title=\"Drug baron\">drug barons</a> declare \"total war\" on the <a href=\"https://wikipedia.org/wiki/Government_of_Colombia\" title=\"Government of Colombia\">Colombian government</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombian</a> <a href=\"https://wikipedia.org/wiki/Drug_baron\" class=\"mw-redirect\" title=\"Drug baron\">drug barons</a> declare \"total war\" on the <a href=\"https://wikipedia.org/wiki/Government_of_Colombia\" title=\"Government of Colombia\">Colombian government</a>.", "links": [{"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Drug baron", "link": "https://wikipedia.org/wiki/Drug_baron"}, {"title": "Government of Colombia", "link": "https://wikipedia.org/wiki/Government_of_Colombia"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON> is chosen as the first non-communist prime minister in Central and Eastern Europe.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is chosen as the first non-<a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">communist</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> in Central and Eastern Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is chosen as the first non-<a href=\"https://wikipedia.org/wiki/Communist\" class=\"mw-redirect\" title=\"Communist\">communist</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> in Central and Eastern Europe.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zowiecki"}, {"title": "Communist", "link": "https://wikipedia.org/wiki/Communist"}, {"title": "Prime minister", "link": "https://wikipedia.org/wiki/Prime_minister"}]}, {"year": "1991", "text": "<PERSON> resigns as head of the Communist Party of the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as head of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union\" title=\"Communist Party of the Soviet Union\">Communist Party of the Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as head of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union\" title=\"Communist Party of the Soviet Union\">Communist Party of the Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union"}]}, {"year": "1991", "text": "Ukraine declares itself independent from the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_Ukraine\" title=\"Declaration of Independence of Ukraine\">declares itself independent</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_Ukraine\" title=\"Declaration of Independence of Ukraine\">declares itself independent</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Declaration of Independence of Ukraine", "link": "https://wikipedia.org/wiki/Declaration_of_Independence_of_Ukraine"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1992", "text": "Hurricane <PERSON> makes landfall in Homestead, Florida as a Category 5 hurricane, causing up to $25 billion (1992 USD) in damages.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Hurricane_Andrew\" title=\"Hurricane Andrew\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Homestead,_Florida\" title=\"Homestead, Florida\">Homestead, Florida</a> as a <a href=\"https://wikipedia.org/wiki/List_of_Category_5_Atlantic_hurricanes\" title=\"List of Category 5 Atlantic hurricanes\">Category 5 hurricane</a>, causing up to $25 billion (1992 <a href=\"https://wikipedia.org/wiki/USD\" class=\"mw-redirect\" title=\"USD\">USD</a>) in damages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Andrew\" title=\"Hurricane Andrew\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Homestead,_Florida\" title=\"Homestead, Florida\">Homestead, Florida</a> as a <a href=\"https://wikipedia.org/wiki/List_of_Category_5_Atlantic_hurricanes\" title=\"List of Category 5 Atlantic hurricanes\">Category 5 hurricane</a>, causing up to $25 billion (1992 <a href=\"https://wikipedia.org/wiki/USD\" class=\"mw-redirect\" title=\"USD\">USD</a>) in damages.", "links": [{"title": "Hurricane Andrew", "link": "https://wikipedia.org/wiki/Hurricane_Andrew"}, {"title": "Homestead, Florida", "link": "https://wikipedia.org/wiki/Homestead,_Florida"}, {"title": "List of Category 5 Atlantic hurricanes", "link": "https://wikipedia.org/wiki/List_of_Category_5_Atlantic_hurricanes"}, {"title": "USD", "link": "https://wikipedia.org/wiki/USD"}]}, {"year": "1995", "text": "Microsoft Windows 95 was released to the public in North America.", "html": "1995 - Microsoft <a href=\"https://wikipedia.org/wiki/Windows_95\" title=\"Windows 95\">Windows 95</a> was released to the public in North America.", "no_year_html": "Microsoft <a href=\"https://wikipedia.org/wiki/Windows_95\" title=\"Windows 95\">Windows 95</a> was released to the public in North America.", "links": [{"title": "Windows 95", "link": "https://wikipedia.org/wiki/Windows_95"}]}, {"year": "1998", "text": "First radio-frequency identification (RFID) human implantation tested in the United Kingdom.", "html": "1998 - First <a href=\"https://wikipedia.org/wiki/Radio-frequency_identification\" title=\"Radio-frequency identification\">radio-frequency identification</a> (RFID) human implantation tested in the United Kingdom.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/Radio-frequency_identification\" title=\"Radio-frequency identification\">radio-frequency identification</a> (RFID) human implantation tested in the United Kingdom.", "links": [{"title": "Radio-frequency identification", "link": "https://wikipedia.org/wiki/Radio-frequency_identification"}]}, {"year": "2001", "text": "Air Transat Flight 236 loses all engine power over the Atlantic Ocean, forcing the pilots to conduct an emergency landing in the Azores.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Air_Transat_Flight_236\" title=\"Air Transat Flight 236\">Air Transat Flight 236</a> loses all engine power over the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a>, forcing the pilots to conduct an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Transat_Flight_236\" title=\"Air Transat Flight 236\">Air Transat Flight 236</a> loses all engine power over the <a href=\"https://wikipedia.org/wiki/Atlantic_Ocean\" title=\"Atlantic Ocean\">Atlantic Ocean</a>, forcing the pilots to conduct an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> in the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "links": [{"title": "Air Transat Flight 236", "link": "https://wikipedia.org/wiki/Air_Transat_Flight_236"}, {"title": "Atlantic Ocean", "link": "https://wikipedia.org/wiki/Atlantic_Ocean"}, {"title": "Emergency landing", "link": "https://wikipedia.org/wiki/Emergency_landing"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}]}, {"year": "2004", "text": "Ninety passengers die after two airliners explode after flying out of Domodedovo International Airport, near Moscow. The explosions are caused by suicide bombers from Chechnya.", "html": "2004 - Ninety passengers die after <a href=\"https://wikipedia.org/wiki/Russian_aircraft_bombings_of_August_2004\" class=\"mw-redirect\" title=\"Russian aircraft bombings of August 2004\">two airliners explode</a> after flying out of <a href=\"https://wikipedia.org/wiki/Domodedovo_International_Airport\" class=\"mw-redirect\" title=\"Domodedovo International Airport\">Domodedovo International Airport</a>, near Moscow. The explosions are caused by suicide bombers from <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>.", "no_year_html": "Ninety passengers die after <a href=\"https://wikipedia.org/wiki/Russian_aircraft_bombings_of_August_2004\" class=\"mw-redirect\" title=\"Russian aircraft bombings of August 2004\">two airliners explode</a> after flying out of <a href=\"https://wikipedia.org/wiki/Domodedovo_International_Airport\" class=\"mw-redirect\" title=\"Domodedovo International Airport\">Domodedovo International Airport</a>, near Moscow. The explosions are caused by suicide bombers from <a href=\"https://wikipedia.org/wiki/Chechnya\" title=\"Chechnya\">Chechnya</a>.", "links": [{"title": "Russian aircraft bombings of August 2004", "link": "https://wikipedia.org/wiki/Russian_aircraft_bombings_of_August_2004"}, {"title": "Domodedovo International Airport", "link": "https://wikipedia.org/wiki/Domodedovo_International_Airport"}, {"title": "Chechnya", "link": "https://wikipedia.org/wiki/Chechnya"}]}, {"year": "2006", "text": "The International Astronomical Union (IAU) redefines the term \"planet\" such that Pluto is now considered a dwarf planet.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/International_Astronomical_Union\" title=\"International Astronomical Union\">International Astronomical Union</a> (IAU) <a href=\"https://wikipedia.org/wiki/IAU_definition_of_planet\" title=\"IAU definition of planet\">redefines the term \"planet\"</a> such that <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> is now considered a <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Astronomical_Union\" title=\"International Astronomical Union\">International Astronomical Union</a> (IAU) <a href=\"https://wikipedia.org/wiki/IAU_definition_of_planet\" title=\"IAU definition of planet\">redefines the term \"planet\"</a> such that <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> is now considered a <a href=\"https://wikipedia.org/wiki/Dwarf_planet\" title=\"Dwarf planet\">dwarf planet</a>.", "links": [{"title": "International Astronomical Union", "link": "https://wikipedia.org/wiki/International_Astronomical_Union"}, {"title": "IAU definition of planet", "link": "https://wikipedia.org/wiki/IAU_definition_of_planet"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "Dwarf planet", "link": "https://wikipedia.org/wiki/Dwarf_planet"}]}, {"year": "2008", "text": "Sixty-five passengers are killed when Iran Aseman Airlines Flight 6895 crashes during an emergency landing at Manas International Airport in Bishkek, Kyrgyzstan.", "html": "2008 - Sixty-five passengers are killed when <a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_6895\" title=\"Iran Aseman Airlines Flight 6895\">Iran Aseman Airlines Flight 6895</a> crashes during an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Manas_International_Airport\" title=\"Manas International Airport\">Manas International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bishkek\" title=\"Bishkek\">Bishkek</a>, <a href=\"https://wikipedia.org/wiki/Kyrgyzstan\" title=\"Kyrgyzstan\">Kyrgyzstan</a>.", "no_year_html": "Sixty-five passengers are killed when <a href=\"https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_6895\" title=\"Iran Aseman Airlines Flight 6895\">Iran Aseman Airlines Flight 6895</a> crashes during an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Manas_International_Airport\" title=\"Manas International Airport\">Manas International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bishkek\" title=\"Bishkek\">Bishkek</a>, <a href=\"https://wikipedia.org/wiki/Kyrgyzstan\" title=\"Kyrgyzstan\">Kyrgyzstan</a>.", "links": [{"title": "Iran Aseman Airlines Flight 6895", "link": "https://wikipedia.org/wiki/Iran_Aseman_Airlines_Flight_6895"}, {"title": "Emergency landing", "link": "https://wikipedia.org/wiki/Emergency_landing"}, {"title": "Manas International Airport", "link": "https://wikipedia.org/wiki/Manas_International_Airport"}, {"title": "Bishkek", "link": "https://wikipedia.org/wiki/Bishkek"}, {"title": "Kyrgyzstan", "link": "https://wikipedia.org/wiki/Kyrgyzstan"}]}, {"year": "2008", "text": "A Cessna 208 Caravan crashes in Cabañas, Zacapa, Guatemala, killing 11 people.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/Cessna_208_Caravan\" title=\"Cessna 208 Caravan\">Cessna 208 Caravan</a> <a href=\"https://wikipedia.org/wiki/2008_A%C3%A9reo_Ruta_Maya_crash\" title=\"2008 Aéreo Ruta Maya crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Caba%C3%B1as,_Zacapa\" title=\"Cabañas, Zacapa\">Cabañas, Zacapa</a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, killing 11 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cessna_208_Caravan\" title=\"Cessna 208 Caravan\">Cessna 208 Caravan</a> <a href=\"https://wikipedia.org/wiki/2008_A%C3%A9reo_Ruta_Maya_crash\" title=\"2008 Aéreo Ruta Maya crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Caba%C3%B1as,_Zacapa\" title=\"Cabañas, Zacapa\">Cabañas, Zacapa</a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a>, killing 11 people.", "links": [{"title": "Cessna 208 Caravan", "link": "https://wikipedia.org/wiki/Cessna_208_Caravan"}, {"title": "2008 Aéreo Ruta Maya crash", "link": "https://wikipedia.org/wiki/2008_A%C3%A9reo_Ruta_Maya_crash"}, {"title": "Cabañas, Zacapa", "link": "https://wikipedia.org/wiki/Caba%C3%B1as,_<PERSON><PERSON><PERSON>"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}]}, {"year": "2010", "text": "In San Fernando, Tamaulipas, Mexico, 72 illegal immigrants are killed by Los Zetas and eventually found dead by Mexican authorities.", "html": "2010 - In <a href=\"https://wikipedia.org/wiki/San_Fernando,_Tamaulipas\" title=\"San Fernando, Tamaulipas\">San Fernando, Tamaulipas</a>, Mexico, 72 illegal immigrants are <a href=\"https://wikipedia.org/wiki/2010_San_Fernando_massacre\" title=\"2010 San Fernando massacre\">killed</a> by <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> and eventually found dead by Mexican authorities.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/San_Fernando,_Tamaulipas\" title=\"San Fernando, Tamaulipas\">San Fernando, Tamaulipas</a>, Mexico, 72 illegal immigrants are <a href=\"https://wikipedia.org/wiki/2010_San_Fernando_massacre\" title=\"2010 San Fernando massacre\">killed</a> by <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> and eventually found dead by Mexican authorities.", "links": [{"title": "San Fernando, Tamaulipas", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>as"}, {"title": "2010 San Fernando massacre", "link": "https://wikipedia.org/wiki/2010_San_Fernando_massacre"}, {"title": "Los Zetas", "link": "https://wikipedia.org/wiki/Los_Zetas"}]}, {"year": "2010", "text": "Henan Airlines Flight 8387 crashes at Yichun Lindu Airport in Yichun, Heilongjiang, China, killing 44 out of the 96 people on board.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Henan_Airlines_Flight_8387\" title=\"Henan Airlines Flight 8387\">Henan Airlines Flight 8387</a> crashes at <a href=\"https://wikipedia.org/wiki/Yichun_Lindu_Airport\" title=\"Yichun Lindu Airport\">Yichun Lindu Airport</a> in <a href=\"https://wikipedia.org/wiki/Yichun,_Heilongjiang\" title=\"Yichun, Heilongjiang\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Heilongjiang\" title=\"Heilongjiang\">Heilongjiang</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, killing 44 out of the 96 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henan_Airlines_Flight_8387\" title=\"Henan Airlines Flight 8387\">Henan Airlines Flight 8387</a> crashes at <a href=\"https://wikipedia.org/wiki/Yichun_Lindu_Airport\" title=\"Yichun Lindu Airport\">Yichun Lindu Airport</a> in <a href=\"https://wikipedia.org/wiki/Yichun,_Heilongjiang\" title=\"Yichun, Heilongjiang\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Heilongjiang\" title=\"Heilongjiang\">Heilongjiang</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, killing 44 out of the 96 people on board.", "links": [{"title": "Henan Airlines Flight 8387", "link": "https://wikipedia.org/wiki/Henan_Airlines_Flight_8387"}, {"title": "Yichun Lindu Airport", "link": "https://wikipedia.org/wiki/Yichun_Lindu_Airport"}, {"title": "Yichun, Heilongjiang", "link": "https://wikipedia.org/wiki/Yi<PERSON>n,_Heilongjiang"}, {"title": "Heilongjiang", "link": "https://wikipedia.org/wiki/Heilongjiang"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "2010", "text": "Agni Air Flight 101 crashes near Shikharpur, Makwanpur, Nepal, killing all 14 people on board.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Agni_Air_Flight_101\" title=\"Agni Air Flight 101\">Agni Air Flight 101</a> crashes near <a href=\"https://wikipedia.org/wiki/Shikharpur,_Makwanpur\" title=\"Shikharpur, Makwanpur\">Shikharpur, Makwanpur</a>, Nepal, killing all 14 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agni_Air_Flight_101\" title=\"Agni Air Flight 101\">Agni Air Flight 101</a> crashes near <a href=\"https://wikipedia.org/wiki/Shikharpur,_Makwanpur\" title=\"Shikharpur, Makwanpur\">Shikharpur, Makwanpur</a>, Nepal, killing all 14 people on board.", "links": [{"title": "Agni Air Flight 101", "link": "https://wikipedia.org/wiki/Agni_Air_Flight_101"}, {"title": "Shikharpur, Makwanpur", "link": "https://wikipedia.org/wiki/Shikharpur,_Makwanpur"}]}, {"year": "2012", "text": "<PERSON>, perpetrator of the 2011 Norway attacks, is sentenced to 21 years of preventive detention.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>reiv<PERSON>\" title=\"<PERSON>\"><PERSON></a>, perpetrator of the <a href=\"https://wikipedia.org/wiki/2011_Norway_attacks\" title=\"2011 Norway attacks\">2011 Norway attacks</a>, is <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_Breivik#Verdict_and_sentencing\" title=\"Trial of <PERSON> Breiv<PERSON>\">sentenced</a> to 21 years of preventive detention.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>reiv<PERSON>\" title=\"<PERSON>\"><PERSON></a>, perpetrator of the <a href=\"https://wikipedia.org/wiki/2011_Norway_attacks\" title=\"2011 Norway attacks\">2011 Norway attacks</a>, is <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_Breivik#Verdict_and_sentencing\" title=\"Trial of <PERSON> Breiv<PERSON>\">sentenced</a> to 21 years of preventive detention.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iv<PERSON>"}, {"title": "2011 Norway attacks", "link": "https://wikipedia.org/wiki/2011_Norway_attacks"}, {"title": "Trial of <PERSON>", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>_Breivik#Verdict_and_sentencing"}]}, {"year": "2014", "text": "A magnitude 6.0 earthquake strikes the San Francisco Bay Area; it is the largest in that area since 1989.", "html": "2014 - A magnitude 6.0 <a href=\"https://wikipedia.org/wiki/2014_South_Napa_earthquake\" title=\"2014 South Napa earthquake\">earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay_Area\" title=\"San Francisco Bay Area\">San Francisco Bay Area</a>; it is the largest in that area <a href=\"https://wikipedia.org/wiki/1989_Loma_Prieta_earthquake\" title=\"1989 Loma Prieta earthquake\">since 1989</a>.", "no_year_html": "A magnitude 6.0 <a href=\"https://wikipedia.org/wiki/2014_South_Napa_earthquake\" title=\"2014 South Napa earthquake\">earthquake</a> strikes the <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay_Area\" title=\"San Francisco Bay Area\">San Francisco Bay Area</a>; it is the largest in that area <a href=\"https://wikipedia.org/wiki/1989_Loma_Prieta_earthquake\" title=\"1989 Loma Prieta earthquake\">since 1989</a>.", "links": [{"title": "2014 South Napa earthquake", "link": "https://wikipedia.org/wiki/2014_South_Napa_earthquake"}, {"title": "San Francisco Bay Area", "link": "https://wikipedia.org/wiki/San_Francisco_Bay_Area"}, {"title": "1989 Loma Prieta earthquake", "link": "https://wikipedia.org/wiki/1989_Loma_Prieta_earthquake"}]}, {"year": "2016", "text": "An earthquake strikes Central Italy with a magnitude of 6.2, with aftershocks felt as far as Rome and Florence. Around 300 people are killed.", "html": "2016 - An <a href=\"https://wikipedia.org/wiki/August_2016_Central_Italy_earthquake\" title=\"August 2016 Central Italy earthquake\">earthquake strikes Central Italy</a> with a magnitude of 6.2, with aftershocks felt as far as <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> and <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>. Around 300 people are killed.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/August_2016_Central_Italy_earthquake\" title=\"August 2016 Central Italy earthquake\">earthquake strikes Central Italy</a> with a magnitude of 6.2, with aftershocks felt as far as <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> and <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a>. Around 300 people are killed.", "links": [{"title": "August 2016 Central Italy earthquake", "link": "https://wikipedia.org/wiki/August_2016_Central_Italy_earthquake"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}]}, {"year": "2016", "text": "Proxima Centauri b, the closest exoplanet to Earth, is discovered by the European Southern Observatory.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Proxima_Centauri_b\" title=\"Proxima Centauri b\">Proxima Centauri b</a>, the closest exoplanet to Earth, is discovered by the <a href=\"https://wikipedia.org/wiki/European_Southern_Observatory\" title=\"European Southern Observatory\">European Southern Observatory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Proxima_Centauri_b\" title=\"Proxima Centauri b\">Proxima Centauri b</a>, the closest exoplanet to Earth, is discovered by the <a href=\"https://wikipedia.org/wiki/European_Southern_Observatory\" title=\"European Southern Observatory\">European Southern Observatory</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> b", "link": "https://wikipedia.org/wiki/Proxima_Centauri_b"}, {"title": "European Southern Observatory", "link": "https://wikipedia.org/wiki/European_Southern_Observatory"}]}, {"year": "2017", "text": "The National Space Agency of Taiwan successfully launches the observation satellite Formosat-5 into space.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/National_Space_Agency\" class=\"mw-redirect\" title=\"National Space Agency\">National Space Agency</a> of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> successfully launches the observation satellite <a href=\"https://wikipedia.org/wiki/Formosat-5\" title=\"Formosat-5\">Formosat-5</a> into space.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Space_Agency\" class=\"mw-redirect\" title=\"National Space Agency\">National Space Agency</a> of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> successfully launches the observation satellite <a href=\"https://wikipedia.org/wiki/Formosat-5\" title=\"Formosat-5\">Formosat-5</a> into space.", "links": [{"title": "National Space Agency", "link": "https://wikipedia.org/wiki/National_Space_Agency"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}, {"title": "Formosat-5", "link": "https://wikipedia.org/wiki/Formosat-5"}]}, {"year": "2020", "text": "<PERSON> is elected leader of the Conservative Party of Canada.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Erin_O%E2%80%99Toole\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2020_Conservative_Party_of_Canada_leadership_election\" title=\"2020 Conservative Party of Canada leadership election\">elected</a> leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erin_O%E2%80%99Toole\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/2020_Conservative_Party_of_Canada_leadership_election\" title=\"2020 Conservative Party of Canada leadership election\">elected</a> leader of the <a href=\"https://wikipedia.org/wiki/Conservative_Party_of_Canada\" title=\"Conservative Party of Canada\">Conservative Party of Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erin_O%E2%80%99Toole"}, {"title": "2020 Conservative Party of Canada leadership election", "link": "https://wikipedia.org/wiki/2020_Conservative_Party_of_Canada_leadership_election"}, {"title": "Conservative Party of Canada", "link": "https://wikipedia.org/wiki/Conservative_Party_of_Canada"}]}, {"year": "2023", "text": "Japan officially begins discharging treated radioactive water from the Fukushima Daiichi Nuclear Power Plant into the Pacific Ocean, sparking international concerns and condemnation.", "html": "2023 - Japan officially begins <a href=\"https://wikipedia.org/wiki/Discharge_of_radioactive_water_of_the_Fukushima_Daiichi_Nuclear_Power_Plant\" title=\"Discharge of radioactive water of the Fukushima Daiichi Nuclear Power Plant\">discharging treated radioactive water</a> from the <a href=\"https://wikipedia.org/wiki/Fukushima_Daiichi_Nuclear_Power_Plant\" title=\"Fukushima Daiichi Nuclear Power Plant\">Fukushima Daiichi Nuclear Power Plant</a> into the Pacific Ocean, sparking international concerns and condemnation.", "no_year_html": "Japan officially begins <a href=\"https://wikipedia.org/wiki/Discharge_of_radioactive_water_of_the_Fukushima_Daiichi_Nuclear_Power_Plant\" title=\"Discharge of radioactive water of the Fukushima Daiichi Nuclear Power Plant\">discharging treated radioactive water</a> from the <a href=\"https://wikipedia.org/wiki/Fukushima_Daiichi_Nuclear_Power_Plant\" title=\"Fukushima Daiichi Nuclear Power Plant\">Fukushima Daiichi Nuclear Power Plant</a> into the Pacific Ocean, sparking international concerns and condemnation.", "links": [{"title": "Discharge of radioactive water of the Fukushima Daiichi Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Discharge_of_radioactive_water_of_the_Fukushima_Daiichi_Nuclear_Power_Plant"}, {"title": "Fukushima Daiichi Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Fukushima_Daiichi_Nuclear_Power_Plant"}]}], "Births": [{"year": "1016", "text": "<PERSON><PERSON>, Japanese empress consort (d. 1039)[citation needed]", "html": "1016 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Genshi\" title=\"Fujiwara no Genshi\"><PERSON><PERSON> no <PERSON></a>, Japanese empress consort (d. 1039)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Genshi\" title=\"Fujiwara no Genshi\"><PERSON><PERSON> no <PERSON></a>, Japanese empress consort (d. 1039)", "links": [{"title": "<PERSON><PERSON> no <PERSON>shi", "link": "https://wikipedia.org/wiki/Fujiwara_no_<PERSON>shi"}]}, {"year": "1113", "text": "<PERSON>, Count of Anjou (d. 1151)", "html": "1113 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a> (d. 1151)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a> (d. 1151)", "links": [{"title": "<PERSON>, Count of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_Plantagenet,_Count_of_Anjou"}]}, {"year": "1198", "text": "<PERSON> of Scotland (d. 1249)", "html": "1198 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1249)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1358", "text": "<PERSON> of Castile (d. 1390)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> of Castile</a> (d. 1390)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (d. 1390)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}]}, {"year": "1393", "text": "<PERSON>, Duke of Brittany (d. 1458)", "html": "1393 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (d. 1458)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1423", "text": "<PERSON>, English cleric (d. 1500)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric (d. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric (d. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1498", "text": "<PERSON>, Hereditary Prince of Saxony (d. 1537)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxony\" title=\"<PERSON>, Hereditary Prince of Saxony\"><PERSON>, Hereditary Prince of Saxony</a> (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxony\" title=\"<PERSON>, Hereditary Prince of Saxony\"><PERSON>, Hereditary Prince of Saxony</a> (d. 1537)", "links": [{"title": "<PERSON>, Hereditary Prince of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Hereditary_Prince_of_Saxony"}]}, {"year": "1510", "text": "<PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen (d. 1558)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen\" title=\"Elisabeth of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen\"><PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen</a> (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen\" title=\"Elisabeth of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen\"><PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen</a> (d. 1558)", "links": [{"title": "<PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON>, Italian painter and educator (d. 1614)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/Lavinia_Fontana\" title=\"<PERSON><PERSON><PERSON> F<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavinia_Fontana\" title=\"<PERSON><PERSON><PERSON> F<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (d. 1614)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavinia_Fontana"}]}, {"year": "1556", "text": "<PERSON>, Danish horticulturalist and astronomer (d. 1643)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish horticulturalist and astronomer (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish horticulturalist and astronomer (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1561", "text": "<PERSON>, 1st Earl of Suffolk (d. 1626)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Suffolk\" title=\"<PERSON>, 1st Earl of Suffolk\"><PERSON>, 1st Earl of Suffolk</a> (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Suffolk\" title=\"<PERSON>, 1st Earl of Suffolk\"><PERSON>, 1st Earl of Suffolk</a> (d. 1626)", "links": [{"title": "<PERSON>, 1st Earl of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Suffolk"}]}, {"year": "1578", "text": "<PERSON>, English poet and author (d. 1653)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and author (d. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and author (d. 1653)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1591", "text": "<PERSON>, English poet and cleric (d. 1674)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and cleric (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and cleric (d. 1674)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1631", "text": "<PERSON>, English minister (d. 1696)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister (d. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1635", "text": "<PERSON><PERSON><PERSON>, Danish lawyer and politician (d. 1699)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish lawyer and politician (d. 1699)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1684", "text": "Sir <PERSON>, 6th Baronet, British politician (d. 1746)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_6th_Baronet\" title=\"Sir <PERSON>, 6th Baronet\">Sir <PERSON>, 6th Baronet</a>, British politician (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_6th_Baronet\" title=\"Sir <PERSON>, 6th Baronet\">Sir <PERSON>, 6th Baronet</a>, British politician (d. 1746)", "links": [{"title": "Sir <PERSON>, 6th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_6th_Baronet"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (d. 1760)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Alaungpaya\" title=\"Alaungpaya\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alaungpaya\" title=\"Alaungpaya\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1760)", "links": [{"title": "Alaungpaya", "link": "https://wikipedia.org/wiki/Alaungpaya"}]}, {"year": "1758", "text": "Duchess <PERSON> of Mecklenburg-Schwerin (d. 1794)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Mecklenburg-Schwerin\" title=\"Duchess <PERSON> of Mecklenburg-Schwerin\">Duchess <PERSON> of Mecklenburg-Schwerin</a> (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Mecklenburg-Schwerin\" title=\"Duchess <PERSON> of Mecklenburg-Schwerin\">Duchess <PERSON> of Mecklenburg-Schwerin</a> (d. 1794)", "links": [{"title": "Duchess <PERSON> of Mecklenburg-Schwerin", "link": "https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Mecklenburg-Schwerin"}]}, {"year": "1759", "text": "<PERSON>, English philanthropist and politician (d. 1833)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist and politician (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philanthropist and politician (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON> of the Netherlands (d. 1840)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1840)", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands"}]}, {"year": "1787", "text": "<PERSON>, Belgian-English sailor, hunter, and explorer (d. 1834)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-English sailor, hunter, and explorer (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-English sailor, hunter, and explorer (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, Italian geologist and scholar (d. 1891)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian geologist and scholar (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian geologist and scholar (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French organist, composer, and educator (d. 1924)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"Th<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French organist, composer, and educator (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>h<PERSON><PERSON><PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, French organist, composer, and educator (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Australian politician, 10th Premier of Queensland (d. 1905)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1845", "text": "<PERSON>, American lieutenant (d. 1876)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, American lieutenant (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, American lieutenant (d. 1876)", "links": [{"title": "<PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)"}]}, {"year": "1851", "text": "<PERSON>, Australian cricketer and journalist (d. 1924)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, English culinary entrepreneur, inventor, and celebrity chef (d. 1905)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English culinary entrepreneur, inventor, and celebrity chef (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English culinary entrepreneur, inventor, and celebrity chef (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Australian lawyer and politician (d. 1916)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician (d. 1916)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1862", "text": "<PERSON><PERSON>, American geographer and geologist (d. 1956)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Zonia_Baber\" title=\"Zonia Baber\"><PERSON><PERSON></a>, American geographer and geologist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zonia_Baber\" title=\"Zonia Baber\"><PERSON><PERSON></a>, American geographer and geologist (d. 1956)", "links": [{"title": "Zonia Baber", "link": "https://wikipedia.org/wiki/Zonia_Baber"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, Croatian explorer (d. 1918)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian explorer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian explorer (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON> of Romania (d. 1927)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (d. 1927)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania"}]}, {"year": "1872", "text": "<PERSON>, English essayist, parodist, and caricaturist (d. 1956)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, parodist, and caricaturist (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, parodist, and caricaturist (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American author and playwright (d. 1933)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American baseball player (d. 1974)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Welsh co-founder of the Martin-Baker Aircraft Company (d. 1942)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Welsh co-founder of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Martin-Baker Aircraft Company</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Welsh co-founder of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Martin-Baker Aircraft Company</a> (d. 1942)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American swimmer, actor, and surfer (d. 1968)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer, actor, and surfer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer, actor, and surfer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Dominican-British novelist (d. 1979)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-British novelist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-British novelist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, German-Israeli biochemist and academic (d. 1978)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Israeli biochemist and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Israeli biochemist and academic (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American cardinal (d. 1970)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American pianist, songwriter, and publisher (d. 1954)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist, songwriter, and publisher (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist, songwriter, and publisher (d. 1954)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1898", "text": "<PERSON>, American novelist, poet, literary critic (d. 1989)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, literary critic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, literary critic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Argentine short-story writer, essayist, poet and translator (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine short-story writer, essayist, poet and translator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine short-story writer, essayist, poet and translator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Belgian biologist and academic, Nobel Prize laureate (d. 1983)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1900", "text": "<PERSON>, American actor (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, French historian and academic (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and academic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and academic (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Italian-American mob boss (d. 1976)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German businessman and politician (d. 1945)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English campaigner for Jewish refugees, and romantic novelist as <PERSON> (d. 1986)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English campaigner for Jewish refugees, and romantic novelist as <PERSON> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English campaigner for Jewish refugees, and romantic novelist as <PERSON> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON> \"<PERSON> Boy\" <PERSON>, American singer-songwriter and guitarist (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"Big Boy\" <PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> \"Big Boy\" <PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Sierra Leonean police officer and politician, 1st President of Sierra Leone (d. 1988)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sierra Leonean police officer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sierra Leonean police officer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Sierra Leone", "link": "https://wikipedia.org/wiki/President_of_Sierra_Leone"}]}, {"year": "1907", "text": "<PERSON>, Swiss architect, designed the Hallenstadion (d. 2012)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect, designed the <a href=\"https://wikipedia.org/wiki/Hallenstadion\" title=\"Hallenstadion\">Hallenstadion</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss architect, designed the <a href=\"https://wikipedia.org/wiki/Hallenstadion\" title=\"Hallenstadion\">Hallenstadion</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hallenstadion", "link": "https://wikipedia.org/wiki/Hallenstadion"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Indian activist (d. 1931)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ru\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ru\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, South African cricketer and soldier (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and soldier (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and soldier (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American physician and mountaineer (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and mountaineer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and mountaineer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer and guitarist (d. 1969)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer and guitarist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer and guitarist (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wyn<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON> (<PERSON>), American psychologist and science fiction author (d. 1987)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> (<PERSON>), American psychologist and science fiction author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> (<PERSON>), American psychologist and science fiction author (d. 1987)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player and politician, Indian Minister of External Affairs (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "Minister of External Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, member of the Polish resistance in World War II (d. 1943)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tosia Altman\"><PERSON><PERSON></a>, member of the Polish resistance in World War II (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, member of the Polish resistance in World War II (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American entomologist, mountaineer, and DDT advocate (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entomologist and mountaineer)\"><PERSON><PERSON></a>, American entomologist, mountaineer, and DDT advocate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entomologist and mountaineer)\"><PERSON><PERSON></a>, American entomologist, mountaineer, and DDT advocate (d. 2004)", "links": [{"title": "<PERSON><PERSON> (entomologist and mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)"}]}, {"year": "1919", "text": "<PERSON>, Mexican wrestler (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican wrestler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican wrestler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Danish composer and pianist (d. 2000)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> V<PERSON>go <PERSON>\"><PERSON><PERSON></a>, Danish composer and pianist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>els Viggo <PERSON>\"><PERSON><PERSON><PERSON></a>, Danish composer and pianist (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian painter and academic (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alex <PERSON>\"><PERSON></a>, Canadian painter and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alex <PERSON>\"><PERSON></a>, Canadian painter and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English ornithologist and conservationist (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, English ornithologist and conservationist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)\" title=\"<PERSON> (ornithologist)\"><PERSON></a>, English ornithologist and conservationist (d. 2009)", "links": [{"title": "<PERSON> (ornithologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ornithologist)"}]}, {"year": "1922", "text": "<PERSON>, Canadian journalist and politician, 23rd Premier of Quebec (d. 1987)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_L%C3%A9vesque"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1922", "text": "<PERSON>, American historian, author, and activist (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American psychologist and academic (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, English singer and conductor (d. 1990)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and conductor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer and conductor (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>sworth"}]}, {"year": "1924", "text": "<PERSON>, American pianist (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American painter and academic (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nancy_Spero"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Indian actress and producer (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and producer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian author and playwright (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/David_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author and playwright (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian author and playwright (d. 2022)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/David_<PERSON>_(author)"}]}, {"year": "1927", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1929", "text": "<PERSON>, American author and educator (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer-songwriter and saxophonist (d. 1979)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and saxophonist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American race car driver (d. 1993)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American captain and religious leader (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and religious leader (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and religious leader (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Australian pianist and composer (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>-<PERSON>, English cardinal (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cardinal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cardinal (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-O%27Connor"}]}, {"year": "1933", "text": "<PERSON> <PERSON>, Spanish-English banker and manager (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Spanish-English banker and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a>, Spanish-English banker and manager (d. 2014)", "links": [{"title": "Prince <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor (d. 2016)", "links": [{"title": "<PERSON> (English actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist and poet (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Byatt\" title=\"<PERSON><PERSON> <PERSON><PERSON> Byatt\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and poet (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._<PERSON><PERSON>_Byatt\" title=\"<PERSON>. <PERSON><PERSON> Byatt\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and poet (d. 2023)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>att"}]}, {"year": "1936", "text": "<PERSON>, American banker and politician, 27th Governor of Nevada (d. 2010)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Nevada\" class=\"mw-redirect\" title=\"Governor of Nevada\">Governor of Nevada</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Nevada\" class=\"mw-redirect\" title=\"Governor of Nevada\">Governor of Nevada</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Nevada", "link": "https://wikipedia.org/wiki/Governor_of_Nevada"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American physicist and academic (d. 2001)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON> Jr.</a>, American physicist and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American physicist and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Nigerian businessman and politician (d. 1998)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Moshood_Abiola\" title=\"Mo<PERSON>ood Abiola\"><PERSON><PERSON><PERSON></a>, Nigerian businessman and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>ood_Abiola\" title=\"Mo<PERSON>ood Abiola\"><PERSON><PERSON><PERSON></a>, Nigerian businessman and politician (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moshood_Abiola"}]}, {"year": "1937", "text": "<PERSON>, Austrian-American journalist and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer and bass player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American guitarist and composer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, British academic, President and co-founder of the Adam Smith Institute", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British academic, President and co-founder of the <a href=\"https://wikipedia.org/wiki/Adam_Smith_Institute\" title=\"Adam Smith Institute\">Adam Smith Institute</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British academic, President and co-founder of the <a href=\"https://wikipedia.org/wiki/Adam_Smith_Institute\" title=\"Adam Smith Institute\">Adam Smith Institute</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Adam <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Institute"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Canadian educator and politician (d. 2014)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian educator and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English rugby player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1941", "text": "<PERSON>, English academic, Professor of Zoology at the University of Bristol", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic, Professor of Zoology at the University of Bristol", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic, Professor of Zoology at the University of Bristol", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American captain and politician (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American pop-soul singer (d. 1988)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Jimmy_Soul\" title=\"Jimmy Soul\"><PERSON></a>, American pop-soul singer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jimmy_Soul\" title=\"Jimmy Soul\"><PERSON></a>, American pop-soul singer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American mathematician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American rock guitarist (d. 1989)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock guitarist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 1996)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American engineer, and astronaut (d. 1986)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, and astronaut (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, and astronaut (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian-American wrestler and trainer (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler and trainer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler and trainer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish saxophonist (d. 2019)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish saxophonist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish saxophonist (d. 2019)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON>, English rock singer-songwriter and musician (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock singer-songwriter and musician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock singer-songwriter and musician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American gay liberation activist and drag queen (d. 1992)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American gay liberation activist and drag queen (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American gay liberation activist and drag queen (d. 1992)", "links": [{"title": "Marsh<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American wrestler, promoter, and entrepreneur; co-founded WWE", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, promoter, and entrepreneur; co-founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, promoter, and entrepreneur; co-founded <a href=\"https://wikipedia.org/wiki/WWE\" title=\"WWE\">WWE</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WWE", "link": "https://wikipedia.org/wiki/WWE"}]}, {"year": "1947", "text": "<PERSON>, American actress and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Brazilian author and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_Coelho"}]}, {"year": "1947", "text": "<PERSON>, Belgian cyclist and coach", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American politician, 34th Governor of West Virginia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 34th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 34th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1947", "text": "<PERSON>, Russian admiral", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian admiral", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, South Korean commander and pilot", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, South Korean commander and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, South Korean commander and pilot", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(general)"}]}, {"year": "1948", "text": "<PERSON>, French pianist, composer, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Finnish captain and politician, 12th President of Finland", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish captain and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sauli_Niinist%C3%B6"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1948", "text": "<PERSON>, Rhodesian-Scottish author and educator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian-Scottish author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rhodesian-Scottish author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American composer and educator (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor and director", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American southern rock singer-songwriter and musician (d. 2005)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American southern rock singer-songwriter and musician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American southern rock singer-songwriter and musician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American novelist, critic, public speaker, essayist, and columnist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Scott Card\"><PERSON><PERSON></a>, American novelist, critic, public speaker, essayist, and columnist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Scott Card\"><PERSON><PERSON></a>, American novelist, critic, public speaker, essayist, and columnist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American author and academic (d. 2013)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch author, director, and painter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, director, and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author, director, and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Jamaican dub poet", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ton K<PERSON>i Johnson\"><PERSON><PERSON><PERSON></a>, Jamaican dub poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ton K<PERSON>i <PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican dub poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish golfer and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sam Torrance\"><PERSON></a>, Scottish golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Torrance"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch footballer, coach, and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American minister and politician, 44th Governor of Arkansas", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1956", "text": "<PERSON>, American boxer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and dancer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English actor, journalist, producer, and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, journalist, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, journalist, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Cal_Ripken_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cal_Ripken_Jr.\" title=\"<PERSON>ken Jr.\"><PERSON>.</a>, American baseball player and coach", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Jr."}]}, {"year": "1961", "text": "<PERSON>, English actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American television host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Dutch educator and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese director, screenwriter and video game designer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, screenwriter and video game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, screenwriter and video game designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jima"}]}, {"year": "1964", "text": "<PERSON><PERSON>, French racing driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ric_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American video game designer, programmer, producer and business executive", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, programmer, producer and business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, programmer, producer and business executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Kyrgyzstani-Russian lieutenant, pilot, and astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani-Russian lieutenant, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani-Russian lieutenant, pilot, and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Sri Lankan-Canadian cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-Canadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-Canadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brian_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1967)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Brunet\" title=\"<PERSON><PERSON><PERSON><PERSON> Brun<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Brunet\" title=\"<PERSON><PERSON><PERSON><PERSON> Brunet\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Brunet"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese-American wrestler and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>aki"}]}, {"year": "1968", "text": "<PERSON>, Brazilian guitarist, songwriter, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American golfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>m"}]}, {"year": "1970", "text": "<PERSON>, American journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Tugay_Kerimo%C4%9Flu\" title=\"Tugay Kerimoğlu\"><PERSON><PERSON><PERSON> Kerimoğlu</a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tugay_Kerimo%C4%9Flu\" title=\"Tugay Kerimoğlu\"><PERSON><PERSON><PERSON> Kerimoğlu</a>, Turkish footballer and manager", "links": [{"title": "Tugay Kerimoğlu", "link": "https://wikipedia.org/wiki/Tugay_Kerimo%C4%9Flu"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Canadian skier and radio host", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian skier and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian skier and radio host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American director and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ava_DuV<PERSON>ay\" title=\"<PERSON> DuV<PERSON>ay\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ava_Du<PERSON>ay\" title=\"Ava DuVernay\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ava_Du<PERSON>ay"}]}, {"year": "1972", "text": "<PERSON>, American politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American comedian, actor, producer and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_D%27Arcy"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Dutch swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Inge_de_<PERSON>\" title=\"Inge de Bruijn\">In<PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_de_<PERSON>\" title=\"Inge de Bruijn\">In<PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON>_<PERSON>_<PERSON>jn"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmine_<PERSON>zzo"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1975", "text": "<PERSON>, Surinamese-Dutch footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese-Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English rower and academic", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower and academic", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rower)"}]}, {"year": "1976", "text": "<PERSON>, Australian actor, writer, director, and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Loughlin\" title=\"<PERSON>\"><PERSON></a>, Australian actor, writer, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Loughlin\" title=\"<PERSON>\"><PERSON></a>, Australian actor, writer, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27L<PERSON>lin"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAjo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAjo\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAjo"}]}, {"year": "1977", "text": "<PERSON>, German footballer (d. 2009)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Danish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Per_<PERSON><PERSON>\" title=\"Per Gade\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_G<PERSON>\" title=\"Per Gade\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Per_Gade"}]}, {"year": "1977", "text": "<PERSON>, American author and vlogger", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and vlogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and vlogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Austrian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Estonian author and poet", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian author and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dutch footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>aar\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>gelaar\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Engelaar"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, model, and author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, model, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, model, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Portuguese footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4llstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4llstr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%A4llstr%C3%B6m"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Dominican-American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Nigerian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American football player, rapper, and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player, rapper, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player, rapper, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Slovenian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/An%C5%BEe_Kopitar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An%C5%BEe_Ko<PERSON>ar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An%C5%BEe_Kopitar"}]}, {"year": "1988", "text": "<PERSON>, English actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Manu_Ma%27u\" class=\"mw-redirect\" title=\"Manu Ma'u\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manu_Ma%27u\" class=\"mw-redirect\" title=\"Manu Ma'u\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manu_Ma%27u"}]}, {"year": "1988", "text": "<PERSON>, Japanese footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Argentinian actress and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Igarz%C3%A1bal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roc%C3%ADo_Igarz%C3%A1bal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian actress and singer", "links": [{"title": "Rocío <PERSON>", "link": "https://wikipedia.org/wiki/Roc%C3%ADo_Igarz%C3%A1bal"}]}, {"year": "1990", "text": "<PERSON>, Argentinian actor and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Puerto Rican baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_Hern%C3%A1ndez_(baseball)"}]}, {"year": "1991", "text": "<PERSON>, Chinese race walker", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Chinese race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Chinese race walker", "links": [{"title": "<PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Belgian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "Lady <PERSON>, member of the British royal family", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, member of the British royal family", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, member of the British royal family", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, British-Norwegian DJ and record producer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_producer)\" class=\"mw-redirect\" title=\"<PERSON> (music producer)\"><PERSON></a>, British-Norwegian DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(music_producer)\" class=\"mw-redirect\" title=\"<PERSON> (music producer)\"><PERSON></a>, British-Norwegian DJ and record producer", "links": [{"title": "<PERSON> (music producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(music_producer)"}]}, {"year": "1998", "text": "<PERSON>, American model and social media personality", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and social media personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Mexican rhythmic gymnast", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "691", "text": "<PERSON>, official of the Tang Dynasty", "html": "691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the Tang Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "842", "text": "<PERSON>, Japanese emperor (b. 786)", "html": "842 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Saga\"><PERSON></a>, Japanese emperor (b. 786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Saga\" title=\"Emperor Saga\"><PERSON></a>, Japanese emperor (b. 786)", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Saga"}]}, {"year": "895", "text": "<PERSON><PERSON><PERSON>, king of Northumbria", "html": "895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Northumbria\" title=\"Northumbria\">Northumbria</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>"}, {"title": "Northumbria", "link": "https://wikipedia.org/wiki/Northumbria"}]}, {"year": "927", "text": "<PERSON><PERSON>, chancellor of Later Tang", "html": "927 - <a href=\"https://wikipedia.org/wiki/Doulu_Ge\" title=\"Doulu Ge\"><PERSON><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Doulu_Ge\" title=\"Doulu Ge\"><PERSON><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "links": [{"title": "Doulu Ge", "link": "https://wikipedia.org/wiki/Doulu_Ge"}, {"title": "Later Tang", "link": "https://wikipedia.org/wiki/Later_Tang"}]}, {"year": "927", "text": "<PERSON>, chancellor of Later Tang", "html": "927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of Later Tang", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chancellor of Later Tang", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "942", "text": "<PERSON>, empress dowager of Later Jin", "html": "942 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Later_Jin)\" title=\"Empress <PERSON><PERSON> (Later Jin)\"><PERSON></a>, empress dowager of Later Jin", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Later_Jin)\" title=\"Empress <PERSON><PERSON> (Later Jin)\"><PERSON></a>, empress dowager of Later Jin", "links": [{"title": "Empress <PERSON><PERSON> (Later Jin)", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Later_<PERSON>)"}]}, {"year": "948", "text": "<PERSON>, Chinese general and chancellor", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Shu)\" title=\"<PERSON> (Later Shu)\"><PERSON></a>, Chinese general and <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Shu)\" title=\"<PERSON> (Later Shu)\"><PERSON></a>, Chinese general and <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor</a>", "links": [{"title": "<PERSON> (Later Shu)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Later_Shu)"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}]}, {"year": "1042", "text": "<PERSON>, Byzantine emperor (b. 1015)", "html": "1042 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1103", "text": "<PERSON>, Norwegian king (b. 1073)", "html": "1103 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian king (b. 1073)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian king (b. 1073)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1217", "text": "<PERSON><PERSON><PERSON> the <PERSON>, French pirate (b. 1170)", "html": "1217 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Monk\" title=\"<PERSON><PERSON><PERSON> the Monk\"><PERSON><PERSON><PERSON> the Monk</a>, French pirate (b. 1170)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Monk\" title=\"<PERSON><PERSON><PERSON> the Monk\"><PERSON><PERSON><PERSON> the Monk</a>, French pirate (b. 1170)", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_<PERSON>"}]}, {"year": "1313", "text": "<PERSON>, Holy Roman Emperor (b. 1275)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1275)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1275)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1372", "text": "<PERSON>, Duke of Pomerania (b. 1348)", "html": "1372 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (b. 1348)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (b. 1348)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1497", "text": "<PERSON> of Pomerania, Duchess of Pomerania (b. 1435)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pomerania,_Duchess_of_Pomerania\" title=\"<PERSON> of Pomerania, Duchess of Pomerania\"><PERSON> of Pomerania, Duchess of Pomerania</a> (b. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pomerania,_Duchess_of_Pomerania\" title=\"<PERSON> of Pomerania, Duchess of Pomerania\"><PERSON> of Pomerania, Duchess of Pomerania</a> (b. 1435)", "links": [{"title": "<PERSON> of Pomerania, Duchess of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_of_Pomerania,_Duchess_of_Pomerania"}]}, {"year": "1507", "text": "<PERSON><PERSON> of York, English princess (b. 1469)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>y_of_York\" title=\"<PERSON><PERSON> of York\"><PERSON><PERSON> of York</a>, English princess (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_York\" title=\"<PERSON><PERSON> of York\"><PERSON><PERSON> of York</a>, English princess (b. 1469)", "links": [{"title": "<PERSON><PERSON> of York", "link": "https://wikipedia.org/wiki/Cecily_of_York"}]}, {"year": "1540", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian painter and etcher (b. 1503)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Parmigianino\" title=\"Parmigiani<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and etcher (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parmigianino\" title=\"Parmigiani<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Italian painter and etcher (b. 1503)", "links": [{"title": "Parmigianino", "link": "https://wikipedia.org/wiki/Parmigianino"}]}, {"year": "1542", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (b. 1483)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ini\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1483)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gasparo_Contarini"}]}, {"year": "1572", "text": "<PERSON><PERSON>, French admiral (b. 1519)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_de_Coligny\" title=\"Gaspard II de Coligny\"><PERSON><PERSON> II de Coligny</a>, French admiral (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_de_Coligny\" title=\"Gaspard II de Coligny\"><PERSON><PERSON> II de Coligny</a>, French admiral (b. 1519)", "links": [{"title": "Gaspard II de Coligny", "link": "https://wikipedia.org/wiki/<PERSON>pard_II_de_Coligny"}]}, {"year": "1572", "text": "<PERSON>, French soldier and diplomat (b. 1535)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9li<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and diplomat (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9li<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and diplomat (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_de_T%C3%A9ligny"}]}, {"year": "1595", "text": "<PERSON>, English mathematician and astronomer (b. 1546)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (b. 1546)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON> of Lima, Peruvian saint (b. 1586)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Rose_of_Lima\" title=\"Rose of Lima\"><PERSON> of Lima</a>, Peruvian saint (b. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rose_of_Lima\" title=\"Rose of Lima\"><PERSON> of Lima</a>, Peruvian saint (b. 1586)", "links": [{"title": "Rose of Lima", "link": "https://wikipedia.org/wiki/Rose_of_Lima"}]}, {"year": "1647", "text": "<PERSON>, English sculptor and architect (b. 1586)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and architect (b. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and architect (b. 1586)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1679", "text": "<PERSON>, French cardinal and author (b. 1614)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and author (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and author (b. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Irish colonel (b. 1618)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish colonel (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish colonel (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1680", "text": "<PERSON>, Dutch painter and etcher (b. 1616)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and etcher (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and etcher (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON>, English theologian and academic (b. 1616)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and academic (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, English theologian and academic (b. 1616)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(theologian)"}]}, {"year": "1759", "text": "<PERSON><PERSON>, German poet and soldier (b. 1715)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and soldier (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and soldier (b. 1715)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, English poet and prodigy (b. 1752)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and prodigy (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and prodigy (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON> of Aetolia, Greek monk and saint (b. 1714)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/Cosmas_of_Aetolia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Aetolia\"><PERSON><PERSON><PERSON> of Aetolia</a>, Greek monk and saint (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Co<PERSON><PERSON>_of_Aetolia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Aetolia\"><PERSON><PERSON><PERSON> of Aetolia</a>, Greek monk and saint (b. 1714)", "links": [{"title": "<PERSON><PERSON><PERSON> of Aetolia", "link": "https://wikipedia.org/wiki/Cosmas_of_Aetolia"}]}, {"year": "1798", "text": "<PERSON>, English priest and author (b. 1709)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest and author (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest and author (b. 1709)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}]}, {"year": "1804", "text": "<PERSON>, American wife of <PERSON> and American Revolutionary War spy (b. 1760)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spy</a> (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a> <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spy</a> (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1818", "text": "<PERSON>, American lawyer and politician (b. 1777)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Massachusetts_politician)\" title=\"<PERSON> (Massachusetts politician)\"><PERSON></a>, American lawyer and politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(Massachusetts_politician)\" title=\"<PERSON> (Massachusetts politician)\"><PERSON></a>, American lawyer and politician (b. 1777)", "links": [{"title": "<PERSON> (Massachusetts politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Massachusetts_politician)"}]}, {"year": "1821", "text": "<PERSON>, English writer and physician (b. 1795)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and physician (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and physician (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, French physicist and engineer (b. 1796)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, British Royal Navy commander (b. 1780/81)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Navy commander (b. 1780/81)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Navy commander (b. 1780/81)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, Hungarian poet, critic, and politician (b. 1790)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet, critic, and politician (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet, critic, and politician (b. 1790)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey"}]}, {"year": "1841", "text": "<PERSON>, English civil servant and composer (b. 1788)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and composer (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil servant and composer (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French-American soldier (b. 1778)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(privateer)\" title=\"<PERSON> (privateer)\"><PERSON></a>, French-American soldier (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(privateer)\" title=\"<PERSON> (privateer)\"><PERSON></a>, French-American soldier (b. 1778)", "links": [{"title": "<PERSON> (privateer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(privateer)"}]}, {"year": "1888", "text": "<PERSON>, German physicist and mathematician (b. 1822)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English mountaineer and author (b. 1855)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author and educator (b. 1856)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin\" title=\"<PERSON> Wiggin\"><PERSON></a>, American author and educator (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin\" title=\"<PERSON> Wiggin\"><PERSON></a>, American author and educator (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin"}]}, {"year": "1930", "text": "<PERSON>, English businessman and showman (b. 1860)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and showman (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and showman (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American activist (b. 1861)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American painter and educator (b. 1874)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Polish-German technician and inventor, invented the <PERSON><PERSON><PERSON><PERSON> disk (b. 1860)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German technician and inventor, invented the <a href=\"https://wikipedia.org/wiki/Nipkow_disk\" title=\"Ni<PERSON><PERSON><PERSON> disk\"><PERSON><PERSON><PERSON>w disk</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German technician and inventor, invented the <a href=\"https://wikipedia.org/wiki/Nipkow_disk\" title=\"<PERSON><PERSON><PERSON><PERSON> disk\"><PERSON><PERSON><PERSON>w disk</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nipkow disk", "link": "https://wikipedia.org/wiki/Nipkow_disk"}]}, {"year": "1943", "text": "<PERSON>, Argentinian painter and educator (b. 1886)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian painter and educator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian painter and educator (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON> aviator, adventurer and politician (b. 1902)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Italian aviator, adventurer and politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Italian aviator, adventurer and politician (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French philosopher and activist (b. 1909)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and activist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and activist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and judge, 48th United States Attorney General (b. 1862)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>cReynolds\" title=\"<PERSON>eynolds\"><PERSON></a>, American lawyer and judge, 48th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>eynolds\" title=\"<PERSON>eynolds\"><PERSON></a>, American lawyer and judge, 48th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Brazilian lawyer and politician, 14th President of Brazil (b. 1882)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Get%C3%BAlio_Vargas"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Japanese director and screenwriter (b. 1898)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English Catholic priest (b. 1888)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Catholic priest (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Irish painter and educator (b. 1876)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Irish painter and educator (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Irish painter and educator (b. 1876)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(painter)"}]}, {"year": "1967", "text": "<PERSON>, American businessman, founded Kaiser Shipyards and Kaiser Aluminum (b. 1882)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Kaiser_Shipyards\" title=\"Kaiser Shipyards\">Kaiser Shipyards</a> and <a href=\"https://wikipedia.org/wiki/Kaiser_Aluminum\" title=\"Kaiser Aluminum\">Kaiser Aluminum</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Kaiser_Shipyards\" title=\"Kaiser Shipyards\">Kaiser Shipyards</a> and <a href=\"https://wikipedia.org/wiki/Kaiser_Aluminum\" title=\"Kaiser Aluminum\">Kaiser Aluminum</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kaiser Shipyards", "link": "https://wikipedia.org/wiki/Kaiser_Shipyards"}, {"title": "Kaiser Aluminum", "link": "https://wikipedia.org/wiki/Kaiser_Aluminum"}]}, {"year": "1974", "text": "<PERSON>, Russian-American pilot and businessman, co-founded Republic Aviation (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-American pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Republic_Aviation\" title=\"Republic Aviation\">Republic Aviation</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian-American pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Republic_Aviation\" title=\"Republic Aviation\">Republic Aviation</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Republic Aviation", "link": "https://wikipedia.org/wiki/Republic_Aviation"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player (b. 1916)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Buddy_O%27Connor"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter, trumpet player, and actor (b. 1910)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, trumpet player, and actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Prima\"><PERSON></a>, American singer-songwriter, trumpet player, and actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German soldier and pilot (b. 1912)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1927)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Canadian priest and author (b. 1896)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian priest and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian priest and author (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix-<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Estonian-Finnish high jumper and discus thrower (b. 1913)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Finnish high jumper and discus thrower (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Finnish high jumper and discus thrower (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American economist, educator, and activist (b. 1883)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, educator, and activist (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, educator, and activist (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American composer and educator (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English rugby player and wrestler (b. 1936)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and wrestler (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and wrestler (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Russian-American journalist and author (b. 1941)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American journalist and author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American journalist and author (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Sudanese-Egyptian poet and academic (b. 1931)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese-Egyptian poet and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese-Egyptian poet and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Italian-American inventor (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American inventor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American inventor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dutch academic and judge (b. 1918)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and judge (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and judge (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Italian racing driver (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor (b. 1910)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress (b. 1916)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Egyptian guitarist and composer (b. 1929)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian guitarist and composer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian guitarist and composer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Swiss martial artist and kick-boxer (b. 1964)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martial artist and kick-boxer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss martial artist and kick-boxer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}]}, {"year": "2001", "text": "<PERSON>, American actress (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Estonian violinist, pianist, and conductor (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Roman_Matsov\" title=\"Roman Matsov\"><PERSON></a>, Estonian violinist, pianist, and conductor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Matsov\" title=\"Roman Matsov\"><PERSON></a>, Estonian violinist, pianist, and conductor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>v"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Russian priest and mystic (b. 1909)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian priest and mystic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian priest and mystic (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Ethiopian-English explorer and author (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian-English explorer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian-English explorer and author (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>-<PERSON>, Swiss-American psychiatrist and academic (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American psychiatrist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American psychiatrist and academic (b. 1926)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Elisabeth_K%C3%BCbler-Ross"}]}, {"year": "2006", "text": "<PERSON><PERSON>, American soldier and engineer (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and engineer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier and engineer (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian tenor and educator (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/L%C3%A9op<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian tenor and educator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9op<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian tenor and educator (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9opold_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Canadian educator and politician, 39th Mayor of Quebec City (b. 1937)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quebec_City\" class=\"mw-redirect\" title=\"Mayor of Quebec City\">Mayor of Quebec City</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian educator and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quebec_City\" class=\"mw-redirect\" title=\"Mayor of Quebec City\">Mayor of Quebec City</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9e_<PERSON>ucher"}, {"title": "Mayor of Quebec City", "link": "https://wikipedia.org/wiki/Mayor_of_Quebec_City"}]}, {"year": "2007", "text": "<PERSON>, American director and producer (b. 1943)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Japanese director and screenwriter (b. 1963)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sato<PERSON>_Kon"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (b. 1962)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>yhan_Er%C3%B6z%C3%A7elik\" title=\"<PERSON><PERSON><PERSON>eli<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>han_Er%C3%B6z%C3%A7elik\" title=\"<PERSON><PERSON><PERSON>rözçelik\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (b. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>ö<PERSON>çelik", "link": "https://wikipedia.org/wiki/Seyhan_Er%C3%B6z%C3%A7elik"}]}, {"year": "2011", "text": "<PERSON>, American baseball player, coach, and sportscaster (b. 1951)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1951)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Pakistani Taliban leader (b. 1965)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Pakistani_Taliban)\" title=\"<PERSON><PERSON><PERSON> (Pakistani Taliban)\"><PERSON><PERSON><PERSON></a>, Pakistani <a href=\"https://wikipedia.org/wiki/Tehrik-i-Taliban_Pakistan\" class=\"mw-redirect\" title=\"Tehrik-i-Taliban Pakistan\">Taliban</a> leader (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Pakistani_Taliban)\" title=\"<PERSON><PERSON><PERSON> (Pakistani Taliban)\"><PERSON><PERSON><PERSON></a>, Pakistani <a href=\"https://wikipedia.org/wiki/Tehrik-i-Taliban_Pakistan\" class=\"mw-redirect\" title=\"Tehrik-i-Taliban Pakistan\">Taliban</a> leader (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON> (Pakistani Taliban)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Pakistani_Taliban)"}, {"title": "Tehrik-i-Taliban Pakistan", "link": "https://wikipedia.org/wiki/Tehrik-i-Taliban_Pakistan"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Faroese surveyor and politician, 6th Prime Minister of the Faroe Islands (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese surveyor and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese surveyor and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Brazilian footballer and manager (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mi%C3%A9lli_Venerando\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Mi%C3%A9lli_Venerando\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Mi%C3%A9lli_Venerando"}]}, {"year": "2013", "text": "<PERSON>, American soccer player and manager (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/N%C3%AD<PERSON>_de_Sordi\" title=\"<PERSON><PERSON><PERSON> de Sordi\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%<PERSON><PERSON>_de_Sordi\" title=\"<PERSON><PERSON><PERSON> de Sordi\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" class=\"mw-redirect\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1925)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American businesswoman and philanthropist (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and philanthropist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and philanthropist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actor, director, producer, and politician (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, producer, and politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian businessman (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4nio_Erm%C3%ADrio_de_Moraes\" title=\"Antônio Ermírio de Moraes\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4nio_Erm%C3%ADrio_de_Moraes\" title=\"Antônio Ermírio de Moraes\"><PERSON><PERSON><PERSON><PERSON>í<PERSON></a>, Brazilian businessman (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4nio_Erm%C3%ADrio_de_Moraes"}]}, {"year": "2015", "text": "<PERSON>, American football player and coach (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German-American computer scientist and academic (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American computer scientist and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American computer scientist and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English racing driver (b. 1978)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English racing driver (b. 1978)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2016", "text": "<PERSON>, German politician, 4th President of Germany (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "2017", "text": "<PERSON>, American actor, comedian, and radio talk show host (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and radio talk show host (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and radio talk show host (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American author, journalist, and lecturer (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and lecturer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and lecturer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English musician (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American wrestler (b. 1987)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German footballer and manager (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}