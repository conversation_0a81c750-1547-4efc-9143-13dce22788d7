{"date": "May 15", "url": "https://wikipedia.org/wiki/May_15", "data": {"Events": [{"year": "221", "text": "<PERSON>, Chinese warlord, proclaims himself emperor of Shu Han, the successor of the Han dynasty.", "html": "221 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bei\"><PERSON></a>, Chinese warlord, proclaims himself emperor of <a href=\"https://wikipedia.org/wiki/Shu_Han\" title=\"Shu Han\"><PERSON></a>, the successor of the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bei\"><PERSON></a>, Chinese warlord, proclaims himself emperor of <a href=\"https://wikipedia.org/wiki/Shu_Han\" title=\"Shu Han\"><PERSON></a>, the successor of the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}, {"title": "Shu Han", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Han dynasty", "link": "https://wikipedia.org/wiki/Han_dynasty"}]}, {"year": "392", "text": "Emperor <PERSON><PERSON><PERSON> is assassinated while advancing into Gaul against the Frankish usurper <PERSON><PERSON><PERSON><PERSON>. He is found hanging in his residence at Vienne.", "html": "392 - Emperor <a href=\"https://wikipedia.org/wiki/Valentinian_II\" title=\"Valentinian II\">Valentinian II</a> is assassinated while advancing into <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">G<PERSON></a> against the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\"><PERSON><PERSON></a> usurper <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a>. He is found hanging in his residence at <a href=\"https://wikipedia.org/wiki/Vienne,_Is%C3%A8re\" title=\"Vienne, Isère\">Vienne</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Valentinian_II\" title=\"Valentinian II\">Valentinian II</a> is assassinated while advancing into <a href=\"https://wikipedia.org/wiki/Gaul\" title=\"Gaul\">Gaul</a> against the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"<PERSON>\"><PERSON><PERSON></a> usurper <a href=\"https://wikipedia.org/wiki/Arbogast_(magister_militum)\" title=\"Arbogast (magister militum)\"><PERSON><PERSON><PERSON><PERSON></a>. He is found hanging in his residence at <a href=\"https://wikipedia.org/wiki/Vienne,_Is%C3%A8re\" title=\"Vienne, Isère\">Vienne</a>.", "links": [{"title": "Valentinian II", "link": "https://wikipedia.org/wiki/Valentinian_II"}, {"title": "Gaul", "link": "https://wikipedia.org/wiki/Gaul"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (magister militum)", "link": "https://wikipedia.org/wiki/Arbogast_(magister_militum)"}, {"title": "Vienne, Isère", "link": "https://wikipedia.org/wiki/Vienne,_Is%C3%A8re"}]}, {"year": "589", "text": "King <PERSON><PERSON><PERSON> marries <PERSON><PERSON><PERSON>, daughter of the Bavarian duke <PERSON><PERSON><PERSON><PERSON> A Catholic, she has great influence among the Lombard nobility.", "html": "589 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavarian</a> duke <a href=\"https://wikipedia.org/wiki/Garibald_I_of_Bavaria\" title=\"Garibald I of Bavaria\">Garibald I</a>. A <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholic</a>, she has great influence among the <a href=\"https://wikipedia.org/wiki/Lombards\" title=\"Lombards\">Lombard</a> nobility.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavarian</a> duke <a href=\"https://wikipedia.org/wiki/Garibald_I_of_Bavaria\" title=\"Garibald I of Bavaria\">Garibald I</a>. A <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholic</a>, she has great influence among the <a href=\"https://wikipedia.org/wiki/Lombards\" title=\"Lombards\">Lombard</a> nobility.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>thari"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Bavaria", "link": "https://wikipedia.org/wiki/Bavaria"}, {"title": "G<PERSON>bald I of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>aribald_I_of_Bavaria"}, {"title": "Catholicism", "link": "https://wikipedia.org/wiki/Catholicism"}, {"title": "Lombards", "link": "https://wikipedia.org/wiki/Lombards"}]}, {"year": "756", "text": "<PERSON><PERSON>, the founder of the Arab dynasty that ruled the greater part of Iberia for nearly three centuries, becomes emir of Cordova, Spain.", "html": "756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, the founder of the Arab dynasty that ruled the greater part of <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberia</a> for nearly three centuries, becomes <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> of <a href=\"https://wikipedia.org/wiki/Province_of_C%C3%B3rdoba_(Spain)\" title=\"Province of Córdoba (Spain)\">Cordova</a>, Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, the founder of the Arab dynasty that ruled the greater part of <a href=\"https://wikipedia.org/wiki/Iberian_Peninsula\" title=\"Iberian Peninsula\">Iberia</a> for nearly three centuries, becomes <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">emir</a> of <a href=\"https://wikipedia.org/wiki/Province_of_C%C3%B3rdoba_(Spain)\" title=\"Province of Córdoba (Spain)\">Cordova</a>, Spain.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Iberian Peninsula", "link": "https://wikipedia.org/wiki/Iberian_Peninsula"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "Province of Córdoba (Spain)", "link": "https://wikipedia.org/wiki/Province_of_C%C3%B3rdoba_(Spain)"}]}, {"year": "1194", "text": "<PERSON> the Syrian reconsecrates the Mor Bar Sauma Monastery, which he reconstructed after its destruction by a fire. The monastery stays a center of the Syriac Orthodox Church until the end of the thirteenth century.", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Syrian\" title=\"<PERSON> the Syrian\"><PERSON> the Syrian</a> reconsecrates the <a href=\"https://wikipedia.org/wiki/Mor_Bar_Sauma_Monastery\" title=\"Mor Bar <PERSON>uma Monastery\"><PERSON>r Bar <PERSON>uma Monastery</a>, which he reconstructed after its destruction by a fire. The monastery stays a center of the <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox Church</a> until the end of the thirteenth century.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Syrian\" title=\"<PERSON> the Syrian\"><PERSON> the Syrian</a> reconsecrates the <a href=\"https://wikipedia.org/wiki/Mor_Bar_Sauma_Monastery\" title=\"Mor Bar <PERSON>uma Monastery\">Mor Bar <PERSON> Monastery</a>, which he reconstructed after its destruction by a fire. The monastery stays a center of the <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox Church</a> until the end of the thirteenth century.", "links": [{"title": "<PERSON> the Syrian", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mor Bar Sauma Monastery", "link": "https://wikipedia.org/wiki/Mor_Bar_Sauma_Monastery"}, {"title": "Syriac Orthodox Church", "link": "https://wikipedia.org/wiki/Syriac_Orthodox_Church"}]}, {"year": "1252", "text": "<PERSON> <PERSON> issues the papal bull ad extirpanda, which authorizes, but also limits, the torture of heretics in the Medieval Inquisition.", "html": "1252 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_IV\" title=\"Pope Innocent IV\">Pope Innocent IV</a> issues the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Ad_extirpanda\" title=\"Ad extirpanda\">ad extirpanda</a></i>, which authorizes, but also limits, the torture of <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heretics</a> in the <a href=\"https://wikipedia.org/wiki/Medieval_Inquisition\" title=\"Medieval Inquisition\">Medieval Inquisition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_IV\" title=\"Pope Innocent IV\">Pope Innocent IV</a> issues the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Ad_extirpanda\" title=\"Ad extirpanda\">ad extirpanda</a></i>, which authorizes, but also limits, the torture of <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heretics</a> in the <a href=\"https://wikipedia.org/wiki/Medieval_Inquisition\" title=\"Medieval Inquisition\">Medieval Inquisition</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_IV"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Ad extirpanda", "link": "https://wikipedia.org/wiki/Ad_extirpanda"}, {"title": "Heresy in Christianity", "link": "https://wikipedia.org/wiki/Heresy_in_Christianity"}, {"title": "Medieval Inquisition", "link": "https://wikipedia.org/wiki/Medieval_Inquisition"}]}, {"year": "1525", "text": "Insurgent peasants led by Anabaptist pastor <PERSON> were defeated at the Battle of Frankenhausen, ending the German Peasants' War in the Holy Roman Empire.", "html": "1525 - <a href=\"https://wikipedia.org/wiki/Insurgency\" title=\"Insurgency\">Insurgent</a> peasants led by <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> pastor <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>%C3%BCntzer\" title=\"<PERSON>\"><PERSON></a> were defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Frankenhausen\" title=\"Battle of Frankenhausen\">Battle of Frankenhausen</a>, ending the <a href=\"https://wikipedia.org/wiki/German_Peasants%27_War\" title=\"German Peasants' War\">German Peasants' War</a> in the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Insurgency\" title=\"Insurgency\">Insurgent</a> peasants led by <a href=\"https://wikipedia.org/wiki/Anabaptist\" class=\"mw-redirect\" title=\"Anabaptist\">Anabaptist</a> pastor <a href=\"https://wikipedia.org/wiki/Thomas_M%C3%BCntzer\" title=\"<PERSON>\"><PERSON></a> were defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Frankenhausen\" title=\"Battle of Frankenhausen\">Battle of Frankenhausen</a>, ending the <a href=\"https://wikipedia.org/wiki/German_Peasants%27_War\" title=\"German Peasants' War\">German Peasants' War</a> in the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>.", "links": [{"title": "Insurgency", "link": "https://wikipedia.org/wiki/Insurgency"}, {"title": "Anabaptist", "link": "https://wikipedia.org/wiki/Anabaptist"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_M%C3%BCntzer"}, {"title": "Battle of Frankenhausen", "link": "https://wikipedia.org/wiki/Battle_of_Frankenhausen"}, {"title": "German Peasants' War", "link": "https://wikipedia.org/wiki/German_Peasants%27_War"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}]}, {"year": "1536", "text": "<PERSON>, Queen of England, stands trial in London on charges of treason, adultery and incest; she is condemned to death by a specially-selected jury.", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of England, stands trial in London on charges of treason, adultery and incest; she is condemned to death by a specially-selected <a href=\"https://wikipedia.org/wiki/Jury\" title=\"Jury\">jury</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of England, stands trial in London on charges of treason, adultery and incest; she is condemned to death by a specially-selected <a href=\"https://wikipedia.org/wiki/Jury\" title=\"Jury\">jury</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jury", "link": "https://wikipedia.org/wiki/Jury"}]}, {"year": "1602", "text": "Cape Cod is sighted by English navigator <PERSON>.", "html": "1602 - <a href=\"https://wikipedia.org/wiki/Cape_Cod\" title=\"Cape Cod\">Cape Cod</a> is sighted by English navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cape_Cod\" title=\"Cape Cod\">Cape Cod</a> is sighted by English navigator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Go<PERSON>ld\"><PERSON></a>.", "links": [{"title": "Cape Cod", "link": "https://wikipedia.org/wiki/Cape_Cod"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ld"}]}, {"year": "1618", "text": "<PERSON> confirms his previously rejected discovery of the third law of planetary motion (he first discovered it on March 8 but soon rejected the idea after some initial calculations were made).", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> confirms his previously rejected discovery of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_laws_of_planetary_motion\" title=\"<PERSON><PERSON>'s laws of planetary motion\">third law of planetary motion</a> (he first discovered it on <a href=\"https://wikipedia.org/wiki/March_8\" title=\"March 8\">March 8</a> but soon rejected the idea after some initial calculations were made).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> confirms his previously rejected discovery of the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_laws_of_planetary_motion\" title=\"<PERSON><PERSON>'s laws of planetary motion\">third law of planetary motion</a> (he first discovered it on <a href=\"https://wikipedia.org/wiki/March_8\" title=\"March 8\">March 8</a> but soon rejected the idea after some initial calculations were made).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s laws of planetary motion", "link": "https://wikipedia.org/wiki/Kepler%27s_laws_of_planetary_motion"}, {"title": "March 8", "link": "https://wikipedia.org/wiki/March_8"}]}, {"year": "1648", "text": "The Peace of Münster is ratified, by which Spain acknowledges Dutch sovereignty.", "html": "1648 - The <a href=\"https://wikipedia.org/wiki/Peace_of_M%C3%BCnster\" title=\"Peace of Münster\">Peace of Münster</a> is ratified, by which Spain acknowledges Dutch sovereignty.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peace_of_M%C3%BCnster\" title=\"Peace of Münster\">Peace of Münster</a> is ratified, by which Spain acknowledges Dutch sovereignty.", "links": [{"title": "Peace of Münster", "link": "https://wikipedia.org/wiki/Peace_of_M%C3%BCnster"}]}, {"year": "1791", "text": "French Revolution: <PERSON><PERSON><PERSON> proposes the Self-denying Ordinance.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a> proposes the <a href=\"https://wikipedia.org/wiki/Self-denying_Ordinance_(French_Revolution)\" title=\"Self-denying Ordinance (French Revolution)\">Self-denying Ordinance</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\">Maxim<PERSON><PERSON></a> proposes the <a href=\"https://wikipedia.org/wiki/Self-denying_Ordinance_(French_Revolution)\" title=\"Self-denying Ordinance (French Revolution)\">Self-denying Ordinance</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}, {"title": "Self-denying Ordinance (French Revolution)", "link": "https://wikipedia.org/wiki/Self-denying_Ordinance_(French_Revolution)"}]}, {"year": "1817", "text": "Opening of the first private mental health hospital in the United States, the Asylum for the Relief of Persons Deprived of the Use of Their Reason (now Friends Hospital, Philadelphia, Pennsylvania).", "html": "1817 - Opening of the first private mental health hospital in the United States, the Asylum for the Relief of Persons Deprived of the Use of Their Reason (now <a href=\"https://wikipedia.org/wiki/Friends_Hospital\" title=\"Friends Hospital\">Friends Hospital</a>, <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, Pennsylvania).", "no_year_html": "Opening of the first private mental health hospital in the United States, the Asylum for the Relief of Persons Deprived of the Use of Their Reason (now <a href=\"https://wikipedia.org/wiki/Friends_Hospital\" title=\"Friends Hospital\">Friends Hospital</a>, <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, Pennsylvania).", "links": [{"title": "Friends Hospital", "link": "https://wikipedia.org/wiki/Friends_Hospital"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1836", "text": "<PERSON> observes \"<PERSON><PERSON>'s beads\" during an annular eclipse.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> observes \"<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_beads\" title=\"<PERSON><PERSON>'s beads\"><PERSON><PERSON>'s beads</a>\" during an <a href=\"https://wikipedia.org/wiki/Annular_eclipse\" class=\"mw-redirect\" title=\"Annular eclipse\">annular eclipse</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> observes \"<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_beads\" title=\"<PERSON><PERSON>'s beads\"><PERSON><PERSON>'s beads</a>\" during an <a href=\"https://wikipedia.org/wiki/Annular_eclipse\" class=\"mw-redirect\" title=\"Annular eclipse\">annular eclipse</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>'s beads", "link": "https://wikipedia.org/wiki/Baily%27s_beads"}, {"title": "Annular eclipse", "link": "https://wikipedia.org/wiki/Annular_eclipse"}]}, {"year": "1849", "text": "The Sicilian revolution of 1848 is finally extinguished.", "html": "1849 - The <a href=\"https://wikipedia.org/wiki/Sicilian_revolution_of_1848\" title=\"Sicilian revolution of 1848\">Sicilian revolution of 1848</a> is finally extinguished.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sicilian_revolution_of_1848\" title=\"Sicilian revolution of 1848\">Sicilian revolution of 1848</a> is finally extinguished.", "links": [{"title": "Sicilian revolution of 1848", "link": "https://wikipedia.org/wiki/Sicilian_revolution_of_1848"}]}, {"year": "1850", "text": "The Arana-Southern Treaty is ratified, ending \"the existing differences\" between Great Britain and Argentina.", "html": "1850 - The <a href=\"https://wikipedia.org/wiki/Arana%E2%80%93Southern_Treaty\" title=\"Arana-Southern Treaty\">Arana-Southern Treaty</a> is ratified, ending \"the existing differences\" between <a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arana%E2%80%93Southern_Treaty\" title=\"Arana-Southern Treaty\">Arana-Southern Treaty</a> is ratified, ending \"the existing differences\" between <a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a> and <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>.", "links": [{"title": "Arana-Southern Treaty", "link": "https://wikipedia.org/wiki/Arana%E2%80%93Southern_Treaty"}, {"title": "Great Britain", "link": "https://wikipedia.org/wiki/Great_Britain"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}]}, {"year": "1851", "text": "The first Australian gold rush is proclaimed, although the discovery had been made three months earlier.", "html": "1851 - The first <a href=\"https://wikipedia.org/wiki/Australian_gold_rushes\" title=\"Australian gold rushes\">Australian gold rush</a> is proclaimed, although the discovery had been made three months earlier.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Australian_gold_rushes\" title=\"Australian gold rushes\">Australian gold rush</a> is proclaimed, although the discovery had been made three months earlier.", "links": [{"title": "Australian gold rushes", "link": "https://wikipedia.org/wiki/Australian_gold_rushes"}]}, {"year": "1864", "text": "American Civil War: Battle of New Market, Virginia: Students from the Virginia Military Institute fight alongside the Confederate army to force Union General <PERSON> out of the Shenandoah Valley.", "html": "1864 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_New_Market\" title=\"Battle of New Market\">Battle of New Market</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>: Students from the <a href=\"https://wikipedia.org/wiki/Virginia_Military_Institute\" title=\"Virginia Military Institute\">Virginia Military Institute</a> fight alongside the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army to force <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> out of the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_New_Market\" title=\"Battle of New Market\">Battle of New Market</a>, <a href=\"https://wikipedia.org/wiki/Virginia\" title=\"Virginia\">Virginia</a>: Students from the <a href=\"https://wikipedia.org/wiki/Virginia_Military_Institute\" title=\"Virginia Military Institute\">Virginia Military Institute</a> fight alongside the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army to force <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> out of the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a>.", "links": [{"title": "Battle of New Market", "link": "https://wikipedia.org/wiki/Battle_of_New_Market"}, {"title": "Virginia", "link": "https://wikipedia.org/wiki/Virginia"}, {"title": "Virginia Military Institute", "link": "https://wikipedia.org/wiki/Virginia_Military_Institute"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shenandoah Valley", "link": "https://wikipedia.org/wiki/Shenandoah_Valley"}]}, {"year": "1891", "text": "Pope <PERSON> defends workers' rights and property rights in the encyclical Rerum novarum, the beginning of modern Catholic social teaching.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> defends <a href=\"https://wikipedia.org/wiki/Labor_rights\" title=\"Labor rights\">workers' rights</a> and property rights in the encyclical <i><a href=\"https://wikipedia.org/wiki/Rerum_novarum\" title=\"Rerum novarum\">Rerum novarum</a></i>, the beginning of modern <a href=\"https://wikipedia.org/wiki/Catholic_social_teaching\" title=\"Catholic social teaching\">Catholic social teaching</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> defends <a href=\"https://wikipedia.org/wiki/Labor_rights\" title=\"Labor rights\">workers' rights</a> and property rights in the encyclical <i><a href=\"https://wikipedia.org/wiki/Rerum_novarum\" title=\"Rerum novarum\">Rerum novarum</a></i>, the beginning of modern <a href=\"https://wikipedia.org/wiki/Catholic_social_teaching\" title=\"Catholic social teaching\">Catholic social teaching</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Labor rights", "link": "https://wikipedia.org/wiki/Labor_rights"}, {"title": "Rerum novarum", "link": "https://wikipedia.org/wiki/Re<PERSON>_novarum"}, {"title": "Catholic social teaching", "link": "https://wikipedia.org/wiki/Catholic_social_teaching"}]}, {"year": "1905", "text": "The city of Las Vegas is founded in Nevada, United States.", "html": "1905 - The city of <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a> is founded in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, United States.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Las_Vegas\" title=\"Las Vegas\">Las Vegas</a> is founded in <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, United States.", "links": [{"title": "Las Vegas", "link": "https://wikipedia.org/wiki/Las_Vegas"}, {"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}]}, {"year": "1911", "text": "In Standard Oil Co. of New Jersey v. United States, the United States Supreme Court declares Standard Oil to be an \"unreasonable\" monopoly under the Sherman Antitrust Act and orders the company to be broken up.", "html": "1911 - In <i><a href=\"https://wikipedia.org/wiki/Standard_Oil_Co._of_New_Jersey_v._United_States\" title=\"Standard Oil Co. of New Jersey v. United States\">Standard Oil Co. of New Jersey v. United States</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> declares <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a> to be an \"unreasonable\" <a href=\"https://wikipedia.org/wiki/Monopoly\" title=\"Monopoly\">monopoly</a> under the <a href=\"https://wikipedia.org/wiki/Sherman_Antitrust_Act\" title=\"Sherman Antitrust Act\">Sherman Antitrust Act</a> and orders the company to be broken up.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/Standard_Oil_Co._of_New_Jersey_v._United_States\" title=\"Standard Oil Co. of New Jersey v. United States\">Standard Oil Co. of New Jersey v. United States</a></i>, the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> declares <a href=\"https://wikipedia.org/wiki/Standard_Oil\" title=\"Standard Oil\">Standard Oil</a> to be an \"unreasonable\" <a href=\"https://wikipedia.org/wiki/Monopoly\" title=\"Monopoly\">monopoly</a> under the <a href=\"https://wikipedia.org/wiki/Sherman_Antitrust_Act\" title=\"Sherman Antitrust Act\">Sherman Antitrust Act</a> and orders the company to be broken up.", "links": [{"title": "Standard Oil Co. of New Jersey v. United States", "link": "https://wikipedia.org/wiki/Standard_Oil_Co._of_New_Jersey_v._United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Standard Oil", "link": "https://wikipedia.org/wiki/Standard_Oil"}, {"title": "Monopoly", "link": "https://wikipedia.org/wiki/Monopoly"}, {"title": "Sherman Antitrust Act", "link": "https://wikipedia.org/wiki/Sherman_Antitrust_Act"}]}, {"year": "1911", "text": "More than 300 Chinese immigrants are killed in the Torreón massacre when the forces of the Mexican Revolution led by <PERSON> take the city of Torreón from the Federales.", "html": "1911 - More than 300 Chinese immigrants are killed in the <a href=\"https://wikipedia.org/wiki/Torre%C3%B3n_massacre\" title=\"Torreón massacre\">Torreón massacre</a> when the forces of the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take the city of <a href=\"https://wikipedia.org/wiki/Torre%C3%B3n\" title=\"Torreón\">Torreón</a> from the <a href=\"https://wikipedia.org/wiki/Federales\" title=\"Federales\">Federales</a>.", "no_year_html": "More than 300 Chinese immigrants are killed in the <a href=\"https://wikipedia.org/wiki/Torre%C3%B3n_massacre\" title=\"Torreón massacre\">Torreón massacre</a> when the forces of the <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> take the city of <a href=\"https://wikipedia.org/wiki/Torre%C3%B3n\" title=\"Torreón\">Torreón</a> from the <a href=\"https://wikipedia.org/wiki/Federales\" title=\"Federales\">Federales</a>.", "links": [{"title": "Torreón massacre", "link": "https://wikipedia.org/wiki/Torre%C3%B3n_massacre"}, {"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Torreón", "link": "https://wikipedia.org/wiki/Torre%C3%B3n"}, {"title": "Federales", "link": "https://wikipedia.org/wiki/Federales"}]}, {"year": "1918", "text": "The Finnish Civil War ends when the Whites took over Fort Ino, a Russian coastal artillery base on the Karelian Isthmus, from Russian troops.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a> ends when the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">Whites</a> took over <a href=\"https://wikipedia.org/wiki/Fort_Ino\" title=\"Fort Ino\">Fort Ino</a>, a Russian <a href=\"https://wikipedia.org/wiki/Coastal_artillery\" title=\"Coastal artillery\">coastal artillery</a> base on the <a href=\"https://wikipedia.org/wiki/Karelian_Isthmus\" title=\"Karelian Isthmus\">Karelian Isthmus</a>, from Russian troops.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a> ends when the <a href=\"https://wikipedia.org/wiki/White_Guard_(Finland)\" title=\"White Guard (Finland)\">Whites</a> took over <a href=\"https://wikipedia.org/wiki/Fort_Ino\" title=\"Fort Ino\">Fort Ino</a>, a Russian <a href=\"https://wikipedia.org/wiki/Coastal_artillery\" title=\"Coastal artillery\">coastal artillery</a> base on the <a href=\"https://wikipedia.org/wiki/Karelian_Isthmus\" title=\"Karelian Isthmus\">Karelian Isthmus</a>, from Russian troops.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "White Guard (Finland)", "link": "https://wikipedia.org/wiki/White_Guard_(Finland)"}, {"title": "Fort Ino", "link": "https://wikipedia.org/wiki/Fort_Ino"}, {"title": "Coastal artillery", "link": "https://wikipedia.org/wiki/Coastal_artillery"}, {"title": "Karelian Isthmus", "link": "https://wikipedia.org/wiki/Karelian_Isthmus"}]}, {"year": "1916", "text": "A seventeen-year-old farmworker, <PERSON>, is infamously lynched in Waco, Texas, USA, after being convicted of rape and murder.", "html": "1916 - A seventeen-year-old <a href=\"https://wikipedia.org/wiki/Farmworker\" title=\"Farmworker\">farmworker</a>, <PERSON>, is infamously <a href=\"https://wikipedia.org/wiki/Lynching_of_<PERSON>_<PERSON>\" title=\"Lynching of <PERSON>\">lynched</a> in <a href=\"https://wikipedia.org/wiki/Waco,_Texas\" title=\"Waco, Texas\">Waco, Texas</a>, USA, after being <a href=\"https://wikipedia.org/wiki/Convicted\" class=\"mw-redirect\" title=\"Convicted\">convicted</a> of <a href=\"https://wikipedia.org/wiki/Rape\" title=\"Rape\">rape</a> and <a href=\"https://wikipedia.org/wiki/Murder\" title=\"Murder\">murder</a>.", "no_year_html": "A seventeen-year-old <a href=\"https://wikipedia.org/wiki/Farmworker\" title=\"Farmworker\">farmworker</a>, <PERSON>, is infamously <a href=\"https://wikipedia.org/wiki/Lynching_of_<PERSON>_<PERSON>\" title=\"Lynching of <PERSON>\">lynched</a> in <a href=\"https://wikipedia.org/wiki/Waco,_Texas\" title=\"Waco, Texas\">Waco, Texas</a>, USA, after being <a href=\"https://wikipedia.org/wiki/Convicted\" class=\"mw-redirect\" title=\"Convicted\">convicted</a> of <a href=\"https://wikipedia.org/wiki/Rape\" title=\"Rape\">rape</a> and <a href=\"https://wikipedia.org/wiki/Murder\" title=\"Murder\">murder</a>.", "links": [{"title": "Farmworker", "link": "https://wikipedia.org/wiki/Farmworker"}, {"title": "Lynching of <PERSON>", "link": "https://wikipedia.org/wiki/Lynching_of_<PERSON>_<PERSON>"}, {"title": "Waco, Texas", "link": "https://wikipedia.org/wiki/Waco,_Texas"}, {"title": "Convicted", "link": "https://wikipedia.org/wiki/Convicted"}, {"title": "Rape", "link": "https://wikipedia.org/wiki/Rape"}, {"title": "Murder", "link": "https://wikipedia.org/wiki/Murder"}]}, {"year": "1919", "text": "The Winnipeg general strike begins. By 11:00, almost the whole working population of Winnipeg had walked off the job.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Winnipeg_general_strike\" title=\"Winnipeg general strike\">Winnipeg general strike</a> begins. By 11:00, almost the whole working population of <a href=\"https://wikipedia.org/wiki/Winnipeg\" title=\"Winnipeg\">Winnipeg</a> had walked off the job.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Winnipeg_general_strike\" title=\"Winnipeg general strike\">Winnipeg general strike</a> begins. By 11:00, almost the whole working population of <a href=\"https://wikipedia.org/wiki/Winnipeg\" title=\"Winnipeg\">Winnipeg</a> had walked off the job.", "links": [{"title": "Winnipeg general strike", "link": "https://wikipedia.org/wiki/Winnipeg_general_strike"}, {"title": "Winnipeg", "link": "https://wikipedia.org/wiki/Winnipeg"}]}, {"year": "1919", "text": "Greek occupation of Smyrna. During the occupation, the Greek army kills or wounds 350 Turks; those responsible are punished by Greek commander <PERSON><PERSON><PERSON>.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Occupation_of_Smyrna\" title=\"Occupation of Smyrna\">Greek occupation of Smyrna</a>. During the occupation, the Greek army kills or wounds 350 <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turks</a>; those responsible are punished by Greek commander <PERSON><PERSON><PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Occupation_of_Smyrna\" title=\"Occupation of Smyrna\">Greek occupation of Smyrna</a>. During the occupation, the Greek army kills or wounds 350 <a href=\"https://wikipedia.org/wiki/Turkish_people\" title=\"Turkish people\">Turks</a>; those responsible are punished by Greek commander <PERSON><PERSON><PERSON>.", "links": [{"title": "Occupation of Smyrna", "link": "https://wikipedia.org/wiki/Occupation_of_Smyrna"}, {"title": "Turkish people", "link": "https://wikipedia.org/wiki/Turkish_people"}]}, {"year": "1929", "text": "A fire at the Cleveland Clinic in Cleveland, Ohio kills 123.", "html": "1929 - A <a href=\"https://wikipedia.org/wiki/Cleveland_Clinic_fire_of_1929\" title=\"Cleveland Clinic fire of 1929\">fire</a> at the <a href=\"https://wikipedia.org/wiki/Cleveland_Clinic\" title=\"Cleveland Clinic\">Cleveland Clinic</a> in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> kills 123.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Cleveland_Clinic_fire_of_1929\" title=\"Cleveland Clinic fire of 1929\">fire</a> at the <a href=\"https://wikipedia.org/wiki/Cleveland_Clinic\" title=\"Cleveland Clinic\">Cleveland Clinic</a> in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a> kills 123.", "links": [{"title": "Cleveland Clinic fire of 1929", "link": "https://wikipedia.org/wiki/Cleveland_Clinic_fire_of_1929"}, {"title": "Cleveland Clinic", "link": "https://wikipedia.org/wiki/Cleveland_Clinic"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "1932", "text": "In an attempted coup d'état, the Prime Minister of Japan <PERSON><PERSON><PERSON> is assassinated.", "html": "1932 - In an attempted <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> <a href=\"https://wikipedia.org/wiki/Inukai_Tsuyoshi\" title=\"Inukai Tsuyoshi\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/May_15_Incident\" class=\"mw-redirect\" title=\"May 15 Incident\">assassinated</a>.", "no_year_html": "In an attempted <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> <a href=\"https://wikipedia.org/wiki/Inukai_Tsuyoshi\" title=\"Inuka<PERSON> Tsuyoshi\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/May_15_Incident\" class=\"mw-redirect\" title=\"May 15 Incident\">assassinated</a>.", "links": [{"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "May 15 Incident", "link": "https://wikipedia.org/wiki/May_15_Incident"}]}, {"year": "1933", "text": "All military aviation organizations within or under the control of the RLM of Germany were officially merged in a covert manner to form its Wehrmacht military's air arm, the Luftwaffe.", "html": "1933 - All military aviation organizations within or under the control of the <a href=\"https://wikipedia.org/wiki/Ministry_of_Aviation_(Nazi_Germany)\" title=\"Ministry of Aviation (Nazi Germany)\">RLM</a> of Germany were officially merged in a covert manner to form its <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> military's air arm, the <a href=\"https://wikipedia.org/wiki/Luftwaffe#Origins\" title=\"Luftwaffe\">Luftwaffe</a>.", "no_year_html": "All military aviation organizations within or under the control of the <a href=\"https://wikipedia.org/wiki/Ministry_of_Aviation_(Nazi_Germany)\" title=\"Ministry of Aviation (Nazi Germany)\">RLM</a> of Germany were officially merged in a covert manner to form its <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> military's air arm, the <a href=\"https://wikipedia.org/wiki/Luftwaffe#Origins\" title=\"Luftwaffe\">Luftwaffe</a>.", "links": [{"title": "Ministry of Aviation (Nazi Germany)", "link": "https://wikipedia.org/wiki/Ministry_of_Aviation_(Nazi_Germany)"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe#Origins"}]}, {"year": "1934", "text": "A self coup by prime minister <PERSON><PERSON><PERSON><PERSON> succeeded in Latvia, suspending its constitution and dissolving its Saeima.", "html": "1934 - A <a href=\"https://wikipedia.org/wiki/1934_Latvian_coup_d%27%C3%A9tat\" title=\"1934 Latvian coup d'état\">self coup</a> by <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">prime minister</a> <a href=\"https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> succeeded in <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, suspending its constitution and dissolving its <a href=\"https://wikipedia.org/wiki/Saeima\" title=\"Saeima\">Saeima</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1934_Latvian_coup_d%27%C3%A9tat\" title=\"1934 Latvian coup d'état\">self coup</a> by <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Latvia\" title=\"Prime Minister of Latvia\">prime minister</a> <a href=\"https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> succeeded in <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, suspending its constitution and dissolving its <a href=\"https://wikipedia.org/wiki/Saeima\" title=\"Saeima\">Saeima</a>.", "links": [{"title": "1934 Latvian coup d'état", "link": "https://wikipedia.org/wiki/1934_Latvian_coup_d%27%C3%A9tat"}, {"title": "Prime Minister of Latvia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Latvia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C4%81rl<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Saeima", "link": "https://wikipedia.org/wiki/Saeima"}]}, {"year": "1940", "text": "USS Sailfish is recommissioned. It was originally the USS Squalus.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/USS_Sailfish_(SS-192)\" title=\"USS Sailfish (SS-192)\">USS <i>Sailfish</i></a> is recommissioned. It was originally the USS <i>Squalus</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Sailfish_(SS-192)\" title=\"USS Sailfish (SS-192)\">USS <i>Sailfish</i></a> is recommissioned. It was originally the USS <i>Squalus</i>.", "links": [{"title": "USS Sailfish (SS-192)", "link": "https://wikipedia.org/wiki/USS_Sailfish_(SS-192)"}]}, {"year": "1940", "text": "World War II: The Battle of the Netherlands: After fierce fighting, the poorly trained and equipped Dutch troops surrender to Germany, marking the beginning of five years of occupation.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Netherlands\" class=\"mw-redirect\" title=\"Battle of the Netherlands\">Battle of the Netherlands</a>: After fierce fighting, the poorly trained and equipped <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Dutch</a> troops surrender to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a>, marking the beginning of five years of occupation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Netherlands\" class=\"mw-redirect\" title=\"Battle of the Netherlands\">Battle of the Netherlands</a>: After fierce fighting, the poorly trained and equipped <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">Dutch</a> troops surrender to <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a>, marking the beginning of five years of occupation.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Netherlands", "link": "https://wikipedia.org/wiki/Battle_of_the_Netherlands"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1940", "text": "<PERSON> and <PERSON> open the first McDonald's restaurant.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> open the first <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> restaurant.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a> open the first <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"<PERSON>'s\"><PERSON>'s</a> restaurant.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "McDonald's", "link": "https://wikipedia.org/wiki/<PERSON>%27s"}]}, {"year": "1941", "text": "First flight of the Gloster E.28/39 the first British and Allied jet aircraft.", "html": "1941 - First flight of the <a href=\"https://wikipedia.org/wiki/Gloster_E.28/39\" title=\"Gloster E.28/39\">Gloster E.28/39</a> the first British and Allied <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Gloster_E.28/39\" title=\"Gloster E.28/39\">Gloster E.28/39</a> the first British and Allied <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>.", "links": [{"title": "Gloster E.28/39", "link": "https://wikipedia.org/wiki/Gloster_E.28/39"}, {"title": "Jet aircraft", "link": "https://wikipedia.org/wiki/Jet_aircraft"}]}, {"year": "1942", "text": "World War II: In the United States, a bill creating the Women's Army Auxiliary Corps (WAAC) is signed into law.", "html": "1942 - World War II: In the United States, a bill creating the <a href=\"https://wikipedia.org/wiki/Women%27s_Army_Corps\" title=\"Women's Army Corps\">Women's Army Auxiliary Corps</a> (WAAC) is signed into law.", "no_year_html": "World War II: In the United States, a bill creating the <a href=\"https://wikipedia.org/wiki/Women%27s_Army_Corps\" title=\"Women's Army Corps\">Women's Army Auxiliary Corps</a> (WAAC) is signed into law.", "links": [{"title": "Women's Army Corps", "link": "https://wikipedia.org/wiki/Women%27s_Army_Corps"}]}, {"year": "1943", "text": "<PERSON> dissolves the Comintern (or Third International).", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dissolves the <a href=\"https://wikipedia.org/wiki/Comintern\" class=\"mw-redirect\" title=\"Comintern\">Comintern</a> (or <i>Third International</i>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> dissolves the <a href=\"https://wikipedia.org/wiki/Comintern\" class=\"mw-redirect\" title=\"Comintern\">Comintern</a> (or <i>Third International</i>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Comintern", "link": "https://wikipedia.org/wiki/Comintern"}]}, {"year": "1945", "text": "World War II: The Battle of Poljana, the final skirmish in Europe is fought near Prevalje, Slovenia.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Poljana\" title=\"Battle of Poljana\">Battle of Poljana</a>, the final skirmish in Europe is fought near <a href=\"https://wikipedia.org/wiki/Prevalje\" title=\"Prevalje\">Prevalje</a>, Slovenia.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Poljana\" title=\"Battle of Poljana\">Battle of Poljana</a>, the final skirmish in Europe is fought near <a href=\"https://wikipedia.org/wiki/Prevalje\" title=\"Prevalje\">Prevalje</a>, Slovenia.", "links": [{"title": "Battle of Poljana", "link": "https://wikipedia.org/wiki/Battle_of_Poljana"}, {"title": "Prevalje", "link": "https://wikipedia.org/wiki/Prevalje"}]}, {"year": "1948", "text": "Following the expiration of The British Mandate for Palestine, the Kingdom of Egypt, Transjordan, Lebanon, Syria, Iraq and Saudi Arabia invade Israel thus starting the 1948 Arab-Israeli War.", "html": "1948 - Following the expiration of <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">The British Mandate for Palestine</a>, the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Egypt\" title=\"Kingdom of Egypt\">Kingdom of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a>, <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> invade <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> thus starting the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "no_year_html": "Following the expiration of <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">The British Mandate for Palestine</a>, the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Egypt\" title=\"Kingdom of Egypt\">Kingdom of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a>, <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> and <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> invade <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> thus starting the <a href=\"https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War\" title=\"1948 Arab-Israeli War\">1948 Arab-Israeli War</a>.", "links": [{"title": "Mandatory Palestine", "link": "https://wikipedia.org/wiki/Mandatory_Palestine"}, {"title": "Kingdom of Egypt", "link": "https://wikipedia.org/wiki/Kingdom_of_Egypt"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "1948 Arab-Israeli War", "link": "https://wikipedia.org/wiki/1948_Arab%E2%80%93Israeli_War"}]}, {"year": "1957", "text": "At Malden Island in the Pacific Ocean, Britain tests its first hydrogen bomb in Operation Grapple.", "html": "1957 - At <a href=\"https://wikipedia.org/wiki/Malden_Island\" title=\"Malden Island\">Malden Island</a> in the Pacific Ocean, Britain tests its first <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> in <a href=\"https://wikipedia.org/wiki/Operation_Grapple#Grapple\" title=\"Operation Grapple\">Operation Grapple</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Malden_Island\" title=\"Malden Island\">Malden Island</a> in the Pacific Ocean, Britain tests its first <a href=\"https://wikipedia.org/wiki/Hydrogen_bomb\" class=\"mw-redirect\" title=\"Hydrogen bomb\">hydrogen bomb</a> in <a href=\"https://wikipedia.org/wiki/Operation_Grapple#Grapple\" title=\"Operation Grapple\">Operation Grapple</a>.", "links": [{"title": "Malden Island", "link": "https://wikipedia.org/wiki/Malden_Island"}, {"title": "Hydrogen bomb", "link": "https://wikipedia.org/wiki/Hydrogen_bomb"}, {"title": "Operation Grapple", "link": "https://wikipedia.org/wiki/Operation_Grapple#Grapple"}]}, {"year": "1963", "text": "Project Mercury: The launch of the final Mercury mission, Mercury-Atlas 9 with astronaut <PERSON> on board. He becomes the first American to spend more than a day in space, and the last American to go into space alone.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: The launch of the final Mercury mission, <a href=\"https://wikipedia.org/wiki/Mercury-Atlas_9\" title=\"Mercury-Atlas 9\">Mercury-Atlas 9</a> with astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on board. He becomes the first American to spend more than a day in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>, and the last American to go into space alone.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Project Mercury</a>: The launch of the final Mercury mission, <a href=\"https://wikipedia.org/wiki/Mercury-Atlas_9\" title=\"Mercury-Atlas 9\">Mercury-Atlas 9</a> with astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on board. He becomes the first American to spend more than a day in <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>, and the last American to go into space alone.", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "Mercury-Atlas 9", "link": "https://wikipedia.org/wiki/Mercury-Atlas_9"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}]}, {"year": "1970", "text": "President <PERSON> appoints <PERSON> and <PERSON> the first female United States Army generals.", "html": "1970 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the first female United States Army <a href=\"https://wikipedia.org/wiki/General_(United_States)\" title=\"General (United States)\">generals</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the first female United States Army <a href=\"https://wikipedia.org/wiki/General_(United_States)\" title=\"General (United States)\">generals</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "General (United States)", "link": "https://wikipedia.org/wiki/General_(United_States)"}]}, {"year": "1972", "text": "The Ryukyu Islands, under U.S. military governance since its conquest in 1945, reverts to Japanese control.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/Ryukyu_Islands\" title=\"Ryukyu Islands\">Ryukyu Islands</a>, under <a href=\"https://wikipedia.org/wiki/United_States_Military_Government_of_the_Ryukyu_Islands\" title=\"United States Military Government of the Ryukyu Islands\">U.S. military governance</a> since its <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">conquest</a> in 1945, reverts to Japanese control.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ryukyu_Islands\" title=\"Ryukyu Islands\">Ryukyu Islands</a>, under <a href=\"https://wikipedia.org/wiki/United_States_Military_Government_of_the_Ryukyu_Islands\" title=\"United States Military Government of the Ryukyu Islands\">U.S. military governance</a> since its <a href=\"https://wikipedia.org/wiki/Battle_of_Okinawa\" title=\"Battle of Okinawa\">conquest</a> in 1945, reverts to Japanese control.", "links": [{"title": "Ryukyu Islands", "link": "https://wikipedia.org/wiki/Ryukyu_Islands"}, {"title": "United States Military Government of the Ryukyu Islands", "link": "https://wikipedia.org/wiki/United_States_Military_Government_of_the_Ryukyu_Islands"}, {"title": "Battle of Okinawa", "link": "https://wikipedia.org/wiki/Battle_of_Okinawa"}]}, {"year": "1974", "text": "Ma'alot massacre: Members of the Democratic Front for the Liberation of Palestine attack and take hostages at an Israeli school; a total of 31 people are killed, including 22 schoolchildren.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ma%27alot_massacre\" title=\"Ma'alot massacre\">Ma'alot massacre</a>: Members of the <a href=\"https://wikipedia.org/wiki/Democratic_Front_for_the_Liberation_of_Palestine\" title=\"Democratic Front for the Liberation of Palestine\">Democratic Front for the Liberation of Palestine</a> attack and take hostages at an <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> school; a total of 31 people are killed, including 22 schoolchildren.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%27alot_massacre\" title=\"Ma'alot massacre\">Ma'alot massacre</a>: Members of the <a href=\"https://wikipedia.org/wiki/Democratic_Front_for_the_Liberation_of_Palestine\" title=\"Democratic Front for the Liberation of Palestine\">Democratic Front for the Liberation of Palestine</a> attack and take hostages at an <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> school; a total of 31 people are killed, including 22 schoolchildren.", "links": [{"title": "Ma'alot massacre", "link": "https://wikipedia.org/wiki/Ma%27alot_massacre"}, {"title": "Democratic Front for the Liberation of Palestine", "link": "https://wikipedia.org/wiki/Democratic_Front_for_the_Liberation_of_Palestine"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1976", "text": "Aeroflot Flight 1802 crashes near Viktorivka, Chernihiv Raion, Chernihiv Oblast, killing 52.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1802\" title=\"Aeroflot Flight 1802\">Aeroflot Flight 1802</a> crashes near <a href=\"https://wikipedia.org/wiki/<PERSON>iv<PERSON>,_Chernihiv_Raion,_Chernihiv_Oblast\" title=\"Viktorivka, Chernihiv Raion, Chernihiv Oblast\">Viktorivka, Chernihiv Raion, Chernihiv Oblast</a>, killing 52.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1802\" title=\"Aeroflot Flight 1802\">Aeroflot Flight 1802</a> crashes near <a href=\"https://wikipedia.org/wiki/Viktoriv<PERSON>,_Chernihiv_Raion,_Chernihiv_Oblast\" title=\"Viktorivka, Chernihiv Raion, Chernihiv Oblast\">Viktorivka, Chernihiv Raion, Chernihiv Oblast</a>, killing 52.", "links": [{"title": "Aeroflot Flight 1802", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_1802"}, {"title": "Viktorivka, Chernihiv Raion, Chernihiv Oblast", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Chernihiv_Raion,_Chernihiv_Oblast"}]}, {"year": "1988", "text": "Soviet-Afghan War: After more than eight years of fighting, the Soviet Army begins to withdraw 115,000 troops from Afghanistan.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: After more than eight years of fighting, the <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet Army</a> begins to withdraw 115,000 troops from <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War\" title=\"Soviet-Afghan War\">Soviet-Afghan War</a>: After more than eight years of fighting, the <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet Army</a> begins to withdraw 115,000 troops from <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>.", "links": [{"title": "Soviet-Afghan War", "link": "https://wikipedia.org/wiki/Soviet%E2%80%93Afghan_War"}, {"title": "Soviet Army", "link": "https://wikipedia.org/wiki/Soviet_Army"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON> becomes France's first female Prime Minister.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/%C3%89dith_Cresson\" title=\"<PERSON><PERSON><PERSON> Cress<PERSON>\"><PERSON><PERSON><PERSON></a> becomes France's first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dith_Cresson\" title=\"<PERSON><PERSON><PERSON> C<PERSON>\"><PERSON><PERSON><PERSON></a> becomes France's first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dith_<PERSON><PERSON>on"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1997", "text": "The United States government acknowledges the existence of the \"Secret War\" in Laos and dedicates the Laos Memorial in honor of <PERSON><PERSON><PERSON> and other \"Secret War\" veterans.", "html": "1997 - The United States government acknowledges the existence of the <a href=\"https://wikipedia.org/wiki/Laotian_Civil_War\" title=\"Laotian Civil War\">\"Secret War\" in Laos</a> and dedicates the <a href=\"https://wikipedia.org/wiki/Laos_Memorial\" title=\"Laos Memorial\">Laos Memorial</a> in honor of <a href=\"https://wikipedia.org/wiki/Hmong_people\" title=\"Hmong people\">Hmong</a> and other \"Secret War\" veterans.", "no_year_html": "The United States government acknowledges the existence of the <a href=\"https://wikipedia.org/wiki/Laotian_Civil_War\" title=\"Laotian Civil War\">\"Secret War\" in Laos</a> and dedicates the <a href=\"https://wikipedia.org/wiki/Laos_Memorial\" title=\"Laos Memorial\">Laos Memorial</a> in honor of <a href=\"https://wikipedia.org/wiki/Hmong_people\" title=\"Hmong people\">Hmong</a> and other \"Secret War\" veterans.", "links": [{"title": "Laotian Civil War", "link": "https://wikipedia.org/wiki/Laotian_Civil_War"}, {"title": "Laos Memorial", "link": "https://wikipedia.org/wiki/Laos_Memorial"}, {"title": "Hmong people", "link": "https://wikipedia.org/wiki/Hmong_people"}]}, {"year": "1997", "text": "The Space Shuttle Atlantis launches on STS-84 to dock with the Russian space station Mir.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-84\" title=\"STS-84\">STS-84</a> to dock with the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-84\" title=\"STS-84\">STS-84</a> to dock with the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-84", "link": "https://wikipedia.org/wiki/STS-84"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "2001", "text": "A CSX EMD SD40-2 rolls out of a train yard in Walbridge, Ohio, with 47 freight cars, including some tank cars with flammable chemical, after its engineer fails to reboard it after setting a yard switch. It travels south driverless for 66 miles (106 km) until it was brought to a halt near Kenton. The incident became the inspiration for the 2010 film Unstoppable.", "html": "2001 - A <a href=\"https://wikipedia.org/wiki/CSX_Transportation\" title=\"CSX Transportation\">CSX</a> <a href=\"https://wikipedia.org/wiki/EMD_SD40-2\" title=\"EMD SD40-2\">EMD SD40-2</a> <a href=\"https://wikipedia.org/wiki/CSX_8888_incident\" title=\"CSX 8888 incident\">rolls out of a train yard</a> in <a href=\"https://wikipedia.org/wiki/Walbridge,_Ohio\" title=\"Walbridge, Ohio\">Walbridge</a>, Ohio, with 47 freight cars, including some <a href=\"https://wikipedia.org/wiki/Tank_car\" title=\"Tank car\">tank cars</a> with flammable chemical, after its engineer fails to reboard it after setting a yard switch. It travels south driverless for 66 miles (106 km) until it was brought to a halt near <a href=\"https://wikipedia.org/wiki/Kenton,_Ohio\" title=\"Kenton, Ohio\">Kenton</a>. The incident became the inspiration for the 2010 film <i><a href=\"https://wikipedia.org/wiki/Unstoppable_(2010_film)\" title=\"Unstoppable (2010 film)\">Unstoppable</a></i>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/CSX_Transportation\" title=\"CSX Transportation\">CSX</a> <a href=\"https://wikipedia.org/wiki/EMD_SD40-2\" title=\"EMD SD40-2\">EMD SD40-2</a> <a href=\"https://wikipedia.org/wiki/CSX_8888_incident\" title=\"CSX 8888 incident\">rolls out of a train yard</a> in <a href=\"https://wikipedia.org/wiki/Walbridge,_Ohio\" title=\"Walbridge, Ohio\">Walbridge</a>, Ohio, with 47 freight cars, including some <a href=\"https://wikipedia.org/wiki/Tank_car\" title=\"Tank car\">tank cars</a> with flammable chemical, after its engineer fails to reboard it after setting a yard switch. It travels south driverless for 66 miles (106 km) until it was brought to a halt near <a href=\"https://wikipedia.org/wiki/Kenton,_Ohio\" title=\"Kenton, Ohio\">Kenton</a>. The incident became the inspiration for the 2010 film <i><a href=\"https://wikipedia.org/wiki/Unstoppable_(2010_film)\" title=\"Unstoppable (2010 film)\">Unstoppable</a></i>.", "links": [{"title": "CSX Transportation", "link": "https://wikipedia.org/wiki/CSX_Transportation"}, {"title": "EMD SD40-2", "link": "https://wikipedia.org/wiki/EMD_SD40-2"}, {"title": "CSX 8888 incident", "link": "https://wikipedia.org/wiki/CSX_8888_incident"}, {"title": "Walbridge, Ohio", "link": "https://wikipedia.org/wiki/Walbridge,_Ohio"}, {"title": "Tank car", "link": "https://wikipedia.org/wiki/Tank_car"}, {"title": "Kenton, Ohio", "link": "https://wikipedia.org/wiki/Kenton,_Ohio"}, {"title": "Unstoppable (2010 film)", "link": "https://wikipedia.org/wiki/Unstoppable_(2010_film)"}]}, {"year": "2004", "text": "Arsenal F.C. go an entire league campaign unbeaten in the English Premier League, joining Preston North End F.C. with the right to claim the title \"The Invincibles\".", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Arsenal_F.C.\" title=\"Arsenal F.C.\">Arsenal F.C.</a> go an entire league campaign unbeaten in the English Premier League, joining <a href=\"https://wikipedia.org/wiki/Preston_North_End_F.C.\" title=\"Preston North End F.C.\">Preston North End F.C.</a> with the right to claim the title \"<a href=\"https://wikipedia.org/wiki/The_Invincibles_(football)\" class=\"mw-redirect\" title=\"The Invincibles (football)\">The Invincibles</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arsenal_F.C.\" title=\"Arsenal F.C.\">Arsenal F.C.</a> go an entire league campaign unbeaten in the English Premier League, joining <a href=\"https://wikipedia.org/wiki/Preston_North_End_F.C.\" title=\"Preston North End F.C.\">Preston North End F.C.</a> with the right to claim the title \"<a href=\"https://wikipedia.org/wiki/The_Invincibles_(football)\" class=\"mw-redirect\" title=\"The Invincibles (football)\">The Invincibles</a>\".", "links": [{"title": "Arsenal F.C.", "link": "https://wikipedia.org/wiki/Arsenal_F.C."}, {"title": "Preston North End F.C.", "link": "https://wikipedia.org/wiki/Preston_North_End_F.C."}, {"title": "The Invincibles (football)", "link": "https://wikipedia.org/wiki/The_Invincibles_(football)"}]}, {"year": "2008", "text": "California becomes the second U.S. state after Massachusetts in 2004 to legalize same-sex marriage after the state's own Supreme Court rules a previous ban unconstitutional.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_California\" title=\"Same-sex marriage in California\">California</a> becomes the second <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> after <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Massachusetts\" title=\"Same-sex marriage in Massachusetts\">Massachusetts</a> in <a href=\"https://wikipedia.org/wiki/2004\" title=\"2004\">2004</a> to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a> after the state's own Supreme Court <a href=\"https://wikipedia.org/wiki/In_re_Marriage_Cases\" title=\"In re Marriage Cases\">rules a previous ban unconstitutional</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_California\" title=\"Same-sex marriage in California\">California</a> becomes the second <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> after <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Massachusetts\" title=\"Same-sex marriage in Massachusetts\">Massachusetts</a> in <a href=\"https://wikipedia.org/wiki/2004\" title=\"2004\">2004</a> to legalize <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a> after the state's own Supreme Court <a href=\"https://wikipedia.org/wiki/In_re_Marriage_Cases\" title=\"In re Marriage Cases\">rules a previous ban unconstitutional</a>.", "links": [{"title": "Same-sex marriage in California", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_California"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Same-sex marriage in Massachusetts", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_Massachusetts"}, {"title": "2004", "link": "https://wikipedia.org/wiki/2004"}, {"title": "Same-sex marriage", "link": "https://wikipedia.org/wiki/Same-sex_marriage"}, {"title": "In re Marriage Cases", "link": "https://wikipedia.org/wiki/In_re_Marriage_Cases"}]}, {"year": "2010", "text": "<PERSON> becomes the youngest person to sail, non-stop and unassisted around the world solo.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the youngest person to sail, non-stop and unassisted around the world solo.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the youngest person to sail, non-stop and unassisted around the world solo.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "An upsurge in violence in Iraq leaves more than 389 people dead over three days.", "html": "2013 - An <a href=\"https://wikipedia.org/wiki/May_2013_Iraq_attacks\" title=\"May 2013 Iraq attacks\">upsurge in violence</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> leaves more than 389 people dead over three days.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/May_2013_Iraq_attacks\" title=\"May 2013 Iraq attacks\">upsurge in violence</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> leaves more than 389 people dead over three days.", "links": [{"title": "May 2013 Iraq attacks", "link": "https://wikipedia.org/wiki/May_2013_Iraq_attacks"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2023", "text": "The UN commemorates the Palestinian Nakba Day for the first time.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a> commemorates the Palestinian <a href=\"https://wikipedia.org/wiki/Nakba_Day\" title=\"Nakba Day\">Nakba Day</a> for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a> commemorates the Palestinian <a href=\"https://wikipedia.org/wiki/Nakba_Day\" title=\"Nakba Day\">Nakba Day</a> for the first time.", "links": [{"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Nakba Day", "link": "https://wikipedia.org/wiki/Nakba_Day"}]}], "Births": [{"year": "1397", "text": "<PERSON><PERSON> the Great, Korean king of Joseon (d. 1450)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Korean king of <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a> (d. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON> the Great</a>, Korean king of <a href=\"https://wikipedia.org/wiki/Joseon\" title=\"Joseon\">Joseon</a> (d. 1450)", "links": [{"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}, {"title": "Joseon", "link": "https://wikipedia.org/wiki/Joseon"}]}, {"year": "1531", "text": "<PERSON> of Austria, Duchess of Jülich-Cleves-Berg (d. 1581)[citation needed]", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Maria_of_Austria,_Duchess_of_J%C3%<PERSON>lich-Cleves-Berg\" title=\"<PERSON> of Austria, Duchess of Jülich-Cleves-Berg\"><PERSON> of Austria, Duchess of Jülich-Cleves-Berg</a> (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Austria,_Duchess_of_J%C3%BClich-Cleves-Berg\" title=\"<PERSON> of Austria, Duchess of Jülich-Cleves-Berg\"><PERSON> of Austria, Duchess of Jülich-Cleves-Berg</a> (d. 1581)", "links": [{"title": "<PERSON> of Austria, Duchess of Jülich-Cleves-Berg", "link": "https://wikipedia.org/wiki/Maria_of_Austria,_Duchess_of_J%C3%<PERSON>lich-Cleves-Berg"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, Dutch sculptor and architect (d. 1621)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sculptor and architect (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch sculptor and architect (d. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, Italian priest and composer (d. 1643)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and composer (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, French-American missionary and saint (d. 1642)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goupil\" title=\"<PERSON>\"><PERSON></a>, French-American missionary and saint (d. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goupil\" title=\"<PERSON>\"><PERSON></a>, French-American missionary and saint (d. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Goupil"}]}, {"year": "1633", "text": "<PERSON><PERSON><PERSON><PERSON>, French noble (d. 1707)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_Prestre_de_Vauban\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Le Prestre de Vauban\"><PERSON><PERSON><PERSON><PERSON>au<PERSON></a>, French noble (d. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>stre_de_Vauban\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> Le Prestre de Vauban\"><PERSON><PERSON><PERSON><PERSON></a>, French noble (d. 1707)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Vau<PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, 1st Baron <PERSON>, British judge (d. 1689)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, British judge (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, British judge (d. 1689)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1689", "text": "<PERSON> <PERSON>, English writer (d. 1762)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English writer (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English writer (d. 1762)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, Hungarian priest and astronomer (d. 1792)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/Maximilian_Hell\" title=\"Maximilian Hell\"><PERSON></a>, Hungarian priest and astronomer (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maximilian_Hell\" title=\"Maximilian Hell\"><PERSON></a>, Hungarian priest and astronomer (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maximilian_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, American lawyer and politician, 4th United States Attorney General (d. 1820)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON>.</a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1820)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sr."}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1759", "text": "<PERSON>, Austrian pianist and composer (d. 1824)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian businessman and politician (d. 1843)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (d. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON>, German-Austrian politician, 1st State Chancellor of the Austrian Empire (d. 1859)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Austrian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Austria\" class=\"mw-redirect\" title=\"List of Ministers-President of Austria\">State Chancellor of the Austrian Empire</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Austrian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Austria\" class=\"mw-redirect\" title=\"List of Ministers-President of Austria\">State Chancellor of the Austrian Empire</a> (d. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ministers-President of Austria", "link": "https://wikipedia.org/wiki/List_of_Ministers-President_of_Austria"}]}, {"year": "1786", "text": "<PERSON><PERSON>, Greek general and politician (d. 1864)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, son of <PERSON>, was a Mexican soldier and diplomat who served as a regent in the Second Mexican Empire (d. 1869)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was a Mexican soldier and diplomat who served as a <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> in the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, was a Mexican soldier and diplomat who served as a <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> in the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (d. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Morelos"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "Second Mexican Empire", "link": "https://wikipedia.org/wiki/Second_Mexican_Empire"}]}, {"year": "1805", "text": "<PERSON>, English railway solicitor and Member of Parliament (MP) (d. 1878)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Coventry_MP)\" title=\"<PERSON> (Coventry MP)\"><PERSON></a>, English railway solicitor and <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Coventry_MP)\" title=\"<PERSON> (Coventry MP)\"><PERSON></a>, English railway solicitor and <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) (d. 1878)", "links": [{"title": "<PERSON> (Coventry MP)", "link": "https://wikipedia.org/wiki/<PERSON>_(Coventry_MP)"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}]}, {"year": "1808", "text": "<PERSON>, Irish composer and conductor (d. 1870)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian philosopher and author (d. 1905)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Tagore\" title=\"<PERSON><PERSON><PERSON><PERSON> Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and author (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Tagore\"><PERSON><PERSON><PERSON><PERSON></a>, Indian philosopher and author (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, American commander and geologist (d. 1912)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and geologist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and geologist (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON>, Russian zoologist (d. 1916)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian zoologist (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian zoologist (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Russian painter and illustrator (d. 1926)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and illustrator (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, German neuropathologist. (d. 1905)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neuropathologist. (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neuropathologist. (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON><PERSON><PERSON>, Ukrainian-French philologist and author (d. 1929)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-French philologist and author (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-French philologist and author (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, American novelist (d. 1919)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American novelist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American novelist (d. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Swiss mountaineer (d. 1917)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, Scottish-American astronomer and academic (d. 1911)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American astronomer and academic (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish-American astronomer and academic (d. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French physicist and academic, Nobel Prize laureate (d. 1906)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1862", "text": "<PERSON>, Austrian author and playwright (d. 1931)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, English businessman and politician, invented <PERSON><PERSON> (d. 1936)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, invented <a href=\"https://wikipedia.org/wiki/Meccano\" title=\"Meccano\">Meccano</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, invented <a href=\"https://wikipedia.org/wiki/Meccano\" title=\"Meccano\">Meccano</a> (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Meccano", "link": "https://wikipedia.org/wiki/Meccano"}]}, {"year": "1869", "text": "<PERSON>, Swiss target shooter (d. 1945)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, Swiss target shooter (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, Swiss target shooter (d. 1945)", "links": [{"title": "<PERSON> (sport shooter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)"}]}, {"year": "1869", "text": "<PERSON>, Australian politician, 20th Premier of New South Wales (d. 1921)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 20th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1921)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Finnish socialist and the Chairman of the Senate of Finland (d. 1963)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish socialist and the Chairman of the Senate of Finland (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish socialist and the Chairman of the Senate of Finland (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_To<PERSON>i"}]}, {"year": "1882", "text": "<PERSON>, Scottish international footballer (d. 1950)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish international footballer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish international footballer (d. 1950)", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)"}]}, {"year": "1890", "text": "<PERSON>, American short story writer, novelist, and essayist (d. 1980)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Russian novelist and playwright (d. 1940)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and playwright (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and playwright (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish journalist, translator and writer (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish journalist, translator and writer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish journalist, translator and writer (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Austrian-Brazilian chemist and academic (d. 1971)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian chemist and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Brazilian chemist and academic (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American admiral (d. 1977)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Welsh boxer (d. 1969)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh boxer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh boxer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Filipino filmmaker, founder of Philippine cinema (d. 1959)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Nepomuceno\" title=\"<PERSON>\"><PERSON></a>, Filipino filmmaker, founder of Philippine cinema (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Nepomuceno\" title=\"<PERSON>\"><PERSON></a>, Filipino filmmaker, founder of Philippine cinema (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Nepomuceno"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American hurdler and cartoonist (d. 1973)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and cartoonist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler and cartoonist (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Feg_Murray"}]}, {"year": "1895", "text": "<PERSON>, American captain, banker, and politician (d. 1972)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Prescott_Bush\" title=\"Prescott Bush\"><PERSON></a>, American captain, banker, and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prescott_Bush\" title=\"Prescott Bush\"><PERSON></a>, American captain, banker, and politician (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Prescott_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American lieutenant and politician (d. 1941)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, French model, actress, and singer (d. 1992)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model, actress, and singer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French model, actress, and singer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French general (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%89tien<PERSON>_<PERSON>luy"}]}, {"year": "1900", "text": "<PERSON>, American mathematician, pioneer in computer programming (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Ida_Rhodes\" title=\"Ida Rhodes\"><PERSON></a>, American mathematician, pioneer in computer programming (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_Rhodes\" title=\"Ida Rhodes\"><PERSON></a>, American mathematician, pioneer in computer programming (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Rhodes"}]}, {"year": "1901", "text": "<PERSON>, Australian author (d. 1984)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Argentinian-Italian footballer and manager (d. 1983)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer and manager (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American lawyer and politician, 48th Mayor of Chicago (d. 1976)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Soviet aircraft pilot of Polish origin (d. 1937)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet aircraft pilot of Polish origin (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Soviet aircraft pilot of Polish origin (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, German mathematician and archaeologist (d. 1998)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and archaeologist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and archaeologist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American game show host and author (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French cartoonist, illustrator, painter, and sculptor (d. 1976)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cartoonist, illustrator, painter, and sculptor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cartoonist, illustrator, painter, and sculptor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American businessman and amateur photographer, filmed the <PERSON><PERSON><PERSON><PERSON> film (d. 1970)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and amateur photographer, filmed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_film\" title=\"<PERSON><PERSON><PERSON><PERSON> film\">Zapruder film</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and amateur photographer, filmed the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_film\" title=\"<PERSON><PERSON><PERSON><PERSON> film\">Z<PERSON>ruder film</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> film", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_film"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist (d. 1931)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON><PERSON><PERSON><PERSON> Thapar</a>, Indian activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukhdev_Thapar\" title=\"Sukhdev Thapar\"><PERSON><PERSON><PERSON><PERSON> Thapar</a>, Indian activist (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> T<PERSON>par", "link": "https://wikipedia.org/wiki/Sukhdev_Thapar"}]}, {"year": "1909", "text": "<PERSON>, English actor, producer, and screenwriter (d. 1984)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Chilean singer-songwriter (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean singer-songwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean singer-songwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1910", "text": "<PERSON>, British-based American actress (d. 2005)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-based American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-based American actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Swiss playwright and novelist (d. 1991)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss playwright and novelist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss playwright and novelist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, German physician (d. 1978)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>heus<PERSON>\"><PERSON><PERSON></a>, German physician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>heus<PERSON>\" title=\"<PERSON><PERSON>heus<PERSON>\"><PERSON><PERSON></a>, German physician (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Oberheuser"}]}, {"year": "1912", "text": "<PERSON>, American composer and educator (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and educator (d. 2003)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach (d. 1972)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Turk_Broda\" title=\"Turk Broda\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turk_Broda\" title=\"Turk Broda\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (d. 1972)", "links": [{"title": "Turk Broda", "link": "https://wikipedia.org/wiki/Tu<PERSON>_<PERSON><PERSON>a"}]}, {"year": "1914", "text": "<PERSON>, Canadian farmer and politician, 25th Premier of Prince Edward Island (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian farmer and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Prince Edward Island", "link": "https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island"}]}, {"year": "1914", "text": "<PERSON><PERSON>, English composer, producer, and conductor (d. 1979)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mor\" title=\"<PERSON><PERSON> Paramor\"><PERSON><PERSON></a>, English composer, producer, and conductor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mor\" title=\"<PERSON><PERSON> Paramor\"><PERSON><PERSON></a>, English composer, producer, and conductor (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Paramor"}]}, {"year": "1915", "text": "<PERSON>, English-South African author and activist (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African author and activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-South African author and activist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2009)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1915", "text": "<PERSON>, Danish production manager and producer (d. 1993)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish production manager and producer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish production manager and producer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Danish actress (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American lieutenant and target shooter (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, American lieutenant and target shooter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, American lieutenant and target shooter (d. 2015)", "links": [{"title": "<PERSON> (sport shooter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)"}]}, {"year": "1918", "text": "<PERSON>, Canadian-American actor (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French director and screenwriter (d. 1985)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Basque writer, member of ETA and translator (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Basque writer, member of ETA and translator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Basque writer, member of ETA and translator (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Russian historian and ethnographer (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>gu<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and ethnographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and ethnographer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese nun and author (d. 2021)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ak<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese nun and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese nun and author (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American sailor and photographer (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and photographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and photographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English-Australian composer and conductor (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian composer and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian composer and conductor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German-Peruvian ornithologist and zoologist (d. 1971)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Peruvian ornithologist and zoologist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Peruvian ornithologist and zoologist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Russian pianist and composer (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English geneticist and biologist (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Mary_<PERSON>_<PERSON>\" title=\"Mary <PERSON> Lyon\"><PERSON></a>, English geneticist and biologist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mary_<PERSON>_<PERSON>\" title=\"Mary <PERSON> Lyon\"><PERSON></a>, English geneticist and biologist (d. 2014)", "links": [{"title": "Mary <PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American soldier, pilot, and politician, 74th Governor of Georgia (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician, 74th <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1925", "text": "<PERSON>, Jamaican-English actor and stuntman (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English actor and stuntman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English actor and stuntman (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Canadian pianist, composer, and educator (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Clermont_P%C3%A9pin\" title=\"Clermont Pépin\"><PERSON><PERSON><PERSON> Pépin</a>, Canadian pianist, composer, and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clermont_P%C3%A9pin\" title=\"Clermont Pépin\"><PERSON><PERSON><PERSON> P<PERSON></a>, Canadian pianist, composer, and educator (d. 2006)", "links": [{"title": "Clermont Pépin", "link": "https://wikipedia.org/wiki/Clermont_P%C3%A9pin"}]}, {"year": "1926", "text": "<PERSON>, English author, playwright, and screenwriter (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author, playwright, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English author, playwright, and screenwriter (d. 2001)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1926", "text": "<PERSON>, English playwright and screenwriter (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American painter and sculptor", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American golfer and sportscaster (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>-<PERSON>, Vincentian politician and agronomist, 2nd Prime Minister of Saint Vincent and the Grenadines (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Vincentian politician and agronomist, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_<PERSON>_<PERSON>_and_the_Grenadines\" class=\"mw-redirect\" title=\"Prime Minister of Saint Vincent and the Grenadines\">Prime Minister of Saint Vincent and the Grenadines</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Vincentian politician and agronomist, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_<PERSON>_<PERSON>_and_the_Grenadines\" class=\"mw-redirect\" title=\"Prime Minister of Saint Vincent and the Grenadines\">Prime Minister of Saint Vincent and the Grenadines</a> (d. 2021)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_<PERSON>"}, {"title": "Prime Minister of Saint Vincent and the Grenadines", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Saint_<PERSON>_and_the_Grenadines"}]}, {"year": "1935", "text": "<PERSON>, American pole vaulter (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Italian-English cricketer (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English cricketer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English cricketer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2008)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Phillips\" title=\"<PERSON> Phillips\"><PERSON></a>, American singer-songwriter and guitarist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Phillips\" title=\"<PERSON> Phillips\"><PERSON></a>, American singer-songwriter and guitarist (d. 2008)", "links": [{"title": "Utah Phillips", "link": "https://wikipedia.org/wiki/Utah_Phillips"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Japanese singer, actor, director, composer, author and drag queen", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, actor, director, composer, author and drag queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer, actor, director, composer, author and drag queen", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Italian-American actress and singer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Estonian basketball player (d. 1977)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Mart_Laga\" title=\"Mart Laga\"><PERSON></a>, Estonian basketball player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart_Laga\" title=\"Mart Laga\"><PERSON></a>, Estonian basketball player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart_Laga"}]}, {"year": "1936", "text": "<PERSON>, English painter and illustrator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American playwright and novelist (d. 2003)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and novelist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Czech-American politician and diplomat, 64th United States Secretary of State (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American politician and diplomat, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American politician and diplomat, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1937", "text": "<PERSON>, Norwegian singer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American singer, guitarist, and actor (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, guitarist, and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, guitarist, and actor (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, French actress, director, and screenwriter (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress, director, and screenwriter (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>c"}]}, {"year": "1938", "text": "<PERSON>, American author (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Nancy_Garden\" title=\"Nancy Garden\"><PERSON></a>, American author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nancy_Garden\" title=\"Nancy Garden\"><PERSON></a>, American author (d. 2014)", "links": [{"title": "Nancy <PERSON>", "link": "https://wikipedia.org/wiki/Nancy_Garden"}]}, {"year": "1939", "text": "<PERSON>, English high jumper and educator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English high jumper and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American businessman (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American actress and singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Lainie_Kazan\" title=\"Lainie Kazan\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lainie_Kazan\" title=\"Lainie Kazan\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lainie_Kazan"}]}, {"year": "1940", "text": "<PERSON>, American basketball player and coach", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American illustrator and publisher, co-founded the Rip Off Press (d. 2006)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, American illustrator and publisher, co-founded the <i><a href=\"https://wikipedia.org/wiki/Rip_Off_Press\" title=\"Rip Off Press\">Rip Off Press</a></i> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, American illustrator and publisher, co-founded the <i><a href=\"https://wikipedia.org/wiki/Rip_Off_Press\" title=\"Rip Off Press\">Rip Off Press</a></i> (d. 2006)", "links": [{"title": "<PERSON><PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)"}, {"title": "Rip Off Press", "link": "https://wikipedia.org/wiki/Rip_Off_Press"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Indonesian businessman and politician, 10th Vice President of Indonesia", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian businessman and politician, 10th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian businessman and politician, 10th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Indonesia\" title=\"Vice President of Indonesia\">Vice President of Indonesia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of Indonesia", "link": "https://wikipedia.org/wiki/Vice_President_of_Indonesia"}]}, {"year": "1942", "text": "<PERSON>, Australian politician, 35th Premier of Tasmania", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 35th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 35th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and actress (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"K. T. O<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_<PERSON><PERSON>\" title=\"K. T. O<PERSON>lin\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and actress (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian lawyer and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gin\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gin\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_B%C3%A9gin"}]}, {"year": "1943", "text": "<PERSON>, American songwriter, producer, and conductor (d. 2004)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, producer, and conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter, producer, and conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American police officer and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German sociologist and academic (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English hematologist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hematologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hematologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American boxer (d. 1999)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Vietnamese priest and activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Thadeus_<PERSON>uy%E1%BB%85n_V%C4%83n_L%C3%BD\" title=\"Thad<PERSON>\">T<PERSON><PERSON></a>, Vietnamese priest and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad<PERSON>_<PERSON>uy%E1%BB%85n_V%C4%83n_L%C3%BD\" title=\"Thad<PERSON>\">T<PERSON><PERSON></a>, Vietnamese priest and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad<PERSON>_<PERSON>uy%E1%BB%85n_V%C4%83n_L%C3%BD"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Australian singer-songwriter, guitarist and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Goble\" title=\"Graeham Goble\"><PERSON><PERSON><PERSON> Goble</a>, Australian singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Goble\" title=\"<PERSON><PERSON><PERSON> Goble\"><PERSON><PERSON><PERSON> Goble</a>, Australian singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Goble"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Yu<PERSON>ka_Enatsu\" title=\"Yu<PERSON>ka Enatsu\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yu<PERSON><PERSON>_Enatsu\" title=\"Yu<PERSON>ka Enatsu\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yutaka_Enatsu"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American politician, 44th Governor of Kansas", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Kansas\" class=\"mw-redirect\" title=\"Governor of Kansas\">Governor of Kansas</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Kansas", "link": "https://wikipedia.org/wiki/Governor_of_Kansas"}]}, {"year": "1949", "text": "<PERSON>, American captain, pilot, and astronaut", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American captain, pilot, and astronaut", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1949", "text": "<PERSON>, English geologist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English geologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian politician, 41st Premier of Tasmania (d. 2004)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 2004)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1950", "text": "<PERSON>, American golfer (d. 2005)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2005)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English political scientist and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American mathematician and physicist, Nobel Prize laureate", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American actor, director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Cha<PERSON> Palm<PERSON>\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Cha<PERSON> Palm<PERSON>eri\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "Chazz <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Palminteri"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, English physicist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"At<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hen<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English-Irish singer-songwriter, guitarist, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English-American geographer and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geographer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English journalist and broadcaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Tunisian politician (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tunisian politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Cypriot singer-songwriter and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot singer-songwriter and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot singer-songwriter and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lia_Vissi"}]}, {"year": "1956", "text": "<PERSON>, Greek lawyer and politician, Greek Minister of Labour", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour_and_Social_Solidarity_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Labour and Social Solidarity (Greece)\">Greek Minister of Labour</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Labour_and_Social_Solidarity_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Labour and Social Solidarity (Greece)\">Greek Minister of Labour</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Labour and Social Solidarity (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Labour_and_Social_Solidarity_(Greece)"}]}, {"year": "1956", "text": "<PERSON>, American television anchor and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American television anchor and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American television anchor and sportscaster", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1956", "text": "<PERSON>, American nuclear engineer (d. 2023)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nuclear engineer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nuclear engineer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American-English author and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Spanish politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Ibarretxe\" title=\"<PERSON>\"><PERSON></a>, Spanish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_Ibarretxe\" title=\"<PERSON>\"><PERSON></a>, Spanish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_I<PERSON><PERSON>xe"}]}, {"year": "1957", "text": "<PERSON>, American football player and wrestler", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American musical theater actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musical theater actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musical theater actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1958", "text": "<PERSON>, American football player and wrestler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Thai boxer and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Khaosai_Galaxy\" title=\"Khaosai Galaxy\">Khaosai Galaxy</a>, Thai boxer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khaosai_Galaxy\" title=\"Khaosai Galaxy\">Khaosai Galaxy</a>, Thai boxer and politician", "links": [{"title": "Khaosai Galaxy", "link": "https://wikipedia.org/wiki/Khaosai_Galaxy"}]}, {"year": "1959", "text": "<PERSON>, Spanish race car driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>rez-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON>rez-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_P%C3%A9<PERSON>-<PERSON>a"}]}, {"year": "1959", "text": "<PERSON>, American-Belgian singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Belgian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Belgian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Australian actress, singer, and dancer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress, singer, and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American director and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Sri Lankan politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>itis"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Mexican-American television journalist.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American television journalist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American television journalist.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giselle_Fern%C3%A1ndez"}]}, {"year": "1962", "text": "<PERSON>, Australian swimmer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, South African footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Danish lawyer and politician, 40th Prime Minister of Denmark", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8k<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B8<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_L%C3%B8k<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1965", "text": "<PERSON>, Brazilian singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ra"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby league player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>c"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_N%C4%9Bmec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_N%C4%9Bmec\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_N%C4%9Bmec"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Norwegian chess grandmaster and football player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>g<PERSON>\"><PERSON><PERSON></a>, Norwegian chess grandmaster and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>g<PERSON>\"><PERSON><PERSON></a>, Norwegian chess grandmaster and football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American journalist and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1968", "text": "<PERSON>, Swedish academic and politician, 15th European Commissioner for Trade", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish academic and politician, 15th <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Trade\" title=\"European Commissioner for Trade\">European Commissioner for Trade</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tr%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish academic and politician, 15th <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Trade\" title=\"European Commissioner for Trade\">European Commissioner for Trade</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cecilia_Malmstr%C3%B6m"}, {"title": "European Commissioner for Trade", "link": "https://wikipedia.org/wiki/European_Commissioner_for_Trade"}]}, {"year": "1968", "text": "<PERSON>, English journalist and broadcaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese-American baseball player (d. 2011)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American baseball player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American baseball player (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American football player and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Dutch footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English photographer, director, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English photographer, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English photographer, director, and screenwriter", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)\" title=\"<PERSON> (wide receiver)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (wide receiver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wide_receiver)"}]}, {"year": "1970", "text": "<PERSON>, English captain and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English captain and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English captain and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1971", "text": "<PERSON>, Slovenian tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1nic\" title=\"<PERSON>\"><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1nic\" title=\"<PERSON>\"><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1nic"}]}, {"year": "1972", "text": "<PERSON>, Scottish politician, Secretary of State for Scotland", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1972", "text": "<PERSON>, French actor and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Greek basketball player and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English chess player and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tredup\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tredup\" title=\"<PERSON><PERSON> Tredup\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tredup"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American musician and writer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zappa\" title=\"<PERSON><PERSON> Zappa\"><PERSON><PERSON></a>, American musician and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zappa\" title=\"<PERSON><PERSON> Zappa\"><PERSON><PERSON></a>, American musician and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ap<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Belarusian lawyer and politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Finnish Sami politician, first Sami ever to be elected to the Finnish Parliament", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4rvi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sami_people\" class=\"mw-redirect\" title=\"Sami people\">Finnish Sami</a> politician, first Sami ever to be elected to the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4rvi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Sami_people\" class=\"mw-redirect\" title=\"Sami people\">Finnish Sami</a> politician, first Sami ever to be elected to the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ne_<PERSON><PERSON><PERSON>%C3%A4rvi"}, {"title": "Sami people", "link": "https://wikipedia.org/wiki/Sami_people"}, {"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1976)\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1976)\" title=\"<PERSON> (footballer, born 1976)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer, born 1976)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1976)"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Polish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3wek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3wek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jace<PERSON>_<PERSON>rzyn%C3%B3wek"}]}, {"year": "1976", "text": "<PERSON>, American football player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Slovenian politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/An%C5%BEe_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An%C5%BEe_Lo<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An%C5%BEe_Logar"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1978", "text": "<PERSON>, American gymnast and pediatrician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and pediatrician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and pediatrician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Canadian soccer player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1978)\" title=\"<PERSON><PERSON> (footballer, born 1978)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/Edu_(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, New Zealand rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American skier", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Irish guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, French footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ra"}]}, {"year": "1981", "text": "<PERSON>, English international footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English equestrian", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Jamie<PERSON><PERSON>, American actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Jamaican sprinter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Ecuadorian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Rafael_P%C3%A9rez_(baseball)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Lebanese singer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Abboud\"><PERSON><PERSON></a>, Lebanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Abboud\"><PERSON><PERSON></a>, Lebanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian rugby league player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch singer, songwriter, rapper, actor and record producer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Mr_<PERSON><PERSON>z\" class=\"mw-redirect\" title=\"Mr <PERSON><PERSON>z\">Mr <PERSON></a>, Dutch singer, songwriter, rapper, actor and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr_<PERSON><PERSON>z\" class=\"mw-redirect\" title=\"Mr <PERSON><PERSON>z\">Mr <PERSON></a>, Dutch singer, songwriter, rapper, actor and record producer", "links": [{"title": "Mr <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mr_<PERSON><PERSON>z"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Italian diver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian diver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English football coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Ugandan football goalkeeper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan football goalkeeper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan football goalkeeper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, South African javelin thrower", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African javelin thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" class=\"mw-redirect\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" class=\"mw-redirect\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Chilean footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Fern%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mat%C3%ADas_Fern%C3%<PERSON><PERSON><PERSON>_(footballer,_born_1986)\" title=\"<PERSON><PERSON> (footballer, born 1986)\"><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/Mat%C3%ADas_Fern%C3%A1<PERSON><PERSON>_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON>, Scottish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Turkish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ersan_%C4%B0lyasova\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ersan_%C4%B0lyasova\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ersan_%C4%B0lyasova"}]}, {"year": "1987", "text": "<PERSON>, Argentinian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Estonian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Indrek_Kajupank\" title=\"Indrek Kajupank\"><PERSON><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indrek_Kajupank\" title=\"Indrek Kajupank\">In<PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "Indrek Kajupank", "link": "https://wikipedia.org/wiki/Indrek_Kajupank"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Korean-American singer and entertainer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Korean-American singer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Korean-American singer and entertainer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Mapou_<PERSON>-<PERSON>wa\" title=\"Mapou Yang<PERSON>-Mbiwa\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mapou_Yang<PERSON>-<PERSON>wa\" title=\"Mapou Yanga-Mbiwa\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mapou_Yanga-Mbiwa"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_E<PERSON>le"}]}, {"year": "1990", "text": "<PERSON>, Korean guitarist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yun"}]}, {"year": "1990", "text": "<PERSON>, New Zealand model", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, New Zealand rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Czech international footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kalas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kalas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech international footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kalas"}]}, {"year": "1996", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ousmane_Demb%C3%A9l%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ousmane_Demb%C3%A9l%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ousmane_Demb%C3%A9l%C3%A9"}]}, {"year": "1997", "text": "<PERSON>, Australian rugby league player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Italian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Russian tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anastasia_Gasanova"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Ukrainian tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ya<PERSON>remska"}]}, {"year": "2002", "text": "<PERSON>, American internet celebrity, singer, actor", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hu<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Huddy\"><PERSON></a>, American internet celebrity, singer, actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Hu<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Huddy\"><PERSON></a>, American internet celebrity, singer, actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Korean singer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Haerin"}]}], "Deaths": [{"year": "392", "text": "<PERSON><PERSON><PERSON> <PERSON>, Roman emperor (b. 371)", "html": "392 - <a href=\"https://wikipedia.org/wiki/Valentinian_II\" title=\"Valentinian II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (b. 371)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_II\" title=\"Valentinian II\"><PERSON>ntin<PERSON> II</a>, Roman emperor (b. 371)", "links": [{"title": "Valentinian II", "link": "https://wikipedia.org/wiki/Valentinian_II"}]}, {"year": "558", "text": "<PERSON> of Galeata, Christian monk (b. 476)", "html": "558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Galeata\" title=\"<PERSON> of Galeata\"><PERSON> of Galeata</a>, Christian monk (b. 476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Galeata\"><PERSON> of Galeata</a>, Christian monk (b. 476)", "links": [{"title": "<PERSON> of Galeata", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "884", "text": "<PERSON><PERSON>, pope of the Catholic Church (b. 830)", "html": "884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Marin<PERSON>\"><PERSON><PERSON></a>, pope of the Catholic Church (b. 830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Marinus <PERSON>\"><PERSON><PERSON></a>, pope of the Catholic Church (b. 830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "913", "text": "<PERSON><PERSON>, German archbishop (b. 850)", "html": "913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Mainz)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON> (archbishop of Mainz)\"><PERSON><PERSON> <PERSON></a>, German archbishop (b. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Mainz)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON> (archbishop of Mainz)\"><PERSON><PERSON> <PERSON></a>, German archbishop (b. 850)", "links": [{"title": "<PERSON><PERSON> <PERSON> (archbishop of Mainz)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Mainz)"}]}, {"year": "926", "text": "<PERSON><PERSON>, Chinese emperor (b. 885)", "html": "926 - <a href=\"https://wikipedia.org/wiki/Li_Cunxu\" title=\"Li Cunxu\"><PERSON><PERSON></a>, Chinese emperor (b. 885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Cunxu\" title=\"Li Cunxu\"><PERSON><PERSON></a>, Chinese emperor (b. 885)", "links": [{"title": "<PERSON> Cunxu", "link": "https://wikipedia.org/wiki/Li_Cunxu"}]}, {"year": "973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bishop of Wells", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(bishop_of_Wells)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (bishop of Wells)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, bishop of Wells", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(bishop_of_Wells)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (bishop of Wells)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, bishop of Wells", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (bishop of Wells)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(bishop_of_Wells)"}]}, {"year": "1036", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, emperor of Japan (b. 1008)", "html": "1036 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-I<PERSON>j%C5%8D\" title=\"Emperor <PERSON>-Ichijō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>j%C5%8D\" title=\"Emperor <PERSON>-Ichijō\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1008)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Ichij%C5%8D"}]}, {"year": "1157", "text": "<PERSON>, Grand Prince of Kiev (b. 1099)", "html": "1157 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of Kiev (b. 1099)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of Kiev (b. 1099)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1175", "text": "<PERSON><PERSON><PERSON>, prince of Armenia", "html": "1175 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Armenia\" title=\"<PERSON><PERSON><PERSON>, Prince of Armenia\"><PERSON><PERSON><PERSON></a>, prince of Armenia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Armenia\" title=\"<PERSON><PERSON><PERSON>, Prince of Armenia\"><PERSON><PERSON><PERSON></a>, prince of Armenia", "links": [{"title": "<PERSON><PERSON><PERSON>, Prince of Armenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Prince_of_Armenia"}]}, {"year": "1174", "text": "<PERSON><PERSON>, Seljuk emir of Syria (b. 1118)", "html": "1174 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_(died_1174)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON> (died 1174)\"><PERSON><PERSON> <PERSON><PERSON></a>, Sel<PERSON>k emir of Syria (b. 1118)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_(died_1174)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> (died 1174)\"><PERSON><PERSON> <PERSON><PERSON></a>, Seljuk emir of Syria (b. 1118)", "links": [{"title": "<PERSON><PERSON> (died 1174)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_(died_1174)"}]}, {"year": "1268", "text": "<PERSON>, count of Savoy (b. 1203)", "html": "1268 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON></a>, count of Savoy (b. 1203)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON></a>, count of Savoy (b. 1203)", "links": [{"title": "<PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy"}]}, {"year": "1461", "text": "<PERSON>, Italian painter (b. c. 1410)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. c. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. c. 1410)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "<PERSON>, 3rd Duke of Somerset (b. 1436)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Somerset\" title=\"<PERSON>, 3rd Duke of Somerset\"><PERSON>, 3rd Duke of Somerset</a> (b. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Somerset\" title=\"<PERSON>, 3rd Duke of Somerset\"><PERSON>, 3rd Duke of Somerset</a> (b. 1436)", "links": [{"title": "<PERSON>, 3rd Duke of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Somerset"}]}, {"year": "1470", "text": "<PERSON>, king of Sweden (b. 1409)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (b. 1409)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> VIII of Sweden\"><PERSON></a>, king of Sweden (b. 1409)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_Sweden"}]}, {"year": "1585", "text": "<PERSON><PERSON>, Japanese samurai (b. 1535)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gahide\" title=\"<PERSON><PERSON> Nagahide\"><PERSON><PERSON></a>, Japanese samurai (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gahide\" title=\"<PERSON><PERSON> Nagahide\"><PERSON><PERSON></a>, Japanese samurai (b. 1535)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni<PERSON>_Nagahide"}]}, {"year": "1609", "text": "<PERSON>, Italian composer and educator (b. 1557)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1557)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1557)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1615", "text": "<PERSON>, English politician (b. 1560)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1615)\" title=\"<PERSON> (died 1615)\"><PERSON></a>, English politician (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1615)\" title=\"<PERSON> (died 1615)\"><PERSON></a>, English politician (b. 1560)", "links": [{"title": "<PERSON> (died 1615)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1615)"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1585)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/Hend<PERSON>_Avercamp\" title=\"Hendrick Avercamp\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hend<PERSON>_Avercamp\" title=\"Hendrick Avercamp\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1585)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rcamp"}]}, {"year": "1698", "text": "<PERSON>, French actress (b. 1642)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marie_Champmesl%C3%A9"}]}, {"year": "1699", "text": "Sir <PERSON>, 3rd Baronet, English politician (b. 1631)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English politician (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English politician (b. 1631)", "links": [{"title": "Sir <PERSON>, 3rd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet"}]}, {"year": "1700", "text": "<PERSON>, American minister (b. 1636)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, American minister (b. 1636)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, American minister (b. 1636)", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(minister)"}]}, {"year": "1740", "text": "<PERSON><PERSON><PERSON><PERSON>, English publisher (b. 1680)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Chambers\" title=\"Ephraim Chambers\"><PERSON><PERSON><PERSON><PERSON></a>, English publisher (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Eph<PERSON>m Chambers\"><PERSON><PERSON><PERSON><PERSON></a>, English publisher (b. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON>, English priest and hagiographer (b. 1710)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English priest and hagiographer (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English priest and hagiographer (b. 1710)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON><PERSON>, Costa Rican lawyer and politician, Head of State of Costa Rica (b. 1800)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Costa Rican lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Costa_Rica\" class=\"mw-redirect\" title=\"List of Presidents of Costa Rica\">Head of State of Costa Rica</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Costa Rican lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Costa_Rica\" class=\"mw-redirect\" title=\"List of Presidents of Costa Rica\">Head of State of Costa Rica</a> (b. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Presidents of Costa Rica", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Costa_Rica"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, German architect and educator, designed the Semper Opera House (b. 1803)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Semperoper\" title=\"<PERSON><PERSON><PERSON>\">Semper Opera House</a> (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Semperoper\" title=\"<PERSON>mperoper\">Semper Opera House</a> (b. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American poet and author (b. 1830)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Austrian-born chemist and educator (b. 1863)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born chemist and educator (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born chemist and educator (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Freund"}]}, {"year": "1919", "text": "<PERSON>, Turkish journalist (b. 1888)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French diplomat and politician, Nobel Prize laureate (b. 1852)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_d%27Estour<PERSON><PERSON>_de_<PERSON>stant\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'Estournelles de Constant\"><PERSON><PERSON><PERSON>urn<PERSON> Constant</a>, French diplomat and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Estour<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>Estournelles de Constant\"><PERSON><PERSON> Constant</a>, French diplomat and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1852)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27Estour<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1926", "text": "<PERSON>, Australian biologist (b. 1850)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 15th <PERSON><PERSON><PERSON><PERSON> (b. 1845)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I\" title=\"Umegatani Tōtarō I\">Umegatani Tōtarō I</a>, Japanese sumo wrestler, the 15th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I\" title=\"Umegatani Tōtarō I\">Umegatani Tōtarō I</a>, Japanese sumo wrestler, the 15th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1845)", "links": [{"title": "Umegatani Tōtarō I", "link": "https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian painter and theoretician (b. 1878)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian painter and theoretician (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian painter and theoretician (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, 1st Viscount <PERSON>, English politician, Chancellor of the Exchequer (b. 1864)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1864)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1945", "text": "<PERSON>, English soldier, bandmaster, and composer (b. 1881)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, bandmaster, and composer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, bandmaster, and composer (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English author, poet, and critic (b. 1886)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_writer)\" title=\"<PERSON> (British writer)\"><PERSON></a>, English author, poet, and critic (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_writer)\" title=\"<PERSON> (British writer)\"><PERSON></a>, English author, poet, and critic (b. 1886)", "links": [{"title": "<PERSON> (British writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_writer)"}]}, {"year": "1948", "text": "<PERSON>, Irish-American priest, founded Boys Town (b. 1886)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American priest, founded <a href=\"https://wikipedia.org/wiki/Boys_Town_(organization)\" title=\"Boys Town (organization)\">Boys Town</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American priest, founded <a href=\"https://wikipedia.org/wiki/Boys_Town_(organization)\" title=\"Boys Town (organization)\">Boys Town</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Boys Town (organization)", "link": "https://wikipedia.org/wiki/Boys_Town_(organization)"}]}, {"year": "1954", "text": "<PERSON>, American soldier and author (b. 1893)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer, politician, and businessperson (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessperson (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessperson (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English painter and magician (b. 1886)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Austin_Osman_Spare\" title=\"Austin Osman Spare\"><PERSON></a>, English painter and magician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>sman_Spare\" title=\"Austin Osman Spare\"><PERSON></a>, English painter and magician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>re"}]}, {"year": "1957", "text": "<PERSON>, American race car driver (b. 1920)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (b. 1920)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1892)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English-born Bishop of Accra and soldier (b. 1884)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English-born <a href=\"https://wikipedia.org/wiki/Bishop_of_Accra\" class=\"mw-redirect\" title=\"Bishop of Accra\">Bishop of Accra</a> and soldier (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English-born <a href=\"https://wikipedia.org/wiki/Bishop_of_Accra\" class=\"mw-redirect\" title=\"Bishop of Accra\">Bishop of Accra</a> and soldier (b. 1884)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}, {"title": "Bishop of Accra", "link": "https://wikipedia.org/wiki/<PERSON>_of_Accra"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Croatian lawyer and politician (b. 1879)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ma%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladko_Ma%C4%8Dek"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Italian businessman (b. 1887)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pio Pi<PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pio_Pion"}]}, {"year": "1967", "text": "<PERSON>, American painter (b. 1882)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Italian painter (b. 1892)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Italo_Mus\" title=\"Italo Mus\"><PERSON><PERSON></a>, Italian painter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Mus\" title=\"Italo Mus\"><PERSON><PERSON></a>, Italian painter (b. 1892)", "links": [{"title": "Italo <PERSON>", "link": "https://wikipedia.org/wiki/Italo_Mus"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player (b. 1890)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English director, producer, and playwright (b. 1900)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and playwright (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and playwright (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian lawyer and politician, 12th Prime Minister of Australia (b. 1894)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1980", "text": "<PERSON>, American historian and author (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>range"}]}, {"year": "1982", "text": "<PERSON>, American race car driver (b. 1946)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American pastor, theologian, and philosopher (b. 1912)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, theologian, and philosopher (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, theologian, and philosopher (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress and writer (b. 1947)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Italian race car driver (b. 1958)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American historian, journalist, and author (b. 1915)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, journalist, and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, journalist, and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American composer and conductor (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ethnographer and author (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ethnographer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Canadian ethnographer and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luc_Lacourci%C3%A8re"}]}, {"year": "1991", "text": "<PERSON>, German mathematician and academic (b. 1956)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Malian ethnologist and author (b. 1901)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Amadou_Hamp%C3%A2t%C3%A9_B%C3%A2\" title=\"<PERSON>ado<PERSON>\"><PERSON><PERSON><PERSON></a>, Malian ethnologist and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amadou_Hamp%C3%A2t%C3%A9_B%C3%A2\" title=\"Amado<PERSON>\"><PERSON><PERSON><PERSON></a>, Malian ethnologist and author (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amadou_Hamp%C3%A2t%C3%A9_B%C3%A2"}]}, {"year": "1991", "text": "<PERSON>, German race car driver (b. 1922)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Sudanese poet and diplomat (b. 1933)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese poet and diplomat (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese poet and diplomat (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1905)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor (b. 1928)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American lawyer and judge (b. 1910)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player (b. 1944)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Turkish economist, banker, politician, 15th Prime Minister of Turkey (b. 1919)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish economist, banker, politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish economist, banker, politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter, guitarist, and actress (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>_<PERSON>\" title=\"June Carter Cash\">June <PERSON></a>, American singer-songwriter, guitarist, and actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>_<PERSON>\" title=\"June <PERSON> Cash\">June <PERSON></a>, American singer-songwriter, guitarist, and actress (b. 1929)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Iraqi footballer (b. 1961)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi footballer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iraqi footballer (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American pastor, founded Liberty University (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, founded <a href=\"https://wikipedia.org/wiki/Liberty_University\" title=\"Liberty University\">Liberty University</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, founded <a href=\"https://wikipedia.org/wiki/Liberty_University\" title=\"Liberty University\">Liberty University</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Liberty University", "link": "https://wikipedia.org/wiki/Liberty_University"}]}, {"year": "2008", "text": "<PERSON>, Scottish footballer and manager (b. 1956)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (b. 1956)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2008", "text": "<PERSON>, American composer and conductor (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Courage\"><PERSON></a>, American composer and conductor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American illustrator (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Australian actor, director, and producer (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American basketball player and bass player (b. 1964)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Wayman_Tisdale\" title=\"Wayman Tisdale\"><PERSON><PERSON></a>, American basketball player and bass player (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wayman_Tisdale\" title=\"Wayman Tisdale\"><PERSON><PERSON></a>, American basketball player and bass player (b. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>man_Tisdale"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Austrian footballer (b. 1987)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Besian_Idrizaj\" title=\"Besian Idrizaj\"><PERSON><PERSON></a>, Austrian footballer (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Besian_Idrizaj\" title=\"Besian Idrizaj\"><PERSON><PERSON></a>, Austrian footballer (b. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Swiss race car driver (b. 1950)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss race car driver (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss race car driver (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mexican novelist and essayist (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican novelist and essayist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican novelist and essayist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, German historian and author (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German historian and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lu<PERSON>\"><PERSON><PERSON></a>, German historian and author (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Egyptian soldier and politician, 33rd Prime Minister of Egypt (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>in"}, {"title": "Prime Minister of Egypt", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Egypt"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Bissau-Guinean politician, President of Guinea-Bissau (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/President_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"President of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/President_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"President of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Guinea-Bissau", "link": "https://wikipedia.org/wiki/President_of_Guinea-Bissau"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, French-Belgian politician, 63rd Prime Minister of Belgium (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Belgian politician, 63rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Belgian politician, 63rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Japanese director and screenwriter (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Norib<PERSON> Suzuki\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Norib<PERSON> Suzuki\"><PERSON><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1933)", "links": [{"title": "Noribumi Suzuki", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Suzuki"}]}, {"year": "2015", "text": "<PERSON>, German-American physical therapist and author (b. 1914)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physical therapist and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physical therapist and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sculptor and educator (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Scottish Gaelic singer (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish Gaelic singer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Cypriot-American football player (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Garo_Yepremian\" title=\"Garo Yepremian\"><PERSON><PERSON></a>, Cypriot-American football player (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garo_Yepremian\" title=\"Garo Yepremian\"><PERSON><PERSON></a>, Cypriot-American football player (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garo_<PERSON>remian"}]}, {"year": "2017", "text": "<PERSON> American tropical fish expert, publisher of pet books, and entrepreneur (b. 1927)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American <a href=\"https://wikipedia.org/wiki/Tropical_fish\" title=\"Tropical fish\">tropical fish</a> expert, publisher of pet books, and entrepreneur (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American <a href=\"https://wikipedia.org/wiki/Tropical_fish\" title=\"Tropical fish\">tropical fish</a> expert, publisher of pet books, and entrepreneur (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Tropical fish", "link": "https://wikipedia.org/wiki/Tropical_fish"}]}, {"year": "2020", "text": "<PERSON>, American actor, comedian, and writer (b. 1933)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and writer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and writer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, British journalist and scientist (b. 1937)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and scientist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and scientist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Australian rugby league player and coach (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English actress (b. 1951)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}