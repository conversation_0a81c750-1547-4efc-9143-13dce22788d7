{"date": "March 2", "url": "https://wikipedia.org/wiki/March_2", "data": {"Events": [{"year": "537", "text": "Siege of Rome: The Ostrogoth army under king <PERSON><PERSON><PERSON> begins the siege of the capital. <PERSON><PERSON><PERSON> conducts a delaying action outside the Flaminian Gate; he and a detachment of his bucellarii are almost cut off.", "html": "537 - <a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%9338)\" class=\"mw-redirect\" title=\"Siege of Rome (537-38)\">Siege of Rome</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoth</a> army under king <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> begins the <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> of the capital. <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON>isa<PERSON></a> conducts a delaying action outside the <a href=\"https://wikipedia.org/wiki/Piazza_del_Popolo\" title=\"Piazza del Popolo\">Flaminian Gate</a>; he and a detachment of his <i><a href=\"https://wikipedia.org/wiki/Bucellarii\" title=\"Bucellarii\">bucellarii</a></i> are almost cut off.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%9338)\" class=\"mw-redirect\" title=\"Siege of Rome (537-38)\">Siege of Rome</a>: The <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoth</a> army under king <a href=\"https://wikipedia.org/wiki/Vitiges\" title=\"Vitiges\">Vitiges</a> begins the <a href=\"https://wikipedia.org/wiki/Siege\" title=\"Siege\">siege</a> of the capital. <a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"Belisarius\"><PERSON><PERSON><PERSON></a> conducts a delaying action outside the <a href=\"https://wikipedia.org/wiki/Piazza_del_Popolo\" title=\"Piazza del Popolo\">Flaminian Gate</a>; he and a detachment of his <i><a href=\"https://wikipedia.org/wiki/Bucellarii\" title=\"Bucellarii\">bucellarii</a></i> are almost cut off.", "links": [{"title": "Siege of Rome (537-38)", "link": "https://wikipedia.org/wiki/Siege_of_Rome_(537%E2%80%9338)"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "Vitiges", "link": "https://wikipedia.org/wiki/Vitiges"}, {"title": "Siege", "link": "https://wikipedia.org/wiki/Siege"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Piazza del Popolo", "link": "https://wikipedia.org/wiki/Piazza_del_Popolo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bucellarii"}]}, {"year": "986", "text": "<PERSON> becomes the last Carolingian king of West Francia after the death of his father, <PERSON><PERSON><PERSON>.", "html": "986 - <a href=\"https://wikipedia.org/wiki/Louis_V_of_France\" title=\"Louis V of France\"><PERSON></a> becomes the last <a href=\"https://wikipedia.org/wiki/Carolingian_dynasty\" title=\"Carolingian dynasty\"><PERSON><PERSON><PERSON></a> king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> after the death of his father, <a href=\"https://wikipedia.org/wiki/Lothair_of_France\" title=\"<PERSON><PERSON><PERSON> of France\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_V_of_France\" title=\"Louis V of France\"><PERSON></a> becomes the last <a href=\"https://wikipedia.org/wiki/Carolingian_dynasty\" title=\"Carolingian dynasty\"><PERSON><PERSON><PERSON></a> king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> after the death of his father, <a href=\"https://wikipedia.org/wiki/Lothair_of_France\" title=\"Lot<PERSON><PERSON> of France\">Lot<PERSON><PERSON></a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_V_of_France"}, {"title": "Carolingian dynasty", "link": "https://wikipedia.org/wiki/Carolingian_dynasty"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}, {"title": "<PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_of_France"}]}, {"year": "1331", "text": "Fall of Nicaea to the Ottoman Turks after a siege.", "html": "1331 - Fall of Nicaea to the Ottoman Turks after a <a href=\"https://wikipedia.org/wiki/Siege_of_Nicaea_(1328%E2%80%931331)\" title=\"Siege of Nicaea (1328-1331)\">siege</a>.", "no_year_html": "Fall of Nicaea to the Ottoman Turks after a <a href=\"https://wikipedia.org/wiki/Siege_of_Nicaea_(1328%E2%80%931331)\" title=\"Siege of Nicaea (1328-1331)\">siege</a>.", "links": [{"title": "Siege of Nicaea (1328-1331)", "link": "https://wikipedia.org/wiki/Siege_of_Nicaea_(1328%E2%80%931331)"}]}, {"year": "1444", "text": "Skanderbeg organizes a group of Albanian nobles to form the League of Lezhë.", "html": "1444 - <a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a> organizes a group of <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> nobles to form the <a href=\"https://wikipedia.org/wiki/League_of_Lezh%C3%AB\" title=\"League of Lezhë\">League of Lezhë</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a> organizes a group of <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> nobles to form the <a href=\"https://wikipedia.org/wiki/League_of_Lezh%C3%AB\" title=\"League of Lezhë\">League of Lezhë</a>.", "links": [{"title": "Skanderbeg", "link": "https://wikipedia.org/wiki/Skanderbeg"}, {"title": "Albanians", "link": "https://wikipedia.org/wiki/Albanians"}, {"title": "League of Lezhë", "link": "https://wikipedia.org/wiki/League_of_Lezh%C3%AB"}]}, {"year": "1458", "text": "<PERSON> of Poděbrady is chosen as the king of Bohemia.", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a> is chosen as the king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady\" title=\"<PERSON> of Poděbrady\"><PERSON> of Poděbrady</a> is chosen as the king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "links": [{"title": "<PERSON> of Poděbrady", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pod%C4%9Bbrady"}, {"title": "Kingdom of Bohemia", "link": "https://wikipedia.org/wiki/Kingdom_of_Bohemia"}]}, {"year": "1476", "text": "Burgundian Wars: The Old Swiss Confederacy hands <PERSON> the <PERSON>, Duke of Burgundy, a major defeat in the Battle of Grandson in Canton of Neuchâtel.", "html": "1476 - <a href=\"https://wikipedia.org/wiki/Burgundian_Wars\" title=\"Burgundian Wars\">Burgundian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> hands <a href=\"https://wikipedia.org/wiki/Charles_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, Duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundy</a>, a major defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_Grandson\" title=\"Battle of Grandson\">Battle of Grandson</a> in <a href=\"https://wikipedia.org/wiki/Canton_of_Neuch%C3%A2tel\" title=\"Canton of Neuchâtel\">Canton of Neuchâtel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Burgundian_Wars\" title=\"Burgundian Wars\">Burgundian Wars</a>: The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> hands <a href=\"https://wikipedia.org/wiki/Charles_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, Duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Burgundy\" title=\"Duchy of Burgundy\">Burgundy</a>, a major defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_Grandson\" title=\"Battle of Grandson\">Battle of Grandson</a> in <a href=\"https://wikipedia.org/wiki/Canton_of_Neuch%C3%A2tel\" title=\"Canton of Neuchâtel\">Canton of Neuchâtel</a>.", "links": [{"title": "Burgundian Wars", "link": "https://wikipedia.org/wiki/Burgundian_Wars"}, {"title": "Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Old_Swiss_Confederacy"}, {"title": "<PERSON> the Bold", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duchy of Burgundy", "link": "https://wikipedia.org/wiki/Duchy_of_Burgundy"}, {"title": "Battle of Grandson", "link": "https://wikipedia.org/wiki/Battle_of_Grandson"}, {"title": "Canton of Neuchâtel", "link": "https://wikipedia.org/wiki/Canton_of_Neuch%C3%A2tel"}]}, {"year": "1484", "text": "The College of Arms is formally incorporated by Royal Charter signed by King <PERSON> of England.", "html": "1484 - The <a href=\"https://wikipedia.org/wiki/College_of_Arms\" title=\"College of Arms\">College of Arms</a> is formally incorporated by <a href=\"https://wikipedia.org/wiki/Royal_Charter\" class=\"mw-redirect\" title=\"Royal Charter\">Royal Charter</a> signed by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/College_of_Arms\" title=\"College of Arms\">College of Arms</a> is formally incorporated by <a href=\"https://wikipedia.org/wiki/Royal_Charter\" class=\"mw-redirect\" title=\"Royal Charter\">Royal Charter</a> signed by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "College of Arms", "link": "https://wikipedia.org/wiki/College_of_Arms"}, {"title": "Royal Charter", "link": "https://wikipedia.org/wiki/Royal_Charter"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1498", "text": "V<PERSON> da Gama's fleet visits the Island of Mozambique.", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a>'s fleet visits the <a href=\"https://wikipedia.org/wiki/Island_of_Mozambique\" title=\"Island of Mozambique\">Island of Mozambique</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a>'s fleet visits the <a href=\"https://wikipedia.org/wiki/Island_of_Mozambique\" title=\"Island of Mozambique\">Island of Mozambique</a>.", "links": [{"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}, {"title": "Island of Mozambique", "link": "https://wikipedia.org/wiki/Island_of_Mozambique"}]}, {"year": "1657", "text": "The Great Fire of Meireki begins in Edo (now Tokyo), Japan, causing more than 100,000 deaths before it exhausts itself three days later.", "html": "1657 - The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Meireki\" title=\"Great Fire of Meireki\">Great Fire of Meireki</a> begins in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> (now <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>), Japan, causing more than 100,000 deaths before it exhausts itself three days later.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Meireki\" title=\"Great Fire of Meireki\">Great Fire of Meireki</a> begins in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> (now <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>), Japan, causing more than 100,000 deaths before it exhausts itself three days later.", "links": [{"title": "Great Fire of Meireki", "link": "https://wikipedia.org/wiki/Great_Fire_of_Mei<PERSON>i"}, {"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}, {"title": "Tokyo", "link": "https://wikipedia.org/wiki/Tokyo"}]}, {"year": "1776", "text": "American Revolutionary War: Patriot militia units attempt to prevent capture of supply ships in and around the Savannah River by a small fleet of the Royal Navy in the Battle of the Rice Boats.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">Patriot</a> militia units attempt to prevent capture of supply ships in and around the <a href=\"https://wikipedia.org/wiki/Savannah_River\" title=\"Savannah River\">Savannah River</a> by a small fleet of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Rice_Boats\" title=\"Battle of the Rice Boats\">Battle of the Rice Boats</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">Patriot</a> militia units attempt to prevent capture of supply ships in and around the <a href=\"https://wikipedia.org/wiki/Savannah_River\" title=\"Savannah River\">Savannah River</a> by a small fleet of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Rice_Boats\" title=\"Battle of the Rice Boats\">Battle of the Rice Boats</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Patriot (American Revolution)", "link": "https://wikipedia.org/wiki/Patriot_(American_Revolution)"}, {"title": "Savannah River", "link": "https://wikipedia.org/wiki/Savannah_River"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battle of the Rice Boats", "link": "https://wikipedia.org/wiki/Battle_of_the_Rice_Boats"}]}, {"year": "1791", "text": "<PERSON> demonstrates the first semaphore line near Paris.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the first <a href=\"https://wikipedia.org/wiki/Optical_telegraph\" title=\"Optical telegraph\">semaphore</a> line near Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the first <a href=\"https://wikipedia.org/wiki/Optical_telegraph\" title=\"Optical telegraph\">semaphore</a> line near Paris.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Optical telegraph", "link": "https://wikipedia.org/wiki/Optical_telegraph"}]}, {"year": "1797", "text": "The Bank of England issues the first one-pound and two-pound banknotes.", "html": "1797 - The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> <a href=\"https://wikipedia.org/wiki/Bank_of_England_note_issues\" title=\"Bank of England note issues\">issues</a> the first <a href=\"https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling\" title=\"Banknotes of the pound sterling\">one-pound</a> and <a href=\"https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling\" title=\"Banknotes of the pound sterling\">two-pound</a> <a href=\"https://wikipedia.org/wiki/Banknote\" title=\"Banknote\">banknotes</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> <a href=\"https://wikipedia.org/wiki/Bank_of_England_note_issues\" title=\"Bank of England note issues\">issues</a> the first <a href=\"https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling\" title=\"Banknotes of the pound sterling\">one-pound</a> and <a href=\"https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling\" title=\"Banknotes of the pound sterling\">two-pound</a> <a href=\"https://wikipedia.org/wiki/Banknote\" title=\"Banknote\">banknotes</a>.", "links": [{"title": "Bank of England", "link": "https://wikipedia.org/wiki/Bank_of_England"}, {"title": "Bank of England note issues", "link": "https://wikipedia.org/wiki/Bank_of_England_note_issues"}, {"title": "Banknotes of the pound sterling", "link": "https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling"}, {"title": "Banknotes of the pound sterling", "link": "https://wikipedia.org/wiki/Banknotes_of_the_pound_sterling"}, {"title": "Banknote", "link": "https://wikipedia.org/wiki/Banknote"}]}, {"year": "1807", "text": "The U.S. Congress passes the Act Prohibiting Importation of Slaves, disallowing the importation of new slaves into the country.", "html": "1807 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves\" title=\"Act Prohibiting Importation of Slaves\">Act Prohibiting Importation of Slaves</a>, disallowing the importation of new <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slaves</a> into the country.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves\" title=\"Act Prohibiting Importation of Slaves\">Act Prohibiting Importation of Slaves</a>, disallowing the importation of new <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slaves</a> into the country.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Act Prohibiting Importation of Slaves", "link": "https://wikipedia.org/wiki/Act_Prohibiting_Importation_of_Slaves"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1811", "text": "Argentine War of Independence: A royalist fleet defeats a small flotilla of revolutionary ships in the Battle of San Nicolás on the River Plate.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>: A <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">royalist</a> fleet defeats a small <a href=\"https://wikipedia.org/wiki/Flotilla\" title=\"Flotilla\">flotilla</a> of <a href=\"https://wikipedia.org/wiki/May_Revolution\" title=\"May Revolution\">revolutionary</a> ships in the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Nicol%C3%A1s\" title=\"Battle of San Nicolás\">Battle of San Nicolás</a> on the <a href=\"https://wikipedia.org/wiki/R%C3%ADo_de_la_Plata\" title=\"Río de la Plata\">River Plate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>: A <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">royalist</a> fleet defeats a small <a href=\"https://wikipedia.org/wiki/Flotilla\" title=\"Flotilla\">flotilla</a> of <a href=\"https://wikipedia.org/wiki/May_Revolution\" title=\"May Revolution\">revolutionary</a> ships in the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Nicol%C3%A1s\" title=\"Battle of San Nicolás\">Battle of San Nicolás</a> on the <a href=\"https://wikipedia.org/wiki/R%C3%ADo_de_la_Plata\" title=\"Río de la Plata\">River Plate</a>.", "links": [{"title": "Argentine War of Independence", "link": "https://wikipedia.org/wiki/Argentine_War_of_Independence"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Flotilla", "link": "https://wikipedia.org/wiki/Flotilla"}, {"title": "May Revolution", "link": "https://wikipedia.org/wiki/May_Revolution"}, {"title": "Battle of San Nicolás", "link": "https://wikipedia.org/wiki/Battle_of_San_Nicol%C3%A1s"}, {"title": "Río de la Plata", "link": "https://wikipedia.org/wiki/R%C3%ADo_de_la_Plata"}]}, {"year": "1815", "text": "Signing of the Kandyan Convention treaty by British invaders and the leaders of the Kingdom of Kandy.", "html": "1815 - Signing of the <a href=\"https://wikipedia.org/wiki/Kandyan_Convention\" title=\"Kandyan Convention\">Kandyan Convention</a> treaty by British invaders and the leaders of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kandy\" title=\"Kingdom of Kandy\">Kingdom of Kandy</a>.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Kandyan_Convention\" title=\"Kandyan Convention\">Kandyan Convention</a> treaty by British invaders and the leaders of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kandy\" title=\"Kingdom of Kandy\">Kingdom of Kandy</a>.", "links": [{"title": "Kandyan Convention", "link": "https://wikipedia.org/wiki/Kandyan_Convention"}, {"title": "Kingdom of Kandy", "link": "https://wikipedia.org/wiki/Kingdom_of_Kandy"}]}, {"year": "1836", "text": "Texas Revolution: The Declaration of independence of the Republic of Texas from Mexico is adopted.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Texas_Declaration_of_Independence\" title=\"Texas Declaration of Independence\">Declaration of independence</a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> from Mexico is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Texas_Declaration_of_Independence\" title=\"Texas Declaration of Independence\">Declaration of independence</a> of the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a> from Mexico is adopted.", "links": [{"title": "Texas Revolution", "link": "https://wikipedia.org/wiki/Texas_Revolution"}, {"title": "Texas Declaration of Independence", "link": "https://wikipedia.org/wiki/Texas_Declaration_of_Independence"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1855", "text": "<PERSON> becomes Tsar of Russia.", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> II</a> becomes <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> of Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> II</a> becomes <a href=\"https://wikipedia.org/wiki/Tsar\" title=\"Tsar\">Tsar</a> of Russia.", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Tsar", "link": "https://wikipedia.org/wiki/Tsar"}]}, {"year": "1859", "text": "The two-day Great Slave Auction, once thought to be the largest such auction in United States history, begins.", "html": "1859 - The two-day <a href=\"https://wikipedia.org/wiki/The_Great_Slave_Auction\" class=\"mw-redirect\" title=\"The Great Slave Auction\">Great Slave Auction</a>, once thought to be the largest such auction in <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> history, begins.", "no_year_html": "The two-day <a href=\"https://wikipedia.org/wiki/The_Great_Slave_Auction\" class=\"mw-redirect\" title=\"The Great Slave Auction\">Great Slave Auction</a>, once thought to be the largest such auction in <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a> history, begins.", "links": [{"title": "The Great Slave Auction", "link": "https://wikipedia.org/wiki/The_Great_Slave_Auction"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1865", "text": "East Cape War: The Völkner Incident in New Zealand.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/East_Cape_War\" title=\"East Cape War\">East Cape War</a>: The <a href=\"https://wikipedia.org/wiki/V%C3%B6lkner_Incident\" class=\"mw-redirect\" title=\"Völkner Incident\">Völkner Incident</a> in New Zealand.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Cape_War\" title=\"East Cape War\">East Cape War</a>: The <a href=\"https://wikipedia.org/wiki/V%C3%B6<PERSON>ner_Incident\" class=\"mw-redirect\" title=\"Völkner Incident\">Völkner Incident</a> in New Zealand.", "links": [{"title": "East Cape War", "link": "https://wikipedia.org/wiki/East_Cape_War"}, {"title": "Völkner Incident", "link": "https://wikipedia.org/wiki/V%C3%B6<PERSON>ner_Incident"}]}, {"year": "1867", "text": "The U.S. Congress passes the first Reconstruction Act.", "html": "1867 - The U.S. Congress passes the first <a href=\"https://wikipedia.org/wiki/Reconstruction_Act\" class=\"mw-redirect\" title=\"Reconstruction Act\">Reconstruction Act</a>.", "no_year_html": "The U.S. Congress passes the first <a href=\"https://wikipedia.org/wiki/Reconstruction_Act\" class=\"mw-redirect\" title=\"Reconstruction Act\">Reconstruction Act</a>.", "links": [{"title": "Reconstruction Act", "link": "https://wikipedia.org/wiki/Reconstruction_Act"}]}, {"year": "1877", "text": "Just two days before inauguration, the U.S. Congress declares <PERSON> the winner of the 1876 U.S. presidential election even though <PERSON> had won the popular vote.", "html": "1877 - Just two days before inauguration, the U.S. Congress declares <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the winner of the <a href=\"https://wikipedia.org/wiki/1876_U.S._presidential_election\" class=\"mw-redirect\" title=\"1876 U.S. presidential election\">1876 U.S. presidential election</a> even though <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tilden\" title=\"<PERSON>\"><PERSON></a> had won the popular vote.", "no_year_html": "Just two days before inauguration, the U.S. Congress declares <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> the winner of the <a href=\"https://wikipedia.org/wiki/1876_U.S._presidential_election\" class=\"mw-redirect\" title=\"1876 U.S. presidential election\">1876 U.S. presidential election</a> even though <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tilden\" title=\"<PERSON>\"><PERSON></a> had won the popular vote.", "links": [{"title": "<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1876 U.S. presidential election", "link": "https://wikipedia.org/wiki/1876_U.S._presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON> narrowly escapes an assassination attempt by <PERSON><PERSON> in Windsor.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a> narrowly escapes an assassination attempt by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in Windsor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a> narrowly escapes an assassination attempt by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in Windsor.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "United States Steel Corporation is founded as a result of a merger between Carnegie Steel Company and Federal Steel Company which became the first corporation in the world with a market capital over $1 billion.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">United States Steel Corporation</a> is founded as a result of a merger between <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> and <a href=\"https://wikipedia.org/wiki/Federal_Steel_Company\" class=\"mw-redirect\" title=\"Federal Steel Company\">Federal Steel Company</a> which became the first corporation in the world with a market capital over $1 billion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">United States Steel Corporation</a> is founded as a result of a merger between <a href=\"https://wikipedia.org/wiki/Carnegie_Steel_Company\" title=\"Carnegie Steel Company\">Carnegie Steel Company</a> and <a href=\"https://wikipedia.org/wiki/Federal_Steel_Company\" class=\"mw-redirect\" title=\"Federal Steel Company\">Federal Steel Company</a> which became the first corporation in the world with a market capital over $1 billion.", "links": [{"title": "U.S. Steel", "link": "https://wikipedia.org/wiki/U.S._Steel"}, {"title": "Carnegie Steel Company", "link": "https://wikipedia.org/wiki/Carnegie_Steel_Company"}, {"title": "Federal Steel Company", "link": "https://wikipedia.org/wiki/Federal_Steel_Company"}]}, {"year": "1901", "text": "The U.S. Congress passes the Platt Amendment limiting the autonomy of Cuba, as a condition of the withdrawal of American troops.", "html": "1901 - The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Platt_Amendment\" title=\"Platt Amendment\">Platt Amendment</a> limiting the autonomy of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, as a condition of the withdrawal of American troops.", "no_year_html": "The U.S. Congress passes the <a href=\"https://wikipedia.org/wiki/Platt_Amendment\" title=\"Platt Amendment\">Platt Amendment</a> limiting the autonomy of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, as a condition of the withdrawal of American troops.", "links": [{"title": "Platt Amendment", "link": "https://wikipedia.org/wiki/Platt_Amendment"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1903", "text": "In New York City the Martha Washington Hotel opens, becoming the first hotel exclusively for women.", "html": "1903 - In New York City the <a href=\"https://wikipedia.org/wiki/Martha_Washington_Hotel\" title=\"Martha Washington Hotel\">Martha Washington Hotel</a> opens, becoming the first hotel exclusively for women.", "no_year_html": "In New York City the <a href=\"https://wikipedia.org/wiki/Martha_Washington_Hotel\" title=\"Martha Washington Hotel\">Martha Washington Hotel</a> opens, becoming the first hotel exclusively for women.", "links": [{"title": "Martha Washington Hotel", "link": "https://wikipedia.org/wiki/Martha_Washington_Hotel"}]}, {"year": "1917", "text": "The enactment of the Jones-Shafroth Act grants Puerto Ricans United States citizenship.", "html": "1917 - The enactment of the <a href=\"https://wikipedia.org/wiki/Jones%E2%80%93Shafroth_Act\" title=\"Jones-Shafroth Act\">Jones-Shafroth Act</a> grants <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Ricans</a> United States citizenship.", "no_year_html": "The enactment of the <a href=\"https://wikipedia.org/wiki/Jones%E2%80%93Shafroth_Act\" title=\"Jones-Shafroth Act\">Jones-Shafroth Act</a> grants <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Ricans</a> United States citizenship.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>roth Act", "link": "https://wikipedia.org/wiki/Jones%E2%80%93Shafroth_Act"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}]}, {"year": "1919", "text": "The first Communist International meets in Moscow.", "html": "1919 - The first <a href=\"https://wikipedia.org/wiki/Communist_International\" title=\"Communist International\">Communist International</a> meets in Moscow.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Communist_International\" title=\"Communist International\">Communist International</a> meets in Moscow.", "links": [{"title": "Communist International", "link": "https://wikipedia.org/wiki/Communist_International"}]}, {"year": "1932", "text": "Finnish president <PERSON><PERSON> <PERSON><PERSON> gives a radio speech, which four days later finally ends the Mäntsälä Rebellion and the far-right Lapua Movement that started it.", "html": "1932 - Finnish president <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON></a> gives a radio speech, which four days later finally ends the <a href=\"https://wikipedia.org/wiki/M%C3%A4nts%C3%A4l%C3%A4_Rebellion\" class=\"mw-redirect\" title=\"Mäntsälä Rebellion\">Mäntsälä Rebellion</a> and the far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a> that started it.", "no_year_html": "Finnish president <a href=\"https://wikipedia.org/wiki/P._E._Svinhufvud\" class=\"mw-redirect\" title=\"P. E. Svinhufvud\"><PERSON><PERSON> <PERSON><PERSON></a> gives a radio speech, which four days later finally ends the <a href=\"https://wikipedia.org/wiki/M%C3%A4nts%C3%A4l%C3%A4_Rebellion\" class=\"mw-redirect\" title=\"Mäntsälä Rebellion\">Mäntsälä Rebellion</a> and the far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a> that started it.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Svinhufvud"}, {"title": "Mäntsälä Rebellion", "link": "https://wikipedia.org/wiki/M%C3%A4nts%C3%A4l%C3%A4_Rebellion"}, {"title": "Lapua Movement", "link": "https://wikipedia.org/wiki/Lapua_Movement"}]}, {"year": "1933", "text": "The film King Kong premieres in Radio City Music Hall and RKO Roxy in New York City", "html": "1933 - The film <i><a href=\"https://wikipedia.org/wiki/King_Kong_(1933_film)\" title=\"King Kong (1933 film)\">King Kong</a></i> premieres in <a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a> and <a href=\"https://wikipedia.org/wiki/RKO_Roxy_Theatre\" class=\"mw-redirect\" title=\"RKO Roxy Theatre\">RKO Roxy</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>", "no_year_html": "The film <i><a href=\"https://wikipedia.org/wiki/King_Kong_(1933_film)\" title=\"King Kong (1933 film)\">King Kong</a></i> premieres in <a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a> and <a href=\"https://wikipedia.org/wiki/RKO_Roxy_Theatre\" class=\"mw-redirect\" title=\"RKO Roxy Theatre\">RKO Roxy</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>", "links": [{"title": "King Kong (1933 film)", "link": "https://wikipedia.org/wiki/King_Kong_(1933_film)"}, {"title": "Radio City Music Hall", "link": "https://wikipedia.org/wiki/Radio_City_Music_Hall"}, {"title": "RKO Roxy Theatre", "link": "https://wikipedia.org/wiki/RKO_Roxy_Theatre"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1937", "text": "The Steel Workers Organizing Committee signs a collective bargaining agreement with U.S. Steel, leading to unionization of the United States steel industry.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Steel_Workers_Organizing_Committee\" title=\"Steel Workers Organizing Committee\">Steel Workers Organizing Committee</a> signs a <a href=\"https://wikipedia.org/wiki/Collective_bargaining\" title=\"Collective bargaining\">collective bargaining</a> agreement with <a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">U.S. Steel</a>, leading to unionization of the United States <a href=\"https://wikipedia.org/wiki/Steel\" title=\"Steel\">steel</a> industry.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Steel_Workers_Organizing_Committee\" title=\"Steel Workers Organizing Committee\">Steel Workers Organizing Committee</a> signs a <a href=\"https://wikipedia.org/wiki/Collective_bargaining\" title=\"Collective bargaining\">collective bargaining</a> agreement with <a href=\"https://wikipedia.org/wiki/U.S._Steel\" title=\"U.S. Steel\">U.S. Steel</a>, leading to unionization of the United States <a href=\"https://wikipedia.org/wiki/Steel\" title=\"Steel\">steel</a> industry.", "links": [{"title": "Steel Workers Organizing Committee", "link": "https://wikipedia.org/wiki/Steel_Workers_Organizing_Committee"}, {"title": "Collective bargaining", "link": "https://wikipedia.org/wiki/Collective_bargaining"}, {"title": "U.S. Steel", "link": "https://wikipedia.org/wiki/U.S._Steel"}, {"title": "Steel", "link": "https://wikipedia.org/wiki/Steel"}]}, {"year": "1939", "text": "<PERSON> <PERSON><PERSON><PERSON> is elected <PERSON> and takes the name <PERSON>.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">Cardinal</a> <PERSON><PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/1939_papal_conclave\" title=\"1939 papal conclave\">elected</a> Pope and takes the name <a href=\"https://wikipedia.org/wiki/Pope_Pius_XII\" title=\"Pope Pius XII\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">Cardinal</a> <PERSON><PERSON><PERSON> is <a href=\"https://wikipedia.org/wiki/1939_papal_conclave\" title=\"1939 papal conclave\">elected</a> Pope and takes the name <a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_XII\" title=\"Pope Pius XII\"><PERSON></a>.", "links": [{"title": "<PERSON> (Catholic Church)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)"}, {"title": "1939 papal conclave", "link": "https://wikipedia.org/wiki/1939_papal_conclave"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "World War II: First German military units enter Bulgaria after it joins the Axis Pact.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First German military units enter Bulgaria <a href=\"https://wikipedia.org/wiki/Military_history_of_Bulgaria_during_World_War_II\" class=\"mw-redirect\" title=\"Military history of Bulgaria during World War II\">after it joins the Axis Pact</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First German military units enter Bulgaria <a href=\"https://wikipedia.org/wiki/Military_history_of_Bulgaria_during_World_War_II\" class=\"mw-redirect\" title=\"Military history of Bulgaria during World War II\">after it joins the Axis Pact</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Military history of Bulgaria during World War II", "link": "https://wikipedia.org/wiki/Military_history_of_Bulgaria_during_World_War_II"}]}, {"year": "1943", "text": "World War II: During the Battle of the Bismarck Sea Allied aircraft defeated a Japanese attempt to ship troops to New Guinea.", "html": "1943 - World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bismarck_Sea\" title=\"Battle of the Bismarck Sea\">Battle of the Bismarck Sea</a> Allied aircraft defeated a Japanese attempt to ship troops to New Guinea.", "no_year_html": "World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bismarck_Sea\" title=\"Battle of the Bismarck Sea\">Battle of the Bismarck Sea</a> Allied aircraft defeated a Japanese attempt to ship troops to New Guinea.", "links": [{"title": "Battle of the Bismarck Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Bismarck_Sea"}]}, {"year": "1949", "text": "Captain <PERSON> lands his B-50 Superfortress Lucky Lady II in Fort Worth, Texas, after completing the first non-stop around-the-world airplane flight in 94 hours and one minute.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Captain_(U.S._Air_Force)\" class=\"mw-redirect\" title=\"Captain (U.S. Air Force)\">Captain</a> <PERSON> lands his <a href=\"https://wikipedia.org/wiki/B-50_Superfortress\" class=\"mw-redirect\" title=\"B-50 Superfortress\">B-50 Superfortress</a> <i><a href=\"https://wikipedia.org/wiki/Lucky_Lady_II\" title=\"Lucky Lady II\">Lucky Lady II</a></i> in <a href=\"https://wikipedia.org/wiki/Fort_Worth,_Texas\" title=\"Fort Worth, Texas\">Fort Worth, Texas</a>, after completing the first non-stop around-the-world airplane flight in 94 hours and one minute.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Captain_(U.S._Air_Force)\" class=\"mw-redirect\" title=\"Captain (U.S. Air Force)\">Captain</a> <PERSON> lands his <a href=\"https://wikipedia.org/wiki/B-50_Superfortress\" class=\"mw-redirect\" title=\"B-50 Superfortress\">B-50 Superfortress</a> <i><a href=\"https://wikipedia.org/wiki/Lucky_Lady_II\" title=\"Lucky Lady II\">Lucky Lady II</a></i> in <a href=\"https://wikipedia.org/wiki/Fort_Worth,_Texas\" title=\"Fort Worth, Texas\">Fort Worth, Texas</a>, after completing the first non-stop around-the-world airplane flight in 94 hours and one minute.", "links": [{"title": "Captain (U.S. Air Force)", "link": "https://wikipedia.org/wiki/Captain_(U.S._Air_Force)"}, {"title": "B-50 Superfortress", "link": "https://wikipedia.org/wiki/B-50_Superfortress"}, {"title": "Lucky Lady II", "link": "https://wikipedia.org/wiki/<PERSON>_Lady_II"}, {"title": "Fort Worth, Texas", "link": "https://wikipedia.org/wiki/Fort_Worth,_Texas"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, king of Cambodia, abdicates the throne in favor of his father, <PERSON><PERSON><PERSON>.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, abdicates the throne in favor of his father, <a href=\"https://wikipedia.org/wiki/Norodom_Suramarit\" title=\"Norodom Surama<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a>, abdicates the throne in favor of his father, <a href=\"https://wikipedia.org/wiki/Norodom_Suramarit\" title=\"Norodom <PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_Suramarit"}]}, {"year": "1962", "text": "In Burma, the army led by General <PERSON><PERSON> seizes power in a coup d'état.", "html": "1962 - In <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>, the army led by General <a href=\"https://wikipedia.org/wiki/Ne_Win\" title=\"Ne Win\">Ne Win</a> seizes power in a <i><a href=\"https://wikipedia.org/wiki/1962_Burmese_coup_d%27%C3%A9tat\" title=\"1962 Burmese coup d'état\">coup d'état</a></i>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma</a>, the army led by General <a href=\"https://wikipedia.org/wiki/Ne_Win\" title=\"Ne Win\">Ne Win</a> seizes power in a <i><a href=\"https://wikipedia.org/wiki/1962_Burmese_coup_d%27%C3%A9tat\" title=\"1962 Burmese coup d'état\">coup d'état</a></i>.", "links": [{"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "<PERSON>e Win", "link": "https://wikipedia.org/wiki/Ne_Win"}, {"title": "1962 Burmese coup d'état", "link": "https://wikipedia.org/wiki/1962_Burmese_coup_d%27%C3%A9tat"}]}, {"year": "1962", "text": "<PERSON><PERSON> <PERSON> sets the single-game scoring record in the National Basketball Association by scoring 100 points.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"Wilt <PERSON>\">W<PERSON> <PERSON></a> sets the single-game scoring record in the <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">National Basketball Association</a> by <a href=\"https://wikipedia.org/wiki/Wilt_Chamberlain%27s_100-point_game\" title=\"W<PERSON> <PERSON>'s 100-point game\">scoring 100 points</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"W<PERSON> <PERSON>\">W<PERSON> <PERSON></a> sets the single-game scoring record in the <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">National Basketball Association</a> by <a href=\"https://wikipedia.org/wiki/Wilt_Chamberlain%27s_100-point_game\" title=\"W<PERSON> <PERSON>'s 100-point game\">scoring 100 points</a>.", "links": [{"title": "W<PERSON>", "link": "https://wikipedia.org/wiki/Wilt_<PERSON>"}, {"title": "National Basketball Association", "link": "https://wikipedia.org/wiki/National_Basketball_Association"}, {"title": "W<PERSON> Chamberlain's 100-point game", "link": "https://wikipedia.org/wiki/Wilt_<PERSON>%27s_100-point_game"}]}, {"year": "1965", "text": "The US and Republic of Vietnam Air Force begin Operation Rolling Thunder, a sustained bombing campaign against North Vietnam.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">US</a> and <a href=\"https://wikipedia.org/wiki/Republic_of_Vietnam_Air_Force\" class=\"mw-redirect\" title=\"Republic of Vietnam Air Force\">Republic of Vietnam Air Force</a> begin <a href=\"https://wikipedia.org/wiki/Operation_Rolling_Thunder\" title=\"Operation Rolling Thunder\">Operation Rolling Thunder</a>, a sustained bombing campaign against <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">US</a> and <a href=\"https://wikipedia.org/wiki/Republic_of_Vietnam_Air_Force\" class=\"mw-redirect\" title=\"Republic of Vietnam Air Force\">Republic of Vietnam Air Force</a> begin <a href=\"https://wikipedia.org/wiki/Operation_Rolling_Thunder\" title=\"Operation Rolling Thunder\">Operation Rolling Thunder</a>, a sustained bombing campaign against <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Republic of Vietnam Air Force", "link": "https://wikipedia.org/wiki/Republic_of_Vietnam_Air_Force"}, {"title": "Operation Rolling Thunder", "link": "https://wikipedia.org/wiki/Operation_Rolling_Thunder"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1968", "text": "Baggeridge Colliery closes marking the end of over 300 years of coal mining in the Black Country.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Baggeridge_Colliery\" title=\"Baggeridge Colliery\">Baggeridge Colliery</a> closes marking the end of over 300 years of <a href=\"https://wikipedia.org/wiki/Coal_mining\" title=\"Coal mining\">coal mining</a> in the <a href=\"https://wikipedia.org/wiki/Black_Country\" title=\"Black Country\">Black Country</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baggeridge_Colliery\" title=\"Baggeridge Colliery\">Baggeridge Colliery</a> closes marking the end of over 300 years of <a href=\"https://wikipedia.org/wiki/Coal_mining\" title=\"Coal mining\">coal mining</a> in the <a href=\"https://wikipedia.org/wiki/Black_Country\" title=\"Black Country\">Black Country</a>.", "links": [{"title": "Baggeridge Colliery", "link": "https://wikipedia.org/wiki/Baggeridge_Colliery"}, {"title": "Coal mining", "link": "https://wikipedia.org/wiki/Coal_mining"}, {"title": "Black Country", "link": "https://wikipedia.org/wiki/Black_Country"}]}, {"year": "1969", "text": "In Toulouse, France, the first test flight of the Anglo-French Concorde is conducted.", "html": "1969 - In <a href=\"https://wikipedia.org/wiki/Toulouse\" title=\"Toulouse\">Toulouse</a>, France, the first test flight of the Anglo-French <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> is conducted.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Toulouse\" title=\"Toulouse\">Toulouse</a>, France, the first test flight of the Anglo-French <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> is conducted.", "links": [{"title": "Toulouse", "link": "https://wikipedia.org/wiki/Toulouse"}, {"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}]}, {"year": "1970", "text": "Rhodesia declares itself a republic, breaking its last links with the British crown.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> declares itself a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, breaking its last links with the British crown.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> declares itself a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>, breaking its last links with the British crown.", "links": [{"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}]}, {"year": "1972", "text": "The Pioneer 10 space probe is launched from Cape Canaveral, Florida with a mission to explore the outer planets.", "html": "1972 - The <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> space probe is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Canaveral</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> with a mission to explore the outer <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planets</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a> space probe is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Canaveral</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> with a mission to explore the outer <a href=\"https://wikipedia.org/wiki/Planet\" title=\"Planet\">planets</a>.", "links": [{"title": "Pioneer 10", "link": "https://wikipedia.org/wiki/Pioneer_10"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Planet", "link": "https://wikipedia.org/wiki/Planet"}]}, {"year": "1977", "text": "Libya becomes the Socialist People's Libyan Arab <PERSON> as the General People's Congress adopted the \"Declaration on the Establishment of the Authority of the People\".", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> becomes the <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"History of Libya under <PERSON><PERSON><PERSON>\">Socialist People's Libyan Arab <PERSON></a> as the <a href=\"https://wikipedia.org/wiki/General_People%27s_Congress_(Libya)\" title=\"General People's Congress (Libya)\">General People's Congress</a> adopted the \"<a href=\"https://wikipedia.org/wiki/Declaration_on_the_Establishment_of_the_Authority_of_the_People\" title=\"Declaration on the Establishment of the Authority of the People\">Declaration on the Establishment of the Authority of the People</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> becomes the <a href=\"https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"History of Libya under <PERSON><PERSON><PERSON>\">Socialist People's Libyan Arab <PERSON></a> as the <a href=\"https://wikipedia.org/wiki/General_People%27s_Congress_(Libya)\" title=\"General People's Congress (Libya)\">General People's Congress</a> adopted the \"<a href=\"https://wikipedia.org/wiki/Declaration_on_the_Establishment_of_the_Authority_of_the_People\" title=\"Declaration on the Establishment of the Authority of the People\">Declaration on the Establishment of the Authority of the People</a>\".", "links": [{"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "History of Libya under <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/History_of_Libya_under_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "General People's Congress (Libya)", "link": "https://wikipedia.org/wiki/General_People%27s_Congress_(Libya)"}, {"title": "Declaration on the Establishment of the Authority of the People", "link": "https://wikipedia.org/wiki/Declaration_on_the_Establishment_of_the_Authority_of_the_People"}]}, {"year": "1978", "text": "Czech <PERSON><PERSON><PERSON><PERSON> becomes the first non-Russian or non-American to go into space, when he is launched aboard Soyuz 28.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czech</a> <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Remek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the first non-Russian or non-American to go into <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>, when he is launched aboard <a href=\"https://wikipedia.org/wiki/Soyuz_28\" title=\"Soyuz 28\">Soyuz 28</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czech</a> <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Remek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> becomes the first non-Russian or non-American to go into <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a>, when he is launched aboard <a href=\"https://wikipedia.org/wiki/Soyuz_28\" title=\"Soyuz 28\">Soyuz 28</a>.", "links": [{"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Remek"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}, {"title": "Soyuz 28", "link": "https://wikipedia.org/wiki/Soyuz_28"}]}, {"year": "1978", "text": "The late iconic actor <PERSON>'s coffin is stolen from his grave in Switzerland.", "html": "1978 - The late iconic actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s coffin is stolen from his grave in Switzerland.", "no_year_html": "The late iconic actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s coffin is stolen from his grave in Switzerland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "Compact discs and players are released for the first time in the United States and other markets. They had previously been available only in Japan.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">Compact discs</a> and players are released for the first time in the United States and other markets. They had previously been available only in Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Compact_disc\" title=\"Compact disc\">Compact discs</a> and players are released for the first time in the United States and other markets. They had previously been available only in Japan.", "links": [{"title": "Compact disc", "link": "https://wikipedia.org/wiki/Compact_disc"}]}, {"year": "1986", "text": "Aeroflot Flight F-77 crashes near Bugulma Airport, killing all 38 people aboard.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_F-77\" title=\"Aeroflot Flight F-77\">Aeroflot Flight F-77</a> crashes near <a href=\"https://wikipedia.org/wiki/Bugulma_Airport\" title=\"Bugulma Airport\">Bugulma Airport</a>, killing all 38 people aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_F-77\" title=\"Aeroflot Flight F-77\">Aeroflot Flight F-77</a> crashes near <a href=\"https://wikipedia.org/wiki/Bugulma_Airport\" title=\"Bugulma Airport\">Bugulma Airport</a>, killing all 38 people aboard.", "links": [{"title": "Aeroflot Flight F-77", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_F-77"}, {"title": "Bugulma Airport", "link": "https://wikipedia.org/wiki/Bugulma_Airport"}]}, {"year": "1989", "text": "Twelve European Community nations agree to ban the production of all chlorofluorocarbons (CFCs) by the end of the century.", "html": "1989 - Twelve <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> nations agree to ban the production of all <a href=\"https://wikipedia.org/wiki/Chlorofluorocarbon\" title=\"Chlorofluorocarbon\">chlorofluorocarbons</a> (CFCs) by the end of the century.", "no_year_html": "Twelve <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> nations agree to ban the production of all <a href=\"https://wikipedia.org/wiki/Chlorofluorocarbon\" title=\"Chlorofluorocarbon\">chlorofluorocarbons</a> (CFCs) by the end of the century.", "links": [{"title": "European Community", "link": "https://wikipedia.org/wiki/European_Community"}, {"title": "Chlorofluorocarbon", "link": "https://wikipedia.org/wiki/Chlorofluorocarbon"}]}, {"year": "1990", "text": "<PERSON> is elected deputy president of the African National Congress.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected deputy president of the <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected deputy president of the <a href=\"https://wikipedia.org/wiki/African_National_Congress\" title=\"African National Congress\">African National Congress</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "African National Congress", "link": "https://wikipedia.org/wiki/African_National_Congress"}]}, {"year": "1991", "text": "Establishment of Kuwait Democratic Forum, center-left political organization in Kuwait.", "html": "1991 - Establishment of <a href=\"https://wikipedia.org/wiki/Kuwait_Democratic_Forum\" title=\"Kuwait Democratic Forum\">Kuwait Democratic Forum</a>, center-left political organization in <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "no_year_html": "Establishment of <a href=\"https://wikipedia.org/wiki/Kuwait_Democratic_Forum\" title=\"Kuwait Democratic Forum\">Kuwait Democratic Forum</a>, center-left political organization in <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "links": [{"title": "Kuwait Democratic Forum", "link": "https://wikipedia.org/wiki/Kuwait_Democratic_Forum"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}]}, {"year": "1991", "text": "Battle at Rumaila oil field brings an end to the 1991 Gulf War.", "html": "1991 - Battle at <a href=\"https://wikipedia.org/wiki/Rumaila_oil_field\" title=\"Rumaila oil field\">Rumaila oil field</a> brings an end to the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">1991 Gulf War</a>.", "no_year_html": "Battle at <a href=\"https://wikipedia.org/wiki/Rumaila_oil_field\" title=\"Rumaila oil field\">Rumaila oil field</a> brings an end to the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">1991 Gulf War</a>.", "links": [{"title": "Rumaila oil field", "link": "https://wikipedia.org/wiki/Rumaila_oil_field"}, {"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}]}, {"year": "1992", "text": "Start of the war in Transnistria.", "html": "1992 - Start of the <a href=\"https://wikipedia.org/wiki/Transnistria_War\" title=\"Transnistria War\">war in Transnistria</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/Transnistria_War\" title=\"Transnistria War\">war in Transnistria</a>.", "links": [{"title": "Transnistria War", "link": "https://wikipedia.org/wiki/Transnistria_War"}]}, {"year": "1992", "text": "Armenia, Azerbaijan, Kazakhstan, Kyrgyzstan, Moldova, San Marino, Tajikistan, Turkmenistan and Uzbekistan, all of which (except San Marino) were former Soviet republics, join the United Nations.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, <a href=\"https://wikipedia.org/wiki/Kyrgyzstan\" title=\"Kyrgyzstan\">Kyrgyzstan</a>, <a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a>, <a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajikistan</a>, <a href=\"https://wikipedia.org/wiki/Turkmenistan\" title=\"Turkmenistan\">Turkmenistan</a> and <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a>, all of which (except San Marino) were former <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Soviet republics</a>, join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, <a href=\"https://wikipedia.org/wiki/Kyrgyzstan\" title=\"Kyrgyzstan\">Kyrgyzstan</a>, <a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a>, <a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, <a href=\"https://wikipedia.org/wiki/Tajikistan\" title=\"Tajikistan\">Tajikistan</a>, <a href=\"https://wikipedia.org/wiki/Turkmenistan\" title=\"Turkmenistan\">Turkmenistan</a> and <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a>, all of which (except San Marino) were former <a href=\"https://wikipedia.org/wiki/Republics_of_the_Soviet_Union\" title=\"Republics of the Soviet Union\">Soviet republics</a>, join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}, {"title": "Kyrgyzstan", "link": "https://wikipedia.org/wiki/Kyrgyzstan"}, {"title": "Moldova", "link": "https://wikipedia.org/wiki/Moldova"}, {"title": "San Marino", "link": "https://wikipedia.org/wiki/San_Marino"}, {"title": "Tajikistan", "link": "https://wikipedia.org/wiki/Tajikistan"}, {"title": "Turkmenistan", "link": "https://wikipedia.org/wiki/Turkmenistan"}, {"title": "Uzbekistan", "link": "https://wikipedia.org/wiki/Uzbekistan"}, {"title": "Republics of the Soviet Union", "link": "https://wikipedia.org/wiki/Republics_of_the_Soviet_Union"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1995", "text": "Researchers at Fermilab announce the discovery of the top quark.", "html": "1995 - Researchers at <a href=\"https://wikipedia.org/wiki/Fermilab\" title=\"Fermilab\">Fermilab</a> announce the discovery of the <a href=\"https://wikipedia.org/wiki/Top_quark\" title=\"Top quark\">top quark</a>.", "no_year_html": "Researchers at <a href=\"https://wikipedia.org/wiki/Fermilab\" title=\"Fermilab\">Fermilab</a> announce the discovery of the <a href=\"https://wikipedia.org/wiki/Top_quark\" title=\"Top quark\">top quark</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fermilab"}, {"title": "Top quark", "link": "https://wikipedia.org/wiki/Top_quark"}]}, {"year": "1995", "text": "Space Shuttle Endeavour launches from the Kennedy Space Center on STS-67, carrying the ASTRO-2 spacelab observatory.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on <a href=\"https://wikipedia.org/wiki/STS-67\" title=\"STS-67\">STS-67</a>, carrying the ASTRO-2 <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">spacelab</a> observatory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches from the <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a> on <a href=\"https://wikipedia.org/wiki/STS-67\" title=\"STS-67\">STS-67</a>, carrying the ASTRO-2 <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">spacelab</a> observatory.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}, {"title": "STS-67", "link": "https://wikipedia.org/wiki/STS-67"}, {"title": "Spacelab", "link": "https://wikipedia.org/wiki/Spacelab"}]}, {"year": "1998", "text": "Data sent from the Galileo spacecraft indicates that Jupiter's moon Europa has a liquid ocean under a thick crust of ice.", "html": "1998 - Data sent from the <a href=\"https://wikipedia.org/wiki/Galileo_spacecraft\" class=\"mw-redirect\" title=\"Galileo spacecraft\">Galileo spacecraft</a> indicates that <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>'s moon <a href=\"https://wikipedia.org/wiki/Europa_(moon)\" title=\"Europa (moon)\">Europa</a> has a liquid ocean under a thick crust of ice.", "no_year_html": "Data sent from the <a href=\"https://wikipedia.org/wiki/Galileo_spacecraft\" class=\"mw-redirect\" title=\"Galileo spacecraft\">Galileo spacecraft</a> indicates that <a href=\"https://wikipedia.org/wiki/Jupiter\" title=\"Jupiter\">Jupiter</a>'s moon <a href=\"https://wikipedia.org/wiki/Europa_(moon)\" title=\"Europa (moon)\">Europa</a> has a liquid ocean under a thick crust of ice.", "links": [{"title": "Galileo spacecraft", "link": "https://wikipedia.org/wiki/Galileo_spacecraft"}, {"title": "Jupiter", "link": "https://wikipedia.org/wiki/Jupiter"}, {"title": "Europa (moon)", "link": "https://wikipedia.org/wiki/Europa_(moon)"}]}, {"year": "2002", "text": "U.S. invasion of Afghanistan: Operation Anaconda begins, (ending on March 19 after killing 500 Taliban and al-Qaeda fighters, with 11 Western troop fatalities).", "html": "2002 - <a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">U.S. invasion of Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anaconda\" title=\"Operation Anaconda\">Operation Anaconda</a> begins, (ending on <a href=\"https://wikipedia.org/wiki/March_19\" title=\"March 19\">March 19</a> after killing 500 <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> and <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> fighters, with 11 Western troop fatalities).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)\" title=\"War in Afghanistan (2001-2021)\">U.S. invasion of Afghanistan</a>: <a href=\"https://wikipedia.org/wiki/Operation_Anaconda\" title=\"Operation Anaconda\">Operation Anaconda</a> begins, (ending on <a href=\"https://wikipedia.org/wiki/March_19\" title=\"March 19\">March 19</a> after killing 500 <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> and <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> fighters, with 11 Western troop fatalities).", "links": [{"title": "War in Afghanistan (2001-2021)", "link": "https://wikipedia.org/wiki/War_in_Afghanistan_(2001%E2%80%932021)"}, {"title": "Operation Anaconda", "link": "https://wikipedia.org/wiki/Operation_Anaconda"}, {"title": "March 19", "link": "https://wikipedia.org/wiki/March_19"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "2004", "text": "War in Iraq: Al-Qaeda carries out the Ashoura Massacre in Iraq, killing 170 and wounding over 500.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">War in Iraq</a>: <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a> carries out the <a href=\"https://wikipedia.org/wiki/Ashoura_Massacre\" class=\"mw-redirect\" title=\"Ashoura Massacre\">Ashoura Massacre</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, killing 170 and wounding over 500.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">War in Iraq</a>: <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a> carries out the <a href=\"https://wikipedia.org/wiki/Ashoura_Massacre\" class=\"mw-redirect\" title=\"Ashoura Massacre\">Ashoura Massacre</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, killing 170 and wounding over 500.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}, {"title": "Ashoura Massacre", "link": "https://wikipedia.org/wiki/Ashoura_Massacre"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2012", "text": "A tornado outbreak occurs over a large section of the Southern United States and into the Ohio Valley region, resulting in 40 tornado-related fatalities.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_March_2%E2%80%933,_2012\" title=\"Tornado outbreak of March 2-3, 2012\">tornado outbreak</a> occurs over a large section of the Southern United States and into the <a href=\"https://wikipedia.org/wiki/Ohio_Valley\" class=\"mw-redirect\" title=\"Ohio Valley\">Ohio Valley</a> region, resulting in 40 tornado-related fatalities.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_March_2%E2%80%933,_2012\" title=\"Tornado outbreak of March 2-3, 2012\">tornado outbreak</a> occurs over a large section of the Southern United States and into the <a href=\"https://wikipedia.org/wiki/Ohio_Valley\" class=\"mw-redirect\" title=\"Ohio Valley\">Ohio Valley</a> region, resulting in 40 tornado-related fatalities.", "links": [{"title": "Tornado outbreak of March 2-3, 2012", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_March_2%E2%80%933,_2012"}, {"title": "Ohio Valley", "link": "https://wikipedia.org/wiki/Ohio_Valley"}]}, {"year": "2017", "text": "The elements Moscovium, Tennessine, and Oganesson are officially added to the periodic table at a conference in Moscow, Russia.", "html": "2017 - The elements <a href=\"https://wikipedia.org/wiki/Moscovium\" title=\"Moscovium\">Moscovium</a>, <a href=\"https://wikipedia.org/wiki/Tennessine\" title=\"Tennessine\">Tennessine</a>, and <a href=\"https://wikipedia.org/wiki/Oganesson\" title=\"Oganesson\">Ogan<PERSON><PERSON></a> are officially added to the <a href=\"https://wikipedia.org/wiki/Periodic_table\" title=\"Periodic table\">periodic table</a> at a conference in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow, Russia</a>.", "no_year_html": "The elements <a href=\"https://wikipedia.org/wiki/Moscovium\" title=\"Moscovium\">Moscovium</a>, <a href=\"https://wikipedia.org/wiki/Tennessine\" title=\"Tennessine\">Tennessine</a>, and <a href=\"https://wikipedia.org/wiki/Oganesson\" title=\"Oganesson\">O<PERSON><PERSON><PERSON></a> are officially added to the <a href=\"https://wikipedia.org/wiki/Periodic_table\" title=\"Periodic table\">periodic table</a> at a conference in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow, Russia</a>.", "links": [{"title": "Moscovium", "link": "https://wikipedia.org/wiki/Moscovium"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ine"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Periodic table", "link": "https://wikipedia.org/wiki/Periodic_table"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "2022", "text": "Russian forces capture the city of Kherson during the Russian invasion of Ukraine, which subsequently began the start of the Russian occupation and military-civilian administration in Kherson. Kherson is the only regional capital in Ukraine that Russia captured.", "html": "2022 - Russian forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kherson\" title=\"Battle of Kherson\">capture the city of Kherson</a> during the <a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Ukraine\" title=\"Russian invasion of Ukraine\">Russian invasion of Ukraine</a>, which subsequently began the start of the <a href=\"https://wikipedia.org/wiki/Russian_occupation_of_Kherson_Oblast\" title=\"Russian occupation of Kherson Oblast\">Russian occupation and military-civilian administration</a> in <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a>. Kherson is the only regional capital in Ukraine that Russia captured.", "no_year_html": "Russian forces <a href=\"https://wikipedia.org/wiki/Battle_of_Kherson\" title=\"Battle of Kherson\">capture the city of Kherson</a> during the <a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Ukraine\" title=\"Russian invasion of Ukraine\">Russian invasion of Ukraine</a>, which subsequently began the start of the <a href=\"https://wikipedia.org/wiki/Russian_occupation_of_Kherson_Oblast\" title=\"Russian occupation of Kherson Oblast\">Russian occupation and military-civilian administration</a> in <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a>. Kherson is the only regional capital in Ukraine that Russia captured.", "links": [{"title": "Battle of Kherson", "link": "https://wikipedia.org/wiki/Battle_of_Kherson"}, {"title": "Russian invasion of Ukraine", "link": "https://wikipedia.org/wiki/Russian_invasion_of_Ukraine"}, {"title": "Russian occupation of Kherson Oblast", "link": "https://wikipedia.org/wiki/Russian_occupation_of_Kherson_Oblast"}, {"title": "Kherson", "link": "https://wikipedia.org/wiki/Kherson"}]}], "Births": [{"year": "480", "text": "<PERSON> of Nursia, Italian Christian saint (d. 543 or 547)", "html": "480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nursia\" title=\"<PERSON> of Nursia\"><PERSON> of Nursia</a>, Italian Christian saint (d. 543 or 547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nursia\" title=\"<PERSON> of Nursia\"><PERSON> of Nursia</a>, Italian Christian saint (d. 543 or 547)", "links": [{"title": "Benedict of Nursia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nursia"}]}, {"year": "1316", "text": "<PERSON> of Scotland (d. 1390)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1390)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> II of Scotland\"><PERSON> of Scotland</a> (d. 1390)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1409", "text": "<PERSON>, Duke of Alençon (d. 1476)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Alençon\"><PERSON>, Duke of Alençon</a> (d. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Alen%C3%A7on\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Alençon\"><PERSON>, Duke of Alençon</a> (d. 1476)", "links": [{"title": "<PERSON>, Duke of Alençon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_Alen%C3%A7on"}]}, {"year": "1432", "text": "Countess <PERSON><PERSON> of Mosbach, countess consort of Hanau (d. 1457)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Mosbach\" title=\"Countess <PERSON><PERSON> of Mosbach\">Countess <PERSON><PERSON> of Mosbach</a>, countess consort of Hanau (d. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Mosbach\" title=\"Countess <PERSON><PERSON> of Mosbach\">Countess <PERSON><PERSON> of Mosbach</a>, countess consort of Hanau (d. 1457)", "links": [{"title": "Countess <PERSON><PERSON> of Mosbach", "link": "https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_<PERSON>_Mosbach"}]}, {"year": "1453", "text": "<PERSON>, German doctor, astronomer and astrologer (d. 1512)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German doctor, astronomer and astrologer (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German doctor, astronomer and astrologer (d. 1512)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1459", "text": "<PERSON> (d. 1523)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Pope <PERSON></a> (d. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Adrian <PERSON>\">Pope <PERSON></a> (d. 1523)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1481", "text": "<PERSON>, German knight (d. 1523)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sickingen\"><PERSON></a>, German knight (d. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Sickingen\"><PERSON></a>, German knight (d. 1523)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON>, English diplomat and scholar, founded the Bodleian Library (d. 1613)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat and scholar, founded the <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> (d. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat and scholar, founded the <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> (d. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bodleian Library", "link": "https://wikipedia.org/wiki/Bodleian_Library"}]}, {"year": "1577", "text": "<PERSON>, English traveller, colonist and poet (d. 1644)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English traveller, colonist and poet (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English traveller, colonist and poet (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON><PERSON><PERSON>, Governor-General of the Dutch East Indies (d. 1684)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Governor-General of the Dutch East Indies (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Governor-General of the Dutch East Indies (d. 1684)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1651", "text": "<PERSON>, Maltese architect, engineer and poet (d. 1730)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect, engineer and poet (d. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese architect, engineer and poet (d. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, 1st Earl of Mansfield, Scottish lawyer, judge, and politician, Chancellor of the Exchequer (d. 1793)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Mansfield\" title=\"<PERSON>, 1st Earl of Mansfield\"><PERSON>, 1st Earl of Mansfield</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of Mansfield\"><PERSON>, 1st Earl of Mansfield</a>, Scottish lawyer, judge, and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1793)", "links": [{"title": "<PERSON>, 1st Earl of Mansfield", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Mansfield"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1740", "text": "<PERSON>, English naval painter (d. 1821)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naval painter (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English naval painter (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, French journalist and politician (d. 1794)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 6th Governor of New York (d. 1828)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1770", "text": "<PERSON><PERSON><PERSON>, French general (d. 1826)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, American physician and politician, 15th United States Secretary of War (d. 1851)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 15th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1793", "text": "<PERSON>, American soldier and politician, 1st President of the Republic of Texas (d. 1863)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Texas\" title=\"President of the Republic of Texas\">President of the Republic of Texas</a> (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Houston\" title=\"Sam Houston\"><PERSON></a>, American soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Texas\" title=\"President of the Republic of Texas\">President of the Republic of Texas</a> (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Houston"}, {"title": "President of the Republic of Texas", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Texas"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON>, Russian-Italian poet and philosopher (d. 1844)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Italian poet and philosopher (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Italian poet and philosopher (d. 1844)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON> (d. 1903)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American lawyer and politician, 26th Governor of Massachusetts (d. 1882)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, Hungarian journalist and poet (d. 1882)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Arany\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and poet (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Arany\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and poet (d. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Arany"}]}, {"year": "1820", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch writer (d. 1887)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Multatu<PERSON>\" title=\"<PERSON>ltatu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch writer (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Multatu<PERSON>\" title=\"<PERSON>lta<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch writer (d. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Multatuli"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON>, Czech pianist and composer (d. 1884)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Smetana\" title=\"Bed<PERSON>ich Smetana\"><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Smetana\" title=\"Bed<PERSON>ich Smetana\"><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bed%C5%99ich_Smetana"}]}, {"year": "1829", "text": "<PERSON>, German-American general, lawyer, and politician, 13th United States Secretary of the Interior (d. 1906)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American general, lawyer, and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American general, lawyer, and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1836", "text": "<PERSON>, American lawyer and judge (d. 1913)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Danish brewer, art collector, and philanthropist (d. 1914)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish brewer, art collector, and philanthropist (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish brewer, art collector, and philanthropist (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, French soprano (d. 1926)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American commander, lawyer, and businessman (d. 1930)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and businessman (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and businessman (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Ukrainian-American author and playwright (d. 1916)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Sholem_Aleichem\" title=\"Sholem Aleichem\"><PERSON><PERSON><PERSON> Aleichem</a>, Ukrainian-American author and playwright (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sholem_Aleichem\" title=\"Sholem Aleichem\">Shole<PERSON> Aleichem</a>, Ukrainian-American author and playwright (d. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sholem_Aleichem"}]}, {"year": "1860", "text": "<PERSON><PERSON>, American activist and politician (d. 1961)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and politician (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and politician (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American lawyer, author, and poet (d. 1933)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and poet (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and poet (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, French military officer and aviator (d. 1914)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, French military officer and aviator (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, French military officer and aviator (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julien_F%C3%A9lix"}]}, {"year": "1876", "text": "<PERSON>, American businessman and baseball executive (d. 1947)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and baseball executive (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and baseball executive (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON> (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, French aviator (d. 1911)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Vallon"}]}, {"year": "1886", "text": "<PERSON>, American animator and director (d. 1962)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American animator and director (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American animator and director (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_O%27Brien"}]}, {"year": "1886", "text": "<PERSON>, German logician and philosopher (d. 1942)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German logician and philosopher (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German logician and philosopher (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German-American pianist and composer (d. 1950)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist and composer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist and composer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, German mathematician and philosopher (d. 1984)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and philosopher (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American baseball player and spy (d. 1972)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and spy (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and spy (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American physicist and academic (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American children's book writer, poet, and illustrator (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Dr<PERSON>_<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON></a>, American children's book writer, poet, and illustrator (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, American children's book writer, poet, and illustrator (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American composer and songwriter (d. 1964)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English poet and critic (d. 1985)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German engineer (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American baseball player, manager, and sportscaster (d. 1958)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American pianist, composer, and painter (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and painter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and painter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Dutch television host and author (d. 1971)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host and author (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch television host and author (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American baseball player (d. 1958)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cooper\"><PERSON><PERSON></a>, American baseball player (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cooper\"><PERSON><PERSON></a>, American baseball player (d. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor and film director (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and film director (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and film director (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Australian public servant and diplomat, Australian High Commissioner to Ceylon (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_Sri_Lanka\" class=\"mw-redirect\" title=\"List of Australian High Commissioners to Sri Lanka\">Australian High Commissioner to Ceylon</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_Sri_Lanka\" class=\"mw-redirect\" title=\"List of Australian High Commissioners to Sri Lanka\">Australian High Commissioner to Ceylon</a> (d. 2010)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>(diplomat)"}, {"title": "List of Australian High Commissioners to Sri Lanka", "link": "https://wikipedia.org/wiki/List_of_Australian_High_Commissioners_to_Sri_Lanka"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Cuban-American actor, singer, and producer (d. 1986)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American actor, singer, and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American actor, singer, and producer (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Desi_A<PERSON>z"}]}, {"year": "1917", "text": "<PERSON>, American author and screenwriter (d. 1967)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and coach (d. 1976)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor, singer, and playwright (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Russian-American ballerina and actress (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina and actress (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and coach (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and coach (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazimierz_G%C3%B3rski"}]}, {"year": "1921", "text": "<PERSON>, Austrian-American photographer and journalist (d. 1986)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American photographer and journalist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American photographer and journalist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, American saxophonist (d. 1986)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Lockjaw%22_<PERSON>\" title='<PERSON> \"<PERSON>ja<PERSON>\" <PERSON>'><PERSON> \"<PERSON>ja<PERSON>\" <PERSON></a>, American saxophonist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Lockjaw%22_<PERSON>\" title='<PERSON> \"<PERSON>ja<PERSON>\" <PERSON>'><PERSON> \"<PERSON>ja<PERSON>\" <PERSON></a>, American saxophonist (d. 1986)", "links": [{"title": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Lockjaw%22_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American computer programmer (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English cardinal (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier and politician (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American basketball player and coach (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American baseball player (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Abrams\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Abrams\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Abrams"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Greek philologist, author, and critic (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philologist, author, and critic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek philologist, author, and critic (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>olidis"}]}, {"year": "1926", "text": "<PERSON>, Ivorian cardinal (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian cardinal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Ivorian cardinal (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_Agr%C3%A9"}]}, {"year": "1926", "text": "<PERSON>, American economist and historian (d. 1995)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and historian (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French cyclist and economist (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist and economist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist and economist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor and singer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Spanish actress (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American journalist and author (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Russian lawyer and politician, the 8th and final leader of the Soviet Union, Nobel Prize laureate (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician, the 8th and final <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">leader</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian lawyer and politician, the 8th and final <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">leader</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of leaders of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1932", "text": "<PERSON>, Swedish journalist and translator (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Gun_H%C3%A4gglund\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and translator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gun_H%C3%A4gg<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish journalist and translator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gun_H%C3%A4gglund"}]}, {"year": "1933", "text": "<PERSON>, American illustrator (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American illustrator (d. 2012)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American football player (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American singer-songwriter (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rambo\"><PERSON><PERSON></a>, American singer-songwriter (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gene_Stallings"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Pakistani-English engineer and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-English engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-English engineer and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Czech-English journalist and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian soldier and politician, 5th President of Algeria (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>flik<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>f<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian soldier and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Bouteflika"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "1938", "text": "<PERSON>, American author and composer (d. 2002)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and composer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Chilean economist, lawyer, and politician, 33rd President of Chile", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Ricardo_Lagos\" title=\"Ricardo Lagos\"><PERSON></a>, Chilean economist, lawyer, and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ricardo_Lagos\" title=\"Ricardo Lagos\"><PERSON></a>, Chilean economist, lawyer, and politician, 33rd <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a>", "links": [{"title": "Ricardo <PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Lagos"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and producer (d. 1997)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American author and academic (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish footballer (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian actor, director, and producer (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and producer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American admiral and physician, 16th Surgeon General of the United States", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and physician, 16th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and physician, 16th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "1942", "text": "<PERSON>, American novelist and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1942)\" title=\"<PERSON> (ice hockey, born 1942)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1942)\" title=\"<PERSON> (ice hockey, born 1942)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey, born 1942)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1942)"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Iranian architect and politician, 79th Prime Minister of Iran", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian architect and politician, 79th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian architect and politician, 79th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iran\" title=\"Prime Minister of Iran\">Prime Minister of Iran</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Iran", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iran"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English footballer (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and poet (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American painter and cartoonist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and cartoonist", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Finnish conductor and composer (d. 2024)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English trumpet player and composer (d. 2013)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, English trumpet player and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)\" title=\"<PERSON> (trumpeter)\"><PERSON></a>, English trumpet player and composer (d. 2013)", "links": [{"title": "<PERSON> (trumpeter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trumpeter)"}]}, {"year": "1947", "text": "<PERSON>, Australian politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Brazilian singer-songwriter (d. 2014)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nelson Ned\"><PERSON></a>, Brazilian singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ned\" title=\"Nelson Ned\"><PERSON></a>, Brazilian singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ned"}]}, {"year": "1947", "text": "<PERSON>, English footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American guitarist and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Irish singer-songwriter, guitarist, and producer (d. 1995)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian journalist and politician, 43rd Premier of Victoria", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 43rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1948", "text": "<PERSON>, Australian politician, 25th Premier of Western Australia", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1950", "text": "<PERSON>, American singer (d. 1983)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American actress and comedian", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Russ_Feingold"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American pop-rock singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop-rock singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop-rock singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer, drummer, actor, and TV/film producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, drummer, actor, and TV/film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, drummer, actor, and TV/film producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician, 50th United States Secretary of the Interior", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1955", "text": "<PERSON>, Australian cricketer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American musician, songwriter, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian rock bass player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian rock bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian rock bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1957", "text": "<PERSON>, American inventor and computer engineer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American inventor and computer engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American inventor and computer engineer", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Iranian general and politician, Iranian Minister of Defense", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_and_Armed_Forces_Logistics_(Iran)\" title=\"Ministry of Defence and Armed Forces Logistics (Iran)\">Iranian Minister of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_and_Armed_Forces_Logistics_(Iran)\" title=\"Ministry of Defence and Armed Forces Logistics (Iran)\">Iranian Minister of Defense</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Defence and Armed Forces Logistics (Iran)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_and_Armed_Forces_Logistics_(Iran)"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Georgian film director and screenwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian film director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, South African-American tennis player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-Welsh golfer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1961", "text": "<PERSON>, Australian conductor, director, and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian conductor, director, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian conductor, director, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English journalist and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Norwegian footballer and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian politician, Australian Minister for Employment", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Employment_(Australia)\" class=\"mw-redirect\" title=\"Minister for Employment (Australia)\">Australian Minister for Employment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Employment_(Australia)\" class=\"mw-redirect\" title=\"Minister for Employment (Australia)\">Australian Minister for Employment</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(politician)"}, {"title": "Minister for Employment (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Employment_(Australia)"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Finnish ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Summa<PERSON>\" title=\"<PERSON><PERSON> Summa<PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Summa<PERSON>\" title=\"<PERSON><PERSON> Summanen\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Italian race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>quini\"><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>quini\"><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>quini"}]}, {"year": "1963", "text": "<PERSON>, Australian politician, 31st Prime Minister of Australia", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1963", "text": "<PERSON>, American singer and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American surfer and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rd <PERSON>\"><PERSON><PERSON></a>, American surfer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rd <PERSON>\"><PERSON><PERSON></a>, American surfer and actor", "links": [{"title": "Laird <PERSON>", "link": "https://wikipedia.org/wiki/Lai<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler (d. 1987)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Northern Irish politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Lembit_%C3%96pik\" title=\"Lembit Öpik\"><PERSON><PERSON><PERSON></a>, Northern Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lembit_%C3%96pik\" title=\"Lembit Öpik\"><PERSON><PERSON><PERSON></a>, Northern Irish politician", "links": [{"title": "Lembit Öpik", "link": "https://wikipedia.org/wiki/Lembit_%C3%96pik"}]}, {"year": "1966", "text": "<PERSON>, American author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English lawyer and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actor and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English politician, Secretary of State for Work and Pensions", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions\" title=\"Secretary of State for Work and Pensions\">Secretary of State for Work and Pensions</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Work and Pensions", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Work_and_Pensions"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swiss footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ciriaco_Sforza\" title=\"Ciriaco Sforza\"><PERSON><PERSON><PERSON></a>, Swiss footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ciriaco_Sforza\" title=\"Ciriaco Sforza\"><PERSON><PERSON><PERSON></a>, Swiss footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iriaco_<PERSON>rza"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Dutch pianist and composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Wibi_Soerjadi\" title=\"Wibi Soerjadi\"><PERSON><PERSON></a>, Dutch pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wibi_Soerjadi\" title=\"Wibi Soerjadi\"><PERSON><PERSON></a>, Dutch pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wibi_Soer<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Czech ice hockey player (d. 2023)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Roman_%C4%8Cechm%C3%A1nek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_%C4%8Cechm%C3%A1nek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_%C4%8Cechm%C3%A1nek"}]}, {"year": "1971", "text": "<PERSON>, English comedian, author and television presenter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, author and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, author and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American rapper, record producer and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Method_Man\" title=\"Method Man\"><PERSON> Man</a>, American rapper, record producer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Method_Man\" title=\"Method Man\"><PERSON> Man</a>, American rapper, record producer and actor", "links": [{"title": "Method Man", "link": "https://wikipedia.org/wiki/<PERSON>_Man"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Serbian basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Australian swimmer and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, New Zealand rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English singer-songwriter (<PERSON><PERSON>)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coldplay", "link": "https://wikipedia.org/wiki/Coldplay"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English swimmer and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" class=\"mw-redirect\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(swimmer)\" class=\"mw-redirect\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer and sportscaster", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1977", "text": "<PERSON>, South African-English cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Filipino actor and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)\" title=\"<PERSON> (footballer, born 1978)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1978)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1978)"}]}, {"year": "1978", "text": "<PERSON>, Polish gridiron football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish gridiron football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish gridiron football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kaberle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Kaberle\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Ka<PERSON>le"}]}, {"year": "1979", "text": "<PERSON>, Irish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer and manager (d. 2020)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian actress and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American wrestler (d. 2010)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cade\"><PERSON></a>, American wrestler (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cade\"><PERSON></a>, American wrestler (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nyi\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nyi\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1nyi"}]}, {"year": "1982", "text": "<PERSON>, Swedish ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Lisa<PERSON>ro_L%C3%B<PERSON><PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1983)\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ro_L%C3%B3<PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1983)\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/Lisandro_L%C3%B3<PERSON><PERSON>_(footballer,_born_1983)"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Swedish ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Aversa\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Aversa\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_D%27Aversa"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1986)\" title=\"<PERSON> (basketball, born 1986)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1986)\" title=\"<PERSON> (basketball, born 1986)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1986)"}]}, {"year": "1987", "text": "<PERSON>, Swedish basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%89dgar_And<PERSON>\" title=\"<PERSON><PERSON><PERSON> And<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89dgar_And<PERSON>\" title=\"Éd<PERSON> And<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dgar_Andrade"}]}, {"year": "1988", "text": "<PERSON>, English singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American beauty queen, Miss America 2012", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_America_2012\" title=\"Miss America 2012\">Miss America 2012</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_America_2012\" title=\"Miss America 2012\">Miss America 2012</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss America 2012", "link": "https://wikipedia.org/wiki/Miss_America_2012"}]}, {"year": "1988", "text": "<PERSON>, Australian diver", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Geert_<PERSON>_Roorda\" title=\"Geert Arend Roorda\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gee<PERSON>_<PERSON>d_Roorda\" title=\"Geert Arend Roorda\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Belgian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Toby_Alderweireld"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Alem%C3%A3<PERSON>_(footballer,_born_1989)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1989)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alem%C3%A3<PERSON>_(footballer,_born_1989)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1989)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1989)", "link": "https://wikipedia.org/wiki/Alem%C3%A3o_(footballer,_born_1989)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Austrian skier", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Portuguese footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Alliku"}]}, {"year": "1990", "text": "<PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Indian actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S<PERSON>\" title=\"<PERSON> Shroff\"><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S<PERSON>\" title=\"<PERSON> Shroff\"><PERSON></a>, Indian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>off"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1992", "text": "<PERSON>, American ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Argentine-Italian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Brussino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Brussino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Brussino"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Cuban baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Adolis_Garc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolis_Garc%C3%ADa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolis_Garc%C3%ADa"}]}, {"year": "1995", "text": "<PERSON>, Dominican baseball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAjar\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAjar\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_And%C3%BAjar"}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dom<PERSON>\" title=\"<PERSON> Domi\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dom<PERSON>\" title=\"<PERSON> Domi\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer and actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tagovailoa\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ovailo<PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pacheco\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pacheco\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pacheco"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Hong Kong singer and actress", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_Zhan"}]}, {"year": "2016", "text": "<PERSON>, Duke of Skåne", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Sk%C3%A5ne\" title=\"Prince <PERSON>, Duke of Skåne\">Prince <PERSON>, Duke of Skåne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Sk%C3%A5ne\" title=\"Prince <PERSON>, Duke of Skåne\">Prince <PERSON>, Duke of Skåne</a>", "links": [{"title": "<PERSON>, Duke of Skåne", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Sk%C3%A5ne"}]}], "Deaths": [{"year": "274", "text": "<PERSON><PERSON>, Persian prophet and founder of Manichaeism (b. 216)", "html": "274 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(prophet)\" title=\"<PERSON><PERSON> (prophet)\"><PERSON><PERSON></a>, Persian prophet and founder of Manichaeism (b. 216)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(prophet)\" title=\"<PERSON><PERSON> (prophet)\"><PERSON><PERSON></a>, Persian prophet and founder of Manichaeism (b. 216)", "links": [{"title": "<PERSON><PERSON> (prophet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(prophet)"}]}, {"year": "672", "text": "<PERSON> of Mercia, English bishop and saint (b. 634)", "html": "672 - <a href=\"https://wikipedia.org/wiki/Chad_of_Mercia\" title=\"Chad of Mercia\"><PERSON> of Mercia</a>, English bishop and saint (b. 634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chad_of_Mercia\" title=\"Chad of Mercia\">Chad of Mercia</a>, English bishop and saint (b. 634)", "links": [{"title": "Chad of Mercia", "link": "https://wikipedia.org/wiki/Chad_of_Mercia"}]}, {"year": "968", "text": "<PERSON>, archbishop of Mainz (b. 929)", "html": "968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON></a>, archbishop of Mainz (b. 929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Mainz)\" title=\"<PERSON> (archbishop of Mainz)\"><PERSON></a>, archbishop of Mainz (b. 929)", "links": [{"title": "<PERSON> (archbishop of Mainz)", "link": "https://wikipedia.org/wiki/<PERSON>(archbishop_of_Mainz)"}]}, {"year": "986", "text": "<PERSON><PERSON><PERSON>, king of West Francia (b. 941)", "html": "986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>r_of_France\" title=\"<PERSON><PERSON><PERSON> of France\"><PERSON><PERSON><PERSON></a>, king of West Francia (b. 941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>r_of_France\" title=\"<PERSON><PERSON><PERSON> of France\"><PERSON><PERSON><PERSON></a>, king of West Francia (b. 941)", "links": [{"title": "<PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>r_of_France"}]}, {"year": "1009", "text": "<PERSON><PERSON><PERSON>, king of Goryeo (b. 980)", "html": "1009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON></a>, king of Goryeo (b. 980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON></a>, king of Goryeo (b. 980)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Mokjong_of_Goryeo"}]}, {"year": "1127", "text": "<PERSON>, Count of Flanders (b. 1084)", "html": "1127 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" class=\"mw-redirect\" title=\"<PERSON>, Count of Flanders\"><PERSON> the <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Count\" title=\"Count\">Count</a> of <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a> (b. 1084)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" class=\"mw-redirect\" title=\"<PERSON>, Count of Flanders\"><PERSON> the <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Count\" title=\"Count\">Count</a> of <a href=\"https://wikipedia.org/wiki/Flanders\" title=\"Flanders\">Flanders</a> (b. 1084)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}, {"title": "Count", "link": "https://wikipedia.org/wiki/Count"}, {"title": "Flanders", "link": "https://wikipedia.org/wiki/Flanders"}]}, {"year": "1316", "text": "<PERSON>, Scottish daughter of <PERSON> (b. 1296)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1296)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1333", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Poland (b. 1261)", "html": "1333 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_I_the_Elbow-high\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> I the Elbow-high\"><PERSON><PERSON><PERSON><PERSON> I</a>, king of Poland (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_I_the_Elbow-high\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> I the Elbow-high\"><PERSON><PERSON><PERSON><PERSON> I</a>, king of Poland (b. 1261)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> the Elbow-high", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_I_the_Elbow-high"}]}, {"year": "1589", "text": "<PERSON>, Italian cardinal and diplomat (b. 1520)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Italian cardinal and diplomat (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Italian cardinal and diplomat (b. 1520)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)"}]}, {"year": "1619", "text": "<PERSON> of Denmark, queen of Scotland (b. 1574)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>, queen of Scotland (b. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>, queen of Scotland (b. 1574)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denmark"}]}, {"year": "1729", "text": "<PERSON>, Italian astronomer and philosopher (b. 1662)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (b. 1662)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, French duke and diplomat (b. 1675)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON></a>, French duke and diplomat (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_de_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON></a>, French duke and diplomat (b. 1675)", "links": [{"title": "<PERSON>, duc de <PERSON>-Simon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON>, English cleric and theologian (b. 1703)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and theologian (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cleric and theologian (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, Swedish-Danish painter and academic (b. 1711)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Danish painter and academic (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Danish painter and academic (b. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, English historian and politician (b. 1717)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and politician (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Mexican revolutionary (b. ca. 1773)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%ADnguez\" title=\"<PERSON><PERSON>z\"><PERSON><PERSON></a>, Mexican revolutionary (b. ca. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%ADnguez\" title=\"<PERSON><PERSON>z\"><PERSON><PERSON></a>, Mexican revolutionary (b. ca. 1773)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%ADnguez"}]}, {"year": "1830", "text": "<PERSON>, German physician, anatomist, and anthropologist (b. 1755)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring\" title=\"<PERSON>\"><PERSON></a>, German physician, anatomist, and anthropologist (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring\" title=\"<PERSON>\"><PERSON></a>, German physician, anatomist, and anthropologist (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring"}]}, {"year": "1835", "text": "<PERSON>, Holy Roman Emperor (b. 1768)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>, Holy Roman Emperor (b. 1768)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1840", "text": "<PERSON>, German physician and astronomer (b. 1758)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astronomer (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and astronomer (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Russian emperor (b. 1796)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian emperor (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, Russian emperor (b. 1796)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON>, American colonel (b. 1842)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American colonel (b. 1842)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, German-New Zealand priest and missionary (b. 1819)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>vi<PERSON>_V%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-New Zealand priest and missionary (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_V%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-New Zealand priest and missionary (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carl_Sylvius_V%C3%B6lkner"}]}, {"year": "1880", "text": "<PERSON>, Irish engineer (b. 1790)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish engineer (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, French painter (b. 1841)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter (b. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian politician (b. 1830)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian politician (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian politician (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%27il_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American general (b. 1816)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON> Early\"><PERSON><PERSON></a>, American general (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON> Early\"><PERSON><PERSON></a>, American general (b. 1816)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American lawyer and politician, 41st Speaker of the United States House of Representatives (b. 1850)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 41st <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist, poet, playwright, and critic (b. 1885)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, poet, playwright, and critic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist, poet, playwright, and critic (b. 1885)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American pianist and composer (b. 1871)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English archaeologist and historian (b. 1874)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and historian (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and historian (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Austrian physician (b.1867)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian physician (b.1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian physician (b.1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, British biochemist, the first woman admitted to the London Chemical Society (b. 1877)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Ida_Maclean\" title=\"Ida Maclean\"><PERSON></a>, British biochemist, the first woman admitted to the <a href=\"https://wikipedia.org/wiki/Chemical_Society\" title=\"Chemical Society\">London Chemical Society</a> (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_Maclean\" title=\"Ida Maclean\"><PERSON></a>, British biochemist, the first woman admitted to the <a href=\"https://wikipedia.org/wiki/Chemical_Society\" title=\"Chemical Society\">London Chemical Society</a> (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_<PERSON>lean"}, {"title": "Chemical Society", "link": "https://wikipedia.org/wiki/Chemical_Society"}]}, {"year": "1945", "text": "<PERSON>, Canadian painter and author (b. 1871)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician, Hungarian Minister of Agriculture (b. 1895)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)\" title=\"Minister of Agriculture (Hungary)\">Hungarian Minister of Agriculture</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)\" title=\"Minister of Agriculture (Hungary)\">Hungarian Minister of Agriculture</a> (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy"}, {"title": "Minister of Agriculture (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)"}]}, {"year": "1946", "text": "<PERSON>, American colonel, Medal of Honor recipient (b. 1872)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Dutch architect and urban planner (b. 1882)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Fr<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect and urban planner (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect and urban planner (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and activist (b. 1879)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Sarojini_Naidu\" title=\"<PERSON><PERSON><PERSON><PERSON> Naidu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarojini_Naidu\" title=\"<PERSON><PERSON><PERSON><PERSON> Naidu\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and activist (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sa<PERSON><PERSON><PERSON>_Naidu"}]}, {"year": "1953", "text": "<PERSON>, American runner (b. 1882)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>body\"><PERSON></a>, American runner (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>body"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Turkish educator and politician (b. 1874)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Selim_S%C4%B1rr%C4%B1_Tarcan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish educator and politician (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selim_S%C4%B1rr%C4%B1_Tarcan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish educator and politician (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Selim_S%C4%B1rr%C4%B1_Tarcan"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and manager (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian mathematician and academic (b. 1866)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belgian mathematician and academic (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Belgian mathematician and academic (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Spanish author and critic (b. 1873)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and critic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish author and critic (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%ADnez_Ruiz"}]}, {"year": "1972", "text": "Léo-<PERSON>, Canadian director and producer (b. 1877)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and producer (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and producer (b. 1877)", "links": [{"title": "Léo<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, French chef (b. 1895)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_Brazier\" title=\"<PERSON>ug<PERSON><PERSON> Brazier\"><PERSON><PERSON><PERSON><PERSON></a>, French chef (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A9nie_Brazier\" title=\"<PERSON>ug<PERSON><PERSON> Brazier\"><PERSON><PERSON><PERSON><PERSON></a>, French chef (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A9nie_<PERSON>razier"}]}, {"year": "1979", "text": "<PERSON>, Irish hurler (b. 1920)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christy Ring\"><PERSON></a>, Irish hurler (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christy Ring\"><PERSON></a>, Irish hurler (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American philosopher and author (b. 1928)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and director (b. 1898)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Indonesian geographer and academic (b. 1935)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian geographer and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian geographer and academic (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>etoro"}]}, {"year": "1991", "text": "<PERSON>, French singer-songwriter, actor, and director (b. 1928)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, actor, and director (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, actor, and director (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actress (b. 1937)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actress, singer, and dancer (b. 1943)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American actress, singer, and dancer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English singer (b. 1939)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Springfield\"><PERSON></a>, English singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Springfield\"><PERSON></a>, English singer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Canadian curler (b. 1963)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Australian pianist and composer (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Irish footballer (b. 1980)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Cormac_Mc<PERSON>nallen\" title=\"Cormac McAnallen\"><PERSON><PERSON><PERSON></a>, Irish footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rmac_<PERSON>len\" title=\"<PERSON>rmac McAnallen\"><PERSON><PERSON><PERSON></a>, Irish footballer (b. 1980)", "links": [{"title": "Cormac McAnallen", "link": "https://wikipedia.org/wiki/Cormac_M<PERSON>nallen"}]}, {"year": "2004", "text": "<PERSON>, American actress (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Mercedes_Mc<PERSON>bridge\" title=\"Mercedes McCambridge\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_Mc<PERSON>ambridge\" title=\"Mercedes McCambridge\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "Mercedes McCambridge", "link": "https://wikipedia.org/wiki/Mercedes_McCambridge"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American businesswoman (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American pianist and composer (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American soldier and politician, 41st United States Secretary of the Interior (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 41st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 41st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American baseball player (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ine\" title=\"Clem Labine\"><PERSON><PERSON></a>, American baseball player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Labine\"><PERSON><PERSON></a>, American baseball player (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lem_Labine"}]}, {"year": "2007", "text": "<PERSON>, Russian colonel and journalist (b. 1956)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and journalist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel and journalist (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Russian-French historian and author (b. 1911)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French historian and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French historian and author (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Canadian singer-songwriter and guitarist (b. 1966)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Bissau-Guinean politician, President of Guinea-Bissau (b. 1939)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/President_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"President of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bissau-Guinean politician, <a href=\"https://wikipedia.org/wiki/President_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"President of Guinea-Bissau\">President of Guinea-Bissau</a> (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Guinea-Bissau", "link": "https://wikipedia.org/wiki/President_of_Guinea-Bissau"}]}, {"year": "2010", "text": "<PERSON>, English journalist and politician (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1940%E2%80%932010)\" title=\"<PERSON> (1940-2010)\"><PERSON></a>, English journalist and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(1940%E2%80%932010)\" title=\"<PERSON> (1940-2010)\"><PERSON></a>, English journalist and politician (b. 1940)", "links": [{"title": "<PERSON> (1940-2010)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1940%E2%80%932010)"}]}, {"year": "2012", "text": "<PERSON>, South African environmentalist, explorer, and author (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African environmentalist, explorer, and author (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African environmentalist, explorer, and author (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American colonel, Medal of Honor recipient (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>foot"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2012", "text": "<PERSON>-<PERSON><PERSON><PERSON>, English academic and politician, Chancellor of the Duchy of Lancaster (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Norman_St_John-Stevas\" title=\"Norman St John-Stevas\"><PERSON> John<PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norman_St_John-Stevas\" title=\"Norman St John-Stevas\"><PERSON> St John-Stevas</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1929)", "links": [{"title": "Norman St John-Stevas", "link": "https://wikipedia.org/wiki/Norman_St_John-<PERSON>eva<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "2012", "text": "<PERSON>, American political scientist and academic (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian journalist (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Greek basketball player (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Pakistani poet and author (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Shabnam_Shakeel\" title=\"Shabnam Shakeel\"><PERSON><PERSON><PERSON></a>, Pakistani poet and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shabnam_Shakeel\" title=\"Shabnam Shakeel\"><PERSON><PERSON><PERSON></a>, Pakistani poet and author (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hab<PERSON>_Shakeel"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Belarusian poet and translator (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and translator (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and translator (b. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American minister and colonel (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and colonel (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and colonel (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Scottish-English footballer and manager (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English footballer and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English author and illustrator (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian priest, historian, and philosopher (b. 1915)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Lacroix\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest, historian, and philosopher (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Lacroix\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest, historian, and philosopher (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Lacroix"}]}, {"year": "2016", "text": "<PERSON>, American businessman (b. 1959)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American actor (b. 1969)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Chinese lieutenant general (b. 1927)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese lieutenant general (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese lieutenant general (b. 1927)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "2019", "text": "<PERSON>, British sociologist, disability rights activist (b. 1945)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(disability_advocate)\" title=\"<PERSON> (disability advocate)\"><PERSON></a>, British sociologist, disability rights activist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(disability_advocate)\" title=\"<PERSON> (disability advocate)\"><PERSON></a>, British sociologist, disability rights activist (b. 1945)", "links": [{"title": "<PERSON> (disability advocate)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(disability_advocate)"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Filipino actress (b. 1963)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}