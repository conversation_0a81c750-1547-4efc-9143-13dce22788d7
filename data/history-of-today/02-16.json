{"date": "February 16", "url": "https://wikipedia.org/wiki/February_16", "data": {"Events": [{"year": "1249", "text": "<PERSON> of Longjumeau is dispatched by <PERSON> of France as his ambassador to meet with the Khagan of the Mongol Empire.", "html": "1249 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_de_Longjumeau\" title=\"<PERSON>\"><PERSON> of Longjumeau</a> is dispatched by <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> as his <a href=\"https://wikipedia.org/wiki/Ambassador\" title=\"Ambassador\">ambassador</a> to meet with the <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">Khagan</a> of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_de_Longju<PERSON>au\" title=\"<PERSON>\"><PERSON> of Longjumeau</a> is dispatched by <a href=\"https://wikipedia.org/wiki/Louis_IX_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> as his <a href=\"https://wikipedia.org/wiki/Ambassador\" title=\"Ambassador\">ambassador</a> to meet with the <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">Khagan</a> of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_de_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "Ambassador", "link": "https://wikipedia.org/wiki/Ambassador"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>gan"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}]}, {"year": "1270", "text": "The Grand Duchy of Lithuania defeats the Livonian Order in the Battle of Karuse.", "html": "1270 - The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Karuse\" title=\"Battle of Karuse\">Battle of Karuse</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeats the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Karuse\" title=\"Battle of Karuse\">Battle of Karuse</a>.", "links": [{"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Livonian Order", "link": "https://wikipedia.org/wiki/Livonian_Order"}, {"title": "Battle of Karuse", "link": "https://wikipedia.org/wiki/Battle_of_Karuse"}]}, {"year": "1630", "text": "Dutch forces led by <PERSON><PERSON><PERSON> capture Olinda in what was to become part of Dutch Brazil.", "html": "1630 - Dutch forces led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Dutch_invasions_of_Brazil#Invasion_of_Olinda_and_Recife_(1630-1654)\" title=\"Dutch invasions of Brazil\">capture</a> <a href=\"https://wikipedia.org/wiki/Olinda\" title=\"Olinda\">Olind<PERSON></a> in what was to become part of <a href=\"https://wikipedia.org/wiki/Dutch_Brazil\" title=\"Dutch Brazil\">Dutch Brazil</a>.", "no_year_html": "Dutch forces led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Dutch_invasions_of_Brazil#Invasion_of_Olinda_and_Recife_(1630-1654)\" title=\"Dutch invasions of Brazil\">capture</a> <a href=\"https://wikipedia.org/wiki/Olinda\" title=\"Olinda\">Olinda</a> in what was to become part of <a href=\"https://wikipedia.org/wiki/Dutch_Brazil\" title=\"Dutch Brazil\">Dutch Brazil</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Dutch invasions of Brazil", "link": "https://wikipedia.org/wiki/Dutch_invasions_of_Brazil#Invasion_of_<PERSON>linda_and_Recife_(1630-1654)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Dutch Brazil", "link": "https://wikipedia.org/wiki/Dutch_Brazil"}]}, {"year": "1646", "text": "Battle of Torrington, Devon: The last major battle of the First English Civil War.", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Battle_of_Torrington\" title=\"Battle of Torrington\">Battle of Torrington</a>, Devon: The last major battle of the <a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Torrington\" title=\"Battle of Torrington\">Battle of Torrington</a>, Devon: The last major battle of the <a href=\"https://wikipedia.org/wiki/First_English_Civil_War\" title=\"First English Civil War\">First English Civil War</a>.", "links": [{"title": "Battle of Torrington", "link": "https://wikipedia.org/wiki/Battle_of_Torrington"}, {"title": "First English Civil War", "link": "https://wikipedia.org/wiki/First_English_Civil_War"}]}, {"year": "1699", "text": "First Leopoldine Diploma is issued by the Holy Roman Emperor, recognizing the Greek Catholic clergy enjoyed the same privileges as Roman Catholic priests in the Principality of Transylvania.", "html": "1699 - First <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"Leopold I, Holy Roman Emperor\">Leopoldine</a> Diploma is issued by the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, recognizing the <a href=\"https://wikipedia.org/wiki/Greek_Catholic\" class=\"mw-redirect\" title=\"Greek Catholic\">Greek Catholic</a> clergy enjoyed the same privileges as <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> priests in the <a href=\"https://wikipedia.org/wiki/Principality_of_Transylvania_(1570%E2%80%931711)\" title=\"Principality of Transylvania (1570-1711)\">Principality of Transylvania</a>.", "no_year_html": "First <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"Leopold I, Holy Roman Emperor\">Leopoldine</a> Diploma is issued by the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, recognizing the <a href=\"https://wikipedia.org/wiki/Greek_Catholic\" class=\"mw-redirect\" title=\"Greek Catholic\">Greek Catholic</a> clergy enjoyed the same privileges as <a href=\"https://wikipedia.org/wiki/Roman_Catholic\" class=\"mw-redirect\" title=\"Roman Catholic\">Roman Catholic</a> priests in the <a href=\"https://wikipedia.org/wiki/Principality_of_Transylvania_(1570%E2%80%931711)\" title=\"Principality of Transylvania (1570-1711)\">Principality of Transylvania</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "Greek Catholic", "link": "https://wikipedia.org/wiki/Greek_Catholic"}, {"title": "Roman Catholic", "link": "https://wikipedia.org/wiki/Roman_Catholic"}, {"title": "Principality of Transylvania (1570-1711)", "link": "https://wikipedia.org/wiki/Principality_of_Transylvania_(1570%E2%80%931711)"}]}, {"year": "1742", "text": "<PERSON>, Earl of Wilmington, becomes British Prime Minister.", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Wilmington\" title=\"<PERSON>, 1st Earl of Wilmington\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wilmington\" title=\"Earl <PERSON> Wilmington\">Earl <PERSON> Wilmington</a>, becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Wilmington\" title=\"<PERSON>, 1st Earl of Wilmington\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wilmington\" title=\"Earl of Wilmington\">Earl <PERSON> Wilmington</a>, becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">British Prime Minister</a>.", "links": [{"title": "<PERSON>, 1st Earl of Wilmington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Wilmington"}, {"title": "<PERSON> of Wilmington", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wilmington"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1796", "text": "Colombo in Ceylon (now Sri Lanka) falls to the British, completing their invasion of Ceylon.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> in Ceylon (now Sri Lanka) falls to the British, completing their <a href=\"https://wikipedia.org/wiki/Invasion_of_Ceylon_(1795)\" class=\"mw-redirect\" title=\"Invasion of Ceylon (1795)\">invasion of Ceylon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> in Ceylon (now Sri Lanka) falls to the British, completing their <a href=\"https://wikipedia.org/wiki/Invasion_of_Ceylon_(1795)\" class=\"mw-redirect\" title=\"Invasion of Ceylon (1795)\">invasion of Ceylon</a>.", "links": [{"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}, {"title": "Invasion of Ceylon (1795)", "link": "https://wikipedia.org/wiki/Invasion_of_Ceylon_(1795)"}]}, {"year": "1804", "text": "First Barbary War: <PERSON> leads a raid to burn the pirate-held frigate USS Philadelphia.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a raid to burn the <a href=\"https://wikipedia.org/wiki/Pirate\" class=\"mw-redirect\" title=\"Pirate\">pirate</a>-held frigate <a href=\"https://wikipedia.org/wiki/USS_Philadelphia_(1799)\" title=\"USS Philadelphia (1799)\">USS <i>Philadelphia</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads a raid to burn the <a href=\"https://wikipedia.org/wiki/Pirate\" class=\"mw-redirect\" title=\"Pirate\">pirate</a>-held frigate <a href=\"https://wikipedia.org/wiki/USS_Philadelphia_(1799)\" title=\"USS Philadelphia (1799)\">USS <i>Philadelphia</i></a>.", "links": [{"title": "First Barbary War", "link": "https://wikipedia.org/wiki/First_Barbary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pirate", "link": "https://wikipedia.org/wiki/Pirate"}, {"title": "USS Philadelphia (1799)", "link": "https://wikipedia.org/wiki/USS_Philadelphia_(1799)"}]}, {"year": "1862", "text": "American Civil War: General <PERSON> captures Fort Donelson, Tennessee.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Donelson\" title=\"Battle of Fort Donelson\">captures</a> <a href=\"https://wikipedia.org/wiki/Fort_Donelson\" title=\"Fort Donelson\">Fort Donelson</a>, <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Grant\" title=\"Ulysses <PERSON>. Grant\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Fort_Donelson\" title=\"Battle of Fort Donelson\">captures</a> <a href=\"https://wikipedia.org/wiki/Fort_Donelson\" title=\"Fort Donelson\">Fort Donelson</a>, <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Fort Donelson", "link": "https://wikipedia.org/wiki/Battle_of_Fort_Donelson"}, {"title": "Fort Donelson", "link": "https://wikipedia.org/wiki/Fort_Donelson"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1866", "text": "<PERSON>, Marquess of Hartington becomes British Secretary of State for War.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Duke_of_Devonshire\" title=\"<PERSON>, 8th Duke of Devonshire\"><PERSON>, Marquess of Hartington</a> becomes British <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Duke_of_Devonshire\" title=\"<PERSON>, 8th Duke of Devonshire\"><PERSON>, Marquess of Hartington</a> becomes British <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a>.", "links": [{"title": "<PERSON>, 8th Duke of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Duke_of_Devonshire"}, {"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}]}, {"year": "1881", "text": "The Canadian Pacific Railway is incorporated by Act of Parliament at Ottawa (44th Vic., c.1).", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Railway\" title=\"Canadian Pacific Railway\">Canadian Pacific Railway</a> is incorporated by Act of Parliament at Ottawa (44th Vic., c.1).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Railway\" title=\"Canadian Pacific Railway\">Canadian Pacific Railway</a> is incorporated by Act of Parliament at Ottawa (44th Vic., c.1).", "links": [{"title": "Canadian Pacific Railway", "link": "https://wikipedia.org/wiki/Canadian_Pacific_Railway"}]}, {"year": "1899", "text": "Iceland's first football club, Knattspyrnufélag Reykjavíkur, is founded.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>'s first football club, <a href=\"https://wikipedia.org/wiki/Knattspyrnuf%C3%A9lag_Reykjav%C3%ADkur\" title=\"Knattspyrnufélag Reykjavíkur\">Knattspyrnufélag Reykjavíkur</a>, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a>'s first football club, <a href=\"https://wikipedia.org/wiki/Knattspyrnuf%C3%A9lag_Reykjav%C3%ADkur\" title=\"Knattspyrnufélag Reykjavíkur\">Knattspyrnufélag Reykjavíkur</a>, is founded.", "links": [{"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Knattspyrnufélag Reykjavíkur", "link": "https://wikipedia.org/wiki/Knattspyrnuf%C3%A9lag_Reykjav%C3%ADkur"}]}, {"year": "1900", "text": "The Southern Cross expedition led by <PERSON><PERSON> achieved a new Farthest South of 78° 50'S, making the first landing at the Great Ice Barrier.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Southern_Cross_expedition\" class=\"mw-redirect\" title=\"Southern Cross expedition\">Southern Cross expedition</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>ten_Borchgrevink\" title=\"<PERSON><PERSON> Borchgrevink\"><PERSON><PERSON></a> achieved a new <a href=\"https://wikipedia.org/wiki/Farthest_South\" title=\"Farthest South\">Farthest South</a> of 78° 50'S, making the first landing at the <a href=\"https://wikipedia.org/wiki/Ross_Ice_Shelf\" title=\"Ross Ice Shelf\">Great Ice Barrier</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Southern_Cross_expedition\" class=\"mw-redirect\" title=\"Southern Cross expedition\">Southern Cross expedition</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Borchgrevink\" title=\"<PERSON><PERSON> Borchgrevink\"><PERSON><PERSON>g<PERSON></a> achieved a new <a href=\"https://wikipedia.org/wiki/Farthest_South\" title=\"Farthest South\">Farthest South</a> of 78° 50'S, making the first landing at the <a href=\"https://wikipedia.org/wiki/Ross_Ice_Shelf\" title=\"Ross Ice Shelf\">Great Ice Barrier</a>.", "links": [{"title": "Southern Cross expedition", "link": "https://wikipedia.org/wiki/Southern_Cross_expedition"}, {"title": "<PERSON><PERSON>gre<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ten_Borchgrevink"}, {"title": "Farthest South", "link": "https://wikipedia.org/wiki/Farthest_South"}, {"title": "Ross Ice Shelf", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lf"}]}, {"year": "1918", "text": "The Council of Lithuania unanimously adopts the Act of Independence, declaring Lithuania an independent state.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Council_of_Lithuania\" title=\"Council of Lithuania\">Council of Lithuania</a> unanimously adopts the <a href=\"https://wikipedia.org/wiki/Act_of_Independence_of_Lithuania\" title=\"Act of Independence of Lithuania\">Act of Independence</a>, declaring <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> an independent state.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Council_of_Lithuania\" title=\"Council of Lithuania\">Council of Lithuania</a> unanimously adopts the <a href=\"https://wikipedia.org/wiki/Act_of_Independence_of_Lithuania\" title=\"Act of Independence of Lithuania\">Act of Independence</a>, declaring <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> an independent state.", "links": [{"title": "Council of Lithuania", "link": "https://wikipedia.org/wiki/Council_of_Lithuania"}, {"title": "Act of Independence of Lithuania", "link": "https://wikipedia.org/wiki/Act_of_Independence_of_Lithuania"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1923", "text": "<PERSON> unseals the burial chamber of <PERSON><PERSON><PERSON>.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> unseals the <a href=\"https://wikipedia.org/wiki/Burial_chamber\" class=\"mw-redirect\" title=\"Burial chamber\">burial chamber</a> of <a href=\"https://wikipedia.org/wiki/Pharaoh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Tutankhamun\" title=\"Tutankhamun\">Tutankhamun</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> unseals the <a href=\"https://wikipedia.org/wiki/Burial_chamber\" class=\"mw-redirect\" title=\"Burial chamber\">burial chamber</a> of <a href=\"https://wikipedia.org/wiki/Pharaoh\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Tutankhamun\" title=\"Tutankhamun\">Tutankhamun</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Burial chamber", "link": "https://wikipedia.org/wiki/Burial_chamber"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Tutankhamun", "link": "https://wikipedia.org/wiki/Tutankhamun"}]}, {"year": "1930", "text": "The Romanian Football Federation joins FIFA.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Romanian_Football_Federation\" title=\"Romanian Football Federation\">Romanian Football Federation</a> joins <a href=\"https://wikipedia.org/wiki/FIFA\" title=\"FIFA\">FIFA</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Romanian_Football_Federation\" title=\"Romanian Football Federation\">Romanian Football Federation</a> joins <a href=\"https://wikipedia.org/wiki/FIFA\" title=\"FIFA\">FIFA</a>.", "links": [{"title": "Romanian Football Federation", "link": "https://wikipedia.org/wiki/Romanian_Football_Federation"}, {"title": "FIFA", "link": "https://wikipedia.org/wiki/FIFA"}]}, {"year": "1934", "text": "The Austrian Civil War ends with the defeat of the Social Democrats and the Republikanischer Schutzbund.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/Austrian_Civil_War\" title=\"Austrian Civil War\">Austrian Civil War</a> ends with the defeat of the <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Austria\" title=\"Social Democratic Party of Austria\">Social Democrats</a> and the <a href=\"https://wikipedia.org/wiki/Republikanischer_Schutzbund\" title=\"Republikanischer Schutzbund\">Republikanischer Schutzbund</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Austrian_Civil_War\" title=\"Austrian Civil War\">Austrian Civil War</a> ends with the defeat of the <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_of_Austria\" title=\"Social Democratic Party of Austria\">Social Democrats</a> and the <a href=\"https://wikipedia.org/wiki/Republikanischer_Schutzbund\" title=\"Republikanischer Schutzbund\">Republikanischer Schutzbund</a>.", "links": [{"title": "Austrian Civil War", "link": "https://wikipedia.org/wiki/Austrian_Civil_War"}, {"title": "Social Democratic Party of Austria", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_of_Austria"}, {"title": "Republikanischer Schutzbund", "link": "https://wikipedia.org/wiki/Republikanischer_Schutzbund"}]}, {"year": "1936", "text": "The Popular Front wins the 1936 Spanish general election.", "html": "1936 - The Popular Front wins the <a href=\"https://wikipedia.org/wiki/1936_Spanish_general_election\" title=\"1936 Spanish general election\">1936 Spanish general election</a>.", "no_year_html": "The Popular Front wins the <a href=\"https://wikipedia.org/wiki/1936_Spanish_general_election\" title=\"1936 Spanish general election\">1936 Spanish general election</a>.", "links": [{"title": "1936 Spanish general election", "link": "https://wikipedia.org/wiki/1936_Spanish_general_election"}]}, {"year": "1937", "text": "<PERSON> receives a United States patent for nylon.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Car<PERSON>\"><PERSON></a> receives a United States <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Nylon\" title=\"Nylon\">nylon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> receives a United States <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Nylon\" title=\"Nylon\">nylon</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Nylon", "link": "https://wikipedia.org/wiki/Nylon"}]}, {"year": "1940", "text": "World War II: Altmark incident: The German tanker Altmark is boarded by sailors from the British destroyer HMS Cossack. A total of 299 British prisoners are freed.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Altmark_incident\" title=\"Altmark incident\">Altmark incident</a>: The <a href=\"https://wikipedia.org/wiki/German_tanker_Altmark\" title=\"German tanker Altmark\">German tanker Alt<PERSON></a> is boarded by sailors from the British <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a> <a href=\"https://wikipedia.org/wiki/HMS_Cossack\" title=\"HMS Cossack\">HMS <i>Cossack</i></a>. A total of 299 British prisoners are freed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Altmark_incident\" title=\"Altmark incident\">Altmark incident</a>: The <a href=\"https://wikipedia.org/wiki/German_tanker_Altmark\" title=\"German tanker Altmark\">German tanker Altmark</a> is boarded by sailors from the British <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a> <a href=\"https://wikipedia.org/wiki/HMS_Cossack\" title=\"HMS Cossack\">HMS <i>Cossack</i></a>. A total of 299 British prisoners are freed.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Altmark incident", "link": "https://wikipedia.org/wiki/Altmark_incident"}, {"title": "German tanker Altmark", "link": "https://wikipedia.org/wiki/German_tanker_Altmark"}, {"title": "Destroyer", "link": "https://wikipedia.org/wiki/Destroyer"}, {"title": "HMS Cossack", "link": "https://wikipedia.org/wiki/HMS_Cossack"}]}, {"year": "1942", "text": "World War II: In Athens, the Greek People's Liberation Army is established", "html": "1942 - World War II: In <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> is established", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, the <a href=\"https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army\" class=\"mw-redirect\" title=\"Greek People's Liberation Army\">Greek People's Liberation Army</a> is established", "links": [{"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "Greek People's Liberation Army", "link": "https://wikipedia.org/wiki/Greek_People%27s_Liberation_Army"}]}, {"year": "1942", "text": "World War II: Attack on Aruba, first World War II German shots fired on a land based object in the Americas.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Attack_on_Aruba\" title=\"Attack on Aruba\">Attack on Aruba</a>, first World War II German shots fired on a land based object in the Americas.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Attack_on_Aruba\" title=\"Attack on Aruba\">Attack on Aruba</a>, first World War II German shots fired on a land based object in the Americas.", "links": [{"title": "Attack on Aruba", "link": "https://wikipedia.org/wiki/Attack_on_Aruba"}]}, {"year": "1943", "text": "World War II: In the early phases of the Third Battle of Kharkov, Red Army troops re-enter the city.", "html": "1943 - World War II: In the early phases of the <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Kharkov\" title=\"Third Battle of Kharkov\">Third Battle of Kharkov</a>, <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> troops re-enter the city.", "no_year_html": "World War II: In the early phases of the <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Kharkov\" title=\"Third Battle of Kharkov\">Third Battle of Kharkov</a>, <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> troops re-enter the city.", "links": [{"title": "Third Battle of Kharkov", "link": "https://wikipedia.org/wiki/Third_Battle_of_Kharkov"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1945", "text": "World War II: American forces land on Corregidor Island in the Philippines.", "html": "1945 - World War II: American forces <a href=\"https://wikipedia.org/wiki/Battle_of_Corregidor_(1945)\" title=\"Battle of Corregidor (1945)\">land on</a> <a href=\"https://wikipedia.org/wiki/Corregidor_Island\" class=\"mw-redirect\" title=\"Corregidor Island\">Corregidor Island</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "World War II: American forces <a href=\"https://wikipedia.org/wiki/Battle_of_Corregidor_(1945)\" title=\"Battle of Corregidor (1945)\">land on</a> <a href=\"https://wikipedia.org/wiki/Corregidor_Island\" class=\"mw-redirect\" title=\"Corregidor Island\">Corregidor Island</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Battle of Corregidor (1945)", "link": "https://wikipedia.org/wiki/Battle_of_Corregidor_(1945)"}, {"title": "Corregidor Island", "link": "https://wikipedia.org/wiki/Corregidor_Island"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1945", "text": "The Alaska Equal Rights Act of 1945, the first anti-discrimination law in the United States, was signed into law.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Alaska_Equal_Rights_Act_of_1945\" title=\"Alaska Equal Rights Act of 1945\">Alaska Equal Rights Act of 1945</a>, the first <a href=\"https://wikipedia.org/wiki/Anti-discrimination_law\" title=\"Anti-discrimination law\">anti-discrimination law</a> in the United States, was signed into law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Alaska_Equal_Rights_Act_of_1945\" title=\"Alaska Equal Rights Act of 1945\">Alaska Equal Rights Act of 1945</a>, the first <a href=\"https://wikipedia.org/wiki/Anti-discrimination_law\" title=\"Anti-discrimination law\">anti-discrimination law</a> in the United States, was signed into law.", "links": [{"title": "Alaska Equal Rights Act of 1945", "link": "https://wikipedia.org/wiki/Alaska_Equal_Rights_Act_of_1945"}, {"title": "Anti-discrimination law", "link": "https://wikipedia.org/wiki/Anti-discrimination_law"}]}, {"year": "1959", "text": "<PERSON><PERSON> becomes Premier of Cuba after dictator <PERSON><PERSON><PERSON><PERSON> was overthrown on January 1.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes Premier of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> after dictator <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON><PERSON></a> was overthrown on <a href=\"https://wikipedia.org/wiki/January_1\" title=\"January 1\">January 1</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes Premier of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> after dictator <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON><PERSON> Batista</a> was overthrown on <a href=\"https://wikipedia.org/wiki/January_1\" title=\"January 1\">January 1</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}, {"title": "January 1", "link": "https://wikipedia.org/wiki/January_1"}]}, {"year": "1960", "text": "The U.S. Navy submarine USS Triton begins Operation Sandblast, setting sail from New London, Connecticut, to begin the first submerged circumnavigation of the globe.", "html": "1960 - The U.S. Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Triton_(SSRN-586)\" title=\"USS Triton (SSRN-586)\">USS <i>Triton</i></a> begins <a href=\"https://wikipedia.org/wiki/Operation_Sandblast\" title=\"Operation Sandblast\">Operation Sandblast</a>, setting sail from <a href=\"https://wikipedia.org/wiki/New_London,_Connecticut\" title=\"New London, Connecticut\">New London, Connecticut</a>, to begin the first submerged circumnavigation of the globe.", "no_year_html": "The U.S. Navy submarine <a href=\"https://wikipedia.org/wiki/USS_Triton_(SSRN-586)\" title=\"USS Triton (SSRN-586)\">USS <i>Triton</i></a> begins <a href=\"https://wikipedia.org/wiki/Operation_Sandblast\" title=\"Operation Sandblast\">Operation Sandblast</a>, setting sail from <a href=\"https://wikipedia.org/wiki/New_London,_Connecticut\" title=\"New London, Connecticut\">New London, Connecticut</a>, to begin the first submerged circumnavigation of the globe.", "links": [{"title": "USS Triton (SSRN-586)", "link": "https://wikipedia.org/wiki/USS_Triton_(SSRN-586)"}, {"title": "Operation Sandblast", "link": "https://wikipedia.org/wiki/Operation_Sandblast"}, {"title": "New London, Connecticut", "link": "https://wikipedia.org/wiki/New_London,_Connecticut"}]}, {"year": "1961", "text": "Explorer program: Explorer 9 (S-56a) is launched.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <a href=\"https://wikipedia.org/wiki/Explorer_9\" title=\"Explorer 9\">Explorer 9</a> (S-56a) is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <a href=\"https://wikipedia.org/wiki/Explorer_9\" title=\"Explorer 9\">Explorer 9</a> (S-56a) is launched.", "links": [{"title": "Explorer program", "link": "https://wikipedia.org/wiki/Explorer_program"}, {"title": "Explorer 9", "link": "https://wikipedia.org/wiki/Explorer_9"}]}, {"year": "1962", "text": "The Great Sheffield Gale impacts the United Kingdom, killing nine people; the city of Sheffield is devastated, with 150,000 homes damaged.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Great_Sheffield_Gale\" title=\"Great Sheffield Gale\">Great Sheffield Gale</a> impacts the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, killing nine people; the city of <a href=\"https://wikipedia.org/wiki/Sheffield\" title=\"Sheffield\">Sheffield</a> is devastated, with 150,000 homes damaged.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Sheffield_Gale\" title=\"Great Sheffield Gale\">Great Sheffield Gale</a> impacts the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>, killing nine people; the city of <a href=\"https://wikipedia.org/wiki/Sheffield\" title=\"Sheffield\">Sheffield</a> is devastated, with 150,000 homes damaged.", "links": [{"title": "Great Sheffield Gale", "link": "https://wikipedia.org/wiki/Great_Sheffield_Gale"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Sheffield", "link": "https://wikipedia.org/wiki/Sheffield"}]}, {"year": "1962", "text": "Flooding in the coastal areas of West Germany kills 315 and destroys the homes of about 60,000 people.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/North_Sea_flood_of_1962\" title=\"North Sea flood of 1962\">Flooding</a> in the coastal areas of West Germany kills 315 and destroys the homes of about 60,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Sea_flood_of_1962\" title=\"North Sea flood of 1962\">Flooding</a> in the coastal areas of West Germany kills 315 and destroys the homes of about 60,000 people.", "links": [{"title": "North Sea flood of 1962", "link": "https://wikipedia.org/wiki/North_Sea_flood_of_1962"}]}, {"year": "1968", "text": "In Haleyville, Alabama, the first 9-1-1 emergency telephone system goes into service.", "html": "1968 - In <a href=\"https://wikipedia.org/wiki/Haleyville,_Alabama\" title=\"Haleyville, Alabama\">Haleyville, Alabama</a>, the first <a href=\"https://wikipedia.org/wiki/9-1-1\" class=\"mw-redirect\" title=\"9-1-1\">9-1-1</a> emergency telephone system goes into service.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Haleyville,_Alabama\" title=\"Haleyville, Alabama\">Haleyville, Alabama</a>, the first <a href=\"https://wikipedia.org/wiki/9-1-1\" class=\"mw-redirect\" title=\"9-1-1\">9-1-1</a> emergency telephone system goes into service.", "links": [{"title": "Haleyville, Alabama", "link": "https://wikipedia.org/wiki/Haleyville,_Alabama"}, {"title": "9-1-1", "link": "https://wikipedia.org/wiki/9-1-1"}]}, {"year": "1968", "text": "Civil Air Transport Flight 010 crashes near Shongshan Airport in Taiwan, killing 21 of the 63 people on board and one more on the ground.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Civil_Air_Transport_Flight_010\" class=\"mw-redirect\" title=\"Civil Air Transport Flight 010\">Civil Air Transport Flight 010</a> crashes near <a href=\"https://wikipedia.org/wiki/Songshan_Airport\" title=\"Songshan Airport\">Shongshan Airport</a> in Taiwan, killing 21 of the 63 people on board and one more on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Civil_Air_Transport_Flight_010\" class=\"mw-redirect\" title=\"Civil Air Transport Flight 010\">Civil Air Transport Flight 010</a> crashes near <a href=\"https://wikipedia.org/wiki/Songshan_Airport\" title=\"Songshan Airport\">Shongshan Airport</a> in Taiwan, killing 21 of the 63 people on board and one more on the ground.", "links": [{"title": "Civil Air Transport Flight 010", "link": "https://wikipedia.org/wiki/Civil_Air_Transport_Flight_010"}, {"title": "Songshan Airport", "link": "https://wikipedia.org/wiki/Songshan_Airport"}]}, {"year": "1978", "text": "The first computer bulletin board system is created (CBBS in Chicago).", "html": "1978 - The first computer <a href=\"https://wikipedia.org/wiki/Bulletin_board_system\" title=\"Bulletin board system\">bulletin board system</a> is created (<a href=\"https://wikipedia.org/wiki/CBBS\" title=\"CBBS\">CBBS</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>).", "no_year_html": "The first computer <a href=\"https://wikipedia.org/wiki/Bulletin_board_system\" title=\"Bulletin board system\">bulletin board system</a> is created (<a href=\"https://wikipedia.org/wiki/CBBS\" title=\"CBBS\">CBBS</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>).", "links": [{"title": "Bulletin board system", "link": "https://wikipedia.org/wiki/Bulletin_board_system"}, {"title": "CBBS", "link": "https://wikipedia.org/wiki/CBBS"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1983", "text": "The Ash Wednesday bushfires in Victoria and South Australia kill 75.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Ash_Wednesday_bushfires\" title=\"Ash Wednesday bushfires\">Ash Wednesday bushfires</a> in <a href=\"https://wikipedia.org/wiki/Victoria_(Australia)\" class=\"mw-redirect\" title=\"Victoria (Australia)\">Victoria</a> and <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a> kill 75.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ash_Wednesday_bushfires\" title=\"Ash Wednesday bushfires\">Ash Wednesday bushfires</a> in <a href=\"https://wikipedia.org/wiki/Victoria_(Australia)\" class=\"mw-redirect\" title=\"Victoria (Australia)\">Victoria</a> and <a href=\"https://wikipedia.org/wiki/South_Australia\" title=\"South Australia\">South Australia</a> kill 75.", "links": [{"title": "Ash Wednesday bushfires", "link": "https://wikipedia.org/wiki/Ash_Wednesday_bushfires"}, {"title": "Victoria (Australia)", "link": "https://wikipedia.org/wiki/Victoria_(Australia)"}, {"title": "South Australia", "link": "https://wikipedia.org/wiki/South_Australia"}]}, {"year": "1985", "text": "Hezbollah is founded.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> is founded.", "links": [{"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}]}, {"year": "1986", "text": "The Soviet liner MS <PERSON> runs aground in the Marlborough Sounds, New Zealand.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> liner <a href=\"https://wikipedia.org/wiki/MS_<PERSON>\" title=\"MS <PERSON>\">MS <i><PERSON></i></a> runs aground in the <a href=\"https://wikipedia.org/wiki/Marlborough_Sounds\" title=\"Marlborough Sounds\">Marlborough Sounds</a>, New Zealand.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> liner <a href=\"https://wikipedia.org/wiki/MS_<PERSON>\" title=\"<PERSON> <PERSON>\">MS <i><PERSON></i></a> runs aground in the <a href=\"https://wikipedia.org/wiki/Marlborough_Sounds\" title=\"Marlborough Sounds\">Marlborough Sounds</a>, New Zealand.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Marlborough Sounds", "link": "https://wikipedia.org/wiki/Marlborough_Sounds"}]}, {"year": "1986", "text": "China Airlines Flight 2265 crashes into the Pacific Ocean near Penghu Airport in Taiwan, killing all 13 aboard.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_2265\" title=\"China Airlines Flight 2265\">China Airlines Flight 2265</a> crashes into the Pacific Ocean near <a href=\"https://wikipedia.org/wiki/Penghu_Airport\" title=\"Penghu Airport\">Penghu Airport</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, killing all 13 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_2265\" title=\"China Airlines Flight 2265\">China Airlines Flight 2265</a> crashes into the Pacific Ocean near <a href=\"https://wikipedia.org/wiki/Penghu_Airport\" title=\"Penghu Airport\">Penghu Airport</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, killing all 13 aboard.", "links": [{"title": "China Airlines Flight 2265", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_2265"}, {"title": "Penghu Airport", "link": "https://wikipedia.org/wiki/Penghu_Airport"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1991", "text": "Nicaraguan Contras leader <PERSON> is assassinated in Managua.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> <a href=\"https://wikipedia.org/wiki/Contras\" title=\"Contras\"><PERSON>tras</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaraguan</a> <a href=\"https://wikipedia.org/wiki/Contras\" title=\"Contras\">Contras</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Managua\" title=\"Managua\">Managua</a>.", "links": [{"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Contras", "link": "https://wikipedia.org/wiki/Contras"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_Berm%C3%BAdez"}, {"title": "Managua", "link": "https://wikipedia.org/wiki/Managua"}]}, {"year": "1996", "text": "A Chicago-bound Amtrak train, the Capitol Limited, collides with a MARC commuter train bound for Washington, D.C., killing 11 people.", "html": "1996 - A Chicago-bound <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> train, the <i><a href=\"https://wikipedia.org/wiki/Capitol_Limited_(Amtrak_train)\" class=\"mw-redirect\" title=\"Capitol Limited (Amtrak train)\">Capitol Limited</a></i>, <a href=\"https://wikipedia.org/wiki/1996_Maryland_train_collision\" title=\"1996 Maryland train collision\">collides</a> with a <a href=\"https://wikipedia.org/wiki/MARC_Train\" title=\"MARC Train\">MARC</a> commuter train bound for Washington, D.C., killing 11 people.", "no_year_html": "A Chicago-bound <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> train, the <i><a href=\"https://wikipedia.org/wiki/Capitol_Limited_(Amtrak_train)\" class=\"mw-redirect\" title=\"Capitol Limited (Amtrak train)\">Capitol Limited</a></i>, <a href=\"https://wikipedia.org/wiki/1996_Maryland_train_collision\" title=\"1996 Maryland train collision\">collides</a> with a <a href=\"https://wikipedia.org/wiki/MARC_Train\" title=\"MARC Train\">MARC</a> commuter train bound for Washington, D.C., killing 11 people.", "links": [{"title": "Amtrak", "link": "https://wikipedia.org/wiki/Amtrak"}, {"title": "Capitol Limited (Amtrak train)", "link": "https://wikipedia.org/wiki/Capitol_Limited_(Amtrak_train)"}, {"title": "1996 Maryland train collision", "link": "https://wikipedia.org/wiki/1996_Maryland_train_collision"}, {"title": "MARC Train", "link": "https://wikipedia.org/wiki/MARC_Train"}]}, {"year": "1998", "text": "China Airlines Flight 676 crashes into a road and residential area near Chiang Kai-shek International Airport in Taiwan, killing all 196 aboard and six more on the ground.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_676\" title=\"China Airlines Flight 676\">China Airlines Flight 676</a> crashes into a road and residential area near <a href=\"https://wikipedia.org/wiki/Taoyuan_International_Airport\" title=\"Taoyuan International Airport\">Chiang Kai-shek International Airport</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, killing all 196 aboard and six more on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_676\" title=\"China Airlines Flight 676\">China Airlines Flight 676</a> crashes into a road and residential area near <a href=\"https://wikipedia.org/wiki/Taoyuan_International_Airport\" title=\"Taoyuan International Airport\">Chiang Kai-shek International Airport</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>, killing all 196 aboard and six more on the ground.", "links": [{"title": "China Airlines Flight 676", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_676"}, {"title": "Taoyuan International Airport", "link": "https://wikipedia.org/wiki/Taoyuan_International_Airport"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "2000", "text": "Emery Worldwide Airlines Flight 17 crashes near Sacramento Mather Airport in Rancho Cordova, California, killing all three aboard.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Emery_Worldwide_Airlines_Flight_17\" title=\"Emery Worldwide Airlines Flight 17\">Emery Worldwide Airlines Flight 17</a> crashes near <a href=\"https://wikipedia.org/wiki/Sacramento_Mather_Airport\" title=\"Sacramento Mather Airport\">Sacramento Mather Airport</a> in <a href=\"https://wikipedia.org/wiki/Rancho_Cordova,_California\" title=\"Rancho Cordova, California\">Rancho Cordova, California</a>, killing all three aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emery_Worldwide_Airlines_Flight_17\" title=\"Emery Worldwide Airlines Flight 17\">Emery Worldwide Airlines Flight 17</a> crashes near <a href=\"https://wikipedia.org/wiki/Sacramento_Mather_Airport\" title=\"Sacramento Mather Airport\">Sacramento Mather Airport</a> in <a href=\"https://wikipedia.org/wiki/Rancho_Cordova,_California\" title=\"Rancho Cordova, California\">Rancho Cordova, California</a>, killing all three aboard.", "links": [{"title": "Emery Worldwide Airlines Flight 17", "link": "https://wikipedia.org/wiki/Emery_Worldwide_Airlines_Flight_17"}, {"title": "Sacramento Mather Airport", "link": "https://wikipedia.org/wiki/Sacramento_Mather_Airport"}, {"title": "Rancho Cordova, California", "link": "https://wikipedia.org/wiki/Rancho_Cordova,_California"}]}, {"year": "2005", "text": "The Kyoto Protocol comes into force, following its ratification by Russia.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> comes into force, following its <a href=\"https://wikipedia.org/wiki/Ratification#Ratification_of_an_international_treaty\" title=\"Ratification\">ratification</a> by Russia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kyoto_Protocol\" title=\"Kyoto Protocol\">Kyoto Protocol</a> comes into force, following its <a href=\"https://wikipedia.org/wiki/Ratification#Ratification_of_an_international_treaty\" title=\"Ratification\">ratification</a> by Russia.", "links": [{"title": "Kyoto Protocol", "link": "https://wikipedia.org/wiki/Kyoto_Protocol"}, {"title": "Ratification", "link": "https://wikipedia.org/wiki/Ratification#Ratification_of_an_international_treaty"}]}, {"year": "2005", "text": "The National Hockey League cancels the entire 2004-05 regular season and playoffs.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> <a href=\"https://wikipedia.org/wiki/2004%E2%80%9305_NHL_lockout\" title=\"2004-05 NHL lockout\">cancels</a> the entire 2004-05 regular season and playoffs.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> <a href=\"https://wikipedia.org/wiki/2004%E2%80%9305_NHL_lockout\" title=\"2004-05 NHL lockout\">cancels</a> the entire 2004-05 regular season and playoffs.", "links": [{"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "2004-05 NHL lockout", "link": "https://wikipedia.org/wiki/2004%E2%80%9305_NHL_lockout"}]}, {"year": "2006", "text": "The last Mobile army surgical hospital (MASH) is decommissioned by the United States Army.", "html": "2006 - The last <a href=\"https://wikipedia.org/wiki/Mobile_army_surgical_hospital_(US)\" class=\"mw-redirect\" title=\"Mobile army surgical hospital (US)\">Mobile army surgical hospital</a> (MASH) is decommissioned by the United States Army.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Mobile_army_surgical_hospital_(US)\" class=\"mw-redirect\" title=\"Mobile army surgical hospital (US)\">Mobile army surgical hospital</a> (MASH) is decommissioned by the United States Army.", "links": [{"title": "Mobile army surgical hospital (US)", "link": "https://wikipedia.org/wiki/Mobile_army_surgical_hospital_(US)"}]}, {"year": "2013", "text": "A bomb blast at a market in Hazara Town, Quetta, Pakistan kills more than 80 people and injures 190 others.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/February_2013_Quetta_bombing\" title=\"February 2013 Quetta bombing\">bomb blast</a> at a market in <a href=\"https://wikipedia.org/wiki/Hazara_Town\" title=\"Hazara Town\">Hazara Town</a>, <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta, Pakistan</a> kills more than 80 people and injures 190 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/February_2013_Quetta_bombing\" title=\"February 2013 Quetta bombing\">bomb blast</a> at a market in <a href=\"https://wikipedia.org/wiki/Hazara_Town\" title=\"Hazara Town\">Hazara Town</a>, <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta, Pakistan</a> kills more than 80 people and injures 190 others.", "links": [{"title": "February 2013 Quetta bombing", "link": "https://wikipedia.org/wiki/February_2013_Quetta_bombing"}, {"title": "Hazara Town", "link": "https://wikipedia.org/wiki/Hazara_Town"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}]}, {"year": "2021", "text": "Five thousand people gathered in the town of Kherrata, Bejaia Province to mark the second anniversary of the Hirak protest movement. Demonstrations had been suspended because of the COVID-19 pandemic in Algeria.", "html": "2021 - Five thousand people gathered in the town of <a href=\"https://wikipedia.org/wiki/Kherrata\" title=\"Kherrata\">Kherrata</a>, <a href=\"https://wikipedia.org/wiki/Bejaia_Province\" class=\"mw-redirect\" title=\"Bejaia Province\">Bejaia Province</a> to mark the second anniversary of the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9321_Algerian_protests\" class=\"mw-redirect\" title=\"2019-21 Algerian protests\">Hirak protest movement</a>. Demonstrations had been suspended because of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Algeria\" title=\"COVID-19 pandemic in Algeria\">COVID-19 pandemic in Algeria</a>.", "no_year_html": "Five thousand people gathered in the town of <a href=\"https://wikipedia.org/wiki/Kherrata\" title=\"Kherrata\">Kherrata</a>, <a href=\"https://wikipedia.org/wiki/Bejaia_Province\" class=\"mw-redirect\" title=\"Bejaia Province\">Bejaia Province</a> to mark the second anniversary of the <a href=\"https://wikipedia.org/wiki/2019%E2%80%9321_Algerian_protests\" class=\"mw-redirect\" title=\"2019-21 Algerian protests\">Hirak protest movement</a>. Demonstrations had been suspended because of the <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic_in_Algeria\" title=\"COVID-19 pandemic in Algeria\">COVID-19 pandemic in Algeria</a>.", "links": [{"title": "Kherrata", "link": "https://wikipedia.org/wiki/Kherrata"}, {"title": "Bejaia Province", "link": "https://wikipedia.org/wiki/Bejaia_Province"}, {"title": "2019-21 Algerian protests", "link": "https://wikipedia.org/wiki/2019%E2%80%9321_Algerian_protests"}, {"title": "COVID-19 pandemic in Algeria", "link": "https://wikipedia.org/wiki/COVID-19_pandemic_in_Algeria"}]}], "Births": [{"year": "1222", "text": "<PERSON><PERSON><PERSON>, founder of Nichiren Buddhism (d. 1282)", "html": "1222 - <a href=\"https://wikipedia.org/wiki/Ni<PERSON>ren\" title=\"Ni<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founder of Nichiren Buddhism (d. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nichiren\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, founder of Nichiren Buddhism (d. 1282)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni<PERSON>ren"}]}, {"year": "1304", "text": "<PERSON><PERSON><PERSON>, Chinese emperor (d. 1332)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>gh_Tem%C3%BCr\" title=\"<PERSON><PERSON><PERSON>ü<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese emperor (d. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Tem%C3%BCr\" title=\"<PERSON><PERSON><PERSON>mür\"><PERSON><PERSON><PERSON></a>, Chinese emperor (d. 1332)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Tem%C3%BCr"}]}, {"year": "1331", "text": "<PERSON><PERSON><PERSON>, Italian political leader (d. 1406)", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian political leader (d. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian political leader (d. 1406)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1419", "text": "<PERSON>, Duke of Cleves (d. 1481)", "html": "1419 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Cleves\" title=\"<PERSON>, Duke of Cleves\"><PERSON>, Duke of Cleves</a> (d. 1481)", "links": [{"title": "<PERSON>, Duke of Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_C<PERSON>"}]}, {"year": "1470", "text": "<PERSON>, Duke of Brunswick-Lüneburg (d. 1540)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1540)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1471", "text": "<PERSON><PERSON><PERSON><PERSON>, emperor of the Vijayanagara Empire (d. 1529)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/Krishnadevaraya\" title=\"Krishna<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of the Vijayanagara Empire (d. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krishnadevaraya\" title=\"Krishna<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, emperor of the Vijayanagara Empire (d. 1529)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krishnadevaraya"}]}, {"year": "1497", "text": "<PERSON>, German astronomer, theologian, and academic (d. 1560)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer, theologian, and academic (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer, theologian, and academic (d. 1560)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON>, Austrian cartographer and instrument maker (d. 1574)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cartographer and instrument maker (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cartographer and instrument maker (d. 1574)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON><PERSON>, French admiral (d. 1572)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_de_Coligny\" title=\"Gaspard II de Coligny\"><PERSON><PERSON> II de Coligny</a>, French admiral (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_II_de_Coligny\" title=\"Gaspard II de Coligny\"><PERSON><PERSON> II de Coligny</a>, French admiral (d. 1572)", "links": [{"title": "Gaspard II de Coligny", "link": "https://wikipedia.org/wiki/<PERSON>pard_II_de_Coligny"}]}, {"year": "1543", "text": "<PERSON><PERSON><PERSON>, Japanese painter and educator (d. 1590)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_E<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter and educator (d. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON><PERSON>ku"}]}, {"year": "1620", "text": "<PERSON>, Elector of Brandenburg (d. 1688)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON></a>, Elector of Brandenburg (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON></a>, Elector of Brandenburg (d. 1688)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1643", "text": "<PERSON>, English archbishop (d. 1714)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop (d. 1714)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1698", "text": "<PERSON>, French mathematician, geophysicist, and astronomer (d. 1758)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, geophysicist, and astronomer (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, geophysicist, and astronomer (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1727", "text": "<PERSON><PERSON>, Austrian botanist, chemist, and mycologist (d. 1817)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian botanist, chemist, and mycologist (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian botanist, chemist, and mycologist (d. 1817)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian publisher and engraver (d. 1813)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian publisher and engraver (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian publisher and engraver (d. 1813)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON>, French general (d. 1804)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1804)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1774", "text": "<PERSON>, French violinist and composer (d. 1830)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist and composer (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, Russian Grand Duchess (d. 1859)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1786%E2%80%931859)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1786-1859)\"><PERSON></a>, Russian Grand Duchess (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1786%E2%80%931859)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1786-1859)\"><PERSON></a>, Russian Grand Duchess (d. 1859)", "links": [{"title": "Grand Duchess <PERSON> of Russia (1786-1859)", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1786%E2%80%931859)"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, American mystic and philosopher (d. 1866)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>nea<PERSON>_Quimby\" class=\"mw-redirect\" title=\"Phi<PERSON><PERSON> Quimby\"><PERSON><PERSON><PERSON></a>, American mystic and philosopher (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uimb<PERSON>\" class=\"mw-redirect\" title=\"Phinea<PERSON> Quimby\"><PERSON><PERSON><PERSON></a>, American mystic and philosopher (d. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_<PERSON>ui<PERSON>y"}]}, {"year": "1804", "text": "<PERSON>, German physiologist and zoologist (d. 1885)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and zoologist (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and zoologist (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American colonel and politician, 18th Vice President of the United States (d. 1875)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 18th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 18th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1821", "text": "<PERSON>, German explorer and scholar (d. 1865)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and scholar (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German explorer and scholar (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, English biologist and statistician (d. 1911)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and statistician (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and statistician (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, Slovenian lawyer, geographer, and cartographer (d. 1879)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian lawyer, geographer, and cartographer (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian lawyer, geographer, and cartographer (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, German poet and author (d. 1886)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Norwegian painter (d. 1902)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Russian author, playwright, and journalist (d. 1895)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, playwright, and journalist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author, playwright, and journalist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, German biologist, physician, and philosopher (d. 1919)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biologist, physician, and philosopher (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German biologist, physician, and philosopher (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American journalist, historian, and author (d. 1918)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, historian, and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, historian, and author (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French painter (d. 1927)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American engineer and businessman, founded Cadillac and Lincoln (d. 1932)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> and <a href=\"https://wikipedia.org/wiki/Lincoln_(automobile)\" class=\"mw-redirect\" title=\"Lincoln (automobile)\">Lincoln</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Cadillac\" title=\"Cadillac\">Cadillac</a> and <a href=\"https://wikipedia.org/wiki/Lincoln_(automobile)\" class=\"mw-redirect\" title=\"Lincoln (automobile)\">Lincoln</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cadillac", "link": "https://wikipedia.org/wiki/Cadillac"}, {"title": "Lincoln (automobile)", "link": "https://wikipedia.org/wiki/<PERSON>_(automobile)"}]}, {"year": "1845", "text": "<PERSON>, American journalist and explorer (d. 1924)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, American journalist and explorer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, American journalist and explorer (d. 1924)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}]}, {"year": "1848", "text": "<PERSON>, Dutch botanist, geneticist, and academic (d. 1935)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist, geneticist, and academic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch botanist, geneticist, and academic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "Octave <PERSON>, French journalist, novelist, and playwright (d. 1917)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Octave_Mirbeau\" title=\"Octave Mirbeau\">Octave <PERSON><PERSON></a>, French journalist, novelist, and playwright (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_<PERSON><PERSON><PERSON>\" title=\"Octave Mirbeau\">Octave <PERSON></a>, French journalist, novelist, and playwright (d. 1917)", "links": [{"title": "Octave <PERSON>", "link": "https://wikipedia.org/wiki/Octave_<PERSON><PERSON><PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, American academic, founded Phi Mu Alpha Sinfonia (d. 1920)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ssi<PERSON>\"><PERSON><PERSON><PERSON></a>, American academic, founded <a href=\"https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia\" title=\"Phi Mu Alpha Sinfonia\">Phi Mu Alpha Sinfonia</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ssi<PERSON>\"><PERSON><PERSON><PERSON></a>, American academic, founded <a href=\"https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia\" title=\"Phi Mu Alpha Sinfonia\">Phi Mu Alpha Sinfonia</a> (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Phi Mu Alpha Sinfonia", "link": "https://wikipedia.org/wiki/Phi_Mu_Alpha_Sinfonia"}]}, {"year": "1866", "text": "<PERSON>, American baseball player and manager (d. 1940)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1866)\" title=\"<PERSON> (baseball, born 1866)\"><PERSON></a>, American baseball player and manager (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1866)\" title=\"<PERSON> (baseball, born 1866)\"><PERSON></a>, American baseball player and manager (d. 1940)", "links": [{"title": "<PERSON> (baseball, born 1866)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1866)"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Serbian journalist and author (d. 1908)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian journalist and author (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian journalist and author (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vi%C4%87"}]}, {"year": "1876", "text": "<PERSON><PERSON> <PERSON><PERSON>, English historian and academic (d. 1962)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and academic (d. 1962)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Irish seaman and Antarctic explorer (d. 1938)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish seaman and Antarctic explorer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Irish seaman and Antarctic explorer (d. 1938)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}]}, {"year": "1878", "text": "<PERSON>, English occultist and illustrator (d. 1951)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English occultist and illustrator (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English occultist and illustrator (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Italian-American mob boss (d. 1920)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American director and producer (d. 1951)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American actress (d. 1962)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German eugenicist and academic (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_K._G%C3%BCnt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German eugenicist and academic (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_K._G%C3%BCnt<PERSON>\" title=\"Hans <PERSON>ü<PERSON>her\"><PERSON></a>, German eugenicist and academic (d. 1968)", "links": [{"title": "<PERSON> F. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._K._G%C3%BCnther"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American actress and producer (d. 1974)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON>, French super-centenarian (d. 2010)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French super-centenarian (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French super-centenarian (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American singer-songwriter and conductor (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and conductor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and conductor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actor (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Morris\" title=\"<PERSON> Morris\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chester_Morris\" title=\"<PERSON> Morris\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON> Morris", "link": "https://wikipedia.org/wiki/Chester_Morris"}]}, {"year": "1902", "text": "<PERSON>, South African cricketer (d. 1968)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American ventriloquist and actor (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ventriloquist and actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ventriloquist and actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edgar_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American actor and singer (d. 1948)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American historian and diplomat, United States Ambassador to the Soviet Union (d. 2005)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Soviet_Union\" class=\"mw-redirect\" title=\"United States Ambassador to the Soviet Union\">United States Ambassador to the Soviet Union</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Soviet_Union\" class=\"mw-redirect\" title=\"United States Ambassador to the Soviet Union\">United States Ambassador to the Soviet Union</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to the Soviet Union", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Soviet_Union"}]}, {"year": "1905", "text": "<PERSON>, English Women's Royal Air Force officer (d. 1985)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)\" title=\"<PERSON> (WRAF officer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)\" class=\"mw-redirect\" title=\"Women's Royal Air Force (World War II)\">Women's Royal Air Force</a> officer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)\" title=\"<PERSON> (WRAF officer)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)\" class=\"mw-redirect\" title=\"Women's Royal Air Force (World War II)\">Women's Royal Air Force</a> officer (d. 1985)", "links": [{"title": "<PERSON> (WRAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(WRAF_officer)"}, {"title": "Women's Royal Air Force (World War II)", "link": "https://wikipedia.org/wiki/Women%27s_Royal_Air_Force_(World_War_II)"}]}, {"year": "1906", "text": "<PERSON>, Russian-Czechoslovak-British chess player (d. 1944)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Czechoslovak-British chess player (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Czechoslovak-British chess player (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actor and director (d. 1982)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American businessman, co-founded McDonald's (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McDonald's", "link": "https://wikipedia.org/wiki/<PERSON>%27s"}]}, {"year": "1914", "text": "<PERSON>, American country music singer-songwriter and actor (d. 1982)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and actor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and actor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American pianist and composer (d. 1996)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Danish-French actor and composer (d. 1989)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-French actor and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-French actor and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American general (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French race car driver (d. 1959)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English priest and academic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, German-American actress, singer, and dancer (d. 1981)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actress, singer, and dancer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actress, singer, and dancer (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, German soldier and pilot (d. 1950)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and pilot (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and pilot (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Polish-Israeli sculptor and painter (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli sculptor and painter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli sculptor and painter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American illustrator and experiment film maker (d. 1990)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and experiment film maker (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and experiment film maker (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, German-Dutch holocaust victim (d. 1945)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Frank\"><PERSON><PERSON></a>, German-Dutch <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">holocaust</a> victim (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Frank\"><PERSON><PERSON></a>, German-Dutch <a href=\"https://wikipedia.org/wiki/Holocaust\" class=\"mw-redirect\" title=\"Holocaust\">holocaust</a> victim (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Holocaust", "link": "https://wikipedia.org/wiki/Holocaust"}]}, {"year": "1926", "text": "<PERSON>, English actor and director (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English actress (d. 2022)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, English actress (d. 2022)", "links": [{"title": "June Brown", "link": "https://wikipedia.org/wiki/June_Brown"}]}, {"year": "1929", "text": "<PERSON>, Austrian footballer and architect (d. 1980)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and architect (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and architect (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian-English poet and educator (d. 2010)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Australian-English poet and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Australian-English poet and educator (d. 2010)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1931", "text": "<PERSON>, American singer-songwriter and pianist (d. 2002)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2006)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Japanese actor and singer (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor and singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor and singer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Sierra Leonean economist, lawyer, and politician, 3rd President of Sierra Leone (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean economist, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sierra Leonean economist, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sierra_Leone\" title=\"President of Sierra Leone\">President of Sierra Leone</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Sierra Leone", "link": "https://wikipedia.org/wiki/President_of_Sierra_Leone"}]}, {"year": "1932", "text": "<PERSON>, American actress, singer, and dancer (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yler"}]}, {"year": "1934", "text": "<PERSON>, American author and academic (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August Coppola\">August <PERSON></a>, American author and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>\" title=\"August Coppola\">August <PERSON></a>, American author and academic (d. 2009)", "links": [{"title": "August <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>ola"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American golfer (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English-American actor and director (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1935", "text": "<PERSON>, American actor, singer, and politician  (d. 1998)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American activist, co-founded The Farm (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/The_Farm_(Tennessee)\" title=\"The Farm (Tennessee)\">The Farm</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/The_Farm_(Tennessee)\" title=\"The Farm (Tennessee)\">The Farm</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The Farm (Tennessee)", "link": "https://wikipedia.org/wiki/The_Farm_(Tennessee)"}]}, {"year": "1935", "text": "<PERSON>, American colonel and engineer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Parkinson\" title=\"<PERSON>\"><PERSON></a>, American colonel and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Parkinson\" title=\"<PERSON> Parkinson\"><PERSON></a>, American colonel and engineer", "links": [{"title": "Bradford Parkinson", "link": "https://wikipedia.org/wiki/Bradford_Parkinson"}]}, {"year": "1935", "text": "<PERSON>, American painter and sculptor (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American businessman and investor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and investor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and investor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, British novelist, critic, and biographer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_writer)\" title=\"<PERSON> (British writer)\"><PERSON></a>, British novelist, critic, and biographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_writer)\" title=\"<PERSON> (British writer)\"><PERSON></a>, British novelist, critic, and biographer", "links": [{"title": "<PERSON> (British writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_writer)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Soviet aviator and cosmonaut (d. 1961)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet aviator and cosmonaut (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet aviator and cosmonaut (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Russian-German mathematician and academic (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German mathematician and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German mathematician and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American composer and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Filipino lawyer and judge", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and judge", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_A<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, German mountaineer (d. 1979)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mountaineer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mountaineer (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z"}]}, {"year": "1941", "text": "<PERSON>, North Korean commander and politician, 2nd Supreme Leader of North Korea (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">Supreme Leader of North Korea</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">Supreme Leader of North Korea</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of leaders of North Korea", "link": "https://wikipedia.org/wiki/List_of_leaders_of_North_Korea"}]}, {"year": "1942", "text": "<PERSON>, Taiwanese politician (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>u\" title=\"<PERSON>u\"><PERSON></a>, Taiwanese politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>u\" title=\"<PERSON>u\"><PERSON></a>, Taiwanese politician (d. 2024)", "links": [{"title": "<PERSON>u", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-fu"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Welsh farmer and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (British politician)\"><PERSON><PERSON></a>, Welsh farmer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (British politician)\"><PERSON><PERSON></a>, Welsh farmer and politician", "links": [{"title": "<PERSON><PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1944", "text": "<PERSON>, American novelist and short story writer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Cape Verdean politician, 2nd President of Cape Verde (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Mascarenhas_Monteiro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cape Verdean politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Cape_Verde\" class=\"mw-redirect\" title=\"List of heads of state of Cape Verde\">President of Cape Verde</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Mascarenhas_Monteiro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cape Verdean politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Cape_Verde\" class=\"mw-redirect\" title=\"List of heads of state of Cape Verde\">President of Cape Verde</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Mascarenhas_Monteiro"}, {"title": "List of heads of state of Cape Verde", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Cape_Verde"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Czech politician (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler and coach (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er<PERSON>\" title=\"<PERSON><PERSON><PERSON> Masateru\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ma<PERSON>eru\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler and coach (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sateru"}]}, {"year": "1950", "text": "<PERSON>, Welsh politician, Secretary of State for Wales", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Wales"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and producer (d. 2019)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English footballer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter <PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peter Kitchen\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English musician, songwriter, and producer (d. 2015)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician, songwriter, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician, songwriter, and producer (d. 2015)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Canadian ice hockey player and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American video game designer, co-founded Sierra Entertainment", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, co-founded <a href=\"https://wikipedia.org/wiki/Sierra_Entertainment\" title=\"Sierra Entertainment\">Sierra Entertainment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer, co-founded <a href=\"https://wikipedia.org/wiki/Sierra_Entertainment\" title=\"Sierra Entertainment\">Sierra Entertainment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sierra Entertainment", "link": "https://wikipedia.org/wiki/Sierra_Entertainment"}]}, {"year": "1954", "text": "<PERSON>, Scottish author and playwright (d. 2013)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and playwright (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and playwright (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, American model and actress (d. 1996)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American model and actress (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Jamaican cricketer and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, New Zealand director and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, New Zealand director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, New Zealand director and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American rapper and actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Ice-T\" title=\"Ice-T\"><PERSON><PERSON><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ice-T\" title=\"Ice-T\"><PERSON><PERSON><PERSON></a>, American rapper and actor", "links": [{"title": "Ice-T", "link": "https://wikipedia.org/wiki/Ice-T"}]}, {"year": "1958", "text": "<PERSON>, Brazilian basketball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American basketball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American tennis player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English guitarist and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Finnish journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rvi"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter (d. 2004)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "Bebeto", "link": "https://wikipedia.org/wiki/Bebeto"}]}, {"year": "1964", "text": "<PERSON>, English actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Cuban-American musician and songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English author and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American attorney and pundit", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and pundit", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and pundit", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Bosnian football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Zoran_%C4%8Campara\" title=\"<PERSON>oran Čampara\"><PERSON><PERSON></a>, Bosnian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zoran_%C4%8Campara\" title=\"Zoran Čampara\"><PERSON><PERSON></a>, Bosnian football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_%C4%8Campara"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Japanese actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian sprinter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Portuguese footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>z"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Japanese singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1977", "text": "<PERSON>, Irish-American computer scientist, founded Freenet", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, Irish-American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Freenet\" class=\"mw-redirect\" title=\"Freenet\">Freenet</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, Irish-American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Freenet\" class=\"mw-redirect\" title=\"Freenet\">Freenet</a>", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}, {"title": "Freenet", "link": "https://wikipedia.org/wiki/Freenet"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Green\" title=\"Ahman Green\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Green\" title=\"Ahman Green\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Russian ice hockey player and executive", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Belgian high jumper and chemist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian high jumper and chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian high jumper and chemist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia_<PERSON>ebaut"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor, singer, and puppeteer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and puppeteer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and puppeteer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>mat\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American-South Korean singer and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Italian motorcycle racer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, French-American musician and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON> III\"><PERSON><PERSON><PERSON> <PERSON><PERSON> III</a>, French-American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON> III\"><PERSON><PERSON><PERSON> <PERSON><PERSON> III</a>, French-American musician and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Swedish sprint hurdler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sprint hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sprint hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Qyn<PERSON> Woods\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Woods\" title=\"Qyn<PERSON> Woods\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Qyntel Woods", "link": "https://wikipedia.org/wiki/Qyntel_Woods"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Estonian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American rapper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>asco\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>asco"}]}, {"year": "1982", "text": "<PERSON><PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, English model, actress, and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Agyness_Deyn\" title=\"Agyness Deyn\">A<PERSON><PERSON> <PERSON></a>, English model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agyness_Deyn\" title=\"Agyness Deyn\"><PERSON><PERSON><PERSON></a>, English model, actress, and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agyness_<PERSON>yn"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish ice hockey player and coach", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ruutu\" title=\"<PERSON><PERSON> Ruutu\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ruutu\" title=\"<PERSON><PERSON> Ruutu\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tu<PERSON>_Ruutu"}]}, {"year": "1984", "text": "<PERSON>, Swedish tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Tunisian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ouss<PERSON>_<PERSON>\" title=\"Oussama Mellouli\"><PERSON><PERSON><PERSON></a>, Tunisian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uss<PERSON>_<PERSON>\" title=\"Oussama Mellouli\"><PERSON><PERSON><PERSON></a>, Tunisian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oussama_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1985", "text": "<PERSON>, American golfer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dutch footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Uruguayan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_God%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_God%C3%ADn"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player (d. 2008)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Singaporean swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American politician and filmmaker", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Tanzanian basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Thabeet\" title=\"<PERSON><PERSON><PERSON> Thabeet\"><PERSON><PERSON><PERSON></a>, Tanzanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Thabeet\" title=\"<PERSON><PERSON><PERSON> Thabeet\"><PERSON><PERSON><PERSON></a>, Tanzanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hab<PERSON>t"}]}, {"year": "1988", "text": "<PERSON>, Spanish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego <PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>l"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/Den%C3%<PERSON><PERSON>_(footballer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, German ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Korb<PERSON>_<PERSON>\" title=\"Korbinian Holzer\"><PERSON><PERSON><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korbinian_<PERSON>\" title=\"Korbinian Holzer\"><PERSON><PERSON><PERSON></a>, German ice hockey player", "links": [{"title": "Korb<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chinese table tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ji<PERSON>\"><PERSON></a>, Chinese table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jike\"><PERSON></a>, Chinese table tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, South Korean actor and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yun"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON> <PERSON>nd, Canadian singer-songwriter and producer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/The_Weeknd\" title=\"The Weeknd\">The Weeknd</a>, Canadian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Weeknd\" title=\"The Weeknd\">The Weeknd</a>, Canadian singer-songwriter and producer", "links": [{"title": "The Weeknd", "link": "https://wikipedia.org/wiki/The_Weeknd"}]}, {"year": "1991", "text": "<PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Canales"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Danish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Zs%C3%B3fia_Sus%C3%A1nyi\" title=\"Zsófia Susányi\"><PERSON>só<PERSON>sányi</a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zs%C3%B3fia_Sus%C3%A1nyi\" title=\"Zsófia Susányi\"><PERSON>só<PERSON></a>, Hungarian tennis player", "links": [{"title": "Zsófia Susányi", "link": "https://wikipedia.org/wiki/Zs%C3%B3fia_Sus%C3%A1nyi"}]}, {"year": "1994", "text": "<PERSON><PERSON>, German tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Italian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American singer and songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ava_Max\" title=\"Ava Max\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ava_Max\" title=\"Ava Max\"><PERSON></a>, American singer and songwriter", "links": [{"title": "Ava Max", "link": "https://wikipedia.org/wiki/Ava_Max"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American rapper", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, German tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Car<PERSON>_Witth%C3%B6ft\" title=\"<PERSON><PERSON> Witthöft\"><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Car<PERSON>_Witth%C3%B6ft\" title=\"<PERSON><PERSON> Witthöft\"><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carina_Witth%C3%B6ft"}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jordan_Greenway\" title=\"Jordan Greenway\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Greenway\" title=\"Jordan Greenway\"><PERSON></a>, American ice hockey player", "links": [{"title": "Jordan Greenway", "link": "https://wikipedia.org/wiki/Jordan_Greenway"}]}, {"year": "1998", "text": "<PERSON>, South Korean volleyball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON> H<PERSON>-jin\"><PERSON></a>, South Korean volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jin\" title=\"An Hye-jin\"><PERSON></a>, South Korean volleyball player", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-jin"}]}, {"year": "1998", "text": "<PERSON>, South Korean diver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diver)\" title=\"<PERSON> (diver)\"><PERSON></a>, South Korean diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diver)\" title=\"<PERSON> (diver)\"><PERSON></a>, South Korean diver", "links": [{"title": "<PERSON> (diver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(diver)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Cameroonian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>go\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Norwegian singer, songwriter and music producer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer, songwriter and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer, songwriter and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Jamaican singer, songwriter and rapper", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>ffee\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican singer, songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican singer, songwriter and rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> White\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> White\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Japanese tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ito"}]}], "Deaths": [{"year": "549", "text": "<PERSON>, Chinese general (b. 483)", "html": "549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liang_Dynasty)\" class=\"mw-redirect\" title=\"<PERSON> (Liang Dynasty)\"><PERSON></a>, Chinese general (b. 483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liang_Dynasty)\" class=\"mw-redirect\" title=\"<PERSON> (Liang Dynasty)\"><PERSON></a>, Chinese general (b. 483)", "links": [{"title": "<PERSON> (Liang Dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liang_Dynasty)"}]}, {"year": "902", "text": "<PERSON> the Younger, Byzantine saint (b. 875)", "html": "902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Byzantine saint (b. 875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, Byzantine saint (b. 875)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1184", "text": "<PERSON> Dover, Archbishop of Canterbury", "html": "1184 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Dover\" title=\"<PERSON> of Dover\"><PERSON> Dover</a>, Archbishop of Canterbury", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Dover\" title=\"<PERSON> Dover\"><PERSON> Dover</a>, Archbishop of Canterbury", "links": [{"title": "<PERSON> Dover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1247", "text": "<PERSON>, Landgrave of Thuringia (b. 1204)", "html": "1247 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" class=\"mw-redirect\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a> (b. 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Thuringia\" class=\"mw-redirect\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON>, Landgrave of Thuringia</a> (b. 1204)", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1279", "text": "<PERSON><PERSON><PERSON> of Portugal (b. 1210)", "html": "1279 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_III_of_Portugal\" title=\"<PERSON><PERSON><PERSON> III of Portugal\"><PERSON><PERSON><PERSON> III of Portugal</a> (b. 1210)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/A<PERSON>nso_III_of_Portugal"}]}, {"year": "1281", "text": "<PERSON> of Hohenberg, queen consort of Germany (b. c. 1225)", "html": "1281 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hohenberg\" title=\"Gertrude of Hohenberg\"><PERSON> of Hohenberg</a>, queen consort of Germany (b. c. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Hohen<PERSON>\" title=\"Gertrude of Hohenberg\">Gertrude of Hohenberg</a>, queen consort of Germany (b. c. 1225)", "links": [{"title": "<PERSON> of Hohenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1390", "text": "<PERSON>, <PERSON><PERSON> (b. 1309)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (b. 1309)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (b. 1309)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1391", "text": "<PERSON>, Byzantine emperor (b. 1332)", "html": "1391 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1332)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1531", "text": "<PERSON>, German mathematician and astronomer (b. 1452)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ffler\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ffler\" title=\"<PERSON>\"><PERSON></a>, German mathematician and astronomer (b. 1452)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_St%C3%B6ffler"}]}, {"year": "1560", "text": "<PERSON>, French cardinal and diplomat (b. 1493)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and diplomat (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and diplomat (b. 1493)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "<PERSON><PERSON><PERSON>, Spanish explorer (b. 1509)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>%C3%A9nez_de_Quesada\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish explorer (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nez_de_Quesada\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish explorer (b. 1509)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9nez_de_Quesada"}]}, {"year": "1645", "text": "<PERSON><PERSON><PERSON>, Spanish general and politician, 24th Governor of the Duchy of Milan (b. 1585)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1585-1645)\"><PERSON><PERSON><PERSON></a>, Spanish general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"Governor of the Duchy of Milan\">Governor of the Duchy of Milan</a> (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (1585-1645)\"><PERSON><PERSON><PERSON></a>, Spanish general and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan\" class=\"mw-redirect\" title=\"Governor of the Duchy of Milan\">Governor of the Duchy of Milan</a> (b. 1585)", "links": [{"title": "<PERSON><PERSON><PERSON> (1585-1645)", "link": "https://wikipedia.org/wiki/Gonzalo_Fern%C3%A1ndez_de_C%C3%B3rdoba_(1585%E2%80%931645)"}, {"title": "Governor of the Duchy of Milan", "link": "https://wikipedia.org/wiki/Governor_of_the_Duchy_of_Milan"}]}, {"year": "1710", "text": "<PERSON><PERSON><PERSON>, French bishop and author (b. 1632)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French bishop and author (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French bishop and author (b. 1632)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esprit_Fl%C3%A9chier"}]}, {"year": "1721", "text": "<PERSON> the Younger, English politician, Secretary of State for the Southern Department (b. 1686)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1686)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1754", "text": "<PERSON>, English physician (b. 1673)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Swedish general (b. 1758)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\"><PERSON></a>, Swedish general (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\"><PERSON></a>, Swedish general (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln"}]}, {"year": "1862", "text": "<PERSON>, American lawyer and politician, 13th Governor of New Jersey, 23rd Speaker of the United States House of Representatives (b. 1796)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, 23rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, 23rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1898", "text": "<PERSON>, Irish-New Zealand journalist, poet, and politician (b. 1843)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand journalist, poet, and politician (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand journalist, poet, and politician (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French merchant and politician, 7th President of France (b. 1841)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Faure\" title=\"<PERSON>\"><PERSON></a>, French merchant and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Faure\" title=\"<PERSON>\"><PERSON></a>, French merchant and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Faure"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet and educator, Nobel Prize laureate (b. 1835)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Giosu%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giosu%C3%A<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet and educator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giosu%C3%A8_<PERSON><PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1912", "text": "<PERSON> of Japan, Russian-Japanese monk and saint (b. 1836)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Japan\" title=\"<PERSON> of Japan\"><PERSON> of Japan</a>, Russian-Japanese monk and saint (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Japan\" title=\"<PERSON> of Japan\"><PERSON> of Japan</a>, Russian-Japanese monk and saint (b. 1836)", "links": [{"title": "Nicholas of Japan", "link": "https://wikipedia.org/wiki/Nicholas_of_Japan"}]}, {"year": "1917", "text": "Octave <PERSON>, French journalist, novelist, and playwright (b. 1848)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Octave_Mirbeau\" title=\"Octave Mirbeau\">Octave <PERSON><PERSON><PERSON></a>, French journalist, novelist, and playwright (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octave_Mir<PERSON><PERSON>\" title=\"Octave Mirbeau\">Octave <PERSON></a>, French journalist, novelist, and playwright (b. 1848)", "links": [{"title": "Octave <PERSON>", "link": "https://wikipedia.org/wiki/Octave_<PERSON><PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Ukrainian actress (b. 1893)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian actress (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian actress (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vera_<PERSON>a"}]}, {"year": "1928", "text": "<PERSON>, American actor and dancer (b. 1856)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (b. 1856)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1932", "text": "<PERSON>, French academic and politician, Nobel Prize laureate (b. 1841)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1932", "text": "<PERSON>, American-English financier and philanthropist (b. 1862)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English financier and philanthropist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English financier and philanthropist (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German opera singer and actress (b. 1872)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German opera singer and actress (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German opera singer and actress (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frida_Felser"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1870)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Polish-American pianist and composer (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American baseball player (b. 1891)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American educator, school administrator, and businessperson (b. 1865)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, school administrator, and businessperson (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator, school administrator, and businessperson (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American singer-songwriter and actor (b. 1911)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actor (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smiley_Burnette"}]}, {"year": "1974", "text": "<PERSON>, Canadian-American engineer, designed the M1 Garand Rifle (b. 1888)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer, designed the <a href=\"https://wikipedia.org/wiki/M1_Garand\" title=\"M1 Garand\">M1 Garand Rifle</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer, designed the <a href=\"https://wikipedia.org/wiki/M1_Garand\" title=\"M1 Garand\">M1 Garand Rifle</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "M1 Garand", "link": "https://wikipedia.org/wiki/M1_Garand"}]}, {"year": "1975", "text": "<PERSON>, American hurdler and coach (b. 1903)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and coach (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, bishop, Church of Uganda, martyr (b. c.1922)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, bishop, Church of Uganda, martyr (b. c.1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, bishop, Church of Uganda, martyr (b. c.1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian mathematician (b. 1905)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian mathematician (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3zsa_P%C3%A9ter"}]}, {"year": "1980", "text": "<PERSON>, German physicist and chemist (b. 1895)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and chemist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and chemist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_H%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Bangladeshi general (b. 1918)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/M._A._G._<PERSON>smani\" title=\"M. A. G. Osmani\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi general (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M._A._<PERSON><PERSON>_<PERSON>\" title=\"M. A. G. <PERSON>smani\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi general (b. 1918)", "links": [{"title": "<PERSON>. <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Chinese writer, educator, and politician (b. 1894)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer, educator, and politician (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer, educator, and politician (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American painter and activist (b. 1958)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and activist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and activist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Nicaraguan lieutenant and engineer (b. 1932)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan lieutenant and engineer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan lieutenant and engineer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_Berm%C3%BAdez"}]}, {"year": "1992", "text": "<PERSON>, English novelist, short story writer (b. 1940)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, short story writer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Brazilian politician, 22nd President of Brazil (b. 1917)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/J%C3%A2nio_Quadros\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A2nio_Quadros\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A2nio_Quadros"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1992", "text": "<PERSON>, Norwegian-Swedish economist and statistician (b. 1908)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Swedish economist and statistician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Swedish economist and statistician (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Argentinian painter and sculptor (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian painter and sculptor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian painter and sculptor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor and author (b. 1932)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American lawyer and politician, 32nd Governor of California (b. 1905)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">Governor of California</a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1915)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Chinese-American physicist and academic (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American physicist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Chien-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Chinese-American physicist and academic (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American toxicologist and public health researcher (b. 1908)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Toxicology\" title=\"Toxicology\">toxicologist</a> and <a href=\"https://wikipedia.org/wiki/Public_health\" title=\"Public health\">public health</a> researcher (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Toxicology\" title=\"Toxicology\">toxicologist</a> and <a href=\"https://wikipedia.org/wiki/Public_health\" title=\"Public health\">public health</a> researcher (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Toxicology", "link": "https://wikipedia.org/wiki/Toxicology"}, {"title": "Public health", "link": "https://wikipedia.org/wiki/Public_health"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Taiwanese politician (b. 1927)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>u_<PERSON>-<PERSON>\" title=\"Sheu Yuan-dong\"><PERSON><PERSON></a>, Taiwanese politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheu_<PERSON>-<PERSON>\" title=\"<PERSON>u Yuan-dong\"><PERSON><PERSON></a>, Taiwanese politician (b. 1927)", "links": [{"title": "Sheu Yuan-dong", "link": "https://wikipedia.org/wiki/Sheu_<PERSON>-dong"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American actress (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Day\"><PERSON><PERSON></a>, American actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Day\"><PERSON><PERSON></a>, American actress (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Russian-French actress and singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Norwegian-American businessman, founded PING (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_(golf)\" title=\"<PERSON> (golf)\">PING</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_(golf)\" title=\"<PERSON> (golf)\">PING</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> (golf)", "link": "https://wikipedia.org/wiki/<PERSON>_(golf)"}]}, {"year": "2001", "text": "<PERSON>, American director and producer (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American gynecologist and sexologist (b. 1915)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Masters\"><PERSON></a>, American gynecologist and sexologist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"William Masters\"><PERSON></a>, American gynecologist and sexologist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English footballer and manager (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor and composer (b. 1955)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American singer-songwriter (b. 1937)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American wrestler (b. 1966)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, German-American football player and coach (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American football player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American football player and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, South Korean cardinal (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>\"><PERSON></a>, South Korean cardinal (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>\"><PERSON></a>, South Korean cardinal (b. 1921)", "links": [{"title": "<PERSON>wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>wan"}]}, {"year": "2011", "text": "<PERSON>, American actor (b. 1922)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lesser\"><PERSON></a>, American actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lesser\"><PERSON></a>, American actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Lithuanian poet and playwright (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian poet and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian poet and playwright (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Justin<PERSON>_Marcinkevi%C4%8Dius"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and coach (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American model, actress, and fashion designer (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and fashion designer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and fashion designer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American swimmer and lieutenant (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and lieutenant (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and lieutenant (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American journalist (b. 1968)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Guyanese footballer (b. 1991)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Guyanese footballer (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Guyanese footballer (b. 1991)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Russian philosopher and author (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian philosopher and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian philosopher and author (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ants"}]}, {"year": "2013", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German guitarist (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German guitarist (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American author (b. 1946)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>(author)"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Algerian-Italian director, producer, and screenwriter (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-Italian director, producer, and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Algerian-Italian director, producer, and screenwriter (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, Deputy Chief Minister of Maharashtra (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Deputy Chief Minister of Maharashtra\">Deputy Chief Minister of Maharashtra</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Deputy Chief Minister of Maharashtra\">Deputy Chief Minister of Maharashtra</a> (b. 1957)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Mexican actress and singer (b. 1971)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican actress and singer (b. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Egyptian politician and diplomat, 6th Secretary-General of the United Nations (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian politician and diplomat, 6th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian politician and diplomat, 6th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Boutros-Ghali"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "2019", "text": "<PERSON>, Swiss actor (b. 1941)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss actor (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_<PERSON>z"}]}, {"year": "2021", "text": "<PERSON>, Ecuadorian politician, 42nd President of Ecuador (b. 1937)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "2024", "text": "<PERSON>, Russian activist (b. 1976)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian activist (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian activist (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}