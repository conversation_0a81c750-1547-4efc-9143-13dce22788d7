{"date": "March 16", "url": "https://wikipedia.org/wiki/March_16", "data": {"Events": [{"year": "934", "text": "<PERSON><PERSON> declares himself emperor and establishes the Later Shu as a new state independent of the Later Tang.", "html": "934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> declares himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">emperor</a> and establishes the <a href=\"https://wikipedia.org/wiki/Later_Shu\" title=\"Later Shu\">Later Shu</a> as a new state independent of the <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> declares himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">emperor</a> and establishes the <a href=\"https://wikipedia.org/wiki/Later_Shu\" title=\"Later Shu\">Later Shu</a> as a new state independent of the <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ng"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}, {"title": "Later Shu", "link": "https://wikipedia.org/wiki/Later_Shu"}, {"title": "Later Tang", "link": "https://wikipedia.org/wiki/Later_Tang"}]}, {"year": "1190", "text": "Massacre of Jews at Clifford's Tower, York.", "html": "1190 - Massacre of Jews at <a href=\"https://wikipedia.org/wiki/York_Castle\" title=\"York Castle\">Clifford's Tower</a>, <a href=\"https://wikipedia.org/wiki/York\" title=\"York\">York</a>.", "no_year_html": "Massacre of Jews at <a href=\"https://wikipedia.org/wiki/York_Castle\" title=\"York Castle\">Clifford's Tower</a>, <a href=\"https://wikipedia.org/wiki/York\" title=\"York\">York</a>.", "links": [{"title": "York Castle", "link": "https://wikipedia.org/wiki/York_Castle"}, {"title": "York", "link": "https://wikipedia.org/wiki/York"}]}, {"year": "1244", "text": "Over 200 Cathars who refuse to recant are burnt to death after the Fall of Montségur.", "html": "1244 - Over 200 <a href=\"https://wikipedia.org/wiki/Cathar\" class=\"mw-redirect\" title=\"Cathar\">Cathars</a> who refuse to recant are burnt to death after the <a href=\"https://wikipedia.org/wiki/Siege_of_Monts%C3%A9gur\" title=\"Siege of Montségur\">Fall of Montségur</a>.", "no_year_html": "Over 200 <a href=\"https://wikipedia.org/wiki/Cathar\" class=\"mw-redirect\" title=\"Cathar\">Cathars</a> who refuse to recant are burnt to death after the <a href=\"https://wikipedia.org/wiki/Siege_of_Monts%C3%A9gur\" title=\"Siege of Montségur\">Fall of Montségur</a>.", "links": [{"title": "Cathar", "link": "https://wikipedia.org/wiki/Cathar"}, {"title": "Siege of Montségur", "link": "https://wikipedia.org/wiki/Siege_of_Monts%C3%A9gur"}]}, {"year": "1355", "text": "Amidst the Red Turban Rebellions, <PERSON>, a claimed descendant of Emperor <PERSON><PERSON> of Song, is proclaimed emperor of the restored Song dynasty in Bozhou.", "html": "1355 - Amidst the <a href=\"https://wikipedia.org/wiki/Red_Turban_Rebellions\" title=\"Red Turban Rebellions\">Red Turban Rebellions</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Lin%27er\" title=\"<PERSON> Lin'er\"><PERSON></a>, a claimed descendant of <a href=\"https://wikipedia.org/wiki/Emperor_Huizong_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a>, is proclaimed emperor of the restored <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> in <a href=\"https://wikipedia.org/wiki/Bozhou\" title=\"Bozhou\">Bozhou</a>.", "no_year_html": "Amidst the <a href=\"https://wikipedia.org/wiki/Red_Turban_Rebellions\" title=\"Red Turban Rebellions\">Red Turban Rebellions</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Lin%27er\" title=\"<PERSON> Lin'er\"><PERSON>er</a>, a claimed descendant of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>zong_of_Song\" title=\"Emperor <PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a>, is proclaimed emperor of the restored <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> in <a href=\"https://wikipedia.org/wiki/Bozhou\" title=\"Bozhou\">Bozhou</a>.", "links": [{"title": "Red Turban Rebellions", "link": "https://wikipedia.org/wiki/Red_Turban_Rebellions"}, {"title": "<PERSON>er", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27er"}, {"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}, {"title": "Bozhou", "link": "https://wikipedia.org/wiki/Bozhou"}]}, {"year": "1621", "text": "<PERSON><PERSON><PERSON>, a Abenaki, visits the settlers of Plymouth Colony and greets them, \"Welcome, Englishmen! My name is <PERSON><PERSON><PERSON>.\"", "html": "1621 - <a href=\"https://wikipedia.org/wiki/Samoset\" title=\"Samose<PERSON>\"><PERSON>ose<PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Abena<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, visits the settlers of <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> and greets them, \"Welcome, Englishmen! My name is <PERSON><PERSON><PERSON>.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samoset\" title=\"Samose<PERSON>\">Samose<PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Abena<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, visits the settlers of <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> and greets them, \"Welcome, Englishmen! My name is <PERSON><PERSON><PERSON>.\"", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Samoset"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ki"}, {"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}]}, {"year": "1660", "text": "The Long Parliament of England is dissolved so as to prepare for the new Convention Parliament.", "html": "1660 - The <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> of England is dissolved so as to prepare for the new <a href=\"https://wikipedia.org/wiki/Convention_Parliament_(1660)\" title=\"Convention Parliament (1660)\">Convention Parliament</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a> of England is dissolved so as to prepare for the new <a href=\"https://wikipedia.org/wiki/Convention_Parliament_(1660)\" title=\"Convention Parliament (1660)\">Convention Parliament</a>.", "links": [{"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}, {"title": "Convention Parliament (1660)", "link": "https://wikipedia.org/wiki/Convention_Parliament_(1660)"}]}, {"year": "1696", "text": "The Dutch bombard Givet during the Nine Years' War.", "html": "1696 - The Dutch <a href=\"https://wikipedia.org/wiki/Bombardment_of_Givet\" title=\"Bombardment of Givet\">bombard Givet</a> during the <a href=\"https://wikipedia.org/wiki/Nine_Years%27_War\" title=\"Nine Years' War\">Nine Years' War</a>.", "no_year_html": "The Dutch <a href=\"https://wikipedia.org/wiki/Bombardment_of_Givet\" title=\"Bombardment of Givet\">bombard Givet</a> during the <a href=\"https://wikipedia.org/wiki/Nine_Years%27_War\" title=\"Nine Years' War\">Nine Years' War</a>.", "links": [{"title": "Bombardment of Givet", "link": "https://wikipedia.org/wiki/Bombardment_of_Givet"}, {"title": "Nine Years' War", "link": "https://wikipedia.org/wiki/Nine_Years%27_War"}]}, {"year": "1792", "text": "King <PERSON> of Sweden is shot; he dies on March 29.", "html": "1792 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> is shot; he dies on <a href=\"https://wikipedia.org/wiki/March_29\" title=\"March 29\">March 29</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> is shot; he dies on <a href=\"https://wikipedia.org/wiki/March_29\" title=\"March 29\">March 29</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "March 29", "link": "https://wikipedia.org/wiki/March_29"}]}, {"year": "1802", "text": "The Army Corps of Engineers is established to found and operate the United States Military Academy at West Point.", "html": "1802 - The <a href=\"https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers\" title=\"United States Army Corps of Engineers\">Army Corps of Engineers</a> is established to found and operate the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy at West Point</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers\" title=\"United States Army Corps of Engineers\">Army Corps of Engineers</a> is established to found and operate the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy at West Point</a>.", "links": [{"title": "United States Army Corps of Engineers", "link": "https://wikipedia.org/wiki/United_States_Army_Corps_of_Engineers"}, {"title": "United States Military Academy", "link": "https://wikipedia.org/wiki/United_States_Military_Academy"}]}, {"year": "1815", "text": "Prince <PERSON> proclaims himself King of the United Kingdom of the Netherlands, the first constitutional monarch in the Netherlands.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"William <PERSON> of the Netherlands\">Prince <PERSON></a> proclaims himself King of the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_the_Netherlands\" title=\"United Kingdom of the Netherlands\">United Kingdom of the Netherlands</a>, the first <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarch</a> in the Netherlands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"William <PERSON> of the Netherlands\">Prince <PERSON></a> proclaims himself King of the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_the_Netherlands\" title=\"United Kingdom of the Netherlands\">United Kingdom of the Netherlands</a>, the first <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarch</a> in the Netherlands.", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands"}, {"title": "United Kingdom of the Netherlands", "link": "https://wikipedia.org/wiki/United_Kingdom_of_the_Netherlands"}, {"title": "Constitutional monarchy", "link": "https://wikipedia.org/wiki/Constitutional_monarchy"}]}, {"year": "1872", "text": "The Wanderers F.C. win the first FA Cup, the oldest football competition in the world, beating Royal Engineers A.F.C. 1-0 at The Oval in Kennington, London.", "html": "1872 - The <a href=\"https://wikipedia.org/wiki/Wanderers_F.C.\" title=\"Wanderers F.C.\">Wanderers F.C.</a> win the first <a href=\"https://wikipedia.org/wiki/FA_Cup\" title=\"FA Cup\">FA Cup</a>, the oldest <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> competition in the world, beating <a href=\"https://wikipedia.org/wiki/Royal_Engineers_A.F.C.\" title=\"Royal Engineers A.F.C.\">Royal Engineers A.F.C.</a> 1-0 at <a href=\"https://wikipedia.org/wiki/The_Oval\" title=\"The Oval\">The Oval</a> in <a href=\"https://wikipedia.org/wiki/Kennington\" title=\"Kennington\">Kennington</a>, London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wanderers_F.C.\" title=\"Wanderers F.C.\">Wanderers F.C.</a> win the first <a href=\"https://wikipedia.org/wiki/FA_Cup\" title=\"FA Cup\">FA Cup</a>, the oldest <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> competition in the world, beating <a href=\"https://wikipedia.org/wiki/Royal_Engineers_A.F.C.\" title=\"Royal Engineers A.F.C.\">Royal Engineers A.F.C.</a> 1-0 at <a href=\"https://wikipedia.org/wiki/The_Oval\" title=\"The Oval\">The Oval</a> in <a href=\"https://wikipedia.org/wiki/Kennington\" title=\"Kennington\">Kennington</a>, London.", "links": [{"title": "Wanderers F.C.", "link": "https://wikipedia.org/wiki/Wanderers_F.C."}, {"title": "FA Cup", "link": "https://wikipedia.org/wiki/FA_Cup"}, {"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}, {"title": "Royal Engineers A.F.C.", "link": "https://wikipedia.org/wiki/Royal_Engineers_A.F.C."}, {"title": "The Oval", "link": "https://wikipedia.org/wiki/The_Oval"}, {"title": "Kennington", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1898", "text": "In Melbourne, the representatives of five colonies adopt a constitution, which would become the basis of the Commonwealth of Australia.[page needed]", "html": "1898 - In <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, the representatives of five colonies adopt a <a href=\"https://wikipedia.org/wiki/Constitution_of_Australia\" title=\"Constitution of Australia\">constitution</a>, which would become the basis of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Australia\" class=\"mw-redirect\" title=\"Commonwealth of Australia\">Commonwealth of Australia</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, the representatives of five colonies adopt a <a href=\"https://wikipedia.org/wiki/Constitution_of_Australia\" title=\"Constitution of Australia\">constitution</a>, which would become the basis of the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Australia\" class=\"mw-redirect\" title=\"Commonwealth of Australia\">Commonwealth of Australia</a>.", "links": [{"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}, {"title": "Constitution of Australia", "link": "https://wikipedia.org/wiki/Constitution_of_Australia"}, {"title": "Commonwealth of Australia", "link": "https://wikipedia.org/wiki/Commonwealth_of_Australia"}]}, {"year": "1916", "text": "The 7th and 10th US cavalry regiments under <PERSON> cross the US-Mexico border to join the hunt for Pancho Villa.", "html": "1916 - The 7th and 10th US cavalry regiments under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> cross the <a href=\"https://wikipedia.org/wiki/US%E2%80%93Mexico_border\" class=\"mw-redirect\" title=\"US-Mexico border\">US-Mexico border</a> to join the hunt for <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a>.", "no_year_html": "The 7th and 10th US cavalry regiments under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> cross the <a href=\"https://wikipedia.org/wiki/US%E2%80%93Mexico_border\" class=\"mw-redirect\" title=\"US-Mexico border\">US-Mexico border</a> to join the hunt for <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "US-Mexico border", "link": "https://wikipedia.org/wiki/US%E2%80%93Mexico_border"}, {"title": "Pancho <PERSON>", "link": "https://wikipedia.org/wiki/Pancho_Villa"}]}, {"year": "1918", "text": "Finnish Civil War: Battle of Länkipohja is infamous for its bloody aftermath as the Whites execute 70-100 capitulated Reds.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_L%C3%A4nkipohja\" title=\"Battle of Länkipohja\">Battle of Länkipohja</a> is infamous for its bloody aftermath as the <a href=\"https://wikipedia.org/wiki/Whites_(Finland)\" title=\"Whites (Finland)\">Whites</a> execute 70-100 capitulated <a href=\"https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic\" title=\"Finnish Socialist Workers' Republic\">Reds</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_L%C3%A4nkipohja\" title=\"Battle of Länkipohja\">Battle of Länkipohja</a> is infamous for its bloody aftermath as the <a href=\"https://wikipedia.org/wiki/Whites_(Finland)\" title=\"Whites (Finland)\">Whites</a> execute 70-100 capitulated <a href=\"https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic\" title=\"Finnish Socialist Workers' Republic\">Reds</a>.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Battle of Länkipohja", "link": "https://wikipedia.org/wiki/Battle_of_L%C3%A4nkipohja"}, {"title": "White<PERSON> (Finland)", "link": "https://wikipedia.org/wiki/Whites_(Finland)"}, {"title": "Finnish Socialist Workers' Republic", "link": "https://wikipedia.org/wiki/Finnish_Socialist_Workers%27_Republic"}]}, {"year": "1924", "text": "In accordance with the Treaty of Rome, Fiume becomes annexed as part of Italy.", "html": "1924 - In accordance with the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rome_(1924)\" title=\"Treaty of Rome (1924)\">Treaty of Rome</a>, <a href=\"https://wikipedia.org/wiki/Rijeka\" title=\"Rijeka\">Fiume</a> becomes annexed as part of Italy.", "no_year_html": "In accordance with the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rome_(1924)\" title=\"Treaty of Rome (1924)\">Treaty of Rome</a>, <a href=\"https://wikipedia.org/wiki/Rijeka\" title=\"Rijeka\">Fiume</a> becomes annexed as part of Italy.", "links": [{"title": "Treaty of Rome (1924)", "link": "https://wikipedia.org/wiki/Treaty_of_Rome_(1924)"}, {"title": "Rijeka", "link": "https://wikipedia.org/wiki/Rijeka"}]}, {"year": "1925", "text": "An earthquake (measuring around 7.0 magnitude) occurs in Dali, China, killing an estimated 5,000 people.", "html": "1925 - An <a href=\"https://wikipedia.org/wiki/1925_Dali_earthquake\" title=\"1925 Dali earthquake\">earthquake</a> (measuring around 7.0 magnitude) occurs in <a href=\"https://wikipedia.org/wiki/Dali,_Yunnan\" class=\"mw-redirect\" title=\"Dali, Yunnan\">Dali</a>, China, killing an estimated 5,000 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1925_Dali_earthquake\" title=\"1925 Dali earthquake\">earthquake</a> (measuring around 7.0 magnitude) occurs in <a href=\"https://wikipedia.org/wiki/Dali,_Yunnan\" class=\"mw-redirect\" title=\"Dali, Yunnan\">Dali</a>, China, killing an estimated 5,000 people.", "links": [{"title": "1925 Dali earthquake", "link": "https://wikipedia.org/wiki/1925_Dali_earthquake"}, {"title": "Dali, Yunnan", "link": "https://wikipedia.org/wiki/Dali,_Yunnan"}]}, {"year": "1926", "text": "History of Rocketry: <PERSON> launches the first liquid-fueled rocket, at Auburn, Massachusetts.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Rocket#Modern_rocketry\" title=\"Rocket\">History of Rocketry</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches the first liquid-fueled <a href=\"https://wikipedia.org/wiki/Rocket\" title=\"Rocket\">rocket</a>, at <a href=\"https://wikipedia.org/wiki/Auburn,_Massachusetts\" title=\"Auburn, Massachusetts\">Auburn, Massachusetts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rocket#Modern_rocketry\" title=\"Rocket\">History of Rocketry</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches the first liquid-fueled <a href=\"https://wikipedia.org/wiki/Rocket\" title=\"Rocket\">rocket</a>, at <a href=\"https://wikipedia.org/wiki/Auburn,_Massachusetts\" title=\"Auburn, Massachusetts\">Auburn, Massachusetts</a>.", "links": [{"title": "Rocket", "link": "https://wikipedia.org/wiki/Rocket#Modern_rocketry"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Rocket", "link": "https://wikipedia.org/wiki/Rocket"}, {"title": "Auburn, Massachusetts", "link": "https://wikipedia.org/wiki/Auburn,_Massachusetts"}]}, {"year": "1935", "text": "Adolf <PERSON> orders Germany to rearm itself in violation of the Treaty of Versailles. Conscription is reintroduced to form the Wehrmacht.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> orders Germany to rearm itself in violation of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>. Conscription is reintroduced to form the <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> orders Germany to rearm itself in violation of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>. Conscription is reintroduced to form the <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Treaty of Versailles", "link": "https://wikipedia.org/wiki/Treaty_of_Versailles"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}]}, {"year": "1936", "text": "Warmer-than-normal temperatures rapidly melt snow and ice on the upper Allegheny and Monongahela rivers, leading to a major flood in Pittsburgh.", "html": "1936 - Warmer-than-normal temperatures rapidly melt snow and ice on the upper <a href=\"https://wikipedia.org/wiki/Allegheny_River\" title=\"Allegheny River\">Allegheny</a> and <a href=\"https://wikipedia.org/wiki/Monongahela_River\" title=\"Monongahela River\">Monongahela</a> rivers, leading to <a href=\"https://wikipedia.org/wiki/Pittsburgh_Flood_of_1936\" class=\"mw-redirect\" title=\"Pittsburgh Flood of 1936\">a major flood in Pittsburgh</a>.", "no_year_html": "Warmer-than-normal temperatures rapidly melt snow and ice on the upper <a href=\"https://wikipedia.org/wiki/Allegheny_River\" title=\"Allegheny River\">Allegheny</a> and <a href=\"https://wikipedia.org/wiki/Monongahela_River\" title=\"Monongahela River\">Monongahela</a> rivers, leading to <a href=\"https://wikipedia.org/wiki/Pittsburgh_Flood_of_1936\" class=\"mw-redirect\" title=\"Pittsburgh Flood of 1936\">a major flood in Pittsburgh</a>.", "links": [{"title": "Allegheny River", "link": "https://wikipedia.org/wiki/Allegheny_River"}, {"title": "Monongahela River", "link": "https://wikipedia.org/wiki/Monongahela_River"}, {"title": "Pittsburgh Flood of 1936", "link": "https://wikipedia.org/wiki/Pittsburgh_Flood_of_1936"}]}, {"year": "1939", "text": "From Prague Castle, <PERSON> proclaims Bohemia and Moravia a German protectorate.", "html": "1939 - From <a href=\"https://wikipedia.org/wiki/Prague_Castle\" title=\"Prague Castle\">Prague Castle</a>, Hitler proclaims <a href=\"https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia\" title=\"Protectorate of Bohemia and Moravia\">Bohemia and Moravia</a> a German <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">protectorate</a>.", "no_year_html": "From <a href=\"https://wikipedia.org/wiki/Prague_Castle\" title=\"Prague Castle\">Prague Castle</a>, Hitler proclaims <a href=\"https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia\" title=\"Protectorate of Bohemia and Moravia\">Bohemia and Moravia</a> a German <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">protectorate</a>.", "links": [{"title": "Prague Castle", "link": "https://wikipedia.org/wiki/Prague_Castle"}, {"title": "Protectorate of Bohemia and Moravia", "link": "https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia"}, {"title": "Protectorate", "link": "https://wikipedia.org/wiki/Protectorate"}]}, {"year": "1941", "text": "Operation Appearance takes place to re-establish British Somaliland", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Operation_Appearance\" title=\"Operation Appearance\">Operation Appearance</a> takes place to re-establish <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Appearance\" title=\"Operation Appearance\">Operation Appearance</a> takes place to re-establish <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a>", "links": [{"title": "Operation Appearance", "link": "https://wikipedia.org/wiki/Operation_Appearance"}, {"title": "British Somaliland", "link": "https://wikipedia.org/wiki/British_Somaliland"}]}, {"year": "1945", "text": "World War II: The Battle of Iwo Jima ends, but small pockets of Japanese resistance persist.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> ends, but small pockets of Japanese resistance persist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a> ends, but small pockets of Japanese resistance persist.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}]}, {"year": "1945", "text": "Ninety percent of Würzburg, Germany is destroyed in only 20 minutes by British bombers, resulting in at least 4,000 deaths.", "html": "1945 - Ninety percent of <a href=\"https://wikipedia.org/wiki/Bombing_of_W%C3%BCrzburg_in_World_War_II\" title=\"Bombing of Würzburg in World War II\">Würzburg, Germany</a> is destroyed in only 20 minutes by British bombers, resulting in at least 4,000 deaths.", "no_year_html": "Ninety percent of <a href=\"https://wikipedia.org/wiki/Bombing_of_W%C3%BCrzburg_in_World_War_II\" title=\"Bombing of Würzburg in World War II\">Würzburg, Germany</a> is destroyed in only 20 minutes by British bombers, resulting in at least 4,000 deaths.", "links": [{"title": "Bombing of Würzburg in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_W%C3%BCrzburg_in_World_War_II"}]}, {"year": "1962", "text": "Flying Tiger Line Flight 739 disappears in the western Pacific Ocean with all 107 aboard missing and presumed dead.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_739\" title=\"Flying Tiger Line Flight 739\">Flying Tiger Line Flight 739</a> disappears in the western Pacific Ocean with all 107 aboard missing and presumed dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_739\" title=\"Flying Tiger Line Flight 739\">Flying Tiger Line Flight 739</a> disappears in the western Pacific Ocean with all 107 aboard missing and presumed dead.", "links": [{"title": "Flying Tiger Line Flight 739", "link": "https://wikipedia.org/wiki/Flying_Tiger_Line_Flight_739"}]}, {"year": "1966", "text": "Launch of Gemini 8 with astronauts <PERSON> and <PERSON>. It would perform the first docking of two spacecraft in orbit.", "html": "1966 - Launch of <a href=\"https://wikipedia.org/wiki/Gemini_8\" title=\"Gemini 8\">Gemini 8</a> with astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. It would perform the first docking of two spacecraft in orbit.", "no_year_html": "Launch of <a href=\"https://wikipedia.org/wiki/Gemini_8\" title=\"Gemini 8\">Gemini 8</a> with astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. It would perform the first docking of two spacecraft in orbit.", "links": [{"title": "Gemini 8", "link": "https://wikipedia.org/wiki/Gemini_8"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Vietnam War: My Lai massacre occurs; between 347 and 500 Vietnamese villagers are killed by American troops.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/My_Lai_massacre\" title=\"My Lai massacre\">My Lai massacre</a> occurs; between 347 and 500 <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> villagers are killed by American troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/My_Lai_massacre\" title=\"My Lai massacre\">My Lai massacre</a> occurs; between 347 and 500 <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> villagers are killed by American troops.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "My Lai massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_massacre"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1969", "text": "A Viasa McDonnell Douglas DC-9 crashes in Maracaibo, Venezuela, killing 155.", "html": "1969 - A <a href=\"https://wikipedia.org/wiki/Viasa\" title=\"Viasa\">Viasa</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">McDonnell Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/Viasa_Flight_742\" title=\"Viasa Flight 742\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Maracaibo\" title=\"Maracaibo\">Maracaibo</a>, Venezuela, killing 155.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Viasa\" title=\"Viasa\">Viasa</a> <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-9\" title=\"McDonnell Douglas DC-9\">McDonnell Douglas DC-9</a> <a href=\"https://wikipedia.org/wiki/Viasa_Flight_742\" title=\"Viasa Flight 742\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Maracaibo\" title=\"Maracaibo\">Maracaibo</a>, Venezuela, killing 155.", "links": [{"title": "Viasa", "link": "https://wikipedia.org/wiki/Viasa"}, {"title": "McDonnell Douglas DC-9", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-9"}, {"title": "Viasa Flight 742", "link": "https://wikipedia.org/wiki/Viasa_Flight_742"}, {"title": "Maracaibo", "link": "https://wikipedia.org/wiki/Maracaibo"}]}, {"year": "1977", "text": "Assassination of <PERSON>, the main leader of the anti-government forces in the Lebanese Civil War.", "html": "1977 - Assassination of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the main leader of the anti-government forces in the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>.", "no_year_html": "Assassination of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the main leader of the anti-government forces in the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lebanese Civil War", "link": "https://wikipedia.org/wiki/Lebanese_Civil_War"}]}, {"year": "1978", "text": "Former Italian Prime Minister <PERSON><PERSON> is kidnapped; he is later murdered by his captors.", "html": "1978 - Former Italian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is kidnapped; he is later murdered by his captors.", "no_year_html": "Former Italian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is kidnapped; he is later murdered by his captors.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Moro"}]}, {"year": "1978", "text": "A Balkan Bulgarian Airlines Tupolev Tu-134 crashes near Gabare, Bulgaria, killing 73.", "html": "1978 - A <a href=\"https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines\" title=\"Balkan Bulgarian Airlines\">Balkan Bulgarian Airlines</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134</a> <a href=\"https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines_Flight_107\" title=\"Balkan Bulgarian Airlines Flight 107\">crashes</a> near Gabare, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, killing 73.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines\" title=\"Balkan Bulgarian Airlines\">Balkan Bulgarian Airlines</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-134\" title=\"Tupolev Tu-134\">Tupolev Tu-134</a> <a href=\"https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines_Flight_107\" title=\"Balkan Bulgarian Airlines Flight 107\">crashes</a> near Gabare, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, killing 73.", "links": [{"title": "Balkan Bulgarian Airlines", "link": "https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines"}, {"title": "Tupolev Tu-134", "link": "https://wikipedia.org/wiki/Tupolev_Tu-134"}, {"title": "Balkan Bulgarian Airlines Flight 107", "link": "https://wikipedia.org/wiki/Balkan_Bulgarian_Airlines_Flight_107"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}]}, {"year": "1978", "text": "Supertanker Amoco Cadiz splits in two after running aground on the Portsall Rocks, three miles off the coast of Brittany, resulting in the largest oil spill in history at that time.", "html": "1978 - Supertanker <i><a href=\"https://wikipedia.org/wiki/Amoco_Cadiz\" title=\"Amoco Cadiz\">Amoco Cadiz</a></i> splits in two after running aground on the Portsall Rocks, three miles off the coast of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a>, resulting in the largest <a href=\"https://wikipedia.org/wiki/Oil_spill\" title=\"Oil spill\">oil spill</a> in history at that time.", "no_year_html": "Supertanker <i><a href=\"https://wikipedia.org/wiki/Amoco_Cadiz\" title=\"Amoco Cadiz\">Amoco Cadiz</a></i> splits in two after running aground on the Portsall Rocks, three miles off the coast of <a href=\"https://wikipedia.org/wiki/Brittany\" title=\"Brittany\">Brittany</a>, resulting in the largest <a href=\"https://wikipedia.org/wiki/Oil_spill\" title=\"Oil spill\">oil spill</a> in history at that time.", "links": [{"title": "Amoco Cadiz", "link": "https://wikipedia.org/wiki/Amoco_Cadiz"}, {"title": "Brittany", "link": "https://wikipedia.org/wiki/Brittany"}, {"title": "Oil spill", "link": "https://wikipedia.org/wiki/Oil_spill"}]}, {"year": "1979", "text": "Sino-Vietnamese War: The People's Liberation Army crosses the border back into China, ending the war.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Sino-Vietnamese War</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> crosses the border back into China, ending the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-Vietnamese_War\" title=\"Sino-Vietnamese War\">Sino-Vietnamese War</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> crosses the border back into China, ending the war.", "links": [{"title": "Sino-Vietnamese War", "link": "https://wikipedia.org/wiki/Sino-Vietnamese_War"}, {"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}]}, {"year": "1984", "text": "<PERSON>, the CIA station chief in Lebanon, is kidnapped by Hezbollah; he later dies in captivity.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the CIA station chief in Lebanon, is kidnapped by <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a>; he later dies in captivity.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the CIA station chief in Lebanon, is kidnapped by <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a>; he later dies in captivity.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}]}, {"year": "1985", "text": "Associated Press newsman <PERSON> is taken hostage in Beirut; he is not released until December 1991.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> newsman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken hostage in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>; he is not released until December 1991.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> newsman <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is taken hostage in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>; he is not released until December 1991.", "links": [{"title": "Associated Press", "link": "https://wikipedia.org/wiki/Associated_Press"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "1988", "text": "Iran-Contra affair: Lieutenant Colonel <PERSON> and Vice Admiral <PERSON> are indicted on charges of conspiracy to defraud the United States.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Lieutenant Colonel <a href=\"https://wikipedia.org/wiki/Oliver_<PERSON>\" title=\"Oliver North\"><PERSON></a> and Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are indicted on charges of <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> to defraud the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Lieutenant Colonel <a href=\"https://wikipedia.org/wiki/Oliver_<PERSON>\" title=\"Oliver North\"><PERSON></a> and Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are indicted on charges of <a href=\"https://wikipedia.org/wiki/Conspiracy_(criminal)\" class=\"mw-redirect\" title=\"Conspiracy (criminal)\">conspiracy</a> to defraud the United States.", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_North"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Conspiracy (criminal)", "link": "https://wikipedia.org/wiki/Conspiracy_(criminal)"}]}, {"year": "1988", "text": "Halabja chemical attack: The Kurdish town of Halabja in Iraq is attacked with a mix of poison gas and nerve agents on the orders of <PERSON>, killing 5,000 people and injuring about 10,000 people.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre\" title=\"Halabja massacre\">Halabja chemical attack</a>: The <a href=\"https://wikipedia.org/wiki/Kurd\" class=\"mw-redirect\" title=\"Kurd\">Kurdish</a> town of <a href=\"https://wikipedia.org/wiki/Halabja\" title=\"Halabja\">Hal<PERSON>ja</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> is attacked with a mix of <a href=\"https://wikipedia.org/wiki/List_of_highly_toxic_gases\" title=\"List of highly toxic gases\">poison gas</a> and <a href=\"https://wikipedia.org/wiki/Nerve_agent\" title=\"Nerve agent\">nerve agents</a> on the orders of <PERSON>, killing 5,000 people and injuring about 10,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre\" title=\"Halabja massacre\">Halabja chemical attack</a>: The <a href=\"https://wikipedia.org/wiki/Kurd\" class=\"mw-redirect\" title=\"Kurd\">Kurdish</a> town of <a href=\"https://wikipedia.org/wiki/Halabja\" title=\"Halabja\">Halabja</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> is attacked with a mix of <a href=\"https://wikipedia.org/wiki/List_of_highly_toxic_gases\" title=\"List of highly toxic gases\">poison gas</a> and <a href=\"https://wikipedia.org/wiki/Nerve_agent\" title=\"Nerve agent\">nerve agents</a> on the orders of <PERSON>, killing 5,000 people and injuring about 10,000 people.", "links": [{"title": "Halabja massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rd"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "List of highly toxic gases", "link": "https://wikipedia.org/wiki/List_of_highly_toxic_gases"}, {"title": "Nerve agent", "link": "https://wikipedia.org/wiki/Nerve_agent"}]}, {"year": "1988", "text": "The Troubles: Ulster loyalist militant <PERSON> attacks a Provisional IRA funeral in Belfast with pistols and grenades. Three persons, one of them a member of PIRA are killed, and more than 60 others are wounded.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">Ulster loyalist</a> militant <a href=\"https://wikipedia.org/wiki/<PERSON>(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Milltown_Cemetery_attack\" title=\"Milltown Cemetery attack\">attacks</a> a <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> funeral in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a> with pistols and grenades. Three persons, one of them a member of PIRA are killed, and more than 60 others are wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Ulster_loyalism\" title=\"Ulster loyalism\">Ulster loyalist</a> militant <a href=\"https://wikipedia.org/wiki/<PERSON>(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Milltown_Cemetery_attack\" title=\"Milltown Cemetery attack\">attacks</a> a <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> funeral in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a> with pistols and grenades. Three persons, one of them a member of PIRA are killed, and more than 60 others are wounded.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Ulster loyalism", "link": "https://wikipedia.org/wiki/Ulster_loyalism"}, {"title": "<PERSON> (loyalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)"}, {"title": "Milltown Cemetery attack", "link": "https://wikipedia.org/wiki/Milltown_Cemetery_attack"}, {"title": "Provisional IRA", "link": "https://wikipedia.org/wiki/Provisional_IRA"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}]}, {"year": "1995", "text": "Mississippi formally ratifies the Thirteenth Amendment to the United States Constitution, becoming the last state to approve the abolition of slavery. The Thirteenth Amendment was officially ratified in 1865.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> formally ratifies the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>, becoming the last state to approve the abolition of <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>. The Thirteenth Amendment was officially ratified in 1865.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> formally ratifies the <a href=\"https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution\" title=\"Thirteenth Amendment to the United States Constitution\">Thirteenth Amendment to the United States Constitution</a>, becoming the last state to approve the abolition of <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>. The Thirteenth Amendment was officially ratified in 1865.", "links": [{"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Thirteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Thirteenth_Amendment_to_the_United_States_Constitution"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "2001", "text": "A series of bomb blasts in the city of Shijiazhuang, China kill 108 people and injure 38 others, the biggest mass murder in China in decades.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Shijiazhuang_bombings\" class=\"mw-redirect\" title=\"Shijiazhuang bombings\">A series of bomb blasts</a> in the city of <a href=\"https://wikipedia.org/wiki/Shijiazhuang\" title=\"Shijiazhuang\">Shijiazhuang</a>, China kill 108 people and injure 38 others, the biggest mass murder in China in decades.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shijiazhuang_bombings\" class=\"mw-redirect\" title=\"Shijiazhuang bombings\">A series of bomb blasts</a> in the city of <a href=\"https://wikipedia.org/wiki/Shijiazhuang\" title=\"Shijiazhuang\">Shijiazhuang</a>, China kill 108 people and injure 38 others, the biggest mass murder in China in decades.", "links": [{"title": "Shijiazhuang bombings", "link": "https://wikipedia.org/wiki/Shijiazhuang_bombings"}, {"title": "Shijiazhuang", "link": "https://wikipedia.org/wiki/Shijiazhuang"}]}, {"year": "2003", "text": "American activist <PERSON> is killed in Rafah by being run over by an Israel Defense Forces bulldozer while trying to obstruct the demolition of a home.", "html": "2003 - American activist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> by being run over by an Israel Defense Forces bulldozer while trying to obstruct the demolition of a home.", "no_year_html": "American activist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> by being run over by an Israel Defense Forces bulldozer while trying to obstruct the demolition of a home.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>h"}]}, {"year": "2005", "text": "Israel officially hands over Jericho to Palestinian control.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> officially hands over <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a> to Palestinian control.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> officially hands over <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a> to Palestinian control.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Jericho", "link": "https://wikipedia.org/wiki/Jericho"}]}, {"year": "2010", "text": "The Kasubi Tombs, Uganda's only cultural World Heritage Site, are destroyed in a fire.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/Kasubi_Tombs\" title=\"Kasubi Tombs\">Kasubi Tombs</a>, Uganda's only cultural <a href=\"https://wikipedia.org/wiki/World_Heritage_Site\" title=\"World Heritage Site\">World Heritage Site</a>, are destroyed in a fire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kasubi_Tombs\" title=\"Kasubi Tombs\">Kasubi Tombs</a>, Uganda's only cultural <a href=\"https://wikipedia.org/wiki/World_Heritage_Site\" title=\"World Heritage Site\">World Heritage Site</a>, are destroyed in a fire.", "links": [{"title": "Kasubi Tombs", "link": "https://wikipedia.org/wiki/Kasubi_Tombs"}, {"title": "World Heritage Site", "link": "https://wikipedia.org/wiki/World_Heritage_Site"}]}, {"year": "2012", "text": "Former Indian cricketer <PERSON><PERSON> becomes the first batter in history to score 100 centuries in international cricket.", "html": "2012 - Former Indian cricketer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first batter in history to score <a href=\"https://wikipedia.org/wiki/List_of_international_cricket_centuries_by_<PERSON><PERSON>_<PERSON>\" title=\"List of international cricket centuries by <PERSON><PERSON>\">100 centuries</a> in international cricket.", "no_year_html": "Former Indian cricketer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first batter in history to score <a href=\"https://wikipedia.org/wiki/List_of_international_cricket_centuries_by_<PERSON><PERSON>_<PERSON>\" title=\"List of international cricket centuries by <PERSON><PERSON>\">100 centuries</a> in international cricket.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of international cricket centuries by <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/List_of_international_cricket_centuries_by_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "Crimea votes in a controversial referendum to secede from Ukraine to join Russia.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> votes in a controversial <a href=\"https://wikipedia.org/wiki/2014_Crimean_status_referendum\" title=\"2014 Crimean status referendum\">referendum</a> to secede from <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> to join Russia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> votes in a controversial <a href=\"https://wikipedia.org/wiki/2014_Crimean_status_referendum\" title=\"2014 Crimean status referendum\">referendum</a> to secede from <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> to join Russia.", "links": [{"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea"}, {"title": "2014 Crimean status referendum", "link": "https://wikipedia.org/wiki/2014_Crimean_status_referendum"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}]}, {"year": "2016", "text": "A bomb detonates in a bus carrying government employees in Peshawar, Pakistan, killing 15 and injuring at least 30.", "html": "2016 - A <a href=\"https://wikipedia.org/wiki/2016_Peshawar_bus_bombing\" title=\"2016 Peshawar bus bombing\">bomb detonates</a> in a bus carrying government employees in <a href=\"https://wikipedia.org/wiki/Peshawar\" title=\"Peshawar\">Peshawar</a>, Pakistan, killing 15 and injuring at least 30.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2016_Peshawar_bus_bombing\" title=\"2016 Peshawar bus bombing\">bomb detonates</a> in a bus carrying government employees in <a href=\"https://wikipedia.org/wiki/Peshawar\" title=\"Peshawar\">Peshawar</a>, Pakistan, killing 15 and injuring at least 30.", "links": [{"title": "2016 Peshawar bus bombing", "link": "https://wikipedia.org/wiki/2016_Peshawar_bus_bombing"}, {"title": "Peshawar", "link": "https://wikipedia.org/wiki/Peshawar"}]}, {"year": "2016", "text": "Two suicide bombers detonate their explosives at a mosque during morning prayer on the outskirts of Maiduguri, Nigeria, killing 24 and injuring 18.", "html": "2016 - Two suicide bombers <a href=\"https://wikipedia.org/wiki/March_2016_Maiduguri_bombings\" title=\"March 2016 Maiduguri bombings\">detonate their explosives</a> at a mosque during morning prayer on the outskirts of <a href=\"https://wikipedia.org/wiki/Maiduguri\" title=\"Maiduguri\">Maiduguri</a>, Nigeria, killing 24 and injuring 18.", "no_year_html": "Two suicide bombers <a href=\"https://wikipedia.org/wiki/March_2016_Maiduguri_bombings\" title=\"March 2016 Maiduguri bombings\">detonate their explosives</a> at a mosque during morning prayer on the outskirts of <a href=\"https://wikipedia.org/wiki/Maiduguri\" title=\"Maiduguri\">Maiduguri</a>, Nigeria, killing 24 and injuring 18.", "links": [{"title": "March 2016 Maiduguri bombings", "link": "https://wikipedia.org/wiki/March_2016_Maiduguri_bombings"}, {"title": "Maiduguri", "link": "https://wikipedia.org/wiki/Maiduguri"}]}, {"year": "2020", "text": "The Dow Jones Industrial Average falls by 2,997.10, the single largest point drop in history and the second-largest percentage drop ever at 12.93%, an even greater crash than Black Monday (1929). This follows the U.S. Federal Reserve announcing that it will cut its target interest rate to 0-0.25%.", "html": "2020 - The Dow Jones Industrial Average <a href=\"https://wikipedia.org/wiki/2020_stock_market_crash#Black_Monday_II_(16_March)\" title=\"2020 stock market crash\">falls by 2,997.10</a>, the single <a href=\"https://wikipedia.org/wiki/List_of_largest_daily_changes_in_the_Dow_Jones_Industrial_Average\" title=\"List of largest daily changes in the Dow Jones Industrial Average\">largest point drop in history</a> and the second-largest percentage drop ever at 12.93%, an even greater crash than <a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">Black Monday (1929)</a>. This follows the <a href=\"https://wikipedia.org/wiki/Federal_Reserve\" title=\"Federal Reserve\">U.S. Federal Reserve</a> announcing that it will cut its target interest rate to 0-0.25%.", "no_year_html": "The Dow Jones Industrial Average <a href=\"https://wikipedia.org/wiki/2020_stock_market_crash#Black_Monday_II_(16_March)\" title=\"2020 stock market crash\">falls by 2,997.10</a>, the single <a href=\"https://wikipedia.org/wiki/List_of_largest_daily_changes_in_the_Dow_Jones_Industrial_Average\" title=\"List of largest daily changes in the Dow Jones Industrial Average\">largest point drop in history</a> and the second-largest percentage drop ever at 12.93%, an even greater crash than <a href=\"https://wikipedia.org/wiki/Wall_Street_Crash_of_1929\" class=\"mw-redirect\" title=\"Wall Street Crash of 1929\">Black Monday (1929)</a>. This follows the <a href=\"https://wikipedia.org/wiki/Federal_Reserve\" title=\"Federal Reserve\">U.S. Federal Reserve</a> announcing that it will cut its target interest rate to 0-0.25%.", "links": [{"title": "2020 stock market crash", "link": "https://wikipedia.org/wiki/2020_stock_market_crash#Black_Monday_II_(16_March)"}, {"title": "List of largest daily changes in the Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/List_of_largest_daily_changes_in_the_Dow_Jones_Industrial_Average"}, {"title": "Wall Street Crash of 1929", "link": "https://wikipedia.org/wiki/Wall_Street_Crash_of_1929"}, {"title": "Federal Reserve", "link": "https://wikipedia.org/wiki/Federal_Reserve"}]}, {"year": "2021", "text": "Atlanta spa shootings: Eight people are killed and one is injured in a trio of shootings at spas in and near Atlanta, Georgia, U.S. A suspect is arrested the same day.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/2021_Atlanta_spa_shootings\" title=\"2021 Atlanta spa shootings\">Atlanta spa shootings</a>: Eight people are killed and one is injured in a trio of shootings at spas in and near <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(U.S_state)\" class=\"mw-redirect\" title=\"Georgia (U.S state)\">Georgia</a>, U.S. A suspect is arrested the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2021_Atlanta_spa_shootings\" title=\"2021 Atlanta spa shootings\">Atlanta spa shootings</a>: Eight people are killed and one is injured in a trio of shootings at spas in and near <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>, <a href=\"https://wikipedia.org/wiki/Georgia_(U.S_state)\" class=\"mw-redirect\" title=\"Georgia (U.S state)\">Georgia</a>, U.S. A suspect is arrested the same day.", "links": [{"title": "2021 Atlanta spa shootings", "link": "https://wikipedia.org/wiki/2021_Atlanta_spa_shootings"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}, {"title": "Georgia (U.S state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S_state)"}]}, {"year": "2022", "text": "A 7.4-magnitude earthquake occurs off the coast of Fukushima, Japan, killing 4 people and injuring 225.", "html": "2022 - A <a href=\"https://wikipedia.org/wiki/2022_Fukushima_earthquake\" title=\"2022 Fukushima earthquake\">7.4-magnitude earthquake</a> occurs off the coast of <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima</a>, Japan, killing 4 people and injuring 225.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2022_Fukushima_earthquake\" title=\"2022 Fukushima earthquake\">7.4-magnitude earthquake</a> occurs off the coast of <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima</a>, Japan, killing 4 people and injuring 225.", "links": [{"title": "2022 Fukushima earthquake", "link": "https://wikipedia.org/wiki/2022_Fukushima_earthquake"}, {"title": "Fukushima Prefecture", "link": "https://wikipedia.org/wiki/Fukushima_Prefecture"}]}, {"year": "2022", "text": "Mariupol theatre airstrike during the siege of Mariupol.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Mariupol_theatre_airstrike\" title=\"Mariupol theatre airstrike\">Mariupol theatre airstrike</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_Mariupol\" title=\"Siege of Mariupol\">siege of Mariupol</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariupol_theatre_airstrike\" title=\"Mariupol theatre airstrike\">Mariupol theatre airstrike</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_Mariupol\" title=\"Siege of Mariupol\">siege of Mariupol</a>.", "links": [{"title": "Mariupol theatre airstrike", "link": "https://wikipedia.org/wiki/Mariupol_theatre_airstrike"}, {"title": "Siege of Mariupol", "link": "https://wikipedia.org/wiki/Siege_of_Mariupol"}]}], "Births": [{"year": "1399", "text": "The <PERSON><PERSON>e Emperor, ruler of Ming China (d. 1435)", "html": "1399 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Xuande Emperor\"><PERSON><PERSON>e Emperor</a>, ruler of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a> (d. 1435)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Xuande Emperor\"><PERSON><PERSON>e Emperor</a>, ruler of <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming China</a> (d. 1435)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1445", "text": "<PERSON>, Swiss priest and theologian (d. 1510)", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> von <PERSON></a>, Swiss priest and theologian (d. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss priest and theologian (d. 1510)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1465", "text": "<PERSON><PERSON><PERSON><PERSON> of Austria, Duchess of Bavaria (d. 1520)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/Kunigunde_of_Austria\" title=\"Kunigunde of Austria\"><PERSON><PERSON><PERSON><PERSON> of Austria</a>, Duchess of Bavaria (d. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kunigunde_of_Austria\" title=\"Kunigunde of Austria\"><PERSON><PERSON><PERSON><PERSON> of Austria</a>, Duchess of Bavaria (d. 1520)", "links": [{"title": "Kunigunde of Austria", "link": "https://wikipedia.org/wiki/Kunigunde_of_Austria"}]}, {"year": "1473", "text": "<PERSON>, Duke of Saxony (d. 1541)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (d. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (d. 1541)", "links": [{"title": "<PERSON>, Duke of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony"}]}, {"year": "1559", "text": "<PERSON>, successor of <PERSON><PERSON><PERSON> of Mewar (d. 1620)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, successor of <PERSON><PERSON><PERSON> of Mewar (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, successor of <PERSON><PERSON><PERSON> of Mewar (d. 1620)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, Dutch historian and poet (d. 1647)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and poet (d. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and poet (d. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON><PERSON><PERSON>, Dutch poet and playwright (d. 1618)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bredero\"><PERSON><PERSON><PERSON></a>, Dutch poet and playwright (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ero\" title=\"<PERSON><PERSON><PERSON> Bredero\"><PERSON><PERSON><PERSON></a>, Dutch poet and playwright (d. 1618)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bredero"}]}, {"year": "1590", "text": "<PERSON><PERSON>, Japanese daimyō (d. 1659)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>otaka\" title=\"<PERSON><PERSON> Naotaka\"><PERSON><PERSON></a>, Japanese daimyō (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ota<PERSON>\" title=\"<PERSON><PERSON> Naotaka\"><PERSON><PERSON></a>, Japanese daimyō (d. 1659)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ka"}]}, {"year": "1596", "text": "<PERSON><PERSON>, Swedish countess (d. 1674)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rahe\" title=\"<PERSON><PERSON>rah<PERSON>\"><PERSON><PERSON></a>, Swedish countess (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>rah<PERSON>\"><PERSON><PERSON></a>, Swedish countess (d. 1674)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1609", "text": "<PERSON>, German poet and composer of hymns (d. 1667)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer of hymns (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer of hymns (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1660)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1660)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, German poet and composer of hymns (d. 1681)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer of hymns (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and composer of hymns (d. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Neumark"}]}, {"year": "1631", "text": "<PERSON>, French literary critic (d. 1680)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French literary critic (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French literary critic (d. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>_<PERSON>u"}]}, {"year": "1638", "text": "<PERSON>, Jesuit missionary (d. 1702)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Cr%C3%A9pieul\" title=\"<PERSON>\"><PERSON></a>, Jesuit missionary (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Cr%C3%A9pieul\" title=\"<PERSON>\"><PERSON></a>, Jesuit missionary (d. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Cr%C3%A9pieul"}]}, {"year": "1654", "text": "<PERSON>, German scholar (d. 1704)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andreas_<PERSON>lut<PERSON>"}]}, {"year": "1670", "text": "<PERSON>, French general (d. 1759)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_Coigny\" title=\"<PERSON> Coigny\"><PERSON>ign<PERSON></a>, French general (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Coigny\" title=\"<PERSON> Coigny\"><PERSON>ign<PERSON></a>, French general (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, French jurist and scholar (d. 1746)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, French jurist and scholar (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, French jurist and scholar (d. 1746)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_(jurist)"}]}, {"year": "1687", "text": "<PERSON> of Hanover, queen consort of <PERSON> (d. 1757)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a>, queen consort of <PERSON> (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hanover\" title=\"<PERSON> of Hanover\"><PERSON> of Hanover</a>, queen consort of <PERSON> (d. 1757)", "links": [{"title": "<PERSON> of Hanover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hanover"}]}, {"year": "1693", "text": "<PERSON><PERSON>, Indian nobleman (d. 1766)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian nobleman (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian nobleman (d. 1766)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, Swedish theologian (d. 1750)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish theologian (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish theologian (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON> (d. 1818)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_of_Leiningen-Dagsburg-Falkenburg\" title=\"Countess <PERSON> of Leiningen-Dagsburg-Falkenburg\"><PERSON></a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_of_Leiningen-Dagsburg-Falkenburg\" title=\"Countess <PERSON> of Leiningen-Dagsburg-Falkenburg\"><PERSON></a> (d. 1818)", "links": [{"title": "Countess <PERSON> of Leiningen-Dagsburg-Falkenburg", "link": "https://wikipedia.org/wiki/Countess_<PERSON>_<PERSON>_<PERSON>_of_Leiningen-Dagsburg-Falkenburg"}]}, {"year": "1741", "text": "<PERSON>, Italian scientist (d. 1816)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian scientist (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian scientist (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON><PERSON><PERSON>, Guadeloupean poet and novelist (d. 1793)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9onard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guadeloupean poet and novelist (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9onard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Guadeloupean poet and novelist (d. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9<PERSON>rd"}]}, {"year": "1750", "text": "<PERSON>, German-English astronomer (d. 1848)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English astronomer (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, American academic and politician, 4th President of the United States (d. 1836)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Madison\"><PERSON></a>, American academic and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1753", "text": "<PERSON>, French general (d. 1799)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Am%C3%A9d%C3%A9e_Doppet\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Am%C3%A9d%C3%A9e_Doppet\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Am%C3%A9d%C3%A9e_Doppet"}]}, {"year": "1760", "text": "<PERSON>, Swiss painter and writer (d. 1832)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and writer (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and writer (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,  French antiquarian, cartographer, artist and explorer (d. 1875)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French antiquarian, cartographer, artist and explorer (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French antiquarian, cartographer, artist and explorer (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9ric_W<PERSON>eck"}]}, {"year": "1771", "text": "<PERSON><PERSON><PERSON>, French painter (d. 1835)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (d. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Argentinian general and politician, 6th Governor of Buenos Aires Province (d. 1836)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Ba<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Ba<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Balcarce"}, {"title": "Governor of Buenos Aires Province", "link": "https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province"}]}, {"year": "1774", "text": "<PERSON>, English navigator and cartographer (d. 1814)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and cartographer (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and cartographer (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, English general and explorer (d. 1872)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and explorer (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and explorer (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ney"}]}, {"year": "1789", "text": "<PERSON>, German physicist and mathematician (d. 1854)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON>, Austrian geologist and ethnographer (d. 1881)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Ami_<PERSON>u%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian geologist and ethnographer (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ami_<PERSON>u%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian geologist and ethnographer (d. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ami_Bou%C3%A9"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, English poet and journalist (d. 1864)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and journalist (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and journalist (d. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, English botanist and photographer (d. 1871)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and photographer (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English botanist and photographer (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1846)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor Ni<PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1846)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nink%C5%8D"}]}, {"year": "1805", "text": "<PERSON>, German philologist and politician (d. 1861)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and politician (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and politician (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, Belgian painter (d. 1862)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_De_Vigne\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_De_Vigne\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_De_Vigne"}]}, {"year": "1808", "text": "<PERSON>, British-born American writer and pioneer (d. 1886)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American writer and pioneer (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born American writer and pioneer (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON><PERSON>, French prime minister (d. 1899)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Ga%C3%ABtan_de_Rochebou%C3%ABt\" title=\"Gaëtan de Rochebouët\"><PERSON><PERSON><PERSON><PERSON></a>, French prime minister (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%ABtan_de_Rochebou%C3%ABt\" title=\"Gaëtan de Rochebouët\"><PERSON><PERSON><PERSON><PERSON></a>, French prime minister (d. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>t", "link": "https://wikipedia.org/wiki/Ga%C3%ABtan_de_Rochebou%C3%ABt"}]}, {"year": "1819", "text": "<PERSON>, Brazilian politician (d. 1880)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>,_Viscount_of_Rio_Branco\" title=\"<PERSON>, Viscount of Rio Branco\"><PERSON></a>, Brazilian politician (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>,_Viscount_of_Rio_Branco\" title=\"<PERSON>, Viscount of Rio Branco\"><PERSON></a>, Brazilian politician (d. 1880)", "links": [{"title": "<PERSON>, Viscount of Rio Branco", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON><PERSON>,_Viscount_of_Rio_Branco"}]}, {"year": "1820", "text": "<PERSON>, Italian tenor (d. 1889)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrico_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, German mathematician and academic (d. 1881)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, French painter and sculptor (d. 1899)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, American general (d. 1892)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(military_officer)\" class=\"mw-redirect\" title=\"<PERSON> (military officer)\"><PERSON></a>, American general (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(military_officer)\" class=\"mw-redirect\" title=\"<PERSON> (military officer)\"><PERSON></a>, American general (d. 1892)", "links": [{"title": "<PERSON> (military officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(military_officer)"}]}, {"year": "1823", "text": "<PERSON>, English organist and composer  (d. 1889)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON>, Portuguese writer (d. 1890)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Branco\" title=\"<PERSON><PERSON> Castelo Branco\"><PERSON><PERSON></a>, Portuguese writer (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Castelo_Branco\" title=\"<PERSON><PERSON> Castelo Branco\"><PERSON><PERSON></a>, Portuguese writer (d. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Castelo_Branco"}]}, {"year": "1828", "text": "<PERSON><PERSON>, French politician (d. 1918)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French politician (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French politician (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1834", "text": "<PERSON>, Scottish geologist and surgeon (d. 1907)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and surgeon (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish geologist and surgeon (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, English-American engineer and inventor (d. 1900)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and inventor (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and inventor (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, French poet and critic, Nobel Prize laureate (d. 1907)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1839", "text": "<PERSON>, Irish painter (d. 1922)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yeats\"><PERSON></a>, Irish painter (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Yeats\"><PERSON></a>, Irish painter (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, Japanese industrialist (d. 1931)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Shibusawa_Eiichi\" title=\"Shibusawa Eiichi\"><PERSON><PERSON><PERSON></a>, Japanese industrialist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shibus<PERSON>_E<PERSON>chi\" title=\"Shibusawa Eiichi\"><PERSON><PERSON><PERSON></a>, Japanese industrialist (d. 1931)", "links": [{"title": "Shibusawa Eiichi", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>chi"}]}, {"year": "1840", "text": "<PERSON>, German linguist and sinologist (d. 1893)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and sinologist (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German linguist and sinologist (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 15th <PERSON><PERSON><PERSON>na (d. 1928)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I\" title=\"Umegatani Tōtarō I\">Umegatani Tōtarō I</a>, Japanese sumo wrestler, the 15th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I\" title=\"Umegatani Tōtarō I\">Umegatani Tōtarō I</a>, Japanese sumo wrestler, the 15th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1928)", "links": [{"title": "Umegatani Tōtarō I", "link": "https://wikipedia.org/wiki/Umegatani_T%C5%8Dtar%C5%8D_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON>, Swedish mathematician and academic (d. 1927)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>ffler\"><PERSON><PERSON><PERSON></a>, Swedish mathematician and academic (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>Leffler\"><PERSON><PERSON><PERSON></a>, Swedish mathematician and academic (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>tta<PERSON>-<PERSON>r"}]}, {"year": "1846", "text": "<PERSON>, American physician and social reformer (d. 1922)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and social reformer (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and social reformer (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON>, Lithuanian book smuggler (d. 1918)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian book smuggler (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian book smuggler (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Norwegian financier and diplomat (d. 1932)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian financier and diplomat (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian financier and diplomat (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German theologian (d. 1935)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hewer"}]}, {"year": "1851", "text": "<PERSON><PERSON>, Dutch microbiologist and botanist (d. 1931)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microbiologist and botanist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch microbiologist and botanist (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON> of France (d. 1879)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Napol%C3%A<PERSON><PERSON>,_Prince_Imperial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Prince <PERSON></a> of France (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napol%C3%A<PERSON><PERSON>,_Prince_Imperial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Prince <PERSON></a> of France (d. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prince Imperial", "link": "https://wikipedia.org/wiki/Napol%C3%A9on,_Prince_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, English historian (d. 1936)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Russian physicist and inventor (d. 1906)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and inventor (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and inventor (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON>, Irish-American baseball player and manager (d. 1953)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American baseball player and manager (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American baseball player and manager (d. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, German violinist (d. 1933)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, South African geologist and philanthropist (d. 1951)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African geologist and philanthropist (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African geologist and philanthropist (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, French rugby player and hurdler (d. 1932)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player and hurdler (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French rugby player and hurdler (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Reichel"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French prime minister (d. 1958)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>%C3%A7ois-<PERSON>al\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French prime minister (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A<PERSON><PERSON>_<PERSON><PERSON>%C3%A7ois-<PERSON>al\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French prime minister (d. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Fran%C3%A7ois-Marsal"}]}, {"year": "1877", "text": "Léo-<PERSON>, Canadian director and producer (d. 1972)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and producer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian director and producer (d. 1972)", "links": [{"title": "Léo<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, German cardinal (d. 1946)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German cardinal (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> August <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German cardinal (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_August_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, French painter (d. 1973)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON>, American composer (d. 1947)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American runner (d. 1953)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>body\"><PERSON></a>, American runner (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>body"}]}, {"year": "1883", "text": "<PERSON>, Australian poet, author, and painter (d. 1958)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet, author, and painter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet, author, and painter (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American journalist and author (d. 1960)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Italian composer and musicologist (d. 1943)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Italian composer and musicologist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Italian composer and musicologist (d. 1943)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1885", "text": "<PERSON>, English actor (d. 1965)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Sydney_Chaplin\" title=\"Sydney Chaplin\"><PERSON></a>, English actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Chaplin\" title=\"Sydney Chaplin\"><PERSON></a>, English actor (d. 1965)", "links": [{"title": "Sydney Chaplin", "link": "https://wikipedia.org/wiki/Sydney_Chaplin"}]}, {"year": "1886", "text": "<PERSON>, Swedish tug of war player (d. 1951)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish tug of war player (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish tug of war player (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Herbert_Lindstr%C3%B6m"}]}, {"year": "1887", "text": "<PERSON>, Italian runner (d. 1925)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, American marine zoologist (1984)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/S<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"S. Stillman Berry\"><PERSON><PERSON></a>, American marine zoologist (1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. Stillman Berry\"><PERSON><PERSON></a>, American marine zoologist (1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, South African athlete (d. 1951)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, South African athlete (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, South African athlete (d. 1951)", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)"}]}, {"year": "1892", "text": "<PERSON>, Peruvian poet (d. 1938)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian poet (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian poet (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>jo"}]}, {"year": "1895", "text": "<PERSON>, French historian (d. 1988)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Italian painter (d. 1963)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American actor (d. 1970)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American novelist and screenwriter (d. 1966)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and screenwriter (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Macedonian revolutionary and assassin (d. 1964)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian revolutionary and assassin (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian revolutionary and assassin (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Belgian footballer (d. 1987)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American politician and diplomat, 22nd United States Ambassador to Japan (d. 2001)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"United States Ambassador to Japan\">United States Ambassador to Japan</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, 22nd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Japan\" class=\"mw-redirect\" title=\"United States Ambassador to Japan\">United States Ambassador to Japan</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Japan", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Japan"}]}, {"year": "1904", "text": "<PERSON>, American baseball player (d. 1974)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> My<PERSON>\"><PERSON></a>, American baseball player (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1906", "text": "<PERSON>, Spanish sociologist, author, and translator (d. 2009)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Spanish sociologist, author, and translator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Spanish sociologist, author, and translator (d. 2009)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1906", "text": "<PERSON>, Welsh-English cricketer and rugby player (d. 1944)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer and rugby player (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer and rugby player (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American baseball player (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, English-American violinist and comedian (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>\" title=\"Henny <PERSON>\"><PERSON><PERSON></a>, English-American violinist and comedian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American violinist and comedian (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>man"}]}, {"year": "1908", "text": "<PERSON>, French author and poet (d. 1944)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>l"}]}, {"year": "1908", "text": "<PERSON>, French water polo player (d. 1986)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French water polo player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French water polo player (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American director, producer, and screenwriter (d. 1966)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American songwriter (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian fencer (d. 1991)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Alad%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian fencer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alad%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian fencer (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alad%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Indian-English cricketer and politician, 8th Nawab of Pataudi (d. 1952)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English cricketer and politician, 8th <a href=\"https://wikipedia.org/wiki/Nawab_of_Pataudi\" class=\"mw-redirect\" title=\"Nawab of Pataudi\">Nawab of Pataudi</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English cricketer and politician, 8th <a href=\"https://wikipedia.org/wiki/Nawab_of_Pataudi\" class=\"mw-redirect\" title=\"Nawab of Pataudi\"><PERSON>wab of Pataudi</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>wab of Pataudi", "link": "https://wikipedia.org/wiki/Na<PERSON>_of_Pataudi"}]}, {"year": "1911", "text": "<PERSON>, Belgian lawyer and diplomat, Prime Minister of Belgium (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1911", "text": "<PERSON>, German physician, captain and mass-murderer (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, captain and mass-murderer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, captain and mass-murderer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American painter and sculptor (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American teacher, First Lady of the United States (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, French soldier (d. 1952)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9my_<PERSON><PERSON><PERSON>i"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Japanese mathematician (d. 1997)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mathematician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese mathematician (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Mercedes_M<PERSON>\" title=\"<PERSON> McCambridge\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_Mc<PERSON>ambridge\" title=\"Mercedes McCambridge\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "Mercedes McCambridge", "link": "https://wikipedia.org/wiki/Mercedes_McCambridge"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese engineer and businessman (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Iranian politician (d. 2018)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pahlbod\"><PERSON><PERSON><PERSON></a>, Iranian politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>hl<PERSON>d\" title=\"<PERSON><PERSON><PERSON> Pahlbod\"><PERSON><PERSON><PERSON></a>, Iranian politician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "1917", "text": "<PERSON><PERSON>,  Mauritian lawyer and jurist (d. 2017)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mauritian lawyer and jurist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mauritian lawyer and jurist (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American lawyer and politician (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>._Wyman"}]}, {"year": "1918", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1998)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Dutch architect (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English-American soldier and composer (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier and composer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American soldier and composer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American author and screenwriter (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, German secretary (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Tra<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ra<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German secretary (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tra<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>raud<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German secretary (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tra<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1920", "text": "<PERSON>, Australian-English actor (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American screenwriter and playwright (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and playwright (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and playwright (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, German conductor (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Lithuanian-German actress and singer (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Cornell_Borchers\" title=\"<PERSON> Borchers\"><PERSON></a>, Lithuanian-German actress and singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornell_Borchers\" title=\"<PERSON> Borchers\"><PERSON></a>, Lithuanian-German actress and singer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornell_Borchers"}]}, {"year": "1925", "text": "<PERSON>, American dancer and choreographer (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Hungarian basketball player and referee (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian basketball player and referee (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian basketball player and referee (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Mexican chemist and engineer (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>montes\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist and engineer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist and engineer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>._Miramontes"}]}, {"year": "1926", "text": "<PERSON>, American lawyer and politician (d. 1987)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and comedian (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Russian pilot, engineer, and cosmonaut (d. 1967)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, engineer, and cosmonaut (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pilot, engineer, and cosmonaut (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American sociologist and politician, 12th United States Ambassador to the United Nations (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1927", "text": "<PERSON>, American actress and dancer (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Olga <PERSON>\"><PERSON></a>, American actress and dancer (d. 2009)", "links": [{"title": "Olga <PERSON>", "link": "https://wikipedia.org/wiki/Olga_San_Juan"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 45th <PERSON><PERSON><PERSON><PERSON> (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_I\" title=\"Wakanohana Kanji I\"><PERSON><PERSON><PERSON><PERSON> Kanji I</a>, Japanese sumo wrestler, the 45th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakanohana_Kanji_I\" title=\"Wakanohana Kanji I\"><PERSON><PERSON><PERSON><PERSON> Kanji I</a>, Japanese sumo wrestler, the 45th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Kanji I", "link": "https://wikipedia.org/wiki/Waka<PERSON><PERSON>_Kanji_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1928", "text": "<PERSON><PERSON>, German opera singer (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German opera singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German opera singer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American singer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian-American physicist and academic (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-American physicist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-American physicist and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Austrian actress (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian actress (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American pianist and composer (d. 2001)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (d. 2001)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Japanese composer (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese composer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Brazilian theatre director, writer and politician (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian theatre director, writer and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian theatre director, writer and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al"}]}, {"year": "1931", "text": "<PERSON>, American-South Korean musicologist and composer (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-South Korean musicologist and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-South Korean musicologist and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English philosopher and academic", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian lawyer and politician, 22nd Canadian Minister of Labour (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Canadian_politician)\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian lawyer and politician, 22nd <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (d. 2003)", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Canadian_politician)"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "1932", "text": "<PERSON>, American baseball player and manager (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American astronaut (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Austrian mountaineer and author", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mountaineer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian politician (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2020)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1933", "text": "<PERSON>, English architect and academic, co-founded Temenos Academy (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic, co-founded <a href=\"https://wikipedia.org/wiki/Temenos_Academy\" title=\"Temenos Academy\">Temenos Academy</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic, co-founded <a href=\"https://wikipedia.org/wiki/Temenos_Academy\" title=\"Temenos Academy\">Temenos Academy</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Temenos Academy", "link": "https://wikipedia.org/wiki/Temenos_Academy"}]}, {"year": "1933", "text": "<PERSON>, American banker, financier, and philanthropist", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, financier, and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, financier, and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian lawyer and politician, 24th Governor General of Canada (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_<PERSON>hyn"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1934", "text": "<PERSON>, English violinist and conductor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American football player and coach (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Spanish soprano and actress (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish soprano and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish soprano and actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Colombian bullfighter (d. 1987)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Pepe_C%C3%A1ceres\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian bullfighter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pepe_C%C3%A1ceres\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian bullfighter (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pepe_C%C3%A1ceres"}]}, {"year": "1936", "text": "<PERSON>, Armenian-American inventor, invented the MRI (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Armenian-American inventor, invented the <a href=\"https://wikipedia.org/wiki/Magnetic_resonance_imaging\" title=\"Magnetic resonance imaging\">MRI</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Dam<PERSON>\"><PERSON></a>, Armenian-American inventor, invented the <a href=\"https://wikipedia.org/wiki/Magnetic_resonance_imaging\" title=\"Magnetic resonance imaging\">MRI</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Magnetic resonance imaging", "link": "https://wikipedia.org/wiki/Magnetic_resonance_imaging"}]}, {"year": "1936", "text": "<PERSON>, American folk singer-songwriter and guitarist (d. 2001)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer-songwriter and guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English historian, journalist, and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, journalist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 2017)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Attil<PERSON>_<PERSON>\" title=\"Attil<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Attil<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attilio_Nicora"}]}, {"year": "1937", "text": "<PERSON>, Israeli-American psychologist and academic (d. 1996)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American psychologist and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American psychologist and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Argentinian footballer and manager", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Canadian politician and teacher", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Yvon_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician and teacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yvon_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian politician and teacher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yvon_C%C3%B4t%C3%A9"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Azerbaijani pianist and composer (d. 1979)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani pianist and composer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani pianist and composer (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vagi<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Dutch academic and politician, Dutch Ministry of Housing, Spatial Planning and the Environment", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch academic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Housing,_Spatial_Planning_and_the_Environment_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Housing, Spatial Planning and the Environment (Netherlands)\">Dutch Ministry of Housing, Spatial Planning and the Environment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>nk\"><PERSON></a>, Dutch academic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Housing,_Spatial_Planning_and_the_Environment_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Housing, Spatial Planning and the Environment (Netherlands)\">Dutch Ministry of Housing, Spatial Planning and the Environment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Pronk"}, {"title": "Ministry of Housing, Spatial Planning and the Environment (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Housing,_Spatial_Planning_and_the_Environment_(Netherlands)"}]}, {"year": "1940", "text": "<PERSON>, English guitarist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Indian cartoonist (d. 2025)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, Indian cartoonist (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)\" title=\"<PERSON><PERSON> (cartoonist)\"><PERSON><PERSON></a>, Indian cartoonist (d. 2025)", "links": [{"title": "<PERSON><PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(cartoonist)"}]}, {"year": "1941", "text": "<PERSON>, Italian director and screenwriter (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Ivorian soldier and politician, 3rd President of Côte d'Ivoire (d. 2002)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9%C3%AF\" title=\"<PERSON>\"><PERSON></a>, Ivorian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9%C3%AF\" title=\"<PERSON>\"><PERSON></a>, Ivorian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire\" class=\"mw-redirect\" title=\"President of Côte d'Ivoire\">President of Côte d'Ivoire</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_Gu%C3%A9%C3%AF"}, {"title": "President of Côte d'Ivoire", "link": "https://wikipedia.org/wiki/President_of_C%C3%B4te_d%27Ivoire"}]}, {"year": "1941", "text": "<PERSON>, American game show host and television personality (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American game show host and television personality (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American game show host and television personality (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oolery"}]}, {"year": "1942", "text": "<PERSON>, Canadian-American ice hockey player (d. 1996)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, French politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Chinese-Taiwanese politician, Governor of Taiwan Province", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Taiwanese politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taiwan_Province\" class=\"mw-redirect\" title=\"Governor of Taiwan Province\">Governor of Taiwan Province</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Taiwanese politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Taiwan_Province\" class=\"mw-redirect\" title=\"Governor of Taiwan Province\">Governor of Taiwan Province</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Taiwan Province", "link": "https://wikipedia.org/wiki/Governor_of_Taiwan_Province"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch race car driver", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Gi<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Dutch race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gi<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Dutch race car driver", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2020)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Peruvian diplomat", "html": "1943 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian diplomat", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American biologist, zoologist, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, zoologist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, zoologist, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German race car driver", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Dutch conductor, composer, and music arranger (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor, composer, and music arranger (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch conductor, composer, and music arranger (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American computer scientist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Norwegian harmonica player and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian harmonica player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian harmonica player and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>n"}]}, {"year": "1946", "text": "<PERSON>, English economist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON> <PERSON><PERSON>, American New Age teacher and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American New Age teacher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American New Age teacher and author", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, French singer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, French singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, French singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian singer-songwriter and director", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%A9r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%A9r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Qu%C3%A9r%C3%A9"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian actor and singer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American-French singer-songwriter and journalist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French singer-songwriter and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English bishop", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1950", "text": "<PERSON>, Canadian actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Bosnian footballer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Edhem_%C5%A0ljivo\" title=\"<PERSON><PERSON> Šljivo\"><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edhem_%C5%A0ljivo\" title=\"<PERSON><PERSON>ji<PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edhem_%C5%A0ljivo"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>,  Algerian footballer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Abdelma<PERSON>_Bourebbou\" title=\"Abdel<PERSON><PERSON> Bo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdelma<PERSON>_Bourebbou\" title=\"Abdelma<PERSON> Bourebbou\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdelma<PERSON>_Bourebbou"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%A5\" title=\"<PERSON><PERSON> Brå\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Br%C3%A5\" title=\"<PERSON><PERSON> Brå\"><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oddvar_Br%C3%A5"}]}, {"year": "1951", "text": "<PERSON>, American football player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, French long-distance runner", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French long-distance runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German conductor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, German chess player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American computer scientist and programmer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1954", "text": "<PERSON>, English serial killer (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Colin_Ireland\" title=\"Colin Ireland\"><PERSON></a>, English serial killer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colin_Ireland\" title=\"Colin Ireland\"><PERSON></a>, English serial killer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Colin_Ireland"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter, guitarist, and actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Na<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jimmy Nail\"><PERSON></a>, English singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nail"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(musician)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Sri Lankan-Australian cricketer and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Dav_Whatmore\" title=\"Dav Whatmore\"><PERSON>v <PERSON><PERSON></a>, Sri Lankan-Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dav_Whatmore\" title=\"Dav Whatmore\"><PERSON>v <PERSON><PERSON></a>, Sri Lankan-Australian cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dav_Whatmore"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)\" title=\"<PERSON> (rock musician)\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actress", "links": [{"title": "<PERSON> (rock musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rock_musician)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Russian ice dancer and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON><PERSON><PERSON> (figure skater)\"><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON><PERSON><PERSON> (figure skater)\"><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(figure_skater)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Lithuanian physicist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Astrauskas\" title=\"<PERSON><PERSON><PERSON> Astrauskas\"><PERSON><PERSON><PERSON></a>, Lithuanian physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Astrauskas\" title=\"<PERSON><PERSON><PERSON> Astrauskas\"><PERSON><PERSON><PERSON></a>, Lithuanian physicist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Riman<PERSON>_Astrauskas"}]}, {"year": "1955", "text": "<PERSON>, Brazilian director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Belgian actress and singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American sports anchor and reporter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports anchor and reporter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports anchor and reporter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian politician (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian politician (d. 2013)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Japanese boxer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> W<PERSON>\"><PERSON><PERSON></a>, Japanese boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nabe"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American football player and executive", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Newsome\" title=\"Ozzie Newsome\"><PERSON><PERSON>ome</a>, American football player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Newsome\" title=\"Ozzie Newsome\"><PERSON><PERSON> Newsome</a>, American football player and executive", "links": [{"title": "Ozzie Newsome", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Newsome"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Japanese writer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss lawyer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>hlumpf\" title=\"<PERSON><PERSON>id<PERSON>-<PERSON>hlumpf\"><PERSON><PERSON>-<PERSON>ump<PERSON></a>, Swiss lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>hlumpf\" title=\"<PERSON><PERSON> Wid<PERSON>-<PERSON>hlumpf\"><PERSON><PERSON>id<PERSON>-<PERSON>hlumpf</a>, Swiss lawyer and politician", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wid<PERSON>-<PERSON>hlumpf"}]}, {"year": "1958", "text": "<PERSON>, Mexican-American journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(news_anchor)\" title=\"<PERSON> (news anchor)\"><PERSON></a>, Mexican-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(news_anchor)\" title=\"<PERSON> (news anchor)\"><PERSON></a>, Mexican-American journalist and author", "links": [{"title": "<PERSON> (news anchor)", "link": "https://wikipedia.org/wiki/<PERSON>_(news_anchor)"}]}, {"year": "1958", "text": "<PERSON>, Australian pianist and composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American author (d. 2004)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American composer and educator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian cricketer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, American rapper and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Flavor_Flav\" title=\"Flavor Flav\"><PERSON><PERSON><PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flavor_Flav\" title=\"Flavor Flav\"><PERSON><PERSON><PERSON></a>, American rapper and actor", "links": [{"title": "Flavor Flav", "link": "https://wikipedia.org/wiki/Flavor_Flav"}]}, {"year": "1959", "text": "<PERSON>, American baseball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1959", "text": "<PERSON>, American musician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Norwegian economist and politician, 27th Prime Minister of Norway, 13th Secretary General of NATO", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian economist and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a>, 13th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian economist and politician, 27th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a>, 13th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1959", "text": "<PERSON>, American actor stuntman and wrestler (d. 2024)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor stuntman and wrestler (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor stuntman and wrestler (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English comedian, actress and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English businessman and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian author, illustrator, and businessman, founded McFarlane Toys", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, illustrator, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Toys\" title=\"McFarlane Toys\"><PERSON>c<PERSON>arlane Toys</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, illustrator, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Toys\" title=\"McFarlane Toys\">McFarlane Toys</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McFarlane Toys", "link": "https://wikipedia.org/wiki/<PERSON>cF<PERSON><PERSON>_Toys"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, French race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Franck_Fr%C3%A9on\" title=\"<PERSON>an<PERSON>\"><PERSON>an<PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran<PERSON>_<PERSON>%C3%A9on\" title=\"<PERSON>an<PERSON>\"><PERSON>an<PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franck_Fr%C3%A9on"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, French athlete", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English actor and singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand actor and singer (d. 2002)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_actor)\" title=\"<PERSON> (New Zealand actor)\"><PERSON></a>, New Zealand actor and singer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_actor)\" title=\"<PERSON> (New Zealand actor)\"><PERSON></a>, New Zealand actor and singer (d. 2002)", "links": [{"title": "<PERSON> (New Zealand actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_Zealand_actor)"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Filipino actress (d. 2024)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Swiss racing cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss racing cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss racing cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1965", "text": "<PERSON>, Canadian-English economist and politician, Prime Minister of Canada delegate", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English economist and politician, Prime Minister of Canada delegate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English economist and politician, Prime Minister of Canada delegate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Italian-Brazilian actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Cristiana_Reali\" title=\"Cristiana Reali\"><PERSON><PERSON><PERSON></a>, Italian-Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cristiana_Reali\" title=\"Cristiana Reali\"><PERSON><PERSON><PERSON></a>, Italian-Brazilian actress", "links": [{"title": "Cristiana Reali", "link": "https://wikipedia.org/wiki/Cristiana_Reali"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, German musician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/H.P._Baxxter\" class=\"mw-redirect\" title=\"H.P. Baxxter\"><PERSON><PERSON><PERSON><PERSON></a>, German musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H.P._Baxxter\" class=\"mw-redirect\" title=\"H.P. Baxxter\"><PERSON><PERSON><PERSON><PERSON></a>, German musician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H.P._Baxxter"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Canadian cross-country cyclist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian cross-country cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian cross-country cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer and violinist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American musician and novelist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and novelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American bluegrass mandolin player, singer and songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass mandolin player, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass mandolin player, singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Swiss alpine skier", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss alpine skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss alpine skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American comedian and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Barbadian cricketer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Russian athlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian athlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Greek basketball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player (d. 2019)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2019)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Moroccan long-distance runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Isma%C3%AFl_Sghyr\" title=\"Ismaïl Sghyr\"><PERSON><PERSON><PERSON><PERSON></a>, French-Moroccan long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isma%C3%AFl_Sghyr\" title=\"Ismaïl Sghyr\"><PERSON><PERSON><PERSON><PERSON></a>, French-Moroccan long-distance runner", "links": [{"title": "Is<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%C3%AFl_Sghyr"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Kazakhstani road bicycle racer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani road bicycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American boxer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Vonda_Ward\" title=\"Vonda Ward\"><PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vonda_Ward\" title=\"Vonda Ward\"><PERSON><PERSON></a>, American boxer", "links": [{"title": "Vonda Ward", "link": "https://wikipedia.org/wiki/Vonda_Ward"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Greek footballer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Anatolakis"}]}, {"year": "1974", "text": "<PERSON>, French actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Zimbabwean cricketer (d. 2023)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Streak\" title=\"Heath Streak\"><PERSON></a>, Zimbabwean cricketer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heath_Streak\" title=\"Heath Streak\"><PERSON></a>, Zimbabwean cricketer (d. 2023)", "links": [{"title": "<PERSON> Streak", "link": "https://wikipedia.org/wiki/Heath_Streak"}]}, {"year": "1975", "text": "<PERSON>, Argentine actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Castro\"><PERSON></a>, Argentine actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English model and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Guillory\" title=\"<PERSON> Guillory\"><PERSON></a>, English model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G<PERSON>lory\" title=\"<PERSON> Guillory\"><PERSON></a>, English model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sienna_Guillory"}]}, {"year": "1975", "text": "<PERSON>, French archer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ll\" title=\"<PERSON> Cantrell\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ll\" title=\"<PERSON>ll\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ll"}]}, {"year": "1976", "text": "<PERSON>, Qatari chess Grandmaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Qatari chess Grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Qatari chess Grandmaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, French handballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French handballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French handballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Swedish cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gskog"}]}, {"year": "1976", "text": "<PERSON>, Dominican baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA%C3%B1ez_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA%C3%B1ez_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/Abraham_N%C3%BA%C3%B1ez_(infielder)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Spanish actress and dancer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/M%C3%B3nica_Cruz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actress and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%B3nica_Cruz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish actress and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%B3nica_Cruz"}]}, {"year": "1977", "text": "<PERSON>, German swimmer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American fashion model, television personality, and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion model, television personality, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion model, television personality, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German actress and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, South Korean baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, South Korean baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swiss equestrian", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss equestrian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, French handball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French handball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Finnish keyboard player and songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish keyboard player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Estonian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, French road bicycle racer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian pole vaulter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian pole vaulter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Guadeloupean footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guadeloupean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "Riley <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Spanish road bicycle racer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON>s_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON>s_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish road bicycle racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Brandon_League\" title=\"Brandon League\">Brandon League</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brandon_League\" title=\"Brandon League\">Brandon League</a>, American baseball player", "links": [{"title": "Brandon League", "link": "https://wikipedia.org/wiki/Brandon_League"}]}, {"year": "1983", "text": "<PERSON>, French road bicycle racer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French road bicycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Tram<PERSON>_<PERSON>\" title=\"Tram<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tram<PERSON>_<PERSON>\" title=\"Tram<PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tram<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Irish comedienne and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Aisling_Be<PERSON>\" title=\"Aisling Bea\"><PERSON><PERSON><PERSON><PERSON></a>, Irish comedienne and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aisling_Be<PERSON>\" title=\"Aisling Bea\"><PERSON><PERSON><PERSON><PERSON></a>, Irish comedienne and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aisling_Bea"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)\" title=\"<PERSON> (offensive tackle)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (offensive tackle)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_tackle)"}]}, {"year": "1984", "text": "<PERSON>, Kenyan long-distance runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan long-distance runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Hosea_Gear\" title=\"Hosea Gear\">Hosea Gear</a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hosea_Gear\" title=\"Hosea Gear\">Hosea Gear</a>, New Zealand rugby player", "links": [{"title": "Hosea Gear", "link": "https://wikipedia.org/wiki/Ho<PERSON>_Gear"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French athlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Panamanian singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Eddy_Lover\" title=\"Eddy Lover\"><PERSON></a>, Panamanian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eddy_Lover\" title=\"Eddy Lover\"><PERSON></a>, Panamanian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Russian hammer thrower", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Aleksei_Sokirskiy\" class=\"mw-redirect\" title=\"Aleksei Sokirskiy\"><PERSON><PERSON><PERSON> Sokirski<PERSON></a>, Russian hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksei_Sokirskiy\" class=\"mw-redirect\" title=\"Aleksei Sokirskiy\"><PERSON><PERSON><PERSON></a>, Russian hammer thrower", "links": [{"title": "Aleksei Sokirskiy", "link": "https://wikipedia.org/wiki/Aleksei_Sokirskiy"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON> Douglas\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Indonesian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ssa"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese figure skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, French football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and rapper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jhen%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jhen%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jhen%C3%A9_<PERSON>ko"}]}, {"year": "1988", "text": "<PERSON>, Canadian speed skater", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer_born_1988)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1988)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer_born_1988)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1988)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer_born_1988)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Marches%C3%ADn\" title=\"A<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Marches%C3%ADn\" title=\"Agust<PERSON> March<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Marches%C3%ADn"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Tlust%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Tlust%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Tlust%C3%BD"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South Korean actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"Jung So-min\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> So-min\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, French racing cyclist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French racing cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1991", "text": "<PERSON>, American football player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1991", "text": "<PERSON>, American bassist", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1992", "text": " <PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English rugby union player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby union player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1993", "text": "<PERSON>, French model and beauty queen, Miss France 2013", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Marine_Lo<PERSON>\" title=\"<PERSON> Lo<PERSON>helin\"><PERSON></a>, French model and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_France_2013\" title=\"Miss France 2013\">Miss France 2013</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marine_Lo<PERSON>\" title=\"<PERSON> Lorphelin\"><PERSON></a>, French model and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_France_2013\" title=\"Miss France 2013\">Miss France 2013</a>", "links": [{"title": "Marine Lorphelin", "link": "https://wikipedia.org/wiki/Marine_Lo<PERSON>helin"}, {"title": "Miss France 2013", "link": "https://wikipedia.org/wiki/Miss_France_2013"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Colombian singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Colombian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Colombian singer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1994", "text": "<PERSON>, Cameroonian basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Sierra_Mc<PERSON>lain\" title=\"<PERSON> McClain\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sierra_McClain\" title=\"<PERSON> McClain\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> McClain", "link": "https://wikipedia.org/wiki/Sierra_M<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Lithuanian figure skater", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Inga_Janulevi%C4%8Di%C5%ABt%C4%97\" title=\"Inga Janulevičiūtė\">Inga Janulevičiūtė</a>, Lithuanian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inga_Janulevi%C4%8Di%C5%ABt%C4%97\" title=\"Inga Janulevičiūtė\">Inga Janulevičiū<PERSON></a>, Lithuanian figure skater", "links": [{"title": "Inga Jan<PERSON>vičiū<PERSON>", "link": "https://wikipedia.org/wiki/Inga_Janulevi%C4%8Di%C5%ABt%C4%97"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Alex<PERSON>\" title=\"<PERSON><PERSON><PERSON> Alexus\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ji<PERSON> Alexus\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Ajiona Alexus", "link": "https://wikipedia.org/wiki/Ajiona_Alexus"}]}, {"year": "1996", "text": "<PERSON>, English footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ty<PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ty<PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian baseball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, Canadian baseball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "37", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 42 BC)", "html": "37 - AD 37 - <a href=\"https://wikipedia.org/wiki/Tiber<PERSON>\" title=\"<PERSON>iber<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 42 BC)", "no_year_html": "AD 37 - <a href=\"https://wikipedia.org/wiki/Tiber<PERSON>\" title=\"<PERSON>iber<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 42 BC)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiberius"}]}, {"year": "455", "text": "<PERSON><PERSON><PERSON>, Roman emperor (assassinated;  b. 419)", "html": "455 - <a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\"><PERSON><PERSON><PERSON> III</a>, Roman emperor (assassinated; b. 419)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_III\" title=\"Valentinian III\"><PERSON><PERSON><PERSON> III</a>, Roman emperor (assassinated; b. 419)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valentinian_III"}]}, {"year": "455", "text": "<PERSON><PERSON><PERSON>, Roman courtier (primicerius sacri cubiculi )", "html": "455 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(primicerius_sacri_cubiculi)\" title=\"<PERSON><PERSON><PERSON> (primicerius sacri cubiculi)\"><PERSON><PERSON><PERSON></a>, Roman courtier (<i><a href=\"https://wikipedia.org/wiki/Primicerius\" title=\"Primicerius\">primicerius sacri cubiculi</a> </i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(primicerius_sacri_cubiculi)\" title=\"<PERSON><PERSON><PERSON> (primicerius sacri cubiculi)\"><PERSON><PERSON><PERSON></a>, Roman courtier (<i><a href=\"https://wikipedia.org/wiki/Primicerius\" title=\"Primicerius\">primicerius sacri cubiculi</a> </i>)", "links": [{"title": "<PERSON><PERSON><PERSON> (primicerius sacri cubiculi)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(primicerius_sacri_cubiculi)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Primicerius"}]}, {"year": "842", "text": "<PERSON>, chancellor of the Tang dynasty", "html": "842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mia<PERSON>\" title=\"<PERSON> Mian\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mia<PERSON>\" title=\"<PERSON> Mian\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang dynasty</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}]}, {"year": "933", "text": "<PERSON><PERSON>, Egyptian commander and politician, Abbasid Governor of Egypt", "html": "933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Khazari\" title=\"Takin al-Khazari\"><PERSON><PERSON>hazar<PERSON></a>, Egyptian commander and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Abbasid Governor of Egypt</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-Khazari\" title=\"<PERSON>kin al-Khazari\"><PERSON><PERSON>hazar<PERSON></a>, Egyptian commander and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt\" class=\"mw-redirect\" title=\"List of governors of Islamic Egypt\">Abbasid Governor of Egypt</a>", "links": [{"title": "Takin al-Khazari", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_al-<PERSON>hazari"}, {"title": "List of governors of Islamic Egypt", "link": "https://wikipedia.org/wiki/List_of_governors_of_Islamic_Egypt"}]}, {"year": "943", "text": "<PERSON>, Chinese official and chancellor (b. 877)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese official and chancellor (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese official and chancellor (b. 877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pi_<PERSON>"}]}, {"year": "1021", "text": "<PERSON><PERSON><PERSON> of Cologne, German archbishop and saint (b. 970)", "html": "1021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON><PERSON> of Cologne\"><PERSON><PERSON><PERSON> of Cologne</a>, German archbishop and saint (b. 970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON><PERSON> of Cologne\"><PERSON><PERSON><PERSON> of Cologne</a>, German archbishop and saint (b. 970)", "links": [{"title": "<PERSON><PERSON><PERSON> of Cologne", "link": "https://wikipedia.org/wiki/Heribert_of_Cologne"}]}, {"year": "1072", "text": "<PERSON><PERSON> of Hamburg, German archbishop (b. 1000)", "html": "1072 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hamburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Hamburg\"><PERSON><PERSON> of Hamburg</a>, German archbishop (b. 1000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hamburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Hamburg\"><PERSON><PERSON> of Hamburg</a>, German archbishop (b. 1000)", "links": [{"title": "<PERSON><PERSON> of Hamburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hamburg"}]}, {"year": "1181", "text": "<PERSON>, Count of Champagne", "html": "1181 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON>, Count of Champagne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON>, Count of Champagne</a>", "links": [{"title": "<PERSON>, Count of Champagne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne"}]}, {"year": "1185", "text": "<PERSON> of Jerusalem (b. 1161)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Jerusalem\" title=\"<PERSON> IV of Jerusalem\"><PERSON> IV of Jerusalem</a> (b. 1161)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Jerusalem\" title=\"<PERSON> IV of Jerusalem\"><PERSON> IV of Jerusalem</a> (b. 1161)", "links": [{"title": "<PERSON> IV of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_IV_of_Jerusalem"}]}, {"year": "1279", "text": "<PERSON> Dammartin, Queen consort of Castile and León (b. 1216)", "html": "1279 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Ponthieu\" title=\"<PERSON>, Countess of Ponthieu\"><PERSON> Dammartin</a>, Queen consort of Castile and León (b. 1216)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Ponthieu\" title=\"<PERSON>, Countess of Ponthieu\"><PERSON> of Dammartin</a>, Queen consort of Castile and León (b. 1216)", "links": [{"title": "<PERSON>, Countess of Ponthieu", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_<PERSON>"}]}, {"year": "1405", "text": "<PERSON>, Countess of Flanders (b. 1350)", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders\" title=\"<PERSON>, Countess of Flanders\"><PERSON>, Countess of Flanders</a> (b. 1350)", "links": [{"title": "<PERSON>, Countess of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Flanders"}]}, {"year": "1410", "text": "<PERSON>, 1st Earl of Somerset, French-English admiral and politician, Lord Warden of the Cinque Ports (b. 1373)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset\" title=\"<PERSON>, 1st Earl of Somerset\"><PERSON>, 1st Earl of Somerset</a>, French-English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1373)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset\" title=\"<PERSON>, 1st Earl of Somerset\"><PERSON>, 1st Earl of Somerset</a>, French-English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1373)", "links": [{"title": "<PERSON>, 1st Earl of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Somerset"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1457", "text": "<PERSON><PERSON><PERSON>, Hungarian politician (b. 1433)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian politician (b. 1433)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1485", "text": "<PERSON>, queen of <PERSON> III of England (b. 1456)", "html": "1485 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1456)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1456)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1559", "text": "<PERSON>, English-Irish politician Lord Deputy of Ireland (b. 1496)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Deputy_of_Ireland)\" title=\"<PERSON> (Lord Deputy of Ireland)\"><PERSON></a>, English-Irish politician <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Lord_Deputy_of_Ireland)\" title=\"<PERSON> (Lord Deputy of Ireland)\"><PERSON></a>, English-Irish politician <a href=\"https://wikipedia.org/wiki/Lord_Deputy_of_Ireland\" title=\"Lord Deputy of Ireland\">Lord Deputy of Ireland</a> (b. 1496)", "links": [{"title": "<PERSON> (Lord Deputy of Ireland)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Lord_Deputy_of_Ireland)"}, {"title": "Lord Deputy of Ireland", "link": "https://wikipedia.org/wiki/Lord_Deputy_of_Ireland"}]}, {"year": "1649", "text": "<PERSON>, French-Canadian missionary and saint (b. 1593)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9beuf\" title=\"<PERSON>\"><PERSON></a>, French-Canadian missionary and saint (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9beuf\" title=\"<PERSON>\"><PERSON></a>, French-Canadian missionary and saint (b. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>r%C3%A9be<PERSON>"}]}, {"year": "1679", "text": "<PERSON>, English general and politician, 19th Governor of the Massachusetts Bay Colony (b. 1616)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (b. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (b. 1616)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of the Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony"}]}, {"year": "1698", "text": "<PERSON><PERSON>, Danish countess, author of <PERSON><PERSON> Minde (b. 1621)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish countess, author of <i><a href=\"https://wikipedia.org/wiki/Jam<PERSON>_Minde\" title=\"Jammers Minde\"><PERSON>mers Minde</a></i> (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish countess, author of <i><a href=\"https://wikipedia.org/wiki/Jammers_Minde\" title=\"Jammers Minde\">Jammers Minde</a></i> (b. 1621)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Jammers Minde", "link": "https://wikipedia.org/wiki/Jammers_Minde"}]}, {"year": "1721", "text": "<PERSON> the Elder, English politician, Postmaster General of the United Kingdom (b. 1657)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, English politician, <a href=\"https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom\" title=\"Postmaster General of the United Kingdom\">Postmaster General of the United Kingdom</a> (b. 1657)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}, {"title": "Postmaster General of the United Kingdom", "link": "https://wikipedia.org/wiki/Postmaster_General_of_the_United_Kingdom"}]}, {"year": "1736", "text": "<PERSON>, Italian composer (b. 1710)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, American minister and academic (b. 1670)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, American minister and academic (b. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, American minister and academic (b. 1670)", "links": [{"title": "<PERSON> (clergyman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)"}]}, {"year": "1738", "text": "<PERSON>, German architect, designed the Dresden Frauenkirche (b. 1666)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A4hr\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A4hr\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Dresden_Frauenkirche\" class=\"mw-redirect\" title=\"Dresden Frauenkirche\">Dresden Frauenkirche</a> (b. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_B%C3%A4hr"}, {"title": "Dresden Frauenkirche", "link": "https://wikipedia.org/wiki/Dresden_Frauenkirche"}]}, {"year": "1747", "text": "<PERSON> <PERSON>, Prince of Anhalt-Zerbst (b. 1690)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/Christian_August,_Prince_of_Anhalt-Zerbst\" title=\"Christian August, Prince of Anhalt-Zerbst\"><PERSON> August, Prince of Anhalt-Zerbst</a> (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_August,_Prince_of_Anhalt-Zerbst\" title=\"Christian August, Prince of Anhalt-Zerbst\">Christian August, Prince of Anhalt-Zerbst</a> (b. 1690)", "links": [{"title": "Christian August, Prince of Anhalt-Zerbst", "link": "https://wikipedia.org/wiki/Christian_August,_Prince_of_Anhalt-Zerbst"}]}, {"year": "1804", "text": "<PERSON>, Finnish professor and historian (b. 1739)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish professor and historian (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish professor and historian (b. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, American ocean navigator and mathematician (b. 1773)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ocean navigator and mathematician (b. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ocean navigator and mathematician (b. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French physicist and psychologist (b. 1791)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Sa<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, French physicist and psychologist (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Sa<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, French physicist and psychologist (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Savart"}]}, {"year": "1868", "text": "<PERSON>, American politician, sponsor of <PERSON><PERSON><PERSON> (b. 1814)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician, sponsor of <a href=\"https://wikipedia.org/wiki/Wilmot_Proviso\" title=\"Wilmot Proviso\">Wil<PERSON></a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician, sponsor of <a href=\"https://wikipedia.org/wiki/Wilmot_Proviso\" title=\"Wil<PERSON> Proviso\">Wil<PERSON>viso</a> (b. 1814)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(politician)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilmot_Proviso"}]}, {"year": "1884", "text": "<PERSON>, American baseball player (b. 1855)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Croft"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, French politician (b. 1801)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Hippolyte_Carnot\" title=\"Hippolyte Carnot\"><PERSON><PERSON><PERSON></a>, French politician (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hippolyte_Carnot\" title=\"Hippolyte Carnot\"><PERSON><PERSON><PERSON></a>, French politician (b. 1801)", "links": [{"title": "Hippol<PERSON>", "link": "https://wikipedia.org/wiki/Hippolyte_Carnot"}]}, {"year": "1892", "text": "<PERSON>, American politician (b. 1827)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American politician (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)\" title=\"<PERSON> (U.S. politician)\"><PERSON></a>, American politician (b. 1827)", "links": [{"title": "<PERSON> (U.S. politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(U.S._politician)"}]}, {"year": "1898", "text": "<PERSON>, English author and illustrator (b. 1872)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American journalist and politician, 26th Mayor of Chicago (b. 1823)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 26th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1903", "text": "<PERSON>, American justice of the peace (b. 1825)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American justice of the peace (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American justice of the peace (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Irish republican and journalist (b. 1830)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Lear<PERSON>_(Fenian)\" title=\"<PERSON> (Fenian)\"><PERSON></a>, Irish republican and journalist (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Lear<PERSON>_(Fenian)\" title=\"<PERSON> (Fenian)\"><PERSON></a>, Irish republican and journalist (b. 1830)", "links": [{"title": "<PERSON> (Fenian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary_(Fen<PERSON>)"}]}, {"year": "1912", "text": "<PERSON>, Austrian theater director (b. 1854)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian theater director (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian theater director (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French journalist (b. 1858)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gaston_Calmette"}]}, {"year": "1914", "text": "<PERSON>, Swiss lawyer and politician, Nobel Prize laureate (b. 1843)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1914", "text": "<PERSON>, Scottish oceanographer, biologist, and limnologist (b. 1841)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(oceanographer)\" title=\"<PERSON> (oceanographer)\"><PERSON></a>, Scottish oceanographer, biologist, and limnologist (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(oceanographer)\" title=\"<PERSON> (oceanographer)\"><PERSON></a>, Scottish oceanographer, biologist, and limnologist (b. 1841)", "links": [{"title": "<PERSON> (oceanographer)", "link": "https://wikipedia.org/wiki/<PERSON>_(oceanographer)"}]}, {"year": "1925", "text": "<PERSON>, German bacteriologist and hygienist (b. 1866)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August von <PERSON>\"><PERSON> <PERSON></a>, German bacteriologist and hygienist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August von <PERSON>\">August <PERSON></a>, German bacteriologist and hygienist (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Spanish general and politician, Prime Minister of Spain (b. 1870)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (b. 1870)", "links": [{"title": "Miguel <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1935", "text": "<PERSON>, Scottish physician and physiologist, Nobel Prize laureate (b. 1876)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, Scottish physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(physiologist)\" title=\"<PERSON> (physiologist)\"><PERSON></a>, Scottish physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1876)", "links": [{"title": "<PERSON> (physiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(physiologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Latvian-Danish chess player (b. 1886)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-Danish chess player (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-Danish chess player (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, French actress, journalist, and activist (b. 1864)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, journalist, and activist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, journalist, and activist (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English politician, Secretary of State for Foreign and Commonwealth Affairs, Nobel Prize laureate (b. 1863)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1937", "text": "<PERSON>, Estonian orientalist and sinologist (b. 1877)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein\" title=\"<PERSON>\"><PERSON></a>, Estonian orientalist and sinologist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein\" title=\"<PERSON>\"><PERSON></a>, Estonian orientalist and sinologist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl-Holstein"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Swedish author and academic, Nobel Prize laureate (b. 1858)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se<PERSON>_<PERSON>l%C3%B6f"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German poet (b. 1874)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/B%C3%B6<PERSON>_von_M%C3%BCnchhausen\" title=\"<PERSON><PERSON><PERSON> von Münchhausen\"><PERSON><PERSON><PERSON> von <PERSON>hausen</a>, German poet (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%B6<PERSON>_von_M%C3%BCnchhausen\" title=\"<PERSON><PERSON><PERSON> von Münchhausen\"><PERSON><PERSON><PERSON> von M<PERSON>hausen</a>, German poet (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON> von <PERSON>", "link": "https://wikipedia.org/wiki/B%C3%B6<PERSON>_von_M%C3%BCnchhausen"}]}, {"year": "1955", "text": "<PERSON>, French-Russian painter and illustrator (b. 1914)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl\" title=\"<PERSON>\"><PERSON></a>, French-Russian painter and illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl\" title=\"<PERSON>\"><PERSON></a>, French-Russian painter and illustrator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ABl"}]}, {"year": "1957", "text": "Con<PERSON><PERSON>, Romanian-French sculptor, painter, and photographer (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-French sculptor, painter, and photographer (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-French sculptor, painter, and photographer (b. 1876)", "links": [{"title": "Constant<PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Br%C3%A2ncu%C8%99i"}]}, {"year": "1958", "text": "<PERSON>, American baseball player (b. 1891)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Chinese general and politician (b. 1903)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech violinist and conductor (b. 1883)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech violinist and conductor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech violinist and conductor (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author and photographer (b. 1874)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, German activist (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German activist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1967", "text": "<PERSON>, Irish poet (b. 1893)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Italian-American pianist and composer (b. 1895)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>esco\" title=\"<PERSON>\"><PERSON></a>, Italian-American pianist and composer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>esco\" title=\"<PERSON>\"><PERSON></a>, Italian-American pianist and composer (b. 1895)", "links": [{"title": "<PERSON>-Tedesco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Tedesco"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Swedish poet and translator (b. 1907)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish poet and translator (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish poet and translator (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gunnar_Ekel%C3%B6f"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American singer (b. 1945)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/1970\" title=\"1970\">1970</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1970\" title=\"1970\">1970</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1945)", "links": [{"title": "1970", "link": "https://wikipedia.org/wiki/1970"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress (b. 1901)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American lawyer and politician, 47th Governor of New York (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1972", "text": "<PERSON>, American baseball player (b. 1898)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Traynor\"><PERSON></a>, American baseball player (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Traynor\"><PERSON></a>, American baseball player (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nor"}]}, {"year": "1975", "text": "T-<PERSON>, American singer-songwriter and guitarist (b. 1910)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON> Walker\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T-<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1910)", "links": [{"title": "T<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T-<PERSON>_Walker"}]}, {"year": "1977", "text": "<PERSON>, Lebanese lawyer and politician (b. 1917)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French economist and politician (b. 1888)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and politician (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Polish-American painter (b. 1898)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor and television host (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and television host (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Polish-Canadian politician (b. 1907)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Polish-Canadian politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Polish-Canadian politician (b. 1907)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1985", "text": "<PERSON>, American composer, critic, and educator (b. 1896)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer, critic, and educator (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Roger Sessions\"><PERSON></a>, American composer, critic, and educator (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian-American ice hockey player (b. 1902)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shore\"><PERSON></a>, Canadian-American ice hockey player (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American baseball player (b. 1897)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jigger_<PERSON>atz\" title=\"Jigger Statz\"><PERSON><PERSON></a>, American baseball player (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jigger_<PERSON>z\" title=\"Jigger Statz\"><PERSON><PERSON></a>, American baseball player (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji<PERSON>_<PERSON>z"}]}, {"year": "1988", "text": "<PERSON>, American race car driver (b. 1928)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American pianist, composer, and conductor (b. 1898)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American country singer (b. 1964)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chris <PERSON>\"><PERSON></a>, American country singer (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian artist (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian artist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian artist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French physicist and engineer (b. 1903)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player (b. 1956)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eric Show\"><PERSON></a>, American baseball player (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Eric Show\"><PERSON></a>, American baseball player (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (b. 1918)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1998", "text": "<PERSON>, American photographer (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Canadian actor, director, and playwright (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Gratien_G%C3%A9linas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor, director, and playwright (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gratien_G%C3%A9linas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor, director, and playwright (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gratien_G%C3%A9linas"}]}, {"year": "2000", "text": "<PERSON>, American colonel and pilot (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Belarusian poet and author (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian poet and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian poet and author (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Canadian judge and politician, 16th Canadian Minister of Labour (b. 1910)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian judge and politician, 16th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian judge and politician, 16th <a href=\"https://wikipedia.org/wiki/Minister_of_Labour_(Canada)\" title=\"Minister of Labour (Canada)\">Canadian Minister of Labour</a> (b. 1910)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}, {"title": "Minister of Labour (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Labour_(Canada)"}]}, {"year": "2000", "text": "<PERSON>, Puerto Rican pitcher (b. 1948)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican pitcher (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Puerto Rican pitcher (b. 1948)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_Vel%C3%<PERSON><PERSON><PERSON>_(baseball)"}]}, {"year": "2001", "text": "<PERSON>, French race car driver (b. 1943)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American activist (b. 1979)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English captain, polo player, and manager (b. 1931)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(polo)\" title=\"<PERSON> (polo)\"><PERSON></a>, English captain, polo player, and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(polo)\" title=\"<PERSON> (polo)\"><PERSON></a>, English captain, polo player, and manager (b. 1931)", "links": [{"title": "<PERSON> (polo)", "link": "https://wikipedia.org/wiki/<PERSON>_(polo)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech conductor and composer (b. 1910)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD\" title=\"Vilé<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor and composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD\" title=\"Vilé<PERSON>sk<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor and composer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vil%C3%A9m_Tausk%C3%BD"}]}, {"year": "2005", "text": "<PERSON>, American football player (b. 1958)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English architect, designed The London Ark (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/The_London_Ark\" class=\"mw-redirect\" title=\"The London Ark\">The London Ark</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/The_London_Ark\" class=\"mw-redirect\" title=\"The London Ark\">The London Ark</a> (b. 1914)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)"}, {"title": "The London Ark", "link": "https://wikipedia.org/wiki/The_London_Ark"}]}, {"year": "2005", "text": "<PERSON>, American baseball player (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Bangladeshi cricketer (b. 1984)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Bangladeshi cricketer (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Rana\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Bangladeshi cricketer (b. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manjural_Islam_Rana"}]}, {"year": "2008", "text": "<PERSON>, Australian cricketer and soldier (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and soldier (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and soldier (b. 1912)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "2008", "text": "<PERSON>, American actor, director, and producer (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American wrestler and manager (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and manager (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and manager (b. 1942)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>,  Serbian singer, dancer and model (b. 1977)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>j%C4%8Din\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian singer, dancer and model (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Din\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian singer, dancer and model (b. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ksenija_Paj%C4%8Din"}]}, {"year": "2011", "text": "<PERSON>, American religious leader (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American colonel and pilot (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Japanese poet, philosopher, and critic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, philosopher, and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet, philosopher, and critic (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Bangladeshi physicist and cosmologist (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi physicist and cosmologist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi physicist and cosmologist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Argentinian economist and politician, Minister of Economy of Argentina (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Alfredo_Mart%C3%ADnez_de_Hoz\" title=\"<PERSON>\"><PERSON></a>, Argentinian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Economy_of_Argentina\" class=\"mw-redirect\" title=\"Minister of Economy of Argentina\">Minister of Economy of Argentina</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_Mart%C3%ADnez_de_Hoz\" title=\"<PERSON>\"><PERSON></a>, Argentinian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Economy_of_Argentina\" class=\"mw-redirect\" title=\"Minister of Economy of Argentina\">Minister of Economy of Argentina</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%ADnez_de_Hoz"}, {"title": "Minister of Economy of Argentina", "link": "https://wikipedia.org/wiki/Minister_of_Economy_of_Argentina"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Cuban pitcher (b. 1986)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban pitcher (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban pitcher (b. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American-born teacher and author (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ruch<PERSON>_<PERSON>n\" title=\"Ruchoma Shain\"><PERSON><PERSON><PERSON></a>, American-born teacher and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruch<PERSON>_<PERSON>n\" title=\"Ruchoma Shain\"><PERSON><PERSON><PERSON></a>, American-born teacher and author (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruch<PERSON>_<PERSON><PERSON>n"}]}, {"year": "2013", "text": "<PERSON>, Russian-Israeli academic and politician (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli academic and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli academic and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Solodkin"}]}, {"year": "2013", "text": "<PERSON>, English actor (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American race car driver (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American chemist and academic (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Sierra Leonean author, poet, and playwright (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean author, poet, and playwright (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean author, poet, and playwright (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English author and illustrator (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, English author and illustrator (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, English author and illustrator (b. 1949)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "2014", "text": "<PERSON>, Russian economist and politician (b. 1958)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian economist and politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian economist and politician (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and sportscaster (b. 1964)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and sportscaster (b. 1964)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2015", "text": "<PERSON>, American pianist and composer (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist and composer (b. 1922)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "2016", "text": "<PERSON>, Russian-American mathematician and poet (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American mathematician and poet (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer and actor (b. 1944)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Sinatra Jr.\"><PERSON> Jr.</a>, American singer and actor (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Sinatra Jr.\"><PERSON> Jr.</a>, American singer and actor (b. 1944)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2017", "text": "<PERSON>, American neurologist (b. 1925)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Member of the U.S. House of Representatives from New York (b. 1929)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Member of the <a href=\"https://wikipedia.org/wiki/U.S._House_of_Representatives\" class=\"mw-redirect\" title=\"U.S. House of Representatives\">U.S. House of Representatives</a> from New York (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Member of the <a href=\"https://wikipedia.org/wiki/U.S._House_of_Representatives\" class=\"mw-redirect\" title=\"U.S. House of Representatives\">U.S. House of Representatives</a> from New York (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ghter"}, {"title": "U.S. House of Representatives", "link": "https://wikipedia.org/wiki/U.S._House_of_Representatives"}]}, {"year": "2019", "text": "<PERSON>, American surf-rock guitarist, singer, and songwriter (b. 1937)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surf-rock guitarist, singer, and songwriter (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surf-rock guitarist, singer, and songwriter (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}