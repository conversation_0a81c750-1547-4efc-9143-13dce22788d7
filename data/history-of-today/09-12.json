{"date": "September 12", "url": "https://wikipedia.org/wiki/September_12", "data": {"Events": [{"year": "490 BC", "text": "Battle of Marathon: The conventionally accepted date for the Battle of Marathon. The Athenians and their Plataean allies defeat the first Persian invasion force of Greece.", "html": "490 BC - 490 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Marathon\" title=\"Battle of Marathon\">Battle of Marathon</a>: The conventionally accepted date for the Battle of Marathon. The <a href=\"https://wikipedia.org/wiki/Classical_Athens\" title=\"Classical Athens\">Athenians</a> and their <a href=\"https://wikipedia.org/wiki/Plataea\" title=\"Plataea\">Plataean</a> allies defeat the <a href=\"https://wikipedia.org/wiki/First_Persian_invasion_of_Greece\" title=\"First Persian invasion of Greece\">first Persian invasion force of Greece</a>.", "no_year_html": "490 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Marathon\" title=\"Battle of Marathon\">Battle of Marathon</a>: The conventionally accepted date for the Battle of Marathon. The <a href=\"https://wikipedia.org/wiki/Classical_Athens\" title=\"Classical Athens\">Athenians</a> and their <a href=\"https://wikipedia.org/wiki/Plataea\" title=\"Plataea\">Plataean</a> allies defeat the <a href=\"https://wikipedia.org/wiki/First_Persian_invasion_of_Greece\" title=\"First Persian invasion of Greece\">first Persian invasion force of Greece</a>.", "links": [{"title": "Battle of Marathon", "link": "https://wikipedia.org/wiki/Battle_of_Marathon"}, {"title": "Classical Athens", "link": "https://wikipedia.org/wiki/Classical_Athens"}, {"title": "Plataea", "link": "https://wikipedia.org/wiki/Plataea"}, {"title": "First Persian invasion of Greece", "link": "https://wikipedia.org/wiki/First_Persian_invasion_of_Greece"}]}, {"year": "372", "text": "Sixteen Kingdoms: <PERSON><PERSON>, age 10, succeeds his father Emperor <PERSON><PERSON><PERSON> as Emperor <PERSON><PERSON> of the Eastern Jin dynasty.", "html": "372 - <a href=\"https://wikipedia.org/wiki/Sixteen_Kingdoms\" title=\"Sixteen Kingdoms\">Sixteen Kingdoms</a>: <PERSON><PERSON>, age 10, succeeds his father <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(266%E2%80%93420)\" title=\"Jin dynasty (266-420)\">Eastern Jin dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sixteen_Kingdoms\" title=\"Sixteen Kingdoms\">Sixteen Kingdoms</a>: <PERSON><PERSON>, age 10, succeeds his father <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(266%E2%80%93420)\" title=\"Jin dynasty (266-420)\">Eastern Jin dynasty</a>.", "links": [{"title": "Sixteen Kingdoms", "link": "https://wikipedia.org/wiki/Sixteen_Kingdoms"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Jin"}, {"title": "Emperor <PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin"}, {"title": "Jin dynasty (266-420)", "link": "https://wikipedia.org/wiki/Jin_dynasty_(266%E2%80%93420)"}]}, {"year": "1213", "text": "Albigensian Crusade: <PERSON>, 5th Earl of Leicester, defeats <PERSON> of Aragon at the Battle of Muret.", "html": "1213 - <a href=\"https://wikipedia.org/wiki/Albigensian_Crusade\" title=\"Albigensian Crusade\">Albigensian Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Leicester\" title=\"<PERSON>, 5th Earl of Leicester\"><PERSON>, 5th Earl of Leicester</a>, defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> of Aragon</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Muret\" title=\"Battle of Muret\">Battle of Muret</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albigensian_Crusade\" title=\"Albigensian Crusade\">Albigensian Crusade</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_<PERSON>_Leicester\" title=\"<PERSON>, 5th Earl of Leicester\"><PERSON>, 5th Earl of Leicester</a>, defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Muret\" title=\"Battle of Muret\">Battle of Muret</a>.", "links": [{"title": "Albigensian Crusade", "link": "https://wikipedia.org/wiki/Albigensian_Crusade"}, {"title": "<PERSON>, 5th Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Leicester"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Battle of Muret", "link": "https://wikipedia.org/wiki/Battle_of_Muret"}]}, {"year": "1229", "text": "Battle of Portopí: The Aragonese army under the command of <PERSON> of Aragon disembarks at Santa Ponça, Majorca, with the purpose of conquering the island.", "html": "1229 - <a href=\"https://wikipedia.org/wiki/Battle_of_Portop%C3%AD\" title=\"Battle of Portopí\">Battle of Portopí</a>: The Aragonese army under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> disembarks at <a href=\"https://wikipedia.org/wiki/Santa_Pon%C3%A7a\" class=\"mw-redirect\" title=\"Santa Ponça\">Santa Ponça</a>, <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a>, with the purpose of conquering the island.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Portop%C3%AD\" title=\"Battle of Portopí\">Battle of Portopí</a>: The Aragonese army under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> disembarks at <a href=\"https://wikipedia.org/wiki/Santa_Pon%C3%A7a\" class=\"mw-redirect\" title=\"Santa Ponça\">Santa Ponça</a>, <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a>, with the purpose of conquering the island.", "links": [{"title": "Battle of Portopí", "link": "https://wikipedia.org/wiki/Battle_of_Portop%C3%AD"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Santa Ponça", "link": "https://wikipedia.org/wiki/Santa_Pon%C3%A7a"}, {"title": "Majorca", "link": "https://wikipedia.org/wiki/Majorca"}]}, {"year": "1297", "text": "The Treaty of Alcañices, mediated by the pope, between the king <PERSON> of Portugal and king <PERSON> of Castile defines the border between the two countries and establishes an alliance of friendship.", "html": "1297 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Alca%C3%B1ices\" title=\"Treaty of Alcañices\">Treaty of Alcañices</a>, mediated by the pope, between the king <a href=\"https://wikipedia.org/wiki/Denis_of_Portugal\" title=\"Denis of Portugal\"><PERSON> of Portugal</a> and king <a href=\"https://wikipedia.org/wiki/Ferdinand_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> of Castile</a> defines the border between the two countries and establishes an alliance of friendship.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Alca%C3%B1ices\" title=\"Treaty of Alcañices\">Treaty of Alcañices</a>, mediated by the pope, between the king <a href=\"https://wikipedia.org/wiki/Denis_<PERSON>_Portugal\" title=\"Denis of Portugal\"><PERSON> of Portugal</a> and king <a href=\"https://wikipedia.org/wiki/Ferdinand_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a> defines the border between the two countries and establishes an alliance of friendship.", "links": [{"title": "Treaty of Alcañices", "link": "https://wikipedia.org/wiki/Treaty_of_Alca%C3%B1ices"}, {"title": "Denis of Portugal", "link": "https://wikipedia.org/wiki/Denis_of_Portugal"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_Castile"}]}, {"year": "1309", "text": "The First siege of Gibraltar takes place in the context of the Spanish Reconquista pitting the forces of the Kingdom of Castile against the Emirate of Granada resulting in a Castilian victory.", "html": "1309 - The <a href=\"https://wikipedia.org/wiki/First_siege_of_Gibraltar\" title=\"First siege of Gibraltar\">First siege of Gibraltar</a> takes place in the context of the Spanish <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a> pitting the forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> against the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> resulting in a Castilian victory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_siege_of_Gibraltar\" title=\"First siege of Gibraltar\">First siege of Gibraltar</a> takes place in the context of the Spanish <a href=\"https://wikipedia.org/wiki/Reconquista\" title=\"Reconquista\">Reconquista</a> pitting the forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Kingdom of Castile</a> against the <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Emirate of Granada</a> resulting in a Castilian victory.", "links": [{"title": "First siege of Gibraltar", "link": "https://wikipedia.org/wiki/First_siege_of_Gibraltar"}, {"title": "Reconquista", "link": "https://wikipedia.org/wiki/Reconquista"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}]}, {"year": "1609", "text": "<PERSON> begins his exploration of the Hudson River while aboard the Halve Maen.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> begins his exploration of the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a> while aboard the <i><a href=\"https://wikipedia.org/wiki/Halve_Maen\" title=\"Hal<PERSON> Maen\"><PERSON><PERSON></a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Henry Hudson\"><PERSON></a> begins his exploration of the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a> while aboard the <i><a href=\"https://wikipedia.org/wiki/Halve_Maen\" title=\"Hal<PERSON> Maen\"><PERSON><PERSON></a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hudson River", "link": "https://wikipedia.org/wiki/Hudson_River"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hal<PERSON>_Maen"}]}, {"year": "1634", "text": "A gunpowder factory explodes in Valletta, Malta, killing 22 people and damaging several buildings.", "html": "1634 - A gunpowder factory <a href=\"https://wikipedia.org/wiki/1634_Valletta_explosion\" title=\"1634 Valletta explosion\">explodes</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, Malta, killing 22 people and damaging several buildings.", "no_year_html": "A gunpowder factory <a href=\"https://wikipedia.org/wiki/1634_Valletta_explosion\" title=\"1634 Valletta explosion\">explodes</a> in <a href=\"https://wikipedia.org/wiki/Valletta\" title=\"Valletta\">Valletta</a>, Malta, killing 22 people and damaging several buildings.", "links": [{"title": "1634 Valletta explosion", "link": "https://wikipedia.org/wiki/1634_Valletta_explosion"}, {"title": "Valletta", "link": "https://wikipedia.org/wiki/Valletta"}]}, {"year": "1683", "text": "Austro-Ottoman War: Battle of Vienna: Several European armies join forces to defeat the Ottoman Empire.", "html": "1683 - <a href=\"https://wikipedia.org/wiki/Great_Turkish_War\" title=\"Great Turkish War\">Austro-Ottoman War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna\" title=\"Battle of Vienna\">Battle of Vienna</a>: Several European armies join forces to defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Turkish_War\" title=\"Great Turkish War\">Austro-Ottoman War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna\" title=\"Battle of Vienna\">Battle of Vienna</a>: Several European armies join forces to defeat the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>.", "links": [{"title": "Great Turkish War", "link": "https://wikipedia.org/wiki/Great_Turkish_War"}, {"title": "Battle of Vienna", "link": "https://wikipedia.org/wiki/Battle_of_Vienna"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1814", "text": "Battle of North Point: an American detachment halts the British land advance to Baltimore in the War of 1812.", "html": "1814 - <a href=\"https://wikipedia.org/wiki/Battle_of_North_Point\" title=\"Battle of North Point\">Battle of North Point</a>: an American detachment halts the British land advance to <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> in the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_North_Point\" title=\"Battle of North Point\">Battle of North Point</a>: an American detachment halts the British land advance to <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a> in the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>.", "links": [{"title": "Battle of North Point", "link": "https://wikipedia.org/wiki/Battle_of_North_Point"}, {"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}, {"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}]}, {"year": "1847", "text": "Mexican-American War: the Battle of Chapultepec begins.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: the <a href=\"https://wikipedia.org/wiki/Battle_of_Chapultepec\" title=\"Battle of Chapultepec\">Battle of Chapultepec</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: the <a href=\"https://wikipedia.org/wiki/Battle_of_Chapultepec\" title=\"Battle of Chapultepec\">Battle of Chapultepec</a> begins.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Battle of Chapultepec", "link": "https://wikipedia.org/wiki/Battle_of_Chapultepec"}]}, {"year": "1848", "text": "A new constitution marks the establishment of Switzerland as a federal state.", "html": "1848 - A new constitution marks the establishment of <a href=\"https://wikipedia.org/wiki/Switzerland_as_a_federal_state\" title=\"Switzerland as a federal state\">Switzerland as a federal state</a>.", "no_year_html": "A new constitution marks the establishment of <a href=\"https://wikipedia.org/wiki/Switzerland_as_a_federal_state\" title=\"Switzerland as a federal state\">Switzerland as a federal state</a>.", "links": [{"title": "Switzerland as a federal state", "link": "https://wikipedia.org/wiki/Switzerland_as_a_federal_state"}]}, {"year": "1857", "text": "The SS Central America sinks about 160 miles east of Cape Hatteras, North Carolina, drowning a total of 426 passengers and crew, including Captain <PERSON>. The ship was carrying 13-15 tons of gold from the California gold rush.", "html": "1857 - The <a href=\"https://wikipedia.org/wiki/SS_Central_America\" title=\"SS Central America\">SS <i>Central America</i></a> sinks about 160 miles east of <a href=\"https://wikipedia.org/wiki/Cape_Hatteras\" title=\"Cape Hatteras\">Cape Hatteras</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, drowning a total of 426 passengers and crew, including Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The ship was carrying 13-15 tons of gold from the <a href=\"https://wikipedia.org/wiki/California_gold_rush\" title=\"California gold rush\">California gold rush</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SS_Central_America\" title=\"SS Central America\">SS <i>Central America</i></a> sinks about 160 miles east of <a href=\"https://wikipedia.org/wiki/Cape_Hatteras\" title=\"Cape Hatteras\">Cape Hatteras</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a>, drowning a total of 426 passengers and crew, including Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. The ship was carrying 13-15 tons of gold from the <a href=\"https://wikipedia.org/wiki/California_gold_rush\" title=\"California gold rush\">California gold rush</a>.", "links": [{"title": "SS Central America", "link": "https://wikipedia.org/wiki/SS_Central_America"}, {"title": "Cape Hatteras", "link": "https://wikipedia.org/wiki/Cape_Hatteras"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "California gold rush", "link": "https://wikipedia.org/wiki/California_gold_rush"}]}, {"year": "1885", "text": "<PERSON>rbroath 36-0 <PERSON>, a world record scoreline in professional Association football.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Arbroath_36%E2%80%930_Bon_Accord\" class=\"mw-redirect\" title=\"Arbroath 36-0 Bon Accord\">Arbroath 36-0 <PERSON></a>, a world record scoreline in professional Association football.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arbroath_36%E2%80%930_Bon_Accord\" class=\"mw-redirect\" title=\"Arbroath 36-0 Bon Accord\">Arbroath 36-0 <PERSON>rd</a>, a world record scoreline in professional Association football.", "links": [{"title": "Arbroath 36-0 <PERSON>", "link": "https://wikipedia.org/wiki/Arbroath_36%E2%80%930_Bon_Accord"}]}, {"year": "1890", "text": "Salisbury, Rhodesia, is founded.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Harare\" title=\"Harare\">Salisbury</a>, <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harare\" title=\"Harare\">Salisbury</a>, <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a>, is founded.", "links": [{"title": "Harare", "link": "https://wikipedia.org/wiki/Harare"}, {"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}]}, {"year": "1897", "text": "Tirah campaign: In the Battle of Saragarhi, ten thousand Pashtun tribesmen suffer several hundred casualties while attacking 21 Sikh soldiers in British service.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Tirah_campaign\" title=\"Tirah campaign\">Tirah campaign</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Saragarhi\" title=\"Battle of Saragarhi\">Battle of Saragarhi</a>, ten thousand Pashtun tribesmen suffer several hundred casualties while attacking 21 Sikh soldiers in British service.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tirah_campaign\" title=\"Tirah campaign\">Tirah campaign</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Saragarhi\" title=\"Battle of Saragarhi\">Battle of Saragarhi</a>, ten thousand Pashtun tribesmen suffer several hundred casualties while attacking 21 Sikh soldiers in British service.", "links": [{"title": "Tirah campaign", "link": "https://wikipedia.org/wiki/Tirah_campaign"}, {"title": "Battle of Saragarhi", "link": "https://wikipedia.org/wiki/Battle_of_Saragarhi"}]}, {"year": "1906", "text": "The Newport Transporter Bridge is opened in Newport, South Wales by <PERSON>.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/Newport_Transporter_Bridge\" title=\"Newport Transporter Bridge\">Newport Transporter Bridge</a> is opened in <a href=\"https://wikipedia.org/wiki/Newport,_Wales\" title=\"Newport, Wales\">Newport, South Wales</a> by <a href=\"https://wikipedia.org/wiki/Viscount_Tredegar\" class=\"mw-redirect\" title=\"Viscount Tredegar\">Viscount <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Newport_Transporter_Bridge\" title=\"Newport Transporter Bridge\">Newport Transporter Bridge</a> is opened in <a href=\"https://wikipedia.org/wiki/Newport,_Wales\" title=\"Newport, Wales\">Newport, South Wales</a> by <a href=\"https://wikipedia.org/wiki/Viscount_Tredegar\" class=\"mw-redirect\" title=\"Viscount Tredegar\">Viscount <PERSON></a>.", "links": [{"title": "Newport Transporter Bridge", "link": "https://wikipedia.org/wiki/Newport_Transporter_Bridge"}, {"title": "Newport, Wales", "link": "https://wikipedia.org/wiki/Newport,_Wales"}, {"title": "Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1910", "text": "Premiere performance of <PERSON>'s Symphony No. 8 in Munich (with a chorus of 852 singers and an orchestra of 171 players. <PERSON><PERSON>'s rehearsal assistant conductor was <PERSON>).", "html": "1910 - Premiere performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._8_(<PERSON><PERSON>)\" title=\"Symphony No. 8 (<PERSON><PERSON>)\">Symphony No. 8</a> in Munich (with a chorus of 852 singers and an orchestra of 171 players. <PERSON><PERSON>'s rehearsal assistant <a href=\"https://wikipedia.org/wiki/Conductor_(music)\" class=\"mw-redirect\" title=\"Conductor (music)\">conductor</a> was <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "no_year_html": "Premiere performance of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Symphony_No._8_(<PERSON><PERSON>)\" title=\"Symphony No. 8 (<PERSON><PERSON>)\">Symphony No. 8</a> in Munich (with a chorus of 852 singers and an orchestra of 171 players. <PERSON><PERSON>'s rehearsal assistant <a href=\"https://wikipedia.org/wiki/Conductor_(music)\" class=\"mw-redirect\" title=\"Conductor (music)\">conductor</a> was <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Symphony No. 8 (<PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._8_(<PERSON><PERSON>)"}, {"title": "Conductor (music)", "link": "https://wikipedia.org/wiki/Conductor_(music)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "French soldiers rescue over 4,000 Armenian genocide survivors stranded on Musa Dagh.", "html": "1915 - French soldiers rescue over 4,000 <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a> survivors stranded on <a href=\"https://wikipedia.org/wiki/Musa_Dagh\" title=\"Musa Dagh\"><PERSON></a>.", "no_year_html": "French soldiers rescue over 4,000 <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a> survivors stranded on <a href=\"https://wikipedia.org/wiki/Musa_Dagh\" title=\"Musa Dagh\"><PERSON> Dagh</a>.", "links": [{"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Musa_<PERSON>gh"}]}, {"year": "1923", "text": "Southern Rhodesia, today called Zimbabwe, is annexed by the United Kingdom.", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Southern_Rhodesia\" title=\"Southern Rhodesia\">Southern Rhodesia</a>, today called <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, is annexed by the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Southern_Rhodesia\" title=\"Southern Rhodesia\">Southern Rhodesia</a>, today called <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, is annexed by the United Kingdom.", "links": [{"title": "Southern Rhodesia", "link": "https://wikipedia.org/wiki/Southern_Rhodesia"}, {"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}]}, {"year": "1933", "text": "<PERSON><PERSON>, waiting for a red light on Southampton Row in Bloomsbury, conceives the idea of the nuclear chain reaction.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, waiting for a red light on <a href=\"https://wikipedia.org/wiki/Southampton_Row\" class=\"mw-redirect\" title=\"Southampton Row\">Southampton Row</a> in <a href=\"https://wikipedia.org/wiki/Bloomsbury\" title=\"Bloomsbury\">Bloomsbury</a>, conceives the idea of the <a href=\"https://wikipedia.org/wiki/Nuclear_chain_reaction\" title=\"Nuclear chain reaction\">nuclear chain reaction</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, waiting for a red light on <a href=\"https://wikipedia.org/wiki/Southampton_Row\" class=\"mw-redirect\" title=\"Southampton Row\">Southampton Row</a> in <a href=\"https://wikipedia.org/wiki/Bloomsbury\" title=\"Bloomsbury\">Bloomsbury</a>, conceives the idea of the <a href=\"https://wikipedia.org/wiki/Nuclear_chain_reaction\" title=\"Nuclear chain reaction\">nuclear chain reaction</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3_Szil%C3%A1rd"}, {"title": "Southampton Row", "link": "https://wikipedia.org/wiki/Southampton_Row"}, {"title": "Bloomsbury", "link": "https://wikipedia.org/wiki/Bloomsbury"}, {"title": "Nuclear chain reaction", "link": "https://wikipedia.org/wiki/Nuclear_chain_reaction"}]}, {"year": "1938", "text": "<PERSON> demands autonomy and self-determination for the Germans of the Sudetenland region of Czechoslovakia.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> demands autonomy and self-determination for the <a href=\"https://wikipedia.org/wiki/Sudeten_Germans\" title=\"Sudeten Germans\">Germans</a> of the <a href=\"https://wikipedia.org/wiki/Sudetenland\" title=\"Sudetenland\">Sudetenland</a> region of <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">Czechoslovakia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> demands autonomy and self-determination for the <a href=\"https://wikipedia.org/wiki/Sudeten_Germans\" title=\"Sudeten Germans\">Germans</a> of the <a href=\"https://wikipedia.org/wiki/Sudetenland\" title=\"Sudetenland\">Sudetenland</a> region of <a href=\"https://wikipedia.org/wiki/First_Czechoslovak_Republic\" title=\"First Czechoslovak Republic\">Czechoslovakia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sudeten Germans", "link": "https://wikipedia.org/wiki/Sudeten_Germans"}, {"title": "Sudetenland", "link": "https://wikipedia.org/wiki/Sudetenland"}, {"title": "First Czechoslovak Republic", "link": "https://wikipedia.org/wiki/First_Czechoslovak_Republic"}]}, {"year": "1940", "text": "Cave paintings are discovered in Lascaux, France.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Cave_painting\" title=\"Cave painting\">Cave paintings</a> are discovered in <a href=\"https://wikipedia.org/wiki/Lascaux\" title=\"Lascaux\">Lascaux</a>, France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cave_painting\" title=\"Cave painting\">Cave paintings</a> are discovered in <a href=\"https://wikipedia.org/wiki/Las<PERSON>ux\" title=\"Lascaux\">Lascaux</a>, France.", "links": [{"title": "Cave painting", "link": "https://wikipedia.org/wiki/Cave_painting"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1940", "text": "The Hercules Powder plant disaster in the United States kills 51 people and injures over 200.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Hercules_Powder_plant_disaster\" title=\"Hercules Powder plant disaster\">Hercules Powder plant disaster</a> in the United States kills 51 people and injures over 200.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hercules_Powder_plant_disaster\" title=\"Hercules Powder plant disaster\">Hercules Powder plant disaster</a> in the United States kills 51 people and injures over 200.", "links": [{"title": "Hercules Powder plant disaster", "link": "https://wikipedia.org/wiki/Hercules_Powder_plant_disaster"}]}, {"year": "1942", "text": "World War II: RMS Laconia, carrying civilians, Allied soldiers and Italian POWs is torpedoed off the coast of West Africa and sinks with a heavy loss of life.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/RMS_Laconia_(1921)\" title=\"RMS Laconia (1921)\">RMS <i>Laconia</i></a>, carrying civilians, Allied soldiers and Italian <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a> is <a href=\"https://wikipedia.org/wiki/Laconia_incident\" title=\"Laconia incident\">torpedoed</a> off the coast of <a href=\"https://wikipedia.org/wiki/West_Africa\" title=\"West Africa\">West Africa</a> and sinks with a heavy loss of life.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/RMS_Laconia_(1921)\" title=\"RMS Laconia (1921)\">RMS <i>Laconia</i></a>, carrying civilians, Allied soldiers and Italian <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">POWs</a> is <a href=\"https://wikipedia.org/wiki/Laconia_incident\" title=\"Laconia incident\">torpedoed</a> off the coast of <a href=\"https://wikipedia.org/wiki/West_Africa\" title=\"West Africa\">West Africa</a> and sinks with a heavy loss of life.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "RMS Laconia (1921)", "link": "https://wikipedia.org/wiki/RMS_Laconia_(1921)"}, {"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}, {"title": "Laconia incident", "link": "https://wikipedia.org/wiki/Laconia_incident"}, {"title": "West Africa", "link": "https://wikipedia.org/wiki/West_Africa"}]}, {"year": "1942", "text": "World War II: First day of the Battle of Edson's Ridge during the Guadalcanal Campaign. U.S. Marines protecting Henderson Field are attacked by Imperial Japanese Army troops.", "html": "1942 - World War II: First day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge\" title=\"Battle of Edson's Ridge\">Battle of Edson's Ridge</a> during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_campaign\" title=\"Guadalcanal campaign\">Guadalcanal Campaign</a>. <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> protecting <a href=\"https://wikipedia.org/wiki/Henderson_Field_(Guadalcanal)\" title=\"Henderson Field (Guadalcanal)\">Henderson Field</a> are attacked by <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> troops.", "no_year_html": "World War II: First day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge\" title=\"Battle of Edson's Ridge\">Battle of Edson's Ridge</a> during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_campaign\" title=\"Guadalcanal campaign\">Guadalcanal Campaign</a>. <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> protecting <a href=\"https://wikipedia.org/wiki/Henderson_Field_(Guadalcanal)\" title=\"Henderson Field (Guadalcanal)\">Henderson Field</a> are attacked by <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> troops.", "links": [{"title": "Battle of Edson's Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge"}, {"title": "Guadalcanal campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_campaign"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Henderson Field (Guadalcanal)", "link": "https://wikipedia.org/wiki/Henderson_Field_(Guadalcanal)"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}]}, {"year": "1943", "text": "World War II: <PERSON> is rescued from house arrest by German commando forces led by <PERSON>.", "html": "1943 - World War II: <PERSON> is <a href=\"https://wikipedia.org/wiki/Gran_Sasso_raid\" title=\"Gran Sasso raid\">rescued from house arrest</a> by German commando forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "World War II: <PERSON> is <a href=\"https://wikipedia.org/wiki/Gran_Sasso_raid\" title=\"Gran Sasso raid\">rescued from house arrest</a> by German commando forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Gran Sasso raid", "link": "https://wikipedia.org/wiki/Gran_<PERSON>_raid"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>zeny"}]}, {"year": "1944", "text": "World War II: The liberation of Yugoslavia from Axis occupation continues. Bajina Bašta in western Serbia is among the liberated cities.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/World_War_II_in_Yugoslavia\" title=\"World War II in Yugoslavia\">liberation of Yugoslavia from Axis occupation</a> continues. <a href=\"https://wikipedia.org/wiki/Bajina_Ba%C5%A1ta\" title=\"Bajina Bašta\">Bajina Bašta</a> in western Serbia is among the liberated cities.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/World_War_II_in_Yugoslavia\" title=\"World War II in Yugoslavia\">liberation of Yugoslavia from Axis occupation</a> continues. <a href=\"https://wikipedia.org/wiki/Bajina_Ba%C5%A1ta\" title=\"Bajina Bašta\">Bajina Bašta</a> in western Serbia is among the liberated cities.", "links": [{"title": "World War II in Yugoslavia", "link": "https://wikipedia.org/wiki/World_War_II_in_Yugoslavia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bajina_Ba%C5%A1ta"}]}, {"year": "1953", "text": "U.S. Senator and future President <PERSON> marries <PERSON> at St. Mary's Church in Newport, Rhode Island.", "html": "1953 - U.S. Senator and future President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/St._Mary%27s_Church_(Newport,_Rhode_Island)\" title=\"St. Mary's Church (Newport, Rhode Island)\">St. Mary's Church</a> in <a href=\"https://wikipedia.org/wiki/Newport,_Rhode_Island\" title=\"Newport, Rhode Island\">Newport, Rhode Island</a>.", "no_year_html": "U.S. Senator and future President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/St._Mary%27s_Church_(Newport,_Rhode_Island)\" title=\"St. Mary's Church (Newport, Rhode Island)\">St. Mary's Church</a> in <a href=\"https://wikipedia.org/wiki/Newport,_Rhode_Island\" title=\"Newport, Rhode Island\">Newport, Rhode Island</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "St. Mary's Church (Newport, Rhode Island)", "link": "https://wikipedia.org/wiki/St._Mary%27s_Church_(Newport,_Rhode_Island)"}, {"title": "Newport, Rhode Island", "link": "https://wikipedia.org/wiki/Newport,_Rhode_Island"}]}, {"year": "1958", "text": "<PERSON> demonstrates the first working integrated circuit while working at Texas Instruments.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the first working <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a> while working at <a href=\"https://wikipedia.org/wiki/Texas_Instruments\" title=\"Texas Instruments\">Texas Instruments</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> demonstrates the first working <a href=\"https://wikipedia.org/wiki/Integrated_circuit\" title=\"Integrated circuit\">integrated circuit</a> while working at <a href=\"https://wikipedia.org/wiki/Texas_Instruments\" title=\"Texas Instruments\">Texas Instruments</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Integrated circuit", "link": "https://wikipedia.org/wiki/Integrated_circuit"}, {"title": "Texas Instruments", "link": "https://wikipedia.org/wiki/Texas_Instruments"}]}, {"year": "1959", "text": "The Soviet Union launches a large rocket, Lunik II, at the Moon.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches a large rocket, <a href=\"https://wikipedia.org/wiki/Lunik_II\" class=\"mw-redirect\" title=\"Lunik II\">Lunik II</a>, at the Moon.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches a large rocket, <a href=\"https://wikipedia.org/wiki/Lunik_II\" class=\"mw-redirect\" title=\"Lunik II\">Lunik II</a>, at the Moon.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Lunik II", "link": "https://wikipedia.org/wiki/Lu<PERSON>_II"}]}, {"year": "1959", "text": "Bonanza, the first regularly scheduled TV program presented in color, is launched in the United States.", "html": "1959 - <i><a href=\"https://wikipedia.org/wiki/Bonanza\" title=\"Bonanza\">Bonanza</a></i>, the first regularly scheduled TV program presented in <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color</a>, is launched in the United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Bonanza\" title=\"Bonanza\">Bonanza</a></i>, the first regularly scheduled TV program presented in <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color</a>, is launched in the United States.", "links": [{"title": "Bonanza", "link": "https://wikipedia.org/wiki/Bonanza"}, {"title": "Color television", "link": "https://wikipedia.org/wiki/Color_television"}]}, {"year": "1961", "text": "The African and Malagasy Union is founded.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/African_and_Malagasy_Union\" title=\"African and Malagasy Union\">African and Malagasy Union</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/African_and_Malagasy_Union\" title=\"African and Malagasy Union\">African and Malagasy Union</a> is founded.", "links": [{"title": "African and Malagasy Union", "link": "https://wikipedia.org/wiki/African_and_Malagasy_Union"}]}, {"year": "1961", "text": "Air France Flight 2005 crashes near Rabat-Salé Airport, in Rabat, Morocco, killing 77 people.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_2005\" title=\"Air France Flight 2005\">Air France Flight 2005</a> crashes near <a href=\"https://wikipedia.org/wiki/Rabat%E2%80%93Sal%C3%A9_Airport\" title=\"Rabat-Salé Airport\">Rabat-Salé Airport</a>, in <a href=\"https://wikipedia.org/wiki/Rabat\" title=\"Rabat\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, killing 77 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_2005\" title=\"Air France Flight 2005\">Air France Flight 2005</a> crashes near <a href=\"https://wikipedia.org/wiki/Rabat%E2%80%93Sal%C3%A9_Airport\" title=\"Rabat-Salé Airport\">Rabat-Salé Airport</a>, in <a href=\"https://wikipedia.org/wiki/Rabat\" title=\"Rabat\">Ra<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>, killing 77 people.", "links": [{"title": "Air France Flight 2005", "link": "https://wikipedia.org/wiki/Air_France_Flight_2005"}, {"title": "Rabat-Salé Airport", "link": "https://wikipedia.org/wiki/Rabat%E2%80%93Sal%C3%A9_Airport"}, {"title": "Rabat", "link": "https://wikipedia.org/wiki/Rabat"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "1962", "text": "US President <PERSON> delivers his \"We choose to go to the Moon\" speech at Rice University.", "html": "1962 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/We_choose_to_go_to_the_Moon\" title=\"We choose to go to the Moon\">We choose to go to the Moon</a>\" speech at Rice University.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/We_choose_to_go_to_the_Moon\" title=\"We choose to go to the Moon\">We choose to go to the Moon</a>\" speech at Rice University.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "We choose to go to the Moon", "link": "https://wikipedia.org/wiki/We_choose_to_go_to_the_Moon"}]}, {"year": "1966", "text": "Gemini 11, the penultimate mission of NASA's Gemini program, and the current human altitude record holder (except for the Apollo lunar missions).", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Gemini_11\" title=\"Gemini 11\">Gemini 11</a>, the penultimate mission of NASA's <a href=\"https://wikipedia.org/wiki/Gemini_program\" class=\"mw-redirect\" title=\"Gemini program\">Gemini program</a>, and the current human altitude record holder (except for the <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo lunar missions</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gemini_11\" title=\"Gemini 11\">Gemini 11</a>, the penultimate mission of NASA's <a href=\"https://wikipedia.org/wiki/Gemini_program\" class=\"mw-redirect\" title=\"Gemini program\">Gemini program</a>, and the current human altitude record holder (except for the <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo lunar missions</a>).", "links": [{"title": "Gemini 11", "link": "https://wikipedia.org/wiki/Gemini_11"}, {"title": "Gemini program", "link": "https://wikipedia.org/wiki/Gemini_program"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}]}, {"year": "1969", "text": "Philippine Air Lines Flight 158 crashes in Antipolo, near Manila International Airport in the Philippines, killing 45 people.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Philippine_Air_Lines_Flight_158\" title=\"Philippine Air Lines Flight 158\">Philippine Air Lines Flight 158</a> crashes in <a href=\"https://wikipedia.org/wiki/Antipolo\" title=\"Antipolo\">Antipolo</a>, near <a href=\"https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport\" title=\"Ninoy Aquino International Airport\">Manila International Airport</a> in the Philippines, killing 45 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippine_Air_Lines_Flight_158\" title=\"Philippine Air Lines Flight 158\">Philippine Air Lines Flight 158</a> crashes in <a href=\"https://wikipedia.org/wiki/Antipolo\" title=\"Antipolo\">Antipolo</a>, near <a href=\"https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport\" title=\"Ninoy Aquino International Airport\">Manila International Airport</a> in the Philippines, killing 45 people.", "links": [{"title": "Philippine Air Lines Flight 158", "link": "https://wikipedia.org/wiki/Philippine_Air_Lines_Flight_158"}, {"title": "Antipolo", "link": "https://wikipedia.org/wiki/Antipolo"}, {"title": "Ninoy Aquino International Airport", "link": "https://wikipedia.org/wiki/Ninoy_Aquino_International_Airport"}]}, {"year": "1970", "text": "Dawson's Field hijackings: Popular Front for the Liberation of Palestine terrorists blow up three hijacked airliners in Zarqa, Jordan, continuing to hold the passengers hostage in various undisclosed locations in Amman.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field hijackings</a>: <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">Popular Front for the Liberation of Palestine</a> terrorists blow up three hijacked airliners in <a href=\"https://wikipedia.org/wiki/Zarqa\" title=\"Zarqa\">Zarqa</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>, continuing to hold the passengers hostage in various undisclosed locations in <a href=\"https://wikipedia.org/wiki/Amman\" title=\"Amman\">Amman</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Field_hijackings\" title=\"Dawson's Field hijackings\">Dawson's Field hijackings</a>: <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine\" title=\"Popular Front for the Liberation of Palestine\">Popular Front for the Liberation of Palestine</a> terrorists blow up three hijacked airliners in <a href=\"https://wikipedia.org/wiki/Zarqa\" title=\"Zarqa\">Zarqa</a>, <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a>, continuing to hold the passengers hostage in various undisclosed locations in <a href=\"https://wikipedia.org/wiki/Amman\" title=\"Amman\">Amman</a>.", "links": [{"title": "Dawson's Field hijackings", "link": "https://wikipedia.org/wiki/Dawson%27s_Field_hijackings"}, {"title": "Popular Front for the Liberation of Palestine", "link": "https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Palestine"}, {"title": "Zarqa", "link": "https://wikipedia.org/wiki/Zarqa"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Amman", "link": "https://wikipedia.org/wiki/Amman"}]}, {"year": "1974", "text": "Emperor <PERSON><PERSON> of Ethiopia, 'Messiah' of the Rastafari movement, is deposed following a military coup by the Derg, ending a reign of 58 years.", "html": "1974 - Emperor <a href=\"https://wikipedia.org/wiki/Haile_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Selassie</a> of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, '<a href=\"https://wikipedia.org/wiki/Messiah\" title=\"Messiah\">Messiah</a>' of the <a href=\"https://wikipedia.org/wiki/Rastafari\" title=\"Rastafari\">Ra<PERSON><PERSON></a> movement, is deposed following a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">military coup</a> by the <a href=\"https://wikipedia.org/wiki/Derg\" title=\"Derg\">Derg</a>, ending a reign of 58 years.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Selassie\" title=\"Haile Selassie\"><PERSON><PERSON> Se<PERSON>ie</a> of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, '<a href=\"https://wikipedia.org/wiki/Messiah\" title=\"Messiah\">Messiah</a>' of the <a href=\"https://wikipedia.org/wiki/Rastafari\" title=\"Rastafari\">Ra<PERSON><PERSON></a> movement, is deposed following a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">military coup</a> by the <a href=\"https://wikipedia.org/wiki/Derg\" title=\"Derg\">Derg</a>, ending a reign of 58 years.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Messiah", "link": "https://wikipedia.org/wiki/Messiah"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>stafari"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Derg"}]}, {"year": "1977", "text": "South African anti-apartheid activist <PERSON> dies in police custody.", "html": "1977 - South African <a href=\"https://wikipedia.org/wiki/Internal_resistance_to_apartheid\" title=\"Internal resistance to apartheid\">anti-apartheid activist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in police custody.", "no_year_html": "South African <a href=\"https://wikipedia.org/wiki/Internal_resistance_to_apartheid\" title=\"Internal resistance to apartheid\">anti-apartheid activist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dies in police custody.", "links": [{"title": "Internal resistance to apartheid", "link": "https://wikipedia.org/wiki/Internal_resistance_to_apartheid"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "The 43rd government of Turkey is overthrown in a coup d'état led by General <PERSON><PERSON>.", "html": "1980 - The <a href=\"https://wikipedia.org/wiki/43rd_government_of_Turkey\" title=\"43rd government of Turkey\">43rd government of Turkey</a> is overthrown in a <a href=\"https://wikipedia.org/wiki/1980_Turkish_coup_d%27%C3%A9tat\" title=\"1980 Turkish coup d'état\">coup d'état</a> led by General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/43rd_government_of_Turkey\" title=\"43rd government of Turkey\">43rd government of Turkey</a> is overthrown in a <a href=\"https://wikipedia.org/wiki/1980_Turkish_coup_d%27%C3%A9tat\" title=\"1980 Turkish coup d'état\">coup d'état</a> led by General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "43rd government of Turkey", "link": "https://wikipedia.org/wiki/43rd_government_of_Turkey"}, {"title": "1980 Turkish coup d'état", "link": "https://wikipedia.org/wiki/1980_Turkish_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "A Wells Fargo depot in West Hartford, Connecticut, United States, is robbed of approximately US$7 million by Los Macheteros.", "html": "1983 - A <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> depot in <a href=\"https://wikipedia.org/wiki/West_Hartford,_Connecticut\" title=\"West Hartford, Connecticut\">West Hartford, Connecticut</a>, United States, is robbed of approximately US$7 million by <a href=\"https://wikipedia.org/wiki/Los_Macheteros\" class=\"mw-redirect\" title=\"Los Macheteros\">Los Macheteros</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> depot in <a href=\"https://wikipedia.org/wiki/West_Hartford,_Connecticut\" title=\"West Hartford, Connecticut\">West Hartford, Connecticut</a>, United States, is robbed of approximately US$7 million by <a href=\"https://wikipedia.org/wiki/Los_Macheteros\" class=\"mw-redirect\" title=\"Los Macheteros\">Los Macheteros</a>.", "links": [{"title": "Wells Fargo", "link": "https://wikipedia.org/wiki/Wells_Fargo"}, {"title": "West Hartford, Connecticut", "link": "https://wikipedia.org/wiki/West_Hartford,_Connecticut"}, {"title": "Los Macheteros", "link": "https://wikipedia.org/wiki/Los_Macheteros"}]}, {"year": "1983", "text": "The USSR vetoes a United Nations Security Council Resolution deploring the Soviet destruction of Korean Air Lines Flight 007.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a> vetoes a <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> Resolution deploring the Soviet destruction of <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a> vetoes a <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> Resolution deploring the Soviet destruction of <a href=\"https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007\" title=\"Korean Air Lines Flight 007\">Korean Air Lines Flight 007</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "Korean Air Lines Flight 007", "link": "https://wikipedia.org/wiki/Korean_Air_Lines_Flight_007"}]}, {"year": "1984", "text": "<PERSON> sets the baseball record for strikeouts in a season by a rookie with 276, previously set by <PERSON> with 246 in 1954. <PERSON><PERSON>'s 276 strikeouts that season, pitched in 218 innings, set the current record.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> record for <a href=\"https://wikipedia.org/wiki/Strikeout\" title=\"Strikeout\">strikeouts</a> in a season by a rookie with 276, previously set by <a href=\"https://wikipedia.org/wiki/Herb_Score\" title=\"Herb Score\">Herb Score</a> with 246 in 1954. <PERSON><PERSON>'s 276 strikeouts that season, pitched in 218 innings, set the current record.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a> record for <a href=\"https://wikipedia.org/wiki/Strikeout\" title=\"Strikeout\">strikeouts</a> in a season by a rookie with 276, previously set by <a href=\"https://wikipedia.org/wiki/Herb_Score\" title=\"Herb Score\">Herb Score</a> with 246 in 1954. <PERSON><PERSON>'s 276 strikeouts that season, pitched in 218 innings, set the current record.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}, {"title": "Strikeout", "link": "https://wikipedia.org/wiki/Strikeout"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Score"}]}, {"year": "1988", "text": "Hurricane <PERSON> devastates Jamaica; it turns towards Mexico's Yucatán Peninsula two days later, causing an estimated $5 billion in damage.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Hurricane_Gilbert\" title=\"Hurricane Gilbert\">Hurricane <PERSON></a> devastates <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>; it turns towards Mexico's <a href=\"https://wikipedia.org/wiki/Yucat%C3%A1n_Peninsula\" title=\"Yucatán Peninsula\">Yucatán Peninsula</a> two days later, causing an estimated $5 billion in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Gilbert\" title=\"Hurricane Gilbert\">Hurricane <PERSON></a> devastates <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>; it turns towards Mexico's <a href=\"https://wikipedia.org/wiki/Yucat%C3%A1n_Peninsula\" title=\"Yucatán Peninsula\">Yucatán Peninsula</a> two days later, causing an estimated $5 billion in damage.", "links": [{"title": "Hurricane Gilbert", "link": "https://wikipedia.org/wiki/Hurricane_Gilbert"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}, {"title": "Yucatán Peninsula", "link": "https://wikipedia.org/wiki/Yucat%C3%A1n_Peninsula"}]}, {"year": "1990", "text": "The two German states and the Four Powers sign the Treaty on the Final Settlement with Respect to Germany in Moscow, paving the way for German reunification.", "html": "1990 - The two German states and the <a href=\"https://wikipedia.org/wiki/Allied_Control_Council\" title=\"Allied Control Council\">Four Powers</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany\" title=\"Treaty on the Final Settlement with Respect to Germany\">Treaty on the Final Settlement with Respect to Germany</a> in Moscow, paving the way for <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>.", "no_year_html": "The two German states and the <a href=\"https://wikipedia.org/wiki/Allied_Control_Council\" title=\"Allied Control Council\">Four Powers</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany\" title=\"Treaty on the Final Settlement with Respect to Germany\">Treaty on the Final Settlement with Respect to Germany</a> in Moscow, paving the way for <a href=\"https://wikipedia.org/wiki/German_reunification\" title=\"German reunification\">German reunification</a>.", "links": [{"title": "Allied Control Council", "link": "https://wikipedia.org/wiki/Allied_Control_Council"}, {"title": "Treaty on the Final Settlement with Respect to Germany", "link": "https://wikipedia.org/wiki/Treaty_on_the_Final_Settlement_with_Respect_to_Germany"}, {"title": "German reunification", "link": "https://wikipedia.org/wiki/German_reunification"}]}, {"year": "1990", "text": "The Red Cross organizations of mainland China and Taiwan sign Kinmen Agreement on repatriation of illegal immigrants and criminal suspects after two days of talks in Kinmen, Fujian Province in response to the two tragedies in repatriation in the previous two months. It is the first agreement reached by private organizations across the Taiwan Strait.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Red_Cross_Society_of_China\" title=\"Red Cross Society of China\">Red Cross organizations of mainland China</a> and <a href=\"https://wikipedia.org/wiki/Red_Cross_Society_of_the_Republic_of_China\" title=\"Red Cross Society of the Republic of China\">Taiwan</a> sign <a href=\"https://wikipedia.org/wiki/Kinmen_Agreement\" title=\"Kinmen Agreement\">Kinmen Agreement</a> on repatriation of illegal immigrants and criminal suspects after two days of talks in <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>, <a href=\"https://wikipedia.org/wiki/Fujian_Province,_Republic_of_China\" class=\"mw-redirect\" title=\"Fujian Province, Republic of China\">Fujian Province</a> in response to the two tragedies in repatriation in the previous two months. It is the first agreement reached by private organizations across the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Taiwan Strait</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Red_Cross_Society_of_China\" title=\"Red Cross Society of China\">Red Cross organizations of mainland China</a> and <a href=\"https://wikipedia.org/wiki/Red_Cross_Society_of_the_Republic_of_China\" title=\"Red Cross Society of the Republic of China\">Taiwan</a> sign <a href=\"https://wikipedia.org/wiki/Kinmen_Agreement\" title=\"Kinmen Agreement\">Kinmen Agreement</a> on repatriation of illegal immigrants and criminal suspects after two days of talks in <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>, <a href=\"https://wikipedia.org/wiki/Fujian_Province,_Republic_of_China\" class=\"mw-redirect\" title=\"Fujian Province, Republic of China\">Fujian Province</a> in response to the two tragedies in repatriation in the previous two months. It is the first agreement reached by private organizations across the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Taiwan Strait</a>.", "links": [{"title": "Red Cross Society of China", "link": "https://wikipedia.org/wiki/Red_Cross_Society_of_China"}, {"title": "Red Cross Society of the Republic of China", "link": "https://wikipedia.org/wiki/Red_Cross_Society_of_the_Republic_of_China"}, {"title": "Kinmen Agreement", "link": "https://wikipedia.org/wiki/Kinmen_Agreement"}, {"title": "Kinmen", "link": "https://wikipedia.org/wiki/Kinmen"}, {"title": "Fujian Province, Republic of China", "link": "https://wikipedia.org/wiki/Fujian_Province,_Republic_of_China"}, {"title": "Taiwan Strait", "link": "https://wikipedia.org/wiki/Taiwan_Strait"}]}, {"year": "1991", "text": "NASA launches Space Shuttle Discovery on STS-48 to deploy the Upper Atmosphere Research Satellite.", "html": "1991 - NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> on <a href=\"https://wikipedia.org/wiki/STS-48\" title=\"STS-48\">STS-48</a> to deploy the <a href=\"https://wikipedia.org/wiki/Upper_Atmosphere_Research_Satellite\" title=\"Upper Atmosphere Research Satellite\">Upper Atmosphere Research Satellite</a>.", "no_year_html": "NASA launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> on <a href=\"https://wikipedia.org/wiki/STS-48\" title=\"STS-48\">STS-48</a> to deploy the <a href=\"https://wikipedia.org/wiki/Upper_Atmosphere_Research_Satellite\" title=\"Upper Atmosphere Research Satellite\">Upper Atmosphere Research Satellite</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-48", "link": "https://wikipedia.org/wiki/STS-48"}, {"title": "Upper Atmosphere Research Satellite", "link": "https://wikipedia.org/wiki/Upper_Atmosphere_Research_Satellite"}]}, {"year": "1992", "text": "NASA launches Space Shuttle Endeavour on STS-47 which marked the 50th shuttle mission. On board are <PERSON>, the first African-American woman in space, <PERSON><PERSON><PERSON>, the first Japanese citizen to fly in a US spaceship, and <PERSON> and <PERSON>, the first married couple in space.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> on <a href=\"https://wikipedia.org/wiki/STS-47\" title=\"STS-47\">STS-47</a> which marked the 50th shuttle mission. On board are <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> woman in space, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the first Japanese citizen to fly in a US spaceship, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first married couple in space.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> on <a href=\"https://wikipedia.org/wiki/STS-47\" title=\"STS-47\">STS-47</a> which marked the 50th shuttle mission. On board are <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> woman in space, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the first Japanese citizen to fly in a US spaceship, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, the first married couple in space.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-47", "link": "https://wikipedia.org/wiki/STS-47"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "African-American", "link": "https://wikipedia.org/wiki/African-American"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, leader of the Shining Path, is captured by Peruvian special forces; shortly thereafter the rest of Shining Path's leadership fell as well.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>uzm%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Shining_Path\" title=\"Shining Path\">Shining Path</a>, is captured by <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peruvian</a> special forces; shortly thereafter the rest of <a href=\"https://wikipedia.org/wiki/Shining_Path\" title=\"Shining Path\">Shining Path</a>'s leadership fell as well.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Shining_Path\" title=\"Shining Path\">Shining Path</a>, is captured by <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peruvian</a> special forces; shortly thereafter the rest of <a href=\"https://wikipedia.org/wiki/Shining_Path\" title=\"Shining Path\">Shining Path</a>'s leadership fell as well.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abimael_Guzm%C3%A1n"}, {"title": "Shining Path", "link": "https://wikipedia.org/wiki/Shining_Path"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Shining Path", "link": "https://wikipedia.org/wiki/Shining_Path"}]}, {"year": "1993", "text": "NASA launches Space Shuttle Discovery on STS-51.", "html": "1993 - NASA launches Space Shuttle <i>Discovery</i> on <a href=\"https://wikipedia.org/wiki/STS-51\" title=\"STS-51\">STS-51</a>.", "no_year_html": "NASA launches Space Shuttle <i>Discovery</i> on <a href=\"https://wikipedia.org/wiki/STS-51\" title=\"STS-51\">STS-51</a>.", "links": [{"title": "STS-51", "link": "https://wikipedia.org/wiki/STS-51"}]}, {"year": "1994", "text": "<PERSON> fatally crashes a single-engine Cessna 150 into the White House's south lawn, striking the West wing. There were no other casualties.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> fatally crashes a single-engine <a href=\"https://wikipedia.org/wiki/Cessna\" title=\"Cessna\">Cessna</a> 150 into the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>'s south lawn, striking the West wing. There were no other casualties.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> fatally crashes a single-engine <a href=\"https://wikipedia.org/wiki/Cessna\" title=\"Cessna\">Cessna</a> 150 into the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>'s south lawn, striking the West wing. There were no other casualties.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cessna", "link": "https://wikipedia.org/wiki/Cessna"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "2001", "text": "Ansett Australia, Australia's first commercial interstate airline, collapses due to increased strain on the international airline industry, leaving 10,000 people unemployed.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Ansett_Australia\" title=\"Ansett Australia\">Ansett Australia</a>, Australia's first commercial interstate airline, collapses due to increased strain on the international airline industry, leaving 10,000 people unemployed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ansett_Australia\" title=\"Ansett Australia\">Ansett Australia</a>, Australia's first commercial interstate airline, collapses due to increased strain on the international airline industry, leaving 10,000 people unemployed.", "links": [{"title": "Ansett Australia", "link": "https://wikipedia.org/wiki/Ansett_Australia"}]}, {"year": "2003", "text": "The United Nations lifts sanctions against Libya after that country agreed to accept responsibility and recompense the families of victims in the 1988 bombing of Pan Am Flight 103.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> lifts <a href=\"https://wikipedia.org/wiki/International_sanctions\" title=\"International sanctions\">sanctions</a> against <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> after that country agreed to accept responsibility and recompense the families of victims in the <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">1988 bombing of Pan Am Flight 103</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> lifts <a href=\"https://wikipedia.org/wiki/International_sanctions\" title=\"International sanctions\">sanctions</a> against <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a> after that country agreed to accept responsibility and recompense the families of victims in the <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_103\" title=\"Pan Am Flight 103\">1988 bombing of Pan Am Flight 103</a>.", "links": [{"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "International sanctions", "link": "https://wikipedia.org/wiki/International_sanctions"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Pan Am Flight 103", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_103"}]}, {"year": "2003", "text": "Iraq War: In Fallujah, U.S. forces mistakenly shoot and kill eight Iraqi police officers.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: In <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>, U.S. forces mistakenly shoot and kill eight Iraqi police officers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Iraq War</a>: In <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>, U.S. forces mistakenly shoot and kill eight Iraqi police officers.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Fallujah", "link": "https://wikipedia.org/wiki/Fallujah"}]}, {"year": "2003", "text": "Typhoon <PERSON><PERSON>, the strongest recorded typhoon to strike South Korea, made landfall near Busan.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Typhoon_Maemi\" title=\"Typhoon Maemi\">Typhoon <PERSON><PERSON></a>, the strongest recorded typhoon to strike South Korea, made landfall near <a href=\"https://wikipedia.org/wiki/Busan\" title=\"Busan\">Busan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Mae<PERSON>\" title=\"Typhoon Maemi\">Typhoon <PERSON><PERSON></a>, the strongest recorded typhoon to strike South Korea, made landfall near <a href=\"https://wikipedia.org/wiki/Busan\" title=\"Busan\">Busan</a>.", "links": [{"title": "Typhoon Maemi", "link": "https://wikipedia.org/wiki/<PERSON>_Mae<PERSON>"}, {"title": "Busan", "link": "https://wikipedia.org/wiki/Busan"}]}, {"year": "2005", "text": "Israeli-Palestinian conflict: the Israeli disengagement from Gaza is completed, leaving some 2,530 homes demolished.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: the <a href=\"https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza\" class=\"mw-redirect\" title=\"Israeli disengagement from Gaza\">Israeli disengagement from Gaza</a> is completed, leaving some 2,530 homes demolished.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: the <a href=\"https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza\" class=\"mw-redirect\" title=\"Israeli disengagement from Gaza\">Israeli disengagement from Gaza</a> is completed, leaving some 2,530 homes demolished.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "Israeli disengagement from Gaza", "link": "https://wikipedia.org/wiki/Israeli_disengagement_from_Gaza"}]}, {"year": "2007", "text": "Former Philippine President <PERSON> is convicted of plunder.", "html": "2007 - Former Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of <a href=\"https://wikipedia.org/wiki/Plunder\" class=\"mw-redirect\" title=\"Plunder\">plunder</a>.", "no_year_html": "Former Philippine President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of <a href=\"https://wikipedia.org/wiki/Plunder\" class=\"mw-redirect\" title=\"Plunder\">plunder</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Plunder", "link": "https://wikipedia.org/wiki/Plunder"}]}, {"year": "2007", "text": "Two earthquakes measuring 8.4 and 7.9 on the Richter Scale hits the Indonesian island of Sumatra, killing 25 people and injuring 161.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/September_2007_Sumatra_earthquakes\" class=\"mw-redirect\" title=\"September 2007 Sumatra earthquakes\">Two earthquakes measuring 8.4 and 7.9</a> on the <a href=\"https://wikipedia.org/wiki/Richter_Scale\" class=\"mw-redirect\" title=\"Richter Scale\">Richter Scale</a> hits the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, killing 25 people and injuring 161.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/September_2007_Sumatra_earthquakes\" class=\"mw-redirect\" title=\"September 2007 Sumatra earthquakes\">Two earthquakes measuring 8.4 and 7.9</a> on the <a href=\"https://wikipedia.org/wiki/Richter_Scale\" class=\"mw-redirect\" title=\"Richter Scale\">Richter Scale</a> hits the <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesian</a> island of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a>, killing 25 people and injuring 161.", "links": [{"title": "September 2007 Sumatra earthquakes", "link": "https://wikipedia.org/wiki/September_2007_Sumatra_earthquakes"}, {"title": "Richter Scale", "link": "https://wikipedia.org/wiki/Richter_Scale"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Sumatra", "link": "https://wikipedia.org/wiki/Sumatra"}]}, {"year": "2008", "text": "The 2008 Chatsworth train collision in Los Angeles between a Metrolink commuter train and a Union Pacific freight train kills 25 people.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/2008_Chatsworth_train_collision\" title=\"2008 Chatsworth train collision\">2008 Chatsworth train collision</a> in Los Angeles between a <a href=\"https://wikipedia.org/wiki/Metrolink_(California)\" title=\"Metrolink (California)\">Metrolink</a> commuter train and a <a href=\"https://wikipedia.org/wiki/Union_Pacific\" class=\"mw-redirect\" title=\"Union Pacific\">Union Pacific</a> freight train kills 25 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2008_Chatsworth_train_collision\" title=\"2008 Chatsworth train collision\">2008 Chatsworth train collision</a> in Los Angeles between a <a href=\"https://wikipedia.org/wiki/Metrolink_(California)\" title=\"Metrolink (California)\">Metrolink</a> commuter train and a <a href=\"https://wikipedia.org/wiki/Union_Pacific\" class=\"mw-redirect\" title=\"Union Pacific\">Union Pacific</a> freight train kills 25 people.", "links": [{"title": "2008 Chatsworth train collision", "link": "https://wikipedia.org/wiki/2008_Chatsworth_train_collision"}, {"title": "Metrolink (California)", "link": "https://wikipedia.org/wiki/Metrolink_(California)"}, {"title": "Union Pacific", "link": "https://wikipedia.org/wiki/Union_Pacific"}]}, {"year": "2011", "text": "The National September 11 Memorial & Museum in New York City opens to the public.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum\" title=\"National September 11 Memorial &amp; Museum\">National September 11 Memorial &amp; Museum</a> in New York City opens to the public.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum\" title=\"National September 11 Memorial &amp; Museum\">National September 11 Memorial &amp; Museum</a> in New York City opens to the public.", "links": [{"title": "National September 11 Memorial & Museum", "link": "https://wikipedia.org/wiki/National_September_11_Memorial_%26_Museum"}]}, {"year": "2012", "text": "Petropavlovsk-Kamchatsky Air Flight 251 crashes on approach to Palana Airport, killing 10 and injuring four.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2012)\" title=\"Petropavlovsk-Kamchatsky Air Flight 251 (2012)\">Petropavlovsk-Kamchatsky Air Flight 251</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Palana_Airport\" title=\"Palana Airport\">Palana Airport</a>, killing 10 and injuring four.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2012)\" title=\"Petropavlovsk-Kamchatsky Air Flight 251 (2012)\">Petropavlovsk-Kamchatsky Air Flight 251</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Palana_Airport\" title=\"Palana Airport\">Palana Airport</a>, killing 10 and injuring four.", "links": [{"title": "Petropavlovsk-Kamchatsky Air Flight 251 (2012)", "link": "https://wikipedia.org/wiki/Petropavlovsk-Kamchatsky_Air_Flight_251_(2012)"}, {"title": "Palana Airport", "link": "https://wikipedia.org/wiki/Palana_Airport"}]}, {"year": "2013", "text": "NASA confirms that its Voyager 1 probe has become the first manmade object to enter interstellar space.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> confirms that its <a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a> probe has become the first <a href=\"https://wikipedia.org/wiki/Interstellar_probe\" title=\"Interstellar probe\">manmade object to enter interstellar space</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> confirms that its <a href=\"https://wikipedia.org/wiki/Voyager_1\" title=\"Voyager 1\">Voyager 1</a> probe has become the first <a href=\"https://wikipedia.org/wiki/Interstellar_probe\" title=\"Interstellar probe\">manmade object to enter interstellar space</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Voyager 1", "link": "https://wikipedia.org/wiki/Voyager_1"}, {"title": "Interstellar probe", "link": "https://wikipedia.org/wiki/Interstellar_probe"}]}, {"year": "2014", "text": "Synagogue Church building collapse saw the deaths of 115 people and several injured, in the Church run by Nigeria's, <PERSON><PERSON> <PERSON><PERSON>.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Synagogue_Church_building_collapse\" title=\"Synagogue Church building collapse\">Synagogue Church building collapse</a> saw the deaths of 115 people and several injured, in the Church run by <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>'s, <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"T<PERSON> <PERSON><PERSON> <PERSON>\">T<PERSON> <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Synagogue_Church_building_collapse\" title=\"Synagogue Church building collapse\">Synagogue Church building collapse</a> saw the deaths of 115 people and several injured, in the Church run by <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>'s, <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"T. <PERSON><PERSON> <PERSON>\">T. <PERSON><PERSON></a>.", "links": [{"title": "Synagogue Church building collapse", "link": "https://wikipedia.org/wiki/Synagogue_Church_building_collapse"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "A series of explosions involving propane triggering nearby illegally stored mining detonators in the Indian town of Petlawad in the state of Madhya Pradesh kills at least 105 people with over 150 injured.", "html": "2015 - A series of <a href=\"https://wikipedia.org/wiki/Petlawad_explosion\" title=\"Petlawad explosion\">explosions</a> involving <a href=\"https://wikipedia.org/wiki/Propane\" title=\"Propane\">propane</a> triggering nearby illegally stored mining detonators in the Indian town of <a href=\"https://wikipedia.org/wiki/Petlawad\" title=\"Petlawad\">Petlawad</a> in the state of <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh\" title=\"Madhya Pradesh\">Madhya Pradesh</a> kills at least 105 people with over 150 injured.", "no_year_html": "A series of <a href=\"https://wikipedia.org/wiki/Petlawad_explosion\" title=\"Petlawad explosion\">explosions</a> involving <a href=\"https://wikipedia.org/wiki/Propane\" title=\"Propane\">propane</a> triggering nearby illegally stored mining detonators in the Indian town of <a href=\"https://wikipedia.org/wiki/Petlawad\" title=\"Petlawad\">Petlawad</a> in the state of <a href=\"https://wikipedia.org/wiki/Madhya_Pradesh\" title=\"Madhya Pradesh\">Madhya Pradesh</a> kills at least 105 people with over 150 injured.", "links": [{"title": "Petlawad explosion", "link": "https://wikipedia.org/wiki/Petlawad_explosion"}, {"title": "Propane", "link": "https://wikipedia.org/wiki/Propane"}, {"title": "Petlawad", "link": "https://wikipedia.org/wiki/<PERSON>lawad"}, {"title": "Madhya Pradesh", "link": "https://wikipedia.org/wiki/Madhya_Pradesh"}]}, {"year": "2021", "text": "Siberian Light Aviation Flight 51 crashes short of the runway at Kazachinskoye Airport, killing four.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Siberian_Light_Aviation_Flight_51\" title=\"Siberian Light Aviation Flight 51\">Siberian Light Aviation Flight 51</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Kazachinskoye_Airport\" title=\"Kazachinskoye Airport\">Kazachinskoye Airport</a>, killing four.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siberian_Light_Aviation_Flight_51\" title=\"Siberian Light Aviation Flight 51\">Siberian Light Aviation Flight 51</a> crashes short of the runway at <a href=\"https://wikipedia.org/wiki/Kazachinskoye_Airport\" title=\"Kazachinskoye Airport\">Kazachinskoye Airport</a>, killing four.", "links": [{"title": "Siberian Light Aviation Flight 51", "link": "https://wikipedia.org/wiki/Siberian_Light_Aviation_Flight_51"}, {"title": "Kazachinskoye Airport", "link": "https://wikipedia.org/wiki/Kazachinskoye_Airport"}]}], "Births": [{"year": "1415", "text": "<PERSON>, 3rd Duke of Norfolk (d. 1461)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Norfolk\" title=\"<PERSON>, 3rd Duke of Norfolk\"><PERSON>, 3rd Duke of Norfolk</a> (d. 1461)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Norfolk\" title=\"<PERSON>, 3rd Duke of Norfolk\"><PERSON>, 3rd Duke of Norfolk</a> (d. 1461)", "links": [{"title": "<PERSON>, 3rd Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Norfolk"}]}, {"year": "1494", "text": "<PERSON> of France (d. 1547)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (d. 1547)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1590", "text": "<PERSON>, Spanish writer (d. 1661)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish writer (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish writer (d. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, English genealogist and historian (d. 1686)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and historian (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English genealogist and historian (d. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, Flemish theologian and academic (d. 1775)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish theologian and academic (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish theologian and academic (d. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (d. 1776)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/Hsinbyushin\" title=\"Hsinbyush<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hsinbyushin\" title=\"Hsinbyush<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1776)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hsinbyushin"}]}, {"year": "1739", "text": "<PERSON>, Methodist preacher and philanthropist (d. 1815)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher and philanthropist (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(preacher)\" title=\"<PERSON> (preacher)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher and philanthropist (d. 1815)", "links": [{"title": "<PERSON> (preacher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(preacher)"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}]}, {"year": "1740", "text": "<PERSON>, German author and academic (d. 1817)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (d. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, English-American singer-songwriter, educator, and publisher (d. 1831)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter, educator, and publisher (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American singer-songwriter, educator, and publisher (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, American activist (d. 1871)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, English historian and jurist (d. 1878)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and jurist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and jurist (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, American engineer and businessman, invented the Rotary printing press (d. 1886)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, invented the <a href=\"https://wikipedia.org/wiki/Rotary_printing_press\" title=\"Rotary printing press\">Rotary printing press</a> (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, invented the <a href=\"https://wikipedia.org/wiki/Rotary_printing_press\" title=\"Rotary printing press\">Rotary printing press</a> (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Rotary printing press", "link": "https://wikipedia.org/wiki/Rotary_printing_press"}]}, {"year": "1818", "text": "<PERSON>, American inventor, invented the Gatling gun (d. 1903)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Gatling_gun\" title=\"Gatling gun\">Gatling gun</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Gatling_gun\" title=\"Gatling gun\">Gatling gun</a> (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Gatling gun", "link": "https://wikipedia.org/wiki/Gat<PERSON>_gun"}]}, {"year": "1818", "text": "<PERSON>, German pianist, composer, and educator (d. 1882)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and educator (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, English-Australian politician, 14th Premier of South Australia (d. 1883)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, English-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Australian_politician)\" title=\"<PERSON> (South Australian politician)\"><PERSON></a>, English-Australian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1883)", "links": [{"title": "<PERSON> (South Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, German painter (d. 1880)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Ansel<PERSON>_<PERSON>\" title=\"An<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>\" title=\"Ansel<PERSON>\"><PERSON><PERSON><PERSON></a>, German painter (d. 1880)", "links": [{"title": "<PERSON>sel<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, American essayist and novelist (d. 1900)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist and novelist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist and novelist (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, American businessman and politician, 27th Governor of Rhode Island (d. 1915)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON>gue IV\"><PERSON></a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"<PERSON>gue IV\"><PERSON></a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (d. 1915)", "links": [{"title": "<PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1837", "text": "<PERSON>, Grand Duke of Hesse (d. 1892)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (d. 1892)", "links": [{"title": "<PERSON>, Grand Duke of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse"}]}, {"year": "1852", "text": "<PERSON><PERSON> <PERSON><PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (d. 1928)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1928)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1855", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 12th Premier of Quebec (d. 1920)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9on_Parent\" title=\"Simon-Na<PERSON>éon Parent\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9on_Parent\" title=\"Simon<PERSON>Na<PERSON>on Parent\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Simon-Napol%C3%A9on_Parent"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1856", "text": "<PERSON>, American composer and conductor (d. 1924)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Colombian pharmacist and politician (d. 1919)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ista\" title=\"<PERSON>\"><PERSON></a>, Colombian pharmacist and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian pharmacist and politician (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Batista"}]}, {"year": "1862", "text": "<PERSON>, German-American painter and illustrator (d. 1925)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, 1st Marquess of Willingdon, English cricketer and politician, 13th Governor General of Canada (d. 1941)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Willingdon\" title=\"<PERSON>, 1st Marquess of Willingdon\"><PERSON>, 1st Marquess of Willingdon</a>, English cricketer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Willingdon\" title=\"<PERSON>, 1st Marquess of Willingdon\"><PERSON>, 1st Marquess of Willingdon</a>, English cricketer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (d. 1941)", "links": [{"title": "<PERSON><PERSON>, 1st Marquess of Willingdon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_1st_Marquess_of_Willingdon"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Polish actor (d. 1957)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ower%C5%82%C5%82o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ower%C5%82%C5%82o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish actor (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_Ower%C5%82%C5%82o"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese actor and director (d. 1926)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and director (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and director (d. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON> <PERSON><PERSON>, American journalist and critic (d. 1956)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and critic (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American journalist and critic (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Romanian journalist, politician, and archbishop (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Ion_Ag%C3%A2rbiceanu\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist, politician, and archbishop (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ion_Ag%C3%A2rbiceanu\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist, politician, and archbishop (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_Ag%C3%A2rbiceanu"}]}, {"year": "1884", "text": "<PERSON>, Estonian wrestler and coach (d. 1947)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Estonian wrestler and coach (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Estonian wrestler and coach (d. 1947)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1885", "text": "<PERSON>, German photographer and art dealer (d. 1957)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, German photographer and art dealer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, German photographer and art dealer (d. 1957)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>(photographer)"}]}, {"year": "1888", "text": "<PERSON>, French actor, singer, and dancer (d. 1972)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, and dancer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, singer, and dancer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Maltese politician, 3rd Prime Minister of Malta (d. 1942)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1891", "text": "<PERSON>, Puerto Rican lawyer and politician (d. 1965)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_Campos\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican lawyer and politician (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>s\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican lawyer and politician (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Albizu_Campos"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Belgian actor (d. 1977)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian actor (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American publisher (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "Alfred A. Knopf Sr., American publisher, founded Alfred A. Knopf Inc. (d. 1984)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sr.\" title=\"<PERSON>\"><PERSON> Sr<PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Alfred A. Knopf Inc.</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> Sr<PERSON></a>, American publisher, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Alfred A. Knopf Inc.</a> (d. 1984)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (d. 1953)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_To<PERSON>da"}]}, {"year": "1894", "text": "<PERSON>, Argentinian-English mathematician, biochemist and philosopher (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rin<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English mathematician, biochemist and philosopher (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English mathematician, biochemist and philosopher (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ch"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic painter and composer (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Freym%C3%B3%C3%B0ur_J%C3%B3<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic painter and composer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Freym%C3%B3%C3%B0ur_J%C3%B3<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic painter and composer (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Freym%C3%B3%C3%B0ur_J%C3%B3han<PERSON>son"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, French chemist and physicist, Nobel Prize laureate (d. 1956)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Ir%C3%A8<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ir%C3%A8<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ir%C3%A8ne_<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1897", "text": "<PERSON>, American magician and author (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Spanish composer (d. 1963)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Salvador_Bacarisse\" title=\"Salvador Bacarisse\"><PERSON></a>, Spanish composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Bacarisse\" title=\"Salvador Bacarisse\"><PERSON></a>, Spanish composer (d. 1963)", "links": [{"title": "Salvador Bacarisse", "link": "https://wikipedia.org/wiki/Salvador_Bacarisse"}]}, {"year": "1898", "text": "<PERSON>, Australian violinist and educator (d. 1943)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Moodie\"><PERSON></a>, Australian violinist and educator (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Moodie\"><PERSON></a>, Australian violinist and educator (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Lithuanian-American painter and photographer (d. 1969)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American painter and photographer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American painter and photographer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American radio director (d. 1949)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio director (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio director (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American mathematician, logician, and academic (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curry\"><PERSON><PERSON></a>, American mathematician, logician, and academic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Curry\"><PERSON><PERSON></a>, American mathematician, logician, and academic (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Israeli agronomist and academic (d. 1999)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli agronomist and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli agronomist and academic (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian physician and politician, 21st President of Brazil (d. 1976)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian physician and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian physician and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juscelino_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Ukrainian-American poet and author (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American poet and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American poet and author (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marya_Zaturenska"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Hungarian fighter pilot and deputy regent (d. 1942)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian fighter pilot and deputy regent (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian fighter pilot and deputy regent (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_<PERSON><PERSON>y"}]}, {"year": "1904", "text": "<PERSON>, American priest and theologian (d. 1967)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and theologian (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American race car driver (d. 1956)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English-Australian murder victim (d. 1934)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Australian murder victim (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English-Australian murder victim (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Irish poet and playwright (d. 1963)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and playwright (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German jurist (d. 2009)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Canadian trade union leader and politician (d. 1986)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian trade union leader and politician (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)\" title=\"<PERSON> (Nova Scotia politician)\"><PERSON></a>, Canadian trade union leader and politician (d. 1986)", "links": [{"title": "<PERSON> (Nova Scotia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Nova_Scotia_politician)"}]}, {"year": "1913", "text": "<PERSON>, American sprinter and long jumper (d. 1980)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and long jumper (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and long jumper (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Pakistani psychoanalyst, poet, and scholar (d. 1988)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani psychoanalyst, poet, and scholar (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani psychoanalyst, poet, and scholar (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Am<PERSON>hvi"}]}, {"year": "1914", "text": "<PERSON>, Welsh-English soldier and actor (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English soldier and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English soldier and actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American race car driver (d. 1961)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Canadian colonel, academic, and politician (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian colonel, academic, and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian colonel, academic, and politician (d. 2004)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_S%C3%<PERSON><PERSON><PERSON><PERSON>_(politician)"}]}, {"year": "1917", "text": "<PERSON>, Chinese-Swiss physician and author (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Su<PERSON>\" title=\"Han Suyin\"><PERSON></a>, Chinese-Swiss physician and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Han Suyin\"><PERSON></a>, Chinese-Swiss physician and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1920", "text": "<PERSON>, American actress (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American journalist (d. 1974)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 1974)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Polish philosopher and author (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Lem\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Lem\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher and author (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Lem"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Turkish architect, city planner, and thinker (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Turgut_Cansever\" title=\"Turgut Cansever\"><PERSON><PERSON><PERSON> Cansever</a>, Turkish architect, city planner, and thinker (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turgut_Cansever\" title=\"Turgut Cansever\"><PERSON><PERSON><PERSON> Canse<PERSON></a>, Turkish architect, city planner, and thinker (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON> Cansever", "link": "https://wikipedia.org/wiki/Turgut_Cansever"}]}, {"year": "1922", "text": "<PERSON>, Argentinian accountant and politician, Governor of Buenos Aires Province (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian accountant and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian accountant and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province\" title=\"Governor of Buenos Aires Province\">Governor of Buenos Aires Province</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Buenos Aires Province", "link": "https://wikipedia.org/wiki/Governor_of_Buenos_Aires_Province"}]}, {"year": "1922", "text": "<PERSON>, American poet, playwright, and composer (d. 2004)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, playwright, and composer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American psychologist and academic (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (d. 2009)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Guinea-Bissauan political leader (d. 1973)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Am%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guinea-Bissauan political leader (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guinea-Bissauan political leader (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%ADlcar_Cabral"}]}, {"year": "1925", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American actor (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, American actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)\" title=\"<PERSON><PERSON> (actor)\"><PERSON><PERSON></a>, American actor (d. 2015)", "links": [{"title": "<PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, French soprano and actress", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Math%C3%A9_Alt%C3%A9ry\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Math%C3%A9_Alt%C3%A9ry\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Math%C3%A9_Alt%C3%A9ry"}]}, {"year": "1928", "text": "<PERSON>, American painter and gardener (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and gardener (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and gardener (d. 2023)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>(artist)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American businesswoman and philanthropist (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and philanthropist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian-American basketball player and physician (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player and physician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American basketball player and physician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American Roman Catholic prelate (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic prelate (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic prelate (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American composer and illustrator (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and illustrator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and illustrator (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American composer and educator (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Larry Austin\"><PERSON></a>, American composer and educator (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English actor (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Faroese engineer and politician, 5th Prime Minister of the Faroe Islands (d. 2005)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Atli_Dam\" title=\"Atli Dam\">Atli Dam</a>, Faroese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atli_Dam\" title=\"Atli Dam\">Atli Dam</a>, Faroese engineer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2005)", "links": [{"title": "Atli Dam", "link": "https://wikipedia.org/wiki/Atli_Dam"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1934", "text": "<PERSON>, American hurdler, sprinter, and football player (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler, sprinter, and football player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hurdler, sprinter, and football player (d. 2009)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean-American philosopher and academic (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean-American philosopher and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean-American philosopher and academic (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Chinese American poet and activist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese American poet and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese American poet and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sculptor (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, American sculptor (d. 2023)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "1937", "text": "<PERSON>, Canadian boxer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Barbadian cricketer and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wes Hall\"><PERSON></a>, Barbadian cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wes_Hall\" title=\"Wes Hall\"><PERSON></a>, Barbadian cricketer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wes_Hall"}]}, {"year": "1938", "text": "<PERSON>, American soul and gospel singer (d. 2001)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul and gospel singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul and gospel singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American operatic soprano (d. 1993)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American operatic soprano (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tatiana_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American model and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French journalist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Spanish composer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Tom%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Marco"}]}, {"year": "1942", "text": "<PERSON>, American folk and blues singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk and blues singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk and blues singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian engineer and academic (d. 2004)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Tavenas"}]}, {"year": "1943", "text": "<PERSON>, American football player (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Sri Lankan-Canadian author and poet", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-Canadian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-Canadian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American wrestler (d. 1978)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American wrestler (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lonnie_<PERSON>ne"}]}, {"year": "1944", "text": "<PERSON>, American political activist and convicted criminal", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist and convicted criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist and convicted criminal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Russian violinist and conductor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian violinist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian violinist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON> \"<PERSON>\" <PERSON>, American drag racer (d. 1977)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> \"Jungle Jim\" <PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_racing\" title=\"Drag racing\">drag racer</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> \"Jungle Jim\" <PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_racing\" title=\"Drag racing\">drag racer</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Drag racing", "link": "https://wikipedia.org/wiki/Drag_racing"}]}, {"year": "1945", "text": "<PERSON>, Italian author and illustrator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American conductor and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tony Bellamy\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tony Bellamy\"><PERSON></a>, American singer-songwriter and guitarist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, British journalist and writer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British journalist and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English engineer and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" class=\"mw-redirect\" title=\"<PERSON> (academic)\"><PERSON></a>, English engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)\" class=\"mw-redirect\" title=\"<PERSON> (academic)\"><PERSON></a>, English engineer and academic", "links": [{"title": "<PERSON> (academic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(academic)"}]}, {"year": "1947", "text": "<PERSON>, English soldier, pilot, and politician, Minister for International Security Strategy", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, pilot, and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_International_Security_Strategy\" title=\"Minister for International Security Strategy\">Minister for International Security Strategy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, pilot, and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_International_Security_Strategy\" title=\"Minister for International Security Strategy\">Minister for International Security Strategy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for International Security Strategy", "link": "https://wikipedia.org/wiki/Minister_for_International_Security_Strategy"}]}, {"year": "1947", "text": "<PERSON>, English actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American trombonist and educator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian footballer, cricketer, sportscaster, and architect (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, sportscaster, and architect (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, sportscaster, and architect (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American captain and pilot (d. 2001)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Russian figure skater and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian journalist and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Austrian engineer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brunner\"><PERSON></a>, Austrian engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gustav Brunner\"><PERSON></a>, Austrian engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1950)\" title=\"<PERSON> (ice hockey, born 1950)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1950)\" title=\"<PERSON> (ice hockey, born 1950)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey, born 1950)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1950)"}]}, {"year": "1951", "text": "<PERSON>, Irish accountant and politician, 11th Taoiseach of Ireland", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish accountant and politician, 11th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish accountant and politician, 11th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Norm_Dub%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norm_Dub%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norm_Dub%C3%A9"}]}, {"year": "1951", "text": "<PERSON>, Welsh rugby player and actor (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter and keyboard player (d. 2010)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter and keyboard player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter and keyboard player (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian drummer, songwriter, and producer (d. 2020)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer, songwriter, and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian drummer, songwriter, and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American photographer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American sculptor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American saxophonist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Estonian singer-songwriter and actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English footballer (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1955)\" title=\"<PERSON> (footballer, born 1955)\"><PERSON></a>, English footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1955)\" title=\"<PERSON> (footballer, born 1955)\"><PERSON></a>, English footballer (d. 2013)", "links": [{"title": "<PERSON> (footballer, born 1955)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1955)"}]}, {"year": "1956", "text": "<PERSON>, English singer and keyboard player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and keyboard player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1956", "text": "<PERSON>, Hong Kong singer-songwriter and actor (d. 2003)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter and actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish songwriter", "links": [{"title": "BA Robertson", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Singaporean lawyer and politician, 7th Attorney-General of Singapore", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Singapore\" title=\"Attorney-General of Singapore\">Attorney-General of Singapore</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Attorney-General_of_Singapore\" title=\"Attorney-General of Singapore\">Attorney-General of Singapore</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Attorney-General of Singapore", "link": "https://wikipedia.org/wiki/Attorney-General_of_Singapore"}]}, {"year": "1957", "text": "<PERSON>, British academic and educator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Norwegian politician, diplomat and humanitarian", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician, diplomat and humanitarian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician, diplomat and humanitarian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1957", "text": "<PERSON>, English-Australian actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German composer and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American boxer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>%C3%ADtez\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>%C3%ADtez\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, American boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>%C3%ADtez"}]}, {"year": "1958", "text": "<PERSON>, American actor and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American colonel and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American colonel and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American colonel and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American football player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, German educator and politician, 17th Vice-Chancellor of Germany", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, 17th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "1960", "text": "Road Warrior Animal, American wrestler (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Road_Warrior_Animal\" title=\"Road Warrior Animal\">Road Warrior Animal</a>, American wrestler (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Road_Warrior_Animal\" title=\"Road Warrior Animal\">Road Warrior Animal</a>, American wrestler (d. 2020)", "links": [{"title": "Road Warrior Animal", "link": "https://wikipedia.org/wiki/Road_Warrior_Animal"}]}, {"year": "1960", "text": "<PERSON>, American academic and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Greek pianist and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ko<PERSON>\"><PERSON><PERSON></a>, Greek pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Canadian-French singer-songwriter, producer, and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Myl%C3%A8ne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-French singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myl%C3%A8ne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-French singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Myl%C3%A8ne_Farmer"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Turkish poet, journalist, and philanthropist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Sunay_Ak%C4%B1n\" title=\"Sunay Akın\"><PERSON><PERSON></a>, Turkish poet, journalist, and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sunay_Ak%C4%B1n\" title=\"Sunay Akın\"><PERSON><PERSON></a>, Turkish poet, journalist, and philanthropist", "links": [{"title": "Sunay Akın", "link": "https://wikipedia.org/wiki/Sunay_Ak%C4%B1n"}]}, {"year": "1962", "text": "<PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American television journalist and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Norwegian animator and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian animator and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian animator and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Jamaican wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\">Midnight</a>, Jamaican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\">Midnight</a>, Jamaican wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Vez<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vez<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vez<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Louis_C.K.\" title=\"Louis C.K.\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_C.K.\" title=\"Louis C.K.\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "Louis C.K.", "link": "https://wikipedia.org/wiki/Louis_C.K."}]}, {"year": "1967", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ach"}]}, {"year": "1968", "text": "<PERSON>, American guitarist and songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, 6th <PERSON>, English politician (d. 2014)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th <PERSON>\"><PERSON>, 6th <PERSON></a>, English politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th <PERSON>\"><PERSON>, 6th <PERSON></a>, English politician (d. 2014)", "links": [{"title": "<PERSON>, 6th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, South African cricketer and physiotherapist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and physiotherapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and physiotherapist", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1968", "text": "<PERSON>, American comedian, actor, and writer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Russian-American historian and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Boot\"><PERSON></a>, Russian-American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Max Boot\"><PERSON></a>, Russian-American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Argentinian golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_<PERSON>abrera"}]}, {"year": "1969", "text": "<PERSON>, American author and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, director, and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Moroccan tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/You<PERSON>_<PERSON>_<PERSON>oui\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/You<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1972", "text": "<PERSON>, English-American actor, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player and coach (d. 2022)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1972", "text": "<PERSON>, Brazilian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON> (footballer, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1972)"}]}, {"year": "1973", "text": "<PERSON>, Filipino journalist and documentarian", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and documentarian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and documentarian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German skier", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German skier", "links": [{"title": "Martina Ertl-Renz", "link": "https://wikipedia.org/wiki/Martina_Ertl-Renz"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor (d. 2013)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French soldier and pilot (d. 2007)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and pilot (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and pilot (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English race car driver", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese voice actor and singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Portuguese footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nte\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uno_Valente\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uno_Valente"}]}, {"year": "1975", "text": "<PERSON>, Dominican baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(second_baseman)\" title=\"<PERSON> (second baseman)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(second_baseman)\" title=\"<PERSON> (second baseman)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (second baseman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(second_baseman)"}]}, {"year": "1975", "text": "<PERSON>, Australian swimmer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>amile"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Maciej_%C5%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maciej_%C5%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maciej_%C5%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "2 <PERSON><PERSON>, American rapper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/2_Chainz\" title=\"2 Chainz\">2 <PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2_Chainz\" title=\"2 Chainz\">2 <PERSON><PERSON></a>, American rapper", "links": [{"title": "2 Chainz", "link": "https://wikipedia.org/wiki/2_<PERSON>z"}]}, {"year": "1977", "text": "<PERSON>, Australian race car driver and journalist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1977)\" title=\"<PERSON> (footballer, born 1977)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1977)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian model and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>is\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>is\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian model and actress", "links": [{"title": "<PERSON>sabet<PERSON>", "link": "https://wikipedia.org/wiki/Elisabetta_Canalis"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American R&B, pop, and gospel singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B, pop, and gospel singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American R&amp;B, pop, and gospel singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player (d. 2024)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Fernando_<PERSON>%C3%A9sar_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fernando_C%C3%A9sar_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_C%C3%A9<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Chinese basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Czech ice hockey player (d. 2011)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>a%C5%A1%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>a%C5%A1%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_Va%C5%A1%C3%AD%C4%8Dek"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Stac<PERSON>_Stitts\" title=\"St<PERSON><PERSON> Stitts\"><PERSON><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stac<PERSON>_Stitts\" title=\"St<PERSON><PERSON> Stitts\"><PERSON><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Staciana_Stitts"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Croatian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ini%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ini%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_Planini%C4%87"}]}, {"year": "1982", "text": "<PERSON>, American wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_Rinauro"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fler\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%9Fler"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Jordanian guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jordanian guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jordanian guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Argentinian-Italian rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Iraqi footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Nash<PERSON>_Akram\" title=\"Nashat Akram\"><PERSON><PERSON></a>, Iraqi footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nash<PERSON>_Akram\" title=\"Nashat Akram\"><PERSON><PERSON></a>, Iraqi footballer", "links": [{"title": "<PERSON><PERSON>kram", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian curler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Carey"}]}, {"year": "1984", "text": "<PERSON>, Swedish singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, English actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Polish heptathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish heptathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Akwas<PERSON>_<PERSON>obi-Edusei\" title=\"Akwas<PERSON> Fobi-Edusei\"><PERSON><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ak<PERSON><PERSON>_<PERSON>obi-Edusei\" title=\"<PERSON>k<PERSON><PERSON> Fobi-Edusei\"><PERSON><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akwas<PERSON>_Fobi-Ed<PERSON>i"}]}, {"year": "1986", "text": "<PERSON>, English swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Yuto_Nagatomo\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Nagatomo\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuto_Nagatomo\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Nagatomo\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yuto_Nagatomo"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Greek sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dimitrios_Regas"}]}, {"year": "1986", "text": "<PERSON>, American singer and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Swedish singer-songwriter and guitarist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic politician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Gu%C3%B0mundur_Ari_Sigurj%C3%B3<PERSON>son\" title=\"<PERSON>u<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gu%C3%B0mundur_Ari_Sigurj%C3%B3nsson\" title=\"<PERSON>u<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gu%C3%B0mundur_Ari_Sigurj%C3%B3nsson"}]}, {"year": "1989", "text": "<PERSON>, American-Canadian baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Scottish professional boxer (d. 2016)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish professional boxer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish professional boxer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fast\" title=\"Alexia Fast\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Fast\" title=\"Alexia Fast\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sviatlana_Pirazhenka\" title=\"Sviatlana Pirazhenka\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sviatlana_Pirazhenka\" title=\"Sviatlana Pirazhenka\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "Sviatlana <PERSON>", "link": "https://wikipedia.org/wiki/Sviatlana_Pirazhenka"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American country pop singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ballerini\" title=\"<PERSON><PERSON><PERSON> Ballerini\"><PERSON><PERSON><PERSON></a>, American country pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ballerini\" title=\"<PERSON><PERSON><PERSON> Ballerini\"><PERSON><PERSON><PERSON></a>, American country pop singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kels<PERSON>_Ballerini"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American comedian and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>i"}]}, {"year": "1994", "text": "<PERSON><PERSON>, South Korean rapper, songwriter and record producer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper, songwriter and record producer", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Ukrainian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Bahamian sprinter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sweeney\" title=\"<PERSON> Sweeney\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sweeney\" title=\"<PERSON> Sweeney\"><PERSON></a>, American actress", "links": [{"title": "Sydney Sweeney", "link": "https://wikipedia.org/wiki/Sydney_Sweeney"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Swedish curler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Almida_de_Val\" title=\"Almida de Val\"><PERSON><PERSON><PERSON></a>, Swedish curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almida_de_Val\" title=\"Almida de Val\"><PERSON><PERSON><PERSON></a>, Swedish curler", "links": [{"title": "Almida de Val", "link": "https://wikipedia.org/wiki/Almida_de_Val"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "640", "text": "<PERSON><PERSON>, Mayan queen", "html": "640 - <a href=\"https://wikipedia.org/wiki/Sak_K%CA%BCuk%CA%BC\" title=\"Sak Kʼuk<PERSON>\"><PERSON><PERSON></a>, Mayan queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sak_K%CA%BCuk%CA%BC\" title=\"Sak K<PERSON>uk<PERSON>\"><PERSON><PERSON></a>, Mayan queen", "links": [{"title": "Sak Kʼukʼ", "link": "https://wikipedia.org/wiki/Sak_K%CA%BCuk%CA%BC"}]}, {"year": "973", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Angers", "html": "973 - <a href=\"https://wikipedia.org/wiki/Nefingus\" title=\"Nefingus\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Angers\" title=\"Roman Catholic Diocese of Angers\">Angers</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nefingus\" title=\"Nefingus\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Angers\" title=\"Roman Catholic Diocese of Angers\">Angers</a>", "links": [{"title": "Nefingus", "link": "https://wikipedia.org/wiki/Nefingus"}, {"title": "Roman Catholic Diocese of Angers", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Angers"}]}, {"year": "1185", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (b. 1118)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/Andronikos_I_Komnenos\" title=\"Andronikos I Komnenos\">Andronikos I Komnenos</a>, Byzantine emperor (b. 1118)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andronikos_I_Komnenos\" title=\"Andronikos I Komnenos\">Andronikos I Komnenos</a>, Byzantine emperor (b. 1118)", "links": [{"title": "Andronikos I Komnenos", "link": "https://wikipedia.org/wiki/Andronikos_I_Komnenos"}]}, {"year": "1213", "text": "<PERSON> of Aragon (b. 1174)", "html": "1213 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of <PERSON></a> (b. 1174)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1174)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1362", "text": "<PERSON> (b. 1295)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_VI\" title=\"Pope Innocent VI\">Pope <PERSON> VI</a> (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_VI\" title=\"Pope Innocent VI\">Pope <PERSON> VI</a> (b. 1295)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_VI"}]}, {"year": "1368", "text": "<PERSON> Lancaster (b. 1345/1347)", "html": "1368 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Lancaster\" title=\"<PERSON> of Lancaster\"><PERSON> of Lancaster</a> (b. 1345/1347)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Lancaster\" title=\"<PERSON> of Lancaster\"><PERSON> of Lancaster</a> (b. 1345/1347)", "links": [{"title": "Blanche of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lancaster"}]}, {"year": "1439", "text": "<PERSON><PERSON>, Algerian imam (b. 1350)", "html": "1439 - <a href=\"https://wikipedia.org/wiki/Sid<PERSON>_<PERSON>_<PERSON>\" title=\"Sidi El Houari\"><PERSON><PERSON></a>, Algerian imam (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sid<PERSON>_<PERSON>_<PERSON>\" title=\"Sidi El Houari\"><PERSON><PERSON></a>, Algerian imam (b. 1350)", "links": [{"title": "Sidi El Houari", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1500", "text": "<PERSON>, Duke of Saxony (b. 1443)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (b. 1443)", "links": [{"title": "<PERSON>, Duke of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony"}]}, {"year": "1544", "text": "<PERSON><PERSON><PERSON>, French poet (b. 1496)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Marot\" title=\"Clé<PERSON> Marot\"><PERSON><PERSON><PERSON></a>, French poet (b. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Marot\" title=\"C<PERSON><PERSON> Marot\"><PERSON><PERSON><PERSON></a>, French poet (b. 1496)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Marot"}]}, {"year": "1612", "text": "<PERSON><PERSON><PERSON> of Russia (b. 1552)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Russia\" title=\"<PERSON><PERSON><PERSON> IV of Russia\"><PERSON><PERSON><PERSON> IV of Russia</a> (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Russia\" title=\"<PERSON><PERSON><PERSON> IV of Russia\"><PERSON><PERSON><PERSON> IV of Russia</a> (b. 1552)", "links": [{"title": "<PERSON><PERSON><PERSON> IV of Russia", "link": "https://wikipedia.org/wiki/V<PERSON>li_IV_of_Russia"}]}, {"year": "1642", "text": "<PERSON>, Marquis of Cinq-Mars, French conspirator (b. 1620)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ruz%C3%A9,_Marquis_of_Cinq-Mars\" title=\"<PERSON>, Marquis of Cinq-Mars\"><PERSON>, Marquis of Cinq-Mars</a>, French conspirator (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ruz%C3%A9,_Marquis_of_Cinq-Mars\" title=\"<PERSON>, Marquis of Cinq-Mars\"><PERSON>, Marquis of Cinq-Mars</a>, French conspirator (b. 1620)", "links": [{"title": "<PERSON>, Marquis of Cinq-Mars", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>uz%C3%A9,_<PERSON>_<PERSON>_Cinq-Mars"}]}, {"year": "1660", "text": "<PERSON>, Dutch poet, jurist, and politician (b. 1577)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jacob Cats\"><PERSON></a>, Dutch poet, jurist, and politician (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jacob_Cats\" title=\"Jacob Cats\"><PERSON></a>, Dutch poet, jurist, and politician (b. 1577)", "links": [{"title": "Jacob Cats", "link": "https://wikipedia.org/wiki/Jacob_Cats"}]}, {"year": "1665", "text": "<PERSON>, Belgian priest and hagiographer (b. 1596)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian priest and hagiographer (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian priest and hagiographer (b. 1596)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1672", "text": "<PERSON><PERSON><PERSON><PERSON>, French scholar and author (b. 1615)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/Tanneguy_Le_F%C3%A8vre\" title=\"Tanneguy Le Fèvre\"><PERSON><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tanneguy_Le_F%C3%A8vre\" title=\"Tanneguy Le Fèvre\"><PERSON><PERSON><PERSON><PERSON></a>, French scholar and author (b. 1615)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Fèvre", "link": "https://wikipedia.org/wiki/Tanneguy_Le_F%C3%A8vre"}]}, {"year": "1674", "text": "<PERSON><PERSON>, Dutch anatomist and politician (b. 1593)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch anatomist and politician (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch anatomist and politician (b. 1593)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON> of Portugal (b. 1643)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_of_Portugal\" title=\"<PERSON><PERSON><PERSON> VI of Portugal\"><PERSON><PERSON><PERSON> VI of Portugal</a> (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_VI_of_Portugal\" title=\"<PERSON><PERSON><PERSON> VI of Portugal\"><PERSON><PERSON><PERSON> VI of Portugal</a> (b. 1643)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Afonso_VI_of_Portugal"}]}, {"year": "1712", "text": "<PERSON>, Dutch painter and illustrator (b. 1637)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and illustrator (b. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON><PERSON><PERSON>, French composer and theorist (b. 1683)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and theorist (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and theorist (b. 1683)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON>, 2nd Earl <PERSON>, English politician, Lord Lieutenant of Buckinghamshire (b. 1711)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>\" title=\"<PERSON>, 2nd Earl <PERSON>\"><PERSON>, 2nd Earl <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire\" title=\"Lord Lieutenant of Buckinghamshire\">Lord Lieutenant of Buckinghamshire</a> (b. 1711)", "links": [{"title": "<PERSON><PERSON>, 2nd Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_2nd_<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Buckinghamshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Buckinghamshire"}]}, {"year": "1810", "text": "Sir <PERSON>, 1st Baronet, English banker and politician (b. 1740)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English banker and politician (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English banker and politician (b. 1740)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1814", "text": "<PERSON>, Irish general (b. 1766)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Irish general (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Irish general (b. 1766)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, Prussian general (b. 1742)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_von_Bl%C3%<PERSON>cher\" title=\"<PERSON><PERSON><PERSON>lücher\"><PERSON><PERSON><PERSON></a>, Prussian general (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_von_Bl%C3%<PERSON>cher\" title=\"<PERSON><PERSON><PERSON> Blücher\"><PERSON><PERSON><PERSON></a>, Prussian general (b. 1742)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_von_Bl%C3%BCcher"}]}, {"year": "1836", "text": "<PERSON>, German playwright (b. 1801)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German playwright (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German playwright (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, English physician, theologian, and lexicographer (b. 1779)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, theologian, and lexicographer (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, theologian, and lexicographer (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, English philanthropist (b. 1782)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English philanthropist (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English philanthropist (b. 1782)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, American journalist, explorer, and author (b. 1836)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON>tz <PERSON></a>, American journalist, explorer, and author (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist, explorer, and author (b. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, French historian and politician, 22nd Prime Minister of France (b. 1787)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_G<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and politician, 22nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1903", "text": "<PERSON>, Scottish-Australian businessman and politician, 14th Premier of Victoria (b. 1834)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian businessman and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian businessman and politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Georgian poet, journalist, and lawyer (b. 1837)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian poet, journalist, and lawyer (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian poet, journalist, and lawyer (b. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, French cardinal (b. 1829)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (b. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1918", "text": "<PERSON>, Australian accountant and politician, 4th Prime Minister of Australia (b. 1845)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian accountant and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian accountant and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Russian author and playwright (b. 1871)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French physicist and academic (b. 1841)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American physicist and astronomer (b. 1847)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Welsh co-founder of the Martin-Baker Aircraft Company (b. 1888)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Welsh co-founder of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Martin-Baker Aircraft Company</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, Welsh co-founder of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Martin-Baker Aircraft Company</a> (b. 1888)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese field marshal and politician, 44th Japanese Minister of War  (b. 1880)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 44th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese field marshal and politician, 44th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of War</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>giyama"}, {"title": "Ministry of War of Japan", "link": "https://wikipedia.org/wiki/Ministry_of_War_of_Japan"}]}, {"year": "1949", "text": "<PERSON>, Finnish physician (b. 1870)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish physician (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish physician (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, 3rd Duke of Abercorn, English politician, Governor of Northern Ireland (b. 1869)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_of_Abercorn\" title=\"<PERSON>, 3rd Duke of Abercorn\"><PERSON>, 3rd Duke of Abercorn</a>, English politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Northern_Ireland\" title=\"Governor of Northern Ireland\">Governor of Northern Ireland</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Abercorn\" title=\"<PERSON>, 3rd Duke of Abercorn\"><PERSON>, 3rd Duke of Abercorn</a>, English politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Northern_Ireland\" title=\"Governor of Northern Ireland\">Governor of Northern Ireland</a> (b. 1869)", "links": [{"title": "<PERSON>, 3rd Duke of Abercorn", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duke_<PERSON>_<PERSON>orn"}, {"title": "Governor of Northern Ireland", "link": "https://wikipedia.org/wiki/Governor_of_Northern_Ireland"}]}, {"year": "1953", "text": "<PERSON>, German engineer (b. 1884)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor (b. 1879)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Hungarian politician, Hungarian Minister of War (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Festetics\" title=\"Sándor Festetics\"><PERSON><PERSON><PERSON> Fest<PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of War</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Festetics\" title=\"Sándor Festetics\"><PERSON><PERSON><PERSON> Fest<PERSON></a>, Hungarian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)\" class=\"mw-redirect\" title=\"Ministry of Defence (Hungary)\">Hungarian Minister of War</a> (b. 1882)", "links": [{"title": "S<PERSON>dor <PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Festetics"}, {"title": "Ministry of Defence (Hungary)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Hungary)"}]}, {"year": "1961", "text": "<PERSON>, German physicist and academic (b. 1898)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player and soldier (b. 1887)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Spot_Poles\" title=\"Spot Poles\">Spot Poles</a>, American baseball player and soldier (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spot_Poles\" title=\"Spot Poles\"><PERSON> Poles</a>, American baseball player and soldier (b. 1887)", "links": [{"title": "Spot Poles", "link": "https://wikipedia.org/wiki/Spot_Poles"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Indian author and playwright (b. 1923)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Raghav\" title=\"<PERSON>ya Raghav\"><PERSON><PERSON></a>, Indian author and playwright (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Raghav\" title=\"<PERSON>ya Raghav\"><PERSON><PERSON></a>, Indian author and playwright (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian-Slovene author and playwright (b. 1903)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Slovene author and playwright (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Slovene author and playwright (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Scottish-American golfer and journalist (b. 1894)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer and journalist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer and journalist (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American golfer (b. 1881)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1881)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1972", "text": "<PERSON>, American actor and producer (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer (b. 1895)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1977", "text": "<PERSON>, South African activist (b. 1946)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African activist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian journalist and politician (b. 1898)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American poet (b. 1917)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand-Australian engineer (b. 1896)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, New Zealand-Australian engineer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, New Zealand-Australian engineer (b. 1896)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_(engineer)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Italian writer, Nobel Prize laureate (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1982", "text": "<PERSON>, Spanish composer and conductor (b. 1891)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish composer and conductor (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French painter and photographer (b. 1894)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and photographer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and photographer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German-English psychotherapist and physician (b. 1897)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English psychotherapist and physician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English psychotherapist and physician (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian-American actor (b. 1899)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1889)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Athen<PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Canadian general and businessman (b. 1909)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_Army_officer)\" title=\"<PERSON> (Canadian Army officer)\"><PERSON></a>, Canadian general and businessman (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_Army_officer)\" title=\"<PERSON> (Canadian Army officer)\"><PERSON></a>, Canadian general and businessman (b. 1909)", "links": [{"title": "<PERSON> (Canadian Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_Army_officer)"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1992", "text": "<PERSON>, American actor, singer, and director (b. 1932)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and director (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian-American actor and director (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and director (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Russian physician and astronaut (b. 1937)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor (b. 1933)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese motorcycle racer (b. 1965)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>gai"}]}, {"year": "1996", "text": "<PERSON>, Brazilian general and politician, 29th President of Brazil (b. 1907)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian general and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian general and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1997", "text": "<PERSON>, American-Canadian science fiction writer, editor and political activist (b. 1923)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian science fiction writer, editor and political activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian science fiction writer, editor and political activist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1922)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American saxophonist, composer, and bandleader (b. 1934)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and bandleader (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1932)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cash\" title=\"Johnny Cash\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cash\" title=\"Johnny Cash\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, canoeist (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, canoeist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, canoeist (b. 1921)", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "2005", "text": "<PERSON>, French-American mathematician, author and academic (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American mathematician, author and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American mathematician, author and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter and producer  (b. 1934)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Australian footballer and coach (b. 1915)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer and coach (b. 1915)", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_footballer)"}]}, {"year": "2008", "text": "<PERSON>, American novelist, short story writer, and essayist (b. 1962)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and essayist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American agronomist and humanitarian, Nobel Prize laureate (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and humanitarian, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American agronomist and humanitarian, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "2009", "text": "<PERSON>, American tennis player and sportscaster (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, French photographer and author (b. 1910)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, French actor, director, producer, and screenwriter (b. 1930)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Italian footballer (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Russian ice hockey player (b. 1985)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian poet and author (b. 1946)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and author (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian actor and screenwriter (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and screenwriter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English journalist and broadcaster (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and broadcaster (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American skateboarder and snowboarder, founded <PERSON> Snowboards (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and snowboarder, founded <a href=\"https://wikipedia.org/wiki/Sims_Snowboards\" class=\"mw-redirect\" title=\"Sims Snowboards\">Sims Snowboards</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and snowboarder, founded <a href=\"https://wikipedia.org/wiki/Sims_Snowboards\" class=\"mw-redirect\" title=\"Sims Snowboards\">Sims Snowboards</a> (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sims Snowboards", "link": "https://wikipedia.org/wiki/Sims_Snowboards"}]}, {"year": "2013", "text": "<PERSON>, American engineer and businessman, founded Dolby Laboratories (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dolby_Laboratories\" class=\"mw-redirect\" title=\"Dolby Laboratories\">Dolby Laboratories</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Dolby_Laboratories\" class=\"mw-redirect\" title=\"Dolby Laboratories\">Dolby Laboratories</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dolby Laboratories", "link": "https://wikipedia.org/wiki/Dolby_Laboratories"}]}, {"year": "2013", "text": "<PERSON>, American football player, coach, and politician (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German author and screenwriter (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American neuroscientist and pharmacologist (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and pharmacologist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and pharmacologist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Egyptian academic and politician, 47th Prime Minister of Egypt (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian academic and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Egypt\" class=\"mw-redirect\" title=\"List of Prime Ministers of Egypt\">Prime Minister of Egypt</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>d\"><PERSON><PERSON></a>, Egyptian academic and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Egypt\" class=\"mw-redirect\" title=\"List of Prime Ministers of Egypt\">Prime Minister of Egypt</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atef_Ebeid"}, {"title": "List of Prime Ministers of Egypt", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Egypt"}]}, {"year": "2014", "text": "<PERSON>, English singer-songwriter and bass player (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player (b. 1942)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2014", "text": "<PERSON>, Northern Irish evangelical pastor (Free Presbyterian Church) and politician, 2nd First Minister of Northern Ireland (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish evangelical pastor (<a href=\"https://wikipedia.org/wiki/Free_Presbyterian_Church_of_Ulster\" title=\"Free Presbyterian Church of Ulster\">Free Presbyterian Church</a>) and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish evangelical pastor (<a href=\"https://wikipedia.org/wiki/Free_Presbyterian_Church_of_Ulster\" title=\"Free Presbyterian Church of Ulster\">Free Presbyterian Church</a>) and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Free Presbyterian Church of Ulster", "link": "https://wikipedia.org/wiki/Free_Presbyterian_Church_of_Ulster"}, {"title": "First Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland"}]}, {"year": "2014", "text": "<PERSON>, American pianist and composer (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Jr., American golfer (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American golfer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American golfer (b. 1936)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2015", "text": "<PERSON>, American philosopher and academic (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Claudia Card\"><PERSON></a>, American philosopher and academic (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American playwright and screenwriter (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and coach (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Al_Mon<PERSON>k\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Monchak\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1917)", "links": [{"title": "Al Monchak", "link": "https://wikipedia.org/wiki/Al_Monchak"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Ugandan general and politician (b. 1959)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Aronda_Nyakairima\" title=\"<PERSON>ronda Nyakairima\"><PERSON><PERSON><PERSON></a>, Ugandan general and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aronda_Nyakairima\" title=\"<PERSON>ron<PERSON> Nyakairima\"><PERSON><PERSON><PERSON></a>, Ugandan general and politician (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aronda_Nyakairima"}]}, {"year": "2017", "text": "<PERSON>, Canadian economist and politician, Deputy Prime Minister of Canada (b. 1921)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "2017", "text": "<PERSON>, American LGBT rights activist and technology manager at IBM (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist and technology manager at IBM (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American LGBT rights activist and technology manager at IBM (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Taiwanese academic (b. 1932)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>han\" title=\"<PERSON>han\"><PERSON></a>, Taiwanese academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>han\" title=\"<PERSON>han\"><PERSON></a>, Taiwanese academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>han"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Tongan politician and activist, Prime Minister of Tonga (b. 1941)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/%CA%BBAkilisi_P%C5%8Dhiva\" title=\"ʻ<PERSON><PERSON><PERSON><PERSON> Pōhiva\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and activist, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tonga\" title=\"Prime Minister of Tonga\">Prime Minister of Tonga</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%CA%BBAkilisi_P%C5%8Dhiva\" title=\"ʻAki<PERSON><PERSON> Pōhiva\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and activist, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tonga\" title=\"Prime Minister of Tonga\">Prime Minister of Tonga</a> (b. 1941)", "links": [{"title": "ʻA<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%CA%BBAkilisi_P%C5%8Dhiva"}, {"title": "Prime Minister of Tonga", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tonga"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Indian politician and leader of CPI(M) (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Sitaram_<PERSON>\" title=\"<PERSON>ara<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and leader of <a href=\"https://wikipedia.org/wiki/CPI(M)\" class=\"mw-redirect\" title=\"CPI(M)\">CPI(M)</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sitara<PERSON>_<PERSON>\" title=\"<PERSON>ara<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and leader of <a href=\"https://wikipedia.org/wiki/CPI(M)\" class=\"mw-redirect\" title=\"CPI(M)\">CPI(M)</a> (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitaram_<PERSON>chury"}, {"title": "CPI(M)", "link": "https://wikipedia.org/wiki/CPI(M)"}]}]}}