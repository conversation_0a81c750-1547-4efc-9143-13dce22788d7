{"date": "August 8", "url": "https://wikipedia.org/wiki/August_8", "data": {"Events": [{"year": "685 BC", "text": "Spring and Autumn period: Battle of Qianshi: Upon the death of the previous Duke of Qi, <PERSON><PERSON>, Duke <PERSON><PERSON> of Lu sends an army into the Duchy of Qi to install the exiled Qi prince <PERSON><PERSON> as the new Duke of Qi - but is defeated at Qianshi by <PERSON><PERSON>'s brother and rival claimant, the newly inaugurated Duke <PERSON><PERSON> of Qi.", "html": "685 BC - 685 BC - <a href=\"https://wikipedia.org/wiki/Spring_and_Autumn_period\" title=\"Spring and Autumn period\">Spring and Autumn period</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Qianshi\" title=\"Battle of Qianshi\">Battle of Qianshi</a>: Upon the death of the previous Duke of <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Qi</a>, <a href=\"https://wikipedia.org/wiki/Wuzhi_(Qi)\" title=\"Wuzhi (Qi)\"><PERSON><PERSON></a>, Duke <PERSON> of <a href=\"https://wikipedia.org/wiki/Lu_(state)\" title=\"Lu (state)\">Lu</a> sends an army into the <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Duchy of Qi</a> to install the exiled <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Qi</a> prince <PERSON><PERSON> as the new Duke of Qi - but is defeated at Qianshi by <PERSON><PERSON>'s brother and rival claimant, the newly inaugurated <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>an_of_Qi\" title=\"Duke <PERSON>an of Qi\">Duke <PERSON>an of <PERSON></a>.", "no_year_html": "685 BC - <a href=\"https://wikipedia.org/wiki/Spring_and_Autumn_period\" title=\"Spring and Autumn period\">Spring and Autumn period</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Qianshi\" title=\"Battle of Qianshi\">Battle of Qianshi</a>: Upon the death of the previous Duke of <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Qi</a>, <a href=\"https://wikipedia.org/wiki/Wuzhi_(Qi)\" title=\"Wuzhi (Qi)\"><PERSON><PERSON></a>, Duke <PERSON> of <a href=\"https://wikipedia.org/wiki/Lu_(state)\" title=\"Lu (state)\">Lu</a> sends an army into the <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Duchy of Qi</a> to install the exiled <a href=\"https://wikipedia.org/wiki/Qi_(state)\" title=\"Qi (state)\">Qi</a> prince <PERSON><PERSON> as the new Duke of Qi - but is defeated at Qianshi by <PERSON><PERSON>'s brother and rival claimant, the newly inaugurated <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>an_of_Qi\" title=\"Duke Huan of Qi\">Duke <PERSON>an of Qi</a>.", "links": [{"title": "Spring and Autumn period", "link": "https://wikipedia.org/wiki/Spring_and_Autumn_period"}, {"title": "Battle of Qianshi", "link": "https://wikipedia.org/wiki/Battle_of_Qianshi"}, {"title": "Qi (state)", "link": "https://wikipedia.org/wiki/Qi_(state)"}, {"title": "<PERSON><PERSON> (Qi)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Qi)"}, {"title": "Lu (state)", "link": "https://wikipedia.org/wiki/Lu_(state)"}, {"title": "Qi (state)", "link": "https://wikipedia.org/wiki/Qi_(state)"}, {"title": "Qi (state)", "link": "https://wikipedia.org/wiki/Qi_(state)"}, {"title": "Duke <PERSON><PERSON> of Qi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Qi"}]}, {"year": "870", "text": "Treaty of Meerssen: King <PERSON> the German and his half-brother <PERSON> the Bald partition the Middle Frankish Kingdom into two larger east and west divisions.", "html": "870 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Meerssen\" title=\"Treaty of Meerssen\">Treaty of Meerssen</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_German\" title=\"<PERSON> the German\"><PERSON> the German</a> and his half-brother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> partition the <a href=\"https://wikipedia.org/wiki/Middle_Francia\" title=\"Middle Francia\">Middle Frankish Kingdom</a> into two larger east and west divisions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Meerssen\" title=\"Treaty of Meerssen\">Treaty of Meerssen</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>\" title=\"<PERSON> the German\"><PERSON> the German</a> and his half-brother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a> partition the <a href=\"https://wikipedia.org/wiki/Middle_Francia\" title=\"Middle Francia\">Middle Frankish Kingdom</a> into two larger east and west divisions.", "links": [{"title": "Treaty of Meerssen", "link": "https://wikipedia.org/wiki/Treaty_of_<PERSON><PERSON>sen"}, {"title": "<PERSON> the German", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_German"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Middle Francia", "link": "https://wikipedia.org/wiki/Middle_Francia"}]}, {"year": "1220", "text": "Sweden is defeated by Estonian tribes in the Battle of Lihula.", "html": "1220 - Sweden is defeated by <a href=\"https://wikipedia.org/wiki/Estonians\" title=\"Estonians\">Estonian</a> tribes in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lihula\" title=\"Battle of Lihula\">Battle of Lihula</a>.", "no_year_html": "Sweden is defeated by <a href=\"https://wikipedia.org/wiki/Estonians\" title=\"Estonians\">Estonian</a> tribes in the <a href=\"https://wikipedia.org/wiki/Battle_of_Lihula\" title=\"Battle of Lihula\">Battle of Lihula</a>.", "links": [{"title": "Estonians", "link": "https://wikipedia.org/wiki/Estonians"}, {"title": "Battle of Lihula", "link": "https://wikipedia.org/wiki/Battle_of_Lihula"}]}, {"year": "1264", "text": "Mudéjar revolt: Muslim rebel forces took the Alcázar of Jerez de la Frontera after defeating the Castilian garrison.", "html": "1264 - <a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%9366\" class=\"mw-redirect\" title=\"Mudéjar revolt of 1264-66\">Mudéjar revolt</a>: Muslim rebel forces took the <a href=\"https://wikipedia.org/wiki/Alc%C3%A1zar_of_Jerez_de_la_Frontera\" title=\"<PERSON><PERSON><PERSON><PERSON> of Jerez de la Frontera\"><PERSON><PERSON><PERSON><PERSON> of Jerez de la Frontera</a> after defeating the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> garrison.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%9366\" class=\"mw-redirect\" title=\"Mudéjar revolt of 1264-66\">Mudéjar revolt</a>: Muslim rebel forces took the <a href=\"https://wikipedia.org/wiki/Alc%C3%A1zar_of_Jerez_de_la_Frontera\" title=\"<PERSON><PERSON><PERSON><PERSON> of Jerez de la Frontera\"><PERSON><PERSON><PERSON><PERSON> of Jerez de la Frontera</a> after defeating the <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> garrison.", "links": [{"title": "Mudéjar revolt of 1264-66", "link": "https://wikipedia.org/wiki/Mud%C3%A9jar_revolt_of_1264%E2%80%9366"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Jerez de la Frontera", "link": "https://wikipedia.org/wiki/Alc%C3%<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_Frontera"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}]}, {"year": "1503", "text": "King <PERSON> of Scotland marries <PERSON>, daughter of King <PERSON> of England at Holyrood Abbey in Edinburgh, Scotland.", "html": "1503 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> of Scotland</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> VII of England</a> at <a href=\"https://wikipedia.org/wiki/Holyrood_Abbey\" title=\"Holyrood Abbey\">Holyrood Abbey</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>, Scotland.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> IV of Scotland\"><PERSON> of Scotland</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, daughter of King <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\"><PERSON> of England</a> at <a href=\"https://wikipedia.org/wiki/Holyrood_Abbey\" title=\"Holyrood Abbey\">Holyrood Abbey</a> in <a href=\"https://wikipedia.org/wiki/Edinburgh\" title=\"Edinburgh\">Edinburgh</a>, Scotland.", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Holyrood Abbey", "link": "https://wikipedia.org/wiki/Holyrood_Abbey"}, {"title": "Edinburgh", "link": "https://wikipedia.org/wiki/Edinburgh"}]}, {"year": "1509", "text": "<PERSON><PERSON><PERSON> is crowned Emperor of Vijayanagara at Chittoor.", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON>de<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is crowned Emperor of <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara</a> at <a href=\"https://wikipedia.org/wiki/Chittoor,_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chittoor, Andhra Pradesh\">Chittoor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is crowned Emperor of <a href=\"https://wikipedia.org/wiki/Vijayanagara_Empire\" title=\"Vijayanagara Empire\">Vijayanagara</a> at <a href=\"https://wikipedia.org/wiki/Chittoor,_Andhra_Pradesh\" class=\"mw-redirect\" title=\"Chittoor, Andhra Pradesh\">Chittoor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Raya"}, {"title": "Vijayanagara Empire", "link": "https://wikipedia.org/wiki/Vijayanagara_Empire"}, {"title": "Chittoor, Andhra Pradesh", "link": "https://wikipedia.org/wiki/Chittoor,_Andhra_Pradesh"}]}, {"year": "1576", "text": "The cornerstone for <PERSON><PERSON>e's Uraniborg observatory is laid on the island of Hven.", "html": "1576 - The cornerstone for <a href=\"https://wikipedia.org/wiki/Tycho_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON> B<PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Uraniborg\" title=\"Uraniborg\">Uraniborg</a> observatory is laid on the island of <a href=\"https://wikipedia.org/wiki/Hven\" class=\"mw-redirect\" title=\"Hven\">Hven</a>.", "no_year_html": "The cornerstone for <a href=\"https://wikipedia.org/wiki/Tycho_Brahe\" title=\"Tycho Brahe\"><PERSON><PERSON> Brah<PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Uraniborg\" title=\"Uraniborg\">Uraniborg</a> observatory is laid on the island of <a href=\"https://wikipedia.org/wiki/Hven\" class=\"mw-redirect\" title=\"Hven\">Hven</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ty<PERSON>_<PERSON>e"}, {"title": "Uraniborg", "link": "https://wikipedia.org/wiki/Uraniborg"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hven"}]}, {"year": "1585", "text": "<PERSON> enters Cumberland Sound in search of the Northwest Passage.", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(English_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (English explorer)\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Cumberland_Sound\" title=\"Cumberland Sound\">Cumberland Sound</a> in search of the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(English_explorer)\" class=\"mw-redirect\" title=\"<PERSON> (English explorer)\"><PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Cumberland_Sound\" title=\"Cumberland Sound\">Cumberland Sound</a> in search of the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a>.", "links": [{"title": "<PERSON> (English explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_explorer)"}, {"title": "Cumberland Sound", "link": "https://wikipedia.org/wiki/Cumberland_Sound"}, {"title": "Northwest Passage", "link": "https://wikipedia.org/wiki/Northwest_Passage"}]}, {"year": "1588", "text": "Anglo-Spanish War: Battle of Gravelines: The naval engagement ends, ending the Spanish Armada's attempt to invade England.", "html": "1588 - <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines\" title=\"Spanish Armada\">Battle of Gravelines</a>: The naval engagement ends, ending the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a>'s attempt to invade <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines\" title=\"Spanish Armada\">Battle of Gravelines</a>: The naval engagement ends, ending the <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a>'s attempt to invade <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "links": [{"title": "Anglo-Spanish War (1585-1604)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)"}, {"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada#Battle_of_Gravelines"}, {"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}, {"year": "1647", "text": "The Irish Confederate Wars and Wars of the Three Kingdoms: Battle of Dungan's Hill: English Parliamentary forces defeat Irish forces.", "html": "1647 - The <a href=\"https://wikipedia.org/wiki/Irish_Confederate_Wars\" title=\"Irish Confederate Wars\">Irish Confederate Wars</a> and <a href=\"https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms\" title=\"Wars of the Three Kingdoms\">Wars of the Three Kingdoms</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Dungan%27s_Hill\" title=\"Battle of Dungan's Hill\">Battle of Dungan's Hill</a>: <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliamentary</a> forces defeat Irish forces.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Irish_Confederate_Wars\" title=\"Irish Confederate Wars\">Irish Confederate Wars</a> and <a href=\"https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms\" title=\"Wars of the Three Kingdoms\">Wars of the Three Kingdoms</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Dungan%27s_Hill\" title=\"Battle of Dungan's Hill\">Battle of Dungan's Hill</a>: <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">English Parliamentary</a> forces defeat Irish forces.", "links": [{"title": "Irish Confederate Wars", "link": "https://wikipedia.org/wiki/Irish_Confederate_Wars"}, {"title": "Wars of the Three Kingdoms", "link": "https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms"}, {"title": "Battle of Dungan's Hill", "link": "https://wikipedia.org/wiki/Battle_of_Dungan%27s_Hill"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}]}, {"year": "1648", "text": "<PERSON><PERSON><PERSON> <PERSON> (1648-1687) succeeds <PERSON> (1640-1648) as Ottoman sultan.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"Mehmed IV\"><PERSON><PERSON><PERSON> IV</a> (1648-1687) succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\"><PERSON> I</a> (1640-1648) as <a href=\"https://wikipedia.org/wiki/Ottoman_sultan\" class=\"mw-redirect\" title=\"Ottoman sultan\">Ottoman sultan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"Mehmed IV\"><PERSON><PERSON><PERSON> IV</a> (1648-1687) succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire\" title=\"Ibrahim of the Ottoman Empire\"><PERSON> I</a> (1640-1648) as <a href=\"https://wikipedia.org/wiki/Ottoman_sultan\" class=\"mw-redirect\" title=\"Ottoman sultan\">Ottoman sultan</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV"}, {"title": "<PERSON> of the Ottoman Empire", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Ottoman_Empire"}, {"title": "Ottoman sultan", "link": "https://wikipedia.org/wiki/Ottoman_sultan"}]}, {"year": "1709", "text": "<PERSON><PERSON><PERSON><PERSON> demonstrates the lifting power of hot air in an audience before the king of Portugal in Lisbon, Portugal.", "html": "1709 - <a href=\"https://wikipedia.org/wiki/Bart<PERSON><PERSON>u_de_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON> Gusmão\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> demonstrates the lifting power of hot air in an audience before the king of Portugal in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>, Portugal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bart<PERSON><PERSON><PERSON>_de_Gusm%C3%A3o\" title=\"<PERSON><PERSON><PERSON><PERSON> Gusmão\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> demonstrates the lifting power of hot air in an audience before the king of Portugal in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>, Portugal.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bart<PERSON>meu_de_Gusm%C3%A3o"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}]}, {"year": "1786", "text": "Mont Blanc on the French-Italian border is climbed for the first time by <PERSON> and Dr. <PERSON><PERSON><PERSON>.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Blanc\" title=\"Mont Blanc\">Mont Blanc</a> on the French-Italian border is climbed for the first time by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mont Blanc\">Mont Blanc</a> on the French-Italian border is climbed for the first time by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Mont Blanc", "link": "https://wikipedia.org/wiki/Mont_Blanc"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1794", "text": "<PERSON> leads an expedition to search for the Northwest Passage near Juneau, Alaska.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an expedition to search for the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a> near <a href=\"https://wikipedia.org/wiki/Juneau,_Alaska\" title=\"Juneau, Alaska\">Juneau, Alaska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads an expedition to search for the <a href=\"https://wikipedia.org/wiki/Northwest_Passage\" title=\"Northwest Passage\">Northwest Passage</a> near <a href=\"https://wikipedia.org/wiki/Juneau,_Alaska\" title=\"Juneau, Alaska\">Juneau, Alaska</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Northwest Passage", "link": "https://wikipedia.org/wiki/Northwest_Passage"}, {"title": "Juneau, Alaska", "link": "https://wikipedia.org/wiki/Juneau,_Alaska"}]}, {"year": "1831", "text": "Four hundred Shawnee people agree to relinquish their lands in Ohio in exchange for land west of the Mississippi River in the Treaty of Wapakoneta.", "html": "1831 - Four hundred <a href=\"https://wikipedia.org/wiki/Shawnee\" title=\"Shawnee\">Shawnee</a> people agree to relinquish their lands in <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> in exchange for land west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Wapakoneta\" title=\"Treaty of Wapakoneta\">Treaty of Wapakoneta</a>.", "no_year_html": "Four hundred <a href=\"https://wikipedia.org/wiki/Shawnee\" title=\"Shawnee\">Shawnee</a> people agree to relinquish their lands in <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a> in exchange for land west of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Wapakoneta\" title=\"Treaty of Wapakoneta\">Treaty of Wapakoneta</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Treaty of Wapakoneta", "link": "https://wikipedia.org/wiki/Treaty_of_Wapakoneta"}]}, {"year": "1844", "text": "The Quorum of the Twelve Apostles, headed by <PERSON>, is reaffirmed as the leading body of the Church of Jesus Christ of Latter-day Saints (LDS Church).", "html": "1844 - The <a href=\"https://wikipedia.org/wiki/Quorum_of_the_Twelve\" title=\"Quorum of the Twelve\">Quorum of the Twelve</a> Apostles, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is reaffirmed as the leading body of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a> (LDS Church).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Quorum_of_the_Twelve\" title=\"Quorum of the Twelve\">Quorum of the Twelve</a> Apostles, headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is reaffirmed as the leading body of <a href=\"https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints\" title=\"The Church of Jesus Christ of Latter-day Saints\">the Church of Jesus Christ of Latter-day Saints</a> (LDS Church).", "links": [{"title": "Quorum of the Twelve", "link": "https://wikipedia.org/wiki/Quorum_of_the_Twelve"}, {"title": "<PERSON> Young", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}, {"title": "The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1863", "text": "American Civil War: Following his defeat in the Battle of Gettysburg, General <PERSON> sends a letter of resignation to Confederate President <PERSON> (which is refused upon receipt).", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Following his defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends a letter of resignation to <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (which is refused upon receipt).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: Following his defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sends a letter of resignation to <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jefferson <PERSON>\"><PERSON></a> (which is refused upon receipt).", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "Tennessee Military Governor <PERSON> frees his personal slaves in Greeneville, Tennessee despite them being exempt from the Emancipation Proclamation, now commemorated as Emancipation Day in the state.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> Military Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> frees his <a href=\"https://wikipedia.org/wiki/<PERSON>_and_slavery\" title=\"<PERSON> and slavery\">personal slaves</a> in <a href=\"https://wikipedia.org/wiki/Greeneville,_Tennessee\" title=\"Greeneville, Tennessee\">Greeneville, Tennessee</a> despite them being exempt from the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>, now commemorated as <a href=\"https://wikipedia.org/wiki/Emancipation_Day#Kentucky_and_Tennessee_-_8_August\" title=\"Emancipation Day\">Emancipation Day</a> in the state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> Military Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> frees his <a href=\"https://wikipedia.org/wiki/<PERSON>_and_slavery\" title=\"<PERSON> and slavery\">personal slaves</a> in <a href=\"https://wikipedia.org/wiki/Greeneville,_Tennessee\" title=\"Greeneville, Tennessee\">Greeneville, Tennessee</a> despite them being exempt from the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>, now commemorated as <a href=\"https://wikipedia.org/wiki/Emancipation_Day#Kentucky_and_Tennessee_-_8_August\" title=\"Emancipation Day\">Emancipation Day</a> in the state.", "links": [{"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> and slavery", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>"}, {"title": "Greeneville, Tennessee", "link": "https://wikipedia.org/wiki/Greeneville,_Tennessee"}, {"title": "Emancipation Proclamation", "link": "https://wikipedia.org/wiki/Emancipation_Proclamation"}, {"title": "Emancipation Day", "link": "https://wikipedia.org/wiki/Emancipation_Day#Kentucky_and_Tennessee_-_8_August"}]}, {"year": "1870", "text": "The Republic of Ploiești, a failed Radical-Liberal rising against <PERSON><PERSON><PERSON> of Romania.", "html": "1870 - The <i><a href=\"https://wikipedia.org/wiki/Republic_of_Ploie%C8%99ti\" title=\"Republic of Ploiești\">Republic of Ploiești</a></i>, a failed <a href=\"https://wikipedia.org/wiki/Liberalism_and_radicalism_in_Romania\" title=\"Liberalism and radicalism in Romania\">Radical-Liberal</a> rising against <a href=\"https://wikipedia.org/wiki/Domnitor\" title=\"Domnitor\">Domnitor</a> <a href=\"https://wikipedia.org/wiki/Carol_I_of_Romania\" title=\"<PERSON> of Romania\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Republic_of_Ploie%C8%99ti\" title=\"Republic of Ploiești\">Republic of Ploiești</a></i>, a failed <a href=\"https://wikipedia.org/wiki/Liberalism_and_radicalism_in_Romania\" title=\"Liberalism and radicalism in Romania\">Radical-Liberal</a> rising against <a href=\"https://wikipedia.org/wiki/Domnitor\" title=\"Domnitor\">Domnitor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_Romania\" title=\"<PERSON> of Romania\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>.", "links": [{"title": "Republic of Ploiești", "link": "https://wikipedia.org/wiki/Republic_of_Ploie%C8%99ti"}, {"title": "Liberalism and radicalism in Romania", "link": "https://wikipedia.org/wiki/Liberalism_and_radicalism_in_Romania"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Domnitor"}, {"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Romania"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1876", "text": "Thomas Edison receives a patent for his mimeograph.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> receives a patent for his <a href=\"https://wikipedia.org/wiki/Mimeograph\" title=\"Mimeograph\">mimeograph</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_Edison\" title=\"Thomas Edison\"><PERSON></a> receives a patent for his <a href=\"https://wikipedia.org/wiki/Mimeograph\" title=\"Mimeograph\">mimeograph</a>.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mimeograph", "link": "https://wikipedia.org/wiki/Mimeograph"}]}, {"year": "1897", "text": "Italian anarchist <PERSON> assassinates Spanish Prime Minister <PERSON>", "html": "1897 - Italian anarchist <PERSON> <a href=\"https://wikipedia.org/wiki/Assassination_of_Antonio_C%C3%A1nova<PERSON>_<PERSON>_Castillo\" title=\"Assassination of <PERSON>\">assassinates Spanish Prime Minister <PERSON></a>", "no_year_html": "Italian anarchist <PERSON> <a href=\"https://wikipedia.org/wiki/Assassination_of_Antonio_C%C3%A1<PERSON><PERSON>_<PERSON>_Castillo\" title=\"Assassination of <PERSON>\">assassinates Spanish Prime Minister <PERSON></a>", "links": [{"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_Antonio_C%C3%A1novas_del_Castillo"}]}, {"year": "1903", "text": "Black Saturday occurs, killing 12 in a stadium collapse in Philadelphia.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Black_Saturday_(1903)\" title=\"Black Saturday (1903)\">Black Saturday</a> occurs, killing 12 in a stadium collapse in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Saturday_(1903)\" title=\"Black Saturday (1903)\">Black Saturday</a> occurs, killing 12 in a stadium collapse in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "Black Saturday (1903)", "link": "https://wikipedia.org/wiki/Black_Saturday_(1903)"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON> makes his first flight at a racecourse at Le Mans, France. It is the Wright Brothers' first public flight.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\">W<PERSON><PERSON></a> makes his first flight at a racecourse at <a href=\"https://wikipedia.org/wiki/Le_Mans\" title=\"Le Mans\">Le Mans</a>, France. It is the Wright Brothers' first public flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\">W<PERSON><PERSON></a> makes his first flight at a racecourse at <a href=\"https://wikipedia.org/wiki/Le_Mans\" title=\"Le Mans\">Le Mans</a>, France. It is the Wright Brothers' first public flight.", "links": [{"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Le Mans", "link": "https://wikipedia.org/wiki/<PERSON>_Mans"}]}, {"year": "1918", "text": "World War I: The Battle of Amiens begins a string of almost continuous Allied victories with a push through the German front lines (Hundred Days Offensive).", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Amiens_(1918)\" title=\"Battle of Amiens (1918)\">Battle of Amiens</a> begins a string of almost continuous Allied victories with a push through the German front lines (<a href=\"https://wikipedia.org/wiki/Hundred_Days_Offensive\" title=\"Hundred Days Offensive\">Hundred Days Offensive</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Amiens_(1918)\" title=\"Battle of Amiens (1918)\">Battle of Amiens</a> begins a string of almost continuous Allied victories with a push through the German front lines (<a href=\"https://wikipedia.org/wiki/Hundred_Days_Offensive\" title=\"Hundred Days Offensive\">Hundred Days Offensive</a>).", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Amiens (1918)", "link": "https://wikipedia.org/wiki/Battle_of_Amiens_(1918)"}, {"title": "Hundred Days Offensive", "link": "https://wikipedia.org/wiki/Hundred_Days_Offensive"}]}, {"year": "1919", "text": "The Anglo-Afghan Treaty of 1919 is signed. It establishes peaceful relations between Afghanistan and the UK, and confirms the Durand line as the mutual border. In return, the UK is no longer obligated to subsidize the Afghan government.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Anglo-Afghan_Treaty_of_1919\" title=\"Anglo-Afghan Treaty of 1919\">Anglo-Afghan Treaty of 1919</a> is signed. It establishes peaceful relations between Afghanistan and the UK, and confirms the <a href=\"https://wikipedia.org/wiki/Durand_line\" class=\"mw-redirect\" title=\"Durand line\">Durand line</a> as the mutual border. In return, the UK is no longer obligated to subsidize the Afghan government.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Afghan_Treaty_of_1919\" title=\"Anglo-Afghan Treaty of 1919\">Anglo-Afghan Treaty of 1919</a> is signed. It establishes peaceful relations between Afghanistan and the UK, and confirms the <a href=\"https://wikipedia.org/wiki/Durand_line\" class=\"mw-redirect\" title=\"Durand line\">Durand line</a> as the mutual border. In return, the UK is no longer obligated to subsidize the Afghan government.", "links": [{"title": "Anglo-Afghan Treaty of 1919", "link": "https://wikipedia.org/wiki/Anglo-Afghan_Treaty_of_1919"}, {"title": "Durand line", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_line"}]}, {"year": "1929", "text": "The German airship Graf Zeppelin begins a round-the-world flight.", "html": "1929 - The German airship <i><a href=\"https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin\" title=\"LZ 127 Graf Zeppelin\">Graf Zeppelin</a></i> begins a round-the-world flight.", "no_year_html": "The German airship <i><a href=\"https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin\" title=\"LZ 127 Graf Zeppelin\">Graf Zeppelin</a></i> begins a round-the-world flight.", "links": [{"title": "LZ 127 Graf Zeppelin", "link": "https://wikipedia.org/wiki/LZ_127_Graf_Zeppelin"}]}, {"year": "1940", "text": "The \"Aufbau Ost\" directive is signed by <PERSON>.", "html": "1940 - The \"<a href=\"https://wikipedia.org/wiki/Aufbau_Ost_(1940)\" title=\"Aufbau Ost (1940)\">Aufbau Ost</a>\" directive is signed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/Aufbau_Ost_(1940)\" title=\"Aufbau Ost (1940)\">Aufbau Ost</a>\" directive is signed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Auf<PERSON><PERSON> Ost (1940)", "link": "https://wikipedia.org/wiki/Aufbau_Ost_(1940)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1942", "text": "Quit India Movement is launched in India against the British rule in response to <PERSON><PERSON>'s call for swaraj or complete independence.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Quit_India_Movement\" title=\"Quit India Movement\">Quit India Movement</a> is launched in India against the British rule in response to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s call for <a href=\"https://wikipedia.org/wiki/Swaraj\" title=\"Swaraj\">swaraj</a> or complete independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quit_India_Movement\" title=\"Quit India Movement\">Quit India Movement</a> is launched in India against the British rule in response to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s call for <a href=\"https://wikipedia.org/wiki/Swaraj\" title=\"Swaraj\">swaraj</a> or complete independence.", "links": [{"title": "Quit India Movement", "link": "https://wikipedia.org/wiki/Quit_India_Movement"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Swaraj"}]}, {"year": "1945", "text": "The London Charter is signed by France, the United Kingdom, the Soviet Union and the United States, establishing the laws and procedures for the Nuremberg trials.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Nuremberg_Charter\" class=\"mw-redirect\" title=\"Nuremberg Charter\">London Charter</a> is signed by France, the United Kingdom, the Soviet Union and the United States, establishing the laws and procedures for the <a href=\"https://wikipedia.org/wiki/Nuremberg_trials\" title=\"Nuremberg trials\">Nuremberg trials</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nuremberg_Charter\" class=\"mw-redirect\" title=\"Nuremberg Charter\">London Charter</a> is signed by France, the United Kingdom, the Soviet Union and the United States, establishing the laws and procedures for the <a href=\"https://wikipedia.org/wiki/Nuremberg_trials\" title=\"Nuremberg trials\">Nuremberg trials</a>.", "links": [{"title": "Nuremberg Charter", "link": "https://wikipedia.org/wiki/Nuremberg_Charter"}, {"title": "Nuremberg trials", "link": "https://wikipedia.org/wiki/Nuremberg_trials"}]}, {"year": "1946", "text": "First flight of the nuclear capable Convair B-36, heaviest mass-produced piston-engined aircraft at the time.", "html": "1946 - First flight of the nuclear capable <a href=\"https://wikipedia.org/wiki/Convair_B-36\" class=\"mw-redirect\" title=\"Convair B-36\">Convair B-36</a>, heaviest mass-produced piston-engined aircraft at the time.", "no_year_html": "First flight of the nuclear capable <a href=\"https://wikipedia.org/wiki/Convair_B-36\" class=\"mw-redirect\" title=\"Convair B-36\">Convair B-36</a>, heaviest mass-produced piston-engined aircraft at the time.", "links": [{"title": "Convair B-36", "link": "https://wikipedia.org/wiki/Convair_B-36"}]}, {"year": "1956", "text": "Marcinelle mining disaster in Belgium. 262 coal miners, including a substantial number of Italian migrant workers, were killed in one of the largest mining accidents in Belgian history.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Marcinelle_mining_disaster\" title=\"Marcinelle mining disaster\">Marcinelle mining disaster</a> in Belgium. 262 coal miners, including a substantial number of Italian migrant workers, were killed in one of the largest mining accidents in Belgian history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marcinelle_mining_disaster\" title=\"Marcinelle mining disaster\">Marcinelle mining disaster</a> in Belgium. 262 coal miners, including a substantial number of Italian migrant workers, were killed in one of the largest mining accidents in Belgian history.", "links": [{"title": "Marcinelle mining disaster", "link": "https://wikipedia.org/wiki/Marc<PERSON>lle_mining_disaster"}]}, {"year": "1963", "text": "Great Train Robbery: In England, a gang of 15 train robbers steal £2.6 million in bank notes.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Great_Train_Robbery_(1963)\" title=\"Great Train Robbery (1963)\">Great Train Robbery</a>: In England, a gang of 15 <a href=\"https://wikipedia.org/wiki/Train_robbery\" title=\"Train robbery\">train robbers</a> steal £2.6 million in bank notes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Train_Robbery_(1963)\" title=\"Great Train Robbery (1963)\">Great Train Robbery</a>: In England, a gang of 15 <a href=\"https://wikipedia.org/wiki/Train_robbery\" title=\"Train robbery\">train robbers</a> steal £2.6 million in bank notes.", "links": [{"title": "Great Train Robbery (1963)", "link": "https://wikipedia.org/wiki/Great_Train_Robbery_(1963)"}, {"title": "Train robbery", "link": "https://wikipedia.org/wiki/Train_robbery"}]}, {"year": "1963", "text": "The Zimbabwe African National Union (ZANU), the current ruling party of Zimbabwe, is formed by a split from the Zimbabwe African People's Union.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_National_Union\" title=\"Zimbabwe African National Union\">Zimbabwe African National Union</a> (ZANU), the current ruling party of Zimbabwe, is formed by a split from the <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union\" title=\"Zimbabwe African People's Union\">Zimbabwe African People's Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_National_Union\" title=\"Zimbabwe African National Union\">Zimbabwe African National Union</a> (ZANU), the current ruling party of Zimbabwe, is formed by a split from the <a href=\"https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union\" title=\"Zimbabwe African People's Union\">Zimbabwe African People's Union</a>.", "links": [{"title": "Zimbabwe African National Union", "link": "https://wikipedia.org/wiki/Zimbabwe_African_National_Union"}, {"title": "Zimbabwe African People's Union", "link": "https://wikipedia.org/wiki/Zimbabwe_African_People%27s_Union"}]}, {"year": "1967", "text": "The Association of Southeast Asian Nations (ASEAN) is founded by Indonesia, Malaysia, the Philippines, Singapore and Thailand.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations\" class=\"mw-redirect\" title=\"Association of Southeast Asian Nations\">Association of Southeast Asian Nations</a> (ASEAN) is founded by <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> and <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations\" class=\"mw-redirect\" title=\"Association of Southeast Asian Nations\">Association of Southeast Asian Nations</a> (ASEAN) is founded by <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> and <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>.", "links": [{"title": "Association of Southeast Asian Nations", "link": "https://wikipedia.org/wiki/Association_of_Southeast_Asian_Nations"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}]}, {"year": "1969", "text": "At a zebra crossing in London, photographer <PERSON> takes the iconic photo that becomes the cover image of the Beatles' album Abbey Road.", "html": "1969 - At a <a href=\"https://wikipedia.org/wiki/Zebra_crossing\" title=\"Zebra crossing\">zebra crossing</a> in London, photographer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes the iconic photo that becomes the cover image of <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">the Beatles</a>' album <i><a href=\"https://wikipedia.org/wiki/Abbey_Road\" title=\"Abbey Road\">Abbey Road</a></i>.", "no_year_html": "At a <a href=\"https://wikipedia.org/wiki/Zebra_crossing\" title=\"Zebra crossing\">zebra crossing</a> in London, photographer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes the iconic photo that becomes the cover image of <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">the Beatles</a>' album <i><a href=\"https://wikipedia.org/wiki/Abbey_Road\" title=\"Abbey Road\">Abbey Road</a></i>.", "links": [{"title": "Zebra crossing", "link": "https://wikipedia.org/wiki/Zebra_crossing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Abbey Road", "link": "https://wikipedia.org/wiki/Abbey_Road"}]}, {"year": "1973", "text": "<PERSON>, a South Korean politician and later president of South Korea, is kidnapped.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a South Korean politician and later president of South Korea, is <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON>_<PERSON>-<PERSON>ung\" title=\"Kidnapping of <PERSON>\">kidnapped</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a South Korean politician and later president of South Korea, is <a href=\"https://wikipedia.org/wiki/Kidnapping_of_<PERSON>_<PERSON>-<PERSON>ung\" title=\"Kidnapping of <PERSON>\">kidnapped</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}, {"title": "Kidnapping of <PERSON>", "link": "https://wikipedia.org/wiki/Kidnapping_of_<PERSON>_<PERSON>-jung"}]}, {"year": "1974", "text": "President <PERSON>, in a nationwide television address, announces his resignation from the office of the President of the United States effective noon the next day.", "html": "1974 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a nationwide television address, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_resignation_speech\" title=\"<PERSON>'s resignation speech\">announces his resignation</a> from the office of the <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> effective noon the next day.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a nationwide television address, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_resignation_speech\" title=\"<PERSON>'s resignation speech\">announces his resignation</a> from the office of the <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> effective noon the next day.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s resignation speech", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_resignation_speech"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1988", "text": "The 8888 Uprising begins in Rangoon (Yangon), Burma (Myanmar). Led by students, hundreds of thousands join in nationwide protests against the one-party regime. On September 18, the demonstrations end in a military crackdown, killing thousands.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/8888_Uprising\" title=\"8888 Uprising\">8888 Uprising</a> begins in <a href=\"https://wikipedia.org/wiki/Yangon\" title=\"Yangon\">Rangoon (Yangon)</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma (Myanmar)</a>. Led by students, hundreds of thousands join in nationwide protests against the one-party regime. On <a href=\"https://wikipedia.org/wiki/September_18\" title=\"September 18\">September 18</a>, the <a href=\"https://wikipedia.org/wiki/Demonstration_(protest)\" class=\"mw-redirect\" title=\"Demonstration (protest)\">demonstrations</a> end in a military crackdown, killing thousands.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/8888_Uprising\" title=\"8888 Uprising\">8888 Uprising</a> begins in <a href=\"https://wikipedia.org/wiki/Yangon\" title=\"Yangon\">Rangoon (Yangon)</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burma (Myanmar)</a>. Led by students, hundreds of thousands join in nationwide protests against the one-party regime. On <a href=\"https://wikipedia.org/wiki/September_18\" title=\"September 18\">September 18</a>, the <a href=\"https://wikipedia.org/wiki/Demonstration_(protest)\" class=\"mw-redirect\" title=\"Demonstration (protest)\">demonstrations</a> end in a military crackdown, killing thousands.", "links": [{"title": "8888 Uprising", "link": "https://wikipedia.org/wiki/8888_Uprising"}, {"title": "Yangon", "link": "https://wikipedia.org/wiki/Yangon"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "September 18", "link": "https://wikipedia.org/wiki/September_18"}, {"title": "Demonstration (protest)", "link": "https://wikipedia.org/wiki/Demonstration_(protest)"}]}, {"year": "1988", "text": "The first night baseball game in the history of Chicago's Wrigley Field (game was rained out in the fourth inning).", "html": "1988 - <a href=\"https://wikipedia.org/wiki/History_of_Wrigley_Field#Night_baseball_(1988)\" title=\"History of Wrigley Field\">The first night baseball game in the history of Chicago's Wrigley Field</a> (game was rained out in the fourth inning).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_Wrigley_Field#Night_baseball_(1988)\" title=\"History of Wrigley Field\">The first night baseball game in the history of Chicago's Wrigley Field</a> (game was rained out in the fourth inning).", "links": [{"title": "History of Wrigley Field", "link": "https://wikipedia.org/wiki/History_of_Wrigley_Field#Night_baseball_(1988)"}]}, {"year": "1989", "text": "Space Shuttle program: STS-28 Mission: Space Shuttle Columbia takes off on a secret five-day military mission.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-28\" title=\"STS-28\">STS-28</a> Mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> takes off on a secret five-day military mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-28\" title=\"STS-28\">STS-28</a> Mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> takes off on a secret five-day military mission.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-28", "link": "https://wikipedia.org/wiki/STS-28"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}]}, {"year": "1990", "text": "Iraq occupies Kuwait and the state is annexed to Iraq. This would lead to the Gulf War shortly afterward.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> occupies <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> and the state is annexed to Iraq. This would lead to the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a> shortly afterward.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> occupies <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> and the state is annexed to Iraq. This would lead to the <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a> shortly afterward.", "links": [{"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}, {"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}]}, {"year": "1991", "text": "The Warsaw radio mast, then the tallest construction ever built, collapses.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Warsaw_radio_mast\" title=\"Warsaw radio mast\">Warsaw radio mast</a>, then the tallest construction ever built, collapses.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Warsaw_radio_mast\" title=\"Warsaw radio mast\">Warsaw radio mast</a>, then the tallest construction ever built, collapses.", "links": [{"title": "Warsaw radio mast", "link": "https://wikipedia.org/wiki/Warsaw_radio_mast"}]}, {"year": "1993", "text": "The 7.8 Mw  Guam earthquake shakes the island with a maximum Mercalli intensity of IX (Violent), causing around $250 million in damage and injuring up to 71 people.", "html": "1993 - The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1993_Guam_earthquake\" title=\"1993 Guam earthquake\">Guam earthquake</a> shakes the island with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), causing around $250 million in damage and injuring up to 71 people.", "no_year_html": "The 7.8 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1993_Guam_earthquake\" title=\"1993 Guam earthquake\">Guam earthquake</a> shakes the island with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), causing around $250 million in damage and injuring up to 71 people.", "links": [{"title": "1993 Guam earthquake", "link": "https://wikipedia.org/wiki/1993_Guam_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1998", "text": "Iranian consulate in Mazar-i-Sharif, Afghanistan is raided by Taliban leading to the deaths of ten Iranian diplomats and a journalist.", "html": "1998 - Iranian consulate in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"<PERSON>zar<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> is <a href=\"https://wikipedia.org/wiki/1998_killing_of_Iranian_diplomats_in_Afghanistan\" title=\"1998 killing of Iranian diplomats in Afghanistan\">raided</a> by <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> leading to the deaths of ten Iranian diplomats and a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">journalist</a>.", "no_year_html": "Iranian consulate in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>\" title=\"Mazar-<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> is <a href=\"https://wikipedia.org/wiki/1998_killing_of_Iranian_diplomats_in_Afghanistan\" title=\"1998 killing of Iranian diplomats in Afghanistan\">raided</a> by <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> leading to the deaths of ten Iranian diplomats and a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">journalist</a>.", "links": [{"title": "Mazar-i-Sharif", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>-<PERSON>"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "1998 killing of Iranian diplomats in Afghanistan", "link": "https://wikipedia.org/wiki/1998_killing_of_Iranian_diplomats_in_Afghanistan"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "Confederate submarine <PERSON><PERSON><PERSON><PERSON> is raised to the surface after 136 years on the ocean floor and 30 years after its discovery by undersea explorer <PERSON><PERSON>.", "html": "2000 - Confederate submarine <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_(submarine)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> (submarine)\"><PERSON><PERSON><PERSON><PERSON></a></i> is raised to the surface after 136 years on the ocean floor and 30 years after its discovery by undersea explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>.", "no_year_html": "Confederate submarine <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(submarine)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> (submarine)\"><PERSON><PERSON><PERSON><PERSON></a></i> is raised to the surface after 136 years on the ocean floor and 30 years after its discovery by undersea explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (submarine)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(submarine)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "Albanian rebels ambush a convoy of the Army of the Republic of Macedonia near Tetovo, killing 10 soldiers.", "html": "2001 - Albanian rebels <a href=\"https://wikipedia.org/wiki/Karpalak_ambush\" title=\"Karpalak ambush\">ambush</a> a convoy of the <a href=\"https://wikipedia.org/wiki/Army_of_North_Macedonia\" title=\"Army of North Macedonia\">Army of the Republic of Macedonia</a> near <a href=\"https://wikipedia.org/wiki/Tetovo\" title=\"Tetovo\">Tetovo</a>, killing 10 soldiers.", "no_year_html": "Albanian rebels <a href=\"https://wikipedia.org/wiki/Karpalak_ambush\" title=\"Karpalak ambush\">ambush</a> a convoy of the <a href=\"https://wikipedia.org/wiki/Army_of_North_Macedonia\" title=\"Army of North Macedonia\">Army of the Republic of Macedonia</a> near <a href=\"https://wikipedia.org/wiki/Tetovo\" title=\"Tetovo\">Tetovo</a>, killing 10 soldiers.", "links": [{"title": "Karpalak ambush", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ala<PERSON>_ambush"}, {"title": "Army of North Macedonia", "link": "https://wikipedia.org/wiki/Army_of_North_Macedonia"}, {"title": "Tetovo", "link": "https://wikipedia.org/wiki/Tetovo"}]}, {"year": "2004", "text": "A tour bus belonging to the Dave Matthews Band dumps approximately 800 pounds of human waste onto a boat full of passengers.", "html": "2004 - A tour bus belonging to the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Band\" title=\"<PERSON> Band\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Chicago_River_incident\" class=\"mw-redirect\" title=\"<PERSON> Band Chicago River incident\">dumps</a> approximately 800 pounds of human waste onto a boat full of passengers.", "no_year_html": "A tour bus belonging to the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Band\" title=\"<PERSON> Band\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Chicago_River_incident\" class=\"mw-redirect\" title=\"<PERSON> Chicago River incident\">dumps</a> approximately 800 pounds of human waste onto a boat full of passengers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> Chicago River incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Band_Chicago_River_incident"}]}, {"year": "2007", "text": "An EF2 tornado touches down in Kings County and Richmond County, New York, the most powerful tornado in New York to date and the first in Brooklyn since 1889.", "html": "2007 - An EF2 <a href=\"https://wikipedia.org/wiki/2007_Brooklyn_tornadoes\" class=\"mw-redirect\" title=\"2007 Brooklyn tornadoes\">tornado</a> touches down in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Kings County</a> and <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Richmond County, New York</a>, the most powerful tornado in New York to date and the first in Brooklyn since 1889.", "no_year_html": "An EF2 <a href=\"https://wikipedia.org/wiki/2007_Brooklyn_tornadoes\" class=\"mw-redirect\" title=\"2007 Brooklyn tornadoes\">tornado</a> touches down in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Kings County</a> and <a href=\"https://wikipedia.org/wiki/Staten_Island\" title=\"Staten Island\">Richmond County, New York</a>, the most powerful tornado in New York to date and the first in Brooklyn since 1889.", "links": [{"title": "2007 Brooklyn tornadoes", "link": "https://wikipedia.org/wiki/2007_Brooklyn_tornadoes"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}, {"title": "Staten Island", "link": "https://wikipedia.org/wiki/Staten_Island"}]}, {"year": "2007", "text": "Space Shuttle program: STS-118 Mission: Endeavor takes off on a mission to the International Space Station.", "html": "2007 - Space Shuttle program: <a href=\"https://wikipedia.org/wiki/STS-118\" title=\"STS-118\">STS-118</a> Mission: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavor</a></i> takes off on a mission to the International Space Station.", "no_year_html": "Space Shuttle program: <a href=\"https://wikipedia.org/wiki/STS-118\" title=\"STS-118\">STS-118</a> Mission: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Endeavor</a></i> takes off on a mission to the International Space Station.", "links": [{"title": "STS-118", "link": "https://wikipedia.org/wiki/STS-118"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}]}, {"year": "2008", "text": "A EuroCity express train en route from Kraków, Poland to Prague, Czech Republic strikes a part of a motorway bridge that had fallen onto the railroad track near Studénka railway station in the Czech Republic and derails, killing eight people and injuring 64 others.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/EuroCity\" title=\"EuroCity\">EuroCity</a> express train en route from <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, Poland to <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, Czech Republic <a href=\"https://wikipedia.org/wiki/2008_Stud%C3%A9nka_train_wreck\" title=\"2008 Studénka train wreck\">strikes a part of a motorway bridge</a> that had fallen onto the railroad track near <a href=\"https://wikipedia.org/wiki/Stud%C3%A9nka\" title=\"Studénka\">Studénka</a> railway station in the Czech Republic and derails, killing eight people and injuring 64 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/EuroCity\" title=\"EuroCity\">EuroCity</a> express train en route from <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, Poland to <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a>, Czech Republic <a href=\"https://wikipedia.org/wiki/2008_Stud%C3%A9nka_train_wreck\" title=\"2008 Studénka train wreck\">strikes a part of a motorway bridge</a> that had fallen onto the railroad track near <a href=\"https://wikipedia.org/wiki/Stud%C3%A9nka\" title=\"Studénka\">Studénka</a> railway station in the Czech Republic and derails, killing eight people and injuring 64 others.", "links": [{"title": "EuroCity", "link": "https://wikipedia.org/wiki/EuroCity"}, {"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "2008 Studénka train wreck", "link": "https://wikipedia.org/wiki/2008_Stud%C3%A9nka_train_wreck"}, {"title": "Studén<PERSON>", "link": "https://wikipedia.org/wiki/Stud%C3%A9nka"}]}, {"year": "2008", "text": "The 29th modern summer Olympic Games took place in Beijing, China until August 24.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/2008_Summer_Olympics\" title=\"2008 Summer Olympics\">29th modern summer Olympic Games</a> took place in Beijing, China until August 24.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2008_Summer_Olympics\" title=\"2008 Summer Olympics\">29th modern summer Olympic Games</a> took place in Beijing, China until August 24.", "links": [{"title": "2008 Summer Olympics", "link": "https://wikipedia.org/wiki/2008_Summer_Olympics"}]}, {"year": "2009", "text": "A Eurocopter AS350 Écureuil and Piper PA-32R collide over the Hudson River, killing nine people.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil\" title=\"Eurocopter AS350 Écureuil\">Eurocopter AS350 Écureuil</a> and <a href=\"https://wikipedia.org/wiki/Piper_PA-32R\" title=\"Piper PA-32R\">Piper PA-32R</a> <a href=\"https://wikipedia.org/wiki/2009_Hudson_River_mid-air_collision\" title=\"2009 Hudson River mid-air collision\">collide</a> over the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>, killing nine people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil\" title=\"Eurocopter AS350 Écureuil\">Eurocopter AS350 Écureuil</a> and <a href=\"https://wikipedia.org/wiki/Piper_PA-32R\" title=\"Piper PA-32R\">Piper PA-32R</a> <a href=\"https://wikipedia.org/wiki/2009_Hudson_River_mid-air_collision\" title=\"2009 Hudson River mid-air collision\">collide</a> over the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>, killing nine people.", "links": [{"title": "Eurocopter AS350 Écureuil", "link": "https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil"}, {"title": "Piper PA-32R", "link": "https://wikipedia.org/wiki/Piper_PA-32R"}, {"title": "2009 Hudson River mid-air collision", "link": "https://wikipedia.org/wiki/2009_Hudson_River_mid-air_collision"}, {"title": "Hudson River", "link": "https://wikipedia.org/wiki/Hudson_River"}]}, {"year": "2010", "text": "China Floods: A mudslide in Zhugqu County, Gansu, China, kills more than 1,400 people.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/2010_China_floods\" title=\"2010 China floods\">China Floods</a>: <a href=\"https://wikipedia.org/wiki/2010_Gansu_mudslide\" title=\"2010 Gansu mudslide\">A mudslide</a> in <a href=\"https://wikipedia.org/wiki/Zhugqu_County\" class=\"mw-redirect\" title=\"Zhugqu County\">Zhugqu County</a>, <a href=\"https://wikipedia.org/wiki/Gansu\" title=\"Gansu\">Gansu</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, kills more than 1,400 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2010_China_floods\" title=\"2010 China floods\">China Floods</a>: <a href=\"https://wikipedia.org/wiki/2010_Gansu_mudslide\" title=\"2010 Gansu mudslide\">A mudslide</a> in <a href=\"https://wikipedia.org/wiki/Zhugqu_County\" class=\"mw-redirect\" title=\"Zhugqu County\">Zhugqu County</a>, <a href=\"https://wikipedia.org/wiki/Gansu\" title=\"Gansu\">Gansu</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, kills more than 1,400 people.", "links": [{"title": "2010 China floods", "link": "https://wikipedia.org/wiki/2010_China_floods"}, {"title": "2010 Gansu mudslide", "link": "https://wikipedia.org/wiki/2010_Gansu_mudslide"}, {"title": "Zhugqu County", "link": "https://wikipedia.org/wiki/Zhugqu_County"}, {"title": "Gansu", "link": "https://wikipedia.org/wiki/Gansu"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "2013", "text": "A suicide bombing at a funeral in the Pakistani city of Quetta kills at least 31 people.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/8_August_2013_Quetta_bombing\" class=\"mw-redirect\" title=\"8 August 2013 Quetta bombing\">suicide bombing</a> at a funeral in the <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistani</a> city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> kills at least 31 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/8_August_2013_Quetta_bombing\" class=\"mw-redirect\" title=\"8 August 2013 Quetta bombing\">suicide bombing</a> at a funeral in the <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistani</a> city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a> kills at least 31 people.", "links": [{"title": "8 August 2013 Quetta bombing", "link": "https://wikipedia.org/wiki/8_August_2013_Quetta_bombing"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}]}, {"year": "2015", "text": "Eight people are killed in a shooting in Harris County, Texas.", "html": "2015 - Eight people are killed in a <a href=\"https://wikipedia.org/wiki/2015_Harris_County_shooting\" title=\"2015 Harris County shooting\">shooting in Harris County, Texas</a>.", "no_year_html": "Eight people are killed in a <a href=\"https://wikipedia.org/wiki/2015_Harris_County_shooting\" title=\"2015 Harris County shooting\">shooting in Harris County, Texas</a>.", "links": [{"title": "2015 Harris County shooting", "link": "https://wikipedia.org/wiki/2015_Harris_County_shooting"}]}, {"year": "2016", "text": "Terrorists attack a government hospital in Quetta, Pakistan with a suicide blast and shooting, killing between 70 and 94 people, and injuring around 130 others.", "html": "2016 - Terrorists <a href=\"https://wikipedia.org/wiki/August_2016_Quetta_attacks\" title=\"August 2016 Quetta attacks\">attack</a> a government hospital in <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta, Pakistan</a> with a suicide blast and shooting, killing between 70 and 94 people, and injuring around 130 others.", "no_year_html": "Terrorists <a href=\"https://wikipedia.org/wiki/August_2016_Quetta_attacks\" title=\"August 2016 Quetta attacks\">attack</a> a government hospital in <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta, Pakistan</a> with a suicide blast and shooting, killing between 70 and 94 people, and injuring around 130 others.", "links": [{"title": "August 2016 Quetta attacks", "link": "https://wikipedia.org/wiki/August_2016_Quetta_attacks"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}]}, {"year": "2019", "text": "An explosion at the State Central Navy Testing Range in Nyonoksa, Russia, kills five people.", "html": "2019 - An <a href=\"https://wikipedia.org/wiki/Nyonoksa_radiation_accident\" title=\"Nyonoksa radiation accident\">explosion</a> at the <a href=\"https://wikipedia.org/wiki/State_Central_Navy_Testing_Range\" title=\"State Central Navy Testing Range\">State Central Navy Testing Range</a> in <a href=\"https://wikipedia.org/wiki/Nyonoksa\" title=\"Nyonoksa\">Nyonoksa</a>, Russia, kills five people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Nyonoksa_radiation_accident\" title=\"Nyonoksa radiation accident\">explosion</a> at the <a href=\"https://wikipedia.org/wiki/State_Central_Navy_Testing_Range\" title=\"State Central Navy Testing Range\">State Central Navy Testing Range</a> in <a href=\"https://wikipedia.org/wiki/Nyonoksa\" title=\"Nyonoksa\">Nyonoksa</a>, Russia, kills five people.", "links": [{"title": "Nyonoksa radiation accident", "link": "https://wikipedia.org/wiki/Nyonoksa_radiation_accident"}, {"title": "State Central Navy Testing Range", "link": "https://wikipedia.org/wiki/State_Central_Navy_Testing_Range"}, {"title": "Nyonoksa", "link": "https://wikipedia.org/wiki/Nyonoksa"}]}, {"year": "2022", "text": "The Federal Bureau of Investigation (FBI) executes a search warrant at former president <PERSON>'s residence in Mar-a-Lago, Palm Beach, Florida.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a> (FBI) <a href=\"https://wikipedia.org/wiki/FBI_search_of_Mar-a-Lago\" title=\"FBI search of Mar-a-Lago\">executes a search warrant</a> at former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s residence in <a href=\"https://wikipedia.org/wiki/Mar-a-Lago\" title=\"Mar-a-Lago\">Mar-a-Lago</a>, <a href=\"https://wikipedia.org/wiki/Palm_Beach,_Florida\" title=\"Palm Beach, Florida\">Palm Beach, Florida</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">Federal Bureau of Investigation</a> (FBI) <a href=\"https://wikipedia.org/wiki/FBI_search_of_Mar-a-Lago\" title=\"FBI search of Mar-a-Lago\">executes a search warrant</a> at former president <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s residence in <a href=\"https://wikipedia.org/wiki/Mar-a-Lago\" title=\"Mar-a-Lago\">Mar-a-Lago</a>, <a href=\"https://wikipedia.org/wiki/Palm_Beach,_Florida\" title=\"Palm Beach, Florida\">Palm Beach, Florida</a>.", "links": [{"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "FBI search of Mar-a-Lago", "link": "https://wikipedia.org/wiki/FBI_search_of_Mar-a-Lago"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mar-a-Lago", "link": "https://wikipedia.org/wiki/Mar-a-Lago"}, {"title": "Palm Beach, Florida", "link": "https://wikipedia.org/wiki/Palm_Beach,_Florida"}]}, {"year": "2023", "text": "Hawaii wildfires: Seventeen thousand acres of land are burned and at least 101 people are killed, with two others missing, when a series of wildfires break out on the island of Maui in Hawaii.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/2023_Hawaii_wildfires\" title=\"2023 Hawaii wildfires\">Hawaii wildfires</a>: Seventeen thousand acres of land are burned and at least 101 people are killed, with two others missing, when a series of <a href=\"https://wikipedia.org/wiki/Wildfire\" title=\"Wildfire\">wildfires</a> break out on the island of <a href=\"https://wikipedia.org/wiki/Maui\" title=\"Maui\">Maui</a> in <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2023_Hawaii_wildfires\" title=\"2023 Hawaii wildfires\">Hawaii wildfires</a>: Seventeen thousand acres of land are burned and at least 101 people are killed, with two others missing, when a series of <a href=\"https://wikipedia.org/wiki/Wildfire\" title=\"Wildfire\">wildfires</a> break out on the island of <a href=\"https://wikipedia.org/wiki/Maui\" title=\"Maui\">Maui</a> in <a href=\"https://wikipedia.org/wiki/Hawaii\" title=\"Hawaii\">Hawaii</a>.", "links": [{"title": "2023 Hawaii wildfires", "link": "https://wikipedia.org/wiki/2023_Hawaii_wildfires"}, {"title": "Wildfire", "link": "https://wikipedia.org/wiki/Wildfire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maui"}, {"title": "Hawaii", "link": "https://wikipedia.org/wiki/Hawaii"}]}, {"year": "2024", "text": "Nobel laureate <PERSON> takes oath as Chief Adviser to form an interim government in Bangladesh.", "html": "2024 - Nobel laureate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes oath as <a href=\"https://wikipedia.org/wiki/Chief_Adviser_(Bangladesh)\" title=\"Chief Adviser (Bangladesh)\">Chief Adviser</a> to form an <a href=\"https://wikipedia.org/wiki/Yun<PERSON>_interim_government\" class=\"mw-redirect\" title=\"Yunus interim government\">interim government</a> in Bangladesh.", "no_year_html": "Nobel laureate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes oath as <a href=\"https://wikipedia.org/wiki/Chief_Adviser_(Bangladesh)\" title=\"Chief Adviser (Bangladesh)\">Chief Adviser</a> to form an <a href=\"https://wikipedia.org/wiki/Yun<PERSON>_interim_government\" class=\"mw-redirect\" title=\"Yunus interim government\">interim government</a> in Bangladesh.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Adviser (Bangladesh)", "link": "https://wikipedia.org/wiki/Chief_Adviser_(Bangladesh)"}, {"title": "<PERSON><PERSON> interim government", "link": "https://wikipedia.org/wiki/Yun<PERSON>_interim_government"}]}], "Births": [{"year": "422", "text": "<PERSON>, ruler of the Maya city of Palenque", "html": "422 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Maya_ruler)\" title=\"<PERSON> (Maya ruler)\"><PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> city of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\">Palenque</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Maya_ruler)\" title=\"<PERSON> (Maya ruler)\"><PERSON></a>, ruler of the <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> city of <a href=\"https://wikipedia.org/wiki/Palenque\" title=\"Palenque\">Palenque</a>", "links": [{"title": "<PERSON> (Maya ruler)", "link": "https://wikipedia.org/wiki/<PERSON>_(Maya_ruler)"}, {"title": "Maya civilization", "link": "https://wikipedia.org/wiki/Maya_civilization"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Palenque"}]}, {"year": "1079", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1107)", "html": "1079 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1107)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1107)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1170", "text": "<PERSON>, founder of the Dominicans (d. 1221)", "html": "1170 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Saint <PERSON>\">Saint <PERSON></a>, founder of the Dominicans (d. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Saint <PERSON></a>, founder of the Dominicans (d. 1221)", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/Saint_Dominic"}]}, {"year": "1306", "text": "<PERSON>, Duke of Bavaria (d. 1353)", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1353)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1492", "text": "<PERSON>, Italian alchemist (d. 1582)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian alchemist (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian alchemist (d. 1582)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1518", "text": "<PERSON>, French-German scholar and author (d. 1561)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German scholar and author (d. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-German scholar and author (d. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Conrad_Ly<PERSON>thenes"}]}, {"year": "1558", "text": "<PERSON>, 3rd Earl of Cumberland, English noble (d. 1605)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Cumberland\" title=\"<PERSON>, 3rd Earl of Cumberland\"><PERSON>, 3rd Earl of Cumberland</a>, English noble (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Cumberland\" title=\"<PERSON>, 3rd Earl of Cumberland\"><PERSON>, 3rd Earl of Cumberland</a>, English noble (d. 1605)", "links": [{"title": "<PERSON>, 3rd Earl of Cumberland", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Cumberland"}]}, {"year": "1605", "text": "<PERSON>, 2nd Baron <PERSON>, English lawyer and politician, Governor of Newfoundland (d. 1675)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Governor of Newfoundland</a> (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron Baltimore\"><PERSON>, 2nd Baron <PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador\" class=\"mw-redirect\" title=\"List of lieutenant governors of Newfoundland and Labrador\">Governor of Newfoundland</a> (d. 1675)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>"}, {"title": "List of lieutenant governors of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/List_of_lieutenant_governors_of_Newfoundland_and_Labrador"}]}, {"year": "1640", "text": "<PERSON><PERSON>, German poet and composer (d. 1697)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Amalia_Catharina\" class=\"mw-redirect\" title=\"Amalia Catharina\"><PERSON><PERSON></a>, German poet and composer (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Catharina\" class=\"mw-redirect\" title=\"Amalia Catharina\"><PERSON><PERSON></a>, German poet and composer (d. 1697)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>na"}]}, {"year": "1646", "text": "<PERSON>, German-English painter (d. 1723)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English painter (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English painter (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1673", "text": "<PERSON>, Scottish spy (d. 1726)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish spy (d. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish spy (d. 1726)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, French composer (d. 1762)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, Irish philosopher and academic (d. 1746)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Irish philosopher and academic (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Irish philosopher and academic (d. 1746)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1706", "text": "<PERSON>, Swedish nobleman and military leader (d. 1778)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish nobleman and military leader (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish nobleman and military leader (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, German-Italian monk and violinist (d. 1779)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian monk and violinist (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian monk and violinist (d. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, Swedish general and politician (d. 1796)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish general and politician (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish general and politician (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish botanist (d. 1816)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Hip%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B3pez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish botanist (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hip%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B3pez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish botanist (d. 1816)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hip%C3%B3<PERSON><PERSON>_<PERSON>_L%C3%B3pez"}]}, {"year": "1758", "text": "<PERSON>, German painter (d. 1828)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON>, Hungarian poet, critic, and politician (d. 1838)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet, critic, and politician (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet, critic, and politician (d. 1838)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_K%C3%B6lcsey"}]}, {"year": "1807", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish author (d. 1892)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>%C3%A9n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author (d. 1892)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gare-Carl%C3%A9n"}]}, {"year": "1814", "text": "<PERSON>, American suffragette and judge (d. 1902)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragette and judge (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragette and judge (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, Jr., United States Army cavalry officer (d. 1894)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, United States Army cavalry officer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, United States Army cavalry officer (d. 1894)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1839", "text": "<PERSON>, American general (d. 1925)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Nelson A. Miles\"><PERSON></a>, American general (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Nelson A<PERSON> Miles\"><PERSON></a>, American general (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Australian politician, 18th Premier of Victoria (d. 1916)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1916)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1856", "text": "<PERSON>, English journalist and author (d. 1934)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON><PERSON>, French pianist and composer (d. 1944)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/C%C3%A9cile_Chaminade\" title=\"Cécile Chaminade\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9cile_Chaminade\" title=\"Cécile Chaminade\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9cile_Chaminade"}]}, {"year": "1863", "text": "<PERSON>, American painter (d. 1930)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American explorer (d. 1955)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American explorer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, 1st Baron <PERSON>, English businessman and politician, President of the Board of Trade (d. 1948)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Board_of_Trade\" title=\"President of the Board of Trade\">President of the Board of Trade</a> (d. 1948)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "President of the Board of Trade", "link": "https://wikipedia.org/wiki/President_of_the_Board_of_Trade"}]}, {"year": "1875", "text": "<PERSON><PERSON>, Brazilian lawyer and politician, 12th President of Brazil (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-Syrian priest, founded the Sisters of the Destitute (d. 1929)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Varghese_Payyappilly_Palakkappilly\" class=\"mw-redirect\" title=\"Varghese Payyappilly Palakkappilly\">Varghese Payyappilly Palakkappilly</a>, Indian-Syrian priest, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_the_Destitute\" title=\"Sisters of the Destitute\">Sisters of the Destitute</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varghese_Payyappilly_Palakkappilly\" class=\"mw-redirect\" title=\"Varghese Payyappilly Palakkappilly\">Varghese Payyappilly Palakkappilly</a>, Indian-Syrian priest, founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_the_Destitute\" title=\"Sisters of the Destitute\">Sisters of the Destitute</a> (d. 1929)", "links": [{"title": "Varghese Payyappilly Palakkappilly", "link": "https://wikipedia.org/wiki/Var<PERSON><PERSON>_Payyappilly_Palakkappilly"}, {"title": "Sisters of the Destitute", "link": "https://wikipedia.org/wiki/Sisters_of_the_Destitute"}]}, {"year": "1879", "text": "<PERSON>, American physician and surgeon, co-founded Alcoholics Anonymous (d. 1950)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician and surgeon, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(doctor)\" title=\"<PERSON> (doctor)\"><PERSON></a>, American physician and surgeon, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (d. 1950)", "links": [{"title": "<PERSON> (doctor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(doctor)"}, {"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Mexican general and politician (d. 1919)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Z<PERSON>\"><PERSON><PERSON></a>, Mexican general and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emiliano_Zapata\" title=\"Emiliano Zapata\"><PERSON><PERSON></a>, Mexican general and politician (d. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_Zapata"}]}, {"year": "1880", "text": "<PERSON>, Australian lawyer, academic, and politician, 11th Prime Minister of Australia (d. 1961)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer, academic, and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1881", "text": "<PERSON>, German field marshal (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Russian-French animator, screenwriter, and cinematographer (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-French animator, screenwriter, and cinematographer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-French animator, screenwriter, and cinematographer (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American poet and educator (d. 1933)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>sdale\"><PERSON></a>, American poet and educator (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_Teasdale"}]}, {"year": "1889", "text": "<PERSON>, Danish actor (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Australian cricketer (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1977)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1891", "text": "<PERSON>, German violinist and composer (d. 1952)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American author and academic (d. 1953)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, French chef (d. 1965)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, French chef (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1958)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1902", "text": "<PERSON>, English-American physicist and academic, Nobel Prize laureate (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (d. 1948)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French composer (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Jolivet\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Jo<PERSON>vet\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Jolivet"}]}, {"year": "1907", "text": "<PERSON>, American saxophonist, trumpet player, and composer (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carter\"><PERSON></a>, American saxophonist, trumpet player, and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Carter\"><PERSON></a>, American saxophonist, trumpet player, and composer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON> (Irish republican), lifelong militant and editor (d. 1970)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, lifelong militant and editor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON> (Irish republican)</a>, lifelong militant and editor (d. 1970)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1908", "text": "<PERSON>, American jurist and politician, 6th United States Ambassador to the United Nations (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1909", "text": "<PERSON>, 10th Viscount <PERSON>, English cricketer and politician, 9th Governor-General of New Zealand (d. 1977)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>\" title=\"<PERSON>, 10th Viscount <PERSON>\"><PERSON>, 10th Viscount <PERSON></a>, English cricketer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>\" title=\"<PERSON>, 10th Viscount <PERSON>\"><PERSON>, 10th Viscount <PERSON></a>, English cricketer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1977)", "links": [{"title": "<PERSON>, 10th Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Viscount_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1909", "text": "<PERSON>, Australian politician, 31st Premier of New South Wales (d. 1987)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1909", "text": "<PERSON>, England cricketer and coach (d. 1984)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, England cricketer and coach (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, England cricketer and coach (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Welsh-English footballer and manager (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh-English footballer and manager (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Welsh-English footballer and manager (d. 1989)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sidney\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American actress (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosetta_LeNoire"}]}, {"year": "1915", "text": "<PERSON>, American runner and coach (d. 1981)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)\" title=\"<PERSON><PERSON> (coach)\"><PERSON></a>, American runner and coach (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)\" title=\"<PERSON><PERSON> (coach)\"><PERSON></a>, American runner and coach (d. 1981)", "links": [{"title": "<PERSON><PERSON> (coach)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(coach)"}]}, {"year": "1919", "text": "<PERSON>, Italian actor and producer (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English animator and producer (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animator and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English animator and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Italian songwriter and producer (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian songwriter and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian songwriter and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American jump blues singer (d. 1997)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jump blues singer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jump blues singer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American director, producer, and screenwriter (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American swimmer and actress (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Austrian-American fashion designer, created the <PERSON><PERSON><PERSON> (d. 1985)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American fashion designer, created the <a href=\"https://wikipedia.org/wiki/Monokini\" title=\"<PERSON>oki<PERSON>\">Mon<PERSON><PERSON></a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American fashion designer, created the <a href=\"https://wikipedia.org/wiki/Monokini\" title=\"<PERSON>oki<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>okini"}]}, {"year": "1922", "text": "<PERSON>, American historian, author, and academic (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian illustrator (d. 1988)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Reich\" title=\"K<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian illustrator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Reich\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian illustrator (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_Reich"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Bosnian lawyer and politician, 1st President of Bosnia and Herzegovina (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tbegovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Presidency_of_Bosnia_and_Herzegovina\" title=\"Presidency of Bosnia and Herzegovina\">President of Bosnia and Herzegovina</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tbegovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Presidency_of_Bosnia_and_Herzegovina\" title=\"Presidency of Bosnia and Herzegovina\">President of Bosnia and Herzegovina</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alija_Izetbegovi%C4%87"}, {"title": "Presidency of Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Presidency_of_Bosnia_and_Herzegovina"}]}, {"year": "1925", "text": "<PERSON>, Malaysian actor, comedian, singer and director (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian actor, comedian, singer and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian actor, comedian, singer and director (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and producer (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American baseball player and coach (d. 1994)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Johnny Temple\"><PERSON></a>, American baseball player and coach (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Johnny Temple\"><PERSON></a>, American baseball player and coach (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Polish-American author (d. 2002)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_W<PERSON>j<PERSON><PERSON>ws<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Australian saxophonist, clarinet player, and flute player (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist, clarinet player, and flute player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist, clarinet player, and flute player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Russian linguist and activist (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian linguist and activist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian linguist and activist (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Bolivian general and politician, 68th President of Bolivia (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Meza_Tejada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 68th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Meza_Tejada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 68th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Garc%C3%ADa_Meza_Tejada"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1929", "text": "<PERSON>, English criminal (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Welsh-American author and screenwriter (d. 1997)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Terry Nation\"><PERSON></a>, Welsh-American author and screenwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terry_Nation\" title=\"Terry Nation\"><PERSON></a>, Welsh-American author and screenwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Terry_Nation"}]}, {"year": "1930", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jerry_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English physicist, mathematician, and philosopher, Nobel Prize laureate", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, mathematician, and philosopher, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist, mathematician, and philosopher, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American soul singer-songwriter (d. 1982)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Joe_Tex\" title=\"Joe Tex\"><PERSON></a>, American soul singer-songwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joe_Tex\" title=\"Joe Tex\"><PERSON></a>, American soul singer-songwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian actor, director, and screenwriter (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Sarat_Pujari\" title=\"Sarat Pujari\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarat_Pujari\" title=\"Sarat Pujari\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarat_Pujari"}]}, {"year": "1935", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Papua New Guinean-Australian singer and radio host", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian singer and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean-Australian singer and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and manager (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (d. 2023)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball)"}]}, {"year": "1936", "text": "<PERSON>, Polish-English author and illustrator (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84kowski\" title=\"<PERSON>\"><PERSON></a>, Polish-English author and illustrator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84kowski\" title=\"<PERSON>\"><PERSON></a>, Polish-English author and illustrator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84kowski"}]}, {"year": "1937", "text": "<PERSON>, American actor and director", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American horse breeder (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American horse breeder (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American horse breeder (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Dutch-Swedish singer-songwriter, guitarist, and actor (d. 1987)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>reeswijk\" title=\"Corne<PERSON> Vreeswijk\"><PERSON><PERSON><PERSON></a>, Dutch-Swedish singer-songwriter, guitarist, and actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vreeswijk\" title=\"Cornelis Vreeswijk\"><PERSON><PERSON><PERSON></a>, Dutch-Swedish singer-songwriter, guitarist, and actor (d. 1987)", "links": [{"title": "Cornelis <PERSON>wi<PERSON>", "link": "https://wikipedia.org/wiki/Cornelis_Vreeswijk"}]}, {"year": "1938", "text": "<PERSON>, English chemist and academic (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, English chemist and academic (d. 2020)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)"}]}, {"year": "1938", "text": "<PERSON>, Canadian composer and educator (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tu\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_H%C3%A9tu"}]}, {"year": "1938", "text": "<PERSON>, American actress and businesswoman", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Czech actress and ballerina (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech actress and ballerina (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech actress and ballerina (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andrsov%C3%A1"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Romanian long jumper", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>anu\" title=\"Viorica Viscopoleanu\"><PERSON><PERSON><PERSON></a>, Romanian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>\" title=\"Viorica Viscopoleanu\"><PERSON><PERSON><PERSON></a>, Romanian long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ior<PERSON>_<PERSON>anu"}]}, {"year": "1939", "text": "<PERSON>, American diplomat, United States Ambassador to Peru", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Peru\" class=\"mw-redirect\" title=\"United States Ambassador to Peru\">United States Ambassador to Peru</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Peru\" class=\"mw-redirect\" title=\"United States Ambassador to Peru\">United States Ambassador to Peru</a>", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}, {"title": "United States Ambassador to Peru", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Peru"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Indian cricketer (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American engineer and businessman, founded Wilshire Associates", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Wilshire_Associates\" title=\"Wilshire Associates\">Wilshire Associates</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Wilshire_Associates\" title=\"Wilshire Associates\">Wilshire Associates</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wilshire Associates", "link": "https://wikipedia.org/wiki/Wilshire_Associates"}]}, {"year": "1942", "text": "<PERSON>, American diplomat and politician, 45th Governor of Michigan", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American diplomat and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American diplomat and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1942", "text": "<PERSON>, Scottish educator and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English singer-songwriter and bass player (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and bass player (d. 2014)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Estonian pianist and musicologist (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian pianist and musicologist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian pianist and musicologist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American film actor (d. 1988)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film actor (d. 1988)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2017)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1944", "text": "<PERSON>, English-Scottish guitarist and songwriter (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish guitarist and songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish guitarist and songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian ice hockey player, lawyer, and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, lawyer, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor, director, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Russian engineer and astronaut", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian engineer and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American philosopher", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actress (d. 2013)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Colombian racing driver (d. 2009)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Colombian racing driver (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Colombian racing driver (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_Londo%C3%B1o"}]}, {"year": "1950", "text": "<PERSON>, American drummer and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(drummer)"}]}, {"year": "1950", "text": "<PERSON>, Japanese businessman, created PlayStation", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese businessman, created <a href=\"https://wikipedia.org/wiki/PlayStation_(console)\" title=\"PlayStation (console)\">PlayStation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese businessman, created <a href=\"https://wikipedia.org/wiki/PlayStation_(console)\" title=\"PlayStation (console)\">PlayStation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PlayStation (console)", "link": "https://wikipedia.org/wiki/PlayStation_(console)"}]}, {"year": "1951", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Martin_<PERSON>\" title=\"Martin <PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martin_Brest\" title=\"Martin Brest\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian cricketer (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Egyptian engineer, academic, and politician, 5th President of Egypt (d. 2019)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian engineer, academic, and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian engineer, academic, and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1951", "text": "<PERSON>, American journalist and author (d. 1994)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South African-American drummer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Norwegian author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gaarder\"><PERSON><PERSON></a>, Norwegian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Jo<PERSON> Gaarder\"><PERSON><PERSON></a>, Norwegian author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian baseball player and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American nurse, radio host/personality, and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, radio host/personality, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, radio host/personality, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>uivers"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English racing driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Most\" title=\"Don Most\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_Most\" title=\"Don Most\"><PERSON></a>, American actor and singer", "links": [{"title": "Don <PERSON>", "link": "https://wikipedia.org/wiki/Don_Most"}]}, {"year": "1954", "text": "<PERSON>, English bishop", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Icelandic singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Didd%C3%BA\" title=\"Diddú\"><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Didd%C3%BA\" title=\"Diddú\"><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "links": [{"title": "Diddú", "link": "https://wikipedia.org/wiki/Didd%C3%BA"}]}, {"year": "1955", "text": "<PERSON>, Austrian footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Irish racing driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Irish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>(racing_driver)"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1956", "text": "<PERSON>, Argentinian actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American keyboard player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Dutch flute player, composer, and educator", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch flute player, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch flute player, composer, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Turkish journalist and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German politician, 16th Mayor of Nuremberg", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 16th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Nuremberg\" title=\"List of mayors of Nuremberg\">Mayor of Nuremberg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 16th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Nuremberg\" title=\"List of mayors of Nuremberg\">Mayor of Nuremberg</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Nuremberg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Nuremberg"}]}, {"year": "1961", "text": "<PERSON>, British-Irish musician, singer and songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/The_Edge\" title=\"The Edge\">The Edge</a>, British-Irish musician, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Edge\" title=\"The Edge\">The Edge</a>, British-Irish musician, singer and songwriter", "links": [{"title": "The Edge", "link": "https://wikipedia.org/wiki/The_Edge"}]}, {"year": "1961", "text": "<PERSON>, American bass player and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician, White House Chief of Staff", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "1961", "text": "<PERSON>, American football player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American glam rock drummer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American glam rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American glam rock drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American musician, singer and actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, South Korean director and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, South Korean director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-ho"}]}, {"year": "1963", "text": "<PERSON>, American baseball player and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ron_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Japanese voice actress and singer (d. 2024)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American director and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player, referee, and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, referee, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, referee, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American blogger and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blogger and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Prime Minister of Italy", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1964)\" title=\"<PERSON> (cricketer, born 1964)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1964)\" title=\"<PERSON> (cricketer, born 1964)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer, born 1964)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer,_born_1964)"}]}, {"year": "1965", "text": "<PERSON>, English cricketer, manager, and journalist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, manager, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angus_Fraser\" title=\"<PERSON>\"><PERSON></a>, English cricketer, manager, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_Fraser"}]}, {"year": "1965", "text": "<PERSON>, Australian talk show host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English boxer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American soccer player, coach, and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player, coach, and sportscaster", "links": [{"title": "Marcel<PERSON>", "link": "https://wikipedia.org/wiki/Marcel<PERSON>_<PERSON>boa"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese theater and film actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese theater and film actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese theater and film actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%<PERSON><PERSON>_Amami"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Scottish soprano", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Honduran journalist (d. 2013)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Aldo_Calder%C3%B3n_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Honduran journalist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldo_Calder%C3%B3n_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Honduran journalist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Calder%C3%B3n_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Indian cricketer and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_<PERSON>villa"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Estonian chess player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ts%C3%B5ganova\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ts%C3%B5ganova\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Monika_Ts%C3%B5ganova"}]}, {"year": "1969", "text": "<PERSON>, Chinese singer-songwriter and actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American football player and journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Trev_<PERSON>\" title=\"Trev <PERSON><PERSON>\">Tre<PERSON></a>, American football player and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trev_<PERSON>\" title=\"Trev <PERSON>\">Tre<PERSON></a>, American football player and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trev_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English chemist and academic", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Spanish footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, South African rugby player and coach (d. 2019)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williams"}]}, {"year": "1971", "text": "<PERSON>, Dutch baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Canadian actress and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Italian rugby player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Belgian cyclist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>x"}]}, {"year": "1972", "text": "<PERSON>, Scottish footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian cricketer and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and guitarist", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Estonian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, German mathematician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Il<PERSON>_Agricola\" title=\"<PERSON><PERSON> Agricola\"><PERSON><PERSON></a>, German mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Il<PERSON>_Agricola\" title=\"Il<PERSON> Agricola\"><PERSON><PERSON></a>, German mathematician", "links": [{"title": "Ilka Agricola", "link": "https://wikipedia.org/wiki/Ilka_Agricola"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Canadian-American mathematician and academic", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON>_Bhargava\" title=\"Man<PERSON><PERSON> Bhargava\"><PERSON><PERSON><PERSON></a>, Canadian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON><PERSON>_<PERSON>hargava\" title=\"Man<PERSON><PERSON> Bhargava\"><PERSON><PERSON><PERSON></a>, Canadian-American mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manjul_Bhargava"}]}, {"year": "1974", "text": "<PERSON>, Canadian wrestler and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amore\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Amore\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Scott_D%27Amore"}]}, {"year": "1974", "text": "<PERSON>, English singer-songwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Guernseyan racing driver", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guernseyan racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guernseyan racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American singer and dancer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON>z\" title=\"J<PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON>z\" title=\"J<PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and dancer", "links": [{"title": "JC Chasez", "link": "https://wikipedia.org/wiki/JC_Chasez"}]}, {"year": "1976", "text": "<PERSON>, American singer and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American sergeant (d. 2013)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Pakistani cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cricketer)"}]}, {"year": "1978", "text": "<PERSON>, Irish footballer and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cellist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Northern Irish racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Northern Irish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Northern Irish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American mixed martial artist and wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist and wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball pitcher and executive", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Mexican singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Dutch baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American soccer player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, director, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swiss tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actress and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Good\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>agan Good\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Israeli singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}]}, {"year": "1982", "text": "<PERSON>, English canoe racer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, English canoe racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David <PERSON>\"><PERSON></a>, English canoe racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actor and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burnet\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Guy Burnet\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willie <PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Willie_<PERSON>\" title=\"Willie Tonga\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Scottish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Hungarian racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Flood\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dutch footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Welsh actor and producer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Polish track and field athlete", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W%C5%82odarc<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish track and field athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_W%C5%82odarczy<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish track and field athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anita_W%C5%82odarczyk"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Ukrainian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dominican-American actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7on\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7on\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7on"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "Princess <PERSON>, British royal", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>\" title=\"Princess <PERSON>\">Princess <PERSON></a>, British royal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>\" title=\"Princess <PERSON>\">Princess <PERSON></a>, British royal", "links": [{"title": "Princess <PERSON>", "link": "https://wikipedia.org/wiki/Princess_Beatrice"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Italian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Indian baseball player and wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, Indian baseball player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)\" title=\"<PERSON><PERSON><PERSON> (baseball)\"><PERSON><PERSON><PERSON></a>, Indian baseball player and wrestler", "links": [{"title": "<PERSON><PERSON><PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(baseball)"}]}, {"year": "1988", "text": "<PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggins\" title=\"<PERSON>iggins\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ig<PERSON>\" title=\"<PERSON>ig<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor and author", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English-Scottish swimmer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Prajakta_Mali\" title=\"Prajakta Mali\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prajakta_<PERSON>\" title=\"Prajakta Mali\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prajakta_Mali"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Darida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladim%C3%ADr_Darida\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_Darida"}]}, {"year": "1990", "text": "<PERSON>, American race car driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>man\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Polish actress and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, New Zealand cricket captain", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricket captain", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricket captain", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Cuban baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Yandy_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yan<PERSON>_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yandy_D%C3%ADaz"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Cameroonian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Jo%C3%ABl_Matip\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%ABl_Matip\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%AB<PERSON>_Matip"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Swiss footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sip_Drmi%C4%87"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Norwegian politician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean rapper and singer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/S.Coups\" title=\"S.Coups\"><PERSON><PERSON></a>, South Korean rapper and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S.Coups\" title=\"<PERSON><PERSON>Coups\"><PERSON><PERSON></a>, South Korean rapper and singer", "links": [{"title": "S.Coups", "link": "https://wikipedia.org/wiki/S.Coups"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/A%27<PERSON>_<PERSON>\" title=\"<PERSON>'ja <PERSON>\"><PERSON><PERSON>j<PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A%27<PERSON>_<PERSON>\" title=\"<PERSON>'ja <PERSON>\"><PERSON><PERSON>j<PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A%27<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1998", "text": "<PERSON>, American boxer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Chinese singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Canadian tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>er-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON>er-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Auger-<PERSON><PERSON><PERSON>"}]}], "Deaths": [{"year": "117", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 53)", "html": "117 - <a href=\"https://wikipedia.org/wiki/Trajan\" title=\"Traj<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 53)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Traj<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (b. 53)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "753", "text": "<PERSON><PERSON><PERSON>, bishop of  Cologne", "html": "753 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Cologne)\" title=\"<PERSON><PERSON><PERSON> (bishop of Cologne)\"><PERSON><PERSON><PERSON></a>, bishop of Cologne", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Cologne)\" title=\"<PERSON><PERSON><PERSON> (bishop of Cologne)\"><PERSON><PERSON><PERSON></a>, bishop of Cologne", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop of Cologne)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Cologne)"}]}, {"year": "869", "text": "<PERSON><PERSON><PERSON> <PERSON>, Frankish king (b. 835)", "html": "869 - <a href=\"https://wikipedia.org/wiki/Lothair_II\" title=\"Lothair II\"><PERSON><PERSON><PERSON> <PERSON></a>, Frankish king (b. 835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Lothair II\"><PERSON><PERSON><PERSON> II</a>, Frankish king (b. 835)", "links": [{"title": "Lothair II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "998", "text": "<PERSON><PERSON>, Korean politician and diplomat (b. 942)", "html": "998 - <a href=\"https://wikipedia.org/wiki/S%C5%8F_H%C5%ADi\" title=\"<PERSON><PERSON> Hŭ<PERSON>\"><PERSON><PERSON></a>, Korean politician and diplomat (b. 942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C5%8F_H%C5%ADi\" title=\"<PERSON>ŏ Hŭi\"><PERSON><PERSON></a>, Korean politician and diplomat (b. 942)", "links": [{"title": "Sŏ Hŭi", "link": "https://wikipedia.org/wiki/S%C5%8F_H%C5%ADi"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON>, chief minister and de facto ruler of Córdoba", "html": "1002 - <a href=\"https://wikipedia.org/wiki/Almanzor\" title=\"Almanzor\"><PERSON><PERSON><PERSON></a>, chief minister and <i>de facto</i> ruler of Córdoba", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almanzor\" title=\"Almanzor\"><PERSON><PERSON><PERSON></a>, chief minister and <i>de facto</i> ruler of Córdoba", "links": [{"title": "Almanzor", "link": "https://wikipedia.org/wiki/Almanzor"}]}, {"year": "1171", "text": "<PERSON> Blois, bishop of Winchester (b. 1111)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Blois\" title=\"<PERSON> of Blois\"><PERSON> of Blois</a>, bishop of Winchester (b. 1111)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Blo<PERSON>\" title=\"<PERSON> of Blois\"><PERSON> of Blois</a>, bishop of Winchester (b. 1111)", "links": [{"title": "<PERSON> of Blois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1303", "text": "<PERSON> of Castile the Senator, Spanish nobleman (b. 1230)", "html": "1303 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Castile_the_Senator\" title=\"<PERSON> of Castile the Senator\"><PERSON> of Castile the Senator</a>, Spanish nobleman (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile_the_Senator\" title=\"<PERSON> of Castile the Senator\"><PERSON> of Castile the Senator</a>, Spanish nobleman (b. 1230)", "links": [{"title": "<PERSON> of Castile the Senator", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Castile_the_Senator"}]}, {"year": "1533", "text": "<PERSON>, Dutch artist (b. 1494)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch artist (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch artist (b. 1494)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1555", "text": "<PERSON><PERSON>, French mathematician and cartographer (b. 1494)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/Oronce_Fin%C3%A9\" class=\"mw-redirect\" title=\"Oronce Finé\"><PERSON><PERSON></a>, French mathematician and cartographer (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oronce_Fin%C3%A9\" class=\"mw-redirect\" title=\"Oronce Finé\"><PERSON><PERSON></a>, French mathematician and cartographer (b. 1494)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oronce_Fin%C3%A9"}]}, {"year": "1588", "text": "<PERSON>, Spanish painter (b. 1532)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish painter (b. 1532)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alonso_S%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON><PERSON>, Japanese daimyō (b. 1578)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese daimyō (b. 1578)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1548)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cornelis Ketel\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Cornelis <PERSON>tel\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1548)", "links": [{"title": "Cornelis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rnelis_Ketel"}]}, {"year": "1631", "text": "<PERSON><PERSON>, Lithuanian priest, lexicographer, and academic (b. 1579)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian priest, lexicographer, and academic (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian priest, lexicographer, and academic (b. 1579)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, 1st Baron <PERSON>, English politician (b. 1622)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1622)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, German painter (b. 1665)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1665)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1747", "text": "<PERSON>, Canadian raid leader (b. 1678)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ch%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, Canadian raid leader (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON>\"><PERSON></a>, Canadian raid leader (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Verch%C3%A8res"}]}, {"year": "1746", "text": "<PERSON>, Irish philosopher (b. 1694)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Irish philosopher (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Irish philosopher (b. 1694)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1759", "text": "<PERSON>, German tenor and composer (b. 1704)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and composer (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and composer (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (b. 1770)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1828", "text": "<PERSON>, Swedish botanist and psychologist (b. 1743)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and psychologist (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and psychologist (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON><PERSON>, Haitian Empress (b. 1758)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_F%C3%A9licit%C3%A9\" title=\"<PERSON><PERSON><PERSON> He<PERSON> Félicité\"><PERSON><PERSON><PERSON>é<PERSON></a>, Haitian Empress (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_F%C3%A9licit%C3%A9\" title=\"<PERSON><PERSON><PERSON> He<PERSON> Félicité\"><PERSON><PERSON><PERSON>é<PERSON></a>, Haitian Empress (b. 1758)", "links": [{"title": "<PERSON><PERSON><PERSON>é<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_F%C3%A9licit%C3%A9"}]}, {"year": "1863", "text": "<PERSON>, Scottish-Canadian giant (b. 1825)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Angus_MacAskill\" title=\"Angus MacAskill\"><PERSON></a>, Scottish-Canadian giant (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angus_MacAskill\" title=\"Angus MacAskill\"><PERSON></a>, Scottish-Canadian giant (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_MacAskill"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, German philosopher and academic (b. 1797)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Imma<PERSON><PERSON>_<PERSON>\" title=\"Immanu<PERSON>\">I<PERSON><PERSON><PERSON></a>, German philosopher and academic (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Immanu<PERSON>\">I<PERSON><PERSON><PERSON> <PERSON></a>, German philosopher and academic (b. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American colonel, lawyer, and politician (b. 1808)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Swiss historian and academic (b. 1818)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and academic (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and academic (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, French painter (b. 1824)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>n"}]}, {"year": "1902", "text": "<PERSON>, French painter and illustrator (b. 1836)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American painter and academic (b. 1853)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian nun and saint, co-founded the Sisters of St Joseph of the Sacred Heart (b. 1842)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian nun and saint, co-founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_St_Joseph_of_the_Sacred_Heart\" title=\"Sisters of St Joseph of the Sacred Heart\">Sisters of St Joseph of the Sacred Heart</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian nun and saint, co-founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_St_Joseph_of_the_Sacred_Heart\" title=\"Sisters of St Joseph of the Sacred Heart\">Sisters of St Joseph of the Sacred Heart</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sisters of St Joseph of the Sacred Heart", "link": "https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_<PERSON>_of_the_Sacred_Heart"}]}, {"year": "1911", "text": "<PERSON>, American lawyer and politician (b. 1830)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Polish-born German cantor (b. 1855)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born German cantor (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born German cantor (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Finnish journalist and author (b. 1861)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist and author (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Croatian politician (b. 1871)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Stjepan_Radi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stjepan_Radi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian politician (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stjepan_Radi%C4%87"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Scottish wrestler and weightlifter (b. 1874)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Elliot\" title=\"<PERSON><PERSON><PERSON> Elliot\"><PERSON><PERSON><PERSON></a>, Scottish wrestler and weightlifter (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Elliot\" title=\"<PERSON><PERSON><PERSON> Elliot\"><PERSON><PERSON><PERSON></a>, Scottish wrestler and weightlifter (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON> Elliot", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, American baseball player, coach, and manager (b. 1863)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Scottish motorcycle racer (b. 1897)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish motorcycle racer (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American clarinet player and saxophonist (b. 1892)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and saxophonist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and saxophonist (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German field marshal (b. 1881)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, German commander (b. 1914)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German commander (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian businessman, founded Q<PERSON><PERSON> (b. 1879)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman, founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas</a> (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Qantas", "link": "https://wikipedia.org/wiki/Qantas"}]}, {"year": "1959", "text": "<PERSON>, Australian painter (b. 1902)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American novelist and short story writer (b. 1916)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German biologist and eugenicist (b. 1896)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, German biologist and eugenicist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German biologist and eugenicist (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English lieutenant (b. 1907)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish historian and author (b. 1898)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/V<PERSON>hel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish historian and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>hel<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish historian and author (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilhel<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, German anti-Nazi resistance fighter (b. 1882)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anti-Nazi resistance fighter (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anti-Nazi resistance fighter (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American saxophonist (b. 1928)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Cannonball_Adderley\" title=\"Cannonball Adderley\"><PERSON><PERSON></a>, American saxophonist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cannonball_Adderley\" title=\"Cannonball Adderley\"><PERSON><PERSON></a>, American saxophonist (b. 1928)", "links": [{"title": "Cannonball Adderley", "link": "https://wikipedia.org/wiki/Cannonball_Adderley"}]}, {"year": "1979", "text": "<PERSON>, English lieutenant and author (b. 1910)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian general, Victoria Cross recipient (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1981", "text": "<PERSON>, Irish republican, PIRA volunteer and Hunger Striker (b. 1957)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican, PIRA volunteer and Hunger Striker (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish republican, PIRA volunteer and Hunger Striker (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English racing driver and businessman (b. 1920)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and businessman (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and businessman (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1921)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1921)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1984", "text": "<PERSON>, American author and illustrator (b. 1928)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Croatian mathematician and physicist (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lanu%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mathematician and physicist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lanu%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mathematician and physicist (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Danilo_Blanu%C5%A1a"}]}, {"year": "1988", "text": "<PERSON>, Canadian singer-songwriter and guitarist (b. 1914)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Leclerc"}]}, {"year": "1988", "text": "<PERSON>, English actor (b. 1903)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American colonel, pilot, and astronaut (b. 1930)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Iranian religious leader and scholar (b. 1899)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Khoe<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-Khoe<PERSON>\"><PERSON></a>, Iranian religious leader and scholar (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Khoe<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Khoei\"><PERSON></a>, Iranian religious leader and scholar (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hoe<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, English physicist and academic, Nobel Prize laureate (b. 1905)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Nevill <PERSON>\"><PERSON><PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Nevill <PERSON>\">N<PERSON><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nev<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Estonian chess player and journalist (b. 1927)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and journalist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player and journalist (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>viir"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Iranian journalist (b. 1968)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian journalist (b. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Dutch-German SS officer (b. 1922)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Malian director and playwright (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Falaba_Issa_Traor%C3%A9\" title=\"Falaba Issa Traoré\"><PERSON><PERSON><PERSON></a>, Malian director and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ala<PERSON>_Is<PERSON>_Traor%C3%A9\" title=\"Falaba Issa Traoré\"><PERSON><PERSON><PERSON></a>, Malian director and playwright (b. 1930)", "links": [{"title": "Falaba Issa <PERSON>", "link": "https://wikipedia.org/wiki/Falaba_Is<PERSON>_Traor%C3%A9"}]}, {"year": "2004", "text": "<PERSON>, American painter and academic (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1922)", "links": [{"title": "Leon <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian-American actress (b. 1907)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actress (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, South African missionary and author (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African missionary and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African missionary and author (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American publisher, founded the Johnson Publishing Company (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Johnson_Publishing_Company\" title=\"Johnson Publishing Company\">Johnson Publishing Company</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, founded the <a href=\"https://wikipedia.org/wiki/Johnson_Publishing_Company\" title=\"Johnson Publishing Company\">Johnson Publishing Company</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Johnson Publishing Company", "link": "https://wikipedia.org/wiki/Johnson_Publishing_Company"}]}, {"year": "2005", "text": "<PERSON>, American baseball player and manager (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American commander, wrestler, and coach (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, wrestler, and coach (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, wrestler, and coach (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Swedish-English painter (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sj%C3%B6%C3%B6\" title=\"<PERSON>\"><PERSON></a>, Swedish-English painter (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sj%C3%B6%C3%B6\" title=\"<PERSON>\"><PERSON></a>, Swedish-English painter (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Monica_Sj%C3%B6%C3%B6"}]}, {"year": "2007", "text": "<PERSON>, Chinese journalist and politician (b. 1952)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "2007", "text": "<PERSON>, American director, producer, and screenwriter (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American golfer (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moody\"><PERSON><PERSON></a>, American golfer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Spanish footballer (b. 1983)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>-<PERSON><PERSON>, German-American physicist and academic (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, German-American physicist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>love\"><PERSON>-<PERSON></a>, German-American physicist and academic (b. 1926)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English poet and academic (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indonesian footballer and manager (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mana\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian footballer and manager (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mana\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian footballer and manager (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sur<PERSON>_<PERSON>mana"}]}, {"year": "2012", "text": "<PERSON>, German director and screenwriter (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Dutch bishop (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Mexican painter, engraver, and illustrator (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter, engraver, and illustrator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter, engraver, and illustrator (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Russian chess player (b. 1985)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American soprano and actress (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1922)", "links": [{"title": "Regina Resnik", "link": "https://wikipedia.org/wiki/Regina_Resnik"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Israeli director and producer (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Menahem_Golan\" title=\"Menahem Golan\"><PERSON><PERSON><PERSON></a>, Israeli director and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Menahem_Golan\" title=\"Menahem Golan\"><PERSON><PERSON><PERSON></a>, Israeli director and producer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>lan"}]}, {"year": "2014", "text": "<PERSON>, English-American actor (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-American actor (b. 1941)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2014", "text": "<PERSON>, Filipino archbishop (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino archbishop (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino archbishop (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Australian composer and conductor (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer and conductor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football and baseball player (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and baseball player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and baseball player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American rapper (b. 1972)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer-songwriter, guitarist, and actor (b. 1936)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Kenyan track and field athlete (b. 1990)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan track and field athlete (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan track and field athlete (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Colombian football player and manager (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian football player and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian football player and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, former Philippine senator and Mayor of Manila (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/List_of_senators_of_the_Philippines\" title=\"List of senators of the Philippines\">Philippine senator</a> and <a href=\"https://wikipedia.org/wiki/Mayor_of_Manila\" title=\"Mayor of Manila\">Mayor of Manila</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/List_of_senators_of_the_Philippines\" title=\"List of senators of the Philippines\">Philippine senator</a> and <a href=\"https://wikipedia.org/wiki/Mayor_of_Manila\" title=\"Mayor of Manila\">Mayor of Manila</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of senators of the Philippines", "link": "https://wikipedia.org/wiki/List_of_senators_of_the_Philippines"}, {"title": "Mayor of Manila", "link": "https://wikipedia.org/wiki/Mayor_of_Manila"}]}, {"year": "2021", "text": "<PERSON>, Canadian politician, 18th premier of Ontario (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 18th premier of Ontario (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, 18th premier of Ontario (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>-<PERSON>, English-Australian singer-songwriter and actress (b. 1948)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and actress (b. 1948)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American singer and songwriter (b. 1942)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, American singer and songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, American singer and songwriter (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Cameroonian basketball player and football executive (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cameroonian basketball player and football executive (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cameroonian basketball player and football executive (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Puerto Rican professional golfer (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Chi-<PERSON>_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican professional golfer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chi-<PERSON>_Rodr%C3%ADguez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican professional golfer (b. 1935)", "links": [{"title": "Chi<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chi-Chi_Rodr%C3%ADguez"}]}, {"year": "2024", "text": "<PERSON>, American politician and lobbyist (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lobbyist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and lobbyist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}