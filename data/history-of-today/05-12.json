{"date": "May 12", "url": "https://wikipedia.org/wiki/May_12", "data": {"Events": [{"year": "254", "text": "<PERSON> <PERSON> succeeds <PERSON> <PERSON>, becoming the 23rd pope of the Catholic Church, and immediately takes a stand against Novatianism.", "html": "254 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_I\" title=\"Pope Stephen <PERSON>\">Pope <PERSON> I</a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Lucius_I\" title=\"Pope Lucius I\">Pope Lucius I</a>, becoming the <a href=\"https://wikipedia.org/wiki/List_of_popes\" title=\"List of popes\">23rd</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>, and immediately takes a stand against <a href=\"https://wikipedia.org/wiki/Novatianism\" title=\"Novatianism\">Novatianism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_I\" title=\"Pope Stephen <PERSON>\">Pope <PERSON> I</a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Lucius_I\" title=\"Pope Lucius I\">Pope Lucius I</a>, becoming the <a href=\"https://wikipedia.org/wiki/List_of_popes\" title=\"List of popes\">23rd</a> <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a> of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>, and immediately takes a stand against <a href=\"https://wikipedia.org/wiki/Novatianism\" title=\"Novatianism\">Novatianism</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_I"}, {"title": "List of popes", "link": "https://wikipedia.org/wiki/List_of_popes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Novatianism", "link": "https://wikipedia.org/wiki/Novatianism"}]}, {"year": "907", "text": "<PERSON> forces Emperor <PERSON> into abdicating, ending the Tang dynasty after nearly three hundred years of rule.", "html": "907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> forces <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Tang\" title=\"Emperor <PERSON> Tang\">Emperor <PERSON></a> into abdicating, ending the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> after nearly three hundred years of rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> forces <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON></a> into abdicating, ending the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> after nearly three hundred years of rule.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emperor <PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "1191", "text": "<PERSON> of England marries <PERSON><PERSON><PERSON><PERSON> of Navarre in Cyprus; she is crowned Queen consort of England the same day.", "html": "1191 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/Berengaria_of_Navarre\" title=\"Berengaria of Navarre\">Berengaria of Navarre</a> in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>; she is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_consorts\" class=\"mw-redirect\" title=\"List of English consorts\">Queen consort of England</a> the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/Berengaria_of_Navarre\" title=\"Berengaria of Navarre\">Berengaria of Navarre</a> in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>; she is crowned <a href=\"https://wikipedia.org/wiki/List_of_English_consorts\" class=\"mw-redirect\" title=\"List of English consorts\">Queen consort of England</a> the same day.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Berengaria of Navarre", "link": "https://wikipedia.org/wiki/Berengaria_of_Navarre"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "List of English consorts", "link": "https://wikipedia.org/wiki/List_of_English_consorts"}]}, {"year": "1328", "text": "<PERSON><PERSON><PERSON>, a claimant to the papacy, is consecrated in Rome by the Bishop of Venice.", "html": "1328 - <a href=\"https://wikipedia.org/wiki/Antipope_Nicholas_V\" title=\"Antipope Nicholas V\">Antipope <PERSON> V</a>, a claimant to the <a href=\"https://wikipedia.org/wiki/Papacy\" class=\"mw-redirect\" title=\"Papa<PERSON>\">papacy</a>, is consecrated in Rome by the <a href=\"https://wikipedia.org/wiki/Patriarch_of_Venice\" title=\"Patriarch of Venice\">Bishop of Venice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Nicholas_V\" title=\"Antipope Nicholas V\">Antipope <PERSON> V</a>, a claimant to the <a href=\"https://wikipedia.org/wiki/Papacy\" class=\"mw-redirect\" title=\"Papa<PERSON>\">papacy</a>, is consecrated in Rome by the <a href=\"https://wikipedia.org/wiki/Patriarch_of_Venice\" title=\"Patriarch of Venice\">Bishop of Venice</a>.", "links": [{"title": "Antipop<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Papacy"}, {"title": "Patriarch of Venice", "link": "https://wikipedia.org/wiki/Patriarch_of_Venice"}]}, {"year": "1364", "text": "Jagiellonian University, the oldest university in Poland, is founded in Kraków.", "html": "1364 - <a href=\"https://wikipedia.org/wiki/Jagiellonian_University\" title=\"Jagiellonian University\">Jagiellonian University</a>, the oldest <a href=\"https://wikipedia.org/wiki/University\" title=\"University\">university</a> in Poland, is founded in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jagiellonian_University\" title=\"Jagiellonian University\">Jagiellonian University</a>, the oldest <a href=\"https://wikipedia.org/wiki/University\" title=\"University\">university</a> in Poland, is founded in <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>.", "links": [{"title": "Jagiellonian University", "link": "https://wikipedia.org/wiki/Jagiellonian_University"}, {"title": "University", "link": "https://wikipedia.org/wiki/University"}, {"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}]}, {"year": "1497", "text": "<PERSON> <PERSON> excommunicates <PERSON><PERSON><PERSON> Savon<PERSON>la.", "html": "1497 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" class=\"mw-redirect\" title=\"Alexander VI\"><PERSON></a> excommunicates <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" class=\"mw-redirect\" title=\"<PERSON> VI\"><PERSON></a> excommunicates <a href=\"https://wikipedia.org/wiki/Girolamo_Savonarola\" title=\"Girolamo Savonarola\"><PERSON><PERSON><PERSON> Savon<PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Savonarola"}]}, {"year": "1510", "text": "The Prince of Anhua rebellion begins when <PERSON> kills all the officials invited to a banquet and declares his intent on ousting the powerful Ming dynasty eunuch <PERSON> during the reign of the <PERSON><PERSON> Emperor.", "html": "1510 - The <a href=\"https://wikipedia.org/wiki/Prince_of_Anhua_rebellion\" title=\"Prince of Anhua rebellion\">Prince of Anhua rebellion</a> begins when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills all the officials invited to a banquet and declares his intent on ousting the powerful <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> eunuch <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Liu Jin\"><PERSON></a> during the reign of the <a href=\"https://wikipedia.org/wiki/Zhengde_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prince_of_Anhua_rebellion\" title=\"Prince of Anhua rebellion\">Prince of Anhua rebellion</a> begins when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> kills all the officials invited to a banquet and declares his intent on ousting the powerful <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a> eunuch <a href=\"https://wikipedia.org/wiki/Liu_Jin\" title=\"Liu Jin\"><PERSON></a> during the reign of the <a href=\"https://wikipedia.org/wiki/Zhengde_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a>.", "links": [{"title": "Prince of Anhua rebellion", "link": "https://wikipedia.org/wiki/Prince_of_Anhua_rebellion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>de Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1551", "text": "National University of San Marcos, the oldest university in the Americas, is founded in Lima, Peru.", "html": "1551 - <a href=\"https://wikipedia.org/wiki/National_University_of_San_Marcos\" title=\"National University of San Marcos\">National University of San Marcos</a>, the oldest university in the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a>, is founded in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima</a>, Peru.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_University_of_San_Marcos\" title=\"National University of San Marcos\">National University of San Marcos</a>, the oldest university in the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a>, is founded in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima</a>, Peru.", "links": [{"title": "National University of San Marcos", "link": "https://wikipedia.org/wiki/National_University_of_San_Marcos"}, {"title": "Americas", "link": "https://wikipedia.org/wiki/Americas"}, {"title": "Lima", "link": "https://wikipedia.org/wiki/Lima"}]}, {"year": "1588", "text": "French Wars of Religion: <PERSON> of France flees Paris after <PERSON>, Duke of Guise, enters the city and a spontaneous uprising occurs.", "html": "1588 - <a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> flees Paris after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_G<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a>, enters the city and a <a href=\"https://wikipedia.org/wiki/Day_of_the_Barricades\" title=\"Day of the Barricades\">spontaneous uprising</a> occurs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Wars_of_Religion\" title=\"French Wars of Religion\">French Wars of Religion</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> flees Paris after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guise\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Guise\"><PERSON>, Duke of Guise</a>, enters the city and a <a href=\"https://wikipedia.org/wiki/Day_of_the_Barricades\" title=\"Day of the Barricades\">spontaneous uprising</a> occurs.", "links": [{"title": "French Wars of Religion", "link": "https://wikipedia.org/wiki/French_Wars_of_Religion"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "<PERSON>, Duke of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_Guise"}, {"title": "Day of the Barricades", "link": "https://wikipedia.org/wiki/Day_of_the_Barricades"}]}, {"year": "1593", "text": "London playwright <PERSON> is arrested and tortured by the Privy Council for libel.", "html": "1593 - London playwright <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested and tortured by the <a href=\"https://wikipedia.org/wiki/Privy_Council_of_England\" title=\"Privy Council of England\">Privy Council</a> for libel.", "no_year_html": "London playwright <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested and tortured by the <a href=\"https://wikipedia.org/wiki/Privy_Council_of_England\" title=\"Privy Council of England\">Privy Council</a> for libel.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Privy Council of England", "link": "https://wikipedia.org/wiki/Privy_Council_of_England"}]}, {"year": "1743", "text": "<PERSON> of Austria is crowned Queen of Bohemia after defeating her rival, <PERSON>, Holy Roman Emperor.", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Austria is crowned Queen of <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> after defeating her rival, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON> VII, Holy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Austria is crowned Queen of <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> after defeating her rival, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bohemia", "link": "https://wikipedia.org/wiki/Bohemia"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1778", "text": "<PERSON>, count of the Principality of Reuss-Greiz, is elevated to Prince by <PERSON>, Holy Roman Emperor.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_of_Greiz\" title=\"<PERSON>, Prince <PERSON><PERSON> of Greiz\"><PERSON></a>, count of the <a href=\"https://wikipedia.org/wiki/Principality_of_Reuss-Greiz\" title=\"Principality of Reuss-Greiz\">Principality of Reuss-Greiz</a>, is elevated to <a href=\"https://wikipedia.org/wiki/Prince\" title=\"Prince\">Prince</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_<PERSON><PERSON>_of_Greiz\" title=\"<PERSON>, Prince <PERSON> of Greiz\"><PERSON></a>, count of the <a href=\"https://wikipedia.org/wiki/Principality_of_Reuss-Greiz\" title=\"Principality of Reuss-Greiz\">Principality of Reuss-Greiz</a>, is elevated to <a href=\"https://wikipedia.org/wiki/Prince\" title=\"Prince\">Prince</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>, Prince <PERSON><PERSON> of Greiz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_of_Greiz"}, {"title": "Principality of Reuss-Greiz", "link": "https://wikipedia.org/wiki/Principality_of_<PERSON><PERSON>-<PERSON>"}, {"title": "Prince", "link": "https://wikipedia.org/wiki/Prince"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1780", "text": "American Revolutionary War: In the largest defeat of the Continental Army, Charleston, South Carolina is taken by British forces.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: In the largest defeat of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>, <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Charleston\" title=\"Siege of Charleston\">is taken</a> by British forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: In the largest defeat of the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>, <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Charleston\" title=\"Siege of Charleston\">is taken</a> by British forces.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}, {"title": "Siege of Charleston", "link": "https://wikipedia.org/wiki/Siege_of_Charleston"}]}, {"year": "1797", "text": "War of the First Coalition: <PERSON> conquers Venice.", "html": "1797 - <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Bonaparte\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Bonaparte\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>.", "links": [{"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venice", "link": "https://wikipedia.org/wiki/Venice"}]}, {"year": "1808", "text": "Finnish War: Swedish-Finnish troops, led by Captain <PERSON>, conquer the city of Kuopio from Russians after the Battle of Kuopio.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Swedish-Finnish troops, led by Captain <PERSON>, conquer the city of <a href=\"https://wikipedia.org/wiki/Kuopio\" title=\"Kuopio\">Ku<PERSON>io</a> from Russians after the <a href=\"https://wikipedia.org/wiki/Battle_of_Kuopio\" title=\"Battle of Kuopio\">Battle of Kuopio</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Swedish-Finnish troops, led by Captain <PERSON>, conquer the city of <a href=\"https://wikipedia.org/wiki/Kuopio\" title=\"Kuopio\">Ku<PERSON>io</a> from Russians after the <a href=\"https://wikipedia.org/wiki/Battle_of_Kuopio\" title=\"Battle of Kuopio\">Battle of Kuopio</a>.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>opio"}, {"title": "Battle of Kuopio", "link": "https://wikipedia.org/wiki/Battle_of_Ku<PERSON>io"}]}, {"year": "1821", "text": "The first major battle of the Greek War of Independence against the Turks is fought in Valtetsi.", "html": "1821 - The first major battle of the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turks</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Valtetsi\" title=\"Battle of Valtetsi\">fought</a> in <a href=\"https://wikipedia.org/wiki/Valtetsi\" title=\"Valtetsi\">Valtetsi</a>.", "no_year_html": "The first major battle of the <a href=\"https://wikipedia.org/wiki/Greek_War_of_Independence\" title=\"Greek War of Independence\">Greek War of Independence</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Turks</a> is <a href=\"https://wikipedia.org/wiki/Battle_of_Valtetsi\" title=\"Battle of Valtetsi\">fought</a> in <a href=\"https://wikipedia.org/wiki/Valtetsi\" title=\"Valtetsi\">Valtetsi</a>.", "links": [{"title": "Greek War of Independence", "link": "https://wikipedia.org/wiki/Greek_War_of_Independence"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Valtetsi", "link": "https://wikipedia.org/wiki/Battle_of_Valtetsi"}, {"title": "Valtetsi", "link": "https://wikipedia.org/wiki/Valtetsi"}]}, {"year": "1846", "text": "The Donner Party of pioneers departs Independence, Missouri for California, on what will become a year-long journey of hardship and cannibalism.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Donner_Party\" title=\"Donner Party\">Donner Party</a> of <a href=\"https://wikipedia.org/wiki/Settler\" title=\"Settler\">pioneers</a> departs <a href=\"https://wikipedia.org/wiki/Independence,_Missouri\" title=\"Independence, Missouri\">Independence, Missouri</a> for <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, on what will become a year-long journey of hardship and <a href=\"https://wikipedia.org/wiki/Human_cannibalism\" title=\"Human cannibalism\">cannibalism</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Donner_Party\" title=\"Donner Party\">Donner Party</a> of <a href=\"https://wikipedia.org/wiki/Settler\" title=\"Settler\">pioneers</a> departs <a href=\"https://wikipedia.org/wiki/Independence,_Missouri\" title=\"Independence, Missouri\">Independence, Missouri</a> for <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, on what will become a year-long journey of hardship and <a href=\"https://wikipedia.org/wiki/Human_cannibalism\" title=\"Human cannibalism\">cannibalism</a>.", "links": [{"title": "Donner Party", "link": "https://wikipedia.org/wiki/<PERSON>ner_Party"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Independence, Missouri", "link": "https://wikipedia.org/wiki/Independence,_Missouri"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Human cannibalism", "link": "https://wikipedia.org/wiki/Human_cannibalism"}]}, {"year": "1862", "text": "American Civil War: Union Army troops occupy Baton Rouge, Louisiana.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> troops occupy <a href=\"https://wikipedia.org/wiki/Baton_Rouge,_Louisiana\" title=\"Baton Rouge, Louisiana\">Baton Rouge, Louisiana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a> troops occupy <a href=\"https://wikipedia.org/wiki/Baton_Rouge,_Louisiana\" title=\"Baton Rouge, Louisiana\">Baton Rouge, Louisiana</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Baton Rouge, Louisiana", "link": "https://wikipedia.org/wiki/Baton_Rouge,_Louisiana"}]}, {"year": "1863", "text": "American Civil War: Battle of Raymond: Two divisions of <PERSON>'s XVII Corps turn the left wing of Confederate General <PERSON>'s defensive line on Fourteen Mile Creek, opening up the interior of Mississippi to the Union Army during the Vicksburg Campaign.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Raymond\" title=\"Battle of Raymond\">Battle of Raymond</a>: Two divisions of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/XVII_Corps_(Union_Army)\" class=\"mw-redirect\" title=\"XVII Corps (Union Army)\">XVII Corps</a> turn the left wing of <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/John_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>'s defensive line on <a href=\"https://wikipedia.org/wiki/Fourteen_Mile_Creek\" title=\"Fourteen Mile Creek\">Fourteen Mile Creek</a>, opening up the interior of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union Army</a> during the <a href=\"https://wikipedia.org/wiki/Vicksburg_Campaign\" class=\"mw-redirect\" title=\"Vicksburg Campaign\">Vicksburg Campaign</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Raymond\" title=\"Battle of Raymond\">Battle of Raymond</a>: Two divisions of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/XVII_Corps_(Union_Army)\" class=\"mw-redirect\" title=\"XVII Corps (Union Army)\">XVII Corps</a> turn the left wing of <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>'s defensive line on <a href=\"https://wikipedia.org/wiki/Fourteen_Mile_Creek\" title=\"Fourteen Mile Creek\">Fourteen Mile Creek</a>, opening up the interior of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union Army</a> during the <a href=\"https://wikipedia.org/wiki/Vicksburg_Campaign\" class=\"mw-redirect\" title=\"Vicksburg Campaign\">Vicksburg Campaign</a>.", "links": [{"title": "Battle of Raymond", "link": "https://wikipedia.org/wiki/Battle_of_Raymond"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "XVII Corps (Union Army)", "link": "https://wikipedia.org/wiki/XVII_Corps_(Union_Army)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Fourteen Mile Creek", "link": "https://wikipedia.org/wiki/Fourteen_Mile_Creek"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Vicksburg Campaign", "link": "https://wikipedia.org/wiki/Vicksburg_Campaign"}]}, {"year": "1864", "text": "American Civil War: The Battle of Spotsylvania Court House: Union troops assault a Confederate salient known as the \"Mule Shoe\", with some of the fiercest fighting of the war, much of it hand-to-hand combat, occurring at \"the Bloody Angle\" on the northwest.", "html": "1864 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House\" title=\"Battle of Spotsylvania Court House\">Battle of Spotsylvania Court House</a>: Union troops assault a Confederate salient known as the \"Mule Shoe\", with some of the fiercest fighting of the war, much of it hand-to-hand combat, occurring at \"the Bloody Angle\" on the northwest.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House\" title=\"Battle of Spotsylvania Court House\">Battle of Spotsylvania Court House</a>: Union troops assault a Confederate salient known as the \"Mule Shoe\", with some of the fiercest fighting of the war, much of it hand-to-hand combat, occurring at \"the Bloody Angle\" on the northwest.", "links": [{"title": "Battle of Spotsylvania Court House", "link": "https://wikipedia.org/wiki/Battle_of_Spotsylvania_Court_House"}]}, {"year": "1865", "text": "American Civil War: The Battle of Palmito Ranch: The first day of the last major land action to take place during the Civil War, resulting in a Confederate victory.", "html": "1865 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Palmito_Ranch\" title=\"Battle of Palmito Ranch\">Battle of Palmito Ranch</a>: The first day of the last major land action to take place during the Civil War, resulting in a Confederate victory.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Palmito_Ranch\" title=\"Battle of Palmito Ranch\">Battle of Palmito Ranch</a>: The first day of the last major land action to take place during the Civil War, resulting in a Confederate victory.", "links": [{"title": "Battle of Palmito Ranch", "link": "https://wikipedia.org/wiki/Battle_of_Palmito_Ranch"}]}, {"year": "1870", "text": "The Manitoba Act is given the Royal Assent, paving the way for Manitoba to become a province of Canada on July 15.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/Manitoba_Act\" class=\"mw-redirect\" title=\"Manitoba Act\">Manitoba Act</a> is given the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a>, paving the way for <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> to become a <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">province of Canada</a> on <a href=\"https://wikipedia.org/wiki/July_15\" title=\"July 15\">July 15</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Manitoba_Act\" class=\"mw-redirect\" title=\"Manitoba Act\">Manitoba Act</a> is given the <a href=\"https://wikipedia.org/wiki/Royal_Assent\" class=\"mw-redirect\" title=\"Royal Assent\">Royal Assent</a>, paving the way for <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> to become a <a href=\"https://wikipedia.org/wiki/Provinces_and_territories_of_Canada\" title=\"Provinces and territories of Canada\">province of Canada</a> on <a href=\"https://wikipedia.org/wiki/July_15\" title=\"July 15\">July 15</a>.", "links": [{"title": "Manitoba Act", "link": "https://wikipedia.org/wiki/Manitoba_Act"}, {"title": "Royal Assent", "link": "https://wikipedia.org/wiki/Royal_Assent"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}, {"title": "Provinces and territories of Canada", "link": "https://wikipedia.org/wiki/Provinces_and_territories_of_Canada"}, {"title": "July 15", "link": "https://wikipedia.org/wiki/July_15"}]}, {"year": "1881", "text": "In North Africa, Tunisia becomes a French protectorate.", "html": "1881 - In North Africa, <a href=\"https://wikipedia.org/wiki/Ottoman_Tunisia\" title=\"Ottoman Tunisia\">Tunisia</a> becomes a <a href=\"https://wikipedia.org/wiki/French_protectorate_of_Tunisia\" title=\"French protectorate of Tunisia\">French protectorate</a>.", "no_year_html": "In North Africa, <a href=\"https://wikipedia.org/wiki/Ottoman_Tunisia\" title=\"Ottoman Tunisia\">Tunisia</a> becomes a <a href=\"https://wikipedia.org/wiki/French_protectorate_of_Tunisia\" title=\"French protectorate of Tunisia\">French protectorate</a>.", "links": [{"title": "Ottoman Tunisia", "link": "https://wikipedia.org/wiki/Ottoman_Tunisia"}, {"title": "French protectorate of Tunisia", "link": "https://wikipedia.org/wiki/French_protectorate_of_Tunisia"}]}, {"year": "1885", "text": "North-West Rebellion: The four-day Battle of Batoche, pitting rebel <PERSON><PERSON><PERSON> against the Canadian government, comes to an end with a decisive rebel defeat.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a>: The four-day <a href=\"https://wikipedia.org/wiki/Battle_of_Batoche\" title=\"Battle of Batoche\">Battle of Batoche</a>, pitting rebel <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> against the Canadian government, comes to an end with a decisive rebel defeat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a>: The four-day <a href=\"https://wikipedia.org/wiki/Battle_of_Batoche\" title=\"Battle of Batoche\">Battle of Batoche</a>, pitting rebel <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> against the Canadian government, comes to an end with a decisive rebel defeat.", "links": [{"title": "North-West Rebellion", "link": "https://wikipedia.org/wiki/North-West_Rebellion"}, {"title": "Battle of Batoche", "link": "https://wikipedia.org/wiki/Battle_of_Batoche"}, {"title": "<PERSON><PERSON><PERSON> people (Canada)", "link": "https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)"}]}, {"year": "1926", "text": "The Italian-built airship <PERSON><PERSON> becomes the first vessel to fly over the North Pole.", "html": "1926 - The Italian-built airship <i><a href=\"https://wikipedia.org/wiki/Norge_(airship)\" title=\"Norge (airship)\"><PERSON><PERSON></a></i> becomes the first vessel to fly over the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "no_year_html": "The Italian-built airship <i><a href=\"https://wikipedia.org/wiki/Norge_(airship)\" title=\"Norge (airship)\">Nor<PERSON></a></i> becomes the first vessel to fly over the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>.", "links": [{"title": "Norge (airship)", "link": "https://wikipedia.org/wiki/Norge_(airship)"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1926", "text": "The 1926 United Kingdom general strike ends.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/1926_United_Kingdom_general_strike\" title=\"1926 United Kingdom general strike\">1926 United Kingdom general strike</a> ends.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1926_United_Kingdom_general_strike\" title=\"1926 United Kingdom general strike\">1926 United Kingdom general strike</a> ends.", "links": [{"title": "1926 United Kingdom general strike", "link": "https://wikipedia.org/wiki/1926_United_Kingdom_general_strike"}]}, {"year": "1932", "text": "Ten weeks after his abduction, <PERSON>, the infant son of <PERSON>, is found dead near Hopewell, New Jersey, just a few miles from the <PERSON><PERSON><PERSON><PERSON>' home.", "html": "1932 - Ten weeks after his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\">abduction</a>, <PERSON>, the infant son of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is found dead near <a href=\"https://wikipedia.org/wiki/Hopewell,_New_Jersey\" title=\"Hopewell, New Jersey\">Hopewell, New Jersey</a>, just a few miles from the Lind<PERSON><PERSON>' home.", "no_year_html": "Ten weeks after his <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping\" title=\"<PERSON><PERSON><PERSON><PERSON> kidnapping\">abduction</a>, <PERSON>, the infant son of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is found dead near <a href=\"https://wikipedia.org/wiki/Hopewell,_New_Jersey\" title=\"Hopewell, New Jersey\">Hopewell, New Jersey</a>, just a few miles from the Lind<PERSON><PERSON>' home.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> kidnapping", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_kidnapping"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hopewell, New Jersey", "link": "https://wikipedia.org/wiki/Hopewell,_New_Jersey"}]}, {"year": "1933", "text": "The Agricultural Adjustment Act, which restricts agricultural production through government purchase of livestock for slaughter and paying subsidies to farmers when they remove land from planting, is signed into law by President <PERSON>.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Agricultural_Adjustment_Act\" title=\"Agricultural Adjustment Act\">Agricultural Adjustment Act</a>, which restricts agricultural production through government purchase of <a href=\"https://wikipedia.org/wiki/Livestock\" title=\"Livestock\">livestock</a> for slaughter and paying <a href=\"https://wikipedia.org/wiki/Subsidy\" title=\"Subsidy\">subsidies</a> to farmers when they remove land from planting, is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Agricultural_Adjustment_Act\" title=\"Agricultural Adjustment Act\">Agricultural Adjustment Act</a>, which restricts agricultural production through government purchase of <a href=\"https://wikipedia.org/wiki/Livestock\" title=\"Livestock\">livestock</a> for slaughter and paying <a href=\"https://wikipedia.org/wiki/Subsidy\" title=\"Subsidy\">subsidies</a> to farmers when they remove land from planting, is signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Agricultural Adjustment Act", "link": "https://wikipedia.org/wiki/Agricultural_Adjustment_Act"}, {"title": "Livestock", "link": "https://wikipedia.org/wiki/Livestock"}, {"title": "Subsidy", "link": "https://wikipedia.org/wiki/Subsidy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "President <PERSON> signs legislation creating the Federal Emergency Relief Administration, the predecessor of the Federal Emergency Management Agency.", "html": "1933 - President <PERSON> signs legislation creating the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration\" title=\"Federal Emergency Relief Administration\">Federal Emergency Relief Administration</a>, the predecessor of the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Management_Agency\" title=\"Federal Emergency Management Agency\">Federal Emergency Management Agency</a>.", "no_year_html": "President <PERSON> signs legislation creating the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration\" title=\"Federal Emergency Relief Administration\">Federal Emergency Relief Administration</a>, the predecessor of the <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Management_Agency\" title=\"Federal Emergency Management Agency\">Federal Emergency Management Agency</a>.", "links": [{"title": "Federal Emergency Relief Administration", "link": "https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration"}, {"title": "Federal Emergency Management Agency", "link": "https://wikipedia.org/wiki/Federal_Emergency_Management_Agency"}]}, {"year": "1937", "text": "The Duke and Duchess of York are crowned as King <PERSON> and Queen <PERSON> of the United Kingdom in Westminster Abbey.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VI_and_<PERSON>\" title=\"Coronation of <PERSON> and <PERSON>\">Duke and Duchess of York are crowned</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VI\">King <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen Elizabeth The Queen Mother\">Queen <PERSON></a> of the United Kingdom in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_VI_and_<PERSON>\" title=\"Coronation of <PERSON> VI and <PERSON>\">Duke and Duchess of York are crowned</a> as <a href=\"https://wikipedia.org/wiki/<PERSON>_VI\" title=\"<PERSON> VI\">King <PERSON> VI</a> and <a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother\" title=\"Queen Elizabeth The Queen Mother\">Queen <PERSON></a> of the United Kingdom in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "Coronation of <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_VI_and_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Queen <PERSON> The Queen Mother", "link": "https://wikipedia.org/wiki/Queen_Elizabeth_The_Queen_Mother"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1941", "text": "<PERSON> presents the Z3, the world's first working programmable, fully automatic computer, in Berlin.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3</a>, the world's first working programmable, fully automatic computer, in Berlin.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents the <a href=\"https://wikipedia.org/wiki/Z3_(computer)\" title=\"Z3 (computer)\">Z3</a>, the world's first working programmable, fully automatic computer, in Berlin.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Z3 (computer)", "link": "https://wikipedia.org/wiki/Z3_(computer)"}]}, {"year": "1942", "text": "World War II: Second Battle of Kharkov: In eastern Ukraine, Red Army forces under Marshal <PERSON><PERSON><PERSON> launch a major offensive from the Izium bridgehead, only to be encircled and destroyed by the troops of Army Group South two weeks later.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Kharkov\" title=\"Second Battle of Kharkov\">Second Battle of Kharkov</a>: In eastern Ukraine, <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> forces under Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> launch a major offensive from the <a href=\"https://wikipedia.org/wiki/Izium\" title=\"Izium\">Izium</a> bridgehead, only to be encircled and destroyed by the troops of <a href=\"https://wikipedia.org/wiki/Army_Group_South\" title=\"Army Group South\">Army Group South</a> two weeks later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Kharkov\" title=\"Second Battle of Kharkov\">Second Battle of Kharkov</a>: In eastern Ukraine, <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> forces under Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> launch a major offensive from the <a href=\"https://wikipedia.org/wiki/Izium\" title=\"Izium\">Izium</a> bridgehead, only to be encircled and destroyed by the troops of <a href=\"https://wikipedia.org/wiki/Army_Group_South\" title=\"Army Group South\">Army Group South</a> two weeks later.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Second Battle of Kharkov", "link": "https://wikipedia.org/wiki/Second_Battle_of_Kharkov"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Izium", "link": "https://wikipedia.org/wiki/Izium"}, {"title": "Army Group South", "link": "https://wikipedia.org/wiki/Army_Group_South"}]}, {"year": "1942", "text": "World War II: The U.S. tanker SS Virginia is torpedoed in the mouth of the Mississippi River by the German submarine U-507.", "html": "1942 - World War II: The U.S. <a href=\"https://wikipedia.org/wiki/Tanker_(ship)\" title=\"Tanker (ship)\">tanker</a> SS <i>Virginia</i> is torpedoed in the mouth of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> by the <a href=\"https://wikipedia.org/wiki/German_submarine_U-507\" title=\"German submarine U-507\">German submarine <i>U-507</i></a>.", "no_year_html": "World War II: The U.S. <a href=\"https://wikipedia.org/wiki/Tanker_(ship)\" title=\"Tanker (ship)\">tanker</a> SS <i>Virginia</i> is torpedoed in the mouth of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> by the <a href=\"https://wikipedia.org/wiki/German_submarine_U-507\" title=\"German submarine U-507\">German submarine <i>U-507</i></a>.", "links": [{"title": "Tanker (ship)", "link": "https://wikipedia.org/wiki/Tanker_(ship)"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "German submarine U-507", "link": "https://wikipedia.org/wiki/German_submarine_U-507"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Queen regnant of the Kingdom of the Netherlands, cedes the throne to her daughter <PERSON>.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands\" title=\"Wilhelmina of the Netherlands\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_Netherlands\" title=\"Monarchy of the Netherlands\">Queen regnant of the Kingdom of the Netherlands</a>, cedes the throne to her daughter <a href=\"https://wikipedia.org/wiki/Juliana_of_the_Netherlands\" title=\"Juliana of the Netherlands\">Juliana</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands\" title=\"Wilhelmina of the Netherlands\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_Netherlands\" title=\"Monarchy of the Netherlands\">Queen regnant of the Kingdom of the Netherlands</a>, cedes the throne to her daughter <a href=\"https://wikipedia.org/wiki/Juliana_of_the_Netherlands\" title=\"Juliana of the Netherlands\">Juliana</a>.", "links": [{"title": "Wilhelmina of the Netherlands", "link": "https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands"}, {"title": "Monarchy of the Netherlands", "link": "https://wikipedia.org/wiki/Monarchy_of_the_Netherlands"}, {"title": "Juliana of the Netherlands", "link": "https://wikipedia.org/wiki/Juliana_of_the_Netherlands"}]}, {"year": "1949", "text": "Cold War: The Soviet Union lifts its blockade of Berlin.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> lifts its <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">blockade of Berlin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> lifts its <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">blockade of Berlin</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Berlin Blockade", "link": "https://wikipedia.org/wiki/Berlin_Blockade"}]}, {"year": "1965", "text": "The Soviet spacecraft Luna 5 crashes on the Moon.", "html": "1965 - The Soviet <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> <i><a href=\"https://wikipedia.org/wiki/Luna_5\" title=\"Luna 5\">Luna 5</a></i> crashes on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "The Soviet <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a> <i><a href=\"https://wikipedia.org/wiki/Luna_5\" title=\"Luna 5\">Luna 5</a></i> crashes on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}, {"title": "Luna 5", "link": "https://wikipedia.org/wiki/Luna_5"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1968", "text": "Vietnam War: North Vietnamese and Viet Cong forces attack Australian troops defending Fire Support Base Coral.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese</a> and <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> forces attack <a href=\"https://wikipedia.org/wiki/1st_Australian_Task_Force\" title=\"1st Australian Task Force\">Australian troops</a> defending <a href=\"https://wikipedia.org/wiki/Battle_of_Coral%E2%80%93Balmoral\" title=\"Battle of Coral-Balmoral\">Fire Support Base Coral</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese</a> and <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> forces attack <a href=\"https://wikipedia.org/wiki/1st_Australian_Task_Force\" title=\"1st Australian Task Force\">Australian troops</a> defending <a href=\"https://wikipedia.org/wiki/Battle_of_Coral%E2%80%93Balmoral\" title=\"Battle of Coral-Balmoral\">Fire Support Base Coral</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "1st Australian Task Force", "link": "https://wikipedia.org/wiki/1st_Australian_Task_Force"}, {"title": "Battle of Coral-Balmoral", "link": "https://wikipedia.org/wiki/Battle_of_Coral%E2%80%93Balmoral"}]}, {"year": "1975", "text": "Indochina Wars: Democratic Kampuchea naval forces capture the SS Mayaguez.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Indochina_Wars\" class=\"mw-redirect\" title=\"Indochina Wars\">Indochina Wars</a>: <a href=\"https://wikipedia.org/wiki/Democratic_Kampuchea\" title=\"Democratic Kampuchea\">Democratic Kampuchea</a> naval forces <a href=\"https://wikipedia.org/wiki/Mayaguez_incident\" title=\"Mayaguez incident\">capture</a> the <a href=\"https://wikipedia.org/wiki/SS_Mayaguez\" title=\"SS Mayaguez\">SS Mayaguez</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indochina_Wars\" class=\"mw-redirect\" title=\"Indochina Wars\">Indochina Wars</a>: <a href=\"https://wikipedia.org/wiki/Democratic_Kampuchea\" title=\"Democratic Kampuchea\">Democratic Kampuchea</a> naval forces <a href=\"https://wikipedia.org/wiki/Mayaguez_incident\" title=\"Mayaguez incident\">capture</a> the <a href=\"https://wikipedia.org/wiki/SS_Mayaguez\" title=\"SS Mayaguez\">SS Mayaguez</a>.", "links": [{"title": "Indochina Wars", "link": "https://wikipedia.org/wiki/Indochina_Wars"}, {"title": "Democratic Kampuchea", "link": "https://wikipedia.org/wiki/Democratic_Kampuchea"}, {"title": "Mayaguez incident", "link": "https://wikipedia.org/wiki/Mayaguez_incident"}, {"title": "SS Mayaguez", "link": "https://wikipedia.org/wiki/SS_Mayaguez"}]}, {"year": "1978", "text": "In Zaire, rebels occupy the city of Kolwezi, the mining center of the province of Shaba (now known as Katanga); the local government asks the US, France and Belgium to restore order.", "html": "1978 - In <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a>, rebels occupy the city of <a href=\"https://wikipedia.org/wiki/Kolwezi\" title=\"Kolwezi\">Kolwezi</a>, the mining center of the province of <a href=\"https://wikipedia.org/wiki/Katanga_Province\" title=\"Katanga Province\">Shaba</a> (now known as Katanga); the local government asks the US, France and Belgium to restore order.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a>, rebels occupy the city of <a href=\"https://wikipedia.org/wiki/Kolwezi\" title=\"Kolwezi\">Kolwezi</a>, the mining center of the province of <a href=\"https://wikipedia.org/wiki/Katanga_Province\" title=\"Katanga Province\">Shaba</a> (now known as Katanga); the local government asks the US, France and Belgium to restore order.", "links": [{"title": "Zaire", "link": "https://wikipedia.org/wiki/Zaire"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Katanga Province", "link": "https://wikipedia.org/wiki/Katanga_Province"}]}, {"year": "1982", "text": "During a procession outside the shrine of the Virgin <PERSON> in Fátima, Portugal, security guards overpower <PERSON> before he can attack <PERSON> <PERSON> with a bayonet.", "html": "1982 - During a procession outside the shrine of the <a href=\"https://wikipedia.org/wiki/Virgin_Mary\" class=\"mw-redirect\" title=\"Virgin Mary\"><PERSON> Mary</a> in <a href=\"https://wikipedia.org/wiki/F%C3%<PERSON><PERSON><PERSON>,_Portugal\" title=\"Fátima, Portugal\">Fátima, Portugal</a>, security guards overpower <a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_Fern%C3%A1ndez_y_K<PERSON>hn\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a> before he can attack <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> II</a> with a <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayonet</a>.", "no_year_html": "During a procession outside the shrine of the <a href=\"https://wikipedia.org/wiki/Virgin_Mary\" class=\"mw-redirect\" title=\"Virgin Mary\"><PERSON> Mary</a> in <a href=\"https://wikipedia.org/wiki/F%C3%<PERSON><PERSON><PERSON>,_Portugal\" title=\"Fátima, Portugal\">Fátima, Portugal</a>, security guards overpower <a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_Fern%C3%A1ndez_y_K<PERSON>hn\" title=\"<PERSON> y <PERSON>\"><PERSON> y <PERSON></a> before he can attack <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> II</a> with a <a href=\"https://wikipedia.org/wiki/Bayonet\" title=\"Bayonet\">bayonet</a>.", "links": [{"title": "Virgin Mary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fátima, Portugal", "link": "https://wikipedia.org/wiki/F%C3%A1tima,_Portugal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mar%C3%ADa_Fern%C3%A1ndez_y_K<PERSON>hn"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bayonet", "link": "https://wikipedia.org/wiki/Bayonet"}]}, {"year": "1989", "text": "The San Bernardino train disaster kills four people, only to be followed a week later by an underground gasoline pipeline explosion, which kills two more people.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/San_Bernardino_train_disaster\" title=\"San Bernardino train disaster\">San Bernardino train disaster</a> kills four people, only to be followed a week later by an underground gasoline pipeline explosion, which kills two more people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/San_Bernardino_train_disaster\" title=\"San Bernardino train disaster\">San Bernardino train disaster</a> kills four people, only to be followed a week later by an underground gasoline pipeline explosion, which kills two more people.", "links": [{"title": "San Bernardino train disaster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_train_disaster"}]}, {"year": "1998", "text": "Four students are shot at Trisakti University, leading to widespread riots and the fall of Suharto.", "html": "1998 - Four students <a href=\"https://wikipedia.org/wiki/Trisakti_shootings\" title=\"Trisakti shootings\">are shot at Trisakti University</a>, leading to <a href=\"https://wikipedia.org/wiki/May_1998_riots_of_Indonesia\" title=\"May 1998 riots of Indonesia\">widespread riots</a> and the fall of <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suharto\">Suharto</a>.", "no_year_html": "Four students <a href=\"https://wikipedia.org/wiki/Trisakti_shootings\" title=\"Trisakti shootings\">are shot at Trisakti University</a>, leading to <a href=\"https://wikipedia.org/wiki/May_1998_riots_of_Indonesia\" title=\"May 1998 riots of Indonesia\">widespread riots</a> and the fall of <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suharto\">Suharto</a>.", "links": [{"title": "Trisakti shootings", "link": "https://wikipedia.org/wiki/Trisakti_shootings"}, {"title": "May 1998 riots of Indonesia", "link": "https://wikipedia.org/wiki/May_1998_riots_of_Indonesia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Su<PERSON>o"}]}, {"year": "2002", "text": "Former US President <PERSON> arrives in Cuba for a five-day visit with <PERSON><PERSON>, becoming the first President of the United States, in or out of office, to visit the island since the Cuban Revolution.", "html": "2002 - Former US President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> for a five-day visit with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, in or out of office, to visit the island since the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>.", "no_year_html": "Former US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> for a five-day visit with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, becoming the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>, in or out of office, to visit the island since the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Cuban Revolution", "link": "https://wikipedia.org/wiki/Cuban_Revolution"}]}, {"year": "2003", "text": "The Riyadh compound bombings in Saudi Arabia, carried out by al-Qaeda, kill 39 people.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Riyadh_compound_bombings\" title=\"Riyadh compound bombings\">Riyadh compound bombings</a> in Saudi Arabia, carried out by <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>, kill 39 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Riyadh_compound_bombings\" title=\"Riyadh compound bombings\">Riyadh compound bombings</a> in Saudi Arabia, carried out by <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a>, kill 39 people.", "links": [{"title": "Riyadh compound bombings", "link": "https://wikipedia.org/wiki/Riyadh_compound_bombings"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "2006", "text": "Mass unrest by the Primeiro Comando da Capital begins in São Paulo (Brazil), leaving at least 150 dead.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence\" class=\"mw-redirect\" title=\"May 2006 São Paulo violence\">Mass unrest</a> by the <a href=\"https://wikipedia.org/wiki/Primeiro_Comando_da_Capital\" title=\"Primeiro Comando da Capital\">Primeiro Comando da Capital</a> begins in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a> (Brazil), leaving at least 150 dead.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence\" class=\"mw-redirect\" title=\"May 2006 São Paulo violence\">Mass unrest</a> by the <a href=\"https://wikipedia.org/wiki/Primeiro_Comando_da_Capital\" title=\"Primeiro Comando da Capital\">Primeiro Comando da Capital</a> begins in <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo\" title=\"São Paulo\">São Paulo</a> (Brazil), leaving at least 150 dead.", "links": [{"title": "May 2006 São Paulo violence", "link": "https://wikipedia.org/wiki/May_2006_S%C3%A3o_Paulo_violence"}, {"title": "Primeiro Comando da Capital", "link": "https://wikipedia.org/wiki/Primeiro_Comando_da_Capital"}, {"title": "São Paulo", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo"}]}, {"year": "2006", "text": "Iranian Azeris interpret a cartoon published in an Iranian magazine as insulting, resulting in massive riots throughout the country.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Iranian_Azeris\" class=\"mw-redirect\" title=\"Iranian Azeris\">Iranian Azeris</a> interpret a <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> published in an Iranian magazine as insulting, <a href=\"https://wikipedia.org/wiki/Iran_newspaper_cockroach_cartoon_controversy\" title=\"Iran newspaper cockroach cartoon controversy\">resulting in massive riots</a> throughout the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iranian_Azeris\" class=\"mw-redirect\" title=\"Iranian Azeris\">Iranian Azeris</a> interpret a <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> published in an Iranian magazine as insulting, <a href=\"https://wikipedia.org/wiki/Iran_newspaper_cockroach_cartoon_controversy\" title=\"Iran newspaper cockroach cartoon controversy\">resulting in massive riots</a> throughout the country.", "links": [{"title": "Iranian Azeris", "link": "https://wikipedia.org/wiki/Iranian_Azeris"}, {"title": "Cartoon", "link": "https://wikipedia.org/wiki/Cartoon"}, {"title": "Iran newspaper cockroach cartoon controversy", "link": "https://wikipedia.org/wiki/Iran_newspaper_cockroach_cartoon_controversy"}]}, {"year": "2008", "text": "An earthquake (measuring around 8.0 magnitude) occurs in Sichuan, China, killing over 69,000 people.", "html": "2008 - An <a href=\"https://wikipedia.org/wiki/2008_Wenchuan_earthquake\" class=\"mw-redirect\" title=\"2008 Wenchuan earthquake\">earthquake</a> (measuring around 8.0 magnitude) occurs in <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China, killing over 69,000 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2008_Wenchuan_earthquake\" class=\"mw-redirect\" title=\"2008 Wenchuan earthquake\">earthquake</a> (measuring around 8.0 magnitude) occurs in <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>, China, killing over 69,000 people.", "links": [{"title": "2008 Wenchuan earthquake", "link": "https://wikipedia.org/wiki/2008_Wenchuan_earthquake"}, {"title": "Sichuan", "link": "https://wikipedia.org/wiki/Sichuan"}]}, {"year": "2008", "text": "U.S. Immigration and Customs Enforcement conducts the largest-ever raid of a workplace in Postville, Iowa, arresting nearly 400 immigrants for identity theft and document fraud.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/U.S._Immigration_and_Customs_Enforcement\" title=\"U.S. Immigration and Customs Enforcement\">U.S. Immigration and Customs Enforcement</a> conducts the <a href=\"https://wikipedia.org/wiki/Postville_Raid\" class=\"mw-redirect\" title=\"Postville Raid\">largest-ever raid</a> of a workplace in <a href=\"https://wikipedia.org/wiki/Postville,_Iowa\" title=\"Postville, Iowa\">Postville, Iowa</a>, arresting nearly 400 <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrants</a> for <a href=\"https://wikipedia.org/wiki/Identity_theft\" title=\"Identity theft\">identity theft</a> and document fraud.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U.S._Immigration_and_Customs_Enforcement\" title=\"U.S. Immigration and Customs Enforcement\">U.S. Immigration and Customs Enforcement</a> conducts the <a href=\"https://wikipedia.org/wiki/Postville_Raid\" class=\"mw-redirect\" title=\"Postville Raid\">largest-ever raid</a> of a workplace in <a href=\"https://wikipedia.org/wiki/Postville,_Iowa\" title=\"Postville, Iowa\">Postville, Iowa</a>, arresting nearly 400 <a href=\"https://wikipedia.org/wiki/Immigration_to_the_United_States\" title=\"Immigration to the United States\">immigrants</a> for <a href=\"https://wikipedia.org/wiki/Identity_theft\" title=\"Identity theft\">identity theft</a> and document fraud.", "links": [{"title": "U.S. Immigration and Customs Enforcement", "link": "https://wikipedia.org/wiki/U.S._Immigration_and_Customs_Enforcement"}, {"title": "Postville Raid", "link": "https://wikipedia.org/wiki/Postville_Raid"}, {"title": "Postville, Iowa", "link": "https://wikipedia.org/wiki/Postville,_Iowa"}, {"title": "Immigration to the United States", "link": "https://wikipedia.org/wiki/Immigration_to_the_United_States"}, {"title": "Identity theft", "link": "https://wikipedia.org/wiki/Identity_theft"}]}, {"year": "2010", "text": "Afriqiyah Airways Flight 771 crashes on final approach to Tripoli International Airport in Tripoli, Libya, killing 103 out of the 104 people on board.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Afriqiyah_Airways_Flight_771\" title=\"Afriqiyah Airways Flight 771\">Afriqiyah Airways Flight 771</a> crashes on final approach to <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a> in Tripoli, Libya, killing 103 out of the 104 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afriqiyah_Airways_Flight_771\" title=\"Afriqiyah Airways Flight 771\">Afriqiyah Airways Flight 771</a> crashes on final approach to <a href=\"https://wikipedia.org/wiki/Tripoli_International_Airport\" title=\"Tripoli International Airport\">Tripoli International Airport</a> in Tripoli, Libya, killing 103 out of the 104 people on board.", "links": [{"title": "Afriqiyah Airways Flight 771", "link": "https://wikipedia.org/wiki/Afriqiyah_Airways_Flight_771"}, {"title": "Tripoli International Airport", "link": "https://wikipedia.org/wiki/Tripoli_International_Airport"}]}, {"year": "2015", "text": "A train derailment in Philadelphia, United States, kills eight people and injures more than 200.", "html": "2015 - A <a href=\"https://wikipedia.org/wiki/2015_Philadelphia_train_derailment\" title=\"2015 Philadelphia train derailment\">train derailment in Philadelphia</a>, United States, kills eight people and injures more than 200.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2015_Philadelphia_train_derailment\" title=\"2015 Philadelphia train derailment\">train derailment in Philadelphia</a>, United States, kills eight people and injures more than 200.", "links": [{"title": "2015 Philadelphia train derailment", "link": "https://wikipedia.org/wiki/2015_Philadelphia_train_derailment"}]}, {"year": "2015", "text": "Massive Nepal earthquake kills 218 people and injures more than 3,500.", "html": "2015 - Massive <a href=\"https://wikipedia.org/wiki/May_2015_Nepal_earthquake\" title=\"May 2015 Nepal earthquake\">Nepal earthquake</a> kills 218 people and injures more than 3,500.", "no_year_html": "Massive <a href=\"https://wikipedia.org/wiki/May_2015_Nepal_earthquake\" title=\"May 2015 Nepal earthquake\">Nepal earthquake</a> kills 218 people and injures more than 3,500.", "links": [{"title": "May 2015 Nepal earthquake", "link": "https://wikipedia.org/wiki/May_2015_Nepal_earthquake"}]}, {"year": "2017", "text": "The WannaCry ransomware attack impacts over 400,000 computers worldwide, targeting computers of the United Kingdom's National Health Services and Telefónica computers.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/WannaCry_ransomware_attack\" title=\"WannaCry ransomware attack\">WannaCry ransomware attack</a> impacts over 400,000 computers worldwide, targeting computers of the United Kingdom's <a href=\"https://wikipedia.org/wiki/National_Health_Services\" class=\"mw-redirect\" title=\"National Health Services\">National Health Services</a> and <a href=\"https://wikipedia.org/wiki/Telef%C3%B3nica\" title=\"Telefónica\">Telefónica</a> computers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/WannaCry_ransomware_attack\" title=\"WannaCry ransomware attack\">WannaCry ransomware attack</a> impacts over 400,000 computers worldwide, targeting computers of the United Kingdom's <a href=\"https://wikipedia.org/wiki/National_Health_Services\" class=\"mw-redirect\" title=\"National Health Services\">National Health Services</a> and <a href=\"https://wikipedia.org/wiki/Telef%C3%B3nica\" title=\"Telefónica\">Telefónica</a> computers.", "links": [{"title": "WannaCry ransomware attack", "link": "https://wikipedia.org/wiki/WannaCry_ransomware_attack"}, {"title": "National Health Services", "link": "https://wikipedia.org/wiki/National_Health_Services"}, {"title": "Telefónica", "link": "https://wikipedia.org/wiki/Telef%C3%B3nica"}]}, {"year": "2018", "text": "Paris knife attack: A man is fatally shot by police in Paris after killing one and injuring several others.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/2018_Paris_knife_attack\" title=\"2018 Paris knife attack\">Paris knife attack</a>: A man is fatally shot by police in Paris after killing one and injuring several others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2018_Paris_knife_attack\" title=\"2018 Paris knife attack\">Paris knife attack</a>: A man is fatally shot by police in Paris after killing one and injuring several others.", "links": [{"title": "2018 Paris knife attack", "link": "https://wikipedia.org/wiki/2018_Paris_knife_attack"}]}, {"year": "2024", "text": "Middle/End of the May 2024 Solar Storms, the most powerful set of Geomagnetic storms since the 2003 Halloween solar storms.", "html": "2024 - Middle/End of the <a href=\"https://wikipedia.org/wiki/May_2024_solar_storms\" title=\"May 2024 solar storms\">May 2024 Solar Storms</a>, the most powerful set of Geomagnetic storms since the <a href=\"https://wikipedia.org/wiki/2003_Halloween_solar_storms\" title=\"2003 Halloween solar storms\">2003 Halloween solar storms</a>.", "no_year_html": "Middle/End of the <a href=\"https://wikipedia.org/wiki/May_2024_solar_storms\" title=\"May 2024 solar storms\">May 2024 Solar Storms</a>, the most powerful set of Geomagnetic storms since the <a href=\"https://wikipedia.org/wiki/2003_Halloween_solar_storms\" title=\"2003 Halloween solar storms\">2003 Halloween solar storms</a>.", "links": [{"title": "May 2024 solar storms", "link": "https://wikipedia.org/wiki/May_2024_solar_storms"}, {"title": "2003 Halloween solar storms", "link": "https://wikipedia.org/wiki/2003_Halloween_solar_storms"}]}], "Births": [{"year": "1325", "text": "<PERSON>, Elector <PERSON> (d. 1398)", "html": "1325 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>, Elector <PERSON>\"><PERSON>, Elector <PERSON></a> (d. 1398)", "links": [{"title": "<PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1401", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1428)", "html": "1401 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>h%C5%8Dk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>h%C5%8Dk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1428)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Sh%C5%8Dk%C5%8D"}]}, {"year": "1479", "text": "<PERSON><PERSON><PERSON>, Catholic cardinal (d. 1532)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/Pompeo_Colonna\" title=\"Pompeo Colonna\">Pomp<PERSON></a>, Catholic cardinal (d. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pompeo_Colonna\" title=\"Pompeo Colonna\">Pompeo Colon<PERSON></a>, Catholic cardinal (d. 1532)", "links": [{"title": "Pompeo <PERSON>", "link": "https://wikipedia.org/wiki/Pompeo_Colonna"}]}, {"year": "1496", "text": "<PERSON> of Sweden (d. 1560)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (d. 1560)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1590", "text": "<PERSON><PERSON><PERSON> <PERSON>, Grand Duke of Tuscany (d. 1621)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_de%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON> <PERSON>, Grand Duke of Tuscany</a> (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27_<PERSON>,_Grand_Duke_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Tuscany\"><PERSON><PERSON><PERSON> <PERSON>, Grand Duke of Tuscany</a> (d. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/Cosimo_II_de%27_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1606", "text": "<PERSON>, German art-historian and painter (d. 1688)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German art-historian and painter (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German art-historian and painter (d. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON>, French-Canadian soldier and politician, third Governor General of New France (d. 1698)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de_Frontenac\" title=\"<PERSON> Frontenac\"><PERSON></a>, French-Canadian soldier and politician, third <a href=\"https://wikipedia.org/wiki/Governor_General_of_New_France\" title=\"Governor General of New France\">Governor General of New France</a> (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Frontenac\" title=\"<PERSON> Frontenac\"><PERSON></a>, French-Canadian soldier and politician, third <a href=\"https://wikipedia.org/wiki/Governor_General_of_New_France\" title=\"Governor General of New France\">Governor General of New France</a> (d. 1698)", "links": [{"title": "<PERSON> Front<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_Frontena<PERSON>"}, {"title": "Governor General of New France", "link": "https://wikipedia.org/wiki/Governor_General_of_New_France"}]}, {"year": "1626", "text": "<PERSON>, Flemish priest and missionary (d. 1705)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and missionary (d. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish priest and missionary (d. 1705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON> the Strong, Polish king (d. 1733)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Strong\" title=\"Augustus II the Strong\"><PERSON> II the Strong</a>, Polish king (d. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Strong\" title=\"Augustus II the Strong\"><PERSON> <PERSON> the Strong</a>, Polish king (d. 1733)", "links": [{"title": "<PERSON> the Strong", "link": "https://wikipedia.org/wiki/<PERSON>_II_the_Strong"}]}, {"year": "1700", "text": "<PERSON>, Italian architect and engineer, designed the Palace of Caserta and Royal Palace of Milan (d. 1773)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Caserta\" class=\"mw-redirect\" title=\"Palace of Caserta\">Palace of Caserta</a> and <a href=\"https://wikipedia.org/wiki/Royal_Palace_of_Milan\" title=\"Royal Palace of Milan\">Royal Palace of Milan</a> (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Palace_of_Caserta\" class=\"mw-redirect\" title=\"Palace of Caserta\">Palace of Caserta</a> and <a href=\"https://wikipedia.org/wiki/Royal_Palace_of_Milan\" title=\"Royal Palace of Milan\">Royal Palace of Milan</a> (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Palace of Caserta", "link": "https://wikipedia.org/wiki/Palace_of_Caserta"}, {"title": "Royal Palace of Milan", "link": "https://wikipedia.org/wiki/Royal_Palace_of_Milan"}]}, {"year": "1725", "text": "<PERSON>, Duke of Orléans (d. 1785)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Orl%C3%A9ans\" title=\"<PERSON>, Duke of Orléans\"><PERSON>, Duke of Orléans</a> (d. 1785)", "links": [{"title": "<PERSON>, Duke of Orléans", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Orl%C3%A9ans"}]}, {"year": "1739", "text": "<PERSON>, Czech-Austrian organist and composer (d. 1813)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian organist and composer (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian organist and composer (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, German composer and publisher (d. 1812)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and publisher (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and publisher (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, Italian violinist and composer (d. 1824)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, Spanish field marshal and politician, Prime Minister of Spain (d. 1851)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish field marshal and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1774", "text": "<PERSON>, English politician (d. 1853)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er"}]}, {"year": "1776", "text": "<PERSON>, Peruvian military leader, President of Peru (d. 1830)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian military leader, President of Peru (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Peruvian military leader, President of Peru (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_La_Mar"}]}, {"year": "1777", "text": "<PERSON>, Australian businesswoman (d. 1855)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businesswoman (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON><PERSON>, German chemist and academic (d. 1873)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and academic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German chemist and academic (d. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Canadian lawyer and politician, third Premier of West Canada (d. 1858)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, third <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of West Canada</a> (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, third <a href=\"https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada\" class=\"mw-redirect\" title=\"List of Joint Premiers of the Province of Canada\">Premier of West Canada</a> (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Joint Premiers of the Province of Canada", "link": "https://wikipedia.org/wiki/List_of_Joint_Premiers_of_the_Province_of_Canada"}]}, {"year": "1806", "text": "<PERSON>, Finnish philosopher and politician (d. 1881)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish philosopher and politician (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish philosopher and politician (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, English poet and illustrator (d. 1888)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and illustrator (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and illustrator (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, German pianist and composer (d. 1889)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, Italian-English nurse, social reformer, and statistician (d. 1910)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON></a>, Italian-English nurse, social reformer, and statistician (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nightingale\"><PERSON></a>, Italian-English nurse, social reformer, and statistician (d. 1910)", "links": [{"title": "Florence Nightingale", "link": "https://wikipedia.org/wiki/Florence_Nightingale"}]}, {"year": "1825", "text": "Oré<PERSON><PERSON><PERSON>, French lawyer and explorer (d. 1878)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and explorer (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and explorer (d. 1878)", "links": [{"title": "Orélie<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, English poet and painter (d. 1882)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and painter (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and painter (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON><PERSON>, Greek composer and educator (d. 1896)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, Vietnamese mandarin (d. 1913)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_Thuy%E1%BA%BFt\" title=\"Tôn Thất <PERSON>hu<PERSON>ết\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Vietnamese <a href=\"https://wikipedia.org/wiki/Mandarin_(bureaucrat)\" title=\"Mandarin (bureaucrat)\">mandarin</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_Thuy%E1%BA%BFt\" title=\"Tôn Thất Thuyết\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Vietnamese <a href=\"https://wikipedia.org/wiki/Mandarin_(bureaucrat)\" title=\"Mandarin (bureaucrat)\">mandarin</a> (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_Thuy%E1%BA%BFt"}, {"title": "Mandarin (bureaucrat)", "link": "https://wikipedia.org/wiki/Mandarin_(bureaucrat)"}]}, {"year": "1840", "text": "<PERSON>, Chilean colonel (d. 1912)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean colonel (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean colonel (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, French composer (d. 1912)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, French pianist, composer, and educator (d. 1924)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and educator (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and educator (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>au<PERSON>%C3%A9"}]}, {"year": "1850", "text": "<PERSON>, American historian and politician (d. 1924)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Australian politician, 19th Premier of South Australia (d. 1909)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1859", "text": "<PERSON>, American lawyer and politician (d. 1932)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, English-Australian politician, ninth Premier of Western Australia (d. 1918)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, ninth <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, ninth <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1918)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON><PERSON>, Bengali writer, painter, violin player and composer, technologist and entrepreneur (d. 1915)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Upendrakishore_<PERSON>_<PERSON>\" title=\"Upendrakishore Ray <PERSON>\">Upendrakishore <PERSON></a>, Bengali writer, painter, violin player and composer, technologist and entrepreneur (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Upendrakishore_<PERSON>_<PERSON>\" title=\"Upendrakishore <PERSON>\">Upendrakishore <PERSON></a>, Bengali writer, painter, violin player and composer, technologist and entrepreneur (d. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Upendrakishore_Ray_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Australian cricketer and accountant (d. 1938)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and accountant (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and accountant (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rumble"}]}, {"year": "1869", "text": "<PERSON>, German gymnast, wrestler, and weightlifter (d. 1946)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast, wrestler, and weightlifter (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast, wrestler, and weightlifter (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Slovenian priest and politician, tenth Prime Minister of Yugoslavia (d. 1940)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1ec\" title=\"<PERSON>\"><PERSON></a>, Slovenian priest and politician, tenth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C5%A1ec\" title=\"<PERSON>\"><PERSON></a>, Slovenian priest and politician, tenth <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ko<PERSON>%C5%A1ec"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1873", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English-Canadian painter (d. 1932)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Canadian painter (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Canadian painter (d. 1932)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Austrian pediatrician and immunologist (d. 1929)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian pediatrician and immunologist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian pediatrician and immunologist (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, English architect, designed the Bristol Central Library (d. 1960)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Bristol_Central_Library\" title=\"Bristol Central Library\">Bristol Central Library</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Bristol_Central_Library\" title=\"Bristol Central Library\">Bristol Central Library</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bristol Central Library", "link": "https://wikipedia.org/wiki/Bristol_Central_Library"}]}, {"year": "1880", "text": "<PERSON>, American explorer (d. 1951)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Lincoln_Ellsworth\" title=\"Lincoln Ellsworth\"><PERSON></a>, American explorer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_Ellsworth\" title=\"Lincoln Ellsworth\"><PERSON></a>, American explorer (d. 1951)", "links": [{"title": "<PERSON> Ellsworth", "link": "https://wikipedia.org/wiki/Lincoln_Ellsworth"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Lithuanian-Israeli lawyer and jurist (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Palt<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and jurist (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-Israeli lawyer and jurist (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paltiel_<PERSON>kan"}]}, {"year": "1886", "text": "<PERSON>, German captain and pilot (d. 1937)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, substitute president of Mexico (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Abelardo_L._Rodr%C3%ADguez\" title=\"<PERSON>ard<PERSON>\"><PERSON><PERSON><PERSON></a>, substitute president of Mexico (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abelardo_L._Rodr%C3%ADguez\" title=\"<PERSON>ard<PERSON>\"><PERSON><PERSON><PERSON></a>, substitute president of Mexico (d. 1967)", "links": [{"title": "Abelardo L. <PERSON>", "link": "https://wikipedia.org/wiki/Abelardo_L._Rodr%C3%ADguez"}]}, {"year": "1889", "text": "<PERSON>, German-Swiss businessman and Holocaust survivor; father of diarist <PERSON> (d. 1980)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman and Holocaust survivor; father of diarist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman and Holocaust survivor; father of diarist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Austrian-German actor and director (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German actor and director (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German actor and director (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Canadian-American chemist and academic, Nobel Prize laureate (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Indian-American philosopher and author (d. 1986)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamu<PERSON>i\"><PERSON><PERSON><PERSON></a>, Indian-American philosopher and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jidd<PERSON>_<PERSON>\" title=\"Jidd<PERSON> Krishnamu<PERSON>i\"><PERSON><PERSON><PERSON></a>, Indian-American philosopher and author (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>i"}]}, {"year": "1897", "text": "<PERSON>, American serial killer and rapist (d. 1928)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and rapist (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and rapist (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Austrian-German actress (d. 1971)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actress (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-German actress (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English actor (d. 1991)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English actor (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English author and screenwriter (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American actress (d. 2003)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Hungarian-English economist (d. 1986)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English economist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English economist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Surinamese educator and politician, first President of Suriname (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese educator and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese educator and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Suriname\" title=\"President of Suriname\">President of Suriname</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Suriname", "link": "https://wikipedia.org/wiki/President_of_Suriname"}]}, {"year": "1910", "text": "<PERSON>, English biochemist, crystallographer, and academic, Nobel Prize laureate (d. 1994)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist, crystallographer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist, crystallographer, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1911", "text": "<PERSON>, American author and illustrator (d. 1972)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American journalist and actor (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American businesswoman, founded Mary <PERSON> Cosmetics (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>sm<PERSON></a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American spy (d. 1953)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American spy (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American spy (d. 1953)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German sculptor and illustrator (d. 1986)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and illustrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and illustrator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Canadian environmentalist and author (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian environmentalist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian environmentalist and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English racing driver and manager (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English actor, producer, and screenwriter (d. 1968)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American baseball player, coach, and manager (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter, pianist, and producer (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Namibian politician, 1st President of Namibia (d. 2025)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Namibia\" class=\"mw-redirect\" title=\"List of Presidents of Namibia\">President of Namibia</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Namibia\" class=\"mw-redirect\" title=\"List of Presidents of Namibia\">President of Namibia</a> (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of Namibia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Namibia"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Spanish director and screenwriter (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Franco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish director and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Franco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish director and screenwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Franco"}]}, {"year": "1935", "text": "<PERSON>, Dominican-American baseball player, coach, and manager", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian ice hockey player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Panamanian lawyer and politician, 32nd President of Panama (d. 2009)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_Heads_of_State_of_Panama\" class=\"mw-redirect\" title=\"List of Heads of State of Panama\">President of Panama</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_Heads_of_State_of_Panama\" class=\"mw-redirect\" title=\"List of Heads of State of Panama\">President of Panama</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Heads of State of Panama", "link": "https://wikipedia.org/wiki/List_of_Heads_of_State_of_Panama"}]}, {"year": "1936", "text": "<PERSON>, American journalist and talk show host (d. 2007)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American painter and sculptor (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, English cyclist (d. 1996)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cyclist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cyclist (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American comedian, actor, and author (d. 2008)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Reg_Gasnier"}]}, {"year": "1940", "text": "<PERSON>, American songwriter and producer (d. 2008)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English singer-songwriter (d. 2000)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English academic and politician, 28th Governor of Hong Kong", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, 28th <a href=\"https://wikipedia.org/wiki/Governor_of_Hong_Kong\" title=\"Governor of Hong Kong\">Governor of Hong Kong</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Hong Kong", "link": "https://wikipedia.org/wiki/Governor_of_Hong_Kong"}]}, {"year": "1945", "text": "<PERSON>, Jr., English footballer and manager (d. 2007)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, English footballer and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, English footballer and manager (d. 2007)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1945", "text": "<PERSON>, English keyboard player and songwriter (d. 2014)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American architect, designed the Imperial War Museum North and Jewish Museum", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Imperial_War_Museum_North\" title=\"Imperial War Museum North\">Imperial War Museum North</a> and <a href=\"https://wikipedia.org/wiki/Jewish_Museum,_Berlin\" class=\"mw-redirect\" title=\"Jewish Museum, Berlin\">Jewish Museum</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Imperial_War_Museum_North\" title=\"Imperial War Museum North\">Imperial War Museum North</a> and <a href=\"https://wikipedia.org/wiki/Jewish_Museum,_Berlin\" class=\"mw-redirect\" title=\"Jewish Museum, Berlin\">Jewish Museum</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Imperial War Museum North", "link": "https://wikipedia.org/wiki/Imperial_War_Museum_North"}, {"title": "Jewish Museum, Berlin", "link": "https://wikipedia.org/wiki/Jewish_Museum,_Berlin"}]}, {"year": "1947", "text": "<PERSON>, Canadian journalist and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and multi-instrumentalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and multi-instrumentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and multi-instrumentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Irish actor, director, and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American basketball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ving_Rhames\" title=\"Ving Rhames\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ving_Rhames\" title=\"Ving Rhames\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ving_R<PERSON>s"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American guitarist and songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, British sprinter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, British sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, British sprinter", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American skateboarder and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American golfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian golfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>horn"}]}, {"year": "1975", "text": "<PERSON>, New Zealand rugby player (d. 2015)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Scottish snooker player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish snooker player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Iranian mathematician (d. 2017)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian mathematician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian mathematician (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish-Canadian model, actress, and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Malin_%C3%85kerman\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Canadian model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malin_%C3%85kerman\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Canadian model, actress, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Malin_%C3%85kerman"}]}, {"year": "1978", "text": "<PERSON>, American actor and comedian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1980", "text": "<PERSON><PERSON>, English politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ak"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Irish actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gleeson\"><PERSON><PERSON><PERSON></a>, Irish actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler and mixed martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Trinidadian cricketer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek Cypriot singer, musician, and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Eleftheria_Eleftheriou\" title=\"<PERSON><PERSON>theria Eleftheriou\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Cypriot singer, musician, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleftheria_Eleftheriou\" title=\"<PERSON><PERSON><PERSON>ia <PERSON>eftheriou\"><PERSON><PERSON><PERSON><PERSON></a>, Greek Cypriot singer, musician, and actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eleftheria_Eleftheriou"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, French figure skater", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Florent_Amodio\" title=\"Florent Amodio\">Florent <PERSON><PERSON><PERSON></a>, French figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Amodio\" title=\"Florent Amodio\">Florent <PERSON><PERSON><PERSON></a>, French figure skater", "links": [{"title": "Florent Amodio", "link": "https://wikipedia.org/wiki/Florent_Amodio"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American YouTuber and live streamer (d. 2019)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Etika\" title=\"<PERSON>ti<PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber and live streamer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Etika\" title=\"<PERSON>ti<PERSON>\"><PERSON><PERSON><PERSON></a>, American YouTuber and live streamer (d. 2019)", "links": [{"title": "Etika", "link": "https://wikipedia.org/wiki/Etika"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Fr<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American-Ivorian basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Mo_<PERSON>\" title=\"Mo Bamba\"><PERSON></a>, American-Ivorian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo_<PERSON>\" title=\"Mo Bamba\"><PERSON></a>, American-Ivorian basketball player", "links": [{"title": "Mo Bamba", "link": "https://wikipedia.org/wiki/Mo_Bamba"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "805", "text": "<PERSON><PERSON><PERSON><PERSON>, archbishop of Canterbury", "html": "805 - <a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of Canterbury", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of Canterbury", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%86thelhard"}]}, {"year": "940", "text": "<PERSON><PERSON><PERSON><PERSON>, patriarch of Alexandria (b. 877)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON><PERSON></a>, patriarch of Alexandria (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON><PERSON></a>, patriarch of Alexandria (b. 877)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Eutychius_of_Alexandria"}]}, {"year": "1003", "text": "<PERSON>, pope of the Catholic Church (b. 946)", "html": "1003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sylvester_II\" title=\"Pope Sylvester II\"><PERSON></a>, pope of the Catholic Church (b. 946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Sylvester II\"><PERSON> II</a>, pope of the Catholic Church (b. 946)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1012", "text": "<PERSON><PERSON><PERSON>, pope of the Catholic Church (b. 970)", "html": "1012 - <a href=\"https://wikipedia.org/wiki/Pope_Sergius_IV\" title=\"Pope Sergius IV\"><PERSON><PERSON><PERSON> IV</a>, pope of the Catholic Church (b. 970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Sergius_IV\" title=\"Pope Sergius IV\"><PERSON><PERSON><PERSON> IV</a>, pope of the Catholic Church (b. 970)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_IV"}]}, {"year": "1090", "text": "<PERSON><PERSON><PERSON> of Eppenstein, duke of Carinthia", "html": "1090 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Eppenstein\" title=\"<PERSON><PERSON><PERSON> of Eppenstein\"><PERSON><PERSON><PERSON> of Eppenstein</a>, duke of Carinthia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Eppenstein\" title=\"<PERSON><PERSON><PERSON> of Eppenstein\"><PERSON><PERSON><PERSON> of Eppenstein</a>, duke of Carinthia", "links": [{"title": "<PERSON><PERSON><PERSON> of Eppenstein", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1161", "text": "<PERSON> of Galloway, Scottish nobleman", "html": "1161 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Galloway\" title=\"<PERSON> of Galloway\"><PERSON> of Galloway</a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Galloway\" title=\"<PERSON> of Galloway\"><PERSON> of Galloway</a>, Scottish nobleman", "links": [{"title": "<PERSON> of Galloway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1182", "text": "<PERSON><PERSON><PERSON>, king of Denmark (b. 1131)", "html": "1182 - <a href=\"https://wikipedia.org/wiki/Valdemar_I_of_Denmark\" title=\"Valdemar I of Denmark\"><PERSON><PERSON><PERSON> I</a>, king of Denmark (b. 1131)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valdemar_I_of_Denmark\" title=\"Valdemar I of Denmark\"><PERSON><PERSON><PERSON> I</a>, king of Denmark (b. 1131)", "links": [{"title": "Valdemar I of Denmark", "link": "https://wikipedia.org/wiki/Valdemar_I_of_Denmark"}]}, {"year": "1331", "text": "<PERSON><PERSON><PERSON> of Admont, Benedictine abbot and scholar", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Admont\" title=\"<PERSON><PERSON><PERSON> of Admont\"><PERSON><PERSON><PERSON> of Admont</a>, Benedictine abbot and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Admont\" title=\"<PERSON><PERSON><PERSON> of Admont\"><PERSON><PERSON><PERSON> of Admont</a>, Benedictine abbot and scholar", "links": [{"title": "<PERSON><PERSON><PERSON> of Admont", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Admont"}]}, {"year": "1465", "text": "<PERSON>, Despot of Morea (b. 1409)", "html": "1465 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Despot of <a href=\"https://wikipedia.org/wiki/Despotate_of_the_Morea\" title=\"Despotate of the Morea\">Morea</a> (b. 1409)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Despot of <a href=\"https://wikipedia.org/wiki/Despotate_of_the_Morea\" title=\"Despotate of the Morea\">Morea</a> (b. 1409)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Despotate of the Morea", "link": "https://wikipedia.org/wiki/Despotate_of_the_Morea"}]}, {"year": "1490", "text": "<PERSON>, Portuguese princess and regent (b. 1452)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Portugal\" title=\"<PERSON>, Princess of Portugal\"><PERSON></a>, Portuguese princess and regent (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Portugal\" title=\"<PERSON>, Princess of Portugal\">Joanna</a>, Portuguese princess and regent (b. 1452)", "links": [{"title": "<PERSON>, Princess of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_of_Portugal"}]}, {"year": "1529", "text": "<PERSON><PERSON>, 7th Baroness <PERSON>, English noblewoman (b. 1460)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_7th_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, 7th Baroness <PERSON>\"><PERSON><PERSON>, 7th Baroness <PERSON></a>, English noblewoman (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_7th_Baroness_<PERSON>\" title=\"<PERSON><PERSON>, 7th Baroness <PERSON>\"><PERSON><PERSON>, 7th Baroness <PERSON></a>, English noblewoman (b. 1460)", "links": [{"title": "<PERSON><PERSON>, 7th Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_7th_Baroness_<PERSON>"}]}, {"year": "1599", "text": "<PERSON><PERSON>, Mughal prince (b. 1570)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(son_of_<PERSON>)\" title=\"<PERSON><PERSON> (son of <PERSON>)\"><PERSON><PERSON></a>, Mughal prince (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(son_of_<PERSON>)\" title=\"<PERSON><PERSON> (son of <PERSON>)\"><PERSON><PERSON></a>, Mughal prince (b. 1570)", "links": [{"title": "<PERSON><PERSON> (son of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(son_of_<PERSON>)"}]}, {"year": "1634", "text": "<PERSON>, English poet and playwright (b. 1559)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1559)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON>, 1st Earl of Strafford, English soldier and politician, Lord Lieutenant of Ireland (b. 1593)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Strafford\" title=\"<PERSON>, 1st Earl of Strafford\"><PERSON>, 1st Earl of Strafford</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Strafford\" title=\"<PERSON>, 1st Earl of Strafford\"><PERSON>, 1st Earl of Strafford</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1593)", "links": [{"title": "<PERSON>, 1st Earl of Strafford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Strafford"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1684", "text": "<PERSON><PERSON>, French physicist and priest (b. 1620)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French physicist and priest (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French physicist and priest (b. 1620)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1699", "text": "<PERSON>, Flemish painter (b. 1626)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (b. 1626)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1700", "text": "<PERSON>, English poet, playwright, and critic (b. 1631)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and critic (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and critic (b. 1631)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1708", "text": "<PERSON><PERSON><PERSON>, duke of Mecklenburg-Strelitz (b. 1658)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON></a>, duke of Mecklenburg-Strelitz (b. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON></a>, duke of Mecklenburg-Strelitz (b. 1658)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Mecklenburg-Strelitz"}]}, {"year": "1748", "text": "<PERSON>, English astronomer and academic (b. 1692)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English astronomer and academic (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English astronomer and academic (b. 1692)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_(astronomer)"}]}, {"year": "1759", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French sculptor (b. 1700)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French sculptor (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French sculptor (b. 1700)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, Swiss zoologist and academic (b. 1710)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss zoologist and academic (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss zoologist and academic (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, French playwright and composer (b. 1710)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, German poet and author (b. 1720)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Russian general and politician, Governor-General of Baltic provinces (b. 1734)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Baltic_provinces\" title=\"Governor-General of Baltic provinces\">Governor-General of Baltic provinces</a> (b. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Baltic_provinces\" title=\"Governor-General of Baltic provinces\">Governor-General of Baltic provinces</a> (b. 1734)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Baltic provinces", "link": "https://wikipedia.org/wiki/Governor-General_of_Baltic_provinces"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian-Polish painter (b. 1799)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Walenty_Wa%C5%84ko<PERSON>\" title=\"Walenty Wańkowicz\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian-Polish painter (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walenty_Wa%C5%84ko<PERSON>\" title=\"Walent<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian-Polish painter (b. 1799)", "links": [{"title": "Walenty Wańkowicz", "link": "https://wikipedia.org/wiki/Walenty_Wa%C5%<PERSON><PERSON><PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, Hungarian poet and academic (b. 1763)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and academic (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and academic (b. 1763)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Bats%C3%A1nyi"}]}, {"year": "1856", "text": "<PERSON>, French mathematician, physicist, and astronomer (b. 1786)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and astronomer (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, physicist, and astronomer (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Russian author and academic (b. 1791)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and academic (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and academic (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, English architect, designed Upper Brook Street Chapel and the Palace of Westminster (b. 1795)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester\" title=\"Upper Brook Street Chapel, Manchester\">Upper Brook Street Chapel</a> and the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed <a href=\"https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester\" title=\"Upper Brook Street Chapel, Manchester\">Upper Brook Street Chapel</a> and the <a href=\"https://wikipedia.org/wiki/Palace_of_Westminster\" title=\"Palace of Westminster\">Palace of Westminster</a> (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Upper Brook Street Chapel, Manchester", "link": "https://wikipedia.org/wiki/Upper_Brook_Street_Chapel,_Manchester"}, {"title": "Palace of Westminster", "link": "https://wikipedia.org/wiki/Palace_of_Westminster"}]}, {"year": "1864", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American general (b. 1833)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. E<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1833)", "links": [{"title": "J. E<PERSON> <PERSON><PERSON> Stuart", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, German archaeologist and academic (b. 1795)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and academic (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and academic (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Bulgarian activist (b. 1843)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian activist (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian activist (b. 1843)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, French chemist and academic (b. 1795)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Anselme_Payen\" title=\"Anselme Payen\"><PERSON><PERSON><PERSON></a>, French chemist and academic (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anselme_<PERSON>en\" title=\"Anselme Payen\"><PERSON><PERSON><PERSON></a>, French chemist and academic (b. 1795)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ansel<PERSON>_<PERSON>en"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Czech composer and educator (b. 1824)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Smetana\" title=\"Bed<PERSON>ich Smetana\"><PERSON><PERSON><PERSON></a>, Czech composer and educator (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Smetana\" title=\"Bed<PERSON>ich Smetana\"><PERSON><PERSON><PERSON></a>, Czech composer and educator (b. 1824)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bed%C5%99ich_Smetana"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Finnish journalist, playwright, and activist (b. 1844)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist, playwright, and activist (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish journalist, playwright, and activist (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Swedish merchant, ironmaster and industrialist (b. 1819)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON><PERSON>_G%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish merchant, ironmaster and industrialist (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON><PERSON>_G%C3%B6<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish merchant, ironmaster and industrialist (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON><PERSON>_G%C3%B6ransson"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, French author and critic (b. 1848)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author and critic (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author and critic (b. 1848)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Scottish-born Irish socialist and rebel leader (b. 1868)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish socialist and rebel leader (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish socialist and rebel leader (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American poet and critic (b. 1874)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Belgian violinist, composer, and conductor (b. 1858)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and conductor (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and conductor (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Ysa%C3%BFe"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Polish field marshal and politician, 15th Prime Minister of Poland (b. 1867)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish field marshal and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>%C5%82sudski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish field marshal and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Pi%C5%82sudski"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1944", "text": "<PERSON>, American journalist and author (b. 1892)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Max_Brand\" title=\"Max Brand\"><PERSON></a>, American journalist and author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_Brand\" title=\"Max Brand\"><PERSON></a>, American journalist and author (b. 1892)", "links": [{"title": "Max <PERSON>", "link": "https://wikipedia.org/wiki/Max_Brand"}]}, {"year": "1944", "text": "<PERSON><PERSON>, English author, poet, and critic (b. 1863)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and critic (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and critic (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor and singer (b. 1895)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>n"}]}, {"year": "1957", "text": "<PERSON>, Spanish bobsledder and racing driver (b. 1928)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ago\"><PERSON></a>, Spanish bobsledder and racing driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ago\"><PERSON></a>, Spanish bobsledder and racing driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ago"}]}, {"year": "1957", "text": "<PERSON>, Austrian-American actor, director, and producer (b. 1885)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor, director, and producer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor, director, and producer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German footballer and manager (b. 1878)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Irish-Canadian sprinter and coach (b. 1882)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-Canadian sprinter and coach (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish-Canadian sprinter and coach (b. 1882)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "1964", "text": "<PERSON>, Scottish medical doctor (b. 1875)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish medical doctor (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish medical doctor (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian-German SS officer (b. 1896)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1967", "text": "<PERSON>, English poet and author (b. 1878)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, German poet and playwright, Nobel Prize laureate (b. 1891)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American baseball player and coach (b. 1901)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American screenwriter, novelist and journalist (b. 1888)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, novelist and journalist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, novelist and journalist (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American race car driver (b. 1927)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_<PERSON>ard\" title=\"Art Poll<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Pollard"}]}, {"year": "1981", "text": "<PERSON>, Provisional IRA hunger striker (b. 1956)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> <a href=\"https://wikipedia.org/wiki/Hunger_striker\" class=\"mw-redirect\" title=\"Hunger striker\">hunger striker</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_IRA\" class=\"mw-redirect\" title=\"Provisional IRA\">Provisional IRA</a> <a href=\"https://wikipedia.org/wiki/Hunger_striker\" class=\"mw-redirect\" title=\"Hunger striker\">hunger striker</a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional IRA", "link": "https://wikipedia.org/wiki/Provisional_IRA"}, {"title": "Hunger striker", "link": "https://wikipedia.org/wiki/Hunger_striker"}]}, {"year": "1981", "text": "<PERSON>, Singaporean professor and politician, second President of Singapore (b. 1907)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean professor and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean professor and politician, second <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1985", "text": "<PERSON>, French painter and sculptor (b. 1901)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German actress (b. 1897)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Greek poet and songwriter (b. 1911)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and songwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and songwriter (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nik<PERSON>_<PERSON>os"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1932)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Italian Olympic alpine skier (b.1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Zeno_Col%C3%B2\" title=\"Zen<PERSON>\"><PERSON><PERSON></a>, Italian Olympic alpine skier (b.1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeno_Col%C3%B2\" title=\"Zen<PERSON>\"><PERSON><PERSON></a>, Italian Olympic alpine skier (b.1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeno_Col%C3%B2"}]}, {"year": "1994", "text": "<PERSON>, German-American psychologist and psychoanalyst (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and psychoanalyst (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and psychoanalyst (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Scottish-English lawyer and politician, Labour Party leader,  Leader of the Opposition (b. 1938)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)\" title=\"<PERSON> (Labour Party leader)\"><PERSON></a>, Scottish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party leader</a>, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)\" title=\"Leader of the Opposition (United Kingdom)\">Leader of the Opposition</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)\" title=\"<PERSON> (Labour Party leader)\"><PERSON></a>, Scottish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party leader</a>, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)\" title=\"Leader of the Opposition (United Kingdom)\">Leader of the Opposition</a> (b. 1938)", "links": [{"title": "<PERSON> (Labour Party leader)", "link": "https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)"}, {"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "Leader of the Opposition (United Kingdom)", "link": "https://wikipedia.org/wiki/Leader_of_the_Opposition_(United_Kingdom)"}]}, {"year": "1999", "text": "<PERSON>, Romanian-American illustrator (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American illustrator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American race car driver (b. 1980)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Petty\"><PERSON></a>, American race car driver (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American singer and television host (b. 1912)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Como\"><PERSON></a>, American singer and television host (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Como\"><PERSON></a>, American singer and television host (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Russian engineer, designed the Tupolev Tu-144 (b. 1925)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer, designed the <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-144\" title=\"Tupolev Tu-144\">Tupolev Tu-144</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tupolev Tu-144", "link": "https://wikipedia.org/wiki/Tupolev_Tu-144"}]}, {"year": "2003", "text": "Prince <PERSON><PERSON><PERSON>, French-American diplomat (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, French-American diplomat (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Prince <PERSON><PERSON><PERSON>\">Prince <PERSON><PERSON><PERSON></a>, French-American diplomat (b. 1933)", "links": [{"title": "Prince <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Turkish director, producer, and screenwriter (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/%C3%96mer_<PERSON>vu<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96mer_<PERSON>vu<PERSON>\" title=\"<PERSON><PERSON>vu<PERSON>\"><PERSON><PERSON></a>, Turkish director, producer, and screenwriter (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96mer_Kavur"}]}, {"year": "2005", "text": "<PERSON>, English author and scholar (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and scholar (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and scholar (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Finnish physician and professor (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4l%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and professor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Set%C3%A4l%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and professor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kai_Set%C3%A4l%C3%A4"}]}, {"year": "2005", "text": "<PERSON>, Swedish actress (b. 1937)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Libyan politician, Prime Minister of Libya (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Libyan politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Libyan politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Libya\" class=\"mw-redirect\" title=\"Prime Minister of Libya\">Prime Minister of Libya</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Libya", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Libya"}]}, {"year": "2008", "text": "<PERSON>, American painter and illustrator (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Polish nurse and humanitarian (b. 1910)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nurse and humanitarian (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish nurse and humanitarian (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ler"}]}, {"year": "2009", "text": "<PERSON>, Spanish singer-songwriter and guitarist (b. 1957)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Spanish singer-songwriter and guitarist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Spanish singer-songwriter and guitarist (b. 1957)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2012", "text": "<PERSON>, Dutch footballer and coach (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Belgian illustrator (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian illustrator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, German political scientist, author, and academic (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German political scientist, author, and academic (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German political scientist, author, and academic (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Lithuanian-German actress and singer (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Cornell_Borchers\" title=\"<PERSON> Borchers\"><PERSON></a>, Lithuanian-German actress and singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornell_Borchers\" title=\"<PERSON> Borchers\"><PERSON></a>, Lithuanian-German actress and singer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cornell_Borchers"}]}, {"year": "2014", "text": "<PERSON>, Italian cardinal (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marco_C%C3%A9"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, Swiss painter, sculptor, and set designer (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss painter, sculptor, and set designer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Swiss painter, sculptor, and set designer (b. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Indian actor, director, and screenwriter (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Sarat_Pujari\" title=\"Sarat Pujari\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarat_Pujari\" title=\"Sarat Pujari\"><PERSON><PERSON></a>, Indian actor, director, and screenwriter (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarat_Pujari"}]}, {"year": "2014", "text": "<PERSON>, Mexican businessman and philanthropist (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and philanthropist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and philanthropist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorenzo_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German-American historian, author, and academic (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American historian, author, and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Trinidadian sprinter (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian sprinter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian sprinter (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Finnish banker and politician, ninth President of Finland (b. 1923)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish banker and politician, ninth President of Finland (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish banker and politician, ninth President of Finland (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Scottish serial killer (b. 1945)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish serial killer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish serial killer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American funeral director and U.S. Supreme Court litigant (b. 1960)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American funeral director and <a href=\"https://wikipedia.org/wiki/R.G._%26_<PERSON><PERSON><PERSON><PERSON>_Harris_Funeral_Homes_Inc._v._Equal_Employment_Opportunity_Commission\" title=\"R.G. &amp; G.R. Harris Funeral Homes Inc. v. Equal Employment Opportunity Commission\">U.S. Supreme Court litigant</a> (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American funeral director and <a href=\"https://wikipedia.org/wiki/R.G._%26_<PERSON><PERSON><PERSON><PERSON>_Harris_Funeral_Homes_Inc._v._Equal_Employment_Opportunity_Commission\" title=\"R.G. &amp; G.R. Harris Funeral Homes Inc. v. Equal Employment Opportunity Commission\">U.S. Supreme Court litigant</a> (b. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "R.G. & G.R. Harris Funeral Homes Inc. v. Equal Employment Opportunity Commission", "link": "https://wikipedia.org/wiki/R.G._%26_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Funeral_Homes_Inc._v._Equal_Employment_Opportunity_Commission"}]}, {"year": "2024", "text": "<PERSON>, American film actor and producer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actor and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actor and producer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American saxophonist (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football executive (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football executive (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football executive (b. 1949)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}]}}