{"date": "July 7", "url": "https://wikipedia.org/wiki/July_7", "data": {"Events": [{"year": "1124", "text": "The city of Tyre falls to the Venetian Crusade after a siege of nineteen weeks.", "html": "1124 - The city of <a href=\"https://wikipedia.org/wiki/Tyre,_Lebanon\" title=\"Tyre, Lebanon\">Tyre</a> falls to the <a href=\"https://wikipedia.org/wiki/Venetian_Crusade\" title=\"Venetian Crusade\">Venetian Crusade</a> after a siege of nineteen weeks.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Tyre,_Lebanon\" title=\"Tyre, Lebanon\">Tyre</a> falls to the <a href=\"https://wikipedia.org/wiki/Venetian_Crusade\" title=\"Venetian Crusade\">Venetian Crusade</a> after a siege of nineteen weeks.", "links": [{"title": "Tyre, Lebanon", "link": "https://wikipedia.org/wiki/Tyre,_Lebanon"}, {"title": "Venetian Crusade", "link": "https://wikipedia.org/wiki/Venetian_Crusade"}]}, {"year": "1456", "text": "A retrial verdict acquits <PERSON> of Arc of heresy 25 years after her execution.", "html": "1456 - A <a href=\"https://wikipedia.org/wiki/Retrial_of_<PERSON>_<PERSON>_Arc\" class=\"mw-redirect\" title=\"Retrial of Joan of Arc\">retrial verdict</a> acquits <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> of <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a> 25 years after her execution.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Retrial_of_<PERSON>_<PERSON>_Arc\" class=\"mw-redirect\" title=\"Retrial of Joan of Arc\">retrial verdict</a> acquits <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Arc\" title=\"Joan of Arc\"><PERSON> of Arc</a> of <a href=\"https://wikipedia.org/wiki/Heresy_in_Christianity\" title=\"Heresy in Christianity\">heresy</a> 25 years after her execution.", "links": [{"title": "Retrial of Joan of Arc", "link": "https://wikipedia.org/wiki/Retrial_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Heresy in Christianity", "link": "https://wikipedia.org/wiki/Heresy_in_Christianity"}]}, {"year": "1520", "text": "Spanish conquistadores defeat a larger Aztec army at the Battle of Otumba.", "html": "1520 - Spanish <i><a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadores</a></i> defeat a larger <a href=\"https://wikipedia.org/wiki/Aztec_Triple_Alliance\" class=\"mw-redirect\" title=\"Aztec Triple Alliance\">Aztec</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Otumba\" title=\"Battle of Otumba\">Battle of Otumba</a>.", "no_year_html": "Spanish <i><a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadores</a></i> defeat a larger <a href=\"https://wikipedia.org/wiki/Aztec_Triple_Alliance\" class=\"mw-redirect\" title=\"Aztec Triple Alliance\">Aztec</a> army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Otumba\" title=\"Battle of Otumba\">Battle of Otumba</a>.", "links": [{"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "Aztec Triple Alliance", "link": "https://wikipedia.org/wiki/Aztec_Triple_Alliance"}, {"title": "Battle of Otumba", "link": "https://wikipedia.org/wiki/Battle_of_Otumba"}]}, {"year": "1534", "text": "<PERSON> makes his first contact with aboriginal peoples in what is now Canada.", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his <a href=\"https://wikipedia.org/wiki/First_contact_(anthropology)\" title=\"First contact (anthropology)\">first contact</a> with <a href=\"https://wikipedia.org/wiki/Aboriginal_peoples_in_Canada\" class=\"mw-redirect\" title=\"Aboriginal peoples in Canada\">aboriginal peoples in what is now Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes his <a href=\"https://wikipedia.org/wiki/First_contact_(anthropology)\" title=\"First contact (anthropology)\">first contact</a> with <a href=\"https://wikipedia.org/wiki/Aboriginal_peoples_in_Canada\" class=\"mw-redirect\" title=\"Aboriginal peoples in Canada\">aboriginal peoples in what is now Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "First contact (anthropology)", "link": "https://wikipedia.org/wiki/First_contact_(anthropology)"}, {"title": "Aboriginal peoples in Canada", "link": "https://wikipedia.org/wiki/Aboriginal_peoples_in_Canada"}]}, {"year": "1575", "text": "The Raid of the Redeswire is the last major battle between England and Scotland.", "html": "1575 - The <a href=\"https://wikipedia.org/wiki/Raid_of_the_Redeswire\" title=\"Raid of the Redeswire\">Raid of the Redeswire</a> is the last major battle between <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Raid_of_the_Redeswire\" title=\"Raid of the Redeswire\">Raid of the Redeswire</a> is the last major battle between <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a>.", "links": [{"title": "Raid of the Redeswire", "link": "https://wikipedia.org/wiki/Raid_of_the_Redeswire"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}]}, {"year": "1585", "text": "The Treaty of Nemours abolishes tolerance to Protestants in France.", "html": "1585 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nemours\" title=\"Treaty of Nemours\">Treaty of Nemours</a> abolishes tolerance to <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestants</a> in France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nemours\" title=\"Treaty of Nemours\">Treaty of Nemours</a> abolishes tolerance to <a href=\"https://wikipedia.org/wiki/Protestantism\" title=\"Protestantism\">Protestants</a> in France.", "links": [{"title": "Treaty of Nemours", "link": "https://wikipedia.org/wiki/Treaty_of_Nemours"}, {"title": "Protestantism", "link": "https://wikipedia.org/wiki/Protestantism"}]}, {"year": "1667", "text": "An English fleet completes the destruction of a French merchant fleet off Fort St Pierre, Martinique during the Second Anglo-Dutch War.", "html": "1667 - An English fleet completes the destruction of a French merchant fleet off <a href=\"https://wikipedia.org/wiki/Saint-Pierre,_Martinique\" title=\"Saint-Pierre, Martinique\">Fort St Pierre</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Dutch_War\" title=\"Second Anglo-Dutch War\">Second Anglo-Dutch War</a>.", "no_year_html": "An English fleet completes the destruction of a French merchant fleet off <a href=\"https://wikipedia.org/wiki/Saint-Pierre,_Martinique\" title=\"Saint-Pierre, Martinique\">Fort St Pierre</a>, <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Second_Anglo-Dutch_War\" title=\"Second Anglo-Dutch War\">Second Anglo-Dutch War</a>.", "links": [{"title": "Saint-Pierre, Martinique", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_Martinique"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}, {"title": "Second Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Second_Anglo-Dutch_War"}]}, {"year": "1770", "text": "The Battle of Larga between the Russian Empire and the Ottoman Empire takes place.", "html": "1770 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Larga\" title=\"Battle of Larga\">Battle of Larga</a> between the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> and the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Larga\" title=\"Battle of Larga\">Battle of Larga</a> between the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a> and the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> takes place.", "links": [{"title": "Battle of Larga", "link": "https://wikipedia.org/wiki/Battle_of_Larga"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1777", "text": "American forces retreating from Fort Ticonderoga are defeated in the Battle of Hubbardton.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">American</a> forces retreating from <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a> are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hubbardton\" title=\"Battle of Hubbardton\">Battle of Hubbardton</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">American</a> forces retreating from <a href=\"https://wikipedia.org/wiki/Fort_Ticonderoga\" title=\"Fort Ticonderoga\">Fort Ticonderoga</a> are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hubbardton\" title=\"Battle of Hubbardton\">Battle of Hubbardton</a>.", "links": [{"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Fort Ticonderoga", "link": "https://wikipedia.org/wiki/Fort_Ticonderoga"}, {"title": "Battle of Hubbardton", "link": "https://wikipedia.org/wiki/Battle_of_Hubbardton"}]}, {"year": "1798", "text": "As a result of the XYZ Affair, the US Congress rescinds the Treaty of Alliance with France sparking the \"Quasi-War\".", "html": "1798 - As a result of the <a href=\"https://wikipedia.org/wiki/XYZ_Affair\" title=\"XYZ Affair\">XYZ Affair</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">US Congress</a> rescinds the <a href=\"https://wikipedia.org/wiki/Treaty_of_Alliance_(1778)\" title=\"Treaty of Alliance (1778)\">Treaty of Alliance</a> with France sparking the \"<a href=\"https://wikipedia.org/wiki/Quasi-War\" title=\"Quasi-War\">Quasi-War</a>\".", "no_year_html": "As a result of the <a href=\"https://wikipedia.org/wiki/XYZ_Affair\" title=\"XYZ Affair\">XYZ Affair</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">US Congress</a> rescinds the <a href=\"https://wikipedia.org/wiki/Treaty_of_Alliance_(1778)\" title=\"Treaty of Alliance (1778)\">Treaty of Alliance</a> with France sparking the \"<a href=\"https://wikipedia.org/wiki/Quasi-War\" title=\"Quasi-War\">Quasi-War</a>\".", "links": [{"title": "XYZ Affair", "link": "https://wikipedia.org/wiki/XYZ_Affair"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Treaty of Alliance (1778)", "link": "https://wikipedia.org/wiki/Treaty_of_Alliance_(1778)"}, {"title": "Quasi-War", "link": "https://wikipedia.org/wiki/Quasi-War"}]}, {"year": "1807", "text": "The first Treaty of Tilsit between France and Russia is signed, ending hostilities between the two countries in the War of the Fourth Coalition.", "html": "1807 - The first <a href=\"https://wikipedia.org/wiki/Treaties_of_Tilsit\" title=\"Treaties of Tilsit\">Treaty of Tilsit</a> between France and Russia is signed, ending hostilities between the two countries in the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Treaties_of_Tilsit\" title=\"Treaties of Tilsit\">Treaty of Tilsit</a> between France and Russia is signed, ending hostilities between the two countries in the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "links": [{"title": "Treaties of Tilsit", "link": "https://wikipedia.org/wiki/Treaties_of_Tilsit"}, {"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}]}, {"year": "1834", "text": "In New York City, four nights of rioting against abolitionists began.", "html": "1834 - In New York City, <a href=\"https://wikipedia.org/wiki/Anti-abolitionist_riots_(1834)\" class=\"mw-redirect\" title=\"Anti-abolitionist riots (1834)\">four nights of rioting</a> against <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionists</a> began.", "no_year_html": "In New York City, <a href=\"https://wikipedia.org/wiki/Anti-abolitionist_riots_(1834)\" class=\"mw-redirect\" title=\"Anti-abolitionist riots (1834)\">four nights of rioting</a> against <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionists</a> began.", "links": [{"title": "Anti-abolitionist riots (1834)", "link": "https://wikipedia.org/wiki/Anti-abolitionist_riots_(1834)"}, {"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}]}, {"year": "1846", "text": "US troops occupy Monterey and Yerba Buena, thus beginning the US conquest of California.", "html": "1846 - US troops occupy <a href=\"https://wikipedia.org/wiki/Monterey,_California\" title=\"Monterey, California\">Monterey</a> and <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\"><PERSON><PERSON><PERSON></a>, thus beginning the US <a href=\"https://wikipedia.org/wiki/Conquest_of_California\" title=\"Conquest of California\">conquest of California</a>.", "no_year_html": "US troops occupy <a href=\"https://wikipedia.org/wiki/Monterey,_California\" title=\"Monterey, California\">Monterey</a> and <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\"><PERSON><PERSON><PERSON></a>, thus beginning the US <a href=\"https://wikipedia.org/wiki/Conquest_of_California\" title=\"Conquest of California\">conquest of California</a>.", "links": [{"title": "Monterey, California", "link": "https://wikipedia.org/wiki/Monterey,_California"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}, {"title": "Conquest of California", "link": "https://wikipedia.org/wiki/Conquest_of_California"}]}, {"year": "1863", "text": "The United States begins its first military draft; exemptions cost $300.", "html": "1863 - The United States begins its first <a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">military draft</a>; exemptions cost $300.", "no_year_html": "The United States begins its first <a href=\"https://wikipedia.org/wiki/Conscription\" title=\"Conscription\">military draft</a>; exemptions cost $300.", "links": [{"title": "Conscription", "link": "https://wikipedia.org/wiki/Conscription"}]}, {"year": "1865", "text": "Four conspirators in the assassination of <PERSON> are hanged.", "html": "1865 - Four conspirators in the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassination of <PERSON></a> are hanged.", "no_year_html": "Four conspirators in the <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassination of <PERSON></a> are hanged.", "links": [{"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "The Katipunan is established, the discovery of which by Spanish authorities initiated the Philippine Revolution.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/Katipunan\" title=\"Katipunan\">Katipunan</a> is established, the discovery of which by <a href=\"https://wikipedia.org/wiki/Captaincy_General_of_the_Philippines\" title=\"Captaincy General of the Philippines\">Spanish authorities</a> initiated the <a href=\"https://wikipedia.org/wiki/Philippine_Revolution\" title=\"Philippine Revolution\">Philippine Revolution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Katipunan\" title=\"Katipunan\">Katipunan</a> is established, the discovery of which by <a href=\"https://wikipedia.org/wiki/Captaincy_General_of_the_Philippines\" title=\"Captaincy General of the Philippines\">Spanish authorities</a> initiated the <a href=\"https://wikipedia.org/wiki/Philippine_Revolution\" title=\"Philippine Revolution\">Philippine Revolution</a>.", "links": [{"title": "Katipunan", "link": "https://wikipedia.org/wiki/Katipunan"}, {"title": "Captaincy General of the Philippines", "link": "https://wikipedia.org/wiki/Captaincy_General_of_the_Philippines"}, {"title": "Philippine Revolution", "link": "https://wikipedia.org/wiki/Philippine_Revolution"}]}, {"year": "1898", "text": "US President <PERSON> signs the Newlands Resolution annexing Hawaii as a territory of the United States.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Newlands_Resolution\" title=\"Newlands Resolution\">Newlands Resolution</a> annexing Hawaii as a territory of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Newlands_Resolution\" title=\"Newlands Resolution\">Newlands Resolution</a> annexing Hawaii as a territory of the United States.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Newlands Resolution", "link": "https://wikipedia.org/wiki/Newlands_Resolution"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON> staged his first Follies on the roof of the New York Theater in New York City.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON>.</a> staged his first <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Follies\" title=\"Ziegfeld Follies\">Follies</a> on the roof of the <a href=\"https://wikipedia.org/wiki/Bowery_Theatre\" title=\"Bowery Theatre\">New York Theater</a> in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON> Jr.</a> staged his first <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Follies\" title=\"Ziegfeld Follies\">Follies</a> on the roof of the <a href=\"https://wikipedia.org/wiki/Bowery_Theatre\" title=\"Bowery Theatre\">New York Theater</a> in New York City.", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Ziegfeld Follies", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>gfeld_Follies"}, {"title": "Bowery Theatre", "link": "https://wikipedia.org/wiki/Bowery_Theatre"}]}, {"year": "1911", "text": "The United States, UK, Japan, and Russia sign the North Pacific Fur Seal Convention of 1911 banning open-water seal hunting, the first international treaty to address wildlife preservation issues.", "html": "1911 - The United States, UK, Japan, and Russia sign the <a href=\"https://wikipedia.org/wiki/North_Pacific_Fur_Seal_Convention_of_1911\" title=\"North Pacific Fur Seal Convention of 1911\">North Pacific Fur Seal Convention of 1911</a> banning open-water <a href=\"https://wikipedia.org/wiki/Seal_hunting\" title=\"Seal hunting\">seal hunting</a>, the first international treaty to address wildlife preservation issues.", "no_year_html": "The United States, UK, Japan, and Russia sign the <a href=\"https://wikipedia.org/wiki/North_Pacific_Fur_Seal_Convention_of_1911\" title=\"North Pacific Fur Seal Convention of 1911\">North Pacific Fur Seal Convention of 1911</a> banning open-water <a href=\"https://wikipedia.org/wiki/Seal_hunting\" title=\"Seal hunting\">seal hunting</a>, the first international treaty to address wildlife preservation issues.", "links": [{"title": "North Pacific Fur Seal Convention of 1911", "link": "https://wikipedia.org/wiki/North_Pacific_Fur_Seal_Convention_of_1911"}, {"title": "Seal hunting", "link": "https://wikipedia.org/wiki/Seal_hunting"}]}, {"year": "1915", "text": "The First Battle of the Isonzo comes to an end.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Isonzo\" title=\"First Battle of the Isonzo\">First Battle of the Isonzo</a> comes to an end.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Battle_of_the_Isonzo\" title=\"First Battle of the Isonzo\">First Battle of the Isonzo</a> comes to an end.", "links": [{"title": "First Battle of the Isonzo", "link": "https://wikipedia.org/wiki/First_Battle_of_the_Isonzo"}]}, {"year": "1915", "text": "Colombo Town Guard officer <PERSON> is executed in British Ceylon for allegedly inciting persecution of Muslims.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Colombo_Town_Guard\" title=\"Colombo Town Guard\">Colombo Town Guard</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/British_Ceylon\" title=\"British Ceylon\">British Ceylon</a> for allegedly inciting <a href=\"https://wikipedia.org/wiki/Persecution_of_Muslims\" title=\"Persecution of Muslims\">persecution of Muslims</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colombo_Town_Guard\" title=\"Colombo Town Guard\">Colombo Town Guard</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed in <a href=\"https://wikipedia.org/wiki/British_Ceylon\" title=\"British Ceylon\">British Ceylon</a> for allegedly inciting <a href=\"https://wikipedia.org/wiki/Persecution_of_Muslims\" title=\"Persecution of Muslims\">persecution of Muslims</a>.", "links": [{"title": "Colombo Town Guard", "link": "https://wikipedia.org/wiki/Colombo_Town_Guard"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British Ceylon", "link": "https://wikipedia.org/wiki/British_Ceylon"}, {"title": "Persecution of Muslims", "link": "https://wikipedia.org/wiki/Persecution_of_Muslims"}]}, {"year": "1916", "text": "The New Zealand Labour Party was founded in Wellington.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/New_Zealand_Labour_Party\" title=\"New Zealand Labour Party\">New Zealand Labour Party</a> was founded in <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_Zealand_Labour_Party\" title=\"New Zealand Labour Party\">New Zealand Labour Party</a> was founded in <a href=\"https://wikipedia.org/wiki/Wellington\" title=\"Wellington\">Wellington</a>.", "links": [{"title": "New Zealand Labour Party", "link": "https://wikipedia.org/wiki/New_Zealand_Labour_Party"}, {"title": "Wellington", "link": "https://wikipedia.org/wiki/Wellington"}]}, {"year": "1928", "text": "Sliced bread is sold for the first time (on the inventor's 48th birthday) by the Chillicothe Baking Company of Chillicothe, Missouri.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Sliced_bread\" title=\"Sliced bread\">Sliced bread</a> is sold for the first time (on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">the inventor's</a> <a class=\"mw-selflink-fragment\" href=\"https://wikipedia.org#Births\">48th birthday</a>) by the Chillicothe Baking Company of <a href=\"https://wikipedia.org/wiki/Chillicothe,_Missouri\" title=\"Chillicothe, Missouri\">Chillicothe, Missouri</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sliced_bread\" title=\"Sliced bread\">Sliced bread</a> is sold for the first time (on <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">the inventor's</a> <a class=\"mw-selflink-fragment\" href=\"https://wikipedia.org#Births\">48th birthday</a>) by the Chillicothe Baking Company of <a href=\"https://wikipedia.org/wiki/Chillicothe,_Missouri\" title=\"Chillicothe, Missouri\">Chillicothe, Missouri</a>.", "links": [{"title": "Sliced bread", "link": "https://wikipedia.org/wiki/Sliced_bread"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chillicothe, Missouri", "link": "https://wikipedia.org/wiki/Chillicothe,_Missouri"}]}, {"year": "1930", "text": "Industrialist <PERSON> begins construction of Boulder Dam (now known as Hoover Dam).", "html": "1930 - Industrialist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins construction of Boulder Dam (now known as <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>).", "no_year_html": "Industrialist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Henry <PERSON>\"><PERSON></a> begins construction of Boulder Dam (now known as <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hoover Dam", "link": "https://wikipedia.org/wiki/Hoover_Dam"}]}, {"year": "1930", "text": "The Finnish far-right Lapua Movement organises the Peasant March demonstration in Helsinki to put pressure on the government to prohibit communist activities.", "html": "1930 - The Finnish far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a> organises the <a href=\"https://wikipedia.org/wiki/Peasant_March\" title=\"Peasant March\">Peasant March</a> demonstration in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a> to put pressure on the government to prohibit communist activities.", "no_year_html": "The Finnish far-right <a href=\"https://wikipedia.org/wiki/Lapua_Movement\" title=\"Lapua Movement\">Lapua Movement</a> organises the <a href=\"https://wikipedia.org/wiki/Peasant_March\" title=\"Peasant March\">Peasant March</a> demonstration in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a> to put pressure on the government to prohibit communist activities.", "links": [{"title": "Lapua Movement", "link": "https://wikipedia.org/wiki/Lapua_Movement"}, {"title": "Peasant March", "link": "https://wikipedia.org/wiki/Peasant_March"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1937", "text": "The Marco Polo Bridge Incident (Lugou Bridge) provides the Imperial Japanese Army with a pretext for starting the Second Sino-Japanese War (China-Japan War).", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Marco_<PERSON>_Bridge_Incident\" class=\"mw-redirect\" title=\"Marco Polo Bridge Incident\">Marco <PERSON> Bridge Incident</a> (Lugou Bridge) provides the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> with a pretext for starting the <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a> (China-Japan War).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bridge_Incident\" class=\"mw-redirect\" title=\"Marco Polo Bridge Incident\">Marco <PERSON> Bridge Incident</a> (Lugou Bridge) provides the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> with a pretext for starting the <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a> (China-Japan War).", "links": [{"title": "Marco <PERSON> Incident", "link": "https://wikipedia.org/wiki/Marco_<PERSON>_Bridge_Incident"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}]}, {"year": "1937", "text": "The Peel Commission Report recommends the partition of Palestine, which was the first formal recommendation for partition in the history of Palestine.", "html": "1937 - The <a href=\"https://wikipedia.org/wiki/Peel_Commission\" title=\"Peel Commission\">Peel Commission</a> Report recommends the partition of Palestine, which was the first formal recommendation for partition in the history of Palestine.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peel_Commission\" title=\"Peel Commission\">Peel Commission</a> Report recommends the partition of Palestine, which was the first formal recommendation for partition in the history of Palestine.", "links": [{"title": "Peel Commission", "link": "https://wikipedia.org/wiki/Peel_Commission"}]}, {"year": "1941", "text": "The US occupation of Iceland replaces the UK's occupation.", "html": "1941 - The US <a href=\"https://wikipedia.org/wiki/Occupation_of_Iceland\" class=\"mw-redirect\" title=\"Occupation of Iceland\">occupation of Iceland</a> replaces the UK's occupation.", "no_year_html": "The US <a href=\"https://wikipedia.org/wiki/Occupation_of_Iceland\" class=\"mw-redirect\" title=\"Occupation of Iceland\">occupation of Iceland</a> replaces the UK's occupation.", "links": [{"title": "Occupation of Iceland", "link": "https://wikipedia.org/wiki/Occupation_of_Iceland"}]}, {"year": "1944", "text": "World War II: Largest Banzai charge of the Pacific War at the Battle of Saipan.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Largest <a href=\"https://wikipedia.org/wiki/Banzai_charge\" title=\"Banzai charge\">Banzai charge</a> of the <a href=\"https://wikipedia.org/wiki/Pacific_War\" title=\"Pacific War\">Pacific War</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">Battle of Saipan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Largest <a href=\"https://wikipedia.org/wiki/Banzai_charge\" title=\"Banzai charge\">Banzai charge</a> of the <a href=\"https://wikipedia.org/wiki/Pacific_War\" title=\"Pacific War\">Pacific War</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">Battle of Saipan</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Banzai charge", "link": "https://wikipedia.org/wiki/Banzai_charge"}, {"title": "Pacific War", "link": "https://wikipedia.org/wiki/Pacific_War"}, {"title": "Battle of Saipan", "link": "https://wikipedia.org/wiki/Battle_of_Saipan"}]}, {"year": "1946", "text": "<PERSON> <PERSON> becomes the first American to be canonized.", "html": "1946 - Mother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first American to be canonized.", "no_year_html": "Mother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the first American to be canonized.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON> nearly dies when his XF-11 reconnaissance aircraft prototype crashes in a Beverly Hills neighborhood.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> nearly dies when his <a href=\"https://wikipedia.org/wiki/Hughes_XF-11\" title=\"<PERSON> XF-11\">XF-11</a> <a href=\"https://wikipedia.org/wiki/Reconnaissance_aircraft\" title=\"Reconnaissance aircraft\">reconnaissance aircraft</a> prototype crashes in a <a href=\"https://wikipedia.org/wiki/Beverly_Hills,_California\" title=\"Beverly Hills, California\">Beverly Hills</a> neighborhood.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> nearly dies when his <a href=\"https://wikipedia.org/wiki/Hughes_XF-11\" title=\"<PERSON> XF-11\">XF-11</a> <a href=\"https://wikipedia.org/wiki/Reconnaissance_aircraft\" title=\"Reconnaissance aircraft\">reconnaissance aircraft</a> prototype crashes in a <a href=\"https://wikipedia.org/wiki/Beverly_Hills,_California\" title=\"Beverly Hills, California\">Beverly Hills</a> neighborhood.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hughes XF-11", "link": "https://wikipedia.org/wiki/Hughes_XF-11"}, {"title": "Reconnaissance aircraft", "link": "https://wikipedia.org/wiki/Reconnaissance_aircraft"}, {"title": "Beverly Hills, California", "link": "https://wikipedia.org/wiki/Beverly_Hills,_California"}]}, {"year": "1952", "text": "The ocean liner SS United States passes Bishop Rock on her maiden voyage, breaking the transatlantic speed record to become the fastest passenger ship in the world.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> passes <a href=\"https://wikipedia.org/wiki/Bishop_Rock,_Isles_of_Scilly\" class=\"mw-redirect\" title=\"Bishop Rock, Isles of Scilly\">Bishop Rock</a> on her maiden voyage, breaking the <a href=\"https://wikipedia.org/wiki/Blue_Riband\" title=\"Blue Riband\">transatlantic speed record</a> to become the fastest passenger ship in the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ocean_liner\" title=\"Ocean liner\">ocean liner</a> <a href=\"https://wikipedia.org/wiki/SS_United_States\" title=\"SS United States\">SS <i>United States</i></a> passes <a href=\"https://wikipedia.org/wiki/Bishop_Rock,_Isles_of_Scilly\" class=\"mw-redirect\" title=\"Bishop Rock, Isles of Scilly\">Bishop Rock</a> on her maiden voyage, breaking the <a href=\"https://wikipedia.org/wiki/Blue_Riband\" title=\"Blue Riband\">transatlantic speed record</a> to become the fastest passenger ship in the world.", "links": [{"title": "Ocean liner", "link": "https://wikipedia.org/wiki/Ocean_liner"}, {"title": "SS United States", "link": "https://wikipedia.org/wiki/SS_United_States"}, {"title": "Bishop Rock, Isles of Scilly", "link": "https://wikipedia.org/wiki/Bishop_Rock,_Isles_of_Scilly"}, {"title": "Blue Riband", "link": "https://wikipedia.org/wiki/Blue_Riband"}]}, {"year": "1953", "text": "<PERSON> \"<PERSON>\" <PERSON> sets out on a trip through Bolivia, Peru, Ecuador, Panama, Costa Rica, Nicaragua, Honduras, and El Salvador.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Che_Guevara\" title=\"Che Guevara\"><PERSON> \"<PERSON><PERSON>\" <PERSON>uevara</a> sets out on a trip through <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>, <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>, and <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ch<PERSON>_Guevara\" title=\"Che Guevara\"><PERSON> \"<PERSON>\" <PERSON>ra</a> sets out on a trip through <a href=\"https://wikipedia.org/wiki/Bolivia\" title=\"Bolivia\">Bolivia</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, <a href=\"https://wikipedia.org/wiki/Costa_Rica\" title=\"Costa Rica\">Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>, <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a>, and <a href=\"https://wikipedia.org/wiki/El_Salvador\" title=\"El Salvador\">El Salvador</a>.", "links": [{"title": "<PERSON>e Guevara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}, {"title": "Bolivia", "link": "https://wikipedia.org/wiki/Bolivia"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Costa Rica", "link": "https://wikipedia.org/wiki/Costa_Rica"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "El Salvador", "link": "https://wikipedia.org/wiki/El_Salvador"}]}, {"year": "1958", "text": "US President <PERSON> signs the Alaska Statehood Act into law.", "html": "1958 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Alaska_Statehood_Act\" title=\"Alaska Statehood Act\">Alaska Statehood Act</a> into <a href=\"https://wikipedia.org/wiki/Law_of_the_United_States\" title=\"Law of the United States\">law</a>.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Alaska_Statehood_Act\" title=\"Alaska Statehood Act\">Alaska Statehood Act</a> into <a href=\"https://wikipedia.org/wiki/Law_of_the_United_States\" title=\"Law of the United States\">law</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Alaska Statehood Act", "link": "https://wikipedia.org/wiki/Alaska_Statehood_Act"}, {"title": "Law of the United States", "link": "https://wikipedia.org/wiki/Law_of_the_United_States"}]}, {"year": "1959", "text": "Venus occults the star <PERSON><PERSON>. This rare event is used to determine the diameter of Venus and the structure of the Venusian atmosphere.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> <a href=\"https://wikipedia.org/wiki/Occultation\" title=\"Occultation\">occults</a> the star <a href=\"https://wikipedia.org/wiki/Regulus\" title=\"Regulus\">Regulus</a>. This rare event is used to determine the diameter of Venus and the structure of the Venusian atmosphere.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> <a href=\"https://wikipedia.org/wiki/Occultation\" title=\"Occultation\">occults</a> the star <a href=\"https://wikipedia.org/wiki/Regulus\" title=\"Regulus\">Regulus</a>. This rare event is used to determine the diameter of Venus and the structure of the Venusian atmosphere.", "links": [{"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "Occultation", "link": "https://wikipedia.org/wiki/Occultation"}, {"title": "Regulus", "link": "https://wikipedia.org/wiki/Regulus"}]}, {"year": "1962", "text": "Alitalia Flight 771 crashes in Junnar, Maharashtra, India, killing 94 people.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Alitalia_Flight_771\" title=\"Alitalia Flight 771\">Alitalia Flight 771</a> crashes in <a href=\"https://wikipedia.org/wiki/Junnar\" title=\"Junnar\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 94 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alitalia_Flight_771\" title=\"Alitalia Flight 771\">Alitalia Flight 771</a> crashes in <a href=\"https://wikipedia.org/wiki/Junnar\" title=\"Junnar\">Jun<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 94 people.", "links": [{"title": "Alitalia Flight 771", "link": "https://wikipedia.org/wiki/Alitalia_Flight_771"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nar"}, {"title": "Maharashtra", "link": "https://wikipedia.org/wiki/Maharashtra"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "1963", "text": "Buddhist crisis: Police commanded by <PERSON><PERSON>, brother and chief political adviser of South Vietnam President <PERSON><PERSON>, attacked a group of American journalists who were covering a protest.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: Police commanded by <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, brother and chief political adviser of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Double_Seven_Day_scuffle\" title=\"Double Seven Day scuffle\">attacked</a> a group of American journalists who were covering a protest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>: Police commanded by <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, brother and chief political adviser of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Double_Seven_Day_scuffle\" title=\"Double Seven Day scuffle\">attacked</a> a group of American journalists who were covering a protest.", "links": [{"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Nhu"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Double Seven Day scuffle", "link": "https://wikipedia.org/wiki/Double_Seven_Day_scuffle"}]}, {"year": "1978", "text": "The Solomon Islands becomes independent from the United Kingdom.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> becomes independent from the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a> becomes independent from the United Kingdom.", "links": [{"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}]}, {"year": "1980", "text": "Institution of sharia law in Iran.", "html": "1980 - Institution of <a href=\"https://wikipedia.org/wiki/Sharia_law\" class=\"mw-redirect\" title=\"Sharia law\">sharia law</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>.", "no_year_html": "Institution of <a href=\"https://wikipedia.org/wiki/Sharia_law\" class=\"mw-redirect\" title=\"Sharia law\">sharia law</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>.", "links": [{"title": "Sharia law", "link": "https://wikipedia.org/wiki/Sharia_law"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1980", "text": "During the Lebanese Civil War, 83 Tiger militants are killed during what will be known as the Safra massacre.", "html": "1980 - During the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>, 83 <a href=\"https://wikipedia.org/wiki/Tigers_Militia\" title=\"Tigers Militia\">Tiger</a> militants are killed during what will be known as the <a href=\"https://wikipedia.org/wiki/Safra_massacre\" title=\"Safra massacre\">Safra massacre</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Lebanese_Civil_War\" title=\"Lebanese Civil War\">Lebanese Civil War</a>, 83 <a href=\"https://wikipedia.org/wiki/Tigers_Militia\" title=\"Tigers Militia\">Tiger</a> militants are killed during what will be known as the <a href=\"https://wikipedia.org/wiki/Safra_massacre\" title=\"Safra massacre\">Safra massacre</a>.", "links": [{"title": "Lebanese Civil War", "link": "https://wikipedia.org/wiki/Lebanese_Civil_War"}, {"title": "Tigers Militia", "link": "https://wikipedia.org/wiki/Tigers_Militia"}, {"title": "Safra massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_massacre"}]}, {"year": "1981", "text": "US President <PERSON> nominates <PERSON> to become the first female member of the Supreme Court of the United States.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a> to become the first female member of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">US President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a> to become the first female member of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}]}, {"year": "1983", "text": "Cold War: <PERSON>, a US schoolgirl, flies to the Soviet Union at the invitation of Secretary General <PERSON>.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a US schoolgirl, flies to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> at the invitation of <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">Secretary General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a US schoolgirl, flies to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> at the invitation of <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union\" title=\"List of leaders of the Soviet Union\">Secretary General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "List of leaders of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_leaders_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON> becomes the youngest male player ever to win Wimbledon at age 17.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the youngest male player ever to win <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon</a> at age 17.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the youngest male player ever to win <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledon</a> at age 17.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Championships, Wimbledon", "link": "https://wikipedia.org/wiki/The_Championships,_Wimbledon"}]}, {"year": "1991", "text": "Yugoslav Wars: The Brioni Agreement ends the ten-day independence war in Slovenia against the rest of the Socialist Federal Republic of Yugoslavia.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Brioni_Agreement\" title=\"Brioni Agreement\">Brioni Agreement</a> ends the <a href=\"https://wikipedia.org/wiki/Ten-Day_War\" title=\"Ten-Day War\">ten-day independence war</a> in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> against the rest of the <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Brioni_Agreement\" title=\"Brioni Agreement\">Brioni Agreement</a> ends the <a href=\"https://wikipedia.org/wiki/Ten-Day_War\" title=\"Ten-Day War\">ten-day independence war</a> in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> against the rest of the <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Socialist Federal Republic of Yugoslavia</a>.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "Brioni Agreement", "link": "https://wikipedia.org/wiki/Brioni_Agreement"}, {"title": "Ten-Day War", "link": "https://wikipedia.org/wiki/Ten-Day_War"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1992", "text": "The New York Court of Appeals rules that women have the same right as men to go topless in public.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/New_York_Court_of_Appeals\" title=\"New York Court of Appeals\">New York Court of Appeals</a> rules that women have the same right as men to go <a href=\"https://wikipedia.org/wiki/Toplessness\" title=\"Toplessness\">topless</a> in public.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Court_of_Appeals\" title=\"New York Court of Appeals\">New York Court of Appeals</a> rules that women have the same right as men to go <a href=\"https://wikipedia.org/wiki/Toplessness\" title=\"Toplessness\">topless</a> in public.", "links": [{"title": "New York Court of Appeals", "link": "https://wikipedia.org/wiki/New_York_Court_of_Appeals"}, {"title": "Toplessness", "link": "https://wikipedia.org/wiki/Toplessness"}]}, {"year": "1997", "text": "The Turkish Armed Forces withdraw from northern Iraq after assisting the Kurdistan Democratic Party in the Iraqi Kurdish Civil War.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> withdraw from northern <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> after <a href=\"https://wikipedia.org/wiki/Operation_Hammer_(1997)\" title=\"Operation Hammer (1997)\">assisting</a> the <a href=\"https://wikipedia.org/wiki/Kurdistan_Democratic_Party\" title=\"Kurdistan Democratic Party\">Kurdistan Democratic Party</a> in the <a href=\"https://wikipedia.org/wiki/Iraqi_Kurdish_Civil_War\" title=\"Iraqi Kurdish Civil War\">Iraqi Kurdish Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> withdraw from northern <a href=\"https://wikipedia.org/wiki/Ba%27athist_Iraq\" title=\"Ba'athist Iraq\">Iraq</a> after <a href=\"https://wikipedia.org/wiki/Operation_Hammer_(1997)\" title=\"Operation Hammer (1997)\">assisting</a> the <a href=\"https://wikipedia.org/wiki/Kurdistan_Democratic_Party\" title=\"Kurdistan Democratic Party\">Kurdistan Democratic Party</a> in the <a href=\"https://wikipedia.org/wiki/Iraqi_Kurdish_Civil_War\" title=\"Iraqi Kurdish Civil War\">Iraqi Kurdish Civil War</a>.", "links": [{"title": "Turkish Armed Forces", "link": "https://wikipedia.org/wiki/Turkish_Armed_Forces"}, {"title": "Ba'athist Iraq", "link": "https://wikipedia.org/wiki/Ba%27athist_Iraq"}, {"title": "Operation Hammer (1997)", "link": "https://wikipedia.org/wiki/Operation_Hammer_(1997)"}, {"title": "Kurdistan Democratic Party", "link": "https://wikipedia.org/wiki/Kurdistan_Democratic_Party"}, {"title": "Iraqi Kurdish Civil War", "link": "https://wikipedia.org/wiki/Iraqi_Kurdish_Civil_War"}]}, {"year": "2003", "text": "NASA Opportunity rover, MER-B or Mars Exploration Rover-B, was launched into space aboard a Delta II rocket.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> <a href=\"https://wikipedia.org/wiki/Opportunity_rover\" class=\"mw-redirect\" title=\"Opportunity rover\">Opportunity rover</a>, MER-B or Mars Exploration Rover-B, was launched into space aboard a <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> rocket.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> <a href=\"https://wikipedia.org/wiki/Opportunity_rover\" class=\"mw-redirect\" title=\"Opportunity rover\">Opportunity rover</a>, MER-B or Mars Exploration Rover-B, was launched into space aboard a <a href=\"https://wikipedia.org/wiki/Delta_II\" title=\"Delta II\">Delta II</a> rocket.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Opportunity rover", "link": "https://wikipedia.org/wiki/Opportunity_rover"}, {"title": "Delta II", "link": "https://wikipedia.org/wiki/Delta_II"}]}, {"year": "2005", "text": "A series of four explosions occurs on London's transport system, killing 56 people, including four suicide bombers, and injuring over 700 others.", "html": "2005 - A series of <a href=\"https://wikipedia.org/wiki/7_July_2005_London_bombings\" title=\"7 July 2005 London bombings\">four explosions</a> occurs on <a href=\"https://wikipedia.org/wiki/Transport_in_London\" title=\"Transport in London\">London's transport system</a>, killing 56 people, including four <a href=\"https://wikipedia.org/wiki/Suicide_bombers\" class=\"mw-redirect\" title=\"Suicide bombers\">suicide bombers</a>, and injuring over 700 others.", "no_year_html": "A series of <a href=\"https://wikipedia.org/wiki/7_July_2005_London_bombings\" title=\"7 July 2005 London bombings\">four explosions</a> occurs on <a href=\"https://wikipedia.org/wiki/Transport_in_London\" title=\"Transport in London\">London's transport system</a>, killing 56 people, including four <a href=\"https://wikipedia.org/wiki/Suicide_bombers\" class=\"mw-redirect\" title=\"Suicide bombers\">suicide bombers</a>, and injuring over 700 others.", "links": [{"title": "7 July 2005 London bombings", "link": "https://wikipedia.org/wiki/7_July_2005_London_bombings"}, {"title": "Transport in London", "link": "https://wikipedia.org/wiki/Transport_in_London"}, {"title": "Suicide bombers", "link": "https://wikipedia.org/wiki/Suicide_bombers"}]}, {"year": "2007", "text": "The first Live Earth benefit concert was held in 11 locations around the world.", "html": "2007 - The first <a href=\"https://wikipedia.org/wiki/Live_Earth_(2007_concert)\" title=\"Live Earth (2007 concert)\">Live Earth</a> benefit concert was held in 11 locations around the world.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Live_Earth_(2007_concert)\" title=\"Live Earth (2007 concert)\">Live Earth</a> benefit concert was held in 11 locations around the world.", "links": [{"title": "Live Earth (2007 concert)", "link": "https://wikipedia.org/wiki/Live_Earth_(2007_concert)"}]}, {"year": "2012", "text": "At least 172 people are killed in a flash flood in the Krasnodar Krai region of Russia.", "html": "2012 - At least 172 people are killed in a <a href=\"https://wikipedia.org/wiki/2012_Russian_floods\" class=\"mw-redirect\" title=\"2012 Russian floods\">flash flood</a> in the <a href=\"https://wikipedia.org/wiki/Krasnodar_Krai\" title=\"Krasnodar Krai\">Krasnodar Krai</a> region of <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "no_year_html": "At least 172 people are killed in a <a href=\"https://wikipedia.org/wiki/2012_Russian_floods\" class=\"mw-redirect\" title=\"2012 Russian floods\">flash flood</a> in the <a href=\"https://wikipedia.org/wiki/Krasnodar_Krai\" title=\"Krasnodar Krai\">Krasnodar Krai</a> region of <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "links": [{"title": "2012 Russian floods", "link": "https://wikipedia.org/wiki/2012_Russian_floods"}, {"title": "Krasnodar Krai", "link": "https://wikipedia.org/wiki/Krasnodar_Krai"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "2013", "text": "A De Havilland Otter air taxi crashes in Soldotna, Alaska, killing ten people.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canada_DHC-3_Otter\" title=\"De Havilland Canada DHC-3 Otter\"><PERSON> <PERSON> Otter</a> <a href=\"https://wikipedia.org/wiki/Air_taxi\" title=\"Air taxi\">air taxi</a> <a href=\"https://wikipedia.org/wiki/2013_Rediske_Air_DHC-3_Otter_crash\" title=\"2013 Rediske Air DHC-3 Otter crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Soldotna,_Alaska\" title=\"Soldotna, Alaska\">Soldotna, Alaska</a>, killing ten people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canada_DHC-3_Otter\" title=\"De Havilland Canada DHC-3 Otter\"><PERSON> <PERSON> Otter</a> <a href=\"https://wikipedia.org/wiki/Air_taxi\" title=\"Air taxi\">air taxi</a> <a href=\"https://wikipedia.org/wiki/2013_Rediske_Air_DHC-3_Otter_crash\" title=\"2013 Rediske Air DHC-3 Otter crash\">crashes</a> in <a href=\"https://wikipedia.org/wiki/Soldotna,_Alaska\" title=\"Soldotna, Alaska\">Soldotna, Alaska</a>, killing ten people.", "links": [{"title": "De Havilland Canada DHC-3 Otter", "link": "https://wikipedia.org/wiki/<PERSON>_Havilland_Canada_DHC-3_Otter"}, {"title": "Air taxi", "link": "https://wikipedia.org/wiki/Air_taxi"}, {"title": "2013 Rediske Air DHC-3 Otter crash", "link": "https://wikipedia.org/wiki/2013_Rediske_Air_DHC-3_<PERSON>tter_crash"}, {"title": "Soldotna, Alaska", "link": "https://wikipedia.org/wiki/Soldotna,_Alaska"}]}, {"year": "2016", "text": "Ex-US Army soldier <PERSON> shoots fourteen policemen during an anti-police protest in downtown Dallas, Texas, killing five of them. He is subsequently killed by a robot-delivered bomb.", "html": "2016 - Ex-US Army soldier <PERSON> <a href=\"https://wikipedia.org/wiki/2016_shooting_of_Dallas_police_officers\" title=\"2016 shooting of Dallas police officers\">shoots fourteen policemen</a> during an anti-police protest in downtown <a href=\"https://wikipedia.org/wiki/Dallas\" title=\"Dallas\">Dallas, Texas</a>, killing five of them. He is subsequently killed by a robot-delivered bomb.", "no_year_html": "Ex-US Army soldier <PERSON> <a href=\"https://wikipedia.org/wiki/2016_shooting_of_Dallas_police_officers\" title=\"2016 shooting of Dallas police officers\">shoots fourteen policemen</a> during an anti-police protest in downtown <a href=\"https://wikipedia.org/wiki/Dallas\" title=\"Dallas\">Dallas, Texas</a>, killing five of them. He is subsequently killed by a robot-delivered bomb.", "links": [{"title": "2016 shooting of Dallas police officers", "link": "https://wikipedia.org/wiki/2016_shooting_of_Dallas_police_officers"}, {"title": "Dallas", "link": "https://wikipedia.org/wiki/Dallas"}]}, {"year": "2017", "text": "The Treaty on the Prohibition of Nuclear Weapons was adopted with 122 countries voting in favour.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Prohibition_of_Nuclear_Weapons\" title=\"Treaty on the Prohibition of Nuclear Weapons\">Treaty on the Prohibition of Nuclear Weapons</a> was adopted with 122 countries voting in favour.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_on_the_Prohibition_of_Nuclear_Weapons\" title=\"Treaty on the Prohibition of Nuclear Weapons\">Treaty on the Prohibition of Nuclear Weapons</a> was adopted with 122 countries voting in favour.", "links": [{"title": "Treaty on the Prohibition of Nuclear Weapons", "link": "https://wikipedia.org/wiki/Treaty_on_the_Prohibition_of_Nuclear_Weapons"}]}, {"year": "2019", "text": "The United States women's national soccer team defeated the Netherlands 2-0 at the 2019 FIFA Women's World Cup Final in Lyon, France.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team\" title=\"United States women's national soccer team\">United States women's national soccer team</a> defeated the <a href=\"https://wikipedia.org/wiki/Netherlands_women%27s_national_football_team\" title=\"Netherlands women's national football team\">Netherlands</a> 2-0 at the <a href=\"https://wikipedia.org/wiki/2019_FIFA_Women%27s_World_Cup_Final\" class=\"mw-redirect\" title=\"2019 FIFA Women's World Cup Final\">2019 FIFA Women's World Cup Final</a> in <a href=\"https://wikipedia.org/wiki/Lyon\" title=\"Lyon\">Lyon</a>, France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team\" title=\"United States women's national soccer team\">United States women's national soccer team</a> defeated the <a href=\"https://wikipedia.org/wiki/Netherlands_women%27s_national_football_team\" title=\"Netherlands women's national football team\">Netherlands</a> 2-0 at the <a href=\"https://wikipedia.org/wiki/2019_FIFA_Women%27s_World_Cup_Final\" class=\"mw-redirect\" title=\"2019 FIFA Women's World Cup Final\">2019 FIFA Women's World Cup Final</a> in <a href=\"https://wikipedia.org/wiki/Lyon\" title=\"Lyon\">Lyon</a>, France.", "links": [{"title": "United States women's national soccer team", "link": "https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team"}, {"title": "Netherlands women's national football team", "link": "https://wikipedia.org/wiki/Netherlands_women%27s_national_football_team"}, {"title": "2019 FIFA Women's World Cup Final", "link": "https://wikipedia.org/wiki/2019_FIFA_Women%27s_World_Cup_Final"}, {"title": "Lyon", "link": "https://wikipedia.org/wiki/Lyon"}]}, {"year": "2022", "text": "<PERSON> announces his resignation as leader of the Conservative Party following days of pressure from the Members of Parliament (MPs) during the July 2022 United Kingdom government crisis.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his resignation as <a href=\"https://wikipedia.org/wiki/Leader_of_the_Conservative_Party_(UK)\" title=\"Leader of the Conservative Party (UK)\">leader of the Conservative Party</a> following days of pressure from the Members of Parliament (MPs) during the <a href=\"https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis\" title=\"July 2022 United Kingdom government crisis\">July 2022 United Kingdom government crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his resignation as <a href=\"https://wikipedia.org/wiki/Leader_of_the_Conservative_Party_(UK)\" title=\"Leader of the Conservative Party (UK)\">leader of the Conservative Party</a> following days of pressure from the Members of Parliament (MPs) during the <a href=\"https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis\" title=\"July 2022 United Kingdom government crisis\">July 2022 United Kingdom government crisis</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leader of the Conservative Party (UK)", "link": "https://wikipedia.org/wiki/Leader_of_the_Conservative_Party_(UK)"}, {"title": "July 2022 United Kingdom government crisis", "link": "https://wikipedia.org/wiki/July_2022_United_Kingdom_government_crisis"}]}], "Births": [{"year": "611", "text": "<PERSON><PERSON><PERSON>, daughter of Byzantine emperor <PERSON><PERSON><PERSON>", "html": "611 - <a href=\"https://wikipedia.org/wiki/Eudoxia_Epiphania\" title=\"Eudoxia Epiphania\"><PERSON><PERSON><PERSON>phania</a>, daughter of <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eudoxia_Epiphania\" title=\"Eudoxia Epiphania\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>", "links": [{"title": "E<PERSON>xia E<PERSON>phania", "link": "https://wikipedia.org/wiki/Eudoxia_Epiphania"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1053", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1129)", "html": "1053 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1129)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1129)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1119", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1164)", "html": "1119 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1164)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1164)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1207", "text": "<PERSON> of Hungary (d. 1231)", "html": "1207 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (d. 1231)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (d. 1231)", "links": [{"title": "Elizabeth of Hungary", "link": "https://wikipedia.org/wiki/Elizabeth_of_Hungary"}]}, {"year": "1482", "text": "<PERSON><PERSON><PERSON>, Polish archbishop (d. 1537)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish archbishop (d. 1537)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1528", "text": "Archduchess <PERSON> of Austria (d. 1590)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduchess_<PERSON>_of_Austria\" title=\"Archduchess <PERSON> of Austria\">Archduchess <PERSON> of Austria</a> (d. 1590)", "links": [{"title": "Archduchess <PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>duchess_<PERSON>_<PERSON>_Austria"}]}, {"year": "1540", "text": "<PERSON>, King of Hungary (d. 1571)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1polya\" title=\"<PERSON>\"><PERSON></a>, King of Hungary (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1polya\" title=\"<PERSON>\"><PERSON></a>, King of Hungary (d. 1571)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>%C3%A1polya"}]}, {"year": "1585", "text": "<PERSON>, 21st Earl of Arundel, English courtier and politician, Lord Lieutenant of Northumberland (d. 1646)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_21st_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 21st Earl of Arundel\"><PERSON>, 21st Earl of Arundel</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northumberland\" title=\"Lord Lieutenant of Northumberland\">Lord Lieutenant of Northumberland</a> (d. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_21st_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 21st Earl of Arundel\"><PERSON>, 21st Earl of Arundel</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Northumberland\" title=\"Lord Lieutenant of Northumberland\">Lord Lieutenant of Northumberland</a> (d. 1646)", "links": [{"title": "<PERSON>, 21st Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_21st_Earl_of_Arundel"}, {"title": "Lord Lieutenant of Northumberland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Northumberland"}]}, {"year": "1588", "text": "<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg (d. 1640)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a> (d. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Waldeck-Eisenberg\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg\"><PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg</a> (d. 1640)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Count of Waldeck-Eisenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_<PERSON>_Waldeck-<PERSON>ise<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, Governor of Massachusetts Bay Colony (d. 1679)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of Massachusetts Bay Colony (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Governor of Massachusetts Bay Colony (d. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, French merchant, invented the <PERSON><PERSON><PERSON><PERSON> loom (d. 1834)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French merchant, invented the <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>rd_loom\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> loom\"><PERSON><PERSON><PERSON><PERSON> loom</a> (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French merchant, invented the <a href=\"https://wikipedia.org/wiki/J<PERSON>quard_loom\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> loom\"><PERSON><PERSON><PERSON><PERSON> loom</a> (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> loom", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_loom"}]}, {"year": "1766", "text": "<PERSON>, French general (d. 1815)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, American poet and religious writer (d. 1914)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and religious writer (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and religious writer (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian painter and illustrator (d. 1898)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/F%C3%A9licien_Rops\" title=\"Félicien Rops\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian painter and illustrator (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9licien_Rops\" title=\"Félicien Rops\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian painter and illustrator (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9<PERSON>ien_Rops"}]}, {"year": "1843", "text": "<PERSON><PERSON>, Italian physician and pathologist, Nobel Prize laureate (d. 1926)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Camillo_Golgi\" title=\"Camillo Golgi\"><PERSON><PERSON></a>, Italian physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Camillo_Golgi\" title=\"Camillo Golgi\"><PERSON><PERSON></a>, Italian physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camillo_Golgi"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1846", "text": "<PERSON>, Estonian physician and author (d. 1916)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and author (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physician and author (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Brazilian politician, 5th President of Brazil (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>\"><PERSON></a>, Brazilian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (d. 1919)", "links": [{"title": "<PERSON> de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1851", "text": "<PERSON>, American minister and composer (d. 1933)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and composer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and composer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, German author and playwright (d. 1920)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician (d. 1945)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Re<PERSON>mal<PERSON>_Sri<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Sri<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Srinivas<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rettamalai_<PERSON>an"}]}, {"year": "1860", "text": "<PERSON>, Austrian composer and conductor (d. 1911)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, American geneticist (d. 1912)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist (d. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American academic (d. 1938)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, French painter and mayor (d. 1949)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ler\"><PERSON><PERSON><PERSON></a>, French painter and mayor (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sadler\"><PERSON><PERSON><PERSON></a>, French painter and mayor (d. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ler"}]}, {"year": "1874", "text": "<PERSON>, German lawyer and jurist (d. 1945)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and jurist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American engineer, invented sliced bread (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Sliced_bread\" title=\"Sliced bread\">sliced bread</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Sliced_bread\" title=\"Sliced bread\">sliced bread</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sliced bread", "link": "https://wikipedia.org/wiki/Sliced_bread"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Belarusian poet and writer (d. 1941)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian poet and writer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian poet and writer (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Finnish conductor and composer (d. 1918)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, German author and playwright (d. 1958)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>wang<PERSON>\"><PERSON></a>, German author and playwright (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Fe<PERSON>wanger"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Japanese general and poet (d. 1945)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and poet (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general and poet (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American model and actress (d. 1921)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Virginia_Rappe\" title=\"Virginia Rappe\"><PERSON></a>, American model and actress (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Rappe\" title=\"Virginia Rappe\"><PERSON></a>, American model and actress (d. 1921)", "links": [{"title": "Virginia Rappe", "link": "https://wikipedia.org/wiki/Virginia_Rappe"}]}, {"year": "1893", "text": "<PERSON>, American historian and author (d. 1972)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Croatian author, poet, and playwright (d. 1981)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian author, poet, and playwright (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian author, poet, and playwright (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Krle%C5%BEa"}]}, {"year": "1898", "text": "<PERSON>, American football player and coach (d. 1985)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American director and producer (d. 1983)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German stage and silent film actress (d. 1944)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German stage and silent film actress (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German stage and silent film actress (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>d"}]}, {"year": "1900", "text": "<PERSON>, American general (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American director and producer (d. 1973)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Italian actor and director (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and director (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>itt<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and director (d. 1974)", "links": [{"title": "Vittorio <PERSON>", "link": "https://wikipedia.org/wiki/Vitt<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Japanese cinematographer and producer (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese cinematographer and producer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese cinematographer and producer (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American baseball player and manager (d. 2005)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French chef and author (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French mathematician (d. 1972)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Croatian-American mathematician and academic (d. 1970)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American mathematician and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-American mathematician and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Austrian zither player and composer (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian <a href=\"https://wikipedia.org/wiki/Zither\" title=\"<PERSON>ither\">zither</a> player and composer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian <a href=\"https://wikipedia.org/wiki/Zither\" title=\"<PERSON>ither\">zither</a> player and composer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zither"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, American baseball player and coach (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and coach (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American science fiction writer and screenwriter (d. 1988)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American author and academic (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>ilo_<PERSON><PERSON>_<PERSON>\" title=\"Revilo P<PERSON> Oliver\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Revilo P<PERSON> Oliver\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and academic (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ilo_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, German tennis player (d. 1976)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian painter and author (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Italian-American composer (d. 2007)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-American composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-American composer (d. 2007)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American singer and pianist (d. 2011)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Pine<PERSON>_Perkins\" title=\"Pine<PERSON> Perkins\"><PERSON><PERSON></a>, American singer and pianist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pinetop_Perkins\" title=\"Pine<PERSON> Perkins\"><PERSON><PERSON></a>, American singer and pianist (d. 2011)", "links": [{"title": "Pinetop Perkins", "link": "https://wikipedia.org/wiki/Pine<PERSON>_Perkins"}]}, {"year": "1915", "text": "<PERSON>, American novelist and poet (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Salvadoran general and politician, President of El Salvador (d. 2003)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Fidel_S%C3%A1nchez_Hern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Salvadoran general and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fidel_S%C3%A1nchez_Hern%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Salvadoran general and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fidel_S%C3%A1nchez_Hern%C3%A1ndez"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Canadian-American actress and singer (d. 2014)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and singer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_Withers"}]}, {"year": "1918", "text": "<PERSON>, American head basketball coach (d. 2016)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American head basketball coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American head basketball coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Chinese businessman (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jing Shuping\"><PERSON></a>, Chinese businessman (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jing Shuping\"><PERSON></a>, Chinese businessman (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English actor (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American boxer (d. 1975)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>rd_<PERSON>\" title=\"<PERSON>zzard <PERSON>\"><PERSON><PERSON><PERSON></a>, American boxer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>zzard <PERSON>\"><PERSON><PERSON><PERSON></a>, American boxer (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German lieutenant and politician (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American director, producer, and screenwriter (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American Air Force lieutenant general (d. 2024)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force lieutenant general (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force lieutenant general (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Romanian actor, director, and screenwriter (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor, director, and screenwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian actor, director, and screenwriter (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Liviu_Ciulei"}]}, {"year": "1923", "text": "<PERSON>., American politician (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Whitney_<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Seymour Jr.\"><PERSON> Jr.</a>, American politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> North Seymour Jr.\"><PERSON> Jr.</a>, American politician (d. 2019)", "links": [{"title": "<PERSON> Seymour Jr.", "link": "https://wikipedia.org/wiki/Whitney_North_Seymour_Jr."}]}, {"year": "1923", "text": "<PERSON>, Argentinian guitarist and composer (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Argentinian guitarist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BA\" title=\"<PERSON>\"><PERSON></a>, Argentinian guitarist and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eduardo_Fal%C3%BA"}]}, {"year": "1924", "text": "<PERSON>, Russian neuroscientist and psychologist (d. 2008)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian neuroscientist and psychologist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian neuroscientist and psychologist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American singer and guitarist (d. 1977)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary <PERSON>\"><PERSON></a>, American singer and guitarist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mary Ford\"><PERSON></a>, American singer and guitarist (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Nigerian sprinter and long jumper (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian sprinter and long jumper (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian sprinter and long jumper (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Filipino director, producer, and screenwriter (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Russian painter (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American radio host (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Cambodian politician (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cambodian politician (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuon_<PERSON>ea"}]}, {"year": "1926", "text": "<PERSON>, Urdu poet (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Urdu poet (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Urdu poet (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American lawyer and politician, 34th Illinois Secretary of State (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Illinois_Secretary_of_State\" title=\"Illinois Secretary of State\">Illinois Secretary of State</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 34th <a href=\"https://wikipedia.org/wiki/Illinois_Secretary_of_State\" title=\"Illinois Secretary of State\">Illinois Secretary of State</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Illinois Secretary of State", "link": "https://wikipedia.org/wiki/Illinois_Secretary_of_State"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American trumpet player and conductor", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Se<PERSON><PERSON>en"}]}, {"year": "1928", "text": "<PERSON>, English actress (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "Ka<PERSON>wa Sikota Zambian nurse and health official (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Kapelwa_Sikota\" title=\"Kapelwa Sikota\">Kapelwa Sikota</a> Zambian nurse and health official (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kapelwa_Sikota\" title=\"Kapelwa Sikota\">Kapelwa Sikota</a> Zambian nurse and health official (d. 2006)", "links": [{"title": "Kapelwa Sikota", "link": "https://wikipedia.org/wiki/Kapelwa_Sikota"}]}, {"year": "1929", "text": "<PERSON>, Pakistani journalist and poet (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani journalist and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani journalist and poet (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Italian writer, journalist, and historian", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Italian writer, journalist, and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Italian writer, journalist, and historian", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Scottish mountaineer and author (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish mountaineer and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish mountaineer and author (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American cardinal", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American saxophonist and composer (d. 1986)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, 2nd President of Republika Srpska", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Biljana_Plav%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 2nd President of Republika Srpska", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Biljana_Plav%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 2nd President of Republika Srpska", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Biljana_Plav%C5%A1i%C4%87"}]}, {"year": "1931", "text": "<PERSON>, American author and academic (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1932", "text": "<PERSON><PERSON> <PERSON><PERSON>, American physician and author (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (author)\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>_(author)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (author)\"><PERSON><PERSON> <PERSON><PERSON></a>, American physician and author (d. 2011)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(author)"}]}, {"year": "1932", "text": "<PERSON>, Austrian jazz keyboardist and composer (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian jazz keyboardist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian jazz keyboardist and composer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American historian and author (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, British zoologist (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British zoologist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British zoologist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Italian-Taiwanese Roman Catholic priest", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-Taiwanese Roman Catholic priest", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian-Taiwanese Roman Catholic priest", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, German mathematician and academic (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mathematician and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Swiss race car driver (d. 1971)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss race car driver (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss race car driver (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Greek singer-songwriter (d. 1980)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer-songwriter (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Hong Kong businessman and politician, 1st Chief Executive of Hong Kong", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ee-hwa\" title=\"<PERSON><PERSON>ee-hwa\"><PERSON><PERSON>-hwa</a>, Hong Kong businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong\" title=\"Chief Executive of Hong Kong\">Chief Executive of Hong Kong</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ee-hwa\" title=\"<PERSON><PERSON>ee-hwa\"><PERSON><PERSON>-hwa</a>, Hong Kong businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong\" title=\"Chief Executive of Hong Kong\">Chief Executive of Hong Kong</a>", "links": [{"title": "<PERSON><PERSON>wa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-hwa"}, {"title": "Chief Executive of Hong Kong", "link": "https://wikipedia.org/wiki/Chief_Executive_of_Hong_Kong"}]}, {"year": "1938", "text": "<PERSON>, American pastor and theologian (d. 2000)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Russian soprano and actress (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano and actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soprano and actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, English singer-songwriter, drummer, and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Starr\"><PERSON><PERSON></a>, English singer-songwriter, drummer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Starr\"><PERSON><PERSON></a>, English singer-songwriter, drummer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Italian rugby player and coach (d. 2021)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Welsh lawyer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Cameroonian politician (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English comedian, actor, and singer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English bass player (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian actress (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American journalist and critic (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English golfer and sportscaster", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, English educator and politician (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Holyhead\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON> of Holyhead\"><PERSON><PERSON></a>, English educator and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Holyhead\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Baroness <PERSON> of Holyhead\"><PERSON><PERSON></a>, English educator and politician (d. 2023)", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON> of Holyhead", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Holyhead"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Tongan politician; Prime Minister of Tonga", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tongan politician; Prime Minister of Tonga", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tongan politician; Prime Minister of Tonga", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1944", "text": "<PERSON>, American boxer and trainer (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ard"}]}, {"year": "1944", "text": "<PERSON>, English-Scottish embryologist and academic (d. 2023)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish embryologist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish embryologist and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English lawyer and politician (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American computer scientist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>,  inspiration for the song \"The Girl from Ipanema\"", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Hel%C3%B4_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, inspiration for the song \"<a href=\"https://wikipedia.org/wiki/The_Girl_from_Ipanema\" title=\"The Girl from Ipanema\">The Girl from Ipanema</a>\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hel%C3%B4_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, inspiration for the song \"<a href=\"https://wikipedia.org/wiki/The_Girl_from_Ipanema\" title=\"The Girl from Ipanema\">The Girl from Ipanema</a>\"", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hel%C3%B4_<PERSON><PERSON><PERSON>"}, {"title": "The Girl from Ipanema", "link": "https://wikipedia.org/wiki/The_Girl_from_Ipanema"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, King of Nepal", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, King of Nepal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, King of Nepal", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author and critic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actress, writer, and producer (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian surfer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American director and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Turkish-Israeli singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-Israeli singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-Israeli singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian figure skater and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American colonel and astronaut", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Czech-Swedish actor and comedian", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Swedish actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Swedish actor and comedian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress, comedian and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English talk show host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Danish race car driver", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Danish race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German luger", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German luger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German luger", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American-Canadian actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Cree_Summer\" title=\"Cree Summer\"><PERSON><PERSON> <PERSON></a>, American-Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cree_Summer\" title=\"Cree Summer\"><PERSON><PERSON> <PERSON></a>, American-Canadian actress", "links": [{"title": "Cree Summer", "link": "https://wikipedia.org/wiki/Cree_Summer"}]}, {"year": "1970", "text": "<PERSON>, Northern Irish boxer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Indian-English cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German cyclist and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>go"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Austrian race car driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actress and writer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Dominican baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jim%C3%A9nez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jim%C3%A9nez_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>%C3%A9nez_(baseball)"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American luger", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American luger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American luger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, South African rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1975", "text": "<PERSON>, American shot putter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Argentinian-French actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/B%C3%A9r%C3%A9<PERSON><PERSON>_Bejo\" title=\"Bérénice Bejo\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9r%C3%A9<PERSON><PERSON>_Bejo\" title=\"Bérénice Bejo\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Argentinian-French actress", "links": [{"title": "Bérénice <PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9r%C3%A9nice_Bejo"}]}, {"year": "1976", "text": "<PERSON>, Irish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish discus thrower and shot putter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Erc%C3%BCment_Olgundeniz\" title=\"Ercüment Olgundeniz\"><PERSON><PERSON><PERSON><PERSON> Olgundeniz</a>, Turkish discus thrower and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erc%C3%BCment_Olgundeniz\" title=\"Ercüment Olgundeniz\"><PERSON><PERSON><PERSON><PERSON> Olgundeniz</a>, Turkish discus thrower and shot putter", "links": [{"title": "Ercüment Olgundeniz", "link": "https://wikipedia.org/wiki/Erc%C3%BCment_Olgundeniz"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Russian conductor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian conductor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Davor_Kraljevi%C4%87"}]}, {"year": "1979", "text": "<PERSON>, Saudi Arabian terrorist (d. 2015)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian terrorist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian terrorist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Go<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Gousis"}]}, {"year": "1979", "text": "<PERSON>, Zimbabwean cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bilge\" title=\"<PERSON><PERSON> Kulbilge\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kulbilge\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ser<PERSON>_<PERSON>ge"}]}, {"year": "1980", "text": "<PERSON>, American figure skater", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/MS_Dhoni\" title=\"<PERSON> Dhoni\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MS_Dhoni\" title=\"MS Dhoni\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "MS Dhoni", "link": "https://wikipedia.org/wiki/MS_<PERSON><PERSON>i"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Synyster Gates\"><PERSON><PERSON><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Synyster Gates\"><PERSON><PERSON><PERSON></a>, American guitarist", "links": [{"title": "Synyster Gates", "link": "https://wikipedia.org/wiki/Synyster_Gates"}]}, {"year": "1982", "text": "<PERSON>, Czech footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%A1t%C5%AFvka\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>%C5%A1t%C5%AFvka\" title=\"<PERSON>\"><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_La%C5%A1t%C5%AFvka"}]}, {"year": "1982", "text": "<PERSON>, Ghanaian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American drag performer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Asia_O%27Hara\" title=\"Asia O'Hara\">Asia O'Hara</a>, American drag performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asia_O%27Hara\" title=\"Asia O'Hara\">Asia O'Hara</a>, American drag performer", "links": [{"title": "Asia O'Hara", "link": "https://wikipedia.org/wiki/Asia_O%27Hara"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1984", "text": "<PERSON>, Greek hurdler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Minas_Alozidis\" title=\"Minas Alozidis\"><PERSON> Alozidis</a>, Greek hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minas_Alozidis\" title=\"Minas Alozidis\"><PERSON> Alozidis</a>, Greek hurdler", "links": [{"title": "Minas Alozidis", "link": "https://wikipedia.org/wiki/Minas_Alozidis"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Bangladeshi cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1986", "text": "<PERSON>, American journalist and producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, German rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Estonian skier", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>-August_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON>-<PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-August_Tii<PERSON>\" title=\"<PERSON>-August T<PERSON>\"><PERSON>-<PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Ghanaian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Austrian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_St%C3%B6ger\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_St%C3%B6ger\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pascal_St%C3%B6ger"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Swedish DJ, record producer and musician ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish DJ, record producer and musician ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish DJ, record producer and musician ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>esso"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Estonian hammer thrower", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian hammer thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Uzbekistani tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbekistani tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uzbekistani tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Northern Irish race car driver (d. 2014)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish race car driver (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian musician", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese idol and model", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese idol and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese idol and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": " <PERSON>, English musician and online content creator", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and online content creator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English musician and online content creator", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "984", "text": "<PERSON><PERSON> the Elder, Italian politician and aristocrat", "html": "984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Italian politician and aristocrat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Elder\" title=\"<PERSON><PERSON> the Elder\"><PERSON><PERSON> the Elder</a>, Italian politician and aristocrat", "links": [{"title": "<PERSON><PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Elder"}]}, {"year": "1021", "text": "<PERSON><PERSON>, Japanese bureaucrat (b. 944)", "html": "1021 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Akimitsu\" title=\"Fujiwara no Akimitsu\"><PERSON><PERSON> no Akimitsu</a>, Japanese bureaucrat (b. 944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Akimitsu\" title=\"Fujiwara no Akimitsu\"><PERSON><PERSON> no Akimitsu</a>, Japanese bureaucrat (b. 944)", "links": [{"title": "Fujiwara no Akimitsu", "link": "https://wikipedia.org/wiki/Fujiwara_no_Akimitsu"}]}, {"year": "1162", "text": "<PERSON><PERSON><PERSON>, king of Norway (b. 1147)", "html": "1162 - <a href=\"https://wikipedia.org/wiki/Haakon_II_of_Norway\" class=\"mw-redirect\" title=\"Haakon II of Norway\"><PERSON><PERSON><PERSON> <PERSON></a>, king of Norway (b. 1147)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haakon_II_of_Norway\" class=\"mw-redirect\" title=\"Haakon II of Norway\"><PERSON><PERSON><PERSON></a>, king of Norway (b. 1147)", "links": [{"title": "Haakon II of Norway", "link": "https://wikipedia.org/wiki/Haakon_II_of_Norway"}]}, {"year": "1285", "text": "<PERSON><PERSON>, German impostor claiming to be <PERSON>", "html": "1285 - <a href=\"https://wikipedia.org/wiki/Tile_<PERSON>lup\" title=\"Tile Kolup\"><PERSON><PERSON></a>, German impostor claiming to be <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tile_<PERSON>lup\" title=\"Tile Kolup\"><PERSON><PERSON></a>, German impostor claiming to be <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tile_<PERSON>lup"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1304", "text": "<PERSON>, pope of the Catholic Church (b. 1240)", "html": "1304 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1240)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1307", "text": "<PERSON>, king of England (b. 1239)", "html": "1307 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, king of England (b. 1239)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, king of England (b. 1239)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}]}, {"year": "1345", "text": "<PERSON><PERSON><PERSON>, Bulgarian brigand and ruler", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian brigand and ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian brigand and ruler", "links": [{"title": "Momchil", "link": "https://wikipedia.org/wiki/Mom<PERSON>l"}]}, {"year": "1531", "text": "<PERSON><PERSON><PERSON>, German sculptor (b. 1460)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Til<PERSON>_<PERSON>\" title=\"Til<PERSON>\">T<PERSON><PERSON></a>, German sculptor (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Til<PERSON>_<PERSON>\" title=\"Til<PERSON>\">T<PERSON><PERSON></a>, German sculptor (b. 1460)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Til<PERSON>_<PERSON>schneider"}]}, {"year": "1568", "text": "<PERSON>, British ornithologist and botanist (b. 1508)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, British ornithologist and botanist (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(naturalist)\" title=\"<PERSON> (naturalist)\"><PERSON></a>, British ornithologist and botanist (b. 1508)", "links": [{"title": "<PERSON> (naturalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(naturalist)"}]}, {"year": "1572", "text": "<PERSON><PERSON><PERSON>, Polish king (b. 1520)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Augustus\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish king (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Augustus\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish king (b. 1520)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON>ola, Italian architect, designed the Church of the Gesù and Villa Farnese (b. 1507)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Vignola\" title=\"<PERSON> Vignola\"><PERSON> Vignola</a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9\" title=\"Church of the Gesù\">Church of the Gesù</a> and <a href=\"https://wikipedia.org/wiki/Villa_Farnese\" title=\"Villa Farnese\">Villa Farnese</a> (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Vignola\" title=\"<PERSON> Vignola\"><PERSON> Vignola</a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9\" title=\"Church of the Gesù\">Church of the Gesù</a> and <a href=\"https://wikipedia.org/wiki/Villa_Farnese\" title=\"Villa Farnese\">Villa Farnese</a> (b. 1507)", "links": [{"title": "<PERSON> Vignola", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Church of the Gesù", "link": "https://wikipedia.org/wiki/Church_of_the_Ges%C3%B9"}, {"title": "Villa Farnese", "link": "https://wikipedia.org/wiki/Villa_Farnese"}]}, {"year": "1593", "text": "<PERSON>, Malian scholar and academic (b. 1523)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malian scholar and academic (b. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malian scholar and academic (b. 1523)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mohammed_<PERSON>yogo"}]}, {"year": "1600", "text": "<PERSON>, English politician (b. 1532)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1532)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, Countess of Devonshire, English noblewoman (b. 1563)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Devonshire\" title=\"<PERSON>, Countess of Devonshire\"><PERSON>, Countess of Devonshire</a>, English noblewoman (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Devonshire\" title=\"<PERSON>, Countess of Devonshire\"><PERSON>, Countess of Devonshire</a>, English noblewoman (b. 1563)", "links": [{"title": "<PERSON>, Countess of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Devonshire"}]}, {"year": "1647", "text": "<PERSON>, English minister, founded the Colony of Connecticut (b. 1586)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, founded the <a href=\"https://wikipedia.org/wiki/Colony_of_Connecticut\" class=\"mw-redirect\" title=\"Colony of Connecticut\">Colony of Connecticut</a> (b. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, founded the <a href=\"https://wikipedia.org/wiki/Colony_of_Connecticut\" class=\"mw-redirect\" title=\"Colony of Connecticut\">Colony of Connecticut</a> (b. 1586)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colony of Connecticut", "link": "https://wikipedia.org/wiki/Colony_of_Connecticut"}]}, {"year": "1701", "text": "<PERSON>, American judge and politician, Governor of the Province of Massachusetts Bay (b. 1631)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Massachusetts)\" class=\"mw-redirect\" title=\"<PERSON> (Massachusetts)\"><PERSON></a>, American judge and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Massachusetts)\" class=\"mw-redirect\" title=\"<PERSON> (Massachusetts)\"><PERSON></a>, American judge and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1631)", "links": [{"title": "<PERSON> (Massachusetts)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Massachusetts)"}, {"title": "Governor of the Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay"}]}, {"year": "1713", "text": "<PERSON>, English bishop (b. 1632)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1632)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1718", "text": "<PERSON>, Russian tsarevich (b. 1690)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON></a>, Russian tsarevich (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON></a>, Russian tsarevich (b. 1690)", "links": [{"title": "<PERSON>, <PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>evich_of_Russia"}]}, {"year": "1730", "text": "<PERSON>, French pirate (b. 1690)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pirate (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pirate (b. 1690)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON>, Raja of Attingal (b. 1706)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arma\" title=\"<PERSON><PERSON> Varma\"><PERSON><PERSON></a>, Raja of Attingal (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>arma\" title=\"<PERSON><PERSON> Varma\"><PERSON><PERSON></a>, Raja of Attingal (b. 1706)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martha<PERSON>_Varma"}]}, {"year": "1764", "text": "<PERSON>, 1st Earl of Bath, English politician, Secretary at War (b. 1683)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl of Bath</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl <PERSON> Bath\"><PERSON>, 1st Earl of Bath</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1683)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Bath", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bath"}, {"title": "Secretary at War", "link": "https://wikipedia.org/wiki/Secretary_at_War"}]}, {"year": "1776", "text": "<PERSON>, English scholar and academic (b. 1693)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and academic (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, Dutch philosopher and author (b. 1721)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and author (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher and author (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1816", "text": "<PERSON>, Irish playwright and poet (b. 1751)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright and poet (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish playwright and poet (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Irish genre painter (b. 1786)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish genre painter (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish genre painter (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON> (b. 1833)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON> (b. 1842)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON> (b. 1844)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a> (b. 1844)", "links": [{"title": "<PERSON> (conspirator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)"}]}, {"year": "1865", "text": "<PERSON> (b. 1823)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, German businessman, founded N<PERSON>lé (b. 1814)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Nestl%C3%A9\" title=\"Nestlé\">Nestlé</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Nestl%C3%A9\" title=\"Nestlé\">Nestlé</a> (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Nestl%C3%A9"}, {"title": "Nestlé", "link": "https://wikipedia.org/wiki/Nestl%C3%A9"}]}, {"year": "1901", "text": "<PERSON>, Swiss author (b. 1827)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American general and diplomat, United States Ambassador to Spain (b. 1841)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Spain\" class=\"mw-redirect\" title=\"United States Ambassador to Spain\">United States Ambassador to Spain</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American general and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Spain\" class=\"mw-redirect\" title=\"United States Ambassador to Spain\">United States Ambassador to Spain</a> (b. 1841)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Ambassador to Spain", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Spain"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Irish revolutionary and politician, active in the Easter Rising, Irish War of Independence; first <PERSON><PERSON> and first President of Dáil Éireann (b. 1874)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Cathal_Brugha\" title=\"Cathal Brugha\"><PERSON><PERSON></a>, Irish revolutionary and politician, active in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>, <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>; first <a href=\"https://wikipedia.org/wiki/Ceann_Co<PERSON>hairle\" title=\"<PERSON><PERSON> Comhairle\"><PERSON><PERSON></a> and first <a href=\"https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann\" title=\"President of Dáil Éireann\">President of Dáil Éireann</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cathal_Brugha\" title=\"Cathal Brugha\"><PERSON><PERSON></a>, Irish revolutionary and politician, active in the <a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>, <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>; first <a href=\"https://wikipedia.org/wiki/Ceann_Comhairle\" title=\"<PERSON><PERSON> Comhairle\"><PERSON><PERSON></a> and first <a href=\"https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann\" title=\"President of Dáil Éireann\">President of Dáil Éireann</a> (b. 1874)", "links": [{"title": "<PERSON><PERSON> Brugha", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Brugha"}, {"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}, {"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Dáil Éireann", "link": "https://wikipedia.org/wiki/President_of_D%C3%A1il_%C3%89ireann"}]}, {"year": "1925", "text": "<PERSON>, American photographer and educator (b. 1871)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Swedish mathematician and academic (b. 1846)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>ffler\"><PERSON><PERSON><PERSON></a>, Swedish mathematician and academic (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%B<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>Leffler\"><PERSON><PERSON><PERSON></a>, Swedish mathematician and academic (b. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%B6<PERSON>_<PERSON>tta<PERSON>-<PERSON>r"}]}, {"year": "1930", "text": "<PERSON>, British writer (b. 1859)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Russian author (b. 1880)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American theologian and educator (b. 1844)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and educator (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and educator (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American baseball player and manager (b. 1847)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American trumpet player and composer (b. 1923)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Navarro\"><PERSON><PERSON></a>, American trumpet player and composer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Turkish journalist and publisher (b. 1896)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and publisher (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and publisher (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, German author and poet (b. 1886)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Irish priest and photographer (b. 1880)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and photographer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and photographer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American discus thrower and shot putter (b. 1904)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American discus thrower and shot putter (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Ukrainian-Israeli lieutenant and politician, 2nd Prime Minister of Israel (b. 1894)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tt"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1968", "text": "<PERSON>, French race car driver (b. 1928)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English artist (b. 1877)", "html": "1970 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist (b. 1877)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian poet and playwright (b. 1925)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and playwright (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and playwright (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON> of Constantinople (b. 1886)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> of Constantinople</a> (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Athenagoras_I_of_Constantinople"}]}, {"year": "1973", "text": "<PERSON>, German philosopher and sociologist (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and sociologist (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and sociologist (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actress (b. 1922)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Veronica_Lake\" title=\"Veronica Lake\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veronica_Lake\" title=\"Veronica Lake\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Veronica_Lake"}]}, {"year": "1978", "text": "<PERSON>, Guinea-Bissau lawyer and politician, 1st Prime Minister of Guinea-Bissau (b. 1933)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guinea-Bissau lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"Prime Minister of Guinea-Bissau\">Prime Minister of Guinea-Bissau</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guinea-Bissau lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Guinea-Bissau\" class=\"mw-redirect\" title=\"Prime Minister of Guinea-Bissau\">Prime Minister of Guinea-Bissau</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Guinea-Bissau", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Guinea-Bissau"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American director, producer, and screenwriter (b. 1905)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American director, producer, and screenwriter (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1982", "text": "<PERSON>, Indian guru and religious writer (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian guru and religious writer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Bon\"><PERSON></a>, Indian guru and religious writer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American poet and author (b. 1908)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>-<PERSON><PERSON>, Dutch-French pianist (b. 1902)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French pianist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French pianist (b. 1902)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American television panelist and game show host (b. 1920)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television panelist and game show host (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television panelist and game show host (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Brazilian singer and songwriter (b. 1958)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Cazuza\" title=\"Cazuza\"><PERSON><PERSON><PERSON></a>, Brazilian singer and songwriter (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cazuza\" title=\"Cazuza\"><PERSON><PERSON><PERSON></a>, Brazilian singer and songwriter (b. 1958)", "links": [{"title": "Cazuza", "link": "https://wikipedia.org/wiki/Cazuza"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish author, poet, and educator (b. 1911)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/R%C4%B1fat_Ilgaz\" title=\"<PERSON><PERSON><PERSON><PERSON> Il<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish author, poet, and educator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C4%B1fat_Ilgaz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish author, poet, and educator (b. 1911)", "links": [{"title": "Rıfat Il<PERSON>z", "link": "https://wikipedia.org/wiki/R%C4%B1fat_Ilgaz"}]}, {"year": "1993", "text": "<PERSON>, American singer (b. 1965)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mia_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Italian engineer (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German general (b. 1907)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Nigerian businessman and politician (b. 1937)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Moshood_Abiola\" title=\"Mo<PERSON>ood Abiola\"><PERSON><PERSON><PERSON></a>, Nigerian businessman and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mo<PERSON>ood_Abiola\" title=\"Mo<PERSON>ood Abiola\"><PERSON><PERSON><PERSON></a>, Nigerian businessman and politician (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Moshood_Abiola"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Indian Army personnel (b. 1974)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Param_Vir_Chakra\" title=\"Param Vir Chakra\"><PERSON><PERSON> V<PERSON></a>, Indian Army personnel (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Param_Vir_Chakra\" title=\"Param Vir Chakra\"><PERSON><PERSON> V<PERSON></a>, Indian Army personnel (b. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Param Vir Chakra", "link": "https://wikipedia.org/wiki/Param_Vir_Chakra"}]}, {"year": "1999", "text": "<PERSON>, American author (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American race car driver (b. 1969)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American race car driver (b. 1969)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1936)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Bulgarian trumpet player and conductor (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>raziani\"><PERSON><PERSON><PERSON></a>, Bulgarian trumpet player and conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>raz<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian trumpet player and conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>raz<PERSON>i"}]}, {"year": "2006", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist (b. 1946)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Spanish sculptor (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81valos\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81valos\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81valos"}]}, {"year": "2006", "text": "<PERSON>, New Zealand-American psychologist and author (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Money\"><PERSON></a>, New Zealand-American psychologist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Money\"><PERSON></a>, New Zealand-American psychologist and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, British scientist (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, British scientist (b. 1923)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American sculptor, painter, and photographer (b. 1933)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, painter, and photographer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, painter, and photographer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American model (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American historian and author (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American baseball player, coach, and manager (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American drummer (b. 1955)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Brazilian poet and politician (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian poet and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian poet and politician (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American sinologist and linguist (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)\" title=\"<PERSON> (sinologist)\"><PERSON></a>, American sinologist and linguist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)\" title=\"<PERSON> (sinologist)\"><PERSON></a>, American sinologist and linguist (b. 1936)", "links": [{"title": "<PERSON> (sinologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sinologist)"}]}, {"year": "2012", "text": "<PERSON>, Swiss politician (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Polish mountaineer (b. 1962)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mountaineer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mountaineer (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>-<PERSON>, South African-American pastor, theologian, and author (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American pastor, theologian, and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, South African-American pastor, theologian, and author (b. 1938)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician, 32nd Mayor of Norwalk, Connecticut (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Norwalk,_Connecticut\" class=\"mw-redirect\" title=\"Mayor of Norwalk, Connecticut\">Mayor of Norwalk, Connecticut</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Mayor_of_Norwalk,_Connecticut\" class=\"mw-redirect\" title=\"Mayor of Norwalk, Connecticut\">Mayor of Norwalk, Connecticut</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Norwalk, Connecticut", "link": "https://wikipedia.org/wiki/Mayor_of_Norwalk,_Connecticut"}]}, {"year": "2013", "text": "<PERSON>, American football player and sportscaster (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian-Spanish footballer and coach (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9fano\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish footballer and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9fano\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish footballer and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_St%C3%A9fano"}]}, {"year": "2014", "text": "<PERSON>, Georgian general and politician, 2nd President of Georgia (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}]}, {"year": "2014", "text": "<PERSON>, Australian lawyer and politician, 27th Governor of Tasmania (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Tasmania\" title=\"Governor of Tasmania\">Governor of Tasmania</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Tasmania\" title=\"Governor of Tasmania\">Governor of Tasmania</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Tasmania", "link": "https://wikipedia.org/wiki/Governor_of_Tasmania"}]}, {"year": "2015", "text": "<PERSON>, Portuguese actress and politician (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese actress and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese actress and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor and director. Father of <PERSON>. (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American actor and director. Father of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American actor and director. Father of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> (b. 1936)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2021", "text": "<PERSON><PERSON>, Indian film actor (b. 1922)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film actor (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Haitian entrepreneur and politician, President of Haiti (b. 1968)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mo%C3%AFse\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian entrepreneur and politician, President of Haiti (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mo%C3%AFse\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian entrepreneur and politician, President of Haiti (b. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jovenel_Mo%C3%AFse"}]}, {"year": "2024", "text": "<PERSON>, American labor organizer and author (b. 1964)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer and author (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer and author (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}