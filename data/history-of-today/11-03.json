{"date": "November 3", "url": "https://wikipedia.org/wiki/November_3", "data": {"Events": [{"year": "361", "text": "Emperor <PERSON><PERSON><PERSON> dies of a fever at Mopsuestia in Cilicia; on his deathbed he is baptised and declares his cousin <PERSON> rightful successor.", "html": "361 - Emperor <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a> dies of a <a href=\"https://wikipedia.org/wiki/Fever\" title=\"Fever\">fever</a> at <a href=\"https://wikipedia.org/wiki/Mopsuestia\" title=\"Mopsuestia\">Mopsuestia</a> in <a href=\"https://wikipedia.org/wiki/Cilicia\" title=\"Cilicia\">Cilicia</a>; on his deathbed he is <a href=\"https://wikipedia.org/wiki/Baptism\" title=\"Baptism\">baptised</a> and declares his cousin <a href=\"https://wikipedia.org/wiki/Julian_the_Apostate\" class=\"mw-redirect\" title=\"Julian the Apostate\">Julian</a> rightful successor.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\"><PERSON><PERSON><PERSON> II</a> dies of a <a href=\"https://wikipedia.org/wiki/Fever\" title=\"Fever\">fever</a> at <a href=\"https://wikipedia.org/wiki/Mopsuestia\" title=\"Mopsuestia\">Mopsuestia</a> in <a href=\"https://wikipedia.org/wiki/Cilicia\" title=\"Cilicia\">Cilicia</a>; on his deathbed he is <a href=\"https://wikipedia.org/wiki/Baptism\" title=\"Baptism\">baptised</a> and declares his cousin <a href=\"https://wikipedia.org/wiki/Julian_the_Apostate\" class=\"mw-redirect\" title=\"Julian the Apostate\">Julian</a> rightful successor.", "links": [{"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}, {"title": "Fever", "link": "https://wikipedia.org/wiki/Fever"}, {"title": "Mopsuestia", "link": "https://wikipedia.org/wiki/Mopsuestia"}, {"title": "Cilicia", "link": "https://wikipedia.org/wiki/Cilicia"}, {"title": "Baptism", "link": "https://wikipedia.org/wiki/Baptism"}, {"title": "<PERSON> the Apostate", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Apostate"}]}, {"year": "1333", "text": "The River Arno floods causing massive damage in Florence as recorded by the Florentine chronicler <PERSON>.", "html": "1333 - The <a href=\"https://wikipedia.org/wiki/Arno\" title=\"Arno\">River Arno</a> floods causing massive damage in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> as recorded by the Florentine chronicler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arno\" title=\"Arno\">River Arno</a> floods causing massive damage in <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> as recorded by the Florentine chronicler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Arno", "link": "https://wikipedia.org/wiki/Arno"}, {"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1468", "text": "Liège is sacked by <PERSON> of Burgundy's troops.", "html": "1468 - <a href=\"https://wikipedia.org/wiki/Li%C3%A8ge\" title=\"Liège\">Liège</a> is sacked by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON> of Burgundy</a>'s troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li%C3%A8ge\" title=\"Liège\">Liège</a> is sacked by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON> of Burgundy</a>'s troops.", "links": [{"title": "Liège", "link": "https://wikipedia.org/wiki/Li%C3%A8ge"}, {"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1492", "text": "Peace of Etaples between <PERSON> of England and <PERSON> of France.", "html": "1492 - <a href=\"https://wikipedia.org/wiki/Peace_of_Etaples\" class=\"mw-redirect\" title=\"Peace of Etaples\">Peace of Etaples</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> VII of England</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_France\" title=\"<PERSON> VIII of France\"><PERSON> VIII of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peace_of_Etaples\" class=\"mw-redirect\" title=\"Peace of Etaples\">Peace of Etaples</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/Charles_VIII_of_France\" title=\"Charles VIII of France\"><PERSON> of France</a>.", "links": [{"title": "Peace of Etaples", "link": "https://wikipedia.org/wiki/Peace_of_Etaples"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}]}, {"year": "1493", "text": "<PERSON> first sights the island of Dominica in the Caribbean Sea.", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> first sights the island of <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> first sights the island of <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dominica", "link": "https://wikipedia.org/wiki/Dominica"}, {"title": "Caribbean Sea", "link": "https://wikipedia.org/wiki/Caribbean_Sea"}]}, {"year": "1534", "text": "English Parliament passes the first Act of Supremacy, making King <PERSON> VIII head of the Anglican Church, supplanting the pope and the  Roman Catholic Church.", "html": "1534 - English Parliament passes <a href=\"https://wikipedia.org/wiki/Acts_of_Supremacy\" title=\"Acts of Supremacy\">the first Act of Supremacy</a>, making King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON></a> head of the Anglican Church, supplanting the pope and the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>.", "no_year_html": "English Parliament passes <a href=\"https://wikipedia.org/wiki/Acts_of_Supremacy\" title=\"Acts of Supremacy\">the first Act of Supremacy</a>, making King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON></a> head of the Anglican Church, supplanting the pope and the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>.", "links": [{"title": "Acts of Supremacy", "link": "https://wikipedia.org/wiki/Acts_of_Supremacy"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "Roman Catholic Church", "link": "https://wikipedia.org/wiki/Roman_Catholic_Church"}]}, {"year": "1783", "text": "The American Continental Army is disbanded.", "html": "1783 - The American <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> is disbanded.", "no_year_html": "The American <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> is disbanded.", "links": [{"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1791", "text": "The University of Vermont, the oldest university in Vermont, and fifth-oldest in New England, is chartered.", "html": "1791 - The <a href=\"https://wikipedia.org/wiki/University_of_Vermont\" title=\"University of Vermont\">University of Vermont</a>, the oldest university in Vermont, and fifth-oldest in New England, is chartered.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/University_of_Vermont\" title=\"University of Vermont\">University of Vermont</a>, the oldest university in Vermont, and fifth-oldest in New England, is chartered.", "links": [{"title": "University of Vermont", "link": "https://wikipedia.org/wiki/University_of_Vermont"}]}, {"year": "1793", "text": "French playwright, journalist and feminist <PERSON><PERSON><PERSON><PERSON> is guillotined.", "html": "1793 - French playwright, journalist and <a href=\"https://wikipedia.org/wiki/Feminist\" class=\"mw-redirect\" title=\"Feminist\">feminist</a> <a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\">Olympe de Gouges</a> is <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotined</a>.", "no_year_html": "French playwright, journalist and <a href=\"https://wikipedia.org/wiki/Feminist\" class=\"mw-redirect\" title=\"Feminist\">feminist</a> <a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\">Olympe de Gouges</a> is <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotined</a>.", "links": [{"title": "Feminist", "link": "https://wikipedia.org/wiki/Feminist"}, {"title": "Olympe de Gouges", "link": "https://wikipedia.org/wiki/Olympe_de_Gouges"}, {"title": "Guillotine", "link": "https://wikipedia.org/wiki/Guillotine"}]}, {"year": "1812", "text": "<PERSON>'s armies are defeated at the Battle of Vyazma.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s armies are defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vyazma\" title=\"Battle of Vyazma\">Battle of Vyazma</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s armies are defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Vyazma\" title=\"Battle of Vyazma\">Battle of Vyazma</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Battle of Vyazma", "link": "https://wikipedia.org/wiki/Battle_of_Vyazma"}]}, {"year": "1817", "text": "The Bank of Montreal, Canada's oldest chartered bank, opens in Montreal.", "html": "1817 - The <a href=\"https://wikipedia.org/wiki/Bank_of_Montreal\" title=\"Bank of Montreal\">Bank of Montreal</a>, Canada's oldest <a href=\"https://wikipedia.org/wiki/List_of_banks_in_the_Americas#Canada\" title=\"List of banks in the Americas\">chartered bank</a>, opens in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bank_of_Montreal\" title=\"Bank of Montreal\">Bank of Montreal</a>, Canada's oldest <a href=\"https://wikipedia.org/wiki/List_of_banks_in_the_Americas#Canada\" title=\"List of banks in the Americas\">chartered bank</a>, opens in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "links": [{"title": "Bank of Montreal", "link": "https://wikipedia.org/wiki/Bank_of_Montreal"}, {"title": "List of banks in the Americas", "link": "https://wikipedia.org/wiki/List_of_banks_in_the_Americas#Canada"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1838", "text": "The Times of India, the world's largest circulated English language daily broadsheet newspaper is founded as The Bombay Times and Journal of Commerce.", "html": "1838 - <i><a href=\"https://wikipedia.org/wiki/The_Times_of_India\" title=\"The Times of India\">The Times of India</a></i>, the world's largest circulated <a href=\"https://wikipedia.org/wiki/English_language\" title=\"English language\">English language</a> daily <a href=\"https://wikipedia.org/wiki/Broadsheet\" title=\"Broadsheet\">broadsheet</a> <a href=\"https://wikipedia.org/wiki/Newspaper\" title=\"Newspaper\">newspaper</a> is founded as <i>The Bombay Times and Journal of Commerce</i>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Times_of_India\" title=\"The Times of India\">The Times of India</a></i>, the world's largest circulated <a href=\"https://wikipedia.org/wiki/English_language\" title=\"English language\">English language</a> daily <a href=\"https://wikipedia.org/wiki/Broadsheet\" title=\"Broadsheet\">broadsheet</a> <a href=\"https://wikipedia.org/wiki/Newspaper\" title=\"Newspaper\">newspaper</a> is founded as <i>The Bombay Times and Journal of Commerce</i>.", "links": [{"title": "The Times of India", "link": "https://wikipedia.org/wiki/The_Times_of_India"}, {"title": "English language", "link": "https://wikipedia.org/wiki/English_language"}, {"title": "Broadsheet", "link": "https://wikipedia.org/wiki/Broadsheet"}, {"title": "Newspaper", "link": "https://wikipedia.org/wiki/Newspaper"}]}, {"year": "1848", "text": "A greatly revised Dutch constitution, which transfers much authority from the king to his parliament and ministers, is proclaimed.", "html": "1848 - A greatly revised <a href=\"https://wikipedia.org/wiki/Dutch_constitution\" class=\"mw-redirect\" title=\"Dutch constitution\">Dutch constitution</a>, which transfers much authority from the king to his parliament and ministers, is proclaimed.", "no_year_html": "A greatly revised <a href=\"https://wikipedia.org/wiki/Dutch_constitution\" class=\"mw-redirect\" title=\"Dutch constitution\">Dutch constitution</a>, which transfers much authority from the king to his parliament and ministers, is proclaimed.", "links": [{"title": "Dutch constitution", "link": "https://wikipedia.org/wiki/Dutch_constitution"}]}, {"year": "1867", "text": "<PERSON> and his followers are defeated in the Battle of Mentana and fail to end the <PERSON>'s Temporal power in Rome (it would be achieved three years later).", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mentana\" title=\"Battle of Mentana\">Battle of Mentana</a> and fail to end the <PERSON>'s <a href=\"https://wikipedia.org/wiki/Temporal_power_(papal)\" class=\"mw-redirect\" title=\"Temporal power (papal)\">Temporal power</a> in Rome (it would be achieved three years later).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his followers are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mentana\" title=\"Battle of Mentana\">Battle of Mentana</a> and fail to end the <PERSON>'s <a href=\"https://wikipedia.org/wiki/Temporal_power_(papal)\" class=\"mw-redirect\" title=\"Temporal power (papal)\">Temporal power</a> in Rome (it would be achieved three years later).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Mentana", "link": "https://wikipedia.org/wiki/Battle_of_Mentana"}, {"title": "Temporal power (papal)", "link": "https://wikipedia.org/wiki/Temporal_power_(papal)"}]}, {"year": "1868", "text": "<PERSON> (R-LA) was the first African American elected to the United States Congress.  Because of an electoral challenge, he was never seated.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (R-LA) was the first African American elected to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>. Because of an electoral challenge, he was never seated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (R-LA) was the first African American elected to the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a>. Because of an electoral challenge, he was never seated.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1881", "text": "The Mapuche uprising of 1881 begins in Chile.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche uprising of 1881</a> begins in Chile.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche uprising of 1881</a> begins in Chile.", "links": [{"title": "Mapuche uprising of 1881", "link": "https://wikipedia.org/wiki/Mapuche_uprising_of_1881"}]}, {"year": "1898", "text": "France withdraws its troops from Fashoda (now in Sudan), ending the Fashoda Incident.", "html": "1898 - France withdraws its troops from <a href=\"https://wikipedia.org/wiki/Fashoda\" class=\"mw-redirect\" title=\"Fashoda\">Fashoda</a> (now in Sudan), ending the <a href=\"https://wikipedia.org/wiki/Fashoda_Incident\" title=\"Fashoda Incident\">Fashoda Incident</a>.", "no_year_html": "France withdraws its troops from <a href=\"https://wikipedia.org/wiki/Fashoda\" class=\"mw-redirect\" title=\"Fashoda\">Fashoda</a> (now in Sudan), ending the <a href=\"https://wikipedia.org/wiki/Fashoda_Incident\" title=\"Fashoda Incident\">Fashoda Incident</a>.", "links": [{"title": "Fashoda", "link": "https://wikipedia.org/wiki/Fashoda"}, {"title": "Fashoda Incident", "link": "https://wikipedia.org/wiki/Fashoda_Incident"}]}, {"year": "1903", "text": "With the encouragement of the United States, Panama separates from Colombia.", "html": "1903 - With the encouragement of the United States, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> <a href=\"https://wikipedia.org/wiki/Separation_of_Panama_from_Colombia\" class=\"mw-redirect\" title=\"Separation of Panama from Colombia\">separates</a> from <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "no_year_html": "With the encouragement of the United States, <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> <a href=\"https://wikipedia.org/wiki/Separation_of_Panama_from_Colombia\" class=\"mw-redirect\" title=\"Separation of Panama from Colombia\">separates</a> from <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>.", "links": [{"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Separation of Panama from Colombia", "link": "https://wikipedia.org/wiki/Separation_of_Panama_from_Colombia"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1908", "text": "<PERSON> is elected the 27th President of the United States.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1908_United_States_presidential_election\" title=\"1908 United States presidential election\">elected</a> the 27th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1908_United_States_presidential_election\" title=\"1908 United States presidential election\">elected</a> the 27th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1908 United States presidential election", "link": "https://wikipedia.org/wiki/1908_United_States_presidential_election"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1911", "text": "Chevrolet officially enters the automobile market in competition with the Ford Model T.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> officially enters the automobile market in competition with the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> officially enters the automobile market in competition with the <a href=\"https://wikipedia.org/wiki/Ford_Model_T\" title=\"Ford Model T\">Ford Model T</a>.", "links": [{"title": "Chevrolet", "link": "https://wikipedia.org/wiki/Chevrolet"}, {"title": "Ford Model T", "link": "https://wikipedia.org/wiki/Ford_Model_T"}]}, {"year": "1918", "text": "The German Revolution of 1918-19 begins when 40,000 sailors take over the port in Kiel.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution of 1918-19</a> begins when 40,000 sailors <a href=\"https://wikipedia.org/wiki/Kiel_mutiny\" title=\"Kiel mutiny\">take over</a> the port in <a href=\"https://wikipedia.org/wiki/Kiel\" title=\"Kiel\">Kiel</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution of 1918-19</a> begins when 40,000 sailors <a href=\"https://wikipedia.org/wiki/Kiel_mutiny\" title=\"Kiel mutiny\">take over</a> the port in <a href=\"https://wikipedia.org/wiki/Kiel\" title=\"Kiel\">Kiel</a>.", "links": [{"title": "German Revolution of 1918-19", "link": "https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319"}, {"title": "Kiel mutiny", "link": "https://wikipedia.org/wiki/Kiel_mutiny"}, {"title": "Kiel", "link": "https://wikipedia.org/wiki/Kiel"}]}, {"year": "1920", "text": "Russian Civil War: The Russian Army retreats to Crimea, after a successful offensive by the Red Army and Revolutionary Insurgent Army of Ukraine.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Wrangel\" title=\"Army of Wrangel\">Russian Army</a> retreats to <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a>, after a <a href=\"https://wikipedia.org/wiki/Northern_Taurida_Operation\" title=\"Northern Taurida Operation\">successful offensive</a> by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Army_of_Wrangel\" title=\"Army of Wrangel\">Russian Army</a> retreats to <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a>, after a <a href=\"https://wikipedia.org/wiki/Northern_Taurida_Operation\" title=\"Northern Taurida Operation\">successful offensive</a> by the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> and <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army of Ukraine</a>.", "links": [{"title": "Russian Civil War", "link": "https://wikipedia.org/wiki/Russian_Civil_War"}, {"title": "Army of Wrangel", "link": "https://wikipedia.org/wiki/Army_of_Wrangel"}, {"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea"}, {"title": "Northern Taurida Operation", "link": "https://wikipedia.org/wiki/Northern_Taurida_Operation"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}]}, {"year": "1929", "text": "The Gwangju Student Independence Movement occurred.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/Gwangju_Student_Independence_Movement\" title=\"Gwangju Student Independence Movement\">Gwangju Student Independence Movement</a> occurred.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gwangju_Student_Independence_Movement\" title=\"Gwangju Student Independence Movement\">Gwangju Student Independence Movement</a> occurred.", "links": [{"title": "Gwangju Student Independence Movement", "link": "https://wikipedia.org/wiki/Gwangju_Student_Independence_Movement"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON> becomes Head of the Provisional Government in Brazil after a bloodless coup on October 24.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes Head of the Provisional Government in <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> after a bloodless coup on <a href=\"https://wikipedia.org/wiki/October_24\" title=\"October 24\">October 24</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Get%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes Head of the Provisional Government in <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> after a bloodless coup on <a href=\"https://wikipedia.org/wiki/October_24\" title=\"October 24\">October 24</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Get%C3%BAlio_Vargas"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "October 24", "link": "https://wikipedia.org/wiki/October_24"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON> becomes the 142nd Prime Minister of Greece.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>dar<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece#Second_Hellenic_Republic_(1924-1935)\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">142nd Prime Minister of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece#Second_Hellenic_Republic_(1924-1935)\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">142nd Prime Minister of Greece</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>s_<PERSON><PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece#Second_Hellenic_Republic_(1924-1935)"}]}, {"year": "1935", "text": "<PERSON> of Greece regains his throne through a popular, though possibly fixed, plebiscite.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> regains his throne through a popular, though possibly fixed, <a href=\"https://wikipedia.org/wiki/Greek_monarchy_referendum,_1935\" class=\"mw-redirect\" title=\"Greek monarchy referendum, 1935\">plebiscite</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> regains his throne through a popular, though possibly fixed, <a href=\"https://wikipedia.org/wiki/Greek_monarchy_referendum,_1935\" class=\"mw-redirect\" title=\"Greek monarchy referendum, 1935\">plebiscite</a>.", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Greece"}, {"title": "Greek monarchy referendum, 1935", "link": "https://wikipedia.org/wiki/Greek_monarchy_referendum,_1935"}]}, {"year": "1936", "text": "<PERSON> is elected the 32nd President of the United States.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1936_United_States_presidential_election\" title=\"1936 United States presidential election\">elected</a> the 32nd President of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1936_United_States_presidential_election\" title=\"1936 United States presidential election\">elected</a> the 32nd President of the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1936 United States presidential election", "link": "https://wikipedia.org/wiki/1936_United_States_presidential_election"}]}, {"year": "1942", "text": "World War II: The Koli Point action begins during the Guadalcanal Campaign and ends on November 12.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Koli_Point_action\" title=\"Koli Point action\">Koli Point action</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> and ends on <a href=\"https://wikipedia.org/wiki/November_12\" title=\"November 12\">November 12</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Koli_Point_action\" title=\"Koli Point action\">Koli Point action</a> begins during the <a href=\"https://wikipedia.org/wiki/Guadalcanal_Campaign\" class=\"mw-redirect\" title=\"Guadalcanal Campaign\">Guadalcanal Campaign</a> and ends on <a href=\"https://wikipedia.org/wiki/November_12\" title=\"November 12\">November 12</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Koli Point action", "link": "https://wikipedia.org/wiki/Koli_Point_action"}, {"title": "Guadalcanal Campaign", "link": "https://wikipedia.org/wiki/Guadalcanal_Campaign"}, {"title": "November 12", "link": "https://wikipedia.org/wiki/November_12"}]}, {"year": "1943", "text": "World War II: Five hundred aircraft of the U.S. 8th Air Force devastate Wilhelmshaven harbor in Germany.", "html": "1943 - World War II: Five hundred aircraft of the U.S. 8th Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Wilhelmshaven_in_World_War_II\" title=\"Bombing of Wilhelmshaven in World War II\">devastate Wilhelmshaven</a> harbor in Germany.", "no_year_html": "World War II: Five hundred aircraft of the U.S. 8th Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Wilhelmshaven_in_World_War_II\" title=\"Bombing of Wilhelmshaven in World War II\">devastate Wilhelmshaven</a> harbor in Germany.", "links": [{"title": "Bombing of Wilhelmshaven in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Wilhelmshaven_in_World_War_II"}]}, {"year": "1944", "text": "World War II: Two supreme commanders of the Slovak National Uprising, Generals <PERSON><PERSON> and <PERSON>, are captured, tortured and later executed by German forces.", "html": "1944 - World War II: Two supreme commanders of the <a href=\"https://wikipedia.org/wiki/Slovak_National_Uprising\" title=\"Slovak National Uprising\">Slovak National Uprising</a>, Generals <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, are captured, tortured and later executed by German forces.", "no_year_html": "World War II: Two supreme commanders of the <a href=\"https://wikipedia.org/wiki/Slovak_National_Uprising\" title=\"Slovak National Uprising\">Slovak National Uprising</a>, Generals <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, are captured, tortured and later executed by German forces.", "links": [{"title": "Slovak National Uprising", "link": "https://wikipedia.org/wiki/Slovak_National_Uprising"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1n_Golian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "The Constitution of Japan is adopted through <PERSON>'s assent.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_Japan\" title=\"Constitution of Japan\">Constitution of Japan</a> is adopted through <PERSON>'s <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">assent</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_Japan\" title=\"Constitution of Japan\">Constitution of Japan</a> is adopted through <PERSON>'s <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">assent</a>.", "links": [{"title": "Constitution of Japan", "link": "https://wikipedia.org/wiki/Constitution_of_Japan"}, {"title": "Royal assent", "link": "https://wikipedia.org/wiki/Royal_assent"}]}, {"year": "1949", "text": "Chinese Civil War: The Battle of Dengbu Island occurs.", "html": "1949 - Chinese Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dengbu_Island\" title=\"Battle of Dengbu Island\">Battle of Dengbu Island</a> occurs.", "no_year_html": "Chinese Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dengbu_Island\" title=\"Battle of Dengbu Island\">Battle of Dengbu Island</a> occurs.", "links": [{"title": "Battle of Dengbu Island", "link": "https://wikipedia.org/wiki/Battle_of_Dengbu_Island"}]}, {"year": "1950", "text": "Air India Flight 245 crashes into Mont Blanc while on approach to Geneva Airport, killing all 48 people on board.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Air_India_Flight_245\" title=\"Air India Flight 245\">Air India Flight 245</a> crashes into <a href=\"https://wikipedia.org/wiki/<PERSON>_Blanc\" title=\"Mont Blanc\"><PERSON></a> while on approach to <a href=\"https://wikipedia.org/wiki/Geneva_Airport\" title=\"Geneva Airport\">Geneva Airport</a>, killing all 48 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_India_Flight_245\" title=\"Air India Flight 245\">Air India Flight 245</a> crashes into <a href=\"https://wikipedia.org/wiki/Mont_Blanc\" title=\"Mont Blanc\"><PERSON></a> while on approach to <a href=\"https://wikipedia.org/wiki/Geneva_Airport\" title=\"Geneva Airport\">Geneva Airport</a>, killing all 48 people on board.", "links": [{"title": "Air India Flight 245", "link": "https://wikipedia.org/wiki/Air_India_Flight_245"}, {"title": "Mont Blanc", "link": "https://wikipedia.org/wiki/Mont_Blanc"}, {"title": "Geneva Airport", "link": "https://wikipedia.org/wiki/Geneva_Airport"}]}, {"year": "1956", "text": "Suez Crisis: The <PERSON> Yun<PERSON> killings by the Israel Defense Forces in Egyptian-controlled Gaza result in the deaths of 275 Palestinians.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Khan_Yunis_massacre\" title=\"Khan Yunis massacre\">Khan Yunis killings</a> by the <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> in Egyptian-controlled Gaza result in the deaths of 275 Palestinians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a>: The <a href=\"https://wikipedia.org/wiki/Khan_Yunis_massacre\" title=\"Khan Yunis massacre\">Khan Yunis killings</a> by the <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel Defense Forces</a> in Egyptian-controlled Gaza result in the deaths of 275 Palestinians.", "links": [{"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Khan Yunis massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_massacre"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}]}, {"year": "1956", "text": "Hungarian Revolution: A new Hungarian government is formed, in which many members of banned non-Communist parties participate.  <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> form a counter-government in Moscow as Soviet troops prepare for the final assault.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: A new Hungarian government is formed, in which many members of banned non-Communist parties participate. <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> form a counter-government in Moscow as Soviet troops prepare for the final assault.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution</a>: A new Hungarian government is formed, in which many members of banned non-Communist parties participate. <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> form a counter-government in Moscow as Soviet troops prepare for the final assault.", "links": [{"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_K%C3%A1d%C3%A1r"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_M%C3%BCnnich"}]}, {"year": "1957", "text": "Sputnik program: The Soviet Union launches Sputnik 2. On board is the first animal to enter orbit, a dog named <PERSON><PERSON>.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Sputnik_program\" class=\"mw-redirect\" title=\"Sputnik program\">Sputnik program</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <i><a href=\"https://wikipedia.org/wiki/Sputnik_2\" title=\"Sputnik 2\">Sputnik 2</a></i>. On board is the first animal to enter <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbit</a>, a <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs\" title=\"Soviet space dogs\">dog</a> named <a href=\"https://wikipedia.org/wiki/Laika\" title=\"Laika\">Laika</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sputnik_program\" class=\"mw-redirect\" title=\"Sputnik program\">Sputnik program</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> launches <i><a href=\"https://wikipedia.org/wiki/Sputnik_2\" title=\"Sputnik 2\">Sputnik 2</a></i>. On board is the first animal to enter <a href=\"https://wikipedia.org/wiki/Orbit\" title=\"Orbit\">orbit</a>, a <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs\" title=\"Soviet space dogs\">dog</a> named <a href=\"https://wikipedia.org/wiki/Laika\" title=\"Laika\">Laika</a>.", "links": [{"title": "Sputnik program", "link": "https://wikipedia.org/wiki/Sputnik_program"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Sputnik 2", "link": "https://wikipedia.org/wiki/Sputnik_2"}, {"title": "Orbit", "link": "https://wikipedia.org/wiki/Orbit"}, {"title": "Soviet space dogs", "link": "https://wikipedia.org/wiki/Soviet_space_dogs"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laika"}]}, {"year": "1960", "text": "The land that would become the Great Swamp National Wildlife Refuge is established by an Act of Congress after a year-long legal battle that pitted local residents against Port Authority of New York and New Jersey officials wishing to turn the Great Swamp into a major regional airport for jet aircraft.", "html": "1960 - The land that would become the <a href=\"https://wikipedia.org/wiki/Great_Swamp_National_Wildlife_Refuge\" title=\"Great Swamp National Wildlife Refuge\">Great Swamp National Wildlife Refuge</a> is established by an <a href=\"https://wikipedia.org/wiki/Act_of_Congress\" title=\"Act of Congress\">Act of Congress</a> after a year-long legal battle that pitted local residents against <a href=\"https://wikipedia.org/wiki/Port_Authority_of_New_York_and_New_Jersey\" title=\"Port Authority of New York and New Jersey\">Port Authority of New York and New Jersey</a> officials wishing to turn the Great Swamp into a major regional airport for jet aircraft.", "no_year_html": "The land that would become the <a href=\"https://wikipedia.org/wiki/Great_Swamp_National_Wildlife_Refuge\" title=\"Great Swamp National Wildlife Refuge\">Great Swamp National Wildlife Refuge</a> is established by an <a href=\"https://wikipedia.org/wiki/Act_of_Congress\" title=\"Act of Congress\">Act of Congress</a> after a year-long legal battle that pitted local residents against <a href=\"https://wikipedia.org/wiki/Port_Authority_of_New_York_and_New_Jersey\" title=\"Port Authority of New York and New Jersey\">Port Authority of New York and New Jersey</a> officials wishing to turn the Great Swamp into a major regional airport for jet aircraft.", "links": [{"title": "Great Swamp National Wildlife Refuge", "link": "https://wikipedia.org/wiki/Great_Swamp_National_Wildlife_Refuge"}, {"title": "Act of Congress", "link": "https://wikipedia.org/wiki/Act_of_Congress"}, {"title": "Port Authority of New York and New Jersey", "link": "https://wikipedia.org/wiki/Port_Authority_of_New_York_and_New_Jersey"}]}, {"year": "1961", "text": "<PERSON> is unanimously appointed as the 3rd Secretary-General of the United Nations, becoming the first non-European individual to occupy the post.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/U_Thant\" title=\"U Thant\"><PERSON></a> is unanimously appointed as the 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, becoming the first non-European individual to occupy the post.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U_Thant\" title=\"U Thant\"><PERSON></a> is unanimously appointed as the 3rd <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, becoming the first non-European individual to occupy the post.", "links": [{"title": "U Thant", "link": "https://wikipedia.org/wiki/U_Thant"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1964", "text": "<PERSON> is elected to a full term as U.S. president, winning 61% of the vote and 44 states, while Washington D.C. residents are able to vote in a presidential election for the first time, casting the majority of their votes for <PERSON>.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1964_United_States_presidential_election\" title=\"1964 United States presidential election\">elected</a> to a full term as U.S. president, winning 61% of the vote and 44 states, while Washington D.C. residents are able to vote in a <a href=\"https://wikipedia.org/wiki/Presidential_election\" title=\"Presidential election\">presidential election</a> for the first time, casting the majority of their votes for <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1964_United_States_presidential_election\" title=\"1964 United States presidential election\">elected</a> to a full term as U.S. president, winning 61% of the vote and 44 states, while Washington D.C. residents are able to vote in a <a href=\"https://wikipedia.org/wiki/Presidential_election\" title=\"Presidential election\">presidential election</a> for the first time, casting the majority of their votes for <PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1964 United States presidential election", "link": "https://wikipedia.org/wiki/1964_United_States_presidential_election"}, {"title": "Presidential election", "link": "https://wikipedia.org/wiki/Presidential_election"}]}, {"year": "1967", "text": "Vietnam War: The Battle of Dak To begins.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dak_To\" title=\"Battle of Dak To\">Battle of Dak To</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Dak_To\" title=\"Battle of Dak To\">Battle of Dak To</a> begins.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Battle of Dak To", "link": "https://wikipedia.org/wiki/Battle_of_Dak_To"}]}, {"year": "1969", "text": "Vietnam War: U.S. President <PERSON> addresses the nation on television and radio, asking the \"silent majority\" to join him in solidarity on the Vietnam War effort and to support his policies.", "html": "1969 - Vietnam War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> addresses the nation on television and radio, asking the \"<a href=\"https://wikipedia.org/wiki/Silent_majority\" title=\"Silent majority\">silent majority</a>\" to join him in solidarity on the Vietnam War effort and to support his policies.", "no_year_html": "Vietnam War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> addresses the nation on television and radio, asking the \"<a href=\"https://wikipedia.org/wiki/Silent_majority\" title=\"Silent majority\">silent majority</a>\" to join him in solidarity on the Vietnam War effort and to support his policies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Silent majority", "link": "https://wikipedia.org/wiki/Silent_majority"}]}, {"year": "1973", "text": "Mariner program: NASA launches the Mariner 10 toward Mercury. On March 29, 1974, it becomes the first space probe to reach that planet.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Mariner_10\" title=\"Mariner 10\">Mariner 10</a></i> toward <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a>. On March 29, 1974, it becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to reach that planet.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the <i><a href=\"https://wikipedia.org/wiki/Mariner_10\" title=\"Mariner 10\">Mariner 10</a></i> toward <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a>. On March 29, 1974, it becomes the first <a href=\"https://wikipedia.org/wiki/Space_probe\" class=\"mw-redirect\" title=\"Space probe\">space probe</a> to reach that planet.", "links": [{"title": "Mariner program", "link": "https://wikipedia.org/wiki/Mariner_program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mariner 10", "link": "https://wikipedia.org/wiki/Mariner_10"}, {"title": "Mercury (planet)", "link": "https://wikipedia.org/wiki/Mercury_(planet)"}, {"title": "Space probe", "link": "https://wikipedia.org/wiki/Space_probe"}]}, {"year": "1975", "text": "Four Bangladeshi politicians are killed in the Dhaka Central Jail.", "html": "1975 - Four Bangladeshi politicians are <a href=\"https://wikipedia.org/wiki/Jail_Killing_Day\" title=\"Jail Killing Day\">killed</a> in the Dhaka Central Jail.", "no_year_html": "Four Bangladeshi politicians are <a href=\"https://wikipedia.org/wiki/Jail_Killing_Day\" title=\"Jail Killing Day\">killed</a> in the Dhaka Central Jail.", "links": [{"title": "Jail Killing Day", "link": "https://wikipedia.org/wiki/Jail_Killing_Day"}]}, {"year": "1978", "text": "Dominica gains its independence from the United Kingdom.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> gains its independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> gains its independence from the United Kingdom.", "links": [{"title": "Dominica", "link": "https://wikipedia.org/wiki/Dominica"}]}, {"year": "1979", "text": "Greensboro massacre: Five members of the Communist Workers Party are shot dead and seven are wounded by a group of Klansmen and neo-Nazis during a \"Death to the Klan\" rally in Greensboro, North Carolina, United States.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Greensboro_massacre\" title=\"Greensboro massacre\">Greensboro massacre</a>: Five members of the <a href=\"https://wikipedia.org/wiki/Communist_Workers%27_Party_(United_States)\" title=\"Communist Workers' Party (United States)\">Communist Workers Party</a> are shot dead and seven are wounded by a group of <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\"><PERSON><PERSON>smen</a> and <a href=\"https://wikipedia.org/wiki/Neo-Nazism\" title=\"Neo-Nazism\">neo-Nazis</a> during a \"Death to the Klan\" rally in <a href=\"https://wikipedia.org/wiki/Greensboro,_North_Carolina\" title=\"Greensboro, North Carolina\">Greensboro, North Carolina</a>, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greensboro_massacre\" title=\"Greensboro massacre\">Greensboro massacre</a>: Five members of the <a href=\"https://wikipedia.org/wiki/Communist_Workers%27_Party_(United_States)\" title=\"Communist Workers' Party (United States)\">Communist Workers Party</a> are shot dead and seven are wounded by a group of <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\"><PERSON><PERSON>smen</a> and <a href=\"https://wikipedia.org/wiki/Neo-Nazism\" title=\"Neo-Nazism\">neo-Nazis</a> during a \"Death to the Klan\" rally in <a href=\"https://wikipedia.org/wiki/Greensboro,_North_Carolina\" title=\"Greensboro, North Carolina\">Greensboro, North Carolina</a>, United States.", "links": [{"title": "Greensboro massacre", "link": "https://wikipedia.org/wiki/Greensboro_massacre"}, {"title": "Communist Workers' Party (United States)", "link": "https://wikipedia.org/wiki/Communist_Workers%27_Party_(United_States)"}, {"title": "Ku Klux Klan", "link": "https://wikipedia.org/wiki/Ku_Klux_Klan"}, {"title": "Neo-Nazism", "link": "https://wikipedia.org/wiki/Neo-Nazism"}, {"title": "Greensboro, North Carolina", "link": "https://wikipedia.org/wiki/Greensboro,_North_Carolina"}]}, {"year": "1980", "text": "A Latin Carga Convair CV-880 crashes at Simón Bolívar International Airport in Venezuela, killing four.", "html": "1980 - A <a href=\"https://wikipedia.org/wiki/Latin_Carga\" title=\"Latin Carga\">Latin Carga</a> <a href=\"https://wikipedia.org/wiki/Convair_880\" title=\"Convair 880\">Convair CV-880</a> <a href=\"https://wikipedia.org/wiki/1980_Latin_Carga_Convair_CV-880_crash\" title=\"1980 Latin Carga Convair CV-880 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar_International_Airport_(Venezuela)\" title=\"Simón Bolívar International Airport (Venezuela)\">Simón Bolívar International Airport</a> in Venezuela, killing four.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Latin_Carga\" title=\"Latin Carga\">Latin Carga</a> <a href=\"https://wikipedia.org/wiki/Convair_880\" title=\"Convair 880\">Convair CV-880</a> <a href=\"https://wikipedia.org/wiki/1980_Latin_Carga_Convair_CV-880_crash\" title=\"1980 Latin Carga Convair CV-880 crash\">crashes</a> at <a href=\"https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar_International_Airport_(Venezuela)\" title=\"Simón Bolívar International Airport (Venezuela)\">Simón Bolívar International Airport</a> in Venezuela, killing four.", "links": [{"title": "Latin Carga", "link": "https://wikipedia.org/wiki/Latin_Carga"}, {"title": "Convair 880", "link": "https://wikipedia.org/wiki/Convair_880"}, {"title": "1980 Latin Carga Convair CV-880 crash", "link": "https://wikipedia.org/wiki/1980_Latin_Carga_Convair_CV-880_crash"}, {"title": "Simón Bolívar International Airport (Venezuela)", "link": "https://wikipedia.org/wiki/Sim%C3%B3n_Bol%C3%ADvar_International_Airport_(Venezuela)"}]}, {"year": "1982", "text": "The Salang Tunnel fire in Afghanistan kills 150-2000 people.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/1982_Salang_Tunnel_fire\" title=\"1982 Salang Tunnel fire\">Salang Tunnel fire</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> kills 150-2000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1982_Salang_Tunnel_fire\" title=\"1982 Salang Tunnel fire\">Salang Tunnel fire</a> in <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> kills 150-2000 people.", "links": [{"title": "1982 Salang Tunnel fire", "link": "https://wikipedia.org/wiki/1982_Salang_Tunnel_fire"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "1986", "text": "Iran-Contra affair: The Lebanese magazine Ash-Shiraa reports that the United States has been secretly selling weapons to Iran in order to secure the release of seven American hostages held by pro-Iranian groups in Lebanon.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: The <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanese</a> magazine <i><a href=\"https://wikipedia.org/wiki/Ash-Shiraa\" title=\"Ash-Shiraa\">Ash-Shira<PERSON></a></i> reports that the United States has been secretly selling weapons to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> in order to secure the release of seven American <a href=\"https://wikipedia.org/wiki/Hostage\" title=\"Hostage\">hostages</a> held by pro-Iranian groups in Lebanon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: The <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanese</a> magazine <i><a href=\"https://wikipedia.org/wiki/Ash-Shiraa\" title=\"Ash-Shiraa\">Ash-Shira<PERSON></a></i> reports that the United States has been secretly selling weapons to <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> in order to secure the release of seven American <a href=\"https://wikipedia.org/wiki/Hostage\" title=\"Hostage\">hostages</a> held by pro-Iranian groups in Lebanon.", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ash-Shi<PERSON>a"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Hostage", "link": "https://wikipedia.org/wiki/Hostage"}]}, {"year": "1986", "text": "The Compact of Free Association becomes law, granting the Federated States of Micronesia and the Marshall Islands independence from the United States.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Compact_of_Free_Association\" title=\"Compact of Free Association\">Compact of Free Association</a> becomes law, granting the <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Federated States of Micronesia</a> and the <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> independence from the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Compact_of_Free_Association\" title=\"Compact of Free Association\">Compact of Free Association</a> becomes law, granting the <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Federated States of Micronesia</a> and the <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> independence from the United States.", "links": [{"title": "Compact of Free Association", "link": "https://wikipedia.org/wiki/Compact_of_Free_Association"}, {"title": "Federated States of Micronesia", "link": "https://wikipedia.org/wiki/Federated_States_of_Micronesia"}, {"title": "Marshall Islands", "link": "https://wikipedia.org/wiki/Marshall_Islands"}]}, {"year": "1988", "text": "Sri Lankan Tamil mercenaries attempt to overthrow the Maldivian government. At President <PERSON><PERSON><PERSON>'s request, the Indian military suppresses the rebellion within 24 hours.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lankan</a> <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> <a href=\"https://wikipedia.org/wiki/Mercenary\" title=\"Mercenary\">mercenaries</a> attempt to <a href=\"https://wikipedia.org/wiki/1988_Maldives_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1988 Maldives coup d'état\">overthrow</a> the <a href=\"https://wikipedia.org/wiki/Maldives\" title=\"Maldives\">Maldivian</a> government. At President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s request, the Indian military suppresses the rebellion within 24 hours.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lankan</a> <a href=\"https://wikipedia.org/wiki/Tamil_people\" class=\"mw-redirect\" title=\"Tamil people\">Tamil</a> <a href=\"https://wikipedia.org/wiki/Mercenary\" title=\"Mercenary\">mercenaries</a> attempt to <a href=\"https://wikipedia.org/wiki/1988_Maldives_coup_d%27%C3%A9tat\" class=\"mw-redirect\" title=\"1988 Maldives coup d'état\">overthrow</a> the <a href=\"https://wikipedia.org/wiki/Maldives\" title=\"Maldives\">Maldivian</a> government. At President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s request, the Indian military suppresses the rebellion within 24 hours.", "links": [{"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Tamil people", "link": "https://wikipedia.org/wiki/Tamil_people"}, {"title": "Mercenary", "link": "https://wikipedia.org/wiki/Mercenary"}, {"title": "1988 Maldives coup d'état", "link": "https://wikipedia.org/wiki/1988_Maldives_coup_d%27%C3%A9tat"}, {"title": "Maldives", "link": "https://wikipedia.org/wiki/Maldives"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "Democratic Arkansas Governor <PERSON> defeats Republican President <PERSON> and Independent candidate <PERSON> in the 1992 United States presidential election.", "html": "1992 - Democratic <a href=\"https://wikipedia.org/wiki/Arkansas\" title=\"Arkansas\">Arkansas</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats Republican President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Independent candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/1992_United_States_presidential_election\" title=\"1992 United States presidential election\">1992 United States presidential election</a>.", "no_year_html": "Democratic <a href=\"https://wikipedia.org/wiki/Arkansas\" title=\"Arkansas\">Arkansas</a> Governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats Republican President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Independent candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/1992_United_States_presidential_election\" title=\"1992 United States presidential election\">1992 United States presidential election</a>.", "links": [{"title": "Arkansas", "link": "https://wikipedia.org/wiki/Arkansas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1992 United States presidential election", "link": "https://wikipedia.org/wiki/1992_United_States_presidential_election"}]}, {"year": "1994", "text": "Space Shuttle program: Atlantis launches on STS-66.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i> launches on <a href=\"https://wikipedia.org/wiki/STS-66\" title=\"STS-66\">STS-66</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Atlantis</a></i> launches on <a href=\"https://wikipedia.org/wiki/STS-66\" title=\"STS-66\">STS-66</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-66", "link": "https://wikipedia.org/wiki/STS-66"}]}, {"year": "1996", "text": "<PERSON>, the leader of the Turkish ultranationalist organization Grey Wolves, dies in the Susurluk car crash, leading to the resignation of Interior Minister <PERSON><PERSON><PERSON> (a leader of the True Path Party).", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%87atl%C4%B1\" title=\"<PERSON>\"><PERSON></a>, the leader of the Turkish ultranationalist organization <a href=\"https://wikipedia.org/wiki/Grey_Wolves_(organization)\" title=\"Grey Wolves (organization)\">Grey Wolves</a>, dies in the <a href=\"https://wikipedia.org/wiki/Susurluk_car_crash\" title=\"Susurluk car crash\">Susurluk car crash</a>, leading to the resignation of <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)\" title=\"Ministry of the Interior (Turkey)\">Interior Minister</a> <a href=\"https://wikipedia.org/wiki/Mehmet_A%C4%9Far\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (a leader of the <a href=\"https://wikipedia.org/wiki/True_Path_Party\" title=\"True Path Party\">True Path Party</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%87atl%C4%B1\" title=\"<PERSON>\"><PERSON></a>, the leader of the Turkish ultranationalist organization <a href=\"https://wikipedia.org/wiki/Grey_Wolves_(organization)\" title=\"Grey Wolves (organization)\">Grey Wolves</a>, dies in the <a href=\"https://wikipedia.org/wiki/Susurluk_car_crash\" title=\"Susurluk car crash\">Susurluk car crash</a>, leading to the resignation of <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)\" title=\"Ministry of the Interior (Turkey)\">Interior Minister</a> <a href=\"https://wikipedia.org/wiki/Mehmet_A%C4%9Far\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (a leader of the <a href=\"https://wikipedia.org/wiki/True_Path_Party\" title=\"True Path Party\">True Path Party</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%87atl%C4%B1"}, {"title": "Grey Wolves (organization)", "link": "https://wikipedia.org/wiki/Grey_Wolves_(organization)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> car crash", "link": "https://wikipedia.org/wiki/Susurl<PERSON>_car_crash"}, {"title": "Ministry of the Interior (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_(Turkey)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmet_A%C4%9Far"}, {"title": "True Path Party", "link": "https://wikipedia.org/wiki/True_Path_Party"}]}, {"year": "1997", "text": "The United States imposes economic sanctions against Sudan in response to its human rights abuses of its own citizens and its material and political assistance to Islamic extremist groups across the Middle East and East Africa.", "html": "1997 - The United States imposes <a href=\"https://wikipedia.org/wiki/Economic_sanctions\" title=\"Economic sanctions\">economic sanctions</a> against <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> in response to <a href=\"https://wikipedia.org/wiki/Human_rights_in_Sudan\" title=\"Human rights in Sudan\">its human rights abuses of its own citizens</a> and its material and political assistance to <a href=\"https://wikipedia.org/wiki/Islamic_extremism\" title=\"Islamic extremism\">Islamic extremist</a> groups across the Middle East and <a href=\"https://wikipedia.org/wiki/East_Africa\" title=\"East Africa\">East Africa</a>.", "no_year_html": "The United States imposes <a href=\"https://wikipedia.org/wiki/Economic_sanctions\" title=\"Economic sanctions\">economic sanctions</a> against <a href=\"https://wikipedia.org/wiki/Sudan\" title=\"Sudan\">Sudan</a> in response to <a href=\"https://wikipedia.org/wiki/Human_rights_in_Sudan\" title=\"Human rights in Sudan\">its human rights abuses of its own citizens</a> and its material and political assistance to <a href=\"https://wikipedia.org/wiki/Islamic_extremism\" title=\"Islamic extremism\">Islamic extremist</a> groups across the Middle East and <a href=\"https://wikipedia.org/wiki/East_Africa\" title=\"East Africa\">East Africa</a>.", "links": [{"title": "Economic sanctions", "link": "https://wikipedia.org/wiki/Economic_sanctions"}, {"title": "Sudan", "link": "https://wikipedia.org/wiki/Sudan"}, {"title": "Human rights in Sudan", "link": "https://wikipedia.org/wiki/Human_rights_in_Sudan"}, {"title": "Islamic extremism", "link": "https://wikipedia.org/wiki/Islamic_extremism"}, {"title": "East Africa", "link": "https://wikipedia.org/wiki/East_Africa"}]}, {"year": "2014", "text": "One World Trade Center officially opens in New York City, replacing the Twin Towers after they were destroyed during the September 11 attacks.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a> officially opens in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, replacing the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">Twin Towers</a> after they were destroyed during the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">One World Trade Center</a> officially opens in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, replacing the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">Twin Towers</a> after they were destroyed during the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "links": [{"title": "One World Trade Center", "link": "https://wikipedia.org/wiki/One_World_Trade_Center"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "2020", "text": "The 2020 United States presidential election takes place between Democratic <PERSON> and Republican incumbent President <PERSON>. On November 7, <PERSON><PERSON> was declared the winner.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 United States presidential election</a> takes place between <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican</a> incumbent <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. On November 7, <PERSON><PERSON> was declared the winner.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 United States presidential election</a> takes place between <a href=\"https://wikipedia.org/wiki/Democratic_Party_(United_States)\" title=\"Democratic Party (United States)\">Democratic</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Republican_Party_(United_States)\" title=\"Republican Party (United States)\">Republican</a> incumbent <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. On November 7, <PERSON><PERSON> was declared the winner.", "links": [{"title": "2020 United States presidential election", "link": "https://wikipedia.org/wiki/2020_United_States_presidential_election"}, {"title": "Democratic Party (United States)", "link": "https://wikipedia.org/wiki/Democratic_Party_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Republican Party (United States)", "link": "https://wikipedia.org/wiki/Republican_Party_(United_States)"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}], "Births": [{"year": "39", "text": "<PERSON>, Roman poet (d. 65)", "html": "39 - AD 39 - <a href=\"https://wikipedia.org/wiki/Lucan\" title=\"Lucan\"><PERSON></a>, Roman poet (d. 65)", "no_year_html": "AD 39 - <a href=\"https://wikipedia.org/wiki/Lucan\" title=\"Lucan\"><PERSON></a>, Roman poet (d. 65)", "links": [{"title": "Lucan", "link": "https://wikipedia.org/wiki/Lucan"}]}, {"year": "1500", "text": "<PERSON><PERSON><PERSON>, Italian sculptor and painter (d. 1571)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and painter (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian sculptor and painter (d. 1571)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benvenuto_Cellini"}]}, {"year": "1505", "text": "<PERSON>, German physician and astrologer (d. 1577)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Achilles Gasser\"><PERSON></a>, German physician and astrologer (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Achilles Gasser\"><PERSON></a>, German physician and astrologer (d. 1577)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Achilles_Gasser"}]}, {"year": "1527", "text": "<PERSON><PERSON><PERSON>, Gnesio-Lutheran theologian (d. 1588)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Heshusius\"><PERSON><PERSON><PERSON></a>, Gnesio-Lutheran theologian (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Heshusius\"><PERSON><PERSON><PERSON></a>, Gnesio-Lutheran theologian (d. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON>, Italian painter and illustrator (d. 1609)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (d. 1609)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1587", "text": "<PERSON>, German organist, composer, and educator (d. 1654)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and educator (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and educator (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON><PERSON>, Ottoman sultan (d. 1622)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>sman_II\" title=\"Osman II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Osman_II\" title=\"Osman II\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (d. 1622)", "links": [{"title": "Osman II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_II"}]}, {"year": "1618", "text": "<PERSON><PERSON><PERSON><PERSON>, Mughal emperor of India (d. 1707)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangzeb\">Aurangzeb</a>, <a href=\"https://wikipedia.org/wiki/Mughal_emperor\" class=\"mw-redirect\" title=\"Mughal emperor\">Mughal emperor</a> of India (d. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangzeb\">Aurangzeb</a>, <a href=\"https://wikipedia.org/wiki/Mughal_emperor\" class=\"mw-redirect\" title=\"Mughal emperor\">Mughal emperor</a> of India (d. 1707)", "links": [{"title": "Aurangzeb", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>zeb"}, {"title": "Mughal emperor", "link": "https://wikipedia.org/wiki/Mughal_emperor"}]}, {"year": "1656", "text": "<PERSON>, Austrian organist and composer (d. 1738)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1659", "text": "<PERSON><PERSON><PERSON>, Royal consort (d. 1701)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Royal consort (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Royal consort (d. 1701)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, Czech composer (d. 1742)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/Jan_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer (d. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Scottish chemist and physician (d. 1819)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and physician (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish chemist and physician (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, American soldier, lawyer, and politician, 6th United States Secretary of State (d. 1842)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Cabinet_member)\" class=\"mw-redirect\" title=\"<PERSON> (Cabinet member)\"><PERSON></a>, American soldier, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Cabinet_member)\" class=\"mw-redirect\" title=\"<PERSON> (Cabinet member)\"><PERSON></a>, American soldier, lawyer, and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1842)", "links": [{"title": "<PERSON> (Cabinet member)", "link": "https://wikipedia.org/wiki/<PERSON>_(Cabinet_member)"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1777", "text": "Princess <PERSON> of the United Kingdom (d. 1848)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Sophia_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1848)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Sophia_of_the_United_Kingdom"}]}, {"year": "1793", "text": "<PERSON>, American businessman and politician (d. 1836)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Stephen <PERSON>\"><PERSON></a>, American businessman and politician (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, American poet and journalist (d. 1878)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and journalist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and journalist (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, American lawyer and politician, 14th Governor of Rhode Island (d. 1856)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"<PERSON> III\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rhode_Island\" class=\"mw-redirect\" title=\"List of Governors of Rhode Island\">Governor of Rhode Island</a> (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Rhode_Island\" class=\"mw-redirect\" title=\"List of Governors of Rhode Island\">Governor of Rhode Island</a> (d. 1856)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_III"}, {"title": "List of Governors of Rhode Island", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Rhode_Island"}]}, {"year": "1801", "text": "<PERSON>, German author and publisher, founded the Baedeker Publishing Company (d. 1859)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and publisher, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Baedeker Publishing Company</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and publisher, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Baedeker Publishing Company</a> (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Italian composer (d. 1835)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Irish journalist and activist (d. 1875)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and activist (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and activist (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON>, American general and lawyer (d. 1894)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Early\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and lawyer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and lawyer (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American minister and activist (d. 1898)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Calvin_<PERSON>bank"}]}, {"year": "1845", "text": "<PERSON>, American lawyer, jurist, and politician, 9th Chief Justice of the United States (d. 1921)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, jurist, and politician, 9th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1852", "text": "Emperor <PERSON> of Japan (d. 1912)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor <PERSON>\">Emperor Meiji</a> of Japan (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\">Emperor Meiji</a> of Japan (d. 1912)", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Italian micropalaeontologist (d. 1931)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian micropalaeontologist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian micropalaeontologist (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Scottish-American baseball player and manager (d. 1918)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Scottish-American baseball player and manager (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Scottish-American baseball player and manager (d. 1918)", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)"}]}, {"year": "1857", "text": "<PERSON>, Russian general (d. 1918)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American journalist and politician (d. 1916)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and politician (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and politician (d. 1916)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1863", "text": "<PERSON>, French physicist and academic (d. 1925)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American baseball player (d. 1910)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, English rugby player and manager (d. 1943)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Latvian composer and conductor (d. 1910)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Em%C4%ABls_D%C4%81rzi%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian composer and conductor (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em%C4%ABls_D%C4%81rzi%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian composer and conductor (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em%C4%ABls_D%C4%81rzi%C5%86%C5%A1"}]}, {"year": "1876", "text": "<PERSON>, American bishop and missionary (d. 1940)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop and missionary (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop and missionary (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Chilean general and politician, 20th President of Chile (d. 1960)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%A1%C3%B1ez_del_Campo\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%A1%C3%B1ez_del_Campo\" title=\"<PERSON>\"><PERSON></a>, Chilean general and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Ib%C3%A1%C3%B1ez_del_Campo"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1877", "text": "<PERSON><PERSON>, American environmentalist (d. 1962)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American environmentalist (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American environmentalist (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Indian Carnatic singer and activist (d. 1952)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Bangalore_Nagarathnamma\" title=\"Bangalore Nagarathnamma\">Bangalore Nagarathnamma</a>, Indian <a href=\"https://wikipedia.org/wiki/Carnatic_music\" title=\"Carnatic music\">Carnatic</a> singer and activist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangalore_Nagarathnamma\" title=\"Bangalore Nagarathnamma\">Bangalore Nagarathnamma</a>, Indian <a href=\"https://wikipedia.org/wiki/Carnatic_music\" title=\"Carnatic music\">Carnatic</a> singer and activist (d. 1952)", "links": [{"title": "Bangalore Nagarathnamma", "link": "https://wikipedia.org/wiki/Bangalore_Nagarathnamma"}, {"title": "Carnatic music", "link": "https://wikipedia.org/wiki/Carnatic_music"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Belarusian writer (d. 1956)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian writer (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian writer (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American publisher and politician, 49th Speaker of the United States House of Representatives (d. 1968)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, American publisher and politician, 49th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American publisher and politician, 49th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1968)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Russian author and poet (d. 1964)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Samuil_Marshak\" title=\"<PERSON><PERSON> Marshak\"><PERSON><PERSON></a>, Russian author and poet (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samuil_<PERSON>ak\" title=\"<PERSON><PERSON>ak\"><PERSON><PERSON></a>, Russian author and poet (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ak"}]}, {"year": "1887", "text": "<PERSON>, British geologist (d. 1978)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American author (d. 1967)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch-Brazilian priest and missionary (d. 1943)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Eust%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Brazilian priest and missionary (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eust%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Dutch-Brazilian priest and missionary (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eust%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Canadian pilot and colonel, Victoria Cross recipient (d. 1930)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pilot and colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pilot and colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek captain and politician, 133rd Prime Minister of Greece (d. 1964)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Sofok<PERSON>_Venizelos\" title=\"Sofok<PERSON> Venizel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek captain and politician, 133rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sofok<PERSON>_Venizelos\" title=\"Sofok<PERSON> Venizelos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek captain and politician, 133rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1964)", "links": [{"title": "Sofok<PERSON>", "link": "https://wikipedia.org/wiki/Sofoklis_Venizelos"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Swedish-American illustrator and animator (d. 1970)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American illustrator and animator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American illustrator and animator (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>taf_<PERSON>ggren"}]}, {"year": "1899", "text": "<PERSON>, American billiards player (d. 1950)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billiards player (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American billiards player (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian pianist and composer (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Rezs%C5%91_Seress\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian pianist and composer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rezs%C5%91_Seress\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian pianist and composer (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rezs%C5%91_Seress"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Italian physicist and academic (d. 1986)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Watagh<PERSON>\" title=\"Gleb Wataghin\"><PERSON><PERSON><PERSON></a>, Ukrainian-Italian physicist and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wata<PERSON>\" title=\"G<PERSON><PERSON> Wataghin\"><PERSON><PERSON><PERSON></a>, Ukrainian-Italian physicist and academic (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gle<PERSON>_<PERSON>ataghin"}]}, {"year": "1900", "text": "<PERSON>, German businessman, founded Adidas (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Adidas\" title=\"Adidas\">Adidas</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Adidas\" title=\"Adidas\">Adidas</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Adidas", "link": "https://wikipedia.org/wiki/Adidas"}]}, {"year": "1901", "text": "<PERSON> of Belgium (d. 1983)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON> of Belgium</a> (d. 1983)", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Leopold_III_of_Belgium"}]}, {"year": "1901", "text": "<PERSON>, French historian, theorist, and author (d. 1976)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, theorist, and author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian, theorist, and author (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ux"}]}, {"year": "1901", "text": "<PERSON>, Canadian ice hockey player (d. 1969)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American photographer and journalist (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American painter and academic (d. 1998)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American teacher and historian (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher and historian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American teacher and historian (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian lawyer and politician, 6th President of Italy (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Giovanni_<PERSON>\" title=\"Giovanni Leone\"><PERSON></a>, Italian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giovanni_<PERSON>\" title=\"Giovanni Leone\"><PERSON></a>, Italian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (d. 2001)", "links": [{"title": "Giovanni Leone", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Canadian-American football player, wrestler, and coach (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gurski\" title=\"<PERSON><PERSON><PERSON>gurski\"><PERSON><PERSON><PERSON></a>, Canadian-American football player, wrestler, and coach (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American football player, wrestler, and coach (d. 1990)", "links": [{"title": "Bron<PERSON>", "link": "https://wikipedia.org/wiki/Bronko_Nagurski"}]}, {"year": "1909", "text": "<PERSON>, Scottish-American journalist and author (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American journalist and author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American journalist and author (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Czech director, animator, production designer, and screenwriter (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech director, animator, production designer, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech director, animator, production designer, and screenwriter (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Dutch footballer and manager (d. 1974)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Kick_Smit\" title=\"Kick Smit\"><PERSON></a>, Dutch footballer and manager (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kick_Smit\" title=\"Kick Smit\"><PERSON></a>, Dutch footballer and manager (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kick_Smit"}]}, {"year": "1912", "text": "<PERSON>, Paraguayan general and politician, 46th President of Paraguay (d. 2006)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan general and politician, 46th <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan general and politician, 46th <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Paraguay", "link": "https://wikipedia.org/wiki/President_of_Paraguay"}]}, {"year": "1915", "text": "<PERSON>, American journalist and radio host (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Indian activist (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maharana\" title=\"<PERSON><PERSON><PERSON> Maharana\"><PERSON><PERSON><PERSON></a>, Indian activist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maharana\" title=\"<PERSON><PERSON><PERSON> Maharana\"><PERSON><PERSON><PERSON></a>, Indian activist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Annapur<PERSON>_Ma<PERSON>na"}]}, {"year": "1918", "text": "<PERSON>, French director, producer, and screenwriter (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American sailor, baseball player, and sportscaster (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor, baseball player, and sportscaster (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor, baseball player, and sportscaster (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American general (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American lieutenant, lawyer, and politician (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Spanish author and illustrator (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Blasco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and illustrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Blasco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and illustrator (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Blasco"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Scottish journalist and author (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish journalist and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish journalist and author (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech author (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Kv%C4%9Bta_Leg%C3%A1tov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kv%C4%9Bta_Leg%C3%A1tov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech author (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kv%C4%9Bta_Leg%C3%A1tov%C3%A1"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian poet, educator, and activist (d. 1993)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>odger<PERSON>_Noonuccal\" title=\"Oodgeroo Noonuccal\"><PERSON><PERSON><PERSON><PERSON>on<PERSON></a>, Australian poet, educator, and activist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>odger<PERSON>_Noonuccal\" title=\"Oodgeroo Noonuccal\"><PERSON><PERSON><PERSON><PERSON> Noonuccal</a>, Australian poet, educator, and activist (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oodger<PERSON>_<PERSON>uccal"}]}, {"year": "1921", "text": "<PERSON>, American soldier and actor (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and actor (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English-Canadian union leader and diplomat, Canadian Ambassador to Ireland (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian union leader and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_and_high_commissioners_to_Ireland\" class=\"mw-redirect\" title=\"List of Canadian ambassadors and high commissioners to Ireland\">Canadian Ambassador to Ireland</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian union leader and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Canadian_ambassadors_and_high_commissioners_to_Ireland\" class=\"mw-redirect\" title=\"List of Canadian ambassadors and high commissioners to Ireland\">Canadian Ambassador to Ireland</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Canadian ambassadors and high commissioners to Ireland", "link": "https://wikipedia.org/wiki/List_of_Canadian_ambassadors_and_high_commissioners_to_Ireland"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Russian ballerina (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ballerina (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ballerina (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Violet<PERSON>_Elvin"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Irish cardinal (d. 1990)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_%C3%93_Fiaich\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish cardinal (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_%C3%93_Fiaich\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish cardinal (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_%C3%93_Fiaich"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Japanese author and critic (d. 1995)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hitomi\"><PERSON><PERSON><PERSON></a>, Japanese author and critic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hitomi\"><PERSON><PERSON><PERSON></a>, Japanese author and critic (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor, director, and choreographer (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and choreographer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and choreographer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Mexican bishop (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican bishop (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican bishop (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Lithuanian engineer and politician, 3rd President of Lithuania", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian engineer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Valdas_Adamkus"}, {"title": "President of Lithuania", "link": "https://wikipedia.org/wiki/President_of_Lithuania"}]}, {"year": "1926", "text": "<PERSON>, Canadian archbishop (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(philanthropist)"}]}, {"year": "1927", "text": "<PERSON>, Canadian businessman, co-founded McCain Foods (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, co-founded <a href=\"https://wikipedia.org/wiki/McCain_Foods\" title=\"McCain Foods\">McCain Foods</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McCain Foods", "link": "https://wikipedia.org/wiki/McCain_Foods"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Norwegian politician, 21st Prime Minister of Norway (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Norway\" title=\"Prime Minister of Norway\">Prime Minister of Norway</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Norway", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Norway"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator (d. 2000)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jima"}]}, {"year": "1928", "text": "<PERSON>, Australian politician and diplomat, 37th Australian Minister for Defence (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician and diplomat, 37th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician and diplomat, 37th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 2013)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese animator and producer (d. 1989)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator and producer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese animator and producer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American basketball player (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chilean footballer, manager, and politician (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer, manager, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer, manager, and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American academic and politician (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American engineer, pilot, and astronaut (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, pilot, and astronaut (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, pilot, and astronaut (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American blues singer (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American blues singer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American blues singer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON> <PERSON>, American pastor and author (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American pastor and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American pastor and author (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English cyclist (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, English cyclist (d. 2022)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese astronomer and academic", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Tsutomu_<PERSON><PERSON>\" title=\"Tsutomu <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese astronomer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsutomu_<PERSON><PERSON>\" title=\"Tsutomu Se<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese astronomer and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsu<PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1930", "text": "<PERSON>, American actress", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Dutch philosopher and scholar (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Frits_Staal\" title=\"Frits Staal\"><PERSON><PERSON>aal</a>, Dutch philosopher and scholar (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Staal\" title=\"Frits Staal\"><PERSON><PERSON> Staal</a>, Dutch philosopher and scholar (d. 2012)", "links": [{"title": "Frits Staal", "link": "https://wikipedia.org/wiki/Frits_Staal"}]}, {"year": "1931", "text": "<PERSON><PERSON>, North Korean soldier and politician, 7th Premier of North Korea (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Yon_<PERSON>yong-muk\" title=\"Yon <PERSON>-muk\"><PERSON><PERSON>-muk</a>, North Korean soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Premiers_of_North_Korea\" class=\"mw-redirect\" title=\"List of Premiers of North Korea\">Premier of North Korea</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yon_<PERSON>yong-muk\" title=\"Yon <PERSON>-muk\"><PERSON><PERSON>-muk</a>, North Korean soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/List_of_Premiers_of_North_Korea\" class=\"mw-redirect\" title=\"List of Premiers of North Korea\">Premier of North Korea</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>uk", "link": "https://wikipedia.org/wiki/Yon_<PERSON><PERSON><PERSON>-muk"}, {"title": "List of Premiers of North Korea", "link": "https://wikipedia.org/wiki/List_of_Premiers_of_North_Korea"}]}, {"year": "1931", "text": "<PERSON>, Italian actress, singer, and screenwriter (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, singer, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress, singer, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vitti"}]}, {"year": "1931", "text": "<PERSON>, Chinese bishop (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese bishop (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese bishop (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Irish businessman and politician, 9th Taoiseach of Ireland (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player (d. 2006)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English-American composer and conductor (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English-American composer and conductor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English-American composer and conductor (d. 2011)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1933", "text": "<PERSON>, American actor, singer, and dancer (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English actor (d. 1995)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress (d. 1995)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rsaut\"><PERSON><PERSON></a>, American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Corsaut\"><PERSON><PERSON></a>, American actress (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ut"}]}, {"year": "1933", "text": "<PERSON>, American lawyer, academic, and politician, 65th Governor of Massachusetts", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and politician, 65th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and politician, 65th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Indian economist and academic, Nobel Prize laureate", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1934", "text": "<PERSON>, Baron <PERSON> of Dorking, English poet and politician, Chancellor of the Duchy of Lancaster", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Dorking\" title=\"<PERSON>, Baron <PERSON> of Dorking\"><PERSON>, Baron <PERSON> of Dorking</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Dorking\" title=\"<PERSON>, Baron <PERSON> of Dorking\"><PERSON>, Baron <PERSON> of Dorking</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "links": [{"title": "<PERSON>, <PERSON> of Dorking", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_Dorking"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1934", "text": "<PERSON>, Dutch businessman, educator, and politician (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, educator, and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch businessman, educator, and politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Estonian philologist and academic, 3rd First Lady of Estonia", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_R%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and academic, 3rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_Estonia\" class=\"mw-redirect\" title=\"First Lady of Estonia\">First Lady of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BC%C3%BCtel\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and academic, 3rd <a href=\"https://wikipedia.org/wiki/First_Lady_of_Estonia\" class=\"mw-redirect\" title=\"First Lady of Estonia\">First Lady of Estonia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ingrid_R%C3%BC%C3%BCtel"}, {"title": "First Lady of Estonia", "link": "https://wikipedia.org/wiki/First_Lady_of_Estonia"}]}, {"year": "1936", "text": "<PERSON>, Australian-American tennis player and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator, created Golgo 13 (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/Golgo_13\" title=\"Golgo 13\">Golgo 13</a></i> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/Golgo_13\" title=\"Golgo 13\">Golgo 13</a></i> (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>to"}, {"title": "Golgo 13", "link": "https://wikipedia.org/wiki/Golgo_13"}]}, {"year": "1937", "text": "<PERSON>, German lawyer and politician, 15th Mayor of Marburg", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ller\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ller\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dietrich_M%C3%B6ller"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1937", "text": "<PERSON>, American football player (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English mathematician and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Japanese actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, French actor, director, and screenwriter (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Scottish-English singer-songwriter and guitarist (d. 2011)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Dutch poet and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and manager (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German footballer and manager (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Gerd_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerd_M%C3%BCller"}]}, {"year": "1945", "text": "<PERSON>, English bass guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, German mountaineer, photographer, and author (d. 1982)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mountaineer, photographer, and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German mountaineer, photographer, and author (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Japanese-American lawyer and politician, U.S. Senator from Hawaii", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American lawyer and politician, U.S. Senator from Hawaii", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American lawyer and politician, U.S. Senator from Hawaii", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 3rd Estonian Minister of Social Affairs", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)\" class=\"mw-redirect\" title=\"Minister of Social Affairs (Estonia)\">Estonian Minister of Social Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ir"}, {"title": "Minister of Social Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Social_Affairs_(Estonia)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Iranian journalist and critic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian journalist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian journalist and critic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON></a>, Japanese politician", "links": [{"title": "<PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "1948", "text": "<PERSON>, Austrian race car driver (d. 1974)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish singer-songwriter and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter and actress", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, German footballer, coach, and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi linguist (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Mahbubul_Haque\" title=\"Mahbubul Haque\"><PERSON><PERSON><PERSON><PERSON> Haque</a>, Bangladeshi linguist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mahbubul_Haque\" title=\"Mahbubul Haque\"><PERSON><PERSON><PERSON><PERSON>que</a>, Bangladeshi linguist (d. 2024)", "links": [{"title": "Mahbubul Haque", "link": "https://wikipedia.org/wiki/Mahbubul_Haque"}]}, {"year": "1949", "text": "<PERSON>, American actor and screenwriter (d. 2006)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2006)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and  politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese engineer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American boxer and talk show host", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English biochemist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English biochemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English biochemist and academic", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)"}]}, {"year": "1949", "text": "<PERSON>, English-American journalist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Italian journalist and author (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_<PERSON>gai"}]}, {"year": "1950", "text": "<PERSON>, American author and critic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and critic", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Baseball)\" class=\"mw-redirect\" title=\"<PERSON> (Baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Baseball)\" class=\"mw-redirect\" title=\"<PERSON> (Baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (Baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Baseball)"}]}, {"year": "1951", "text": "<PERSON>, American cartoonist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch footballer and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American comedian, actress, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American voice actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Taiwanese-American scientist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" class=\"mw-redirect\" title=\"<PERSON> (scientist)\"><PERSON></a>, Taiwanese-American scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" class=\"mw-redirect\" title=\"<PERSON> (scientist)\"><PERSON></a>, Taiwanese-American scientist", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)"}]}, {"year": "1953", "text": "<PERSON>, American actress and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"He<PERSON><PERSON> Creed\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English director and composer (d. 2025)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and composer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and composer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American comedian, producer, and talk show host", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, producer, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, producer, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Filipino actress and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actress and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter and actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adam <PERSON>\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress and comedian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Italian singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English nurse and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American football player and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish politician, 2nd Scottish Minister for Justice", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Justice_(Scotland)\" class=\"mw-redirect\" title=\"Minister for Justice (Scotland)\">Scottish Minister for Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_for_Justice_(Scotland)\" class=\"mw-redirect\" title=\"Minister for Justice (Scotland)\">Scottish Minister for Justice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Justice (Scotland)", "link": "https://wikipedia.org/wiki/Minister_for_Justice_(Scotland)"}]}, {"year": "1956", "text": "<PERSON>, American actor, puppeteer, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, puppeteer, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, puppeteer, producer, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1956", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and coach (d. 2014)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2014)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian sertanejo singer (d. 2024)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian <a href=\"https://wikipedia.org/wiki/Sertanejo_music\" title=\"Sertanejo music\">sertane<PERSON></a> singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian <a href=\"https://wikipedia.org/wiki/Sertanejo_music\" title=\"Sertanejo music\">se<PERSON><PERSON><PERSON></a> singer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(singer)"}, {"title": "Sertanejo music", "link": "https://wikipedia.org/wiki/Sertanejo_music"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Swedish actor, director, producer, screenwriter, and martial artist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor, director, producer, screenwriter, and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish actor, director, producer, screenwriter, and martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English actor (d. 2000)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1958", "text": "<PERSON>, American football coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American volleyball player, coach, and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American volleyball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American volleyball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English singer-songwriter and musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, 2nd Earl of Snowdon, English businessman", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Snowdon\" title=\"<PERSON>, 2nd Earl of Snowdon\"><PERSON>, 2nd Earl of Snowdon</a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Snowdon\" title=\"<PERSON>, 2nd Earl of Snowdon\"><PERSON>, 2nd Earl of Snowdon</a>, English businessman", "links": [{"title": "<PERSON>, 2nd Earl of Snowdon", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Japanese-American mountaineer and journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American mountaineer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American mountaineer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American businessman, co-founded Valve", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Valve_Corporation\" title=\"Valve Corporation\">Valve</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Valve_Corporation\" title=\"Valve Corporation\">Valve</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Valve Corporation", "link": "https://wikipedia.org/wiki/Valve_Corporation"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, English lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guggenheim\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Guggenheim\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1963", "text": "<PERSON>, English footballer, manager, and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American football player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Algimantas_Briaunis\" class=\"mw-redirect\" title=\"Algimantas Briaunis\"><PERSON><PERSON><PERSON><PERSON> Briaunis</a>, Lithuanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Algimantas_Briaunis\" class=\"mw-redirect\" title=\"Algimantas Briaunis\"><PERSON><PERSON><PERSON><PERSON> Briaunis</a>, Lithuanian footballer and manager", "links": [{"title": "Algiman<PERSON>", "link": "https://wikipedia.org/wiki/Algimantas_B<PERSON>unis"}]}, {"year": "1964", "text": "<PERSON>, New Zealand cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Dutch footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French-English author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_novelist)\" title=\"<PERSON> (French novelist)\"><PERSON></a>, French-English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_novelist)\" title=\"<PERSON> (French novelist)\"><PERSON></a>, French-English author", "links": [{"title": "<PERSON> (French novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_novelist)"}]}, {"year": "1965", "text": "<PERSON>, American golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian ice hockey player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neil<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neill_(ice_hockey)"}]}, {"year": "1967", "text": "<PERSON>, Welsh singer and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1967", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish mountaineer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1urrategi\" title=\"<PERSON>\"><PERSON></a>, Spanish mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1urrategi\" title=\"<PERSON>\"><PERSON></a>, Spanish mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_I%C3%B1urrategi"}]}, {"year": "1968", "text": "<PERSON>, Canadian baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swiss-Italian DJ and producer (d. 2017)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian DJ and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Miles\"><PERSON></a>, Swiss-Italian DJ and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Finnish politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>po"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Dutch rower", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A5rd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A5rd\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ig%C3%A5rd"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American aerospace engineer and astronaut", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American aerospace engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American aerospace engineer and astronaut", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Spanish football manager and former player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish football manager and former player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish football manager and former player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Irish actor, comedian, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English archer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Tobagonian footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tobagonian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tobagonian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English footballer and manager (d. 2017)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Ugo_Ehiogu\" title=\"Ugo Ehiogu\"><PERSON><PERSON></a>, English footballer and manager (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ugo_Ehiogu\" title=\"Ugo Ehiogu\"><PERSON><PERSON></a>, English footballer and manager (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ugo_Ehiogu"}]}, {"year": "1972", "text": "<PERSON>, German footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Dutch runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English television host and author", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American rapper, producer, and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Sticky_Fingaz\" title=\"Sticky Fingaz\"><PERSON><PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sticky_Fingaz\" title=\"Sticky Fingaz\"><PERSON><PERSON></a>, American rapper, producer, and actor", "links": [{"title": "<PERSON>y Fin<PERSON>z", "link": "https://wikipedia.org/wiki/Sticky_Fingaz"}]}, {"year": "1973", "text": "<PERSON>, American businessman and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian radio and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian radio and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, French basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Argentinian-Mexican footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American ukulele player and composer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ukulele player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ukulele player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, German footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American model and actor (d. 2015)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Norwegian drummer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sj%C3%B8vaag\" title=\"<PERSON> Sjøvaag\"><PERSON></a>, Norwegian drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sj%C3%B8vaag\" title=\"<PERSON> Sjøvaag\"><PERSON></a>, Norwegian drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Sj%C3%B8vaag"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese softball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>rok<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese softball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rok<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese softball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sa<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Argentinian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Danish motorcycle racer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Danish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Danish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Diego_L%C3%B3pez_Rodr%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_L%C3%B3pez_Rodr%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_L%C3%B3pez_Rodr%C3%ADguez"}]}, {"year": "1981", "text": "<PERSON>, Argentinian-Mexican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADas_Vuoso\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADas_Vuoso\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Mat%C3%ADas_Vuoso"}]}, {"year": "1981", "text": "<PERSON>, Chilean footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Estonian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Sten_Pentus\" title=\"Sten Pentus\"><PERSON><PERSON></a>, Estonian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sten_Pentus\" title=\"Sten Pentus\"><PERSON><PERSON></a>, Estonian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sten_Pentus"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Dutch speed skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch speed skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Egemen_Korkmaz\" title=\"Egemen Korkmaz\"><PERSON><PERSON><PERSON> Korkmaz</a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egemen_Korkmaz\" title=\"Egemen Korkmaz\"><PERSON><PERSON>men Korkmaz</a>, Turkish footballer", "links": [{"title": "Egemen <PERSON>", "link": "https://wikipedia.org/wiki/Egemen_Korkmaz"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American professional basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian figure skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Rinn<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> R<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>e"}]}, {"year": "1982", "text": "<PERSON>, Russian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Norwegian singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Tamba_Hali\" title=\"Tamba Hali\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tamba_Hali\" title=\"Tamba Hali\"><PERSON><PERSON></a>, American football player", "links": [{"title": "Tamba Hali", "link": "https://wikipedia.org/wiki/Tamba_Hali"}]}, {"year": "1984", "text": "<PERSON>, Danish race car driver (d. 2011)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish race car driver (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish race car driver (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese singer-songwriter and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paul_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American  basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, English actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thomas"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Piet_Velthuizen\" title=\"Piet Velthuizen\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piet_Velthuizen\" title=\"Piet Velthuizen\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piet_Velthuizen"}]}, {"year": "1986", "text": "<PERSON><PERSON>, South Korean singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCtz\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCtz\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sch%C3%BCtz"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American kidnapping victim, activist, and journalist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American kidnapping victim, activist, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Smart\"><PERSON></a>, American kidnapping victim, activist, and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian model and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gemma_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Di<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian rower", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_El_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, French singer-songwriter and guitarist", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Australian footballer and cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer and cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer and cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Barbadian netball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English slalom canoeist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, English slalom canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, English slalom canoeist", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Vale<PERSON>_Solo<PERSON>eva\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eva\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vale<PERSON>_<PERSON>eva"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>re<PERSON>\" title=\"<PERSON> Tre<PERSON>\"><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>re<PERSON>\" title=\"<PERSON> Trevisan\"><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martina_Trevisan"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American television personality and model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Indian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese actor ", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban athlete", "html": "1997 - <a href=\"https://wikipedia.org/wiki/L%C3%A1zaro_Mart%C3%AD<PERSON><PERSON>_(triple_jumper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (triple jumper)\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1zaro_Mart%C3%<PERSON><PERSON><PERSON>_(triple_jumper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (triple jumper)\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban athlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (triple jumper)", "link": "https://wikipedia.org/wiki/L%C3%A1zaro_Mart%C3%ADnez_(triple_jumper)"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Australian paralympic swimmer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Elliott\"><PERSON><PERSON><PERSON></a>, Australian paralympic swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Elliott\"><PERSON><PERSON><PERSON></a>, Australian paralympic swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>R<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actress", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "361", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 317)", "html": "361 - <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (b. 317)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON>ius_II\" title=\"Constantius II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (b. 317)", "links": [{"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}]}, {"year": "753", "text": "Saint <PERSON><PERSON><PERSON>, Spanish-German monk and saint (b. 700)", "html": "753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pirmin\" title=\"Saint Pirmin\"><PERSON></a>, Spanish-German monk and saint (b. 700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pirmin\" title=\"Saint Pirmin\"><PERSON></a>, Spanish-German monk and saint (b. 700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Pi<PERSON>in"}]}, {"year": "1219", "text": "<PERSON><PERSON>, 1st Earl of Winchester, English baron and rebel (b. c. 1170)", "html": "1219 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Winchester\" title=\"<PERSON><PERSON>, 1st Earl of Winchester\"><PERSON><PERSON>, 1st Earl of Winchester</a>, English baron and rebel (b. c. 1170)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Winchester\" title=\"<PERSON><PERSON> <PERSON>, 1st Earl of Winchester\"><PERSON><PERSON>, 1st Earl of Winchester</a>, English baron and rebel (b. c. 1170)", "links": [{"title": "<PERSON><PERSON>, 1st Earl of Winchester", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Winchester"}]}, {"year": "1220", "text": "<PERSON><PERSON><PERSON> of Castile, Queen of Portugal, spouse of King <PERSON><PERSON><PERSON> II of Portugal (b. 1186)", "html": "1220 - <a href=\"https://wikipedia.org/wiki/Urraca_of_Castile,_Queen_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Castile, Queen of Portugal\"><PERSON><PERSON><PERSON> of Castile, Queen of Portugal</a>, spouse of King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Portugal\" title=\"<PERSON><PERSON><PERSON> II of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1186)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urraca_of_Castile,_Queen_of_Portugal\" title=\"<PERSON><PERSON><PERSON> of Castile, Queen of Portugal\"><PERSON><PERSON><PERSON> of Castile, Queen of Portugal</a>, spouse of King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Portugal\" title=\"<PERSON><PERSON><PERSON> II of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1186)", "links": [{"title": "<PERSON><PERSON><PERSON> of Castile, Queen of Portugal", "link": "https://wikipedia.org/wiki/Urraca_of_Castile,_Queen_of_Portugal"}, {"title": "<PERSON><PERSON><PERSON> II of Portugal", "link": "https://wikipedia.org/wiki/Afonso_II_of_Portugal"}]}, {"year": "1254", "text": "<PERSON> III <PERSON>, Byzantine emperor (b. 1193)", "html": "1254 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1193)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1193)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1324", "text": "<PERSON><PERSON><PERSON>, Irish suspected witch (b. c. 1300)", "html": "1324 - <a href=\"https://wikipedia.org/wiki/Pet<PERSON><PERSON>_de_Meath\" title=\"<PERSON><PERSON><PERSON> de Meath\"><PERSON><PERSON><PERSON></a>, Irish suspected witch (b. c. 1300)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pet<PERSON><PERSON>_de_Meath\" title=\"<PERSON><PERSON><PERSON> de Meath\"><PERSON><PERSON><PERSON></a>, Irish suspected witch (b. c. 1300)", "links": [{"title": "Petronilla de <PERSON>", "link": "https://wikipedia.org/wiki/Petronilla_de_<PERSON>h"}]}, {"year": "1373", "text": "<PERSON>, Queen of Navarre (b. 1343)", "html": "1373 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre\" class=\"mw-redirect\" title=\"<PERSON>, Queen of Navarre\"><PERSON>, Queen of Navarre</a> (b. 1343)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre\" class=\"mw-redirect\" title=\"<PERSON>, Queen of Navarre\"><PERSON>, Queen of Navarre</a> (b. 1343)", "links": [{"title": "<PERSON>, Queen of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Navarre"}]}, {"year": "1428", "text": "<PERSON>, 4th Earl of Salisbury, English general and politician (b. 1388)", "html": "1428 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 4th Earl <PERSON> Salisbury\"><PERSON>, 4th Earl <PERSON> Salisbury</a>, English general and politician (b. 1388)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Salisbury\" class=\"mw-redirect\" title=\"<PERSON>, 4th Earl <PERSON> Salisbury\"><PERSON>, 4th Earl of Salisbury</a>, English general and politician (b. 1388)", "links": [{"title": "<PERSON>, 4th Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Salisbury"}]}, {"year": "1456", "text": "<PERSON>, 1st Earl of Richmond, father of King <PERSON> of England (b. 1431)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Richmond\" title=\"<PERSON>, 1st Earl of Richmond\"><PERSON>, 1st Earl of Richmond</a>, father of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VII of England\"><PERSON> VII of England</a> (b. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_<PERSON>\" title=\"<PERSON>, 1st Earl of Richmond\"><PERSON>, 1st Earl of Richmond</a>, father of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VII of England\"><PERSON> VII of England</a> (b. 1431)", "links": [{"title": "<PERSON>, 1st Earl of Richmond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Richmond"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish historian and author (b. 1512)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_Zuri<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish historian and author (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_Zurita_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish historian and author (b. 1512)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jer%C3%B3ni<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON>, Italian cardinal and saint (b. 1538)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON>, Prince of Transylvania (b. c. 1563)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (b. c. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (b. c. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andrew_B%C3%A1thory"}]}, {"year": "1600", "text": "<PERSON>, English priest and theologian (b. 1554)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and theologian (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and theologian (b. 1554)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, Peruvian saint (b. 1579)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian saint (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian saint (b. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON>, English astronomer and academic (b. 1582)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English astronomer and academic (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(astronomer)\" title=\"<PERSON> (astronomer)\"><PERSON></a>, English astronomer and academic (b. 1582)", "links": [{"title": "<PERSON> (astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_(astronomer)"}]}, {"year": "1643", "text": "<PERSON>, Swiss astronomer and mathematician (b. 1577)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss astronomer and mathematician (b. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss astronomer and mathematician (b. 1577)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman soldier and politician, 110th Grand Vizier of the Ottoman Empire (b. 1635)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ıl <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman soldier and politician, 110th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman soldier and politician, 110th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1635)", "links": [{"title": "Köprülü Fazıl <PERSON>", "link": "https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1l_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1711", "text": "<PERSON>, German theologian and academic (b. 1666)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (b. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (b. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, English bishop and academic (b. 1710)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and academic (b. 1710)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON><PERSON>, French playwright and activist (b. 1748)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\">O<PERSON><PERSON><PERSON> de Gouges</a>, French playwright and activist (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympe_de_Gouges\" title=\"Olympe de Gouges\"><PERSON><PERSON><PERSON><PERSON> de Gouges</a>, French playwright and activist (b. 1748)", "links": [{"title": "Olympe de Gouges", "link": "https://wikipedia.org/wiki/Olympe_de_Gouges"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, French cardinal and diplomat (b. 1715)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal and diplomat (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal and diplomat (b. 1715)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Irish-born American politician (b. 1821/1822)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born American politician (b. 1821/1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born American politician (b. 1821/1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English philosopher and author (b. 1807)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Greek poet and playwright (b. 1792)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and playwright (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek poet and playwright (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Swiss lawyer and politician, 1st President of the Swiss National Council (b. 1811)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Presidents of the National Council of Switzerland", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland"}]}, {"year": "1891", "text": "<PERSON>, English-Italian philologist and politician (b. 1813)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian philologist and politician (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian philologist and politician (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American philanthropist, founder of the oldest black orphanage in the United States (b. ~1829)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Philanthropy\" title=\"Philanthropy\">philanthropist</a>, founder of the oldest black <a href=\"https://wikipedia.org/wiki/Orphanage\" title=\"Orphanage\">orphanage</a> in the United States (b. ~1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Philanthropy\" title=\"Philanthropy\">philanthropist</a>, founder of the oldest black <a href=\"https://wikipedia.org/wiki/Orphanage\" title=\"Orphanage\">orphanage</a> in the United States (b. ~1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Philanthropy", "link": "https://wikipedia.org/wiki/Philanthropy"}, {"title": "Orphanage", "link": "https://wikipedia.org/wiki/Orphanage"}]}, {"year": "1914", "text": "<PERSON>, Austrian-Polish pharmacist and poet (b. 1887)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish pharmacist and poet (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Polish pharmacist and poet (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_Trakl"}]}, {"year": "1917", "text": "<PERSON>, French author and poet (b. 1846)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Bloy"}]}, {"year": "1918", "text": "<PERSON>, Russian mathematician and physicist (b. 1857)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American entertainer and target shooter (b. 1860)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer and target shooter (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entertainer and target shooter (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>-<PERSON><PERSON>, Czech journalist and author (b. 1860)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%9Bj_%C4%8C<PERSON><PERSON>-Chod\" title=\"<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, Czech journalist and author (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%9Bj_%C4%8<PERSON><PERSON><PERSON>-Chod\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Czech journalist and author (b. 1860)", "links": [{"title": "<PERSON><PERSON>-Chod", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%9Bj_%C4%8Capek-Chod"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Norwegian poet and educator (b. 1883)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and educator (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian poet and educator (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French physician, bacteriologist, and immunologist (b. 1853)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physician, bacteriologist, and immunologist (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physician, bacteriologist, and immunologist (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89mile_Roux"}]}, {"year": "1939", "text": "<PERSON>, French organist and composer (b. 1870)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American art collector and philanthropist, founded the Solomon R. Guggenheim Museum (b. 1861)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim\" title=\"Solomon R. Guggenheim\"><PERSON></a>, American art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum\" title=\"Solomon R. Guggenheim Museum\">Solomon R. Guggenheim Museum</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim\" title=\"Solomon R. Guggenheim\"><PERSON></a>, American art collector and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum\" title=\"Solomon R. Guggenheim Museum\">Solomon R. Guggenheim Museum</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Solomon R. Guggenheim Museum", "link": "https://wikipedia.org/wiki/Solomon_R._Guggenheim_Museum"}]}, {"year": "1954", "text": "<PERSON>, French painter and sculptor (b. 1869)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, French artist, (b. 1883)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Ukrainian-Austrian psychotherapist and author (b. 1897)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Wilhelm_Reich\" title=\"Wilhelm Reich\">Wilhelm <PERSON></a>, Ukrainian-Austrian psychotherapist and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelm_Reich\" title=\"Wilhelm Reich\"><PERSON></a>, Ukrainian-Austrian psychotherapist and author (b. 1897)", "links": [{"title": "Wilhelm <PERSON>", "link": "https://wikipedia.org/wiki/Wilhelm_Reich"}]}, {"year": "1960", "text": "<PERSON>, American actor and director (b. 1901)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (b. 1901)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1962", "text": "<PERSON><PERSON> <PERSON><PERSON>, Dutch sculptor and painter (b. 1895)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch sculptor and painter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch sculptor and painter (b. 1895)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American baseball player (b. 1920)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Turkish footballer (b. 1898)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Zeki_R%C4%B1za_Sporel\" title=\"<PERSON><PERSON><PERSON>porel\"><PERSON><PERSON><PERSON></a>, Turkish footballer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeki_R%C4%B1za_Sporel\" title=\"<PERSON><PERSON><PERSON>ı<PERSON>porel\"><PERSON><PERSON><PERSON></a>, Turkish footballer (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeki_R%C4%B1za_Sporel"}]}, {"year": "1973", "text": "<PERSON>, Swiss-French director and screenwriter (b. 1900)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, Swiss-French director and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gret\" title=\"<PERSON>\"><PERSON></a>, Swiss-French director and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_All%C3%A9gret"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician, 1st Prime Minister of Bangladesh (b. 1925)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}]}, {"year": "1975", "text": "<PERSON>, Bangladeshi captain and politician, 3rd Prime Minister of Bangladesh (b. 1919)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}]}, {"year": "1975", "text": "<PERSON>, Bangladeshi lawyer and politician, President of Bangladesh (b. 1925)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Bangladeshi lawyer and politician (b. 1926)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi lawyer and politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, French noblewoman, Duchess of Ayen and journalist (b. 1898)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Solange_d%27Ayen\" title=\"Sol<PERSON><PERSON> d'Aye<PERSON>\"><PERSON><PERSON><PERSON></a>, French noblewoman, Duchess of Ayen and journalist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solange_d%27Ayen\" title=\"Sol<PERSON><PERSON> d'Ayen\"><PERSON><PERSON><PERSON></a>, French noblewoman, Duchess of Ayen and journalist (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Solange_d%27Ayen"}]}, {"year": "1980", "text": "<PERSON>, American painter and author (b. 1897)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Italian-American conductor and composer (b. 1901)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American conductor and composer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American conductor and composer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian fighter ace (b. 1894)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fighter ace (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fighter ace (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American sportscaster and educator (b. 1945)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and educator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and educator (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Dutch philosopher, theologian, and educator (b. 1916)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and educator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch philosopher, theologian, and educator (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American journalist (b. 1893)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Turkish archaeologist and academic (b. 1929)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish archaeologist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish archaeologist and academic (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Turkish physician and politician, Turkish Minister of Health (b. 1914)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek"}, {"title": "Ministry of Health (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)"}]}, {"year": "1990", "text": "<PERSON>, American actress and singer (b. 1913)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer (b. 1972)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer (b. 1972)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1993", "text": "<PERSON>, Russian physicist and engineer, invented the <PERSON><PERSON> (b. 1895)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_Theremin\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">Theremin</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_Theremin\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">Theremin</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Theremin"}, {"title": "Theremin", "link": "https://wikipedia.org/wiki/Theremin"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Estonian-American boxer (b. 1905)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Palm\"><PERSON><PERSON></a>, Estonian-American boxer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Palm\"><PERSON><PERSON></a>, Estonian-American boxer (b. 1905)", "links": [{"title": "Valter Palm", "link": "https://wikipedia.org/wiki/Valter_Palm"}]}, {"year": "1995", "text": "<PERSON>, Canadian physician (b. 1887)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ahrni\" title=\"<PERSON>\"><PERSON></a>, Canadian physician (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fahrni\" title=\"<PERSON>\"><PERSON></a>, Canadian physician (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gordon_<PERSON>_F<PERSON>rni"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Central African general and politician, 2nd President of the Central African Republic (b. 1921)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Central African general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Central African general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"President of the Central African Republic\">President of the Central African Republic</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9del_<PERSON>"}, {"title": "President of the Central African Republic", "link": "https://wikipedia.org/wiki/President_of_the_Central_African_Republic"}]}, {"year": "1997", "text": "<PERSON>, American carillon player and composer (b. 1927)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(carillonist)\" title=\"<PERSON> (carillonist)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Carillon\" title=\"Carillon\">carillon</a> player and composer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(carillonist)\" title=\"<PERSON> (carillonist)\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Carillon\" title=\"Carillon\">carillon</a> player and composer (b. 1927)", "links": [{"title": "<PERSON> (carillonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(carillonist)"}, {"title": "Carillon", "link": "https://wikipedia.org/wiki/Carillon"}]}, {"year": "1998", "text": "<PERSON>, American author and illustrator, co-created <PERSON> (b. 1915)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created <i><a href=\"https://wikipedia.org/wiki/Batman\" title=\"Batman\">Batman</a></i> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created <i><a href=\"https://wikipedia.org/wiki/Batman\" title=\"Batman\">Batman</a></i> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Scottish actor (b. 1928)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Austrian-English historian and author (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English historian and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English historian and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Scottish singer-songwriter and guitarist (b. 1931)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nnie_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Russian poet and educator (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian poet and educator (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey player (b. 1972)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Sergejs_%C5%BDoltoks\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergejs_%C5%BDoltoks\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey player (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sergejs_%C5%BDoltoks"}]}, {"year": "2006", "text": "<PERSON>, French pianist, composer, and conductor (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, composer, and conductor (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American author (b. 1911)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Ecuadorean footballer (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorean footballer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorean footballer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Belarusian-Russian actor (b. 1962)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian actor (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian actor (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, PIRA volunteer and Irish republican politician (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA</a> volunteer and Irish republican politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA</a> volunteer and Irish republican politician (b. 1945)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}, {"title": "PIRA", "link": "https://wikipedia.org/wiki/PIRA"}]}, {"year": "2007", "text": "<PERSON>, American runner (b. 1979)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, French conductor (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Spanish sociologist, author, and critic (b. 1906)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Spanish sociologist, author, and critic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Spanish sociologist, author, and critic (b. 1906)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "2009", "text": "<PERSON>, Scottish footballer, journalist, and educator (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, journalist, and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, journalist, and educator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American magician and actor (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American magician and actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American composer (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Russian politician and diplomat, 30th Prime Minister of Russia (b. 1938)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician and diplomat, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "2010", "text": "<PERSON>, Canadian bass player (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Estonian lawyer and politician (b. 1948)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Brazilian singer (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Carm%C3%A9lia_Alves\" title=\"Carmélia Alves\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carm%C3%A9lia_Alves\" title=\"Carmélia Alves\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1923)", "links": [{"title": "Carmélia <PERSON>", "link": "https://wikipedia.org/wiki/Carm%C3%A9lia_Alves"}]}, {"year": "2012", "text": "<PERSON>, English cricketer and coach (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American-English cyclist and coach (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist born 1920)\"><PERSON></a>, American-English cyclist and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)\" class=\"mw-redirect\" title=\"<PERSON> (cyclist born 1920)\"><PERSON></a>, American-English cyclist and coach (b. 1920)", "links": [{"title": "<PERSON> (cyclist born 1920)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist_born_1920)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish academic, author, and politician (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/M%C3%BCkerrem_Hi%C3%A7\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish academic, author, and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%BCkerrem_Hi%C3%A7\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish academic, author, and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%BCkerrem_Hi%C3%A7"}]}, {"year": "2012", "text": "<PERSON>, American historian and academic (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist and politician, 18th Governor of Gujarat (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Gujarat\" class=\"mw-redirect\" title=\"List of Governors of Gujarat\">Governor of Gujarat</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Gujarat\" class=\"mw-redirect\" title=\"List of Governors of Gujarat\">Governor of Gujarat</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ra"}, {"title": "List of Governors of Gujarat", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Gujarat"}]}, {"year": "2013", "text": "<PERSON>, American soldier and illustrator (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and illustrator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish footballer and manager (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Blik\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Blik\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and manager (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Blik"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Sri Lankan economist and diplomat (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Gamani_Corea\" title=\"Gamani Corea\"><PERSON><PERSON><PERSON></a>, Sri Lankan economist and diplomat (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gamani_Corea\" title=\"Gamani Corea\"><PERSON><PERSON><PERSON></a>, Sri Lankan economist and diplomat (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gamani_Corea"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian historian and author (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian painter and educator (b. 1911)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and educator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and educator (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian composer and conductor (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and conductor (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American economist and academic (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian actor (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>rap<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>rap<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Iraqi businessman and politician (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi businessman and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi businessman and politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American captain, lawyer, and politician (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English cricketer and sportscaster (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and sportscaster (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, South African novelist and essayist (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African novelist and essayist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African novelist and essayist (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lau<PERSON>_<PERSON>bo"}]}, {"year": "2016", "text": "<PERSON>, American singer (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, American actress and director (b. 1944)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and director (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American producer (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jones\"><PERSON></a>, American producer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}