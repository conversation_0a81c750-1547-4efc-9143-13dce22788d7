{"date": "May 19", "url": "https://wikipedia.org/wiki/May_19", "data": {"Events": [{"year": "639", "text": "<PERSON><PERSON> and his tribesmen assaulted Emperor <PERSON><PERSON> at Jiucheng Palace.", "html": "639 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>esh<PERSON>\"><PERSON><PERSON></a> and his tribesmen assaulted <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON><PERSON></a> at Jiucheng Palace.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>esh<PERSON>\"><PERSON><PERSON></a> and his tribesmen assaulted <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON><PERSON></a> at Jiucheng Palace.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "715", "text": "<PERSON> is elected.", "html": "715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"<PERSON> Gregory II\">Pope <PERSON> II</a> is elected.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory II\"><PERSON> <PERSON> II</a> is elected.", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "934", "text": "The Byzantine Empire reconquers <PERSON><PERSON><PERSON> under the leadership of <PERSON>.", "html": "934 - The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> reconquers <a href=\"https://wikipedia.org/wiki/Melitene\" class=\"mw-redirect\" title=\"<PERSON>ite<PERSON>\"><PERSON><PERSON><PERSON></a> under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> reconquers <a href=\"https://wikipedia.org/wiki/Melitene\" class=\"mw-redirect\" title=\"<PERSON>ite<PERSON>\"><PERSON><PERSON><PERSON></a> under the leadership of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Mel<PERSON>ne", "link": "https://wikipedia.org/wiki/Melitene"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1051", "text": "<PERSON> of France marries the Rus' princess, <PERSON> of Kiev.", "html": "1051 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> I of France\"><PERSON> of France</a> marries the <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan R<PERSON>'\">Rus'</a> princess, <a href=\"https://wikipedia.org/wiki/Anne_of_Kiev\" title=\"<PERSON> of Kiev\"><PERSON> of Kiev</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> marries the <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan Rus'\">Rus'</a> princess, <a href=\"https://wikipedia.org/wiki/Anne_of_Kiev\" title=\"<PERSON> of Kiev\"><PERSON> of Kiev</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "<PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Kievan_Rus%27"}, {"title": "<PERSON> of Kiev", "link": "https://wikipedia.org/wiki/Anne_of_Kiev"}]}, {"year": "1445", "text": "<PERSON> of Castile defeats the Infantes of Aragon at the First Battle of Olmedo.", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> II of Castile\"><PERSON> of Castile</a> defeats the <a href=\"https://wikipedia.org/wiki/Infantes_of_Aragon\" title=\"Infant<PERSON> of Aragon\"><PERSON><PERSON><PERSON> of Aragon</a> at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Olmedo\" title=\"First Battle of Olmedo\">First Battle of Olmedo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> II of Castile\"><PERSON> of Castile</a> defeats the <a href=\"https://wikipedia.org/wiki/Infantes_of_Aragon\" title=\"Infant<PERSON> of Aragon\">In<PERSON><PERSON> of Aragon</a> at the <a href=\"https://wikipedia.org/wiki/First_Battle_of_Olmedo\" title=\"First Battle of Olmedo\">First Battle of Olmedo</a>.", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "<PERSON><PERSON><PERSON> of Aragon", "link": "https://wikipedia.org/wiki/Infantes_of_Aragon"}, {"title": "First Battle of Olmedo", "link": "https://wikipedia.org/wiki/First_Battle_of_Olmedo"}]}, {"year": "1499", "text": "<PERSON> of Aragon is married by proxy to <PERSON>, Prince of Wales. <PERSON> is 13 and <PERSON> is 12.", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">married by proxy</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>. <PERSON> is 13 and <PERSON> is 12.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Proxy_marriage\" title=\"Proxy marriage\">married by proxy</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>. <PERSON> is 13 and <PERSON> is 12.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Proxy marriage", "link": "https://wikipedia.org/wiki/Proxy_marriage"}, {"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales"}]}, {"year": "1535", "text": "French explorer <PERSON> sets sail on his second voyage to North America with three ships, 110 men, and Chief <PERSON><PERSON>'s two sons (whom <PERSON><PERSON> had kidnapped during his first voyage).", "html": "1535 - French explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail on his second voyage to North America with three ships, 110 men, and <a href=\"https://wikipedia.org/wiki/Chief_<PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON></a>'s two sons (whom <PERSON><PERSON> had kidnapped during his first voyage).", "no_year_html": "French explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets sail on his second voyage to North America with three ships, 110 men, and <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON></a>'s two sons (whom <PERSON><PERSON> had kidnapped during his first voyage).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON>"}]}, {"year": "1536", "text": "<PERSON>, the second wife of <PERSON> of England, is beheaded for adultery, treason, and incest.", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a>, is beheaded for adultery, treason, and incest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a>, is beheaded for adultery, treason, and incest.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1542", "text": "The Prome Kingdom falls to the Taungoo Dynasty in present-day Myanmar.", "html": "1542 - The <a href=\"https://wikipedia.org/wiki/Prome_Kingdom\" title=\"Prome Kingdom\">Prome Kingdom</a> falls to the <a href=\"https://wikipedia.org/wiki/Taungoo_Dynasty\" class=\"mw-redirect\" title=\"Taungoo Dynasty\">Taungoo Dynasty</a> in present-day <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prome_Kingdom\" title=\"Prome Kingdom\">Prome Kingdom</a> falls to the <a href=\"https://wikipedia.org/wiki/Taungoo_Dynasty\" class=\"mw-redirect\" title=\"Taungoo Dynasty\">Taungoo Dynasty</a> in present-day <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "links": [{"title": "Prome Kingdom", "link": "https://wikipedia.org/wiki/Prome_Kingdom"}, {"title": "Taungoo Dynasty", "link": "https://wikipedia.org/wiki/Taungoo_Dynasty"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "1643", "text": "Thirty Years' War: French forces under the du<PERSON> <PERSON><PERSON><PERSON><PERSON> decisively defeat Spanish forces at the Battle of Rocroi, marking the symbolic end of Spain as a dominant land power.", "html": "1643 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: French forces under the <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\">duc <PERSON><PERSON></a> decisively defeat Spanish forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rocroi\" title=\"Battle of Rocroi\">Battle of Rocroi</a>, marking the symbolic end of Spain as a dominant land power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: French forces under the <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9\" title=\"<PERSON>, Grand Condé\">duc <PERSON><PERSON><PERSON></a> decisively defeat Spanish forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rocroi\" title=\"Battle of Rocroi\">Battle of Rocroi</a>, marking the symbolic end of Spain as a dominant land power.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "<PERSON>, Grand Condé", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Cond%C3%A9"}, {"title": "Battle of Rocroi", "link": "https://wikipedia.org/wiki/Battle_of_Rocroi"}]}, {"year": "1649", "text": "An Act of Parliament declaring England a Commonwealth is passed by the Long Parliament. England would be a republic for the next eleven years.", "html": "1649 - An <a href=\"https://wikipedia.org/wiki/Act_of_Parliament\" class=\"mw-redirect\" title=\"Act of Parliament\">Act of Parliament</a> declaring England a <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth</a> is passed by the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>. England would be a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> for the next eleven years.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Act_of_Parliament\" class=\"mw-redirect\" title=\"Act of Parliament\">Act of Parliament</a> declaring England a <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth</a> is passed by the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>. England would be a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> for the next eleven years.", "links": [{"title": "Act of Parliament", "link": "https://wikipedia.org/wiki/Act_of_Parliament"}, {"title": "Commonwealth of England", "link": "https://wikipedia.org/wiki/Commonwealth_of_England"}, {"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}]}, {"year": "1655", "text": "The Invasion of Jamaica begins during the Anglo-Spanish War.", "html": "1655 - The <a href=\"https://wikipedia.org/wiki/Invasion_of_Jamaica\" title=\"Invasion of Jamaica\">Invasion of Jamaica</a> begins during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Invasion_of_Jamaica\" title=\"Invasion of Jamaica\">Invasion of Jamaica</a> begins during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "links": [{"title": "Invasion of Jamaica", "link": "https://wikipedia.org/wiki/Invasion_of_Jamaica"}, {"title": "Anglo-Spanish War (1654-60)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)"}]}, {"year": "1743", "text": "<PERSON><PERSON><PERSON> developed the centigrade temperature scale.", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> developed the <a href=\"https://wikipedia.org/wiki/Centigrade_temperature_scale\" class=\"mw-redirect\" title=\"Centigrade temperature scale\">centigrade temperature scale</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> developed the <a href=\"https://wikipedia.org/wiki/Centigrade_temperature_scale\" class=\"mw-redirect\" title=\"Centigrade temperature scale\">centigrade temperature scale</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Centigrade temperature scale", "link": "https://wikipedia.org/wiki/Centigrade_temperature_scale"}]}, {"year": "1749", "text": "King <PERSON> of Great Britain grants the Ohio Company a charter of land around the forks of the Ohio River.", "html": "1749 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> II of Great Britain\"><PERSON> of Great Britain</a> grants the <a href=\"https://wikipedia.org/wiki/Ohio_Company\" title=\"Ohio Company\">Ohio Company</a> a charter of land around the forks of the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> II of Great Britain\"><PERSON> of Great Britain</a> grants the <a href=\"https://wikipedia.org/wiki/Ohio_Company\" title=\"Ohio Company\">Ohio Company</a> a charter of land around the forks of the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "links": [{"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}, {"title": "Ohio Company", "link": "https://wikipedia.org/wiki/Ohio_Company"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}]}, {"year": "1776", "text": "American Revolutionary War: A Continental Army garrison surrenders in the Battle of The Cedars.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> garrison surrenders in the <a href=\"https://wikipedia.org/wiki/Battle_of_The_Cedars\" class=\"mw-redirect\" title=\"Battle of The Cedars\">Battle of The Cedars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> garrison surrenders in the <a href=\"https://wikipedia.org/wiki/Battle_of_The_Cedars\" class=\"mw-redirect\" title=\"Battle of The Cedars\">Battle of The Cedars</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Battle of The Cedars", "link": "https://wikipedia.org/wiki/Battle_of_The_Cedars"}]}, {"year": "1780", "text": "New England's Dark Day, an unusual darkening of the day sky, was observed over the New England states and parts of Canada.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/New_England%27s_Dark_Day\" title=\"New England's Dark Day\">New England's Dark Day</a>, an unusual darkening of the day sky, was observed over the <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> states and parts of <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_England%27s_Dark_Day\" title=\"New England's Dark Day\">New England's Dark Day</a>, an unusual darkening of the day sky, was observed over the <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a> states and parts of <a href=\"https://wikipedia.org/wiki/Canada\" title=\"Canada\">Canada</a>.", "links": [{"title": "New England's Dark Day", "link": "https://wikipedia.org/wiki/New_England%27s_Dark_Day"}, {"title": "New England", "link": "https://wikipedia.org/wiki/New_England"}, {"title": "Canada", "link": "https://wikipedia.org/wiki/Canada"}]}, {"year": "1802", "text": "<PERSON> founds the Legion of Honour.", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Legion_of_Honour\" title=\"Legion of Honour\">Legion of Honour</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds the <a href=\"https://wikipedia.org/wiki/Legion_of_Honour\" title=\"Legion of Honour\">Legion of Honour</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Legion of Honour", "link": "https://wikipedia.org/wiki/Legion_of_Honour"}]}, {"year": "1828", "text": "U.S. President <PERSON> signs the Tariff of 1828 into law, protecting wool manufacturers in the United States.", "html": "1828 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Tariff_of_1828\" class=\"mw-redirect\" title=\"Tariff of 1828\">Tariff of 1828</a> into law, protecting <a href=\"https://wikipedia.org/wiki/Wool\" title=\"Wool\">wool</a> manufacturers in the United States.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Tariff_of_1828\" class=\"mw-redirect\" title=\"Tariff of 1828\">Tariff of 1828</a> into law, protecting <a href=\"https://wikipedia.org/wiki/Wool\" title=\"Wool\">wool</a> manufacturers in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tariff of 1828", "link": "https://wikipedia.org/wiki/Tariff_of_1828"}, {"title": "Wool", "link": "https://wikipedia.org/wiki/Wool"}]}, {"year": "1845", "text": "Captain Sir <PERSON> and his ill-fated Arctic expedition depart from Greenhithe, England.", "html": "1845 - Captain Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Franklin%27s_lost_expedition\" title=\"<PERSON>'s lost expedition\">ill-fated Arctic expedition</a> depart from <a href=\"https://wikipedia.org/wiki/Greenhithe,_Kent\" title=\"Greenhithe, Kent\">Greenhithe</a>, England.", "no_year_html": "Captain Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Franklin%27s_lost_expedition\" title=\"<PERSON>'s lost expedition\">ill-fated Arctic expedition</a> depart from <a href=\"https://wikipedia.org/wiki/Greenhithe,_Kent\" title=\"Greenhithe, Kent\">Greenhithe</a>, England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s lost expedition", "link": "https://wikipedia.org/wiki/Franklin%27s_lost_expedition"}, {"title": "Greenhithe, Kent", "link": "https://wikipedia.org/wiki/Green<PERSON>he,_Kent"}]}, {"year": "1848", "text": "Mexican-American War: Mexico ratifies the Treaty of Guadalupe Hidalgo thus ending the war and ceding California, Nevada, Utah and parts of four other modern-day U.S. states to the United States for US$15 million.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: Mexico ratifies the <a href=\"https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo\" title=\"Treaty of Guadalupe Hidalgo\">Treaty of Guadalupe Hidalgo</a> thus ending the war and ceding <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a> and parts of four other modern-day U.S. states to the United States for US$15 million.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: Mexico ratifies the <a href=\"https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo\" title=\"Treaty of Guadalupe Hidalgo\">Treaty of Guadalupe Hidalgo</a> thus ending the war and ceding <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>, <a href=\"https://wikipedia.org/wiki/Nevada\" title=\"Nevada\">Nevada</a>, <a href=\"https://wikipedia.org/wiki/Utah\" title=\"Utah\">Utah</a> and parts of four other modern-day U.S. states to the United States for US$15 million.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Treaty of Guadalupe Hidalgo", "link": "https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Nevada", "link": "https://wikipedia.org/wiki/Nevada"}, {"title": "Utah", "link": "https://wikipedia.org/wiki/Utah"}]}, {"year": "1883", "text": "Buffalo Bill's first Buffalo Bill's Wild West opens in Omaha, Nebraska.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Buffalo_Bill\" title=\"Buffalo Bill\">Buffalo Bill</a>'s first <i>Buffalo Bill's Wild West</i> opens in Omaha, Nebraska.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buffalo_Bill\" title=\"Buffalo Bill\">Buffalo Bill</a>'s first <i>Buffalo Bill's Wild West</i> opens in Omaha, Nebraska.", "links": [{"title": "Buffalo Bill", "link": "https://wikipedia.org/wiki/Buffalo_Bill"}]}, {"year": "1900", "text": "Great Britain annexes Tonga Island.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a> annexes <a href=\"https://wikipedia.org/wiki/Tonga_Island\" title=\"Tonga Island\">Tonga Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Britain\" title=\"Great Britain\">Great Britain</a> annexes <a href=\"https://wikipedia.org/wiki/Tonga_Island\" title=\"Tonga Island\">Tonga Island</a>.", "links": [{"title": "Great Britain", "link": "https://wikipedia.org/wiki/Great_Britain"}, {"title": "Tonga Island", "link": "https://wikipedia.org/wiki/Tonga_Island"}]}, {"year": "1900", "text": "Second Boer War: British troops relieve Mafeking.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: British troops relieve <a href=\"https://wikipedia.org/wiki/Siege_of_Mafeking\" title=\"Siege of Mafeking\">Mafe<PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: British troops relieve <a href=\"https://wikipedia.org/wiki/Siege_of_Mafeking\" title=\"Siege of Mafeking\">Mafeking</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Siege of Mafeking", "link": "https://wikipedia.org/wiki/Siege_of_Mafe<PERSON>"}]}, {"year": "1911", "text": "Parks Canada, the world's first national park service, is established as the Dominion Parks Branch under the Department of the Interior.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Parks_Canada\" title=\"Parks Canada\">Parks Canada</a>, the world's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a> service, is established as the Dominion Parks Branch under the Department of the Interior.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parks_Canada\" title=\"Parks Canada\">Parks Canada</a>, the world's first <a href=\"https://wikipedia.org/wiki/National_park\" title=\"National park\">national park</a> service, is established as the Dominion Parks Branch under the Department of the Interior.", "links": [{"title": "Parks Canada", "link": "https://wikipedia.org/wiki/Parks_Canada"}, {"title": "National park", "link": "https://wikipedia.org/wiki/National_park"}]}, {"year": "1917", "text": "The Norwegian football club Rosenborg BK is founded.", "html": "1917 - The Norwegian football club <a href=\"https://wikipedia.org/wiki/Rosenborg_BK\" title=\"Rosenborg BK\">Rosenborg BK</a> is founded.", "no_year_html": "The Norwegian football club <a href=\"https://wikipedia.org/wiki/Rosenborg_BK\" title=\"Rosenborg BK\">Rosenborg BK</a> is founded.", "links": [{"title": "Rosenborg BK", "link": "https://wikipedia.org/wiki/Rosenborg_BK"}]}, {"year": "1919", "text": "<PERSON> lands at Samsun on the Anatolian Black Sea coast, initiating what is later termed the Turkish War of Independence.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>ü<PERSON>\"><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Samsun\" title=\"Samsun\"><PERSON><PERSON></a> on the <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolian</a> <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> coast, initiating what is later termed the <a href=\"https://wikipedia.org/wiki/Turkish_War_of_Independence\" title=\"Turkish War of Independence\">Turkish War of Independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>\"><PERSON></a> lands at <a href=\"https://wikipedia.org/wiki/Samsun\" title=\"<PERSON>sun\"><PERSON><PERSON></a> on the <a href=\"https://wikipedia.org/wiki/Anatolia\" title=\"Anatolia\">Anatolian</a> <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> coast, initiating what is later termed the <a href=\"https://wikipedia.org/wiki/Turkish_War_of_Independence\" title=\"Turkish War of Independence\">Turkish War of Independence</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Atat%C3%BCrk"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sun"}, {"title": "Anatolia", "link": "https://wikipedia.org/wiki/Anatolia"}, {"title": "Black Sea", "link": "https://wikipedia.org/wiki/Black_Sea"}, {"title": "Turkish War of Independence", "link": "https://wikipedia.org/wiki/Turkish_War_of_Independence"}]}, {"year": "1921", "text": "The United States Congress passes the Emergency Quota Act establishing national quotas on immigration.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Emergency_Quota_Act\" title=\"Emergency Quota Act\">Emergency Quota Act</a> establishing national quotas on immigration.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Emergency_Quota_Act\" title=\"Emergency Quota Act\">Emergency Quota Act</a> establishing national quotas on immigration.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Emergency Quota Act", "link": "https://wikipedia.org/wiki/Emergency_Quota_Act"}]}, {"year": "1922", "text": "The Young Pioneer Organization of the Soviet Union is established.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Young_Pioneer_Organization_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Young Pioneer Organization of the Soviet Union\">Young Pioneer Organization of the Soviet Union</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Young_Pioneer_Organization_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Young Pioneer Organization of the Soviet Union\">Young Pioneer Organization of the Soviet Union</a> is established.", "links": [{"title": "Young Pioneer Organization of the Soviet Union", "link": "https://wikipedia.org/wiki/Young_Pioneer_Organization_of_the_Soviet_Union"}]}, {"year": "1933", "text": "Finnish cavalry general <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> is appointed the field marshal.", "html": "1933 - Finnish cavalry general <a href=\"https://wikipedia.org/wiki/C._G._<PERSON><PERSON>_<PERSON>heim\" class=\"mw-redirect\" title=\"C. G. E. <PERSON>\">C. G<PERSON> <PERSON><PERSON></a> is appointed the <a href=\"https://wikipedia.org/wiki/Field_marshal\" title=\"Field marshal\">field marshal</a>.", "no_year_html": "Finnish cavalry general <a href=\"https://wikipedia.org/wiki/C._G._E<PERSON>_<PERSON>heim\" class=\"mw-redirect\" title=\"C. G. E. Mann<PERSON>heim\">C. G. <PERSON><PERSON></a> is appointed the <a href=\"https://wikipedia.org/wiki/Field_marshal\" title=\"Field marshal\">field marshal</a>.", "links": [{"title": "C. G. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON>._<PERSON><PERSON>_<PERSON>"}, {"title": "Field marshal", "link": "https://wikipedia.org/wiki/Field_marshal"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON> and the Bulgarian Army engineer a coup d'état and install <PERSON><PERSON> as the new Prime Minister of Bulgaria.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Zveno\" title=\"Zveno\">Zveno</a> and the <a href=\"https://wikipedia.org/wiki/Bulgarian_Army\" class=\"mw-redirect\" title=\"Bulgarian Army\">Bulgarian Army</a> engineer a <a href=\"https://wikipedia.org/wiki/Bulgarian_coup_d%27%C3%A9tat_of_1934\" class=\"mw-redirect\" title=\"Bulgarian coup d'état of 1934\">coup d'état</a> and install <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Georgiev\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the new <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zveno\" title=\"Zveno\">Zveno</a> and the <a href=\"https://wikipedia.org/wiki/Bulgarian_Army\" class=\"mw-redirect\" title=\"Bulgarian Army\">Bulgarian Army</a> engineer a <a href=\"https://wikipedia.org/wiki/Bulgarian_coup_d%27%C3%A9tat_of_1934\" class=\"mw-redirect\" title=\"Bulgarian coup d'état of 1934\">coup d'état</a> and install <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as the new <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bulgaria\">Prime Minister of Bulgaria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zveno"}, {"title": "Bulgarian Army", "link": "https://wikipedia.org/wiki/Bulgarian_Army"}, {"title": "Bulgarian coup d'état of 1934", "link": "https://wikipedia.org/wiki/Bulgarian_coup_d%27%C3%A9tat_of_1934"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}, {"title": "List of Prime Ministers of Bulgaria", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bulgaria"}]}, {"year": "1942", "text": "World War II: In the aftermath of the Battle of the Coral Sea, Task Force 16 heads to Pearl Harbor for repairs.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the aftermath of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a>, <a href=\"https://wikipedia.org/wiki/Task_Force_16\" title=\"Task Force 16\">Task Force 16</a> heads to <a href=\"https://wikipedia.org/wiki/Pearl_Harbor\" title=\"Pearl Harbor\">Pearl Harbor</a> for repairs.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: In the aftermath of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a>, <a href=\"https://wikipedia.org/wiki/Task_Force_16\" title=\"Task Force 16\">Task Force 16</a> heads to <a href=\"https://wikipedia.org/wiki/Pearl_Harbor\" title=\"Pearl Harbor\">Pearl Harbor</a> for repairs.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Coral Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Coral_Sea"}, {"title": "Task Force 16", "link": "https://wikipedia.org/wiki/Task_Force_16"}, {"title": "Pearl Harbor", "link": "https://wikipedia.org/wiki/Pearl_Harbor"}]}, {"year": "1943", "text": "<PERSON>'s second wartime address to the U.S. Congress", "html": "1943 - <PERSON>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_address_to_Congress_(1943)\" title=\"<PERSON>'s address to Congress (1943)\">second wartime address</a> to the U.S. Congress", "no_year_html": "<PERSON>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_address_to_<PERSON>_(1943)\" title=\"<PERSON>'s address to Congress (1943)\">second wartime address</a> to the U.S. Congress", "links": [{"title": "<PERSON>'s address to Congress (1943)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_address_to_<PERSON>_(1943)"}]}, {"year": "1945", "text": "Syrian demonstrators in Damascus are fired upon by French troops injuring twelve, leading to the Levant Crisis.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Syrian\" class=\"mw-redirect\" title=\"Syrian\">Syrian</a> demonstrators in <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a> are fired upon by French troops injuring twelve, leading to the <a href=\"https://wikipedia.org/wiki/Levant_Crisis\" title=\"Levant Crisis\">Levant Crisis</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian\" class=\"mw-redirect\" title=\"Syrian\">Syrian</a> demonstrators in <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a> are fired upon by French troops injuring twelve, leading to the <a href=\"https://wikipedia.org/wiki/Levant_Crisis\" title=\"Levant Crisis\">Levant Crisis</a>.", "links": [{"title": "Syrian", "link": "https://wikipedia.org/wiki/Syrian"}, {"title": "Damascus", "link": "https://wikipedia.org/wiki/Damascus"}, {"title": "Levant Crisis", "link": "https://wikipedia.org/wiki/Levant_Crisis"}]}, {"year": "1950", "text": "A barge containing munitions destined for Pakistan explodes in the harbor at South Amboy, New Jersey, devastating the city.", "html": "1950 - A barge containing munitions destined for <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/South_Amboy_powder_pier_explosion\" title=\"South Amboy powder pier explosion\">explodes</a> in the harbor at <a href=\"https://wikipedia.org/wiki/South_Amboy,_New_Jersey\" title=\"South Amboy, New Jersey\">South Amboy, New Jersey</a>, devastating the city.", "no_year_html": "A barge containing munitions destined for <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> <a href=\"https://wikipedia.org/wiki/South_Amboy_powder_pier_explosion\" title=\"South Amboy powder pier explosion\">explodes</a> in the harbor at <a href=\"https://wikipedia.org/wiki/South_Amboy,_New_Jersey\" title=\"South Amboy, New Jersey\">South Amboy, New Jersey</a>, devastating the city.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "South Amboy powder pier explosion", "link": "https://wikipedia.org/wiki/South_Amboy_powder_pier_explosion"}, {"title": "South Amboy, New Jersey", "link": "https://wikipedia.org/wiki/South_Amboy,_New_Jersey"}]}, {"year": "1950", "text": "Egypt announces that the Suez Canal is closed to Israeli ships and commerce.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> announces that the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> is closed to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> ships and commerce.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> announces that the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> is closed to <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> ships and commerce.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}]}, {"year": "1959", "text": "The North Vietnamese Army establishes Group 559, whose responsibility is to determine how to maintain supply lines to South Vietnam; the resulting route is the Ho Chi Minh trail.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese Army</a> establishes <a href=\"https://wikipedia.org/wiki/Group_559\" title=\"Group 559\">Group 559</a>, whose responsibility is to determine how to maintain <a href=\"https://wikipedia.org/wiki/Supply_line\" class=\"mw-redirect\" title=\"Supply line\">supply lines</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>; the resulting route is the <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_trail\" title=\"Ho Chi Minh trail\">Ho Chi Minh trail</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/People%27s_Army_of_Vietnam\" title=\"People's Army of Vietnam\">North Vietnamese Army</a> establishes <a href=\"https://wikipedia.org/wiki/Group_559\" title=\"Group 559\">Group 559</a>, whose responsibility is to determine how to maintain <a href=\"https://wikipedia.org/wiki/Supply_line\" class=\"mw-redirect\" title=\"Supply line\">supply lines</a> to <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>; the resulting route is the <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_trail\" title=\"Ho Chi Minh trail\">Ho Chi Minh trail</a>.", "links": [{"title": "People's Army of Vietnam", "link": "https://wikipedia.org/wiki/People%27s_Army_of_Vietnam"}, {"title": "Group 559", "link": "https://wikipedia.org/wiki/Group_559"}, {"title": "Supply line", "link": "https://wikipedia.org/wiki/Supply_line"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Ho Chi Minh trail", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_trail"}]}, {"year": "1961", "text": "Venera program: Venera 1 becomes the first man-made object to fly by another planet by passing Venus (the probe had lost contact with Earth a month earlier and did not send back any data).", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: <i><a href=\"https://wikipedia.org/wiki/Venera_1\" title=\"Venera 1\">Venera 1</a></i> becomes the first man-made object to fly by another planet by passing <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> (the probe had lost contact with Earth a month earlier and did not send back any data).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venera_program\" class=\"mw-redirect\" title=\"Venera program\">Venera program</a>: <i><a href=\"https://wikipedia.org/wiki/Venera_1\" title=\"Venera 1\">Venera 1</a></i> becomes the first man-made object to fly by another planet by passing <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> (the probe had lost contact with Earth a month earlier and did not send back any data).", "links": [{"title": "Venera program", "link": "https://wikipedia.org/wiki/Venera_program"}, {"title": "Venera 1", "link": "https://wikipedia.org/wiki/Venera_1"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1961", "text": "At Silchar Railway Station, Assam, 11 Bengalis die when police open fire on protesters demanding state recognition of Bengali language in the Bengali Language Movement.", "html": "1961 - At <a href=\"https://wikipedia.org/wiki/Silchar\" title=\"Silchar\">Silchar</a> Railway Station, <a href=\"https://wikipedia.org/wiki/Assam\" title=\"Assam\">Assam</a>, 11 Bengalis die when police open fire on protesters demanding state recognition of Bengali language in the <a href=\"https://wikipedia.org/wiki/Bengali_Language_Movement#Legacy\" class=\"mw-redirect\" title=\"Bengali Language Movement\">Bengali Language Movement</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Silchar\" title=\"Silchar\">Silchar</a> Railway Station, <a href=\"https://wikipedia.org/wiki/Assam\" title=\"Assam\">Assam</a>, 11 Bengalis die when police open fire on protesters demanding state recognition of Bengali language in the <a href=\"https://wikipedia.org/wiki/Bengali_Language_Movement#Legacy\" class=\"mw-redirect\" title=\"Bengali Language Movement\">Bengali Language Movement</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silchar"}, {"title": "Assam", "link": "https://wikipedia.org/wiki/Assam"}, {"title": "Bengali Language Movement", "link": "https://wikipedia.org/wiki/Bengali_Language_Movement#Legacy"}]}, {"year": "1962", "text": "A birthday salute to U.S. President <PERSON> takes place at Madison Square Garden, New York City. The highlight is <PERSON>'s rendition of \"Happy Birthday\".", "html": "1962 - A birthday salute to U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes place at <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1925)\" title=\"Madison Square Garden (1925)\">Madison Square Garden</a>, New York City. The highlight is <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Monroe\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Happy_Birthday,_Mr._President\" title=\"Happy Birthday, Mr. President\">rendition</a> of \"<a href=\"https://wikipedia.org/wiki/Happy_Birthday_to_You\" title=\"Happy Birthday to You\">Happy Birthday</a>\".", "no_year_html": "A birthday salute to U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes place at <a href=\"https://wikipedia.org/wiki/Madison_Square_Garden_(1925)\" title=\"Madison Square Garden (1925)\">Madison Square Garden</a>, New York City. The highlight is <a href=\"https://wikipedia.org/wiki/<PERSON>_Monroe\" title=\"<PERSON> Monroe\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Happy_Birthday,_Mr._President\" title=\"Happy Birthday, Mr. President\">rendition</a> of \"<a href=\"https://wikipedia.org/wiki/Happy_Birthday_to_You\" title=\"Happy Birthday to You\">Happy Birthday</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Madison Square Garden (1925)", "link": "https://wikipedia.org/wiki/Madison_Square_Garden_(1925)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Happy Birthday, Mr. President", "link": "https://wikipedia.org/wiki/Happy_Birthday,_Mr._President"}, {"title": "Happy Birthday to You", "link": "https://wikipedia.org/wiki/Happy_Birthday_to_You"}]}, {"year": "1963", "text": "The New York Post Sunday Magazine publishes <PERSON>'s Letter from Birmingham Jail.", "html": "1963 - The <i>New York Post Sunday Magazine</i> publishes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>'s <a href=\"https://wikipedia.org/wiki/Letter_from_Birmingham_Jail\" title=\"Letter from Birmingham Jail\">Letter from Birmingham Jail</a>.", "no_year_html": "The <i>New York Post Sunday Magazine</i> publishes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>'s <a href=\"https://wikipedia.org/wiki/Letter_from_Birmingham_Jail\" title=\"Letter from Birmingham Jail\">Letter from Birmingham Jail</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Letter from Birmingham Jail", "link": "https://wikipedia.org/wiki/Letter_from_Birmingham_Jail"}]}, {"year": "1971", "text": "Mars probe program: Mars 2 is launched by the Soviet Union.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Mars_probe_program\" class=\"mw-redirect\" title=\"Mars probe program\">Mars probe program</a>: <i><a href=\"https://wikipedia.org/wiki/Mars_2\" title=\"Mars 2\">Mars 2</a></i> is launched by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mars_probe_program\" class=\"mw-redirect\" title=\"Mars probe program\">Mars probe program</a>: <i><a href=\"https://wikipedia.org/wiki/Mars_2\" title=\"Mars 2\">Mars 2</a></i> is launched by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Mars probe program", "link": "https://wikipedia.org/wiki/Mars_probe_program"}, {"title": "Mars 2", "link": "https://wikipedia.org/wiki/Mars_2"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1986", "text": "The Firearm Owners Protection Act is signed into law by U.S. President <PERSON>.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Firearm_Owners_Protection_Act\" title=\"Firearm Owners Protection Act\">Firearm Owners Protection Act</a> is signed into law by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Firearm_Owners_Protection_Act\" title=\"Firearm Owners Protection Act\">Firearm Owners Protection Act</a> is signed into law by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Firearm Owners Protection Act", "link": "https://wikipedia.org/wiki/Firearm_Owners_Protection_Act"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "Croatians vote for independence in a referendum.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatians</a> vote for independence in a <a href=\"https://wikipedia.org/wiki/1991_Croatian_independence_referendum\" title=\"1991 Croatian independence referendum\">referendum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatians</a> vote for independence in a <a href=\"https://wikipedia.org/wiki/1991_Croatian_independence_referendum\" title=\"1991 Croatian independence referendum\">referendum</a>.", "links": [{"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "1991 Croatian independence referendum", "link": "https://wikipedia.org/wiki/1991_Croatian_independence_referendum"}]}, {"year": "1993", "text": "SAM Colombia Flight 501 crashes on approach to José María Córdova International Airport in Medellín, Colombia, killing 132.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/SAM_Colombia_Flight_501\" title=\"SAM Colombia Flight 501\">SAM Colombia Flight 501</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_C%C3%B3rdova_International_Airport\" title=\"José María Córdova International Airport\">José María Córdova International Airport</a> in <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn\" title=\"Medellín\">Medellín</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 132.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SAM_Colombia_Flight_501\" title=\"SAM Colombia Flight 501\">SAM Colombia Flight 501</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_C%C3%B3rdova_International_Airport\" title=\"José María Córdova International Airport\">José María Córdova International Airport</a> in <a href=\"https://wikipedia.org/wiki/Medell%C3%ADn\" title=\"Medellín\">Medellín</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 132.", "links": [{"title": "SAM Colombia Flight 501", "link": "https://wikipedia.org/wiki/SAM_Colombia_Flight_501"}, {"title": "<PERSON>va International Airport", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_C%C3%B3rdova_International_Airport"}, {"title": "Medellín", "link": "https://wikipedia.org/wiki/Medell%C3%ADn"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1996", "text": "Space Shuttle program: Space Shuttle Endeavour is launched on mission STS-77.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle Endeavour</a></i> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-77\" title=\"STS-77\">STS-77</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle Endeavour</a></i> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-77\" title=\"STS-77\">STS-77</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-77", "link": "https://wikipedia.org/wiki/STS-77"}]}, {"year": "1997", "text": "The Sierra Gorda biosphere, the most ecologically diverse region in Mexico, is established as a result of grassroots efforts.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Sierra_Gorda_biosphere\" class=\"mw-redirect\" title=\"Sierra Gorda biosphere\">Sierra Gorda biosphere</a>, the most <a href=\"https://wikipedia.org/wiki/Ecosystem_diversity\" title=\"Ecosystem diversity\">ecologically diverse</a> region in Mexico, is established as a result of <a href=\"https://wikipedia.org/wiki/Grassroots\" title=\"Grassroots\">grassroots</a> efforts.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sierra_Gorda_biosphere\" class=\"mw-redirect\" title=\"Sierra Gorda biosphere\">Sierra Gorda biosphere</a>, the most <a href=\"https://wikipedia.org/wiki/Ecosystem_diversity\" title=\"Ecosystem diversity\">ecologically diverse</a> region in Mexico, is established as a result of <a href=\"https://wikipedia.org/wiki/Grassroots\" title=\"Grassroots\">grassroots</a> efforts.", "links": [{"title": "Sierra Gorda biosphere", "link": "https://wikipedia.org/wiki/Sierra_Gorda_biosphere"}, {"title": "Ecosystem diversity", "link": "https://wikipedia.org/wiki/Ecosystem_diversity"}, {"title": "Grassroots", "link": "https://wikipedia.org/wiki/Grassroots"}]}, {"year": "2000", "text": "Space Shuttle program: Space Shuttle Atlantis is launched on mission STS-101 to resupply the International Space Station.", "html": "2000 - Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-101\" title=\"STS-101\">STS-101</a> to resupply the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "Space Shuttle program: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on mission <a href=\"https://wikipedia.org/wiki/STS-101\" title=\"STS-101\">STS-101</a> to resupply the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-101", "link": "https://wikipedia.org/wiki/STS-101"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2007", "text": "President of Romania <PERSON><PERSON><PERSON> survives an impeachment referendum and returns to office from suspension.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/Traian_B%C4%83<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> survives an <a href=\"https://wikipedia.org/wiki/2007_Romanian_presidential_impeachment_referendum\" title=\"2007 Romanian presidential impeachment referendum\">impeachment referendum</a> and returns to office from suspension.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/Traian_B%C4%83<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> survives an <a href=\"https://wikipedia.org/wiki/2007_Romanian_presidential_impeachment_referendum\" title=\"2007 Romanian presidential impeachment referendum\">impeachment referendum</a> and returns to office from suspension.", "links": [{"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Traian_B%C4%83sescu"}, {"title": "2007 Romanian presidential impeachment referendum", "link": "https://wikipedia.org/wiki/2007_Romanian_presidential_impeachment_referendum"}]}, {"year": "2010", "text": "The Royal Thai Armed Forces concludes its crackdown on protests by forcing the surrender of United Front for Democracy Against Dictatorship leaders.", "html": "2010 - The <a href=\"https://wikipedia.org/wiki/Royal_Thai_Armed_Forces\" title=\"Royal Thai Armed Forces\">Royal Thai Armed Forces</a> concludes its <a href=\"https://wikipedia.org/wiki/2010_Thai_military_crackdown\" title=\"2010 Thai military crackdown\">crackdown</a> on <a href=\"https://wikipedia.org/wiki/2010_Thai_political_protests\" title=\"2010 Thai political protests\">protests</a> by forcing the surrender of <a href=\"https://wikipedia.org/wiki/United_Front_for_Democracy_Against_Dictatorship\" title=\"United Front for Democracy Against Dictatorship\">United Front for Democracy Against Dictatorship</a> leaders.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Thai_Armed_Forces\" title=\"Royal Thai Armed Forces\">Royal Thai Armed Forces</a> concludes its <a href=\"https://wikipedia.org/wiki/2010_Thai_military_crackdown\" title=\"2010 Thai military crackdown\">crackdown</a> on <a href=\"https://wikipedia.org/wiki/2010_Thai_political_protests\" title=\"2010 Thai political protests\">protests</a> by forcing the surrender of <a href=\"https://wikipedia.org/wiki/United_Front_for_Democracy_Against_Dictatorship\" title=\"United Front for Democracy Against Dictatorship\">United Front for Democracy Against Dictatorship</a> leaders.", "links": [{"title": "Royal Thai Armed Forces", "link": "https://wikipedia.org/wiki/Royal_Thai_Armed_Forces"}, {"title": "2010 Thai military crackdown", "link": "https://wikipedia.org/wiki/2010_Thai_military_crackdown"}, {"title": "2010 Thai political protests", "link": "https://wikipedia.org/wiki/2010_Thai_political_protests"}, {"title": "United Front for Democracy Against Dictatorship", "link": "https://wikipedia.org/wiki/United_Front_for_Democracy_Against_Dictatorship"}]}, {"year": "2012", "text": "Three gas cylinder bombs explode in front of a vocational school in the Italian city of Brindisi, killing one person and injuring five others.", "html": "2012 - Three gas cylinder bombs <a href=\"https://wikipedia.org/wiki/2012_Brindisi_school_bombing\" class=\"mw-redirect\" title=\"2012 Brindisi school bombing\">explode</a> in front of a vocational school in the Italian city of <a href=\"https://wikipedia.org/wiki/Brindisi\" title=\"Brindisi\">Brindisi</a>, killing one person and injuring five others.", "no_year_html": "Three gas cylinder bombs <a href=\"https://wikipedia.org/wiki/2012_Brindisi_school_bombing\" class=\"mw-redirect\" title=\"2012 Brindisi school bombing\">explode</a> in front of a vocational school in the Italian city of <a href=\"https://wikipedia.org/wiki/Brindisi\" title=\"Brindisi\">Brindisi</a>, killing one person and injuring five others.", "links": [{"title": "2012 Brindisi school bombing", "link": "https://wikipedia.org/wiki/2012_Brindisi_school_bombing"}, {"title": "Brindisi", "link": "https://wikipedia.org/wiki/Brindisi"}]}, {"year": "2012", "text": "A car bomb explodes near a military complex in the Syrian city of Deir ez-Zor, killing nine people.", "html": "2012 - A car bomb <a href=\"https://wikipedia.org/wiki/2012_Deir_ez-Zor_bombing\" title=\"2012 Deir ez-Zor bombing\">explodes</a> near a military complex in the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian</a> city of <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor\" title=\"Deir ez-Zor\">Deir ez-Zor</a>, killing nine people.", "no_year_html": "A car bomb <a href=\"https://wikipedia.org/wiki/2012_Deir_ez-Zor_bombing\" title=\"2012 Deir ez-Zor bombing\">explodes</a> near a military complex in the <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian</a> city of <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor\" title=\"Deir ez-Zor\">Deir ez-Zor</a>, killing nine people.", "links": [{"title": "2012 Deir ez-Zor bombing", "link": "https://wikipedia.org/wiki/2012_Deir_ez-Zor_bombing"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "<PERSON>ir ez-Zor", "link": "https://wikipedia.org/wiki/Deir_ez-<PERSON>"}]}, {"year": "2015", "text": "The Refugio oil spill deposited 142,800 U.S. gallons (3,400 barrels) of crude oil onto an area in California considered one of the most biologically diverse coastlines of the west coast.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Refugio_oil_spill\" title=\"Refugio oil spill\">Refugio oil spill</a> deposited 142,800 U.S. gallons (3,400 barrels) of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a> onto an area in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> considered one of the most biologically diverse coastlines of the <a href=\"https://wikipedia.org/wiki/West_Coast_of_the_United_States\" title=\"West Coast of the United States\">west coast</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Refugio_oil_spill\" title=\"Refugio oil spill\">Refugio oil spill</a> deposited 142,800 U.S. gallons (3,400 barrels) of <a href=\"https://wikipedia.org/wiki/Crude_oil\" class=\"mw-redirect\" title=\"Crude oil\">crude oil</a> onto an area in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> considered one of the most biologically diverse coastlines of the <a href=\"https://wikipedia.org/wiki/West_Coast_of_the_United_States\" title=\"West Coast of the United States\">west coast</a>.", "links": [{"title": "Refugio oil spill", "link": "https://wikipedia.org/wiki/Refugio_oil_spill"}, {"title": "Crude oil", "link": "https://wikipedia.org/wiki/Crude_oil"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "West Coast of the United States", "link": "https://wikipedia.org/wiki/West_Coast_of_the_United_States"}]}, {"year": "2016", "text": "EgyptAir Flight 804 crashes into the Mediterranean Sea while traveling from Paris to Cairo, killing all on board.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_804\" title=\"EgyptAir Flight 804\">EgyptAir Flight 804</a> crashes into the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> while traveling from <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, killing all on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_804\" title=\"EgyptAir Flight 804\">EgyptAir Flight 804</a> crashes into the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> while traveling from <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> to <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a>, killing all on board.", "links": [{"title": "EgyptAir Flight 804", "link": "https://wikipedia.org/wiki/EgyptAir_Flight_804"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}]}, {"year": "2018", "text": "The wedding of <PERSON> and <PERSON><PERSON> is held at St George's Chapel, Windsor, with an estimated global audience of 1.9 billion.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON><PERSON>_<PERSON>\" title=\"Wedding of <PERSON> and <PERSON><PERSON>\">wedding of Prince <PERSON> and <PERSON><PERSON></a> is held at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel</a>, <a href=\"https://wikipedia.org/wiki/Windsor,_Berkshire\" title=\"Windsor, Berkshire\">Windsor</a>, with an estimated global audience of 1.9 billion.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON><PERSON>_<PERSON>\" title=\"Wedding of <PERSON> and <PERSON><PERSON>\">wedding of Prince <PERSON> and <PERSON><PERSON></a> is held at <a href=\"https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle\" title=\"St George's Chapel, Windsor Castle\">St George's Chapel</a>, <a href=\"https://wikipedia.org/wiki/Windsor,_Berkshire\" title=\"Windsor, Berkshire\">Windsor</a>, with an estimated global audience of 1.9 billion.", "links": [{"title": "Wedding of <PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Prince_<PERSON>_and_<PERSON><PERSON>_<PERSON>"}, {"title": "St George's Chapel, Windsor Castle", "link": "https://wikipedia.org/wiki/St_George%27s_Chapel,_Windsor_Castle"}, {"title": "Windsor, Berkshire", "link": "https://wikipedia.org/wiki/Windsor,_Berkshire"}]}, {"year": "2024", "text": " A helicopter crash in Iran leaves 8 people dead, including the country's president <PERSON><PERSON><PERSON> & foreign minister <PERSON><PERSON><PERSON>.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Varzaqan_helicopter_crash\" title=\"2024 Varzaqan helicopter crash\"> A helicopter crash</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> leaves 8 people dead, including the country's president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> &amp; foreign minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Varzaqan_helicopter_crash\" title=\"2024 Varzaqan helicopter crash\"> A helicopter crash</a> in <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> leaves 8 people dead, including the country's president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> &amp; foreign minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "2024 Varzaqan helicopter crash", "link": "https://wikipedia.org/wiki/2024_<PERSON>ar<PERSON><PERSON><PERSON>_helicopter_crash"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Births": [{"year": "1400", "text": "<PERSON>, 1st Baron <PERSON>, English soldier and politician (d. 1462)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician (d. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician (d. 1462)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1462", "text": "<PERSON><PERSON><PERSON>, Italian woodcarver, sculptor and architect (d. 1543)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/Baccio_D%27Agnolo\" class=\"mw-redirect\" title=\"Baccio D'Agnolo\"><PERSON><PERSON><PERSON>A<PERSON>lo</a>, Italian <a href=\"https://wikipedia.org/wiki/Woodcarver\" class=\"mw-redirect\" title=\"Woodcarver\">woodcarver</a>, <a href=\"https://wikipedia.org/wiki/Sculpture\" title=\"Sculpture\">sculptor</a> and <a href=\"https://wikipedia.org/wiki/Architect\" title=\"Architect\">architect</a> (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baccio_D%27Agnolo\" class=\"mw-redirect\" title=\"Baccio D'Agnolo\"><PERSON><PERSON><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Woodcarver\" class=\"mw-redirect\" title=\"Woodcarver\">woodcarver</a>, <a href=\"https://wikipedia.org/wiki/Sculpture\" title=\"Sculpture\">sculptor</a> and <a href=\"https://wikipedia.org/wiki/Architect\" title=\"Architect\">architect</a> (d. 1543)", "links": [{"title": "Baccio D'Agnolo", "link": "https://wikipedia.org/wiki/Baccio_D%27A<PERSON><PERSON>"}, {"title": "Woodcarver", "link": "https://wikipedia.org/wiki/Woodcarver"}, {"title": "Sculpture", "link": "https://wikipedia.org/wiki/Sculpture"}, {"title": "Architect", "link": "https://wikipedia.org/wiki/Architect"}]}, {"year": "1476 (or 1474)", "text": "<PERSON> of Moscow, Grand Duchess consort of Lithuania and Queen consort of Poland (d. 1513)", "html": "1476 (or 1474) - <a href=\"https://wikipedia.org/wiki/1476\" title=\"1476\">1476</a> (or <a href=\"https://wikipedia.org/wiki/1474\" title=\"1474\">1474</a>) - <a href=\"https://wikipedia.org/wiki/Helena_of_Moscow\" title=\"<PERSON> of Moscow\"><PERSON> of Moscow</a>, Grand Duchess consort of Lithuania and Queen consort of Poland (d. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1476\" title=\"1476\">1476</a> (or <a href=\"https://wikipedia.org/wiki/1474\" title=\"1474\">1474</a>) - <a href=\"https://wikipedia.org/wiki/Helena_of_Moscow\" title=\"<PERSON> of Moscow\"><PERSON> of Moscow</a>, Grand Duchess consort of Lithuania and Queen consort of Poland (d. 1513)", "links": [{"title": "1476", "link": "https://wikipedia.org/wiki/1476"}, {"title": "1474", "link": "https://wikipedia.org/wiki/1474"}, {"title": "Helena of Moscow", "link": "https://wikipedia.org/wiki/Helena_of_Moscow"}]}, {"year": "1593", "text": "<PERSON>, French painter (d. 1670)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, German organist and composer (d. 1667)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, 3rd Earl of Portland, English soldier and noble (d. 1665)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Portland\" title=\"<PERSON>, 3rd Earl of Portland\"><PERSON>, 3rd Earl of Portland</a>, English soldier and noble (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Portland\" title=\"<PERSON>, 3rd Earl of Portland\"><PERSON>, 3rd Earl of Portland</a>, English soldier and noble (d. 1665)", "links": [{"title": "<PERSON>, 3rd Earl of Portland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Portland"}]}, {"year": "1700", "text": "<PERSON>, 1st Count of Sierra Gorda, Spanish sergeant and politician (d. 1770)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Escand%C3%B3n,_1st_Count_of_Sierra_Gorda\" title=\"<PERSON>, 1st Count of Sierra Gorda\"><PERSON>, 1st Count of Sierra Gorda</a>, Spanish sergeant and politician (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Escand%C3%B3n,_1st_Count_of_Sierra_Gorda\" title=\"<PERSON>, 1st Count of Sierra Gorda\"><PERSON>, 1st Count of Sierra Gorda</a>, Spanish sergeant and politician (d. 1770)", "links": [{"title": "<PERSON>, 1st Count of Sierra Gorda", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_Escand%C3%B3n,_1st_Count_of_Sierra_Gorda"}]}, {"year": "1724", "text": "<PERSON>, 3rd Earl of Bristol, English admiral and politician, Chief Secretary for Ireland (d. 1779)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bristol\" title=\"<PERSON>, 3rd Earl of Bristol\"><PERSON>, 3rd Earl of Bristol</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (d. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bristol\" title=\"<PERSON>, 3rd Earl of Bristol\"><PERSON>, 3rd Earl of Bristol</a>, English admiral and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (d. 1779)", "links": [{"title": "<PERSON>, 3rd Earl of Bristol", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bristol"}, {"title": "Chief Secretary for Ireland", "link": "https://wikipedia.org/wiki/Chief_Secretary_for_Ireland"}]}, {"year": "1744", "text": "<PERSON> of Mecklenburg-Strelitz, German-born Queen to <PERSON> of the United Kingdom (d. 1818)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>, German-born Queen to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> III of the United Kingdom\"><PERSON> of the United Kingdom</a> (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>, German-born Queen to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> III of the United Kingdom\"><PERSON> of the United Kingdom</a> (d. 1818)", "links": [{"title": "<PERSON> of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz"}, {"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom"}]}, {"year": "1762", "text": "<PERSON>, German philosopher and academic (d. 1814)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and academic (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, English chemist and mineralogist (d. 1854)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mineralogist (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and mineralogist (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, American businessman and philanthropist (d. 1873)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, French academic and politician, French Minister of Foreign Affairs (d. 1896)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (France)\">French Minister of Foreign Affairs</a> (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (France)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(France)"}]}, {"year": "1832", "text": "<PERSON>, Jr., English politician, brewer and cricketer (d. 1886)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_junior\" class=\"mw-redirect\" title=\"<PERSON> junior\"><PERSON>, Jr.</a>, English politician, brewer and cricketer (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_junior\" class=\"mw-redirect\" title=\"<PERSON> junior\"><PERSON>, Jr.</a>, English politician, brewer and cricketer (d. 1886)", "links": [{"title": "<PERSON> junior", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_junior"}]}, {"year": "1857", "text": "<PERSON>, American biochemist and pharmacologist (d. 1938)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and pharmacologist (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Australian soprano and actress (d. 1931)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soprano and actress (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soprano and actress (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American painter, sculptor, and author (d. 1963)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and author (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and author (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, English cricketer and soldier (d. 1955)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and soldier (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Canadian sculptor and painter (d. 1953)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Lalibert%C3%A9"}]}, {"year": "1879", "text": "<PERSON>, Viscountess <PERSON>, American-English politician (d. 1964)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, American-English politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, American-English politician (d. 1964)", "links": [{"title": "<PERSON>, Viscountess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1880", "text": "<PERSON>, English architect and educator, designed the Manchester Opera House (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Manchester_Opera_House\" title=\"Manchester Opera House\">Manchester Opera House</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, English architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Manchester_Opera_House\" title=\"Manchester Opera House\">Manchester Opera House</a> (d. 1964)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}, {"title": "Manchester Opera House", "link": "https://wikipedia.org/wiki/Manchester_Opera_House"}]}, {"year": "1881", "text": "<PERSON> (official birthday), Turkish field marshal and statesman, 1st President of Turkey (d. 1938)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>\"><PERSON></a> (official birthday), Turkish field marshal and statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>at%C3%BCrk\" title=\"<PERSON>\"><PERSON></a> (official birthday), Turkish field marshal and statesman, 1st <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Atat%C3%BCrk"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1884", "text": "<PERSON>, American runner (d. 1953)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American lawyer and judge, 58th United States Attorney General (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 58th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 58th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1887", "text": "<PERSON>, Romanian soldier and sculptor (d. 1983)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian soldier and sculptor (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian soldier and sculptor (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_J<PERSON>a"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Vietnamese poet and author (d. 1939)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/T%E1%BA%A3n_%C4%90%C3%A0\" title=\"Tản <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese poet and author (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%E1%BA%A3n_%C4%90%C3%A0\" title=\"Tản <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese poet and author (d. 1939)", "links": [{"title": "<PERSON>ả<PERSON>", "link": "https://wikipedia.org/wiki/T%E1%BA%A3n_%C4%90%C3%A0"}]}, {"year": "1889", "text": "<PERSON>, American archer (d. 1963)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, German-American illustrator (d. 1962)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American illustrator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American illustrator (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Vietnamese politician, 1st President of Vietnam (d. 1969)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President of Vietnam</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Vietnam\" title=\"President of Vietnam\">President of Vietnam</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Vietnam", "link": "https://wikipedia.org/wiki/President_of_Vietnam"}]}, {"year": "1891", "text": "<PERSON>, German captain and pilot (d. 1916)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Romanian author, poet, and journalist (d. 1950)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author, poet, and journalist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian author, poet, and journalist (d. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American lieutenant and pilot, Medal of Honor recipient (d. 1918)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1898", "text": "<PERSON>, Italian philosopher and painter (d. 1974)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and painter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and painter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Evola"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Romanian journalist, linguist, and politician (d. 1955)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Lothar_R%C4%83d%C4%83ceanu\" title=\"Lothar Rădăceanu\"><PERSON><PERSON></a>, Romanian journalist, linguist, and politician (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lothar_R%C4%83d%C4%83ceanu\" title=\"<PERSON>har Rădăceanu\"><PERSON><PERSON></a>, Romanian journalist, linguist, and politician (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lothar_R%C4%83d%C4%83ceanu"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Canadian pianist and educator (d. 1997)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian pianist and educator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian pianist and educator (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American scientist (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Swedish modern pentathlete and épée fencer (d. 1993)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish modern pentathlete and épée fencer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish modern pentathlete and épée fencer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American shot putter and actor (d. 2007)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Indian author, poet, and playwright (d. 1956)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and playwright (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author, poet, and playwright (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>adhyay"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, American author (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON>am_<PERSON>l"}]}, {"year": "1908", "text": "<PERSON>, Canadian sprinter (d. 1982)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, Canadian sprinter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, Canadian sprinter (d. 1982)", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter)"}]}, {"year": "1909", "text": "<PERSON>, English banker and humanitarian (d. 2015)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and humanitarian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English banker and humanitarian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, South African cricketer (d. 1983)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, 6th President of India (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1914", "text": "<PERSON>, Austrian-English biologist and academic, Nobel Prize laureate (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1914", "text": "<PERSON>, Canadian ice hockey player (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American photographer and journalist (d. 1975)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, English actress (d. 2014)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Dutch-American physicist, historian, and academic (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist, historian, and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist, historian, and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian-American saxophonist, clarinet player, and bandleader (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American saxophonist, clarinet player, and bandleader (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American saxophonist, clarinet player, and bandleader (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Italian-Slovenian soldier and politician, 25th Prime Minister of Yugoslavia (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Mitja_Ribi%C4%8Di%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Slovenian soldier and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mit<PERSON>_<PERSON>ibi%C4%8Di%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Slovenian soldier and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mitja_Ribi%C4%8Di%C4%8D"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1920", "text": "<PERSON>, Dutch psychiatrist known for rescuing Jews during World War II (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch psychiatrist known for rescuing Jews during World War II (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch psychiatrist known for rescuing Jews during World War II (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English lieutenant and pilot (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American colonel and pilot (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American colonel and pilot (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(pilot)\" title=\"<PERSON> (pilot)\"><PERSON></a>, American colonel and pilot (d. 1991)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(pilot)"}]}, {"year": "1921", "text": "<PERSON>, French actor, director, and screenwriter (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lin\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Daniel_G%C3%A9lin"}]}, {"year": "1921", "text": "<PERSON>, American activist (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Dutch historian and author (d. 1999)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_van_het_<PERSON>\" title=\"<PERSON><PERSON> van het <PERSON>\"><PERSON><PERSON> van <PERSON></a>, Dutch historian and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_van_het_<PERSON>\" title=\"<PERSON><PERSON> van het <PERSON>\"><PERSON><PERSON> van he<PERSON></a>, Dutch historian and author (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_he<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian hobby shop proprietor (d. 1992)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hobby shop proprietor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hobby shop proprietor (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English composer and songwriter (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and songwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Cambodian general and politician, 29th Prime Minister of Cambodia (d. 1998)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cambodian general and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cambodian general and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cambodia\" title=\"Prime Minister of Cambodia\">Prime Minister of Cambodia</a> (d. 1998)", "links": [{"title": "Pol Po<PERSON>", "link": "https://wikipedia.org/wiki/Pol_Pot"}, {"title": "Prime Minister of Cambodia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Cambodia"}]}, {"year": "1925", "text": "<PERSON>, American minister and activist (d. 1965)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Malcolm X\"><PERSON></a>, American minister and activist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English engineer and academic (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, German director and screenwriter (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, French-American mathematician, author and academic (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American mathematician, author and academic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American mathematician, author and academic (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English engineer and businessman, founded Lotus Cars (d. 1982)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Lotus_Cars\" title=\"Lotus Cars\">Lotus Cars</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Lotus_Cars\" title=\"Lotus Cars\">Lotus Cars</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lotus Cars", "link": "https://wikipedia.org/wiki/Lotus_Cars"}]}, {"year": "1928", "text": "<PERSON>, English air marshal (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal (d. 2013)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "1928", "text": "<PERSON>, American baseball player and coach (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German-American violinist and composer (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American violinist and composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian painter (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American politician (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American historian and author (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Genovese"}]}, {"year": "1930", "text": "<PERSON>, American playwright and director (d. 1965)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and director (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and director (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English race car driver (d. 1967)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 1967)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1931", "text": "<PERSON>, English actor, screenwriter and songwriter (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, screenwriter and songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English singer (d. 1966)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cogan\" title=\"<PERSON> Cogan\"><PERSON></a>, English singer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cogan\"><PERSON></a>, English singer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alma_<PERSON>gan"}]}, {"year": "1932", "text": "<PERSON>, American economist and author (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American basketball player and coach (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Mexican intellectual and journalist", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican intellectual and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican intellectual and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Maltese physician, author, and academic (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician, author, and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese physician, author, and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Indian author and poet", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bond\"><PERSON><PERSON><PERSON></a>, Indian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bond\"><PERSON><PERSON><PERSON></a>, Indian author and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American journalist and author (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American journalist and television personality", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_personality)\" title=\"<PERSON> (TV personality)\"><PERSON></a>, American journalist and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(TV_personality)\" title=\"<PERSON> (TV personality)\"><PERSON></a>, American journalist and television personality", "links": [{"title": "<PERSON> (TV personality)", "link": "https://wikipedia.org/wiki/<PERSON>_(TV_personality)"}]}, {"year": "1937", "text": "<PERSON>, English wrestler (d. 2004)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, East Timorese politician (d. 1989)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON>_Costa_Amaral\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, East Timorese politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON>_Costa_Amaral\" title=\"<PERSON><PERSON><PERSON>al\"><PERSON><PERSON><PERSON> <PERSON></a>, East Timorese politician (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mois%C3%A9s_<PERSON>_<PERSON>_Amaral"}]}, {"year": "1938", "text": "<PERSON><PERSON>, English musician (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Ukrainian long jumper and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian long jumper and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian long jumper and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Italian sprinter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Be<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Be<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Livio_Berruti"}]}, {"year": "1939", "text": "<PERSON>, English actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Hong Kong-American actress and makeup artist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-American actress and makeup artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong-American actress and makeup artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Latvian javelin thrower and coach (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/J%C4%81nis_L%C5%ABsis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian javelin thrower and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C4%81nis_L%C5%ABsis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian javelin thrower and coach (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C4%81nis_L%C5%ABsis"}]}, {"year": "1939", "text": "<PERSON>, American pilot, and astronaut (d. 1986)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, and astronaut (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, and astronaut (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Dutch cyclist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American country/pop singer-songwriter (d. 2002)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country/pop singer-songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country/pop singer-songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American director, producer, and screenwriter (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1941", "text": "Igor Judge, Baron Judge, Maltese-English lawyer and judge, Lord Chief Justice of England and Wales (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Igor_Judge,_Baron_Judge\" title=\"Igor Judge, Baron Judge\">Igor Judge, Baron Judge</a>, Maltese-English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igor_Judge,_Baron_Judge\" title=\"Igor Judge, Baron Judge\">Igor Judge, Baron Judge</a>, Maltese-English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (d. 2023)", "links": [{"title": "<PERSON> Judge, Baron Judge", "link": "https://wikipedia.org/wiki/Igor_Judge,_Baron_Judge"}, {"title": "Lord Chief Justice of England and Wales", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales"}]}, {"year": "1942", "text": "<PERSON>, American computer scientist, founded Digital Research Inc. (d. 1994)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Digital_Research\" title=\"Digital Research\">Digital Research Inc.</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded <a href=\"https://wikipedia.org/wiki/Digital_Research\" title=\"Digital Research\">Digital Research Inc.</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Digital Research", "link": "https://wikipedia.org/wiki/Digital_Research"}]}, {"year": "1942", "text": "<PERSON>-Silk, English television host and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and politician", "links": [{"title": "<PERSON>-Silk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer and manager (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American author, publisher, and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ades\" title=\"Shirrel Rhoades\"><PERSON><PERSON></a>, American author, publisher, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Shirrel Rhoades\"><PERSON><PERSON></a>, American author, publisher, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English-American actor (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Belgian activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, Belgian activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON>\"><PERSON></a>, Belgian activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8vre"}]}, {"year": "1946", "text": "<PERSON>, Italian actor and director", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON> the <PERSON>, French-American wrestler and actor (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_the_Giant\" title=\"<PERSON> the Giant\"><PERSON> the Giant</a>, French-American wrestler and actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_the_Giant\" title=\"<PERSON> the Giant\"><PERSON> the <PERSON></a>, French-American wrestler and actor (d. 1993)", "links": [{"title": "<PERSON> the Giant", "link": "https://wikipedia.org/wiki/Andr%C3%A9_the_Giant"}]}, {"year": "1947", "text": "<PERSON>, Irish singer-songwriter, guitarist, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian pianist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Jamaican-American singer-songwriter, producer, and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and bass player (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Hill\"><PERSON></a>, American singer-songwriter and bass player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Hill\"><PERSON></a>, American singer-songwriter and bass player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Baron <PERSON> of Kings Heath, English politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Kings_Heath\" title=\"<PERSON>, Baron <PERSON> of Kings Heath\"><PERSON>, Baron <PERSON> of Kings Heath</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kings_Heath\" title=\"<PERSON>, Baron <PERSON> of Kings Heath\"><PERSON>, Baron <PERSON> of Kings Heath</a>, English politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Kings Heath", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Kings_Heath"}]}, {"year": "1949", "text": "<PERSON>, American football player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Polish pole vaulter (d. 1998)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Tad<PERSON>z_%C5%9Alusarski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pole vaulter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%9Alusarski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pole vaulter (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_%C5%9Alusarski"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter (d. 2001)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ramon<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American wrestler (d. 2018)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English runner", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Spedding"}]}, {"year": "1952", "text": "<PERSON>, Dutch footballer, coach, and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, <PERSON>, Scottish lawyer and judge", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Scottish lawyer and judge", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian finswimmer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian finswimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian finswimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Romanian footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian footballer and manager", "links": [{"title": "Flor<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lor<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English actress, singer, director, and screenwriter (d. 2016)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Victoria_Wood\" title=\"Victoria Wood\"><PERSON></a>, English actress, singer, director, and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Wood\" title=\"Victoria Wood\"><PERSON></a>, English actress, singer, director, and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Wood"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Swedish director, writer and physician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director, writer and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish director, writer and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese voice actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/H%C5%8Dch%C5%AB_%C5%8Ctsuka\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C5%8Dch%C5%AB_%C5%8Ctsuka\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C5%8Dch%C5%AB_%C5%8Ctsuka"}]}, {"year": "1954", "text": "<PERSON>, Australian-New Zealand drummer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian-American computer scientist, created Java", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American computer scientist, created <a href=\"https://wikipedia.org/wiki/Java_(programming_language)\" title=\"Java (programming language)\">Java</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American computer scientist, created <a href=\"https://wikipedia.org/wiki/Java_(programming_language)\" title=\"Java (programming language)\">Java</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Java (programming language)", "link": "https://wikipedia.org/wiki/Java_(programming_language)"}]}, {"year": "1956", "text": "<PERSON>, English philosopher and politician, Chancellor of the Duchy of Lancaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1956", "text": "<PERSON><PERSON>, English keyboard player, songwriter, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English keyboard player, songwriter, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American basketball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Nigerian-Australian singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Moldovan politician (d. 2021)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian rugby league player and sportscaster (d. 1997)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and sportscaster (d. 1997)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1964", "text": "<PERSON>, South Korean-American football player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(placekicker)\" title=\"<PERSON> (placekicker)\"><PERSON></a>, South Korean-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(placekicker)\" title=\"<PERSON> (placekicker)\"><PERSON></a>, South Korean-American football player", "links": [{"title": "<PERSON> (placekicker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(placekicker)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Slovak tennis player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8D%C3%AD%C5%99\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8D%C3%AD%C5%99\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miloslav_Me%C4%8D%C3%AD%C5%99"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Flanagan\"><PERSON><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maile_F<PERSON>gan"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/Marc_Bureau_(ice_hockey)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American author and educator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Italian_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Italian singer)\"><PERSON></a>, Italian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Italian_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Italian singer)\"><PERSON></a>, Italian singer", "links": [{"title": "<PERSON> (Italian singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Italian_singer)"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Irish-born English actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor and bass player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Welsh drummer (d. 2010)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh drummer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cable"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, South Korean golfer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South Korean golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South Korean golfer", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Estonian chess player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Regina Narva\"><PERSON></a>, Estonian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Regina Narva\"><PERSON></a>, Estonian chess player", "links": [{"title": "<PERSON> Narva", "link": "https://wikipedia.org/wiki/Regina_Narva"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Indonesian actress, singer and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress, singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actress, singer and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Estonian biologist, biochemist, and educator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian biologist, biochemist, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian biologist, biochemist, and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andres_Salumets"}]}, {"year": "1972", "text": "<PERSON>, Swedish singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian actress, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Scottish race car driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French soprano", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Pretinha\" title=\"Pretin<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pretinha\" title=\"Pretin<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pretinha"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/London_Fletcher\" title=\"<PERSON> Fletcher\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/London_Fletcher\" title=\"<PERSON> Fletcher\"><PERSON></a>, American football player", "links": [{"title": "London Fletcher", "link": "https://wikipedia.org/wiki/London_Fletcher"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Swedish singer-songwriter, guitarist, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Spanish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manuel_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Dutch singer and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Wout<PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer and guitarist", "links": [{"title": "W<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> In<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> In<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1977", "text": "<PERSON>, Uruguayan singer-songwriter and actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bus\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Uruguayan footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Forl%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Forl%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Forl%C3%A1n"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American country singer, songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country singer, songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Shoot<PERSON>\"><PERSON><PERSON></a>, American country singer, songwriter", "links": [{"title": "Shooter <PERSON>", "link": "https://wikipedia.org/wiki/Shooter_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American rapper", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Got<PERSON>\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gotti\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, German sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Dutch swimmer", "links": [{"title": "Klaas<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/P%C3%A5l_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>l Steffen <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A5l_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>l Steffen <PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A5l_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American comedian", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Dutch professional wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Malakai_Black\" title=\"Malakai Black\"><PERSON><PERSON><PERSON></a>, Dutch professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malakai_Black\" title=\"Malaka<PERSON> Black\"><PERSON><PERSON><PERSON></a>, Dutch professional wrestler", "links": [{"title": "Malaka<PERSON>", "link": "https://wikipedia.org/wiki/Malaka<PERSON>_Black"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and producer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian soccer player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" class=\"mw-redirect\" title=\"<PERSON> (footballer)\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1987", "text": "<PERSON>, Argentinian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Pruitt"}]}, {"year": "1992", "text": "<PERSON>, Italian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, New Zealand-Tongan rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Tongan rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American electronic music producer and DJ", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American electronic music producer and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American electronic music producer and DJ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marshmello"}]}, {"year": "1992", "text": "<PERSON>, English singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1992", "text": "<PERSON>, British tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Mexican footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Guzm%C3%A1n"}]}, {"year": "1995", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American dancer, singer, actress, and YouTube personality", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer, singer, actress, and YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer, singer, actress, and YouTube personality", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "804", "text": "<PERSON><PERSON><PERSON>, English monk and scholar (b. 735)", "html": "804 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English monk and scholar (b. 735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English monk and scholar (b. 735)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>in"}]}, {"year": "956", "text": "<PERSON>, archbishop of Trier", "html": "956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON> (archbishop of Trier)\"><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier\" title=\"Roman Catholic Diocese of Trier\">Trier</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Trier)\" title=\"<PERSON> (archbishop of Trier)\"><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier\" title=\"Roman Catholic Diocese of Trier\">Trier</a>", "links": [{"title": "<PERSON> (archbishop of Trier)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Trier)"}, {"title": "Roman Catholic Diocese of Trier", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Trier"}]}, {"year": "988", "text": "<PERSON><PERSON><PERSON>, English archbishop and saint (b. 909)", "html": "988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archbishop and saint (b. 909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archbishop and saint (b. 909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1102", "text": "<PERSON>, Count of Blois (b. 1045)", "html": "1102 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Blois\" title=\"<PERSON>, Count of Blois\"><PERSON>, Count of Blois</a> (b. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Blois\" title=\"<PERSON>, Count of Blois\"><PERSON>, Count of Blois</a> (b. 1045)", "links": [{"title": "<PERSON>, Count of Blois", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1125", "text": "<PERSON>, Grand Duke of Kyiv", "html": "1125 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_Monomakh\" title=\"Vladimir II Monomakh\">Vladimir II Monomakh</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duke\" class=\"mw-redirect\" title=\"Grand Duke\">Grand Duke</a> of <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_Monomakh\" title=\"Vladimir II Monomakh\"><PERSON> II Monomakh</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duke\" class=\"mw-redirect\" title=\"Grand Duke\">Grand Duke</a> of <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>", "links": [{"title": "<PERSON> II Monomakh", "link": "https://wikipedia.org/wiki/<PERSON>_II_Mon<PERSON>kh"}, {"title": "Grand Duke", "link": "https://wikipedia.org/wiki/Grand_Duke"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}]}, {"year": "1164", "text": "Saint <PERSON><PERSON><PERSON><PERSON>, Egyptian saint and martyr", "html": "1164 - <a href=\"https://wikipedia.org/wiki/Saint_Bashnouna\" class=\"mw-redirect\" title=\"Saint Bashnouna\">Saint Bashnouna</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a> and <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Bashnouna\" class=\"mw-redirect\" title=\"Saint Bashnouna\">Saint Bashnouna</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a> and <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a>", "links": [{"title": "Saint <PERSON>", "link": "https://wikipedia.org/wiki/Saint_Bashnouna"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint"}, {"title": "Martyr", "link": "https://wikipedia.org/wiki/Martyr"}]}, {"year": "1218", "text": "<PERSON>, Holy Roman Emperor", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> IV, Holy Roman Emperor</a>", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1296", "text": "<PERSON> <PERSON><PERSON><PERSON> (b. 1215)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/Pope_Ce<PERSON>tine_V\" title=\"Pope Celestine V\"><PERSON> <PERSON><PERSON><PERSON> V</a> (b. 1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Ce<PERSON><PERSON>_V\" title=\"Pope Celestine V\"><PERSON> <PERSON><PERSON><PERSON> V</a> (b. 1215)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1303", "text": "<PERSON> <PERSON><PERSON> of Kermartin, French canon lawyer (b. 1253)", "html": "1303 - <PERSON> <a href=\"https://wikipedia.org/wiki/I<PERSON>_of_Kermartin\" title=\"<PERSON><PERSON> of Kermartin\"><PERSON><PERSON> of Kermartin</a>, French canon lawyer (b. 1253)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/I<PERSON>_of_Kermartin\" title=\"<PERSON><PERSON> of Kermartin\"><PERSON><PERSON> of Kermartin</a>, French canon lawyer (b. 1253)", "links": [{"title": "<PERSON><PERSON> of Kermartin", "link": "https://wikipedia.org/wiki/Ivo_of_Kermartin"}]}, {"year": "1319", "text": "<PERSON>, Count of Évreux (b. 1276)", "html": "1319 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux\" title=\"<PERSON>, Count of Évreux\"><PERSON>, Count of Évreux</a> (b. 1276)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux\" title=\"<PERSON>, Count of Évreux\"><PERSON>, Count of Évreux</a> (b. 1276)", "links": [{"title": "<PERSON>, Count of Évreux", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_%C3%89vreux"}]}, {"year": "1389", "text": "<PERSON>, Grand Prince of Muscovy (b. 1350)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of Muscovy (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Prince of Muscovy (b. 1350)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1396", "text": "<PERSON> of Aragon (b. 1350)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (b. 1350)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1526", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1464)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ka<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1464)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ka<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1464)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1531", "text": "<PERSON>, Polish archbishop and diplomat (b. 1456)", "html": "1531 - <a href=\"https://wikipedia.org/wiki/Jan_%C5%81aski_(1456%E2%80%931531)\" title=\"<PERSON> (1456-1531)\"><PERSON></a>, Polish archbishop and diplomat (b. 1456)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_%C5%81aski_(1456%E2%80%931531)\" title=\"<PERSON> (1456-1531)\"><PERSON></a>, Polish archbishop and diplomat (b. 1456)", "links": [{"title": "<PERSON> (1456-1531)", "link": "https://wikipedia.org/wiki/Jan_%C5%81aski_(1456%E2%80%931531)"}]}, {"year": "1536", "text": "<PERSON>, Queen of England (1533-1536); second wife of <PERSON> of England", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of England (1533-1536); second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Queen of England (1533-1536); second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\">Henry VIII of England</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1601", "text": "<PERSON><PERSON><PERSON>, Italian composer (b. 1528)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/Costanzo_Porta\" title=\"Costanzo Porta\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costanzo_Porta\" title=\"Costanzo Porta\"><PERSON><PERSON><PERSON></a>, Italian composer (b. 1528)", "links": [{"title": "Costanzo Porta", "link": "https://wikipedia.org/wiki/Costanzo_Porta"}]}, {"year": "1609", "text": "<PERSON>, 5th Marquis of Cañete (b. 1535)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Hurt<PERSON>_de_Mendoza,_5th_Marquis_of_Ca%C3%B1ete\" title=\"<PERSON>, 5th Marquis of Cañete\"><PERSON>, 5th Marquis of Cañete</a> (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Hurt<PERSON>_de_Mendoza,_5th_Marquis_of_Ca%C3%B1ete\" title=\"<PERSON>, 5th Marquis of Cañete\"><PERSON>, 5th Marquis of Cañete</a> (b. 1535)", "links": [{"title": "<PERSON>, 5th Marquis of Cañete", "link": "https://wikipedia.org/wiki/Garc%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>,_5th_Marquis_of_Ca%C3%B1ete"}]}, {"year": "1610", "text": "<PERSON>, Spanish priest and theologian (b. 1550)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and theologian (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and theologian (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Empress of the Mughal Empire (b. 1542)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/Mariam-uz-Zamani\" title=\"Mariam-uz-Zamani\">Mariam<PERSON><PERSON>z<PERSON>Zaman<PERSON></a>, Empress of the Mughal Empire (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariam-uz-Zamani\" title=\"Mariam-uz-Zamani\"><PERSON>m-<PERSON>z<PERSON>Zaman<PERSON></a>, Empress of the Mughal Empire (b. 1542)", "links": [{"title": "Mariam-uz-Zamani", "link": "https://wikipedia.org/wiki/Mariam-uz-Zamani"}]}, {"year": "1637", "text": "<PERSON>, Dutch scientist and philosopher (b. 1588)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scientist and philosopher (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch scientist and philosopher (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, 1st Earl of Halifax, English poet and politician, Chancellor of the Exchequer (b. 1661)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Halifax\" title=\"<PERSON>, 1st Earl of Halifax\"><PERSON>, 1st Earl of Halifax</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Halifax\" title=\"<PERSON>, 1st Earl of Halifax\"><PERSON>, 1st Earl of Halifax</a>, English poet and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1661)", "links": [{"title": "<PERSON>, 1st Earl of Halifax", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Halifax"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1786", "text": "<PERSON>, English organist and composer (b. 1712)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1712)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1795", "text": "<PERSON>, American physician and politician, 4th Governor of New Hampshire (b. 1729)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1795", "text": "<PERSON>, Scottish biographer (b. 1740)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biographer (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish biographer (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, 5th Baron <PERSON>, English lieutenant and politician (b. 1722)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English lieutenant and politician (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English lieutenant and politician (b. 1722)", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, French lawyer and politician (b. 1771)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, French lawyer and politician (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, French lawyer and politician (b. 1771)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1825", "text": "<PERSON>, comte <PERSON>, French philosopher and theorist (b. 1760)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_comte_de_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>, French philosopher and theorist (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_comte_de_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>, French philosopher and theorist (b. 1760)", "links": [{"title": "<PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Estonian-German physician, botanist, and entomologist (b. 1793)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German physician, botanist, and entomologist (b. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German physician, botanist, and entomologist (b. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American novelist and short story writer (b. 1804)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Mongolian general (b. 1811)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>gg<PERSON> Rinchen\"><PERSON><PERSON><PERSON></a>, Mongolian general (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>gg<PERSON> Rinchen\"><PERSON><PERSON><PERSON></a>, Mongolian general (b. 1811)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1872", "text": "<PERSON>, English-Australian politician, 2nd Premier of South Australia (b. 1813)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1813)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1876", "text": "<PERSON>, Dutch historian and politician (b. 1801)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and politician (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and politician (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, English engineer (b. 1809)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Cuban journalist, poet, and philosopher (b. 1853)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist, poet, and philosopher (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist, poet, and philosopher (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD"}]}, {"year": "1898", "text": "<PERSON>, English lawyer and politician, Prime Minister of the United Kingdom (b. 1809)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, South African general and politician, 1st President of the South African Republic (b. 1819)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the South African Republic\">President of the South African Republic</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African general and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"List of Presidents of the South African Republic\">President of the South African Republic</a> (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ius"}, {"title": "List of Presidents of the South African Republic", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_South_African_Republic"}]}, {"year": "1903", "text": "<PERSON>, English cricketer (b. 1856)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur <PERSON>\"><PERSON></a>, English cricketer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur <PERSON>\"><PERSON></a>, English cricketer (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Shrewsbury"}]}, {"year": "1904", "text": "<PERSON>, French librarian and historian (b. 1851)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French librarian and historian (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French librarian and historian (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Indian businessman, founded Tata Group (b. 1839)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Jamsetji_Tata\" class=\"mw-redirect\" title=\"Jamsetji <PERSON>ta\"><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Tata_Group\" title=\"Tata Group\">Tata Group</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jamsetji_Tata\" class=\"mw-redirect\" title=\"Jamsetji Tata\"><PERSON><PERSON><PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/Tata_Group\" title=\"Tata Group\">Tata Group</a> (b. 1839)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Tata Group", "link": "https://wikipedia.org/wiki/Tata_Group"}]}, {"year": "1906", "text": "<PERSON>, Canadian Métis leader (b. 1837)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(M%C3%A9tis_leader)\" title=\"<PERSON> (Métis leader)\"><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> leader (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(M%C3%A9tis_leader)\" title=\"<PERSON> (Métis leader)\"><PERSON></a>, Canadian <a href=\"https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)\" class=\"mw-redirect\" title=\"Métis people (Canada)\"><PERSON><PERSON><PERSON></a> leader (b. 1837)", "links": [{"title": "<PERSON> (Métis leader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(M%C3%A9tis_leader)"}, {"title": "<PERSON><PERSON><PERSON> people (Canada)", "link": "https://wikipedia.org/wiki/M%C3%A9tis_people_(Canada)"}]}, {"year": "1907", "text": "<PERSON>, English engineer, designed the Forth Bridge (b. 1840)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Forth_Bridge\" title=\"Forth Bridge\">Forth Bridge</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Forth_Bridge\" title=\"Forth Bridge\">Forth Bridge</a> (b. 1840)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>(engineer)"}, {"title": "Forth Bridge", "link": "https://wikipedia.org/wiki/Forth_Bridge"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Polish journalist and author (b. 1847)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_Prus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_Prus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_Prus"}]}, {"year": "1915", "text": "<PERSON>, English-Australian soldier (b. 1892)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian soldier (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian soldier (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, French-American soldier and pilot (b. 1885)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American soldier and pilot (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-American soldier and pilot (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON> <PERSON><PERSON>, British colonel and archaeologist (b. 1888)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British colonel and archaeologist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British colonel and archaeologist (b. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, British Islamic scholar (b. 1875)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British Islamic scholar (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, British Islamic scholar (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Azerbaijani-Turkish journalist and publicist (b. 1869)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ahmet_A%C4%9Fao%C4%9Flu\" title=\"<PERSON><PERSON> Ağaoğlu\"><PERSON><PERSON></a>, Azerbaijani-Turkish journalist and publicist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_A%C4%9Fao%C4%9Flu\" title=\"<PERSON><PERSON> Ağaoğlu\"><PERSON><PERSON></a>, Azerbaijani-Turkish journalist and publicist (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_A%C4%9Fao%C4%9Flu"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Estonian painter and illustrator (b. 1865)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter and illustrator (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian painter and illustrator (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German soldier and politician (b. 1889)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American novelist and dramatist (b. 1869)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Booth_Tarkington\" title=\"Booth Tarkington\"><PERSON></a>, American novelist and dramatist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Booth_Tarkington\" title=\"Booth Tarkington\"><PERSON></a>, American novelist and dramatist (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Booth_Tarkington"}]}, {"year": "1950", "text": "<PERSON>, Romanian physician and politician, Prime Minister of Moldova (b. 1884)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Moldova\" title=\"Prime Minister of Moldova\">Prime Minister of Moldova</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Moldova\" title=\"Prime Minister of Moldova\">Prime Minister of Moldova</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Moldova", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Moldova"}]}, {"year": "1954", "text": "<PERSON>, American composer and educator (b. 1874)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian historian (b. 1870)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian historian (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian historian (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish race car driver (b. 1927)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English actor (b. 1891)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American painter, sculptor, and author (b. 1871)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter, sculptor, and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German painter (d. 1877)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Gabriel<PERSON>_M%C3%BCnter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gabriel<PERSON>_M%C3%BCnter\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabriele_M%C3%BCnter"}]}, {"year": "1969", "text": "<PERSON>, American saxophonist and clarinet player (b. 1901)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American poet (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nash\"><PERSON></a>, American poet (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ogden_Nash\" title=\"<PERSON> Nash\"><PERSON></a>, American poet (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ogden_Nash"}]}, {"year": "1978", "text": "<PERSON>, Estonian-Swedish journalist and author (b. 1898)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Swedish journalist and author (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Swedish journalist and author (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian playwright and historian (b. 1906)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian playwright and historian (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian playwright and historian (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Belgian lawyer and politician, 2nd President of the European Commission (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (b. 1902)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1984", "text": "<PERSON>, English poet and academic (b. 1906)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi academic (b. 1928)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi academic (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American saxophonist (b. 1931)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Jr., American psychologist and author (b. 1915)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American psychologist and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American psychologist and author (b. 1915)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Greek composer and educator (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French sociologist, philosopher, and academic (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, philosopher, and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, philosopher, and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American journalist, 37th First Lady of the United States (b. 1929)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 37th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 37th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1994", "text": "<PERSON>, Spanish cyclist (b. 1945)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>%C3%B1a"}]}, {"year": "1996", "text": "<PERSON>, American baseball player and actor (b. 1917)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Japanese soldier and politician, 75th Prime Minister of Japan (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/S%C5%8Ds<PERSON>_<PERSON>o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 75th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C5%8<PERSON><PERSON>_<PERSON>o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 75th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C5%8Dsuke_Uno"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Russian soldier and pilot (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and pilot (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American singer (b. 1946)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Australian lieutenant and politician, 19th Prime Minister of Australia (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lieutenant and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lieutenant and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "2002", "text": "<PERSON>, American historian and author (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Dutch actress and screenwriter (b. 1907)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actress and screenwriter (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actress and screenwriter (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Polish footballer and coach (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand politician (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Indian playwright and screenwriter (b. 1928)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian playwright and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian playwright and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2009", "text": "<PERSON>, English composer and academic (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Irish lawyer and politician, 8th Taoiseach of Ireland (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>eral<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoise<PERSON>\">Taoiseach of Ireland</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "2011", "text": "<PERSON>, American artist (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American conductor and educator (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English race car driver (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German-Mexican swimmer (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Mexican swimmer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Mexican swimmer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand soldier and pilot (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and pilot (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand soldier and pilot (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American lawyer and jurist (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and jurist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sa<PERSON> Ford\"><PERSON><PERSON></a>, American lawyer and jurist (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Ford"}]}, {"year": "2013", "text": "<PERSON>, English-Canadian pianist and composer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, English-Canadian pianist and composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, English-Canadian pianist and composer (b. 1932)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pianist)"}]}, {"year": "2013", "text": "<PERSON>, Canadian journalist and politician (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English motorcycle racer (b. 1982)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(motorcycle_racer)\" class=\"mw-redirect\" title=\"<PERSON> (motorcycle racer)\"><PERSON></a>, English motorcycle racer (b. 1982)", "links": [{"title": "<PERSON> (motorcycle racer)", "link": "https://wikipedia.org/wiki/<PERSON>(motorcycle_racer)"}]}, {"year": "2014", "text": "<PERSON>, Australian race car driver (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and poet (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American historian and scholar (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American historian and author (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish boxer (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish boxer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish boxer (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian-Canadian lawyer and politician (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Canadian lawyer and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Canadian lawyer and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American philanthropist, socialite; 31st Second Lady of the United States (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rockefeller\"><PERSON></a>, American philanthropist, socialite; 31st <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rockefeller\"><PERSON></a>, American philanthropist, socialite; 31st <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rockefeller"}, {"title": "Second Lady of the United States", "link": "https://wikipedia.org/wiki/Second_Lady_of_the_United_States"}]}, {"year": "2015", "text": "<PERSON>, English historian, author, and academic (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, English-born Canadian-American actor (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Canadian-American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-born Canadian-American actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian-born American journalist (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Safer\"><PERSON></a>, Canadian-born American journalist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Safer\"><PERSON></a>, Canadian-born American journalist (b. 1931)", "links": [{"title": "<PERSON>r", "link": "https://wikipedia.org/wiki/<PERSON>_Safer"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON><PERSON>, General coordinator of the Movement for Change (Gorran) (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, General coordinator of the <a href=\"https://wikipedia.org/wiki/Movement_for_Change\" class=\"mw-redirect\" title=\"Movement for Change\">Movement for Change</a> (<PERSON><PERSON><PERSON>) (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, General coordinator of the <a href=\"https://wikipedia.org/wiki/Movement_for_Change\" class=\"mw-redirect\" title=\"Movement for Change\">Movement for Change</a> (<PERSON><PERSON><PERSON>) (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Movement for Change", "link": "https://wikipedia.org/wiki/Movement_for_Change"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Lt. Colonel in Soviet Air Defence Forces (b. 1939)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lt. Colonel in <a href=\"https://wikipedia.org/wiki/Soviet_Air_Defence_Forces\" title=\"Soviet Air Defence Forces\">Soviet Air Defence Forces</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lt. Colonel in <a href=\"https://wikipedia.org/wiki/Soviet_Air_Defence_Forces\" title=\"Soviet Air Defence Forces\">Soviet Air Defence Forces</a> (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Soviet Air Defence Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Defence_Forces"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Chinese linguist (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Zheng<PERSON><PERSON>_Shangfang\" title=\"Zheng<PERSON><PERSON> Shangfang\"><PERSON><PERSON><PERSON></a>, Chinese linguist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zheng<PERSON><PERSON>_Shangfang\" title=\"Zheng<PERSON><PERSON> Shangfang\"><PERSON><PERSON><PERSON></a>, Chinese linguist (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zhengzhang_<PERSON>gfang"}]}, {"year": "2021", "text": "<PERSON>, American comedian (b. 1941)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian (b. 1941)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "2023", "text": "<PERSON>, English bassist (b. 1964)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Congolese politician, businessman and military officer (b. 1983)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Christian_Malanga\" title=\"Christian Malanga\"><PERSON></a>, Congolese politician, businessman and military officer (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Malanga\" title=\"Christian Malanga\"><PERSON></a>, Congolese politician, businessman and military officer (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Malanga"}]}, {"year": "2024", "text": "Victims in the 2024 Varzaqan helicopter crash:\n<PERSON><PERSON><PERSON>, Iranian politician (b. 1964)\n<PERSON><PERSON><PERSON>, 8th President of Iran (b. 1960)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Varzaqan_helicopter_crash\" title=\"2024 Varzaqan helicopter crash\">Victims in the 2024 Varzaqan helicopter crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (b. 1964)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 8th President of Iran (b. 1960)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Varzaqan_helicopter_crash\" title=\"2024 Varzaqan helicopter crash\">Victims in the 2024 Varzaqan helicopter crash</a>:\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (b. 1964)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 8th President of Iran (b. 1960)</li>\n</ul>", "links": [{"title": "2024 Varzaqan helicopter crash", "link": "https://wikipedia.org/wiki/2024_<PERSON>ar<PERSON><PERSON><PERSON>_helicopter_crash"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, Iranian politician (b. 1964)", "text": null, "html": "<PERSON><PERSON><PERSON>, Iranian politician (b. 1964) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Ho<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON><PERSON>, 8th President of Iran (b. 1960)", "text": null, "html": "<PERSON><PERSON><PERSON>, 8th President of Iran (b. 1960) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 8th President of Iran (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 8th President of Iran (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}