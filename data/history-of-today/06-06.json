{"date": "June 6", "url": "https://wikipedia.org/wiki/June_6", "data": {"Events": [{"year": "913", "text": "<PERSON>, the eight-year-old illegitimate son of <PERSON> the <PERSON>, becomes nominal ruler of the Byzantine Empire under the regency of a seven-man council headed by Patriarch <PERSON>, appointed by <PERSON>'s uncle <PERSON> on his deathbed.", "html": "913 - <a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"<PERSON> VII\"><PERSON> VII</a>, the eight-year-old illegitimate son of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> VI the Wise</a>, becomes nominal ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> under the <a href=\"https://wikipedia.org/wiki/Regency\" class=\"mw-redirect\" title=\"Regency\">regency</a> of a seven-man council headed by Patriarch <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, appointed by <PERSON>'s uncle <a href=\"https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)\" title=\"<PERSON> (Byzantine emperor)\"><PERSON></a> on his deathbed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"<PERSON> VII\"><PERSON> VII</a>, the eight-year-old illegitimate son of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_the_Wise\" title=\"<PERSON> VI the Wise\"><PERSON> VI the Wise</a>, becomes nominal ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> under the <a href=\"https://wikipedia.org/wiki/Regency\" class=\"mw-redirect\" title=\"Regency\">regency</a> of a seven-man council headed by Patriarch <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, appointed by <PERSON>'s uncle <a href=\"https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)\" title=\"<PERSON> (Byzantine emperor)\"><PERSON></a> on his deathbed.", "links": [{"title": "Constantine VII", "link": "https://wikipedia.org/wiki/Constantine_VII"}, {"title": "<PERSON> the Wise", "link": "https://wikipedia.org/wiki/<PERSON>_VI_the_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "Regency", "link": "https://wikipedia.org/wiki/Regency"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ikos"}, {"title": "<PERSON> (Byzantine emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)"}]}, {"year": "1505", "text": "The M8.2-8.8 Lo Mustang earthquake affects Tibet and Nepal, causing severe damage in Kathmandu and parts of the Indo-Gangetic plain.", "html": "1505 - The M8.2-8.8 <a href=\"https://wikipedia.org/wiki/1505_Lo_Mustang_earthquake\" title=\"1505 Lo Mustang earthquake\">Lo Mustang earthquake</a> affects Tibet and Nepal, causing severe damage in <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a> and parts of the Indo-Gangetic plain.", "no_year_html": "The M8.2-8.8 <a href=\"https://wikipedia.org/wiki/1505_Lo_Mustang_earthquake\" title=\"1505 Lo Mustang earthquake\">Lo Mustang earthquake</a> affects Tibet and Nepal, causing severe damage in <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a> and parts of the Indo-Gangetic plain.", "links": [{"title": "1505 Lo Mustang earthquake", "link": "https://wikipedia.org/wiki/1505_Lo_Mustang_earthquake"}, {"title": "Kathman<PERSON>", "link": "https://wikipedia.org/wiki/Kathmandu"}]}, {"year": "1513", "text": "Battle of Novara. In the Italian Wars, Swiss troops defeat the French under <PERSON>, forcing them to abandon Milan; Duke <PERSON><PERSON><PERSON><PERSON> is restored.", "html": "1513 - <a href=\"https://wikipedia.org/wiki/Battle_of_Novara_(1513)\" title=\"Battle of Novara (1513)\">Battle of Novara</a>. In the <a href=\"https://wikipedia.org/wiki/Italian_Wars\" title=\"Italian Wars\">Italian Wars</a>, Swiss troops defeat the French under <a href=\"https://wikipedia.org/wiki/Louis_II_de_la_Tr%C3%A9moille\" title=\"Louis II de la Trémoille\"><PERSON> Trémoille</a>, forcing them to abandon <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>iliano_<PERSON>rz<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is restored.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Novara_(1513)\" title=\"Battle of Novara (1513)\">Battle of Novara</a>. In the <a href=\"https://wikipedia.org/wiki/Italian_Wars\" title=\"Italian Wars\">Italian Wars</a>, Swiss troops defeat the French under <a href=\"https://wikipedia.org/wiki/Louis_II_de_la_Tr%C3%A9moille\" title=\"Louis II de la Trémoille\"><PERSON> Trémoille</a>, forcing them to abandon <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; Duke <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>o_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is restored.", "links": [{"title": "Battle of Novara (1513)", "link": "https://wikipedia.org/wiki/Battle_of_Novara_(1513)"}, {"title": "Italian Wars", "link": "https://wikipedia.org/wiki/Italian_Wars"}, {"title": "Louis II de la Trémoille", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_la_Tr%C3%A9moille"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1523", "text": "Swedish regent <PERSON> is elected King of Sweden and, marking a symbolic end to the Kalmar Union, 6 June is designated the country's national day.", "html": "1523 - Swedish regent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Monarchy_of_Sweden\" title=\"Monarchy of Sweden\">King of Sweden</a> and, marking a symbolic end to the <a href=\"https://wikipedia.org/wiki/Kalmar_Union\" title=\"Kalmar Union\">Kalmar Union</a>, 6 June is designated the country's national day.", "no_year_html": "Swedish regent <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Monarchy_of_Sweden\" title=\"Monarchy of Sweden\">King of Sweden</a> and, marking a symbolic end to the <a href=\"https://wikipedia.org/wiki/Kalmar_Union\" title=\"Kalmar Union\">Kalmar Union</a>, 6 June is designated the country's national day.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Monarchy of Sweden", "link": "https://wikipedia.org/wiki/Monarchy_of_Sweden"}, {"title": "Kalmar Union", "link": "https://wikipedia.org/wiki/Kalmar_Union"}]}, {"year": "1654", "text": "Swedish Queen <PERSON> abdicated her throne in favour of her cousin <PERSON> and converted to Catholicism.", "html": "1654 - Swedish Queen <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON></a> abdicated her throne in favour of her cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a> and converted to <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholicism</a>.", "no_year_html": "Swedish Queen <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden\" title=\"<PERSON>, Queen of Sweden\"><PERSON></a> abdicated her throne in favour of her cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> of Sweden\"><PERSON></a> and converted to <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholicism</a>.", "links": [{"title": "<PERSON>, Queen of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Sweden"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Sweden"}, {"title": "Catholicism", "link": "https://wikipedia.org/wiki/Catholicism"}]}, {"year": "1674", "text": "<PERSON><PERSON> is crowned as the first Ch<PERSON><PERSON><PERSON> of the Maratha Empire at Raigad Fort.", "html": "1674 - <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON></a> is crowned as the first <a href=\"https://wikipedia.org/wiki/Chhatrapati\" title=\"Ch<PERSON>rap<PERSON>\">Chhatrapati</a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> at <a href=\"https://wikipedia.org/wiki/Raigad_Fort\" title=\"Raigad Fort\">Raigad Fort</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ji\" title=\"Shivaji\"><PERSON><PERSON></a> is crowned as the first <a href=\"https://wikipedia.org/wiki/Chhatrapati\" title=\"Chhatrap<PERSON>\">Chhatrapati</a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> at <a href=\"https://wikipedia.org/wiki/Raigad_Fort\" title=\"Raigad Fort\">Raigad Fort</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ch<PERSON>rapati"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}, {"title": "Raigad Fort", "link": "https://wikipedia.org/wiki/Raigad_Fort"}]}, {"year": "1762", "text": "In the Seven Years' War, British forces begin the Siege of Havana and temporarily capture the city.", "html": "1762 - In the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>, British forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Havana\" title=\"Siege of Havana\">Siege of Havana</a> and temporarily capture the city.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>, British forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Havana\" title=\"Siege of Havana\">Siege of Havana</a> and temporarily capture the city.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Siege of Havana", "link": "https://wikipedia.org/wiki/Siege_of_Havana"}]}, {"year": "1813", "text": "The Battle of Stoney Creek, considered a critical turning point in the War of 1812. A British force of 700 under <PERSON> defeats an American force twice its size under <PERSON> and <PERSON>.", "html": "1813 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Stoney_Creek\" title=\"Battle of Stoney Creek\">Battle of Stoney Creek</a>, considered a critical turning point in the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>. A British force of 700 under <a href=\"https://wikipedia.org/wiki/<PERSON>(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a> defeats an American force twice its size under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Stoney_Creek\" title=\"Battle of Stoney Creek\">Battle of Stoney Creek</a>, considered a critical turning point in the <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>. A British force of 700 under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a> defeats an American force twice its size under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Stoney Creek", "link": "https://wikipedia.org/wiki/Battle_of_Stoney_Creek"}, {"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(general)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON> is accidentally shot in the stomach, leading to <PERSON>'s studies on digestion.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alexis St Martin\"><PERSON></a> is accidentally shot in the stomach, leading to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s studies on digestion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alexis St Martin\"><PERSON></a> is accidentally shot in the stomach, leading to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s studies on digestion.", "links": [{"title": "Alexis <PERSON> Martin", "link": "https://wikipedia.org/wiki/Alexis_St_Martin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "The June Rebellion in Paris is put down by the National Guard.", "html": "1832 - The <a href=\"https://wikipedia.org/wiki/June_Rebellion\" title=\"June Rebellion\">June Rebellion</a> in Paris is put down by the National Guard.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/June_Rebellion\" title=\"June Rebellion\">June Rebellion</a> in Paris is put down by the National Guard.", "links": [{"title": "June Rebellion", "link": "https://wikipedia.org/wiki/June_Rebellion"}]}, {"year": "1844", "text": "The Young Men's Christian Association (YMCA) is founded in London.", "html": "1844 - The <a href=\"https://wikipedia.org/wiki/YMCA\" title=\"YMCA\">Young Men's Christian Association</a> (YMCA) is founded in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/YMCA\" title=\"YMCA\">Young Men's Christian Association</a> (YMCA) is founded in London.", "links": [{"title": "YMCA", "link": "https://wikipedia.org/wiki/YMCA"}]}, {"year": "1859", "text": "Queensland is established as a separate colony from New South Wales. The date is still celebrated as Queensland Day.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Queensland\" title=\"Queensland\">Queensland</a> is established as a separate <a href=\"https://wikipedia.org/wiki/Crown_colony\" title=\"Crown colony\">colony</a> from <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>. The date is still celebrated as <a href=\"https://wikipedia.org/wiki/Queensland_Day\" title=\"Queensland Day\">Queensland Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queensland\" title=\"Queensland\">Queensland</a> is established as a separate <a href=\"https://wikipedia.org/wiki/Crown_colony\" title=\"Crown colony\">colony</a> from <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a>. The date is still celebrated as <a href=\"https://wikipedia.org/wiki/Queensland_Day\" title=\"Queensland Day\">Queensland Day</a>.", "links": [{"title": "Queensland", "link": "https://wikipedia.org/wiki/Queensland"}, {"title": "Crown colony", "link": "https://wikipedia.org/wiki/Crown_colony"}, {"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}, {"title": "Queensland Day", "link": "https://wikipedia.org/wiki/Queensland_Day"}]}, {"year": "1862", "text": "The First Battle of Memphis, a naval engagement fought on the Mississippi results in the capture of Memphis, Tennessee by Union forces from the Confederates.", "html": "1862 - The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Memphis\" title=\"First Battle of Memphis\">First Battle of Memphis</a>, a naval engagement fought on the <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> results in the capture of <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a> by <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces from the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederates</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Battle_of_Memphis\" title=\"First Battle of Memphis\">First Battle of Memphis</a>, a naval engagement fought on the <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> results in the capture of <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a> by <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces from the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederates</a>.", "links": [{"title": "First Battle of Memphis", "link": "https://wikipedia.org/wiki/First_Battle_of_Memphis"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Memphis, Tennessee", "link": "https://wikipedia.org/wiki/Memphis,_Tennessee"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1882", "text": "The Shewan forces of Menelik II of Ethiopia defeat the Gojjame army in the Battle of Embabo. The Shewans capture Negus <PERSON>manot of Gojjam, and their victory leads to a Shewan hegemony over the territories south of the Abay River.", "html": "1882 - The <a href=\"https://wikipedia.org/wiki/She<PERSON>\" title=\"Shewa\">Shewan</a> forces of <a href=\"https://wikipedia.org/wiki/Menelik_II_of_Ethiopia\" class=\"mw-redirect\" title=\"Menelik II of Ethiopia\">Menelik II of Ethiopia</a> defeat the <a href=\"https://wikipedia.org/wiki/Gojjam\" title=\"Gojjam\">Gojjame</a> army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Embabo\" title=\"Battle of Embabo\">Battle of Embabo</a>. The Shewans capture Negus Tekle Haymanot of Gojjam, and their victory leads to a Shewan hegemony over the territories south of the <a href=\"https://wikipedia.org/wiki/Abay_River\" class=\"mw-redirect\" title=\"Abay River\">Abay River</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Shewa\" title=\"Shewa\"><PERSON>wan</a> forces of <a href=\"https://wikipedia.org/wiki/Menelik_II_of_Ethiopia\" class=\"mw-redirect\" title=\"Menelik II of Ethiopia\">Menelik II of Ethiopia</a> defeat the <a href=\"https://wikipedia.org/wiki/Gojjam\" title=\"Gojjam\">Gojjame</a> army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Embabo\" title=\"Battle of Embabo\">Battle of Embabo</a>. The Shewans capture Negus Tekle Haymanot of Gojjam, and their victory leads to a Shewan hegemony over the territories south of the <a href=\"https://wikipedia.org/wiki/Abay_River\" class=\"mw-redirect\" title=\"Abay River\">Abay River</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa"}, {"title": "Menelik II of Ethiopia", "link": "https://wikipedia.org/wiki/Menelik_II_of_Ethiopia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gojjam"}, {"title": "Battle of Embabo", "link": "https://wikipedia.org/wiki/Battle_of_Embabo"}, {"title": "Abay River", "link": "https://wikipedia.org/wiki/Abay_River"}]}, {"year": "1889", "text": "The Great Seattle Fire destroys all of downtown Seattle.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/Great_Seattle_Fire\" title=\"Great Seattle Fire\">Great Seattle Fire</a> destroys all of downtown Seattle.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Seattle_Fire\" title=\"Great Seattle Fire\">Great Seattle Fire</a> destroys all of downtown Seattle.", "links": [{"title": "Great Seattle Fire", "link": "https://wikipedia.org/wiki/Great_Seattle_Fire"}]}, {"year": "1892", "text": "The Chicago \"L\" elevated rail system begins operation.", "html": "1892 - The <a href=\"https://wikipedia.org/wiki/Chicago_%22L%22#History\" title='Chicago \"L\"'>Chicago \"L\"</a> elevated rail system begins operation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_%22L%22#History\" title='Chicago \"L\"'>Chicago \"L\"</a> elevated rail system begins operation.", "links": [{"title": "Chicago \"L\"", "link": "https://wikipedia.org/wiki/Chicago_%22L%22#History"}]}, {"year": "1894", "text": "Governor <PERSON> orders the Colorado state militia to protect and support the miners engaged in the Cripple Creek miners' strike.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> <a href=\"https://wikipedia.org/wiki/U.S._National_Guard\" class=\"mw-redirect\" title=\"U.S. National Guard\">state militia</a> to protect and support the miners engaged in the <a href=\"https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894\" title=\"Cripple Creek miners' strike of 1894\">Cripple Creek miners' strike</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> <a href=\"https://wikipedia.org/wiki/U.S._National_Guard\" class=\"mw-redirect\" title=\"U.S. National Guard\">state militia</a> to protect and support the miners engaged in the <a href=\"https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894\" title=\"Cripple Creek miners' strike of 1894\">Cripple Creek miners' strike</a>.", "links": [{"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}, {"title": "U.S. National Guard", "link": "https://wikipedia.org/wiki/U.S._National_Guard"}, {"title": "Cripple Creek miners' strike of 1894", "link": "https://wikipedia.org/wiki/Cripple_Creek_miners%27_strike_of_1894"}]}, {"year": "1912", "text": "The eruption of Novarupta in Alaska begins. It is the largest volcanic eruption of the 20th century.", "html": "1912 - The eruption of <a href=\"https://wikipedia.org/wiki/Novarupta\" title=\"Novarupta\">Novarupta</a> in Alaska begins. It is the largest <a href=\"https://wikipedia.org/wiki/Volcanic\" class=\"mw-redirect\" title=\"Volcanic\">volcanic</a> eruption of the 20th century.", "no_year_html": "The eruption of <a href=\"https://wikipedia.org/wiki/Novarupta\" title=\"Novarupta\">Novarupta</a> in Alaska begins. It is the largest <a href=\"https://wikipedia.org/wiki/Volcanic\" class=\"mw-redirect\" title=\"Volcanic\">volcanic</a> eruption of the 20th century.", "links": [{"title": "Novarupta", "link": "https://wikipedia.org/wiki/Novarupta"}, {"title": "Volcanic", "link": "https://wikipedia.org/wiki/Volcanic"}]}, {"year": "1918", "text": "Battle of Belleau Wood in World War I: the U.S. Marine Corps suffers its worst single day's casualties while attempting to recapture the wood at Château-Thierry (the losses are exceeded at the Battle of Tarawa in November 1943).", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a> in <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: the <a href=\"https://wikipedia.org/wiki/U.S._Marine_Corps\" class=\"mw-redirect\" title=\"U.S. Marine Corps\">U.S. Marine Corps</a> suffers its worst single day's casualties while attempting to recapture the wood at <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau-Thierry\" title=\"Château-Thierry\">Château-Thierry</a> (the losses are exceeded at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tarawa\" title=\"Battle of Tarawa\">Battle of Tarawa</a> in November 1943).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a> in <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: the <a href=\"https://wikipedia.org/wiki/U.S._Marine_Corps\" class=\"mw-redirect\" title=\"U.S. Marine Corps\">U.S. Marine Corps</a> suffers its worst single day's casualties while attempting to recapture the wood at <a href=\"https://wikipedia.org/wiki/Ch%C3%A2teau-Thierry\" title=\"Château-Thierry\">Château-Thierry</a> (the losses are exceeded at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tarawa\" title=\"Battle of Tarawa\">Battle of Tarawa</a> in November 1943).", "links": [{"title": "Battle of Belleau Wood", "link": "https://wikipedia.org/wiki/Battle_of_Belleau_Wood"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "U.S. Marine Corps", "link": "https://wikipedia.org/wiki/U.S._Marine_Corps"}, {"title": "Château-T<PERSON>erry", "link": "https://wikipedia.org/wiki/Ch%C3%A2teau-Thierry"}, {"title": "Battle of Tarawa", "link": "https://wikipedia.org/wiki/Battle_of_Tarawa"}]}, {"year": "1925", "text": "The original Chrysler Corporation was founded by Walter Chrysler from the remains of the Maxwell Motor Company.", "html": "1925 - The original <a href=\"https://wikipedia.org/wiki/Chrysler_Corporation\" class=\"mw-redirect\" title=\"Chrysler Corporation\">Chrysler Corporation</a> was founded by <a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a> from the remains of the <a href=\"https://wikipedia.org/wiki/Maxwell_automobile\" class=\"mw-redirect\" title=\"Maxwell automobile\">Maxwell Motor Company</a>.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/Chrysler_Corporation\" class=\"mw-redirect\" title=\"Chrysler Corporation\">Chrysler Corporation</a> was founded by <a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a> from the remains of the <a href=\"https://wikipedia.org/wiki/Maxwell_automobile\" class=\"mw-redirect\" title=\"Maxwell automobile\">Maxwell Motor Company</a>.", "links": [{"title": "Chrysler Corporation", "link": "https://wikipedia.org/wiki/Chrysler_Corporation"}, {"title": "Walter <PERSON>", "link": "https://wikipedia.org/wiki/Walter_Chrysler"}, {"title": "Maxwell automobile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "The first drive-in theater opens in Camden, New Jersey.", "html": "1933 - The first <a href=\"https://wikipedia.org/wiki/Drive-in_theater\" title=\"Drive-in theater\">drive-in theater</a> opens in <a href=\"https://wikipedia.org/wiki/Camden,_New_Jersey\" title=\"Camden, New Jersey\">Camden, New Jersey</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Drive-in_theater\" title=\"Drive-in theater\">drive-in theater</a> opens in <a href=\"https://wikipedia.org/wiki/Camden,_New_Jersey\" title=\"Camden, New Jersey\">Camden, New Jersey</a>.", "links": [{"title": "Drive-in theater", "link": "https://wikipedia.org/wiki/Drive-in_theater"}, {"title": "Camden, New Jersey", "link": "https://wikipedia.org/wiki/Camden,_New_Jersey"}]}, {"year": "1934", "text": "New Deal: U.S. President <PERSON> signs the Securities Exchange Act of 1934 into law, establishing the U.S. Securities and Exchange Commission.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Securities_Exchange_Act_of_1934\" title=\"Securities Exchange Act of 1934\">Securities Exchange Act of 1934</a> into law, establishing the <a href=\"https://wikipedia.org/wiki/U.S._Securities_and_Exchange_Commission\" title=\"U.S. Securities and Exchange Commission\">U.S. Securities and Exchange Commission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Securities_Exchange_Act_of_1934\" title=\"Securities Exchange Act of 1934\">Securities Exchange Act of 1934</a> into law, establishing the <a href=\"https://wikipedia.org/wiki/U.S._Securities_and_Exchange_Commission\" title=\"U.S. Securities and Exchange Commission\">U.S. Securities and Exchange Commission</a>.", "links": [{"title": "New Deal", "link": "https://wikipedia.org/wiki/New_Deal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Securities Exchange Act of 1934", "link": "https://wikipedia.org/wiki/Securities_Exchange_Act_of_1934"}, {"title": "U.S. Securities and Exchange Commission", "link": "https://wikipedia.org/wiki/U.S._Securities_and_Exchange_Commission"}]}, {"year": "1942", "text": "The United States Navy's victory over the Imperial Japanese Navy at the Battle of Midway is a major turning point in the Pacific Theater of World War II. All four Japanese fleet carriers taking part—Akagi, Kaga, Sōryū and Hiryū—are sunk, as is the heavy cruiser Mi<PERSON><PERSON>. The American carrier Yorktown and the destroyer Hammann are also sunk.", "html": "1942 - The <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a>'s victory over the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> is a major turning point in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II\" title=\"Pacific Ocean theater of World War II\">Pacific Theater</a> of <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>. All four Japanese <a href=\"https://wikipedia.org/wiki/Fleet_carrier\" title=\"Fleet carrier\">fleet carriers</a> taking part—<a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Akagi\" title=\"Japanese aircraft carrier Akagi\"><i>Akagi</i></a>, <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Kaga\" title=\"Japanese aircraft carrier Kaga\"><i>Kaga</i></a>, <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_S%C5%8Dry%C5%AB\" title=\"Japanese aircraft carrier Sōryū\"><i>Sōryū</i></a> and <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Hiry%C5%AB\" title=\"Japanese aircraft carrier Hiryū\"><i>Hiryū</i></a>—are sunk, as is the <a href=\"https://wikipedia.org/wiki/Heavy_cruiser\" title=\"Heavy cruiser\">heavy cruiser</a> <a href=\"https://wikipedia.org/wiki/Japanese_cruiser_Mikuma\" title=\"Japanese cruiser Mikuma\"><i>Mikuma</i></a>. The American carrier <a href=\"https://wikipedia.org/wiki/USS_Yorktown_(CV-5)\" title=\"USS Yorktown (CV-5)\"><i>Yorktown</i></a> and the destroyer <a href=\"https://wikipedia.org/wiki/USS_Hammann_(DD-412)\" title=\"USS Hammann (DD-412)\"><i>Hammann</i></a> are also sunk.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a>'s victory over the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Midway\" title=\"Battle of Midway\">Battle of Midway</a> is a major turning point in the <a href=\"https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II\" title=\"Pacific Ocean theater of World War II\">Pacific Theater</a> of <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>. All four Japanese <a href=\"https://wikipedia.org/wiki/Fleet_carrier\" title=\"Fleet carrier\">fleet carriers</a> taking part—<a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Akagi\" title=\"Japanese aircraft carrier Akagi\"><i>Akagi</i></a>, <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Kaga\" title=\"Japanese aircraft carrier Kaga\"><i>Kaga</i></a>, <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_S%C5%8Dry%C5%AB\" title=\"Japanese aircraft carrier Sōryū\"><i>Sōryū</i></a> and <a href=\"https://wikipedia.org/wiki/Japanese_aircraft_carrier_Hiry%C5%AB\" title=\"Japanese aircraft carrier Hiryū\"><i>Hiryū</i></a>—are sunk, as is the <a href=\"https://wikipedia.org/wiki/Heavy_cruiser\" title=\"Heavy cruiser\">heavy cruiser</a> <a href=\"https://wikipedia.org/wiki/Japanese_cruiser_Mikuma\" title=\"Japanese cruiser Mikuma\"><i>Mikuma</i></a>. The American carrier <a href=\"https://wikipedia.org/wiki/USS_Yorktown_(CV-5)\" title=\"USS Yorktown (CV-5)\"><i>Yorktown</i></a> and the destroyer <a href=\"https://wikipedia.org/wiki/USS_Hammann_(DD-412)\" title=\"USS Hammann (DD-412)\"><i>Hammann</i></a> are also sunk.", "links": [{"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Battle of Midway", "link": "https://wikipedia.org/wiki/Battle_of_Midway"}, {"title": "Pacific Ocean theater of World War II", "link": "https://wikipedia.org/wiki/Pacific_Ocean_theater_of_World_War_II"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Fleet carrier", "link": "https://wikipedia.org/wiki/Fleet_carrier"}, {"title": "Japanese aircraft carrier Akagi", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_Akagi"}, {"title": "Japanese aircraft carrier Kaga", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_Kaga"}, {"title": "Japanese aircraft carrier Sōryū", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_S%C5%8Dry%C5%AB"}, {"title": "Japanese aircraft carrier Hiryū", "link": "https://wikipedia.org/wiki/Japanese_aircraft_carrier_Hiry%C5%AB"}, {"title": "Heavy cruiser", "link": "https://wikipedia.org/wiki/Heavy_cruiser"}, {"title": "Japanese cruiser <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Japanese_cruiser_<PERSON><PERSON><PERSON>"}, {"title": "USS Yorktown (CV-5)", "link": "https://wikipedia.org/wiki/USS_Yorktown_(CV-5)"}, {"title": "<PERSON> (DD-412)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(DD-412)"}]}, {"year": "1944", "text": "Commencement of Operation Overlord, the Allied invasion of Normandy, with the execution of Operation Neptune—commonly referred to as D-Day—the largest seaborne invasion in history. Nearly 160,000 Allied troops cross the English Channel with about 5,000 landing and assault craft, 289 escort vessels, and 277 minesweepers participating. By the end of the day, the Allies have landed on five invasion beaches and are pushing inland.", "html": "1944 - Commencement of <a href=\"https://wikipedia.org/wiki/Operation_Overlord\" title=\"Operation Overlord\">Operation Overlord</a>, the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> invasion of <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>, with the execution of <a href=\"https://wikipedia.org/wiki/Normandy_landings\" title=\"Normandy landings\">Operation Neptune</a>—commonly referred to as <a href=\"https://wikipedia.org/wiki/D-Day\" class=\"mw-redirect\" title=\"D-Day\">D-Day</a>—the largest seaborne invasion in history. Nearly 160,000 Allied troops cross the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> with about 5,000 landing and assault craft, 289 escort vessels, and 277 minesweepers participating. By the end of the day, the Allies have landed on five invasion beaches and are pushing inland.", "no_year_html": "Commencement of <a href=\"https://wikipedia.org/wiki/Operation_Overlord\" title=\"Operation Overlord\">Operation Overlord</a>, the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> invasion of <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a>, with the execution of <a href=\"https://wikipedia.org/wiki/Normandy_landings\" title=\"Normandy landings\">Operation Neptune</a>—commonly referred to as <a href=\"https://wikipedia.org/wiki/D-Day\" class=\"mw-redirect\" title=\"D-Day\">D-Day</a>—the largest seaborne invasion in history. Nearly 160,000 Allied troops cross the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a> with about 5,000 landing and assault craft, 289 escort vessels, and 277 minesweepers participating. By the end of the day, the Allies have landed on five invasion beaches and are pushing inland.", "links": [{"title": "Operation Overlord", "link": "https://wikipedia.org/wiki/Operation_Overlord"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "Normandy", "link": "https://wikipedia.org/wiki/Normandy"}, {"title": "Normandy landings", "link": "https://wikipedia.org/wiki/Normandy_landings"}, {"title": "D-Day", "link": "https://wikipedia.org/wiki/D-Day"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}]}, {"year": "1944 the Capture of the Caen canal and Orne river bridges by paratroopers also known as operation coup de main (wrongly known as operation deadstick.)[31]", "text": null, "html": "1944 the Capture of the Caen canal and Orne river bridges by paratroopers also known as operation coup de main (wrongly known as operation deadstick.)[31] - 1944 the <a href=\"https://wikipedia.org/wiki/Capture_of_the_Caen_canal_and_Orne_river_bridges\" title=\"Capture of the Caen canal and Orne river bridges\">Capture of the Caen canal and Orne river bridges</a> by paratroopers also known as operation coup de main (wrongly known as operation deadstick.)", "no_year_html": "1944 the <a href=\"https://wikipedia.org/wiki/Capture_of_the_Caen_canal_and_Orne_river_bridges\" title=\"Capture of the Caen canal and Orne river bridges\">Capture of the Caen canal and Orne river bridges</a> by paratroopers also known as operation coup de main (wrongly known as operation deadstick.)", "links": [{"title": "Capture of the Caen canal and Orne river bridges", "link": "https://wikipedia.org/wiki/Capture_of_the_Caen_canal_and_Orne_river_bridges"}]}, {"year": "1966", "text": "March Against Fear: African-American civil rights activist <PERSON> is wounded in an ambush by white sniper <PERSON>. <PERSON> and <PERSON><PERSON><PERSON> are photographed by <PERSON>, whose photo will receive the 1967 Pulitzer Prize in Photography, the last one to be awarded in the category.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/March_Against_Fear\" title=\"March Against Fear\">March Against Fear</a>: <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> <a href=\"https://wikipedia.org/wiki/Civil_rights\" class=\"mw-redirect\" title=\"Civil rights\">civil rights</a> activist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is wounded in an ambush by white sniper <PERSON> and <PERSON><PERSON><PERSON> are photographed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, whose photo will receive the <a href=\"https://wikipedia.org/wiki/1967_Pulitzer_Prize\" title=\"1967 Pulitzer Prize\">1967 Pulitzer Prize</a> in <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography\" title=\"Pulitzer Prize for Photography\">Photography</a>, the last one to be awarded in the category.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/March_Against_Fear\" title=\"March Against Fear\">March Against Fear</a>: <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> <a href=\"https://wikipedia.org/wiki/Civil_rights\" class=\"mw-redirect\" title=\"Civil rights\">civil rights</a> activist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is wounded in an ambush by white sniper <PERSON>. <PERSON> and <PERSON><PERSON><PERSON> are photographed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, whose photo will receive the <a href=\"https://wikipedia.org/wiki/1967_Pulitzer_Prize\" title=\"1967 Pulitzer Prize\">1967 Pulitzer Prize</a> in <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography\" title=\"Pulitzer Prize for Photography\">Photography</a>, the last one to be awarded in the category.", "links": [{"title": "March Against Fear", "link": "https://wikipedia.org/wiki/March_Against_Fear"}, {"title": "African-American", "link": "https://wikipedia.org/wiki/African-American"}, {"title": "Civil rights", "link": "https://wikipedia.org/wiki/Civil_rights"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "1967 Pulitzer Prize", "link": "https://wikipedia.org/wiki/1967_Pulitzer_Prize"}, {"title": "Pulitzer Prize for Photography", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Photography"}]}, {"year": "1971", "text": "Soyuz 11 is launched. The mission ends in disaster when all three cosmonauts, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON> are suffocated by uncontrolled decompression of the capsule during re-entry on 29 June.", "html": "1971 - <i><a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a></i> is launched. The mission ends in disaster when all three cosmonauts, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are suffocated by <a href=\"https://wikipedia.org/wiki/Uncontrolled_decompression\" title=\"Uncontrolled decompression\">uncontrolled decompression</a> of the capsule during <a href=\"https://wikipedia.org/wiki/Atmospheric_entry\" title=\"Atmospheric entry\">re-entry</a> on 29 June.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Soyuz_11\" title=\"Soyuz 11\">Soyuz 11</a></i> is launched. The mission ends in disaster when all three cosmonauts, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are suffocated by <a href=\"https://wikipedia.org/wiki/Uncontrolled_decompression\" title=\"Uncontrolled decompression\">uncontrolled decompression</a> of the capsule during <a href=\"https://wikipedia.org/wiki/Atmospheric_entry\" title=\"Atmospheric entry\">re-entry</a> on 29 June.", "links": [{"title": "Soyuz 11", "link": "https://wikipedia.org/wiki/Soyuz_11"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ky"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Uncontrolled decompression", "link": "https://wikipedia.org/wiki/Uncontrolled_decompression"}, {"title": "Atmospheric entry", "link": "https://wikipedia.org/wiki/Atmospheric_entry"}]}, {"year": "1971", "text": "Hughes Airwest Flight 706 collides with a McDonnell Douglas F-4 Phantom II of the United States Marine Corps over the San Gabriel Mountains, killing 50.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Hughes_Airwest_Flight_706\" title=\"Hughes Airwest Flight 706\">Hughes Airwest Flight 706</a> collides with a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> over the <a href=\"https://wikipedia.org/wiki/San_Gabriel_Mountains\" title=\"San Gabriel Mountains\">San Gabriel Mountains</a>, killing 50.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hughes_Airwest_Flight_706\" title=\"Hughes Airwest Flight 706\">Hughes Airwest Flight 706</a> collides with a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_F-4_Phantom_II\" title=\"McDonnell Douglas F-4 Phantom II\">McDonnell Douglas F-4 Phantom II</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> over the <a href=\"https://wikipedia.org/wiki/San_Gabriel_Mountains\" title=\"San Gabriel Mountains\">San Gabriel Mountains</a>, killing 50.", "links": [{"title": "Hughes Airwest Flight 706", "link": "https://wikipedia.org/wiki/Hughes_Airwest_Flight_706"}, {"title": "McDonnell Douglas F-4 Phantom II", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_F-4_Phantom_II"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "San Gabriel Mountains", "link": "https://wikipedia.org/wiki/San_Gabriel_Mountains"}]}, {"year": "1975", "text": "British referendum results in continued membership of the European Economic Community, with 67% of votes in favour.", "html": "1975 - British <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a> results in continued membership of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Economic Community</a>, with 67% of votes in favour.", "no_year_html": "British <a href=\"https://wikipedia.org/wiki/Referendum\" title=\"Referendum\">referendum</a> results in continued membership of the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Economic Community</a>, with 67% of votes in favour.", "links": [{"title": "Referendum", "link": "https://wikipedia.org/wiki/Referendum"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "1976", "text": "Chief Minister of Sabah <PERSON><PERSON>, <PERSON>, and several other politicians are killed in a plane crash near Kota Kinabalu International Airport in Malaysia.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sabah\" title=\"Chief Minister of Sabah\">Chief Minister of Sabah</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and several other politicians are killed in a <a href=\"https://wikipedia.org/wiki/1976_Sabah_Air_GAF_Nomad_crash\" title=\"1976 Sabah Air GAF Nomad crash\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Kota_Kinabalu_International_Airport\" title=\"Kota Kinabalu International Airport\">Kota Kinabalu International Airport</a> in Malaysia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sabah\" title=\"Chief Minister of Sabah\">Chief Minister of Sabah</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and several other politicians are killed in a <a href=\"https://wikipedia.org/wiki/1976_Sabah_Air_GAF_Nomad_crash\" title=\"1976 Sabah Air GAF Nomad crash\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Kota_Kinabalu_International_Airport\" title=\"Kota Kinabalu International Airport\">Kota Kinabalu International Airport</a> in Malaysia.", "links": [{"title": "Chief Minister of Sabah", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Sabah"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1976 Sabah Air GAF Nomad crash", "link": "https://wikipedia.org/wiki/1976_Sabah_Air_GAF_Nomad_crash"}, {"title": "Kota Kinabalu International Airport", "link": "https://wikipedia.org/wiki/Kota_Kinabalu_International_Airport"}]}, {"year": "1982", "text": "The Lebanon War begins. Forces under Israeli Defense Minister <PERSON> invade southern Lebanon during Operation Peace for the Galilee, eventually reaching as far north as the capital Beirut.", "html": "1982 - The <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a> begins. Forces under <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> <a href=\"https://wikipedia.org/wiki/Defense_Minister\" class=\"mw-redirect\" title=\"Defense Minister\">Defense Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sharon\"><PERSON></a> invade <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">southern Lebanon</a> during <a href=\"https://wikipedia.org/wiki/Operation_Peace_for_the_Galilee\" class=\"mw-redirect\" title=\"Operation Peace for the Galilee\">Operation Peace for the Galilee</a>, eventually reaching as far north as the capital <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a> begins. Forces under <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> <a href=\"https://wikipedia.org/wiki/Defense_Minister\" class=\"mw-redirect\" title=\"Defense Minister\">Defense Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ariel Sharon\"><PERSON></a> invade <a href=\"https://wikipedia.org/wiki/Southern_Lebanon\" title=\"Southern Lebanon\">southern Lebanon</a> during <a href=\"https://wikipedia.org/wiki/Operation_Peace_for_the_Galilee\" class=\"mw-redirect\" title=\"Operation Peace for the Galilee\">Operation Peace for the Galilee</a>, eventually reaching as far north as the capital <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>.", "links": [{"title": "1982 Lebanon War", "link": "https://wikipedia.org/wiki/1982_Lebanon_War"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Defense Minister", "link": "https://wikipedia.org/wiki/Defense_Minister"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Southern Lebanon", "link": "https://wikipedia.org/wiki/Southern_Lebanon"}, {"title": "Operation Peace for the Galilee", "link": "https://wikipedia.org/wiki/Operation_Peace_for_the_Galilee"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "1985", "text": "The grave of \"<PERSON>\" is opened in Embu, Brazil; the exhumed remains are later proven to be those of <PERSON>, Auschwitz's \"Angel of Death\"; <PERSON><PERSON><PERSON> is thought to have drowned while swimming in February 1979.", "html": "1985 - The grave of \"<PERSON>\" is opened in <a href=\"https://wikipedia.org/wiki/Embu_das_Artes\" title=\"Embu das Artes\">Embu</a>, Brazil; the exhumed remains are later proven to be those of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a>'s \"Angel of Death\"; <PERSON><PERSON><PERSON> is thought to have drowned while swimming in February 1979.", "no_year_html": "The grave of \"<PERSON>\" is opened in <a href=\"https://wikipedia.org/wiki/Embu_das_Artes\" title=\"Embu das Artes\">Embu</a>, Brazil; the exhumed remains are later proven to be those of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz</a>'s \"Angel of Death\"; <PERSON><PERSON><PERSON> is thought to have drowned while swimming in February 1979.", "links": [{"title": "Embu das Artes", "link": "https://wikipedia.org/wiki/Embu_das_Artes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1992", "text": "Copa Airlines Flight 201 breaks apart in mid-air and crashes into the Darién Gap in Panama, killing all 47 aboard.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Copa_Airlines_Flight_201\" title=\"Copa Airlines Flight 201\">Copa Airlines Flight 201</a> breaks apart in mid-air and crashes into the <a href=\"https://wikipedia.org/wiki/Dari%C3%A9n_Gap\" title=\"Darién Gap\">Darién Gap</a> in Panama, killing all 47 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Copa_Airlines_Flight_201\" title=\"Copa Airlines Flight 201\">Copa Airlines Flight 201</a> breaks apart in mid-air and crashes into the <a href=\"https://wikipedia.org/wiki/Dari%C3%A9n_Gap\" title=\"Darién Gap\">Darién Gap</a> in Panama, killing all 47 aboard.", "links": [{"title": "Copa Airlines Flight 201", "link": "https://wikipedia.org/wiki/Copa_Airlines_Flight_201"}, {"title": "Darién Gap", "link": "https://wikipedia.org/wiki/Dari%C3%A9n_Gap"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wins the first presidential election in Mongolia.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Punsal<PERSON><PERSON><PERSON>_Ochirbat\" title=\"Punsal<PERSON><PERSON><PERSON> Ochirb<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1993_Mongolian_presidential_election\" title=\"1993 Mongolian presidential election\">first presidential election in Mongolia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Ochirbat\" title=\"Punsalmaagi<PERSON> Ochirbat\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1993_Mongolian_presidential_election\" title=\"1993 Mongolian presidential election\">first presidential election in Mongolia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Punsalmaagiin_<PERSON>at"}, {"title": "1993 Mongolian presidential election", "link": "https://wikipedia.org/wiki/1993_Mongolian_presidential_election"}]}, {"year": "1994", "text": "China Northwest Airlines Flight 2303 crashes near Xi'an Xianyang International Airport, killing all 160 people on board.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2303\" title=\"China Northwest Airlines Flight 2303\">China Northwest Airlines Flight 2303</a> crashes near <a href=\"https://wikipedia.org/wiki/Xi%27an_Xianyang_International_Airport\" title=\"Xi'an Xianyang International Airport\">Xi'an Xianyang International Airport</a>, killing all 160 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2303\" title=\"China Northwest Airlines Flight 2303\">China Northwest Airlines Flight 2303</a> crashes near <a href=\"https://wikipedia.org/wiki/Xi%27an_Xianyang_International_Airport\" title=\"Xi'an Xianyang International Airport\">Xi'an Xianyang International Airport</a>, killing all 160 people on board.", "links": [{"title": "China Northwest Airlines Flight 2303", "link": "https://wikipedia.org/wiki/China_Northwest_Airlines_Flight_2303"}, {"title": "Xi'an Xianyang International Airport", "link": "https://wikipedia.org/wiki/Xi%27an_Xianyang_International_Airport"}]}, {"year": "2002", "text": "Eastern Mediterranean event. A near-Earth asteroid estimated at ten meters in diameter explodes over the Mediterranean Sea between Greece and Libya. The explosion is estimated to have a force of 26 kilotons, slightly more powerful than the Nagasaki atomic bomb.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2002_Eastern_Mediterranean_event\" title=\"2002 Eastern Mediterranean event\">Eastern Mediterranean event</a>. A <a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">near-Earth asteroid</a> estimated at ten meters in diameter explodes over the Mediterranean Sea between Greece and <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>. The explosion is estimated to have a force of 26 <a href=\"https://wikipedia.org/wiki/Kiloton\" class=\"mw-redirect\" title=\"Kiloton\">kilotons</a>, slightly more powerful than the <a href=\"https://wikipedia.org/wiki/Nagasaki_atomic_bomb\" class=\"mw-redirect\" title=\"Nagasaki atomic bomb\">Nagasaki atomic bomb</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2002_Eastern_Mediterranean_event\" title=\"2002 Eastern Mediterranean event\">Eastern Mediterranean event</a>. A <a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">near-Earth asteroid</a> estimated at ten meters in diameter explodes over the Mediterranean Sea between Greece and <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>. The explosion is estimated to have a force of 26 <a href=\"https://wikipedia.org/wiki/Kiloton\" class=\"mw-redirect\" title=\"Kiloton\">kilotons</a>, slightly more powerful than the <a href=\"https://wikipedia.org/wiki/Nagasaki_atomic_bomb\" class=\"mw-redirect\" title=\"Nagasaki atomic bomb\">Nagasaki atomic bomb</a>.", "links": [{"title": "2002 Eastern Mediterranean event", "link": "https://wikipedia.org/wiki/2002_Eastern_Mediterranean_event"}, {"title": "Near-Earth asteroid", "link": "https://wikipedia.org/wiki/Near-Earth_asteroid"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>on"}, {"title": "Nagasaki atomic bomb", "link": "https://wikipedia.org/wiki/Nagasaki_atomic_bomb"}]}, {"year": "2017", "text": "Syrian civil war: The Battle of Raqqa begins with an offensive by the Syrian Democratic Forces (SDF) to capture the city from the Islamic State of Iraq and the Levant (ISIL).", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Raqqa_(2017)\" title=\"Battle of Raqqa (2017)\">Battle of Raqqa</a> begins with an offensive by the <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) to capture the city from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Raqqa_(2017)\" title=\"Battle of Raqqa (2017)\">Battle of Raqqa</a> begins with an offensive by the <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) to capture the city from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL).", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Battle of Raqqa (2017)", "link": "https://wikipedia.org/wiki/Battle_of_Raq<PERSON>_(2017)"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "2023", "text": "Destruction of the Kakhovka Dam during the Russo-Ukrainian war.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Destruction_of_the_Kakhovka_Dam\" title=\"Destruction of the Kakhovka Dam\">Destruction of the Kakhovka Dam</a> during the <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian war</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Destruction_of_the_Kakhovka_Dam\" title=\"Destruction of the Kakhovka Dam\">Destruction of the Kakhovka Dam</a> during the <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian war</a>.", "links": [{"title": "Destruction of the Kakhovka Dam", "link": "https://wikipedia.org/wiki/Destruction_of_the_Kakhovka_Dam"}, {"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}]}, {"year": "2024", "text": " The launch of SpaceX Starship integrated flight test 4 (IFT-4)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/SpaceX_Starship_integrated_flight_test_4\" class=\"mw-redirect\" title=\"SpaceX Starship integrated flight test 4\"> The launch of SpaceX Starship integrated flight test 4 (IFT-4)</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SpaceX_Starship_integrated_flight_test_4\" class=\"mw-redirect\" title=\"SpaceX Starship integrated flight test 4\"> The launch of SpaceX Starship integrated flight test 4 (IFT-4)</a>", "links": [{"title": "SpaceX Starship integrated flight test 4", "link": "https://wikipedia.org/wiki/SpaceX_Starship_integrated_flight_test_4"}]}], "Births": [{"year": "1436", "text": "<PERSON><PERSON><PERSON><PERSON> (<PERSON>), German mathematician, astronomer, and bishop (d. 1476)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/Regiomontanus\" title=\"Regiomontanus\">Regiomontanus</a> (<PERSON> von <PERSON>), German mathematician, astronomer, and bishop (d. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Regiomontanus\" title=\"Regiomontanus\">Regiomontanus</a> (<PERSON>), German mathematician, astronomer, and bishop (d. 1476)", "links": [{"title": "Regiomontanus", "link": "https://wikipedia.org/wiki/Regiomontanus"}]}, {"year": "1519", "text": "<PERSON>, Italian philosopher, physician, and botanist (d. 1603)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, physician, and botanist (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, physician, and botanist (d. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1599", "text": "<PERSON> (date of baptism), Spanish painter and educator (d. 1660)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/Diego_Vel%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> (<i>date of baptism</i>), Spanish painter and educator (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Vel%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> (<i>date of baptism</i>), Spanish painter and educator (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Vel%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1606", "text": "<PERSON>, French playwright and producer (d. 1684)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and producer (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and producer (d. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON><PERSON><PERSON>, French-American missionary and explorer (d. 1689)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American missionary and explorer (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American missionary and explorer (d. 1689)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1714", "text": "<PERSON> of Portugal, King of Portugal from 31 July 1750 until his death (d. 1777)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, King of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> from 31 July 1750 until his death (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, King of <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> from 31 July 1750 until his death (d. 1777)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Portugal"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "1755", "text": "<PERSON>, American soldier (d. 1776)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, American soldier and painter (d. 1843)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and painter (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Russian author and poet (d. 1837)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, German philologist and scholar (d. 1856)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, German pharmacist, founded <PERSON><PERSON> (d. 1880)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pharmacist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pharmacist, founded <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er"}]}, {"year": "1841", "text": "<PERSON>, Polish author and publisher (d. 1910)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and publisher (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and publisher (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON>, Norwegian school owner and writer (d. 1906)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian school owner and writer (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian school owner and writer (d. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, German-American physicist and academic, Nobel Prize laureate in 1909 for physics (d. 1918)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate in 1909 for <a href=\"https://wikipedia.org/wiki/Physics\" title=\"Physics\">physics</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate in 1909 for <a href=\"https://wikipedia.org/wiki/Physics\" title=\"Physics\">physics</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}, {"title": "Physics", "link": "https://wikipedia.org/wiki/Physics"}]}, {"year": "1851", "text": "<PERSON>, Italian inventor of the espresso machine (d. 1914)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian inventor of the espresso machine (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian inventor of the espresso machine (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Russian mathematician and physicist (d. 1918)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and physicist (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English historian, author, and poet (d. 1938)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and poet (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and poet (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American entrepreneur and co-founder of lifestyle brand Abercrombie & Fitch (d. 1931)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and co-founder of lifestyle brand <a href=\"https://wikipedia.org/wiki/Abercrom<PERSON>_%26_Fitch\" title=\"Abe<PERSON><PERSON><PERSON> &amp; Fitch\">Abercrombie &amp; Fitch</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur and co-founder of lifestyle brand <a href=\"https://wikipedia.org/wiki/Abe<PERSON><PERSON><PERSON>_%26_Fitch\" title=\"Aberc<PERSON>bie &amp; Fitch\">Abercrombie &amp; Fitch</a> (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Abercrombie & Fitch", "link": "https://wikipedia.org/wiki/Abercrombie_%26_Fitch"}]}, {"year": "1868", "text": "<PERSON>, English sailor and explorer (d. 1912)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and explorer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor and explorer (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON> of Hesse, German princess and Russian empress (d. 1918)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Ali<PERSON>_of_Hesse\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Hesse\"><PERSON><PERSON> of Hesse</a>, German princess and Russian empress (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ali<PERSON>_of_Hesse\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Hesse\"><PERSON><PERSON> of Hesse</a>, German princess and Russian empress (d. 1918)", "links": [{"title": "<PERSON>x of Hesse", "link": "https://wikipedia.org/wiki/Alix_of_Hesse"}]}, {"year": "1872", "text": "<PERSON>, Australian journalist and author (d. 1936)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, German author and critic, Nobel Prize laureate (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Mann\"><PERSON></a>, German author and critic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1890", "text": "<PERSON>, American singer, clarinet player, and bandleader (d. 1971)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer, clarinet player, and bandleader (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer, clarinet player, and bandleader (d. 1971)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Indian author and academic (d. 1986)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON><PERSON>a_Iyengar\" title=\"<PERSON><PERSON><PERSON> Iyengar\"><PERSON><PERSON><PERSON></a>, Indian author and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>yenga<PERSON>\" title=\"<PERSON><PERSON><PERSON> Iyengar\"><PERSON><PERSON><PERSON></a>, Indian author and academic (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>nga<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German general in WWII who planned Operation Barbarossa (d. 1944)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general in WWII who planned <a href=\"https://wikipedia.org/wiki/Operation_Barbarossa\" title=\"Operation Barbarossa\">Operation Barbarossa</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general in WWII who planned <a href=\"https://wikipedia.org/wiki/Operation_Barbarossa\" title=\"Operation Barbarossa\">Operation Barbarossa</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Operation Barbarossa", "link": "https://wikipedia.org/wiki/Operation_Barbarossa"}]}, {"year": "1896", "text": "<PERSON>, English World War I soldier and supercentenarian (d. 2009)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> soldier and <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> soldier and <a href=\"https://wikipedia.org/wiki/Supercentenarian\" title=\"Supercentenarian\">supercentenarian</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Supercentenarian", "link": "https://wikipedia.org/wiki/Supercentenarian"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Italian air marshal and fascist politician who played a key role in developing <PERSON>'s air force (d. 1940)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Italo_Balbo\" title=\"Italo Balbo\"><PERSON><PERSON></a>, Italian air marshal and fascist politician who played a key role in developing <PERSON>'s air force (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Balbo\" title=\"Italo Balbo\"><PERSON><PERSON></a>, Italian air marshal and fascist politician who played a key role in developing <PERSON>'s air force (d. 1940)", "links": [{"title": "Italo <PERSON>lbo", "link": "https://wikipedia.org/wiki/Italo_Balbo"}]}, {"year": "1897", "text": "<PERSON>, Finnish actor (d. 1981)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish actor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish actor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, South African politician, 2nd State President of South Africa (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ch%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African politician, 2nd <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ch%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African politician, 2nd <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ch%C3%A9"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "1898", "text": "<PERSON><PERSON>, English ballerina, choreographer, and director (d. 2001)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Valois\"><PERSON><PERSON></a>, English ballerina, choreographer, and director (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English ballerina, choreographer, and director (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Ukrainian-American psychiatrist and physician (d. 1957)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American psychiatrist and physician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American psychiatrist and physician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, English author, poet and hymnwriter who created the character Mrs <PERSON> (d. 1953)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet and hymnwriter who created the character <a href=\"https://wikipedia.org/wiki/Mrs._<PERSON><PERSON>_(character)\" title=\"Mrs. <PERSON> (character)\">Mrs <PERSON></a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet and hymnwriter who created the character <a href=\"https://wikipedia.org/wiki/Mrs._<PERSON><PERSON>_(character)\" title=\"Mrs. <PERSON> (character)\">Mrs <PERSON></a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mrs. <PERSON> (character)", "link": "https://wikipedia.org/wiki/Mrs._<PERSON><PERSON>_(character)"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Indonesian engineer and politician, 1st President of Indonesia (d. 1970)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a>, Indonesian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sukarno\" title=\"Sukarno\"><PERSON><PERSON><PERSON></a>, Indonesian engineer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Indonesia\" title=\"President of Indonesia\">President of Indonesia</a> (d. 1970)", "links": [{"title": "Sukar<PERSON>", "link": "https://wikipedia.org/wiki/Sukarno"}, {"title": "President of Indonesia", "link": "https://wikipedia.org/wiki/President_of_Indonesia"}]}, {"year": "1902", "text": "<PERSON>, American saxophonist and bandleader (d. 1947)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and bandleader (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Armenian composer and conductor (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Aram_<PERSON>hachatur<PERSON>\" title=\"Aram Khacha<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> composer and conductor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> composer and conductor (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aram_Khachaturian"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}]}, {"year": "1906", "text": "<PERSON>, German mathematician and academic who is noted for <PERSON><PERSON>'s Lemma (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> August <PERSON>\"><PERSON> <PERSON></a>, German mathematician and academic who is noted for <a href=\"https://wikipedia.org/wiki/<PERSON>orn%27s_Lemma\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s Lemma\"><PERSON><PERSON>'s Lemma</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"Max August <PERSON>\"><PERSON> <PERSON></a>, German mathematician and academic who is noted for <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Lemma\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s Lemma\"><PERSON><PERSON>'s Lemma</a> (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_August_<PERSON>"}, {"title": "<PERSON><PERSON>'s Lemma", "link": "https://wikipedia.org/wiki/Zorn%27s_Lemma"}]}, {"year": "1907", "text": "<PERSON>, American baseball player and manager who played in eight World Series, winning seven (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager who played in eight <a href=\"https://wikipedia.org/wiki/World_Series\" title=\"World Series\">World Series</a>, winning seven (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager who played in eight <a href=\"https://wikipedia.org/wiki/World_Series\" title=\"World Series\">World Series</a>, winning seven (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Series", "link": "https://wikipedia.org/wiki/World_Series"}]}, {"year": "1909", "text": "<PERSON>, Latvian-English historian and philosopher (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Isaiah_Berlin\" title=\"Isaiah Berlin\"><PERSON> Berlin</a>, Latvian-English historian and philosopher (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isaiah_Berlin\" title=\"Isaiah Berlin\"><PERSON> Berlin</a>, Latvian-English historian and philosopher (d. 1997)", "links": [{"title": "Isaiah Berlin", "link": "https://wikipedia.org/wiki/Isaiah_Berlin"}]}, {"year": "1915", "text": "<PERSON>, American pianist and composer (d. 1987)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, Nigerien academic and politician, 1st President of Niger (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Nigerien</a> academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Niger\" class=\"mw-redirect\" title=\"President of Niger\">President of Niger</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Niger\" title=\"Niger\">Nigerien</a> academic and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Niger\" class=\"mw-redirect\" title=\"President of Niger\">President of Niger</a> (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Niger", "link": "https://wikipedia.org/wiki/Niger"}, {"title": "President of Niger", "link": "https://wikipedia.org/wiki/President_of_Niger"}]}, {"year": "1917", "text": "<PERSON>, American businessman, founded the Tracinda Corporation (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Tracinda_Corporation\" class=\"mw-redirect\" title=\"Tracinda Corporation\">Tracinda Corporation</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Tracinda_Corporation\" class=\"mw-redirect\" title=\"Tracinda Corporation\">Tracinda Corporation</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_<PERSON>n"}, {"title": "Tracinda Corporation", "link": "https://wikipedia.org/wiki/Tracinda_Corporation"}]}, {"year": "1918", "text": "<PERSON>, English comedy actor (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedy actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedy actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1919", "text": "<PERSON>, 6th Baron <PERSON>, English army officer and politician, 6th Secretary General of NATO (d. 2018)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English army officer and politician, 6th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English army officer and politician, 6th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 2018)", "links": [{"title": "<PERSON>, 6th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Baron_<PERSON>"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1923", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author, illustrator, and painter (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, illustrator, and painter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, illustrator, and painter (d. 1986)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian broadcaster (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian broadcaster (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian broadcaster (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American poet and author (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and author (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American soldier and politician, 4th Vice President of the Navajo Nation and a noted code talker during World War II (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation\" title=\"Vice President of the Navajo Nation\">Vice President of the Navajo Nation</a> and a noted <a href=\"https://wikipedia.org/wiki/Code_talker\" title=\"Code talker\">code talker</a> during World War II (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation\" title=\"Vice President of the Navajo Nation\">Vice President of the Navajo Nation</a> and a noted <a href=\"https://wikipedia.org/wiki/Code_talker\" title=\"Code talker\">code talker</a> during World War II (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the Navajo Nation", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Navajo_Nation"}, {"title": "Code talker", "link": "https://wikipedia.org/wiki/Code_talker"}]}, {"year": "1926", "text": "<PERSON>, German conductor (d. 1998)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Ghanaian photographer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Indian actor, director, producer, and politician (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and politician (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English-Australian cricketer, coach and journalist (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer, coach and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian cricketer, coach and journalist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American colonel, engineer, and astronaut who was the commander of Apollo 15", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut who was the commander of <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut who was the commander of <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apollo 15", "link": "https://wikipedia.org/wiki/Apollo_15"}]}, {"year": "1933", "text": "<PERSON>, Swiss physicist and academic, Nobel Prize laureate (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1934", "text": "<PERSON>, King of the Belgians from 9 August 1993 to 21 July 2013 (abdicated)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Albert II of Belgium\"><PERSON></a>, King of the Belgians from 9 August 1993 to 21 July 2013 (abdicated)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"<PERSON> II of Belgium\"><PERSON></a>, King of the Belgians from 9 August 1993 to 21 July 2013 (abdicated)", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Japanese screenwriter and novelist (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese screenwriter and novelist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese screenwriter and novelist (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian swimmer; winner of two Olympic gold medals in 1956", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer; winner of two <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> gold medals in 1956", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer; winner of two <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> gold medals in 1956", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Indian actor, director, and producer, founded Suresh Productions (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/D._<PERSON>\" title=\"D. Rama<PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer, founded <a href=\"https://wikipedia.org/wiki/Suresh_Productions\" title=\"Suresh Productions\">Suresh Productions</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>\" title=\"D. Rama<PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer, founded <a href=\"https://wikipedia.org/wiki/Suresh_Productions\" title=\"Suresh Productions\">Suresh Productions</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D._Ramanaidu"}, {"title": "Suresh Productions", "link": "https://wikipedia.org/wiki/Suresh_Productions"}]}, {"year": "1936", "text": "<PERSON>, American soul singer; lead vocalist of the Four Tops (d. 2008)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer; lead vocalist of the <a href=\"https://wikipedia.org/wiki/Four_Tops\" title=\"Four Tops\">Four Tops</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer; lead vocalist of the <a href=\"https://wikipedia.org/wiki/Four_Tops\" title=\"Four Tops\">Four Tops</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Four Tops", "link": "https://wikipedia.org/wiki/Four_Tops"}]}, {"year": "1939", "text": "<PERSON>, Dutch pianist and composer (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist and composer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch pianist and composer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Gary_U.S._Bonds\" title=\"Gary U.S. Bonds\"><PERSON>.S<PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gary_U.S._Bonds\" title=\"Gary U.S. Bonds\"><PERSON>.S. <PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>.<PERSON>", "link": "https://wikipedia.org/wiki/Gary_U.S._Bonds"}]}, {"year": "1940", "text": "<PERSON>, Northern Irish rugby player who toured with the British Lions five times", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish rugby player who toured with the <a href=\"https://wikipedia.org/wiki/British_and_Irish_Lions\" class=\"mw-redirect\" title=\"British and Irish Lions\">British Lions</a> five times", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish rugby player who toured with the <a href=\"https://wikipedia.org/wiki/British_and_Irish_Lions\" class=\"mw-redirect\" title=\"British and Irish Lions\">British Lions</a> five times", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "British and Irish Lions", "link": "https://wikipedia.org/wiki/British_and_Irish_Lions"}]}, {"year": "1943", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate in 1996 for chemistry (d. 2005)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate in 1996 for <a href=\"https://wikipedia.org/wiki/Chemistry\" title=\"Chemistry\">chemistry</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate in 1996 for <a href=\"https://wikipedia.org/wiki/Chemistry\" title=\"Chemistry\">chemistry</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}, {"title": "Chemistry", "link": "https://wikipedia.org/wiki/Chemistry"}]}, {"year": "1944", "text": "<PERSON>, Jamaican jazz pianist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican jazz pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican jazz pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American molecular biologist; 1993 Nobel Prize laureate (Physiology or Medicine)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American molecular biologist; 1993 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (Physiology or Medicine)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American molecular biologist; 1993 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (Physiology or Medicine)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American sprinter and football player; winner of 1968 Olympic 200m gold medal in a world record time", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player; winner of 1968 Olympic 200m gold medal in a world record time", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player; winner of 1968 Olympic 200m gold medal in a world record time", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American bass player and songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, British Labour politician; Home Secretary 2001-2004", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour</a> politician; <a href=\"https://wikipedia.org/wiki/Home_Secretary\" title=\"Home Secretary\">Home Secretary</a> 2001-2004", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour</a> politician; <a href=\"https://wikipedia.org/wiki/Home_Secretary\" title=\"Home Secretary\">Home Secretary</a> 2001-2004", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "Home Secretary", "link": "https://wikipedia.org/wiki/Home_Secretary"}]}, {"year": "1947", "text": "<PERSON>, American actor; best known for Nightmare on Elm Street", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor; best known for <i><a href=\"https://wikipedia.org/wiki/Nightmare_on_Elm_Street\" class=\"mw-redirect\" title=\"Nightmare on Elm Street\">Nightmare on Elm Street</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor; best known for <i><a href=\"https://wikipedia.org/wiki/Nightmare_on_Elm_Street\" class=\"mw-redirect\" title=\"Nightmare on Elm Street\">Nightmare on Elm Street</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nightmare on Elm Street", "link": "https://wikipedia.org/wiki/Nightmare_on_Elm_Street"}]}, {"year": "1947", "text": "<PERSON>, Dutch butterfly stroke swimmer; winner of three Olympic medals including gold in 1968", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch <a href=\"https://wikipedia.org/wiki/Butterfly_stroke\" title=\"Butterfly stroke\">butterfly stroke</a> swimmer; winner of three <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> medals including gold in 1968", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch <a href=\"https://wikipedia.org/wiki/Butterfly_stroke\" title=\"Butterfly stroke\">butterfly stroke</a> swimmer; winner of three <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> medals including gold in 1968", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}, {"title": "Butterfly stroke", "link": "https://wikipedia.org/wiki/Butterfly_stroke"}, {"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}]}, {"year": "1947", "text": "<PERSON>, American convicted rapist and triple murderer (d. 1996)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American convicted rapist and triple murderer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American convicted rapist and triple murderer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American entrepreneur, inventor, investor and policy advocate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON><PERSON> (inventor)\"><PERSON><PERSON></a>, American entrepreneur, inventor, investor and policy advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON><PERSON> (inventor)\"><PERSON><PERSON></a>, American entrepreneur, inventor, investor and policy advocate", "links": [{"title": "<PERSON><PERSON> (inventor)", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>_(inventor)"}]}, {"year": "1949", "text": "<PERSON>, American folk singer and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Near\"><PERSON></a>, American folk singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Near\"><PERSON></a>, American folk singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American pop/rock singer and songwriter (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop/rock singer and songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop/rock singer and songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor and playwright; winner of four Tony Awards", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright; winner of four <a href=\"https://wikipedia.org/wiki/Tony_Awards\" title=\"Tony Awards\">Tony Awards</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright; winner of four <a href=\"https://wikipedia.org/wiki/Tony_Awards\" title=\"Tony Awards\">Tony Awards</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tony Awards", "link": "https://wikipedia.org/wiki/Tony_Awards"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and manager; 91 caps for Poland and voted Best Young Player at the 1974 FIFA World Cup", "html": "1954 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"W<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and manager; 91 caps for Poland and voted Best Young Player at the <a href=\"https://wikipedia.org/wiki/1974_FIFA_World_Cup\" title=\"1974 FIFA World Cup\">1974 FIFA World Cup</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"W<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish footballer and manager; 91 caps for Poland and voted Best Young Player at the <a href=\"https://wikipedia.org/wiki/1974_FIFA_World_Cup\" title=\"1974 FIFA World Cup\">1974 FIFA World Cup</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_Zmuda"}, {"title": "1974 FIFA World Cup", "link": "https://wikipedia.org/wiki/1974_FIFA_World_Cup"}]}, {"year": "1955", "text": "<PERSON>, American director, producer and screenwriter; co-developer of The Simpsons (d. 2015)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer and screenwriter; co-developer of <i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer and screenwriter; co-developer of <i><a href=\"https://wikipedia.org/wiki/The_Simpsons\" title=\"The Simpsons\">The Simpsons</a></i> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Simpsons", "link": "https://wikipedia.org/wiki/The_Simpsons"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish tennis player; winner of eleven Grand Slam singles titles including five consecutive Wimbledons", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish tennis player; winner of eleven <a href=\"https://wikipedia.org/wiki/Grand_Slam_(tennis)\" title=\"Grand Slam (tennis)\">Grand Slam</a> singles titles including five consecutive <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledons</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish tennis player; winner of eleven <a href=\"https://wikipedia.org/wiki/Grand_Slam_(tennis)\" title=\"Grand Slam (tennis)\">Grand Slam</a> singles titles including five consecutive <a href=\"https://wikipedia.org/wiki/The_Championships,_Wimbledon\" class=\"mw-redirect\" title=\"The Championships, Wimbledon\">Wimbledons</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_Borg"}, {"title": "Grand Slam (tennis)", "link": "https://wikipedia.org/wiki/Grand_Slam_(tennis)"}, {"title": "The Championships, Wimbledon", "link": "https://wikipedia.org/wiki/The_Championships,_Wimbledon"}]}, {"year": "1959", "text": "<PERSON>, American comedian and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian endocrinologist involved in scientific misconduct", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian endocrinologist involved in scientific misconduct", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian endocrinologist involved in scientific misconduct", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Ghanaian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American television journalist and NBC News anchor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American television journalist and <a href=\"https://wikipedia.org/wiki/NBC_News\" title=\"NBC News\">NBC News</a> anchor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American television journalist and <a href=\"https://wikipedia.org/wiki/NBC_News\" title=\"NBC News\">NBC News</a> anchor", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}, {"title": "NBC News", "link": "https://wikipedia.org/wiki/NBC_News"}]}, {"year": "1974", "text": "<PERSON>, American musician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Uncle Kracker\">Uncle <PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Uncle Kracker\">Uncle <PERSON></a>, American musician", "links": [{"title": "Uncle <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, British-American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ger\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ger"}]}, {"year": "1977", "text": "<PERSON>, Irish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian football manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian football manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Danish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Swedish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Scottish professional wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American footballer; twice a winner of the FIFA Women's World Cup, also an Olympic gold medallist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer; twice a winner of the <a href=\"https://wikipedia.org/wiki/FIFA_Women%27s_World_Cup\" title=\"FIFA Women's World Cup\">FIFA Women's World Cup</a>, also an <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> gold medallist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer; twice a winner of the <a href=\"https://wikipedia.org/wiki/FIFA_Women%27s_World_Cup\" title=\"FIFA Women's World Cup\">FIFA Women's World Cup</a>, also an <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic</a> gold medallist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FIFA Women's World Cup", "link": "https://wikipedia.org/wiki/FIFA_Women%27s_World_Cup"}, {"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}]}, {"year": "1988", "text": "<PERSON>, Irish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English born footballer who represented Trinidad and Tobago", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born footballer who represented <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago_national_football_team\" title=\"Trinidad and Tobago national football team\">Trinidad and Tobago</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English born footballer who represented <a href=\"https://wikipedia.org/wiki/Trinidad_and_Tobago_national_football_team\" title=\"Trinidad and Tobago national football team\">Trinidad and Tobago</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trinidad and Tobago national football team", "link": "https://wikipedia.org/wiki/Trinidad_and_Tobago_national_football_team"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Pa<PERSON>_<PERSON>uar%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pa<PERSON>_<PERSON>uar%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pape_Souar%C3%A9"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American rapper and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_Mensa"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Swiss footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American soccer player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Haechan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haechan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "Haechan", "link": "https://wikipedia.org/wiki/Haechan"}]}, {"year": "2001", "text": "<PERSON><PERSON>, French-Algerian footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Rayan_A%C3%AFt-<PERSON>uri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rayan_A%C3%AFt-<PERSON>uri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Algerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rayan_A%C3%AFt-Nouri"}]}], "Deaths": [{"year": "184", "text": "<PERSON><PERSON>, Chinese official (b. c. 110)", "html": "184 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese official (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 110</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese official (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 110</span>)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an"}]}, {"year": "863", "text": "<PERSON>, vizier to the Abbasid Caliphate", "html": "863 - <a href=\"https://wikipedia.org/wiki/Utamish\" title=\"Utamish\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a> to the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\">Abbasid Caliphate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Utamish\" title=\"Utamish\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Vizier\" title=\"Vizier\">vizier</a> to the <a href=\"https://wikipedia.org/wiki/Abbasid_Caliphate\" title=\"Abbasid Caliphate\"><PERSON>id Caliphate</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Utamish"}, {"title": "Vizier", "link": "https://wikipedia.org/wiki/Vizier"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abbasid_Caliphate"}]}, {"year": "913", "text": "<PERSON>, Byzantine emperor (b. 870)", "html": "913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)\" title=\"<PERSON> (Byzantine emperor)\"><PERSON> III</a>, Byzantine emperor (b. 870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)\" title=\"<PERSON> (Byzantine emperor)\"><PERSON> III</a>, Byzantine emperor (b. 870)", "links": [{"title": "<PERSON> (Byzantine emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(Byzantine_emperor)"}]}, {"year": "1097", "text": "<PERSON> of Aquitaine, Queen of Aragon and Navarre", "html": "1097 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aquitaine,_Queen_of_Aragon_and_Navarre\" title=\"<PERSON> of Aquitaine, Queen of Aragon and Navarre\"><PERSON> of Aquitaine, Queen of Aragon and Navarre</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Aquitaine,_Queen_of_Aragon_and_Navarre\" title=\"<PERSON> of Aquitaine, Queen of Aragon and Navarre\"><PERSON> of Aquitaine, Queen of Aragon and Navarre</a>", "links": [{"title": "<PERSON> of Aquitaine, Queen of Aragon and Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_A<PERSON>,_Queen_of_Aragon_and_Navarre"}]}, {"year": "1134", "text": "<PERSON><PERSON> of Xanten, German bishop and saint (b. 1060)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Xanten\" title=\"<PERSON><PERSON> of Xanten\"><PERSON><PERSON> of Xanten</a>, German bishop and saint (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Xanten\" title=\"<PERSON><PERSON> of Xanten\"><PERSON><PERSON> of Xanten</a>, German bishop and saint (b. 1060)", "links": [{"title": "<PERSON><PERSON> of Xanten", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Xanten"}]}, {"year": "1217", "text": "<PERSON>, King of Castile and Toledo (b. 1204)", "html": "1217 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">King of Castile and Toledo</a> (b. 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">King of Castile and Toledo</a> (b. 1204)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}]}, {"year": "1251", "text": "<PERSON> of Dampierre, Count of Flanders", "html": "1251 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON> of Dampierre</a>, Count of Flanders", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON> of Dampierre</a>, Count of Flanders", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1252", "text": "<PERSON>, Bishop of Chichester", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Chichester", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bishop of Chichester", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1480", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter, sculptor, and architect (b. 1412)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/V<PERSON>chi<PERSON>\" title=\"V<PERSON>chi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter, sculptor, and architect (b. 1412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>chi<PERSON>\" title=\"V<PERSON>chi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter, sculptor, and architect (b. 1412)", "links": [{"title": "Vecchietta", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>chietta"}]}, {"year": "1548", "text": "<PERSON>, Portuguese soldier and politician, Governor of Portuguese India (b. 1500)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India\" title=\"List of governors of Portuguese India\">Governor of Portuguese India</a> (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India\" title=\"List of governors of Portuguese India\">Governor of Portuguese India</a> (b. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of governors of Portuguese India", "link": "https://wikipedia.org/wiki/List_of_governors_of_Portuguese_India"}]}, {"year": "1583", "text": "<PERSON><PERSON><PERSON>, Japanese daimyo (b. 1556)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yohide\" title=\"Naka<PERSON> Kiyohide\"><PERSON><PERSON><PERSON></a>, Japanese daimyo (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yo<PERSON>de\" title=\"Naka<PERSON> Kiyohide\"><PERSON><PERSON><PERSON></a>, Japanese daimyo (b. 1556)", "links": [{"title": "Nakagawa Kiyohide", "link": "https://wikipedia.org/wiki/Nakagawa_Kiyohide"}]}, {"year": "1661", "text": "<PERSON><PERSON>, Italian Jesuit missionary (b. 1614)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Jesuit missionary (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Jesuit missionary (b. 1614)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, American lawyer and politician, 1st Governor of Virginia (b. 1736)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (b. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a> (b. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1813", "text": "<PERSON>, Maltese architect, engineer and archaeologist (b. 1739)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Antonio_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a>, Maltese architect, engineer and archaeologist (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a>, Maltese architect, engineer and archaeologist (b. 1739)", "links": [{"title": "Antonio <PERSON>", "link": "https://wikipedia.org/wiki/Antonio_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English jurist and philosopher (b. 1748)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and philosopher (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English jurist and philosopher (b. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Count of Cavour, Italian politician, 1st Prime Minister of Italy (b. 1810)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, Italian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, Italian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1810)", "links": [{"title": "<PERSON><PERSON>, Count of Cavour", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1865", "text": "<PERSON>, American Confederate guerrilla band leader (b. 1837)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Confederate guerrilla band leader (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Confederate guerrilla band leader (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Scottish minister and engineer, invented the stirling engine (b. 1790)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and engineer, invented the <a href=\"https://wikipedia.org/wiki/Stirling_engine\" title=\"Stirling engine\">stirling engine</a> (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and engineer, invented the <a href=\"https://wikipedia.org/wiki/Stirling_engine\" title=\"Stirling engine\">stirling engine</a> (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Stirling engine", "link": "https://wikipedia.org/wiki/Stirling_engine"}]}, {"year": "1881", "text": "<PERSON>, Belgian violinist and composer (b. 1820)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist and composer (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Scottish-Canadian lawyer and politician, 1st Prime Minister of Canada (b. 1815)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1916", "text": "<PERSON>, Chinese general and politician, 2nd President of the Republic of China (b. 1859)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Shi<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Shi<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (b. 1859)", "links": [{"title": "Yuan Shikai", "link": "https://wikipedia.org/wiki/Yuan_Shikai"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1922", "text": "<PERSON>, American actress and singer (b. 1860)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, 1st Viscount <PERSON> of Vimy, English field marshal and politician, 12th Governor-General of Canada (b. 1862)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON><PERSON>_of_Vimy\" title=\"<PERSON>, 1st Viscount <PERSON>ng of Vimy\"><PERSON>, 1st Viscount <PERSON> of Vimy</a>, English field marshal and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Canada\" class=\"mw-redirect\" title=\"Governor-General of Canada\">Governor-General of Canada</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON><PERSON>_of_Vimy\" title=\"<PERSON>, 1st Viscount <PERSON><PERSON> of Vimy\"><PERSON>, 1st Viscount <PERSON><PERSON> of Vimy</a>, English field marshal and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Canada\" class=\"mw-redirect\" title=\"Governor-General of Canada\">Governor-General of Canada</a> (b. 1862)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Vimy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_V<PERSON><PERSON>"}, {"title": "Governor-General of Canada", "link": "https://wikipedia.org/wiki/Governor-General_of_Canada"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Meg<PERSON>o-Romanian editor and professor (b. 1883)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Constantin_<PERSON><PERSON>\" title=\"Constantin <PERSON><PERSON>\">Con<PERSON><PERSON></a>, <PERSON><PERSON><PERSON>-Romanian editor and professor (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_<PERSON><PERSON>\" title=\"Constantin <PERSON><PERSON>\">Con<PERSON><PERSON></a>, <PERSON><PERSON>o-Romanian editor and professor (b. 1883)", "links": [{"title": "Constantin <PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON>e"}]}, {"year": "1941", "text": "<PERSON>, American race car driver and businessman, founded Chevrolet and Frontenac Motor Corporation (b. 1878)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Louis_Chevrolet\" title=\"Louis Chevrolet\"><PERSON> Chevrolet</a>, American race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> and <a href=\"https://wikipedia.org/wiki/Frontenac_Motor_Corporation\" title=\"Frontenac Motor Corporation\">Frontenac Motor Corporation</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_Chevrolet\" title=\"Louis Chevrolet\"><PERSON> Chevrolet</a>, American race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> and <a href=\"https://wikipedia.org/wiki/Frontenac_Motor_Corporation\" title=\"Frontenac Motor Corporation\">Frontenac Motor Corporation</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chevrolet", "link": "https://wikipedia.org/wiki/Chevrolet"}, {"title": "Frontenac Motor Corporation", "link": "https://wikipedia.org/wiki/Frontenac_Motor_Corporation"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, German novelist, poet, and playwright, Nobel Prize laureate (b. 1862)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German novelist, poet, and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1947", "text": "<PERSON>, English author and critic (b. 1877)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French film director, producer, and screenwriter (b. 1864)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, French film director, producer, and screenwriter (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, French film director, producer, and screenwriter (b. 1864)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1955", "text": "<PERSON>, Scottish-Australian painter and educator (b. 1875)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian painter and educator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian painter and educator (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>drum"}]}, {"year": "1961", "text": "<PERSON>, Swiss psychiatrist and psychotherapist (b. 1875)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychotherapist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swiss psychiatrist and psychotherapist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French painter (b. 1928)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian motorcycle racer (b. 1934)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian motorcycle racer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American painter and academic (b. 1912)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American soldier, lawyer, and politician, 64th United States Attorney General (b. 1925)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 64th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 64th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American businessman, founded the Getty Oil Company (b. 1892)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil Company</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil Company</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Getty Oil", "link": "https://wikipedia.org/wiki/Getty_Oil"}]}, {"year": "1979", "text": "<PERSON>, American actor (b. 1897)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American poet and academic (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German author, poet, and playwright who wrote the lyrics of <PERSON><PERSON> (b. 1893)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright who wrote the lyrics of <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright who wrote the lyrics of <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American saxophonist and jazz innovator (b. 1927)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and jazz innovator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and jazz innovator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Scottish actor (b. 1935)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American film actor (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American film actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American film actor (b. 1912)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1996", "text": "<PERSON>, American geneticist and immunologist; awarded the Nobel Prize in Physiology or Medicine in 1980 for his studies of histocompatibility (b. 1903)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and immunologist; awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize in Physiology or Medicine</a> in 1980 for his studies of histocompatibility (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and immunologist; awarded the <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize in Physiology or Medicine</a> in 1980 for his studies of histocompatibility (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2005", "text": "<PERSON>, American film actress; winner of the 1963 Academy Award for Best Actress for The Miracle Worker (b. 1931)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actress; winner of the 1963 <a href=\"https://wikipedia.org/wiki/Academy_Award_for_Best_Actress\" title=\"Academy Award for Best Actress\">Academy Award for Best Actress</a> for <i><a href=\"https://wikipedia.org/wiki/The_Miracle_Worker_(1962_film)\" title=\"The Miracle Worker (1962 film)\">The Miracle Worker</a></i> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film actress; winner of the 1963 <a href=\"https://wikipedia.org/wiki/Academy_Award_for_Best_Actress\" title=\"Academy Award for Best Actress\">Academy Award for Best Actress</a> for <i><a href=\"https://wikipedia.org/wiki/The_Miracle_Worker_(1962_film)\" title=\"The Miracle Worker (1962 film)\">The Miracle Worker</a></i> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Academy Award for Best Actress", "link": "https://wikipedia.org/wiki/Academy_Award_for_Best_Actress"}, {"title": "The Miracle Worker (1962 film)", "link": "https://wikipedia.org/wiki/The_Miracle_Worker_(1962_film)"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter, pianist, and actor (b. 1946)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and actor (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, French-Spanish immunologist and academic; awarded the 1980 Nobel Prize in Physiology or Medicine for his studies of the genetic basis of immunological reaction (b. 1916)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Spanish immunologist and academic; awarded the 1980 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize in Physiology or Medicine</a> for his studies of the genetic basis of immunological reaction (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Spanish immunologist and academic; awarded the 1980 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize in Physiology or Medicine</a> for his studies of the genetic basis of immunological reaction (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON>, Russian ice hockey player; together with <PERSON> and <PERSON>, formed the famed KLM Line. (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player; together with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1958)\" title=\"<PERSON> (ice hockey, born 1958)\"><PERSON></a>, formed the famed <i>KLM Line</i>. (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player; together with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1958)\" title=\"<PERSON> (ice hockey, born 1958)\"><PERSON></a>, formed the famed <i>KLM Line</i>. (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (ice hockey, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1958)"}]}, {"year": "2013", "text": "<PERSON>, American crystallographer and academic; awarded the 1985 Nobel Prize in Chemistry for research into the molecular structure of chemical compounds (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crystallographer and academic; awarded the 1985 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize in Chemistry</a> for research into the molecular structure of chemical compounds (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crystallographer and academic; awarded the 1985 <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize in Chemistry</a> for research into the molecular structure of chemical compounds (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2013", "text": "<PERSON>, American swimmer and actress (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, English psychiatrist and physician; pioneered studies of autism (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Wing\"><PERSON><PERSON></a>, English psychiatrist and physician; pioneered studies of <a href=\"https://wikipedia.org/wiki/Autism\" title=\"Autism\">autism</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Wing\"><PERSON><PERSON></a>, English psychiatrist and physician; pioneered studies of <a href=\"https://wikipedia.org/wiki/Autism\" title=\"Autism\">autism</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lorna_Wing"}, {"title": "Autism", "link": "https://wikipedia.org/wiki/Autism"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and author; prosecuting attorney in the Tate-LaBianca murders case (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author; prosecuting attorney in the <a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a> case (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author; prosecuting attorney in the <a href=\"https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders\" title=\"Tate-LaBianca murders\">Tate-LaBianca murders</a> case (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/Tate%E2%80%93LaBianca_murders"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech journalist and author; noted for The Two Thousand Words which inspired the Prague Spring (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk\" title=\"Ludvík Vaculík\"><PERSON><PERSON>v<PERSON>acul<PERSON></a>, Czech journalist and author; noted for <i><a href=\"https://wikipedia.org/wiki/The_Two_Thousand_Words\" title=\"The Two Thousand Words\">The Two Thousand Words</a></i> which inspired the <a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk\" title=\"Ludvík Vaculík\"><PERSON><PERSON><PERSON><PERSON></a>, Czech journalist and author; noted for <i><a href=\"https://wikipedia.org/wiki/The_Two_Thousand_Words\" title=\"The Two Thousand Words\">The Two Thousand Words</a></i> which inspired the <a href=\"https://wikipedia.org/wiki/Prague_Spring\" title=\"Prague Spring\">Prague Spring</a> (b. 1926)", "links": [{"title": "Ludvík Vaculík", "link": "https://wikipedia.org/wiki/Ludv%C3%ADk_Vacul%C3%ADk"}, {"title": "The Two Thousand Words", "link": "https://wikipedia.org/wiki/The_Two_Thousand_Words"}, {"title": "Prague Spring", "link": "https://wikipedia.org/wiki/Prague_Spring"}]}, {"year": "2016", "text": "<PERSON>, Russian chess grandmaster; arguably the best player never to become World Chess Champion (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess grandmaster; arguably the best player never to become <a href=\"https://wikipedia.org/wiki/World_Chess_Champion\" class=\"mw-redirect\" title=\"World Chess Champion\">World Chess Champion</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess grandmaster; arguably the best player never to become <a href=\"https://wikipedia.org/wiki/World_Chess_Champion\" class=\"mw-redirect\" title=\"World Chess Champion\">World Chess Champion</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Chess Champion", "link": "https://wikipedia.org/wiki/World_Chess_Champion"}]}, {"year": "2016", "text": "<PERSON>, English playwright and screenwriter; works included <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter; works included <i><a href=\"https://wikipedia.org/wiki/Equus_(film)\" title=\"<PERSON>qu<PERSON> (film)\">Equus</a></i> and <i><a href=\"https://wikipedia.org/wiki/Amadeus_(film)\" title=\"<PERSON>ade<PERSON> (film)\">Amade<PERSON></a></i> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter; works included <i><a href=\"https://wikipedia.org/wiki/Equus_(film)\" title=\"<PERSON>qu<PERSON> (film)\">Equus</a></i> and <i><a href=\"https://wikipedia.org/wiki/Amadeus_(film)\" title=\"<PERSON>ade<PERSON> (film)\">Amade<PERSON></a></i> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (film)", "link": "https://wikipedia.org/wiki/Equus_(film)"}, {"title": "<PERSON>ade<PERSON> (film)", "link": "https://wikipedia.org/wiki/Amadeus_(film)"}]}]}}