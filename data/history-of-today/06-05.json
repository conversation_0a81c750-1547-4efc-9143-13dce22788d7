{"date": "June 5", "url": "https://wikipedia.org/wiki/June_5", "data": {"Events": [{"year": "1086", "text": "<PERSON><PERSON><PERSON>, brother of Seljuk sultan <PERSON>, defeats <PERSON><PERSON><PERSON> ibn <PERSON>, the Turkish ruler of Anatolia in the battle of Ain Salm.", "html": "1086 - <a href=\"https://wikipedia.org/wiki/Tutush_I\" title=\"Tutush I\"><PERSON><PERSON><PERSON></a>, brother of <a href=\"https://wikipedia.org/wiki/Seljuk_Sultanate\" class=\"mw-redirect\" title=\"Seljuk Sultanate\">Seljuk sultan</a> <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"Malik<PERSON>Shah <PERSON>\"><PERSON></a>, defeats <a href=\"https://wikipedia.org/wiki/Suleiman_ibn_Qutalmish\" title=\"Suleim<PERSON> ibn Qutalmish\"><PERSON><PERSON><PERSON> ibn Qutalmish</a>, the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Rum\" title=\"Sultanate of Rum\">Turkish ruler of Anatolia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Salm\" title=\"Battle of Ain Salm\">battle of Ain Salm</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tutush_I\" title=\"Tutush I\"><PERSON><PERSON><PERSON></a>, brother of <a href=\"https://wikipedia.org/wiki/Seljuk_Sultanate\" class=\"mw-redirect\" title=\"Seljuk Sultanate\">Seljuk sultan</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Malik<PERSON>Shah <PERSON>\"><PERSON></a>, defeats <a href=\"https://wikipedia.org/wiki/Suleiman_ibn_Qutalmish\" title=\"Suleiman ibn Qutalmish\"><PERSON><PERSON><PERSON> ibn Qutalmish</a>, the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Rum\" title=\"Sultanate of Rum\">Turkish ruler of Anatolia</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Ain_Salm\" title=\"Battle of Ain Salm\">battle of Ain Salm</a>.", "links": [{"title": "Tutush I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}, {"title": "Seljuk Sultanate", "link": "https://wikipedia.org/wiki/Seljuk_Sultanate"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Sultanate of Rum", "link": "https://wikipedia.org/wiki/Sultanate_of_Rum"}, {"title": "Battle of Ain Salm", "link": "https://wikipedia.org/wiki/Battle_of_Ain_Salm"}]}, {"year": "1257", "text": "Kraków, in Poland, receives city rights.", "html": "1257 - <a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, in Poland, receives <a href=\"https://wikipedia.org/wiki/City_rights\" class=\"mw-redirect\" title=\"City rights\">city rights</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Krak%C3%B3w\" title=\"Kraków\">Kraków</a>, in Poland, receives <a href=\"https://wikipedia.org/wiki/City_rights\" class=\"mw-redirect\" title=\"City rights\">city rights</a>.", "links": [{"title": "Kraków", "link": "https://wikipedia.org/wiki/Krak%C3%B3w"}, {"title": "City rights", "link": "https://wikipedia.org/wiki/City_rights"}]}, {"year": "1284", "text": "Battle of the Gulf of Naples: <PERSON> of Lauria, admiral to King <PERSON> of Aragon, destroys the Neapolitan fleet and captures <PERSON> of Salerno.", "html": "1284 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Gulf_of_Naples\" title=\"Battle of the Gulf of Naples\">Battle of the Gulf of Naples</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> of Lauria\"><PERSON> of Lauria</a>, admiral to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, destroys the Neapolitan fleet and captures <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Salerno</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Gulf_of_Naples\" title=\"Battle of the Gulf of Naples\">Battle of the Gulf of Naples</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lauria\"><PERSON> of Lauria</a>, admiral to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a>, destroys the Neapolitan fleet and captures <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Salerno</a>.", "links": [{"title": "Battle of the Gulf of Naples", "link": "https://wikipedia.org/wiki/Battle_of_the_Gulf_of_Naples"}, {"title": "<PERSON> of Lauria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples"}]}, {"year": "1288", "text": "The Battle of Worringen ends the War of the Limburg Succession, with <PERSON>, Duke of Brabant, being one of the more important victors.", "html": "1288 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Worringen\" title=\"Battle of Worringen\">Battle of Worringen</a> ends the <a href=\"https://wikipedia.org/wiki/War_of_the_Limburg_Succession\" title=\"War of the Limburg Succession\">War of the Limburg Succession</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_B<PERSON>ant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a>, being one of the more important victors.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Worringen\" title=\"Battle of Worringen\">Battle of Worringen</a> ends the <a href=\"https://wikipedia.org/wiki/War_of_the_Limburg_Succession\" title=\"War of the Limburg Succession\">War of the Limburg Succession</a>, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_B<PERSON>ant\" title=\"<PERSON>, Duke of Brabant\"><PERSON>, Duke of Brabant</a>, being one of the more important victors.", "links": [{"title": "Battle of Worringen", "link": "https://wikipedia.org/wiki/Battle_of_Worringen"}, {"title": "War of the Limburg Succession", "link": "https://wikipedia.org/wiki/War_of_the_Limburg_Succession"}, {"title": "<PERSON>, Duke of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1610", "text": "The masque Tethys' Festival is performed at Whitehall Palace to celebrate the investiture of <PERSON>, Prince of Wales.", "html": "1610 - The masque <a href=\"https://wikipedia.org/wiki/Tethys%27_Festival\" title=\"Tethys' Festival\">Tethys' Festival</a> is performed at <a href=\"https://wikipedia.org/wiki/Whitehall_Palace\" class=\"mw-redirect\" title=\"Whitehall Palace\">Whitehall Palace</a> to celebrate the investiture of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>.", "no_year_html": "The masque <a href=\"https://wikipedia.org/wiki/Tethys%27_Festival\" title=\"Tethys' Festival\">Tethys' Festival</a> is performed at <a href=\"https://wikipedia.org/wiki/Whitehall_Palace\" class=\"mw-redirect\" title=\"Whitehall Palace\">Whitehall Palace</a> to celebrate the investiture of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>.", "links": [{"title": "Tethys' Festival", "link": "https://wikipedia.org/wiki/Tethys%27_Festival"}, {"title": "Whitehall Palace", "link": "https://wikipedia.org/wiki/Whitehall_Palace"}, {"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Wales"}]}, {"year": "1644", "text": "The Qing dynasty's Manchu forces led by the Shunzhi Emperor take Beijing during the collapse of the Ming dynasty.", "html": "1644 - The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>'s <a href=\"https://wikipedia.org/wiki/Manchu_people\" title=\"Manchu people\">Manchu</a> forces led by the <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a> take Beijing during the collapse of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>'s <a href=\"https://wikipedia.org/wiki/Manchu_people\" title=\"Manchu people\">Manchu</a> forces led by the <a href=\"https://wikipedia.org/wiki/Shunzhi_Emperor\" title=\"Shunzhi Emperor\">Shunzhi Emperor</a> take Beijing during the collapse of the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>.", "links": [{"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Manchu people", "link": "https://wikipedia.org/wiki/Manchu_people"}, {"title": "Shunzhi Emperor", "link": "https://wikipedia.org/wiki/Shunz<PERSON>_Emperor"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}]}, {"year": "1794", "text": "Haitian Revolution: Battle of Port-Républicain: British troops capture the capital of Saint-Domingue.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Port-R%C3%A9publicain\" title=\"Battle of Port-Républicain\">Battle of Port-Républicain</a>: British troops capture the capital of <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Port-R%C3%A9publicain\" title=\"Battle of Port-Républicain\">Battle of Port-Républicain</a>: British troops capture the capital of <a href=\"https://wikipedia.org/wiki/Saint-Domingue\" title=\"Saint-Domingue\">Saint-Domingue</a>.", "links": [{"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}, {"title": "Battle of Port-Républicain", "link": "https://wikipedia.org/wiki/Battle_of_Port-R%C3%A9publicain"}, {"title": "Saint-Domingue", "link": "https://wikipedia.org/wiki/Saint-Domingue"}]}, {"year": "1798", "text": "Battle of New Ross: The attempt to spread the United Irish Rebellion into Munster is defeated.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Battle_of_New_Ross_(1798)\" title=\"Battle of New Ross (1798)\">Battle of New Ross</a>: The attempt to spread the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irish Rebellion</a> into <a href=\"https://wikipedia.org/wiki/Munster\" title=\"Munster\">Munster</a> is defeated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_New_Ross_(1798)\" title=\"Battle of New Ross (1798)\">Battle of New Ross</a>: The attempt to spread the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irish Rebellion</a> into <a href=\"https://wikipedia.org/wiki/Munster\" title=\"Munster\">Munster</a> is defeated.", "links": [{"title": "Battle of New Ross (1798)", "link": "https://wikipedia.org/wiki/Battle_of_New_Ross_(1798)"}, {"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Munster", "link": "https://wikipedia.org/wiki/Munster"}]}, {"year": "1817", "text": "The first Great Lakes steamer, the Frontenac, is launched.", "html": "1817 - The first <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> steamer, the <a href=\"https://wikipedia.org/wiki/PS_Frontenac\" title=\"PS Frontenac\"><i>Frontenac</i></a>, is launched.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Great_Lakes\" title=\"Great Lakes\">Great Lakes</a> steamer, the <a href=\"https://wikipedia.org/wiki/PS_Frontenac\" title=\"PS Frontenac\"><i>Frontenac</i></a>, is launched.", "links": [{"title": "Great Lakes", "link": "https://wikipedia.org/wiki/Great_Lakes"}, {"title": "PS Frontenac", "link": "https://wikipedia.org/wiki/PS_Frontenac"}]}, {"year": "1829", "text": "HMS Pickle captures the armed slave ship Voladora off the coast of Cuba.", "html": "1829 - <a href=\"https://wikipedia.org/wiki/HMS_Pickle\" title=\"HMS Pickle\">HMS <i><PERSON>le</i></a> captures the armed slave ship <i>Voladora</i> off the coast of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Pickle\" title=\"HMS Pickle\">HMS <i>Pickle</i></a> captures the armed slave ship <i>Voladora</i> off the coast of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "HMS Pickle", "link": "https://wikipedia.org/wiki/<PERSON>_Pickle"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1832", "text": "The June Rebellion breaks out in Paris in an attempt to overthrow the monarchy of <PERSON>.", "html": "1832 - The <a href=\"https://wikipedia.org/wiki/June_Rebellion\" title=\"June Rebellion\">June Rebellion</a> breaks out in Paris in an attempt to overthrow the monarchy of <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/June_Rebellion\" title=\"June Rebellion\">June Rebellion</a> breaks out in Paris in an attempt to overthrow the monarchy of <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "June Rebellion", "link": "https://wikipedia.org/wiki/June_Rebellion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "Houston is incorporated by the Republic of Texas.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a> is incorporated by the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a> is incorporated by the <a href=\"https://wikipedia.org/wiki/Republic_of_Texas\" title=\"Republic of Texas\">Republic of Texas</a>.", "links": [{"title": "Houston", "link": "https://wikipedia.org/wiki/Houston"}, {"title": "Republic of Texas", "link": "https://wikipedia.org/wiki/Republic_of_Texas"}]}, {"year": "1849", "text": "Denmark becomes a constitutional monarchy by the signing of a new constitution.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> becomes a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a> by the signing of a new <a href=\"https://wikipedia.org/wiki/Constitution_of_Denmark\" title=\"Constitution of Denmark\">constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> becomes a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a> by the signing of a new <a href=\"https://wikipedia.org/wiki/Constitution_of_Denmark\" title=\"Constitution of Denmark\">constitution</a>.", "links": [{"title": "Denmark", "link": "https://wikipedia.org/wiki/Denmark"}, {"title": "Constitutional monarchy", "link": "https://wikipedia.org/wiki/Constitutional_monarchy"}, {"title": "Constitution of Denmark", "link": "https://wikipedia.org/wiki/Constitution_of_Denmark"}]}, {"year": "1851", "text": "<PERSON>'s anti-slavery serial, Uncle <PERSON>'s Cabin, or Life Among the Lowly, starts a ten-month run in the National Era abolitionist newspaper.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s anti-<a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> serial, <i><a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>%27s_Cabin\" title=\"Uncle Tom's Cabin\">Uncle <PERSON>'s <PERSON><PERSON><PERSON></a></i>, or <i>Life Among the Lowly</i>, starts a ten-month run in the <i>National Era</i> <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> newspaper.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Stowe\"><PERSON></a>'s anti-<a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> serial, <i><a href=\"https://wikipedia.org/wiki/Uncle_<PERSON>%27s_Cabin\" title=\"Uncle Tom's Cabin\">Uncle <PERSON>'s Cabin</a></i>, or <i>Life Among the Lowly</i>, starts a ten-month run in the <i>National Era</i> <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolitionist</a> newspaper.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>e"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}, {"title": "Uncle <PERSON>'s Cabin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Cabin"}, {"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}]}, {"year": "1862", "text": "As the Treaty of Saigon is signed, ceding parts of southern Vietnam to France, the guerrilla leader <PERSON><PERSON><PERSON><PERSON><PERSON> decides to defy Emperor <PERSON><PERSON> of Vietnam and fight on against the Europeans.", "html": "1862 - As the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saigon_(1862)\" title=\"Treaty of Saigon (1862)\">Treaty of Saigon</a> is signed, ceding parts of southern <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> to France, the guerrilla leader <a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%E1%BB%8Bnh\" title=\"Trương Định\">Trư<PERSON><PERSON> Định</a> decides to defy Emperor <a href=\"https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c\" title=\"Tự Đức\">Tự Đức</a> of Vietnam and fight on against the Europeans.", "no_year_html": "As the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saigon_(1862)\" title=\"Treaty of Saigon (1862)\">Treaty of Saigon</a> is signed, ceding parts of southern <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> to France, the guerrilla leader <a href=\"https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%E1%BB%8Bnh\" title=\"Trương Định\">Trư<PERSON>ng Định</a> decides to defy Emperor <a href=\"https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c\" title=\"Tự Đức\">T<PERSON>ức</a> of Vietnam and fight on against the Europeans.", "links": [{"title": "Treaty of Saigon (1862)", "link": "https://wikipedia.org/wiki/Treaty_of_Saigon_(1862)"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%C6%B0%C6%A1ng_%C4%90%E1%BB%8Bnh"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%E1%BB%B1_%C4%90%E1%BB%A9c"}]}, {"year": "1864", "text": "American Civil War: Battle of Piedmont: Union forces under General <PERSON> defeat a Confederate army at Piedmont, Virginia, taking nearly 1,000 prisoners.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Piedmont\" title=\"Battle of Piedmont\">Battle of Piedmont</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces under <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army at <a href=\"https://wikipedia.org/wiki/Piedmont,_Augusta_County,_Virginia\" title=\"Piedmont, Augusta County, Virginia\">Piedmont, Virginia</a>, taking nearly 1,000 prisoners.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Piedmont\" title=\"Battle of Piedmont\">Battle of Piedmont</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces under <a href=\"https://wikipedia.org/wiki/General\" class=\"mw-redirect\" title=\"General\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army at <a href=\"https://wikipedia.org/wiki/Piedmont,_Augusta_County,_Virginia\" title=\"Piedmont, Augusta County, Virginia\">Piedmont, Virginia</a>, taking nearly 1,000 prisoners.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Piedmont", "link": "https://wikipedia.org/wiki/Battle_of_Piedmont"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "General", "link": "https://wikipedia.org/wiki/General"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Piedmont, Augusta County, Virginia", "link": "https://wikipedia.org/wiki/Piedmont,_Augusta_County,_Virginia"}]}, {"year": "1873", "text": "Sultan <PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar closes the great slave market under the terms of a treaty with Great Britain.", "html": "1873 - Sultan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a> closes the great slave market under the terms of a treaty with Great Britain.", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a> closes the great slave market under the terms of a treaty with Great Britain.", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON> of Zanzibar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Zanzibar"}]}, {"year": "1883", "text": "The first regularly scheduled Orient Express departs Paris.", "html": "1883 - The first regularly scheduled <a href=\"https://wikipedia.org/wiki/Orient_Express\" title=\"Orient Express\">Orient Express</a> departs Paris.", "no_year_html": "The first regularly scheduled <a href=\"https://wikipedia.org/wiki/Orient_Express\" title=\"Orient Express\">Orient Express</a> departs Paris.", "links": [{"title": "Orient Express", "link": "https://wikipedia.org/wiki/Orient_Express"}]}, {"year": "1888", "text": "The Rio de la Plata earthquake takes place.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/1888_R%C3%ADo_de_la_Plata_earthquake\" title=\"1888 Río de la Plata earthquake\">Rio de la Plata earthquake</a> takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1888_R%C3%ADo_de_la_Plata_earthquake\" title=\"1888 Río de la Plata earthquake\">Rio de la Plata earthquake</a> takes place.", "links": [{"title": "1888 Río de la Plata earthquake", "link": "https://wikipedia.org/wiki/1888_R%C3%ADo_de_la_Plata_earthquake"}]}, {"year": "1893", "text": "The trial of <PERSON> for the murder of her father and step-mother begins in New Bedford, Massachusetts.", "html": "1893 - The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> for the murder of her father and step-mother begins in New Bedford, Massachusetts.", "no_year_html": "The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> for the murder of her father and step-mother begins in New Bedford, Massachusetts.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "Second Boer War: British soldiers take Pretoria.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: British soldiers take <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: British soldiers take <a href=\"https://wikipedia.org/wiki/Pretoria\" title=\"Pretoria\">Pretoria</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Pretoria", "link": "https://wikipedia.org/wiki/Pretoria"}]}, {"year": "1915", "text": "Denmark amends its constitution to allow women's suffrage.", "html": "1915 - Denmark amends its <a href=\"https://wikipedia.org/wiki/Constitution_of_Denmark\" title=\"Constitution of Denmark\">constitution</a> to allow <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">women's suffrage</a>.", "no_year_html": "Denmark amends its <a href=\"https://wikipedia.org/wiki/Constitution_of_Denmark\" title=\"Constitution of Denmark\">constitution</a> to allow <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage\" title=\"Women's suffrage\">women's suffrage</a>.", "links": [{"title": "Constitution of Denmark", "link": "https://wikipedia.org/wiki/Constitution_of_Denmark"}, {"title": "Women's suffrage", "link": "https://wikipedia.org/wiki/Women%27s_suffrage"}]}, {"year": "1916", "text": "<PERSON> is sworn in as a Justice of the United States Supreme Court; he is the first American Jew to hold such a position.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as a <a href=\"https://wikipedia.org/wiki/Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Justice of the United States Supreme Court\">Justice of the United States Supreme Court</a>; he is the first <a href=\"https://wikipedia.org/wiki/History_of_Jews_in_the_United_States\" class=\"mw-redirect\" title=\"History of Jews in the United States\">American Jew</a> to hold such a position.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as a <a href=\"https://wikipedia.org/wiki/Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Justice of the United States Supreme Court\">Justice of the United States Supreme Court</a>; he is the first <a href=\"https://wikipedia.org/wiki/History_of_Jews_in_the_United_States\" class=\"mw-redirect\" title=\"History of Jews in the United States\">American Jew</a> to hold such a position.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Brandeis"}, {"title": "Justice of the United States Supreme Court", "link": "https://wikipedia.org/wiki/Justice_of_the_United_States_Supreme_Court"}, {"title": "History of Jews in the United States", "link": "https://wikipedia.org/wiki/History_of_Jews_in_the_United_States"}]}, {"year": "1916", "text": "World War I: The Arab Revolt against the Ottoman Empire breaks out.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> breaks out.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Arab_Revolt\" title=\"Arab Revolt\">Arab Revolt</a> against the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> breaks out.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Arab Revolt", "link": "https://wikipedia.org/wiki/Arab_Revolt"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}]}, {"year": "1917", "text": "World War I: Conscription begins in the United States as \"Army registration day\".", "html": "1917 - World War I: <a href=\"https://wikipedia.org/wiki/Selective_Service_Act_of_1917\" title=\"Selective Service Act of 1917\">Conscription</a> begins in the United States as \"Army registration day\".", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Selective_Service_Act_of_1917\" title=\"Selective Service Act of 1917\">Conscription</a> begins in the United States as \"Army registration day\".", "links": [{"title": "Selective Service Act of 1917", "link": "https://wikipedia.org/wiki/Selective_Service_Act_of_1917"}]}, {"year": "1940", "text": "World War II: After a brief lull in the Battle of France, the Germans renew the offensive against the remaining French divisions south of the River Somme in Operation Fall Rot (\"Case Red\").", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: After a brief lull in the <a href=\"https://wikipedia.org/wiki/Battle_of_France\" title=\"Battle of France\">Battle of France</a>, the Germans renew the offensive against the remaining French divisions south of the <a href=\"https://wikipedia.org/wiki/River_Somme\" class=\"mw-redirect\" title=\"River Somme\">River Somme</a> in Operation <i><a href=\"https://wikipedia.org/wiki/Fall_Rot\" title=\"Fall Rot\">Fall Rot</a></i> (\"Case Red\").", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: After a brief lull in the <a href=\"https://wikipedia.org/wiki/Battle_of_France\" title=\"Battle of France\">Battle of France</a>, the Germans renew the offensive against the remaining French divisions south of the <a href=\"https://wikipedia.org/wiki/River_Somme\" class=\"mw-redirect\" title=\"River Somme\">River Somme</a> in Operation <i><a href=\"https://wikipedia.org/wiki/Fall_Rot\" title=\"Fall Rot\">Fall Rot</a></i> (\"Case Red\").", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of France", "link": "https://wikipedia.org/wiki/Battle_of_France"}, {"title": "River Somme", "link": "https://wikipedia.org/wiki/River_Somme"}, {"title": "Fall Rot", "link": "https://wikipedia.org/wiki/Fall_Rot"}]}, {"year": "1941", "text": "World War II: Four thousand Chongqing residents are asphyxiated in a bomb shelter during the Bombing of Chongqing.", "html": "1941 - World War II: Four thousand <a href=\"https://wikipedia.org/wiki/Chongqing\" title=\"Chongqing\">Chongqing</a> residents are <a href=\"https://wikipedia.org/wiki/Asphyxia\" title=\"Asphyxia\">asphyxiated</a> in a bomb shelter during the <a href=\"https://wikipedia.org/wiki/Bombing_of_Chongqing\" title=\"Bombing of Chongqing\">Bombing of Chongqing</a>.", "no_year_html": "World War II: Four thousand <a href=\"https://wikipedia.org/wiki/Chongqing\" title=\"Chongqing\">Chongqing</a> residents are <a href=\"https://wikipedia.org/wiki/Asphyxia\" title=\"Asphyxia\">asphyxiated</a> in a bomb shelter during the <a href=\"https://wikipedia.org/wiki/Bombing_of_Chongqing\" title=\"Bombing of Chongqing\">Bombing of Chongqing</a>.", "links": [{"title": "Chongqing", "link": "https://wikipedia.org/wiki/Chongqing"}, {"title": "Asphyxia", "link": "https://wikipedia.org/wiki/Asphyxia"}, {"title": "Bombing of Chongqing", "link": "https://wikipedia.org/wiki/Bombing_of_Chongqing"}]}, {"year": "1942", "text": "World War II: The United States declares war on Bulgaria, Hungary, and Romania.", "html": "1942 - World War II: The United States declares war on <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "no_year_html": "World War II: The United States declares war on <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bulgaria\" class=\"mw-redirect\" title=\"Kingdom of Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a>, and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "links": [{"title": "Kingdom of Bulgaria", "link": "https://wikipedia.org/wiki/Kingdom_of_Bulgaria"}, {"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}]}, {"year": "1944", "text": "World War II: More than 1,000 British bombers drop 5,000 tons of bombs on German gun batteries on the Normandy coast in preparation for D-Day.", "html": "1944 - World War II: More than 1,000 British bombers drop 5,000 tons of bombs on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Atlantic_Wall\" title=\"Atlantic Wall\">gun batteries</a> on the <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a> coast in preparation for <a href=\"https://wikipedia.org/wiki/Normandy_landings\" title=\"Normandy landings\">D-Day</a>.", "no_year_html": "World War II: More than 1,000 British bombers drop 5,000 tons of bombs on <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Atlantic_Wall\" title=\"Atlantic Wall\">gun batteries</a> on the <a href=\"https://wikipedia.org/wiki/Normandy\" title=\"Normandy\">Normandy</a> coast in preparation for <a href=\"https://wikipedia.org/wiki/Normandy_landings\" title=\"Normandy landings\">D-Day</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Atlantic Wall", "link": "https://wikipedia.org/wiki/Atlantic_Wall"}, {"title": "Normandy", "link": "https://wikipedia.org/wiki/Normandy"}, {"title": "Normandy landings", "link": "https://wikipedia.org/wiki/Normandy_landings"}]}, {"year": "1945", "text": "The Allied Control Council, the military occupation governing body of Germany, formally takes power.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/Allied_Control_Council\" title=\"Allied Control Council\">Allied Control Council</a>, the military occupation governing body of Germany, formally takes power.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Allied_Control_Council\" title=\"Allied Control Council\">Allied Control Council</a>, the military occupation governing body of Germany, formally takes power.", "links": [{"title": "Allied Control Council", "link": "https://wikipedia.org/wiki/Allied_Control_Council"}]}, {"year": "1946", "text": "A fire in the La Salle Hotel in Chicago, Illinois, kills 61 people.", "html": "1946 - A fire in the <a href=\"https://wikipedia.org/wiki/La_Salle_Hotel\" title=\"La Salle Hotel\">La Salle Hotel</a> in Chicago, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, kills 61 people.", "no_year_html": "A fire in the <a href=\"https://wikipedia.org/wiki/La_Salle_Hotel\" title=\"La Salle Hotel\">La Salle Hotel</a> in Chicago, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, kills 61 people.", "links": [{"title": "La Salle Hotel", "link": "https://wikipedia.org/wiki/La_Salle_Hotel"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}]}, {"year": "1947", "text": "Cold War: Marshall Plan: In a speech at Harvard University, the United States Secretary of State <PERSON> calls for economic aid to war-torn Europe.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a>: In a speech at <a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for economic aid to war-torn Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Marshall_Plan\" title=\"Marshall Plan\">Marshall Plan</a>: In a speech at <a href=\"https://wikipedia.org/wiki/Harvard_University\" title=\"Harvard University\">Harvard University</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> calls for economic aid to war-torn Europe.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Marshall Plan", "link": "https://wikipedia.org/wiki/Marshall_Plan"}, {"title": "Harvard University", "link": "https://wikipedia.org/wiki/Harvard_University"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "Thailand elects <PERSON><PERSON><PERSON>, the first female member of Thailand's Parliament.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> elects <a href=\"https://wikipedia.org/wiki/Orapin_Chaiyakan\" title=\"Orapin Cha<PERSON>kan\"><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Women_in_Thailand\" title=\"Women in Thailand\">female</a> member of <a href=\"https://wikipedia.org/wiki/Thailand%27s_Parliament\" class=\"mw-redirect\" title=\"Thailand's Parliament\">Thailand's Parliament</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> elects <a href=\"https://wikipedia.org/wiki/Orapin_Chaiyakan\" title=\"Orapin Chaiyakan\"><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Women_in_Thailand\" title=\"Women in Thailand\">female</a> member of <a href=\"https://wikipedia.org/wiki/Thailand%27s_Parliament\" class=\"mw-redirect\" title=\"Thailand's Parliament\">Thailand's Parliament</a>.", "links": [{"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>kan"}, {"title": "Women in Thailand", "link": "https://wikipedia.org/wiki/Women_in_Thailand"}, {"title": "Thailand's Parliament", "link": "https://wikipedia.org/wiki/Thailand%27s_Parliament"}]}, {"year": "1956", "text": "<PERSON> introduces his new single, \"Hound Dog\", on The Milton Berle Show, scandalizing the audience with his suggestive hip movements.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> introduces his new single, \"<a href=\"https://wikipedia.org/wiki/Hound_Dog_(song)\" title=\"Hound Dog (song)\">Hound Dog</a>\", on <i><a href=\"https://wikipedia.org/wiki/The_Milton_Berle_Show\" class=\"mw-redirect\" title=\"The Milton Berle Show\">The <PERSON> Berle Show</a></i>, scandalizing the audience with his suggestive hip movements.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> introduces his new single, \"<a href=\"https://wikipedia.org/wiki/Hound_Dog_(song)\" title=\"Hound Dog (song)\">Hound Dog</a>\", on <i><a href=\"https://wikipedia.org/wiki/The_Milton_Berle_Show\" class=\"mw-redirect\" title=\"The Milton Berle Show\">The Milton Berle Show</a></i>, scandalizing the audience with his suggestive hip movements.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hound Dog (song)", "link": "https://wikipedia.org/wiki/Hound_Dog_(song)"}, {"title": "The Milton Berle Show", "link": "https://wikipedia.org/wiki/The_<PERSON>_<PERSON>_Show"}]}, {"year": "1959", "text": "The first government of Singapore is sworn in.", "html": "1959 - The first government of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> is sworn in.", "no_year_html": "The first government of <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a> is sworn in.", "links": [{"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1960", "text": "The Lake Bodom murders occur in Finland.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Lake_Bodom_murders\" title=\"Lake Bodom murders\">Lake Bodom murders</a> occur in <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lake_Bodom_murders\" title=\"Lake Bodom murders\">Lake Bodom murders</a> occur in <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "Lake Bodom murders", "link": "https://wikipedia.org/wiki/Lake_Bodom_murders"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1963", "text": "The British Secretary of State for War, <PERSON>, resigns in a sex scandal known as the \"<PERSON><PERSON><PERSON> affair\".", "html": "1963 - The British <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resigns in a <a href=\"https://wikipedia.org/wiki/Sex_scandal\" title=\"Sex scandal\">sex scandal</a> known as the \"<a href=\"https://wikipedia.org/wiki/Profumo_affair\" title=\"Profumo affair\">Profumo affair</a>\".", "no_year_html": "The British <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, resigns in a <a href=\"https://wikipedia.org/wiki/Sex_scandal\" title=\"Sex scandal\">sex scandal</a> known as the \"<a href=\"https://wikipedia.org/wiki/Profumo_affair\" title=\"Profumo affair\">Profumo affair</a>\".", "links": [{"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sex scandal", "link": "https://wikipedia.org/wiki/Sex_scandal"}, {"title": "Profumo affair", "link": "https://wikipedia.org/wiki/Profumo_affair"}]}, {"year": "1963", "text": "Movement of 15 Khordad: Protests against the arrest of <PERSON><PERSON><PERSON><PERSON> by the <PERSON> of Iran, <PERSON>. In several cities, masses of angry demonstrators are confronted by tanks and paratroopers.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Movement_of_15_Khordad\" class=\"mw-redirect\" title=\"Movement of 15 Khordad\">Movement of 15 Khordad</a>: Protests against the arrest of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>yato<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Ruhollah_Khomeini\" title=\"Ruhollah Khomeini\"><PERSON><PERSON><PERSON><PERSON>homein<PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. In several cities, masses of angry demonstrators are confronted by tanks and <a href=\"https://wikipedia.org/wiki/Paratrooper\" title=\"Paratrooper\">paratroopers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Movement_of_15_Khordad\" class=\"mw-redirect\" title=\"Movement of 15 Khordad\">Movement of 15 Khordad</a>: Protests against the arrest of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>yato<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Ruhollah_Khomeini\" title=\"Ruhollah Khomeini\"><PERSON><PERSON><PERSON><PERSON>homein<PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. In several cities, masses of angry demonstrators are confronted by tanks and <a href=\"https://wikipedia.org/wiki/Paratrooper\" title=\"Paratrooper\">paratroopers</a>.", "links": [{"title": "Movement of 15 Khordad", "link": "https://wikipedia.org/wiki/Movement_of_15_Khordad"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shah"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Paratrooper", "link": "https://wikipedia.org/wiki/Paratrooper"}]}, {"year": "1964", "text": "DSV Alvin is commissioned.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/DSV_<PERSON>\" title=\"DSV Alvin\">DSV <i><PERSON></i></a> is commissioned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DSV_<PERSON>\" title=\"DSV Alvin\">DSV <i><PERSON></i></a> is commissioned.", "links": [{"title": "DSV Alvin", "link": "https://wikipedia.org/wiki/DSV_Alvin"}]}, {"year": "1967", "text": "The Six-Day War begins: Israel launches surprise strikes against Egyptian air-fields in response to the mobilisation of Egyptian forces on the Israeli border.", "html": "1967 - The <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a> begins: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches surprise strikes against <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> air-fields in response to the mobilisation of Egyptian forces on the Israeli border.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Six-Day_War\" title=\"Six-Day War\">Six-Day War</a> begins: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches surprise strikes against <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> air-fields in response to the mobilisation of Egyptian forces on the Israeli border.", "links": [{"title": "Six-Day War", "link": "https://wikipedia.org/wiki/Six-Day_War"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1968", "text": "Presidential candidate <PERSON> is assassinated by <PERSON><PERSON>.", "html": "1968 - Presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Presidential candidate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">assassinated</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "The Suez Canal opens for the first time since the Six-Day War.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> opens for the first time since the Six-Day War.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> opens for the first time since the Six-Day War.", "links": [{"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1975", "text": "The United Kingdom holds its first country-wide referendum on membership of the European Economic Community (EEC).", "html": "1975 - The United Kingdom holds its first <a href=\"https://wikipedia.org/wiki/1975_United_Kingdom_European_Communities_membership_referendum\" title=\"1975 United Kingdom European Communities membership referendum\">country-wide referendum</a> on membership of the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> (EEC).", "no_year_html": "The United Kingdom holds its first <a href=\"https://wikipedia.org/wiki/1975_United_Kingdom_European_Communities_membership_referendum\" title=\"1975 United Kingdom European Communities membership referendum\">country-wide referendum</a> on membership of the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> (EEC).", "links": [{"title": "1975 United Kingdom European Communities membership referendum", "link": "https://wikipedia.org/wiki/1975_United_Kingdom_European_Communities_membership_referendum"}, {"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}]}, {"year": "1976", "text": "The Teton Dam in Idaho, United States, collapses. Eleven people are killed as a result of flooding.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Teton_Dam\" title=\"Teton Dam\">Teton Dam</a> in <a href=\"https://wikipedia.org/wiki/Idaho\" title=\"Idaho\">Idaho</a>, United States, collapses. Eleven people are killed as a result of flooding.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Teton_Dam\" title=\"Teton Dam\">Teton Dam</a> in <a href=\"https://wikipedia.org/wiki/Idaho\" title=\"Idaho\">Idaho</a>, United States, collapses. Eleven people are killed as a result of flooding.", "links": [{"title": "Teton Dam", "link": "https://wikipedia.org/wiki/Teton_Dam"}, {"title": "Idaho", "link": "https://wikipedia.org/wiki/Idaho"}]}, {"year": "1981", "text": "The Morbidity and Mortality Weekly Report of the Centers for Disease Control and Prevention reports that five people in Los Angeles, California, have a rare form of pneumonia seen only in patients with weakened immune systems, in what turns out to be the first recognized cases of AIDS.", "html": "1981 - The <i><a href=\"https://wikipedia.org/wiki/Morbidity_and_Mortality_Weekly_Report\" title=\"Morbidity and Mortality Weekly Report\">Morbidity and Mortality Weekly Report</a></i> of the <a href=\"https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention\" title=\"Centers for Disease Control and Prevention\">Centers for Disease Control and Prevention</a> reports that five people in Los Angeles, California, have a rare form of <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a> seen only in patients with weakened <a href=\"https://wikipedia.org/wiki/Immune_system\" title=\"Immune system\">immune systems</a>, in what turns out to be the first recognized cases of <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Morbidity_and_Mortality_Weekly_Report\" title=\"Morbidity and Mortality Weekly Report\">Morbidity and Mortality Weekly Report</a></i> of the <a href=\"https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention\" title=\"Centers for Disease Control and Prevention\">Centers for Disease Control and Prevention</a> reports that five people in Los Angeles, California, have a rare form of <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a> seen only in patients with weakened <a href=\"https://wikipedia.org/wiki/Immune_system\" title=\"Immune system\">immune systems</a>, in what turns out to be the first recognized cases of <a href=\"https://wikipedia.org/wiki/AIDS\" class=\"mw-redirect\" title=\"AIDS\">AIDS</a>.", "links": [{"title": "Morbidity and Mortality Weekly Report", "link": "https://wikipedia.org/wiki/Morbidity_and_Mortality_Weekly_Report"}, {"title": "Centers for Disease Control and Prevention", "link": "https://wikipedia.org/wiki/Centers_for_Disease_Control_and_Prevention"}, {"title": "Pneumonia", "link": "https://wikipedia.org/wiki/Pneumonia"}, {"title": "Immune system", "link": "https://wikipedia.org/wiki/Immune_system"}, {"title": "AIDS", "link": "https://wikipedia.org/wiki/AIDS"}]}, {"year": "1983", "text": "More than 100 people are killed when the Russian river cruise ship <PERSON> collides with a girder of the Ulyanovsk Railway Bridge. The collision caused a freight train to derail, further damaging the vessel, yet the ship remained afloat and was eventually restored and returned to service.", "html": "1983 - More than 100 people are killed when the Russian river cruise ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> collides with a girder of the <a href=\"https://wikipedia.org/wiki/Ulyanovsk\" title=\"Ulyanovsk\">Ulyanovsk</a> Railway Bridge. The collision caused a freight train to <a href=\"https://wikipedia.org/wiki/Derailment\" title=\"Derailment\">derail</a>, further damaging the vessel, yet the ship remained afloat and was eventually restored and returned to service.", "no_year_html": "More than 100 people are killed when the Russian river cruise ship <i><a href=\"https://wikipedia.org/wiki/<PERSON>(ship)\" title=\"<PERSON> (ship)\"><PERSON></a></i> collides with a girder of the <a href=\"https://wikipedia.org/wiki/Ulyanovsk\" title=\"Ulyanovsk\">Ulyanovsk</a> Railway Bridge. The collision caused a freight train to <a href=\"https://wikipedia.org/wiki/Derailment\" title=\"Derailment\">derail</a>, further damaging the vessel, yet the ship remained afloat and was eventually restored and returned to service.", "links": [{"title": "<PERSON> (ship)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ship)"}, {"title": "Ulyanovsk", "link": "https://wikipedia.org/wiki/Ulyanovsk"}, {"title": "Derailment", "link": "https://wikipedia.org/wiki/Derailment"}]}, {"year": "1984", "text": "Operation Blue Star: Under orders from India's prime minister, <PERSON><PERSON>, the Indian Army begins an invasion of the Golden Temple, the holiest site of the Sikh religion.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Operation_Blue_Star\" title=\"Operation Blue Star\">Operation Blue Star</a>: Under orders from <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">India's prime minister</a>, <a href=\"https://wikipedia.org/wiki/In<PERSON>_Gandhi\" title=\"In<PERSON> Gandhi\"><PERSON><PERSON> Gandhi</a>, the Indian Army begins an invasion of the <a href=\"https://wikipedia.org/wiki/Harmandir_Sahib\" class=\"mw-redirect\" title=\"Harmandir Sahib\">Golden Temple</a>, the holiest site of the <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikh</a> religion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Blue_Star\" title=\"Operation Blue Star\">Operation Blue Star</a>: Under orders from <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">India's prime minister</a>, <a href=\"https://wikipedia.org/wiki/In<PERSON>_Gandhi\" title=\"In<PERSON> Gandhi\">In<PERSON> Gandhi</a>, the Indian Army begins an invasion of the <a href=\"https://wikipedia.org/wiki/Harmandir_Sahib\" class=\"mw-redirect\" title=\"Harmandir Sahib\">Golden Temple</a>, the holiest site of the <a href=\"https://wikipedia.org/wiki/Sikh\" class=\"mw-redirect\" title=\"Sikh\">Sikh</a> religion.", "links": [{"title": "Operation Blue Star", "link": "https://wikipedia.org/wiki/Operation_Blue_Star"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sikh", "link": "https://wikipedia.org/wiki/Sikh"}]}, {"year": "1989", "text": "The Tank Man halts the progress of a column of advancing tanks for over half an hour after the Tiananmen Square protests of 1989.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/Tank_Man\" title=\"Tank Man\">Tank Man</a> halts the progress of a column of advancing tanks for over half an hour after the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tank_Man\" title=\"Tank Man\">Tank Man</a> halts the progress of a column of advancing tanks for over half an hour after the <a href=\"https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989\" class=\"mw-redirect\" title=\"Tiananmen Square protests of 1989\">Tiananmen Square protests of 1989</a>.", "links": [{"title": "Tank Man", "link": "https://wikipedia.org/wiki/Tank_Man"}, {"title": "Tiananmen Square protests of 1989", "link": "https://wikipedia.org/wiki/Tiananmen_Square_protests_of_1989"}]}, {"year": "1991", "text": "Space Shuttle Columbia is launched on STS-40, the fifth spacelab mission.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-40\" title=\"STS-40\">STS-40</a>, the fifth <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">spacelab</a> mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-40\" title=\"STS-40\">STS-40</a>, the fifth <a href=\"https://wikipedia.org/wiki/Spacelab\" title=\"Spacelab\">spacelab</a> mission.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-40", "link": "https://wikipedia.org/wiki/STS-40"}, {"title": "Spacelab", "link": "https://wikipedia.org/wiki/Spacelab"}]}, {"year": "1993", "text": "Portions of the Holbeck Hall Hotel in Scarborough, North Yorkshire, UK, fall into the sea following a landslide.", "html": "1993 - Portions of the <a href=\"https://wikipedia.org/wiki/Holbeck_Hall_Hotel\" title=\"Holbeck Hall Hotel\">Holbeck Hall Hotel</a> in <a href=\"https://wikipedia.org/wiki/Scarborough,_North_Yorkshire\" title=\"Scarborough, North Yorkshire\">Scarborough, North Yorkshire</a>, UK, fall into the sea following a landslide.", "no_year_html": "Portions of the <a href=\"https://wikipedia.org/wiki/Holbeck_Hall_Hotel\" title=\"Holbeck Hall Hotel\">Holbeck Hall Hotel</a> in <a href=\"https://wikipedia.org/wiki/Scarborough,_North_Yorkshire\" title=\"Scarborough, North Yorkshire\">Scarborough, North Yorkshire</a>, UK, fall into the sea following a landslide.", "links": [{"title": "Holbeck Hall Hotel", "link": "https://wikipedia.org/wiki/Holbeck_Hall_Hotel"}, {"title": "Scarborough, North Yorkshire", "link": "https://wikipedia.org/wiki/Scarborough,_North_Yorkshire"}]}, {"year": "1995", "text": "The Bose-Einstein condensate is first created.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Bose%E2%80%93Einstein_condensate\" title=\"Bose-Einstein condensate\">Bose-Einstein condensate</a> is first created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bose%E2%80%93Einstein_condensate\" title=\"Bose-Einstein condensate\">Bose-Einstein condensate</a> is first created.", "links": [{"title": "Bose-Einstein condensate", "link": "https://wikipedia.org/wiki/Bose%E2%80%93E<PERSON><PERSON>_condensate"}]}, {"year": "1997", "text": "The Second Republic of the Congo Civil War begins.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_Civil_War_(1997%E2%80%931999)\" title=\"Republic of the Congo Civil War (1997-1999)\">Second Republic of the Congo Civil War</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo_Civil_War_(1997%E2%80%931999)\" title=\"Republic of the Congo Civil War (1997-1999)\">Second Republic of the Congo Civil War</a> begins.", "links": [{"title": "Republic of the Congo Civil War (1997-1999)", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo_Civil_War_(1997%E2%80%931999)"}]}, {"year": "1998", "text": "A strike begins at the General Motors parts factory in Flint, Michigan, that quickly spreads to five other assembly plants. The strike lasts seven weeks.", "html": "1998 - A <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> begins at the <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> parts factory in <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a>, that quickly spreads to five other assembly plants. The strike lasts seven weeks.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> begins at the <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> parts factory in <a href=\"https://wikipedia.org/wiki/Flint,_Michigan\" title=\"Flint, Michigan\">Flint, Michigan</a>, that quickly spreads to five other assembly plants. The strike lasts seven weeks.", "links": [{"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}, {"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}, {"title": "Flint, Michigan", "link": "https://wikipedia.org/wiki/Flint,_Michigan"}]}, {"year": "2000", "text": "The Six-Day War in Kisangani begins in Kisangani, in the Democratic Republic of the Congo, between Ugandan and Rwandan forces. A large part of the city is destroyed.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/Six-Day_War_(2000)\" title=\"Six-Day War (2000)\">Six-Day War in Kisangani</a> begins in <a href=\"https://wikipedia.org/wiki/Kisangani\" title=\"Kisangani\">Kisangani</a>, in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, between <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Ugandan</a> and <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> forces. A large part of the city is destroyed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Six-Day_War_(2000)\" title=\"Six-Day War (2000)\">Six-Day War in Kisangani</a> begins in <a href=\"https://wikipedia.org/wiki/Kisangani\" title=\"Kisangani\">Kisangani</a>, in the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>, between <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Ugandan</a> and <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> forces. A large part of the city is destroyed.", "links": [{"title": "Six-Day War (2000)", "link": "https://wikipedia.org/wiki/Six-Day_War_(2000)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}, {"title": "Uganda", "link": "https://wikipedia.org/wiki/Uganda"}, {"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}]}, {"year": "2001", "text": "Tropical Storm <PERSON> makes landfall on the upper-Texas coastline as a strong tropical storm and dumps large amounts of rain over Houston. The storm causes $5.5 billion in damages, making <PERSON> the second costliest tropical storm in U.S. history.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Tropical_Storm_Allison\" title=\"Tropical Storm Allison\">Tropical Storm Allison</a> makes landfall on the upper-<a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coastline as a strong tropical storm and dumps large amounts of rain over Houston. The storm causes $5.5 billion in damages, making <PERSON> the second costliest tropical storm in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tropical_Storm_Allison\" title=\"Tropical Storm Allison\">Tropical Storm Allison</a> makes landfall on the upper-<a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> coastline as a strong tropical storm and dumps large amounts of rain over Houston. The storm causes $5.5 billion in damages, making <PERSON> the second costliest tropical storm in U.S. history.", "links": [{"title": "Tropical Storm Allison", "link": "https://wikipedia.org/wiki/Tropical_Storm_Allison"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "2002", "text": "Space Shuttle Endeavour launches on STS-111, carrying the Expedition 5 crew to the International Space Station to replace the Expedition 4 crew. Astronaut <PERSON> becomes the second person to have flown on seven spaceflights.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-111\" title=\"STS-111\">STS-111</a>, carrying the <a href=\"https://wikipedia.org/wiki/Expedition_5\" title=\"Expedition 5\">Expedition 5</a> crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> to replace the <a href=\"https://wikipedia.org/wiki/Expedition_4\" title=\"Expedition 4\">Expedition 4</a> crew. Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a> becomes the second person to have flown on seven spaceflights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-111\" title=\"STS-111\">STS-111</a>, carrying the <a href=\"https://wikipedia.org/wiki/Expedition_5\" title=\"Expedition 5\">Expedition 5</a> crew to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a> to replace the <a href=\"https://wikipedia.org/wiki/Expedition_4\" title=\"Expedition 4\">Expedition 4</a> crew. Astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a> becomes the second person to have flown on seven spaceflights.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-111", "link": "https://wikipedia.org/wiki/STS-111"}, {"title": "Expedition 5", "link": "https://wikipedia.org/wiki/Expedition_5"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "Expedition 4", "link": "https://wikipedia.org/wiki/Expedition_4"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-D%C3%ADaz"}]}, {"year": "2003", "text": "A severe heat wave across Pakistan and India reaches its peak, as temperatures exceed 50 °C (122 °F) in the region.", "html": "2003 - A severe <a href=\"https://wikipedia.org/wiki/Heat_wave\" title=\"Heat wave\">heat wave</a> across <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> and India reaches its peak, as temperatures exceed 50 °C (122 °F) in the region.", "no_year_html": "A severe <a href=\"https://wikipedia.org/wiki/Heat_wave\" title=\"Heat wave\">heat wave</a> across <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> and India reaches its peak, as temperatures exceed 50 °C (122 °F) in the region.", "links": [{"title": "Heat wave", "link": "https://wikipedia.org/wiki/Heat_wave"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Mayor of Bègles, celebrates marriage for two men for the first time in France.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Mam%C3%A8re\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mayor of Bègles, celebrates <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_France\" title=\"Same-sex marriage in France\">marriage for two men</a> for the first time in France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Mam%C3%A8re\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mayor of Bègles, celebrates <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_France\" title=\"Same-sex marriage in France\">marriage for two men</a> for the first time in France.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Mam%C3%A8re"}, {"title": "Same-sex marriage in France", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_France"}]}, {"year": "2006", "text": "Serbia declares independence from the State Union of Serbia and Montenegro.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">State Union of Serbia and Montenegro</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">State Union of Serbia and Montenegro</a>.", "links": [{"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Serbia_and_Montenegro"}]}, {"year": "2009", "text": "After 65 straight days of civil disobedience, at least 31 people are killed in clashes between security forces and indigenous people near Bagua, Peru.", "html": "2009 - After 65 straight days of civil disobedience, at least 31 people <a href=\"https://wikipedia.org/wiki/2009_Peruvian_political_crisis\" title=\"2009 Peruvian political crisis\">are killed</a> in clashes between security forces and indigenous people near <a href=\"https://wikipedia.org/wiki/Bagua,_Peru\" title=\"Bagua, Peru\">Bagua, Peru</a>.", "no_year_html": "After 65 straight days of civil disobedience, at least 31 people <a href=\"https://wikipedia.org/wiki/2009_Peruvian_political_crisis\" title=\"2009 Peruvian political crisis\">are killed</a> in clashes between security forces and indigenous people near <a href=\"https://wikipedia.org/wiki/Bagua,_Peru\" title=\"Bagua, Peru\">Bagua, Peru</a>.", "links": [{"title": "2009 Peruvian political crisis", "link": "https://wikipedia.org/wiki/2009_Peruvian_political_crisis"}, {"title": "Bagua, Peru", "link": "https://wikipedia.org/wiki/Bagua,_Peru"}]}, {"year": "2012", "text": "Last transit of Venus until the year 2117.", "html": "2012 - Last <a href=\"https://wikipedia.org/wiki/2012_transit_of_Venus\" title=\"2012 transit of Venus\">transit of Venus</a> until the year 2117.", "no_year_html": "Last <a href=\"https://wikipedia.org/wiki/2012_transit_of_Venus\" title=\"2012 transit of Venus\">transit of Venus</a> until the year 2117.", "links": [{"title": "2012 transit of Venus", "link": "https://wikipedia.org/wiki/2012_transit_of_Venus"}]}, {"year": "2015", "text": "An earthquake with a moment magnitude of 6.0 strikes Ranau, Sabah, Malaysia, killing 18 people, including hikers and mountain guides on Mount Kinabalu, after mass landslides that occurred during the earthquake. This is the strongest earthquake to strike Malaysia since 1975.", "html": "2015 - An <a href=\"https://wikipedia.org/wiki/2015_Sabah_earthquake\" title=\"2015 Sabah earthquake\">earthquake with a moment magnitude of 6.0</a> strikes Ranau, Sabah, Malaysia, killing 18 people, including hikers and mountain guides on <a href=\"https://wikipedia.org/wiki/Mount_Kinabalu\" title=\"Mount Kinabalu\">Mount Kinabalu</a>, after mass landslides that occurred during the earthquake. This is the strongest earthquake to strike Malaysia since 1975.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2015_Sabah_earthquake\" title=\"2015 Sabah earthquake\">earthquake with a moment magnitude of 6.0</a> strikes Ranau, Sabah, Malaysia, killing 18 people, including hikers and mountain guides on <a href=\"https://wikipedia.org/wiki/Mount_Kinabalu\" title=\"Mount Kinabalu\">Mount Kinabalu</a>, after mass landslides that occurred during the earthquake. This is the strongest earthquake to strike Malaysia since 1975.", "links": [{"title": "2015 Sabah earthquake", "link": "https://wikipedia.org/wiki/2015_Sabah_earthquake"}, {"title": "Mount Kinabalu", "link": "https://wikipedia.org/wiki/Mount_Kinabalu"}]}, {"year": "2016", "text": "Two shootings in Aktobe, Kazakhstan, kill six people.", "html": "2016 - Two <a href=\"https://wikipedia.org/wiki/2016_Aktobe_shootings\" title=\"2016 Aktobe shootings\">shootings</a> in <a href=\"https://wikipedia.org/wiki/Aktobe\" title=\"Aktobe\">Aktobe</a>, Kazakhstan, kill six people.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/2016_Aktobe_shootings\" title=\"2016 Aktobe shootings\">shootings</a> in <a href=\"https://wikipedia.org/wiki/Aktobe\" title=\"Aktobe\">Aktobe</a>, Kazakhstan, kill six people.", "links": [{"title": "2016 Aktobe shootings", "link": "https://wikipedia.org/wiki/2016_Aktobe_shootings"}, {"title": "Aktobe", "link": "https://wikipedia.org/wiki/Aktobe"}]}, {"year": "2017", "text": "Montenegro becomes the 29th member of NATO.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a> becomes the 29th member of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a> becomes the 29th member of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a>.", "links": [{"title": "Montenegro", "link": "https://wikipedia.org/wiki/Montenegro"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "2017", "text": "Six Arab countries—Bahrain, Egypt, Libya, Saudi Arabia, Yemen, and the United Arab Emirates—cut diplomatic ties with Qatar, accusing it of destabilising the region.", "html": "2017 - Six Arab countries—<a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>, and the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>—<a href=\"https://wikipedia.org/wiki/Qatar_diplomatic_crisis\" title=\"Qatar diplomatic crisis\">cut diplomatic ties</a> with <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, accusing it of destabilising the region.", "no_year_html": "Six Arab countries—<a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>, and the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>—<a href=\"https://wikipedia.org/wiki/Qatar_diplomatic_crisis\" title=\"Qatar diplomatic crisis\">cut diplomatic ties</a> with <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, accusing it of destabilising the region.", "links": [{"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}, {"title": "Qatar diplomatic crisis", "link": "https://wikipedia.org/wiki/Qatar_diplomatic_crisis"}, {"title": "Qatar", "link": "https://wikipedia.org/wiki/Qatar"}]}, {"year": "2022", "text": "A constitutional referendum is held in Kazakhstan following violent protests and civil unrest against the government.", "html": "2022 - A <a href=\"https://wikipedia.org/wiki/2022_Kazakh_constitutional_referendum\" title=\"2022 Kazakh constitutional referendum\">constitutional referendum</a> is held in Kazakhstan following <a href=\"https://wikipedia.org/wiki/2022_Kazakh_unrest\" title=\"2022 Kazakh unrest\">violent protests and civil unrest</a> against the government.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2022_Kazakh_constitutional_referendum\" title=\"2022 Kazakh constitutional referendum\">constitutional referendum</a> is held in Kazakhstan following <a href=\"https://wikipedia.org/wiki/2022_Kazakh_unrest\" title=\"2022 Kazakh unrest\">violent protests and civil unrest</a> against the government.", "links": [{"title": "2022 Kazakh constitutional referendum", "link": "https://wikipedia.org/wiki/2022_Kazakh_constitutional_referendum"}, {"title": "2022 Kazakh unrest", "link": "https://wikipedia.org/wiki/2022_Kazakh_unrest"}]}], "Births": [{"year": "1341", "text": "<PERSON> of <PERSON>, 1st Duke of York, son of King <PERSON> of England and Lord Warden of the Cinque Ports (d. 1402)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York\" title=\"<PERSON>, 1st Duke of York\"><PERSON>, 1st Duke of York</a>, son of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York\" title=\"<PERSON>, 1st Duke of York\"><PERSON> Langley, 1st Duke of York</a>, son of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 1402)", "links": [{"title": "<PERSON>, 1st Duke of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1412", "text": "<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua, Italian ruler (d. 1478)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua</a>, Italian ruler (d. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua</a>, Italian ruler (d. 1478)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Marquis of Mantua", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Marquis_of_Mantua"}]}, {"year": "1493", "text": "<PERSON><PERSON>, German priest and academic (d. 1555)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German priest and academic (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German priest and academic (d. 1555)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1523", "text": "<PERSON> France, Duchess of Berry (d. 1573)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Berry\" class=\"mw-redirect\" title=\"<PERSON> of France, Duchess of Berry\"><PERSON> of France, Duchess of Berry</a> (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Berry\" class=\"mw-redirect\" title=\"<PERSON> of France, Duchess of Berry\"><PERSON> of France, Duchess of Berry</a> (d. 1573)", "links": [{"title": "<PERSON> of France, Duchess of Berry", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_<PERSON>_Berry"}]}, {"year": "1554", "text": "<PERSON><PERSON><PERSON>, Italian clergyman (d. 1621)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian clergyman (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian clergyman (d. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1587", "text": "<PERSON>, 2nd Earl of Warwick, English colonial administrator and admiral (d. 1658)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Warwick\" title=\"<PERSON>, 2nd Earl of Warwick\"><PERSON>, 2nd Earl of Warwick</a>, English colonial administrator and admiral (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Warwick\" title=\"<PERSON>, 2nd Earl of Warwick\"><PERSON>, 2nd Earl of Warwick</a>, English colonial administrator and admiral (d. 1658)", "links": [{"title": "<PERSON>, 2nd Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Warwick"}]}, {"year": "1596", "text": "<PERSON>, Dutch Golden Age painter (d. 1660)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Golden Age painter (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Golden Age painter (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON><PERSON>, Chinese author (d. 1715)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Pu_<PERSON>\" title=\"P<PERSON> Songling\"><PERSON><PERSON></a>, Chinese author (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"P<PERSON> Song<PERSON>\"><PERSON><PERSON></a>, Chinese author (d. 1715)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pu_Songling"}]}, {"year": "1646", "text": "<PERSON>, Italian mathematician and philosopher (d. 1684)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Piscopia"}]}, {"year": "1660", "text": "<PERSON>, Duchess of Marlborough (d. 1744)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Marlborough\" title=\"<PERSON>, Duchess of Marlborough\"><PERSON>, Duchess of Marlborough</a> (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Marlborough\" title=\"<PERSON>, Duchess of Marlborough\"><PERSON>, Duchess of Marlborough</a> (d. 1744)", "links": [{"title": "<PERSON>, Duchess of Marlborough", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Marlborough"}]}, {"year": "1757", "text": "<PERSON>, French physiologist and philosopher (d. 1808)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physiologist and philosopher (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French physiologist and philosopher (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, Finnish chemist, physicist, and mineralogist (d. 1852)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish chemist, physicist, and mineralogist (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish chemist, physicist, and mineralogist (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, King of Hanover (d. 1851)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Hanover\" title=\"<PERSON>, King of Hanover\"><PERSON>, King of Hanover</a> (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Hanover\" title=\"<PERSON>, King of Hanover\"><PERSON>, King of Hanover</a> (d. 1851)", "links": [{"title": "<PERSON>, King of Hanover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Hanover"}]}, {"year": "1781", "text": "<PERSON>, German scholar and academic (d. 1860)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, English architect and engineer (d. 1872)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and engineer (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English mathematician and astronomer (d. 1892)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Italian soldier (d. 1905)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American sheriff (d. 1908)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sheriff (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sheriff (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Swedish ophthalmologist and optician, Nobel Prize laureate (d. 1930)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>strand\" title=\"<PERSON><PERSON> Gullstrand\"><PERSON><PERSON></a>, Swedish ophthalmologist and optician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nd\" title=\"<PERSON><PERSON> Gullstrand\"><PERSON><PERSON></a>, Swedish ophthalmologist and optician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>var_<PERSON>strand"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1868", "text": "<PERSON>, Scottish-born Irish rebel leader (d. 1916)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish rebel leader (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-born Irish rebel leader (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Swiss captain and sailor (d. 1935)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, Swiss captain and sailor (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, Swiss captain and sailor (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pourtal%C3%A8s"}]}, {"year": "1876", "text": "<PERSON>, German-Israeli scholar and academic (d. 1957)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli scholar and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli scholar and academic (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Canadian-American sailor, Medal of Honor recipient (d. 1959)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sailor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American sailor, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Mexican general and politician, Governor of Chihuahua (d. 1923)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\"><PERSON>cho <PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Chihuahua\" title=\"Governor of Chihuahua\">Governor of Chihuahua</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\"><PERSON><PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Chihuahua\" title=\"Governor of Chihuahua\">Governor of Chihuahua</a> (d. 1923)", "links": [{"title": "Pancho <PERSON>", "link": "https://wikipedia.org/wiki/Pancho_Villa"}, {"title": "Governor of Chihuahua", "link": "https://wikipedia.org/wiki/Governor_of_Chihuahua"}]}, {"year": "1879", "text": "<PERSON>, German-English businessman and philanthropist (d. 1985)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, German-English businessman and philanthropist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, German-English businessman and philanthropist (d. 1985)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>_(philanthropist)"}]}, {"year": "1883", "text": "<PERSON>, English economist, philosopher, and academic (d. 1946)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, philosopher, and academic (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, philosopher, and academic (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Scottish nurse and resistance fighter during World War II (d. 1945)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish nurse and resistance fighter during World War II (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish nurse and resistance fighter during World War II (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Czech-Swiss composer (d. 1957)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Swiss composer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Swiss composer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English author (d. 1969)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American runner (d. 1914)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Estonian weightlifter (d. 1944)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian weightlifter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian weightlifter (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, 1st <PERSON> of Fleet, Canadian-English publisher and academic (d. 1976)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 1st Baron <PERSON> of Fleet\"><PERSON>, 1st Baron <PERSON> of Fleet</a>, Canadian-English publisher and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Fleet\" title=\"<PERSON>, 1st Baron <PERSON> of Fleet\"><PERSON>, 1st Baron <PERSON> of Fleet</a>, Canadian-English publisher and academic (d. 1976)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Fleet", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Fleet"}]}, {"year": "1895", "text": "<PERSON>, American actor and producer (d. 1972)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer (d. 1972)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1895", "text": "<PERSON>, English soldier and painter (d. 1980)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English soldier and painter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English soldier and painter (d. 1980)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_(painter)"}]}, {"year": "1898", "text": "<PERSON>, Italian shoe designer, founded Salvatore Ferragamo S.p.A. (d. 1960)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian shoe designer, founded <a href=\"https://wikipedia.org/wiki/Salvatore_Ferragamo_S.p.A.\" class=\"mw-redirect\" title=\"<PERSON> S.p.A.\"><PERSON> S.p.A.</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian shoe designer, founded <a href=\"https://wikipedia.org/wiki/Salvatore_Ferragamo_S.p.A.\" class=\"mw-redirect\" title=\"<PERSON> S.p.A.\">Salvatore <PERSON> S.p.A.</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvatore_<PERSON>gamo"}, {"title": "Salvatore <PERSON>o S.p.A.", "link": "https://wikipedia.org/wiki/Salvatore_Ferragamo_S.p.A."}]}, {"year": "1898", "text": "<PERSON>, Spanish poet, playwright, and director (d. 1936)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Lorca\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, playwright, and director (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Lorca\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, playwright, and director (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Federico_Garc%C3%ADa_Lorca"}]}, {"year": "1899", "text": "<PERSON>, American diver, engineer, and actor, designed the bathysphere (d. 1992)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver, engineer, and actor, designed the <a href=\"https://wikipedia.org/wiki/Bathysphere\" title=\"Bathysphere\">bathysphere</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diver, engineer, and actor, designed the <a href=\"https://wikipedia.org/wiki/Bathysphere\" title=\"Bathysphere\">bathysphere</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bathysphere", "link": "https://wikipedia.org/wiki/Bathysphere"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Burmese writer (d. 1942)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>a\" title=\"Theippan <PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese writer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>a\" title=\"Theippan <PERSON>ung W<PERSON>\"><PERSON><PERSON><PERSON></a>, Burmese writer (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theip<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1900", "text": "<PERSON>, Hungarian-English physicist and engineer, Nobel Prize laureate (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1912", "text": "<PERSON>, American ornithologist and author (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1912", "text": "<PERSON>, English cricketer (d. 1981)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American-Italian painter and academic (d. 2000)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian painter and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian painter and academic (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English archaeologist and academic (d. 2016)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>,  English barrister and biochemist, co-founder of Mensa (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ware\"><PERSON><PERSON></a>, English barrister and biochemist, co-founder of Mensa (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ware\"><PERSON><PERSON></a>, English barrister and biochemist, co-founder of Mensa (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lancelot_Ware"}]}, {"year": "1916", "text": "<PERSON>, Australian cricketer (d. 1973)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American baseball player and manager (d. 2011)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American-Swiss author and illustrator (d. 1994)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss author and illustrator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss author and illustrator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American football player and coach (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Irish-American journalist and author (d. 1974)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American journalist and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American journalist and author (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Dutch-Australian soldier, pilot, and politician (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian soldier, pilot, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Australian soldier, pilot, and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English actress (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Argentinian racing driver (d. 1963)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American organist and composer (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American football player and radio host (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Donovan"}]}, {"year": "1926", "text": "<PERSON>, Hungarian-American engineer and businessman (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American engineer and businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American engineer and businessman (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor (d. 1994)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1928", "text": "<PERSON>, English-American director and producer (d. 1991)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Egyptian author (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Ali<PERSON>_Rifaat\" title=\"<PERSON><PERSON> Rifaat\"><PERSON><PERSON></a>, Egyptian author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ali<PERSON>_Rifaat\" title=\"Ali<PERSON> Rifaat\"><PERSON><PERSON></a>, Egyptian author (d. 1996)", "links": [{"title": "Alifa Rifaat", "link": "https://wikipedia.org/wiki/Alifa_Rifaat"}]}, {"year": "1931", "text": "<PERSON>, Canadian businessman and politician (d. 1998)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, French actor, director, and screenwriter (d. 1990)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Polish anthropologist and philosopher (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish anthropologist and philosopher (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish anthropologist and philosopher (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Irish painter and author (d. 1981)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and author (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and author (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American businessman, founded the 99 Cents Only Stores (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/99_Cents_Only_Stores\" title=\"99 Cents Only Stores\">99 Cents Only Stores</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dave Gold\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/99_Cents_Only_Stores\" title=\"99 Cents Only Stores\">99 Cents Only Stores</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "99 Cents Only Stores", "link": "https://wikipedia.org/wiki/99_Cents_Only_Stores"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Serbian actor and politician (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian actor and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian actor and politician (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic triple jumper, painter, and educator (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Vilhj%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vilhj<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic triple jumper, painter, and educator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vilhj%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vilhj<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Icelandic triple jumper, painter, and educator (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilhj%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American journalist, 13th White House Press Secretary", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 13th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 13th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, French author, poet, and critic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Cixous\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author, poet, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Cixous\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author, poet, and critic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_Cixous"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Scottish singer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German hurdler (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German hurdler (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German hurdler (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian jockey (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, Australian jockey (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, Australian jockey (d. 2014)", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)"}]}, {"year": "1939", "text": "<PERSON>, Canadian journalist and politician, 16th Prime Minister of Canada", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1939", "text": "<PERSON>, English novelist, biographer, and critic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, biographer, and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, biographer, and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Argentinian pianist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Brazilian singer-songwriter (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American writer, actor, and monologist (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American writer, actor, and monologist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spalding_Gray\" title=\"Spalding Gray\"><PERSON><PERSON></a>, American writer, actor, and monologist (d. 2004)", "links": [{"title": "Spalding Gray", "link": "https://wikipedia.org/wiki/Spalding_Gray"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Swedish designer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Sj%C3%B6d%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sj%C3%B6d%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gudrun_Sj%C3%B6d%C3%A9n"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Equatoguinean lieutenant and politician, 2nd President of Equatorial Guinea", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Equatoguinean lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"President of Equatorial Guinea\">President of Equatorial Guinea</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Equatoguinean lieutenant and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"President of Equatorial Guinea\">President of Equatorial Guinea</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Equatorial Guinea", "link": "https://wikipedia.org/wiki/President_of_Equatorial_Guinea"}]}, {"year": "1943", "text": "<PERSON>, Roman Catholic Archbishop of Nagpur, Maharashtra, India (d. 2018)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic Archbishop of Nagpur, Maharashtra, India (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic Archbishop of Nagpur, Maharashtra, India (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American cryptographer and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Whitfield_Di<PERSON>ie\" title=\"Whitfield Diffie\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, American cryptographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whitfield_Di<PERSON>ie\" title=\"Whitfield Diffie\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American cryptographer and academic", "links": [{"title": "<PERSON><PERSON><PERSON> Diffie", "link": "https://wikipedia.org/wiki/Whit<PERSON>_<PERSON><PERSON>ie"}]}, {"year": "1945", "text": "<PERSON>, American runner and football player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Lac<PERSON>x_(ice_hockey)"}]}, {"year": "1946", "text": "<PERSON>, English guitarist (d. 2001)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian rugby league player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1946", "text": "<PERSON>, English engineer and businessman, co-founded Williams F1", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Patrick Head\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Williams_F1\" class=\"mw-redirect\" title=\"Williams F1\">Williams F1</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Patrick Head\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Williams_F1\" class=\"mw-redirect\" title=\"Williams F1\">Williams F1</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Williams F1", "link": "https://wikipedia.org/wiki/Williams_F1"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian singer and television host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Wanderl%C3%A9a\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wanderl%C3%A9a\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer and television host", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wanderl%C3%A9a"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and violinist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter and guitarist (d. 1983)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (d. 1983)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, English director, playwright, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English director, playwright, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English director, playwright, and screenwriter", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>(playwright)"}]}, {"year": "1947", "text": "<PERSON>, American singer, guitarist, and pastor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, guitarist, and pastor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Stone\"><PERSON></a>, American singer, guitarist, and pastor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Welsh author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English lawyer and judge", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, 12th Earl of Dundee, Scottish politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Dundee\" title=\"<PERSON>, 12th Earl of Dundee\"><PERSON>, 12th Earl of Dundee</a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Dundee\" title=\"<PERSON>, 12th Earl of Dundee\"><PERSON>, 12th Earl of Dundee</a>, Scottish politician", "links": [{"title": "<PERSON>, 12th Earl of Dundee", "link": "https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Dundee"}]}, {"year": "1950", "text": "<PERSON>, American singer and actor (d. 1990)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Jr., Filipino journalist and activist (d. 1977)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Filipino journalist and activist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Filipino journalist and activist (d. 1977)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1951", "text": "<PERSON><PERSON>, American financial adviser, author, and television host", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American financial adviser, author, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American financial adviser, author, and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian journalist and news anchor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and news anchor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Canadian journalist and news anchor", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1952", "text": "<PERSON>, American singer  (d. 2001)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, English drummer and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American film producer, co-founded Amblin Entertainment", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" class=\"mw-redirect\" title=\"<PERSON> (film producer)\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Amblin_Entertainment\" title=\"Amblin Entertainment\">Amblin Entertainment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(film_producer)\" class=\"mw-redirect\" title=\"<PERSON> (film producer)\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Amblin_Entertainment\" title=\"Amblin Entertainment\">Amblin Entertainment</a>", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}, {"title": "Amblin Entertainment", "link": "https://wikipedia.org/wiki/Amblin_Entertainment"}]}, {"year": "1954", "text": "<PERSON>, Italian footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English cricketer, coach, and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American model and actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Brazilian footballer and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nazareth_Filho\" class=\"mw-redirect\" title=\"<PERSON><PERSON>zar<PERSON> Filho\"><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Nazareth_Filho\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Nazareth Filho\"><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edino_Nazareth_Fi<PERSON>ho"}]}, {"year": "1956", "text": "<PERSON>, American saxophonist, songwriter, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Moldavian-Israeli politician, Deputy Prime Minister of Israel", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moldavian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Deputy_leaders_of_Israel\" class=\"mw-redirect\" title=\"Deputy leaders of Israel\">Deputy Prime Minister of Israel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moldavian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Deputy_leaders_of_Israel\" class=\"mw-redirect\" title=\"Deputy leaders of Israel\">Deputy Prime Minister of Israel</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy leaders of Israel", "link": "https://wikipedia.org/wiki/Deputy_leaders_of_Israel"}]}, {"year": "1958", "text": "<PERSON>, Comorian businessman and politician, President of Comoros", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Comorian businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Comoros\" class=\"mw-redirect\" title=\"President of Comoros\">President of Comoros</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Comorian businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Comoros\" class=\"mw-redirect\" title=\"President of Comoros\">President of Comoros</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Comoros", "link": "https://wikipedia.org/wiki/President_of_Comoros"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German runner", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English author and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, German heptathlete", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American voice actress (d. 1999)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer and pianist (d. 2006)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Burger\"><PERSON></a>, American singer and pianist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Italian engineer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Aldo_Costa\" title=\"Aldo Costa\"><PERSON><PERSON></a>, Italian engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldo_Costa\" title=\"Aldo Costa\"><PERSON><PERSON></a>, Italian engineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Costa"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indian tennis player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, comedian, director, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Estonian historian and politician, 34th Estonian Minister of Education", "html": "1962 - <a href=\"https://wikipedia.org/wiki/T%C3%B5<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian historian and politician, 34th <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5<PERSON>_Lukas"}, {"title": "Minister of Education and Research (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)"}]}, {"year": "1964", "text": "<PERSON>, American director and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American astronomer and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, French soprano", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American sprinter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English lawyer and politician, Minister for Culture, Communications and Creative Industries", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Culture,_Communications_and_Creative_Industries\" class=\"mw-redirect\" title=\"Minister for Culture, Communications and Creative Industries\">Minister for Culture, Communications and Creative Industries</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Culture,_Communications_and_Creative_Industries\" class=\"mw-redirect\" title=\"Minister for Culture, Communications and Creative Industries\">Minister for Culture, Communications and Creative Industries</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ed_<PERSON>y"}, {"title": "Minister for Culture, Communications and Creative Industries", "link": "https://wikipedia.org/wiki/Minister_for_Culture,_Communications_and_Creative_Industries"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Martin_<PERSON>%C3%A9lina<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_G%C3%A9linas"}]}, {"year": "1971", "text": "<PERSON>, Northern Irish actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American model, actor, producer, and rapper", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, producer, and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actor, producer, and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian priest and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian priest and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian priest and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Polish conductor and academic", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ko<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish conductor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Ko<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish conductor and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON><PERSON>a"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American boxer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Belgian martial artist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_Van<PERSON>veye"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Trinidadian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian tennis player and golfer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian-American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>as_<PERSON>gauskas\" title=\"<PERSON>ydrunas Ilgauskas\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>gau<PERSON>\" title=\"<PERSON><PERSON><PERSON>as Ilgauska<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player", "links": [{"title": "Zydrunas Ilgauskas", "link": "https://wikipedia.org/wiki/Zydrunas_Ilgauskas"}]}, {"year": "1975", "text": "<PERSON>, English drummer and keyboard player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Belgian runner", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American comedian", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Canadian basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1978", "text": "<PERSON>, American actor and comedian", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Portuguese footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Greek footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, bass player, actor, and fashion designer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, actor, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, actor, and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_racing_driver)\" title=\"<PERSON> (American racing driver)\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_racing_driver)\" title=\"<PERSON> (American racing driver)\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON> (American racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_racing_driver)"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1980", "text": "<PERSON>, Spanish racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Spanish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Spanish racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Serhat_Ak%C4%B1n\" title=\"Ser<PERSON> Akın\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serhat_Ak%C4%B1n\" title=\"Serhat Akın\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Serhat_Ak%C4%B1n"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American trombonist (d. 2005)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American trombonist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American trombonist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian-Italian rugby player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American figure skater", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)\" title=\"<PERSON> (basketball, born 1987)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)\" title=\"<PERSON> (basketball, born 1987)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1987)"}]}, {"year": "1988", "text": "<PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey defenceman", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey defenceman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey defenceman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/S%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B6ren_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American professional gamer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(gamer)\" title=\"Ninja (gamer)\"><PERSON></a>, American professional gamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ninja_(gamer)\" title=\"Ninja (gamer)\"><PERSON></a>, American professional gamer", "links": [{"title": "<PERSON> (gamer)", "link": "https://wikipedia.org/wiki/<PERSON>_(gamer)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Joazhi%C3%B1o_Arroe\" title=\"<PERSON><PERSON><PERSON><PERSON>rro<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joazhi%C3%B1o_Arroe\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joazhi%C3%B1o_Arroe"}]}, {"year": "1992", "text": "<PERSON>, Australian swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>-<PERSON><PERSON>, Samoan-New Zealand rugby league player ", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby league player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-New Zealand rugby league player ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South African-born Australian singer-songwriter, actor, and YouTuber", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-born Australian singer-songwriter, actor, and <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-born Australian singer-songwriter, actor, and <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Troye_Sivan"}, {"title": "YouTuber", "link": "https://wikipedia.org/wiki/YouTuber"}]}, {"year": "1995", "text": "<PERSON>, English table tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)\" title=\"<PERSON> (table tennis)\"><PERSON></a>, English table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)\" title=\"<PERSON> (table tennis)\"><PERSON></a>, English table tennis player", "links": [{"title": "<PERSON> (table tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(table_tennis)"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Clague\" title=\"<PERSON><PERSON> Clague\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Clague\" title=\"<PERSON><PERSON> Clague\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ue"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Romanian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_C<PERSON>ian\" title=\"<PERSON><PERSON><PERSON> C<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_C<PERSON>ian\" title=\"<PERSON><PERSON><PERSON> C<PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ian"}]}, {"year": "1998", "text": "<PERSON>, British rapper", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, British rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, British rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>(rapper)"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Russian figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Li<PERSON>nitskaya\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Li<PERSON>nitsk<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yulia_Lipnitskaya"}]}], "Deaths": [{"year": "301", "text": "<PERSON><PERSON>, Chinese emperor (b. 249)", "html": "301 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lu<PERSON>\" title=\"<PERSON><PERSON> Lun\"><PERSON><PERSON></a>, Chinese emperor (b. 249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lu<PERSON>\"><PERSON><PERSON></a>, Chinese emperor (b. 249)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "535", "text": "<PERSON><PERSON><PERSON><PERSON>, patriarch of Constantinople", "html": "535 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON></a>, patriarch of Constantinople", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Constantinople\" title=\"<PERSON><PERSON><PERSON><PERSON> of Constantinople\"><PERSON><PERSON><PERSON><PERSON></a>, patriarch of Constantinople", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Epiphanius_of_Constantinople"}]}, {"year": "567", "text": "<PERSON><PERSON><PERSON>, patriarch of Alexandria", "html": "567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON></a>, patriarch of Alexandria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_I_of_Alexandria\" title=\"<PERSON> <PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON> I</a>, patriarch of Alexandria", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I_of_Alexandria"}]}, {"year": "708", "text": "<PERSON> of Edessa, Syrian bishop (b. 640)", "html": "708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Edessa\" title=\"<PERSON> of Edessa\"><PERSON> of Edessa</a>, Syrian bishop (b. 640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ed<PERSON>\" title=\"<PERSON> of Edessa\"><PERSON> of Edessa</a>, Syrian bishop (b. 640)", "links": [{"title": "<PERSON> of Edessa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "754", "text": "<PERSON><PERSON><PERSON>, bishop of Utrecht", "html": "754 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of Utrecht", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, bishop of Utrecht", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oban"}]}, {"year": "754", "text": "<PERSON><PERSON><PERSON>, English missionary and martyr (b. 675)", "html": "754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bon<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, English missionary and martyr (b. 675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bon<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English missionary and martyr (b. 675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Boniface"}]}, {"year": "879", "text": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON>, Persian emir (b. 840)", "html": "879 - <a href=\"https://wikipedia.org/wiki/Ya%27qub_ibn_al-<PERSON><PERSON>_al-Sa<PERSON>ar\" title=\"Ya'qub ibn al-<PERSON><PERSON> al-Sa<PERSON>ar\">Ya'qub ibn <PERSON></a>, Persian emir (b. 840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27qub_ibn_al-<PERSON><PERSON>_al-Sa<PERSON>ar\" title=\"Ya'qub ibn al-Lay<PERSON> al-Saffar\">Ya'qub ibn <PERSON></a>, Persian emir (b. 840)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya%27qub_<PERSON>_<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "928", "text": "<PERSON> the <PERSON>, king of Provence", "html": "928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_<PERSON>\" title=\"<PERSON> the Blind\"><PERSON> the <PERSON></a>, king of Provence", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Blind\"><PERSON></a>, king of Provence", "links": [{"title": "Louis the Blind", "link": "https://wikipedia.org/wiki/<PERSON>_the_Blind"}]}, {"year": "1017", "text": "<PERSON><PERSON>, emperor of Japan (b. 976)", "html": "1017 - <a href=\"https://wikipedia.org/wiki/Emperor_Sanj%C5%8D\" title=\"Emperor Sanjō\"><PERSON><PERSON></a>, emperor of Japan (b. 976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Sanj%C5%8D\" title=\"Emperor Sanjō\"><PERSON><PERSON></a>, emperor of Japan (b. 976)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Sanj%C5%8D"}]}, {"year": "1118", "text": "<PERSON>, 1st Earl of Leicester, Norman nobleman and politician (b. 1049)", "html": "1118 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, Norman nobleman and politician (b. 1049)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, Norman nobleman and politician (b. 1049)", "links": [{"title": "<PERSON>, 1st Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Leicester"}]}, {"year": "1296", "text": "<PERSON>, English politician, Lord Warden of the Cinque Ports (b. 1245)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1245)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1245)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1310", "text": "<PERSON><PERSON><PERSON>, prince of Tyre", "html": "1310 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Lord_of_Tyre\" title=\"<PERSON><PERSON><PERSON>, Lord of Tyre\"><PERSON><PERSON><PERSON></a>, prince of Tyre", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Lord_of_Tyre\" title=\"<PERSON><PERSON><PERSON>, Lord of Tyre\"><PERSON><PERSON><PERSON></a>, prince of Tyre", "links": [{"title": "<PERSON><PERSON><PERSON>, Lord of Tyre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Lord_of_Tyre"}]}, {"year": "1316", "text": "<PERSON>, king of France (b. 1289)", "html": "1316 - <a href=\"https://wikipedia.org/wiki/Louis_X_of_France\" title=\"Louis X of France\"><PERSON></a>, king of France (b. 1289)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_X_of_France\" title=\"Louis <PERSON> of France\"><PERSON></a>, king of France (b. 1289)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_X_of_France"}]}, {"year": "1383", "text": "<PERSON> of Suzdal, Russian grand prince (b. 1324)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Suzdal\" title=\"Dmitry of Suzdal\">Dmitry of Suzdal</a>, Russian grand prince (b. 1324)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Suzdal\" title=\"Dmitry of Suzdal\">Dmitry of Suzdal</a>, Russian grand prince (b. 1324)", "links": [{"title": "Dmitry of Suzdal", "link": "https://wikipedia.org/wiki/<PERSON>_of_Suzdal"}]}, {"year": "1400", "text": "<PERSON>, duke of Brunswick-Lüneburg", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON></a>, duke of Brunswick-Lüneburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON></a>, duke of Brunswick-Lüneburg", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1424", "text": "<PERSON><PERSON><PERSON>, Italian nobleman (b. 1368)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Montone\" title=\"<PERSON><PERSON><PERSON> da Montone\"><PERSON><PERSON><PERSON> Montone</a>, Italian nobleman (b. 1368)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_da_Montone\" title=\"<PERSON><PERSON><PERSON> da Montone\"><PERSON><PERSON><PERSON> Montone</a>, Italian nobleman (b. 1368)", "links": [{"title": "<PERSON><PERSON><PERSON> da Montone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1434", "text": "<PERSON>, Russian grand prince (b. 1374)", "html": "1434 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" class=\"mw-redirect\" title=\"<PERSON> IV\"><PERSON></a>, Russian grand prince (b. 1374)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV\" class=\"mw-redirect\" title=\"<PERSON> IV\"><PERSON> IV</a>, Russian grand prince (b. 1374)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_IV"}]}, {"year": "1443", "text": "<PERSON>, Portuguese prince (b. 1402)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Holy_Prince\" title=\"<PERSON> the Holy Prince\"><PERSON></a>, Portuguese prince (b. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Holy_Prince\" title=\"<PERSON> the Holy Prince\"><PERSON></a>, Portuguese prince (b. 1402)", "links": [{"title": "<PERSON> the Holy Prince", "link": "https://wikipedia.org/wiki/<PERSON>_the_Holy_Prince"}]}, {"year": "1445", "text": "<PERSON><PERSON>, English composer", "html": "1445 - <a href=\"https://wikipedia.org/wiki/Leonel_Power\" title=\"Leonel Power\"><PERSON><PERSON></a>, English composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leonel_Power\" title=\"Leonel Power\"><PERSON><PERSON> Power</a>, English composer", "links": [{"title": "Leonel Power", "link": "https://wikipedia.org/wiki/Leonel_Power"}]}, {"year": "1530", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian statesman and jurist (b. 1465)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/Mercuri<PERSON>_Gattinara\" class=\"mw-redirect\" title=\"Me<PERSON><PERSON><PERSON> Gatt<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian statesman and jurist (b. 1465)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON>uri<PERSON>_<PERSON>ra\" class=\"mw-redirect\" title=\"Me<PERSON><PERSON><PERSON> Gattinara\"><PERSON><PERSON><PERSON><PERSON></a>, Italian statesman and jurist (b. 1465)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON><PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1568", "text": "<PERSON><PERSON>, Count of Egmont (b. 1522)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont\" title=\"<PERSON><PERSON>, Count of Egmont\"><PERSON><PERSON>, Count of Egmont</a> (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont\" title=\"<PERSON><PERSON>, Count of Egmont\"><PERSON><PERSON>, Count of Egmont</a> (b. 1522)", "links": [{"title": "<PERSON><PERSON>, Count of Egmont", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Count_of_Egmont"}]}, {"year": "1625", "text": "<PERSON>, English organist and composer (b. 1583)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbons\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Gibbons\" title=\"<PERSON> Gibbons\"><PERSON></a>, English organist and composer (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Gibbons"}]}, {"year": "1667", "text": "<PERSON>, Italian cardinal and historian (b. 1607)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and historian (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rz<PERSON>\"><PERSON></a>, Italian cardinal and historian (b. 1607)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON><PERSON><PERSON>, Syriac Orthodox Patriarch of Antioch (b. 1648)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ignatius <PERSON>\"><PERSON><PERSON><PERSON> II</a>, Syriac Orthodox Patriarch of Antioch (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ignatius <PERSON>\"><PERSON><PERSON><PERSON> II</a>, Syriac Orthodox Patriarch of Antioch (b. 1648)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, English mathematician and academic (b. 1682)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, German organist and composer (b. 1660)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, French pastor and theologian (b. 1659)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (b. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, 1st Duke of Kent, English politician and courtier (b. 1671)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Kent\" title=\"<PERSON>, 1st Duke of Kent\"><PERSON>, 1st Duke of Kent</a>, English politician and courtier (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Kent\" title=\"<PERSON>, 1st Duke of Kent\"><PERSON>, 1st Duke of Kent</a>, English politician and courtier (b. 1671)", "links": [{"title": "<PERSON>, 1st Duke of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Kent"}]}, {"year": "1791", "text": "<PERSON>, Swiss-Canadian general and politician, 22nd Governor of Quebec (b. 1718)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Canadian general and politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor of Quebec</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Canadian general and politician, 22nd <a href=\"https://wikipedia.org/wiki/List_of_Governors_General_of_Canada\" class=\"mw-redirect\" title=\"List of Governors General of Canada\">Governor of Quebec</a> (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors General of Canada", "link": "https://wikipedia.org/wiki/List_of_Governors_General_of_Canada"}]}, {"year": "1816", "text": "<PERSON>, Italian composer and educator (b. 1741)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek soldier (b. 1788)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Odysseas_Androutsos\" title=\"Odysseas Androutsos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek soldier (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odysseas_Androutsos\" title=\"Odysseas Androutsos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek soldier (b. 1788)", "links": [{"title": "O<PERSON>sseas And<PERSON>sos", "link": "https://wikipedia.org/wiki/Odysseas_Androutsos"}]}, {"year": "1826", "text": "<PERSON>, German pianist, composer, and conductor (b. 1786)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Scottish explorer and surveyor (b. 1815)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish explorer and surveyor (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish explorer and surveyor (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Filipino general (b. 1866)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a>, Filipino general (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Antonio Luna\"><PERSON></a>, Filipino general (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American poet, novelist, and short story writer (b. 1871)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German philosopher and author (b. 1842)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American short story writer (b. 1862)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American short story writer (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German-American businessman (b. 1851)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, 1st <PERSON>, Irish-born British field marshal and politician, Secretary of State for War (b. 1850)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Irish-born British field marshal and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON>, 1st <PERSON></a>, Irish-born British field marshal and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (b. 1850)", "links": [{"title": "<PERSON>, 1st <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>"}, {"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Welsh-English author (b. 1840)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>ho<PERSON>_B<PERSON>ton\" title=\"<PERSON><PERSON><PERSON> Broughton\"><PERSON><PERSON><PERSON></a>, Welsh-English author (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_B<PERSON>ton\" title=\"<PERSON><PERSON><PERSON> Broughton\"><PERSON><PERSON><PERSON></a>, Welsh-English author (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English trade unionist and politician (b. 1852)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade unionist and politician (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade unionist and politician (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>s"}]}, {"year": "1921", "text": "<PERSON>, French playwright (b. 1862)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Swedish athlete (b. 1880)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish athlete (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Bulgarian-French painter and illustrator (b. 1885)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Pascin\" class=\"mw-redirect\" title=\"Pa<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian-French painter and illustrator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pascin\" class=\"mw-redirect\" title=\"Pas<PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian-French painter and illustrator (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pascin"}]}, {"year": "1934", "text": "<PERSON>, Australian philanthropist (b. 1842)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philanthropist (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philanthropist (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English-Australian politician, 19th Premier of New South Wales (b. 1871)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Swedish-American actor and director (b. 1884)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actor and director (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actor and director (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English author, poet, and playwright (b. 1881)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Israeli philologist, philosopher, and academic (b. 1878)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli philologist, philosopher, and academic (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli philologist, philosopher, and academic (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian public servant (b. 1878)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (b. 1878)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1933)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Twitty\"><PERSON></a>, American singer-songwriter and guitarist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Twitty\"><PERSON></a>, American singer-songwriter and guitarist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>witty"}]}, {"year": "1996", "text": "A<PERSON>, Indian poet and scholar (b. 1933)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Acharya_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Acharya <PERSON>\">Acharya <PERSON></a>, Indian poet and scholar (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Acharya <PERSON>\">Acharya <PERSON></a>, Indian poet and scholar (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON>, American journalist and author (b. 1933)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American actress (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American soldier and politician, 37th Mayor of Los Angeles (b. 1909)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 37th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter (b. 1925)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Mel_Torm%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel_Torm%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Torm%C3%A9"}]}, {"year": "2000", "text": "<PERSON>, American baseball player (b. 1925)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter and bass player (b. 1951)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, German soldier and politician, 10th Vice-Chancellor of Germany (b. 1945)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_M%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_M%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_M%C3%B6<PERSON><PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "2003", "text": "<PERSON>, French composer and conductor (b. 1904)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English violinist and conductor (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brown\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brown\" title=\"<PERSON>\"><PERSON></a>, English violinist and conductor (b. 1941)", "links": [{"title": "Iona Brown", "link": "https://wikipedia.org/wiki/Iona_Brown"}]}, {"year": "2004", "text": "<PERSON>, American actor and politician, 40th President of the United States (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician, 40th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician, 40th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Mexican scholar and politician (b. 1949)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Z%C3%ADnser\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican scholar and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Z%C3%ADnser\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican scholar and politician (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_Aguilar_Z%C3%ADnser"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Singaporean judge (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Wee Chong Jin\"><PERSON><PERSON></a>, Singaporean judge (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Wee Chong Jin\"><PERSON><PERSON><PERSON></a>, Singaporean judge (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Dutch-American painter, sculptor, and author (b. 1909)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American painter, sculptor, and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American painter, sculptor, and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American businessman (b. 1928)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1978)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Bangladeshi singer-songwriter (b. 1950)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Bangladeshi singer-songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Bangladeshi singer-songwriter (b. 1950)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "2012", "text": "<PERSON>, American science fiction writer and screenwriter  (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player and manager (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Romanian-American computer scientist (b. 1982)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C4%83tra%C8%99<PERSON>_(computer_scientist)\" title=\"<PERSON><PERSON> (computer scientist)\"><PERSON><PERSON></a>, Romanian-American computer scientist (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C4%83tra%C8%99<PERSON>_(computer_scientist)\" title=\"<PERSON><PERSON> (computer scientist)\"><PERSON><PERSON></a>, Romanian-American computer scientist (b. 1982)", "links": [{"title": "<PERSON><PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_P%C4%83tra%C8%99cu_(computer_scientist)"}]}, {"year": "2012", "text": "<PERSON>, Australian footballer and coach (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Scottish politician (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Polish cardinal (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Nagy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cardinal (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Nagy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish cardinal (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Nagy"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish republican activist and politician (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Ruair%C3%AD_%C3%93_Br%C3%A1daigh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish republican activist and politician (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruair%C3%AD_%C3%93_Br%C3%A1daigh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Irish republican activist and politician (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruair%C3%AD_%C3%93_Br%C3%A1daigh"}]}, {"year": "2013", "text": "<PERSON>, Belgian physiologist and physician (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian physiologist and physician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian physiologist and physician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Iraqi commander (b. 1971)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi commander (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi commander (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American songwriter and producer (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American songwriter and producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)\" title=\"<PERSON> (record producer)\"><PERSON></a>, American songwriter and producer (b. 1938)", "links": [{"title": "<PERSON> (record producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(record_producer)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist and politician, Norwegian Minister of Transport and Communications (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_and_Communications_(Norway)\" class=\"mw-redirect\" title=\"Minister of Transport and Communications (Norway)\">Norwegian Minister of Transport and Communications</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Transport_and_Communications_(Norway)\" class=\"mw-redirect\" title=\"Minister of Transport and Communications (Norway)\">Norwegian Minister of Transport and Communications</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Transport and Communications (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Transport_and_Communications_(Norway)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Iraqi journalist and politician, Iraqi Minister of Foreign Affairs (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Iraq)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Iraq)\">Iraqi Minister of Foreign Affairs</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi journalist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Iraq)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs (Iraq)\">Iraqi Minister of Foreign Affairs</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Iraq)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Iraq)"}]}, {"year": "2015", "text": "<PERSON>, English-Australian businessman (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, English-Australian businessman (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, English-Australian businessman (b. 1938)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>(businessman)"}]}, {"year": "2015", "text": "<PERSON>, English actor (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2015", "text": "<PERSON>, French chef and author (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French chef and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French chef and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roger_Verg%C3%A9"}]}, {"year": "2016", "text": "<PERSON>, American psychologist (b. 1915)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, English actor (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1950)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer (b. 1986)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Ch<PERSON><PERSON>_Tiot%C3%A9\" title=\"Ch<PERSON><PERSON> Tioté\"><PERSON><PERSON><PERSON></a>, Ivorian footballer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tiot%C3%A9\" title=\"<PERSON><PERSON><PERSON> Tioté\"><PERSON><PERSON><PERSON></a>, Ivorian footballer (b. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cheick_Tiot%C3%A9"}]}, {"year": "2018", "text": "<PERSON>, American fashion designer (b. 1962)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>de\"><PERSON></a>, American fashion designer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Spade\"><PERSON></a>, American fashion designer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON> <PERSON><PERSON>, Nigerian televangelist (b. 1963)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"T<PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian televangelist (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Nigerian televangelist (b. 1963)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Brazilian singer (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Astrud_<PERSON>"}]}]}}