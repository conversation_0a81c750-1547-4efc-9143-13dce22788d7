{"date": "November 21", "url": "https://wikipedia.org/wiki/November_21", "data": {"Events": [{"year": "164 BCE", "text": "<PERSON><PERSON>, son of <PERSON><PERSON><PERSON> of the Hasmonean family, rededicates the Temple in Jerusalem, an event that is commemorated each year by the festival of Hanukkah. (25 Kislev 3597 in the Hebrew calendar.)", "html": "164 BCE - 164 BCE - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>be<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Mattathias\" title=\"<PERSON>ath<PERSON>\">Matt<PERSON>ias</a> of the <a href=\"https://wikipedia.org/wiki/Hasmonean_family\" class=\"mw-redirect\" title=\"Hasmonean family\">Hasmonean family</a>, rededicates the <a href=\"https://wikipedia.org/wiki/Temple_in_Jerusalem\" title=\"Temple in Jerusalem\">Temple in Jerusalem</a>, an event that is commemorated each year by the festival of <a href=\"https://wikipedia.org/wiki/Hanukkah\" title=\"Hanukkah\">Hanukkah</a>. (25 <a href=\"https://wikipedia.org/wiki/Kislev\" title=\"Kislev\">Kislev</a> 3597 in the <a href=\"https://wikipedia.org/wiki/Hebrew_calendar\" title=\"Hebrew calendar\">Hebrew calendar</a>.)", "no_year_html": "164 BCE - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, son of <a href=\"https://wikipedia.org/wiki/Mattathias\" title=\"Mattath<PERSON>\">Mattathias</a> of the <a href=\"https://wikipedia.org/wiki/Hasmonean_family\" class=\"mw-redirect\" title=\"Hasmonean family\">Hasmonean family</a>, rededicates the <a href=\"https://wikipedia.org/wiki/Temple_in_Jerusalem\" title=\"Temple in Jerusalem\">Temple in Jerusalem</a>, an event that is commemorated each year by the festival of <a href=\"https://wikipedia.org/wiki/Hanukkah\" title=\"Hanukkah\">Hanukkah</a>. (25 <a href=\"https://wikipedia.org/wiki/Kislev\" title=\"Kislev\">Kislev</a> 3597 in the <a href=\"https://wikipedia.org/wiki/Hebrew_calendar\" title=\"Hebrew calendar\">Hebrew calendar</a>.)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mattathias"}, {"title": "Hasmonean family", "link": "https://wikipedia.org/wiki/Hasmonean_family"}, {"title": "Temple in Jerusalem", "link": "https://wikipedia.org/wiki/Temple_in_Jerusalem"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hanukkah"}, {"title": "Kislev", "link": "https://wikipedia.org/wiki/Kislev"}, {"title": "Hebrew calendar", "link": "https://wikipedia.org/wiki/Hebrew_calendar"}]}, {"year": "235", "text": "<PERSON> <PERSON><PERSON><PERSON> succeeds <PERSON><PERSON> as the nineteenth pope.", "html": "235 - <a href=\"https://wikipedia.org/wiki/Pope_Anterus\" title=\"Pope Anterus\">Pope <PERSON>ter<PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Pontian\" title=\"Pope Pontian\"><PERSON><PERSON></a> as the nineteenth <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Anterus\" title=\"Pope Anterus\">Pope <PERSON>ter<PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Pontian\" title=\"Pope Pontian\"><PERSON><PERSON></a> as the nineteenth <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Anterus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Pontian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}]}, {"year": "1386", "text": "<PERSON><PERSON> of Samarkand captures and sacks the Georgian capital of Tbilisi, taking King <PERSON><PERSON><PERSON> of Georgia captive.", "html": "1386 - <a href=\"https://wikipedia.org/wiki/Timur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> of Samarkand</a> <a href=\"https://wikipedia.org/wiki/Timur%27s_invasions_of_Georgia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s invasions of Georgia\">captures and sacks</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Georgian</a> capital of <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, taking King <a href=\"https://wikipedia.org/wiki/Bagrat_V_of_Georgia\" title=\"Bagrat V of Georgia\">Bagrat V of Georgia</a> captive.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> of Samarkand</a> <a href=\"https://wikipedia.org/wiki/Timur%27s_invasions_of_Georgia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>'s invasions of Georgia\">captures and sacks</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Georgian</a> capital of <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>, taking King <a href=\"https://wikipedia.org/wiki/Bagrat_V_of_Georgia\" title=\"Bagrat V of Georgia\">Bagrat V of Georgia</a> captive.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "<PERSON><PERSON>'s invasions of Georgia", "link": "https://wikipedia.org/wiki/Timur%27s_invasions_of_Georgia"}, {"title": "Kingdom of Georgia", "link": "https://wikipedia.org/wiki/Kingdom_of_Georgia"}, {"title": "Tbilisi", "link": "https://wikipedia.org/wiki/Tbilisi"}, {"title": "Bagrat V of Georgia", "link": "https://wikipedia.org/wiki/Bagrat_V_of_Georgia"}]}, {"year": "1620", "text": "Plymouth Colony settlers sign the Mayflower Compact (November 11, O.S.)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> <a href=\"https://wikipedia.org/wiki/List_of_Mayflower_passengers\" title=\"List of Mayflower passengers\">settlers</a> sign the <a href=\"https://wikipedia.org/wiki/Mayflower_Compact\" title=\"Mayflower Compact\">Mayflower Compact</a> (<a href=\"https://wikipedia.org/wiki/November_11\" title=\"November 11\">November 11</a>, <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">Plymouth Colony</a> <a href=\"https://wikipedia.org/wiki/List_of_Mayflower_passengers\" title=\"List of Mayflower passengers\">settlers</a> sign the <a href=\"https://wikipedia.org/wiki/Mayflower_Compact\" title=\"Mayflower Compact\">Mayflower Compact</a> (<a href=\"https://wikipedia.org/wiki/November_11\" title=\"November 11\">November 11</a>, <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>)", "links": [{"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}, {"title": "List of Mayflower passengers", "link": "https://wikipedia.org/wiki/List_of_Mayflower_passengers"}, {"title": "Mayflower Compact", "link": "https://wikipedia.org/wiki/Mayflower_Compact"}, {"title": "November 11", "link": "https://wikipedia.org/wiki/November_11"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1676", "text": "The Danish astronomer <PERSON> presents the first quantitative measurements of the speed of light.", "html": "1676 - The Danish <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/R%C3%B8mer%27s_determination_of_the_speed_of_light\" title=\"<PERSON><PERSON><PERSON>'s determination of the speed of light\">presents</a> the first quantitative <a href=\"https://wikipedia.org/wiki/Measurement\" title=\"Measurement\">measurements</a> of the <a href=\"https://wikipedia.org/wiki/Speed_of_light\" title=\"Speed of light\">speed of light</a>.", "no_year_html": "The Danish <a href=\"https://wikipedia.org/wiki/Astronomer\" title=\"Astronomer\">astronomer</a> <a href=\"https://wikipedia.org/wiki/Ole_R%C3%B8mer\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/R%C3%B8mer%27s_determination_of_the_speed_of_light\" title=\"<PERSON><PERSON><PERSON>'s determination of the speed of light\">presents</a> the first quantitative <a href=\"https://wikipedia.org/wiki/Measurement\" title=\"Measurement\">measurements</a> of the <a href=\"https://wikipedia.org/wiki/Speed_of_light\" title=\"Speed of light\">speed of light</a>.", "links": [{"title": "Astronomer", "link": "https://wikipedia.org/wiki/Astronomer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ole_R%C3%B8mer"}, {"title": "<PERSON><PERSON><PERSON>'s determination of the speed of light", "link": "https://wikipedia.org/wiki/R%C3%B8mer%27s_determination_of_the_speed_of_light"}, {"title": "Measurement", "link": "https://wikipedia.org/wiki/Measurement"}, {"title": "Speed of light", "link": "https://wikipedia.org/wiki/Speed_of_light"}]}, {"year": "1783", "text": "In Paris, <PERSON><PERSON><PERSON> and <PERSON> make the first untethered hot air balloon flight.", "html": "1783 - In Paris, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%A2<PERSON>_de_Rozier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>%27Arlandes\" title=\"<PERSON>\"><PERSON></a> make the first untethered <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> flight.", "no_year_html": "In Paris, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%<PERSON><PERSON>_de_Rozier\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>%27Arlandes\" title=\"<PERSON>\"><PERSON></a> make the first untethered <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> flight.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Pil%C3%<PERSON><PERSON>_<PERSON>_R<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27Arlandes"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1789", "text": "North Carolina ratifies the United States Constitution and is admitted as the 12th U.S. state.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> ratifies the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> and is admitted as the 12th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> ratifies the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> and is admitted as the 12th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1851", "text": "Mutineers take control of the Chilean penal colony of Punta Arenas in the Strait of Magellan.", "html": "1851 - Mutineers <a href=\"https://wikipedia.org/wiki/Mutiny_of_Cambiazo\" title=\"Mutiny of Cambiazo\">take control</a> of the Chilean <a href=\"https://wikipedia.org/wiki/Penal_colony\" title=\"Penal colony\">penal colony</a> of <a href=\"https://wikipedia.org/wiki/Punta_Arenas\" title=\"Punta Arenas\">Punta Arenas</a> in the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "no_year_html": "Mutineers <a href=\"https://wikipedia.org/wiki/Mutiny_of_Cambiazo\" title=\"Mutiny of Cambiazo\">take control</a> of the Chilean <a href=\"https://wikipedia.org/wiki/Penal_colony\" title=\"Penal colony\">penal colony</a> of <a href=\"https://wikipedia.org/wiki/Punta_Arenas\" title=\"Punta Arenas\">Punta Arenas</a> in the <a href=\"https://wikipedia.org/wiki/Strait_of_Magellan\" title=\"Strait of Magellan\">Strait of Magellan</a>.", "links": [{"title": "Mutiny of Cambiazo", "link": "https://wikipedia.org/wiki/Mutiny_of_Cambiazo"}, {"title": "Penal colony", "link": "https://wikipedia.org/wiki/Penal_colony"}, {"title": "Punta Arenas", "link": "https://wikipedia.org/wiki/Punta_Arenas"}, {"title": "Strait of Magellan", "link": "https://wikipedia.org/wiki/Strait_of_Magellan"}]}, {"year": "1861", "text": "American Civil War: Confederate President <PERSON> appoints <PERSON> Secretary of War.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> Secretary of War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> Secretary of War.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON> announces his invention of the phonograph, a machine that can record and play sound.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> announces his invention of the <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a>, a machine that can record and play sound.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Edison\"><PERSON></a> announces his invention of the <a href=\"https://wikipedia.org/wiki/Phonograph\" title=\"Phonograph\">phonograph</a>, a machine that can record and play sound.", "links": [{"title": "Thomas <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Phonograph", "link": "https://wikipedia.org/wiki/Phonograph"}]}, {"year": "1894", "text": "Port Arthur, China, falls to the Japanese, a decisive victory of the First Sino-Japanese War; Japanese troops are accused of massacring the remaining inhabitants.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Port_Arthur,_China\" class=\"mw-redirect\" title=\"Port Arthur, China\">Port Arthur, China</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Lushunkou\" class=\"mw-redirect\" title=\"Battle of Lushunkou\">falls to the Japanese</a>, a decisive victory of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>; Japanese troops are accused of <a href=\"https://wikipedia.org/wiki/Port_Arthur_massacre_(China)\" title=\"Port Arthur massacre (China)\">massacring</a> the remaining inhabitants.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Port_Arthur,_China\" class=\"mw-redirect\" title=\"Port Arthur, China\">Port Arthur, China</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Lushunkou\" class=\"mw-redirect\" title=\"Battle of Lushunkou\">falls to the Japanese</a>, a decisive victory of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>; Japanese troops are accused of <a href=\"https://wikipedia.org/wiki/Port_Arthur_massacre_(China)\" title=\"Port Arthur massacre (China)\">massacring</a> the remaining inhabitants.", "links": [{"title": "Port Arthur, China", "link": "https://wikipedia.org/wiki/Port_Arthur,_China"}, {"title": "Battle of Lushunkou", "link": "https://wikipedia.org/wiki/Battle_of_Lushunkou"}, {"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}, {"title": "Port Arthur massacre (China)", "link": "https://wikipedia.org/wiki/Port_Arthur_massacre_(China)"}]}, {"year": "1900", "text": "<PERSON>'s paintings shown at Gallery Durand-Ruel in Paris.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s paintings shown at Gallery Durand-Ruel in Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s paintings shown at Gallery Durand-Ruel in Paris.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "The Philadelphia Football Athletics defeat the Kanaweola Athletic Club of Elmira, New York, 39-0, in the first-ever professional American football night game.", "html": "1902 - The <a href=\"https://wikipedia.org/wiki/Philadelphia_Athletics_(NFL)\" title=\"Philadelphia Athletics (NFL)\">Philadelphia Football Athletics</a> defeat the <a href=\"https://wikipedia.org/wiki/Kanaweola_Athletic_Club\" title=\"Kanaweola Athletic Club\">Kanaweola Athletic Club</a> of <a href=\"https://wikipedia.org/wiki/Elmira,_New_York\" title=\"Elmira, New York\">Elmira, New York</a>, 39-0, in the first-ever professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> <a href=\"https://wikipedia.org/wiki/Night_game\" title=\"Night game\">night game</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philadelphia_Athletics_(NFL)\" title=\"Philadelphia Athletics (NFL)\">Philadelphia Football Athletics</a> defeat the <a href=\"https://wikipedia.org/wiki/Kanaweola_Athletic_Club\" title=\"Kanaweola Athletic Club\">Kanaweola Athletic Club</a> of <a href=\"https://wikipedia.org/wiki/Elmira,_New_York\" title=\"Elmira, New York\">Elmira, New York</a>, 39-0, in the first-ever professional <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> <a href=\"https://wikipedia.org/wiki/Night_game\" title=\"Night game\">night game</a>.", "links": [{"title": "Philadelphia Athletics (NFL)", "link": "https://wikipedia.org/wiki/Philadelphia_Athletics_(NFL)"}, {"title": "Kanaweola Athletic Club", "link": "https://wikipedia.org/wiki/Kanaweola_Athletic_Club"}, {"title": "Elmira, New York", "link": "https://wikipedia.org/wiki/Elmira,_New_York"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}, {"title": "Night game", "link": "https://wikipedia.org/wiki/Night_game"}]}, {"year": "1905", "text": "<PERSON>'s paper that leads to the mass-energy equivalence formula, E = mc², is published in the journal Annalen der Physik.", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Einstein\"><PERSON></a>'s paper that leads to the <a href=\"https://wikipedia.org/wiki/Mass%E2%80%93energy_equivalence\" title=\"Mass-energy equivalence\">mass-energy equivalence</a> formula, <i>E</i> = <i>mc</i>², is published in the journal <i>Annalen der Physik</i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert Einstein\"><PERSON></a>'s paper that leads to the <a href=\"https://wikipedia.org/wiki/Mass%E2%80%93energy_equivalence\" title=\"Mass-energy equivalence\">mass-energy equivalence</a> formula, <i>E</i> = <i>mc</i>², is published in the journal <i>Annalen der Physik</i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mass-energy equivalence", "link": "https://wikipedia.org/wiki/Mass%E2%80%93energy_equivalence"}]}, {"year": "1910", "text": "Sailors on board Brazil's warships including the Minas Gerais, São Paulo, and Bahia, violently rebel in what is now known as the Revolta da Chibata (Revolt of the Lash).", "html": "1910 - Sailors on board Brazil's warships including the <a href=\"https://wikipedia.org/wiki/Brazilian_battleship_Minas_Geraes\" title=\"Brazilian battleship Minas Geraes\"><i>Minas Gerais</i></a>, <a href=\"https://wikipedia.org/wiki/Brazilian_battleship_S%C3%A3o_Paulo\" title=\"Brazilian battleship São Paulo\"><i>São Paulo</i></a>, and <a href=\"https://wikipedia.org/wiki/Brazilian_cruiser_Bahia\" title=\"Brazilian cruiser Bahia\"><i>Bahia</i></a>, violently rebel in what is now known as the <i>Revolta da Chibata</i> (<a href=\"https://wikipedia.org/wiki/Revolt_of_the_Lash\" title=\"Revolt of the Lash\">Revolt of the Lash</a>).", "no_year_html": "Sailors on board Brazil's warships including the <a href=\"https://wikipedia.org/wiki/Brazilian_battleship_Minas_Geraes\" title=\"Brazilian battleship Minas Geraes\"><i>Minas Gerais</i></a>, <a href=\"https://wikipedia.org/wiki/Brazilian_battleship_S%C3%A3o_Paulo\" title=\"Brazilian battleship São Paulo\"><i>São Paulo</i></a>, and <a href=\"https://wikipedia.org/wiki/Brazilian_cruiser_Bahia\" title=\"Brazilian cruiser Bahia\"><i>Bahia</i></a>, violently rebel in what is now known as the <i>Revolta da Chibata</i> (<a href=\"https://wikipedia.org/wiki/Revolt_of_the_Lash\" title=\"Revolt of the Lash\">Revolt of the Lash</a>).", "links": [{"title": "Brazilian battleship Minas Geraes", "link": "https://wikipedia.org/wiki/Brazilian_battleship_Minas_Geraes"}, {"title": "Brazilian battleship São Paulo", "link": "https://wikipedia.org/wiki/Brazilian_battleship_S%C3%A3o_Paulo"}, {"title": "Brazilian cruiser <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brazilian_cruiser_Bahia"}, {"title": "Revolt of the Lash", "link": "https://wikipedia.org/wiki/Revolt_of_the_Lash"}]}, {"year": "1916", "text": "Mines from SM U-73 sink HMHS Britannic, the largest ship lost in the First World War.", "html": "1916 - Mines from <a href=\"https://wikipedia.org/wiki/SM_U-73\" title=\"SM U-73\">SM U-73</a> sink <a href=\"https://wikipedia.org/wiki/HMHS_Britannic\" title=\"HMHS Britannic\">HMHS <i>Britannic</i></a>, the largest ship lost in the <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">First World War</a>.", "no_year_html": "Mines from <a href=\"https://wikipedia.org/wiki/SM_U-73\" title=\"SM U-73\">SM U-73</a> sink <a href=\"https://wikipedia.org/wiki/HMHS_Britannic\" title=\"HMHS Britannic\">HMHS <i>Britannic</i></a>, the largest ship lost in the <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">First World War</a>.", "links": [{"title": "SM U-73", "link": "https://wikipedia.org/wiki/SM_U-73"}, {"title": "HMHS Britannic", "link": "https://wikipedia.org/wiki/HMHS_Britannic"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1918", "text": "The Flag of Estonia, previously used by pro-independence activists, is formally adopted as the national flag of the Republic of Estonia.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Flag_of_Estonia\" title=\"Flag of Estonia\">Flag of Estonia</a>, previously used by pro-independence activists, is formally adopted as the national flag of the <a href=\"https://wikipedia.org/wiki/Republic_of_Estonia\" class=\"mw-redirect\" title=\"Republic of Estonia\">Republic of Estonia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flag_of_Estonia\" title=\"Flag of Estonia\">Flag of Estonia</a>, previously used by pro-independence activists, is formally adopted as the national flag of the <a href=\"https://wikipedia.org/wiki/Republic_of_Estonia\" class=\"mw-redirect\" title=\"Republic of Estonia\">Republic of Estonia</a>.", "links": [{"title": "Flag of Estonia", "link": "https://wikipedia.org/wiki/Flag_of_Estonia"}, {"title": "Republic of Estonia", "link": "https://wikipedia.org/wiki/Republic_of_Estonia"}]}, {"year": "1918", "text": "The Parliament (Qualification of Women) Act 1918 is passed, allowing women to stand for Parliament in the UK.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Parliament_(Qualification_of_Women)_Act_1918\" title=\"Parliament (Qualification of Women) Act 1918\">Parliament (Qualification of Women) Act 1918</a> is passed, allowing women to stand for Parliament in the UK.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_(Qualification_of_Women)_Act_1918\" title=\"Parliament (Qualification of Women) Act 1918\">Parliament (Qualification of Women) Act 1918</a> is passed, allowing women to stand for Parliament in the UK.", "links": [{"title": "Parliament (Qualification of Women) Act 1918", "link": "https://wikipedia.org/wiki/Parliament_(Qualification_of_Women)_Act_1918"}]}, {"year": "1918", "text": "A pogrom takes place in Lwów (now Lviv); over three days, at least 50 Jews and 270 Ukrainian Christians are killed by Poles.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Lw%C3%B3w_pogrom_(1918)\" title=\"Lwów pogrom (1918)\">A pogrom</a> takes place in Lwów (now <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>); over three days, at least 50 Jews and 270 Ukrainian Christians are killed by Poles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lw%C3%B3w_pogrom_(1918)\" title=\"Lwów pogrom (1918)\">A pogrom</a> takes place in Lwów (now <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>); over three days, at least 50 Jews and 270 Ukrainian Christians are killed by Poles.", "links": [{"title": "Lwów pogrom (1918)", "link": "https://wikipedia.org/wiki/Lw%C3%B3w_pogrom_(1918)"}, {"title": "Lviv", "link": "https://wikipedia.org/wiki/Lviv"}]}, {"year": "1920", "text": "Irish War of Independence: On \"Bloody Sunday\" in Dublin, the Irish Republican Army (IRA) assassinated a group of British Intelligence agents, and British forces killed 14 civilians at a Gaelic football match at Croke Park.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>: On \"<a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1920)\" title=\"Bloody Sunday (1920)\">Bloody Sunday</a>\" in Dublin, the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> (IRA) assassinated a <a href=\"https://wikipedia.org/wiki/Cairo_Gang\" title=\"Cairo Gang\">group of British Intelligence agents</a>, and British forces killed 14 civilians at a <a href=\"https://wikipedia.org/wiki/Gaelic_football\" title=\"Gaelic football\">Gaelic football</a> match at <a href=\"https://wikipedia.org/wiki/Croke_Park\" title=\"Croke Park\">Croke Park</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>: On \"<a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1920)\" title=\"Bloody Sunday (1920)\">Bloody Sunday</a>\" in Dublin, the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)\" title=\"Irish Republican Army (1919-1922)\">Irish Republican Army</a> (IRA) assassinated a <a href=\"https://wikipedia.org/wiki/Cairo_Gang\" title=\"Cairo Gang\">group of British Intelligence agents</a>, and British forces killed 14 civilians at a <a href=\"https://wikipedia.org/wiki/Gaelic_football\" title=\"Gaelic football\">Gaelic football</a> match at <a href=\"https://wikipedia.org/wiki/Croke_Park\" title=\"Croke Park\">Croke Park</a>.", "links": [{"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}, {"title": "Bloody Sunday (1920)", "link": "https://wikipedia.org/wiki/Bloody_Sunday_(1920)"}, {"title": "Irish Republican Army (1919-1922)", "link": "https://wikipedia.org/wiki/Irish_Republican_Army_(1919%E2%80%931922)"}, {"title": "Cairo Gang", "link": "https://wikipedia.org/wiki/Cairo_Gang"}, {"title": "Gaelic football", "link": "https://wikipedia.org/wiki/Gaelic_football"}, {"title": "Croke Park", "link": "https://wikipedia.org/wiki/Croke_Park"}]}, {"year": "1922", "text": "<PERSON> of Georgia takes the oath of office, becoming the first female United States Senator.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> takes the oath of office, becoming the first female <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senator</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> takes the oath of office, becoming the first female <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senator</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1927", "text": "Columbine Mine massacre: Striking coal miners are allegedly attacked with machine guns by a detachment of state police dressed in civilian clothes.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Columbine_Mine_massacre\" title=\"Columbine Mine massacre\">Columbine Mine massacre</a>: <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">Striking</a> coal miners are allegedly attacked with <a href=\"https://wikipedia.org/wiki/Machine_gun\" title=\"Machine gun\">machine guns</a> by a detachment of state police dressed in civilian clothes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbine_Mine_massacre\" title=\"Columbine Mine massacre\">Columbine Mine massacre</a>: <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">Striking</a> coal miners are allegedly attacked with <a href=\"https://wikipedia.org/wiki/Machine_gun\" title=\"Machine gun\">machine guns</a> by a detachment of state police dressed in civilian clothes.", "links": [{"title": "Columbine Mine massacre", "link": "https://wikipedia.org/wiki/Columbine_Mine_massacre"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}, {"title": "Machine gun", "link": "https://wikipedia.org/wiki/Machine_gun"}]}, {"year": "1942", "text": "The completion of the Alaska Highway (also known as the Alcan Highway) is celebrated (however, the highway is not usable by standard road vehicles until 1943).", "html": "1942 - The completion of the <a href=\"https://wikipedia.org/wiki/Alaska_Highway\" title=\"Alaska Highway\">Alaska Highway</a> (also known as the Alcan Highway) is celebrated (however, the highway is not usable by standard road vehicles until 1943).", "no_year_html": "The completion of the <a href=\"https://wikipedia.org/wiki/Alaska_Highway\" title=\"Alaska Highway\">Alaska Highway</a> (also known as the Alcan Highway) is celebrated (however, the highway is not usable by standard road vehicles until 1943).", "links": [{"title": "Alaska Highway", "link": "https://wikipedia.org/wiki/Alaska_Highway"}]}, {"year": "1944", "text": "World War II: American submarine USS <PERSON>ion sinks the Japanese battleship Kong<PERSON> and Japanese destroyer Urakaze in the Formosa Strait.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: American submarine <a href=\"https://wikipedia.org/wiki/USS_Sealion_(SS-315)\" title=\"USS Sealion (SS-315)\">USS <i>Sealion</i></a> sinks the <a href=\"https://wikipedia.org/wiki/Japanese_battleship_Kong%C5%8D\" title=\"Japanese battleship Kong<PERSON>\">Japanese battleship <i>Kongō</i></a> and <a href=\"https://wikipedia.org/wiki/Japanese_destroyer_Urakaze_(1940)\" title=\"Japanese destroyer Urakaze (1940)\">Japanese destroyer <i>Urakaze</i></a> in the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Formosa Strait</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: American submarine <a href=\"https://wikipedia.org/wiki/USS_Sealion_(SS-315)\" title=\"USS Sealion (SS-315)\">USS <i>Sealion</i></a> sinks the <a href=\"https://wikipedia.org/wiki/Japanese_battleship_Kong%C5%8D\" title=\"Japanese battleship Kong<PERSON>\">Japanese battleship <i>Kongō</i></a> and <a href=\"https://wikipedia.org/wiki/Japanese_destroyer_Urakaze_(1940)\" title=\"Japanese destroyer Urakaze (1940)\">Japanese destroyer <i>Urakaze</i></a> in the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Formosa Strait</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "USS Sealion (SS-315)", "link": "https://wikipedia.org/wiki/USS_Sealion_(SS-315)"}, {"title": "Japanese battleship Kongō", "link": "https://wikipedia.org/wiki/Japanese_battleship_Kong%C5%8D"}, {"title": "Japanese destroyer Urakaze (1940)", "link": "https://wikipedia.org/wiki/Japanese_destroyer_Urakaze_(1940)"}, {"title": "Taiwan Strait", "link": "https://wikipedia.org/wiki/Taiwan_Strait"}]}, {"year": "1945", "text": "The United Auto Workers strike 92 General Motors plants in 50 cities to back up worker demands for a 30-percent raise.", "html": "1945 - The United Auto Workers <a href=\"https://wikipedia.org/wiki/1945%E2%80%931946_General_Motors_strike\" title=\"1945-1946 General Motors strike\">strike</a> 92 General Motors plants in 50 cities to back up worker demands for a 30-percent raise.", "no_year_html": "The United Auto Workers <a href=\"https://wikipedia.org/wiki/1945%E2%80%931946_General_Motors_strike\" title=\"1945-1946 General Motors strike\">strike</a> 92 General Motors plants in 50 cities to back up worker demands for a 30-percent raise.", "links": [{"title": "1945-1946 General Motors strike", "link": "https://wikipedia.org/wiki/1945%E2%80%931946_General_Motors_strike"}]}, {"year": "1950", "text": "Two Canadian National Railway trains collide in northeastern British Columbia in the Canoe River train crash; the death toll is 21, with 17 of them Canadian troops bound for Korea.", "html": "1950 - Two <a href=\"https://wikipedia.org/wiki/Canadian_National_Railway\" title=\"Canadian National Railway\">Canadian National Railway</a> trains collide in northeastern <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a> in the <a href=\"https://wikipedia.org/wiki/Canoe_River_train_crash\" title=\"Canoe River train crash\">Canoe River train crash</a>; the death toll is 21, with 17 of them Canadian troops bound for Korea.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Canadian_National_Railway\" title=\"Canadian National Railway\">Canadian National Railway</a> trains collide in northeastern <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a> in the <a href=\"https://wikipedia.org/wiki/Canoe_River_train_crash\" title=\"Canoe River train crash\">Canoe River train crash</a>; the death toll is 21, with 17 of them Canadian troops bound for Korea.", "links": [{"title": "Canadian National Railway", "link": "https://wikipedia.org/wiki/Canadian_National_Railway"}, {"title": "British Columbia", "link": "https://wikipedia.org/wiki/British_Columbia"}, {"title": "Canoe River train crash", "link": "https://wikipedia.org/wiki/Canoe_River_train_crash"}]}, {"year": "1953", "text": "The Natural History Museum, London announces that the \"Piltdown Man\" skull, initially believed to be one of the most important fossilized hominid skulls ever found, is a hoax.", "html": "1953 - The <a href=\"https://wikipedia.org/wiki/Natural_History_Museum,_London\" title=\"Natural History Museum, London\">Natural History Museum, London</a> announces that the \"<a href=\"https://wikipedia.org/wiki/Piltdown_Man\" title=\"Piltdown Man\">Piltdown Man</a>\" <a href=\"https://wikipedia.org/wiki/Human_skull\" class=\"mw-redirect\" title=\"Human skull\">skull</a>, initially believed to be one of the most important fossilized hominid skulls ever found, is a hoax.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Natural_History_Museum,_London\" title=\"Natural History Museum, London\">Natural History Museum, London</a> announces that the \"<a href=\"https://wikipedia.org/wiki/Piltdown_Man\" title=\"Piltdown Man\">Pilt<PERSON> Man</a>\" <a href=\"https://wikipedia.org/wiki/Human_skull\" class=\"mw-redirect\" title=\"Human skull\">skull</a>, initially believed to be one of the most important fossilized hominid skulls ever found, is a hoax.", "links": [{"title": "Natural History Museum, London", "link": "https://wikipedia.org/wiki/Natural_History_Museum,_London"}, {"title": "Piltdown Man", "link": "https://wikipedia.org/wiki/Piltdown_Man"}, {"title": "Human skull", "link": "https://wikipedia.org/wiki/Human_skull"}]}, {"year": "1954", "text": "People's Action Party, an eventual dominative political party in Singapore, was established.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/People%27s_Action_Party\" title=\"People's Action Party\">People's Action Party</a>, an eventual dominative political party in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, was established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/People%27s_Action_Party\" title=\"People's Action Party\">People's Action Party</a>, an eventual dominative political party in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, was established.", "links": [{"title": "People's Action Party", "link": "https://wikipedia.org/wiki/People%27s_Action_Party"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}]}, {"year": "1959", "text": "American disc jockey <PERSON>, who had popularized the term \"rock and roll\" and music of that style, is fired from WABC radio over allegations he had participated in the payola scandal.", "html": "1959 - American disc jockey <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had popularized the term \"<a href=\"https://wikipedia.org/wiki/Rock_and_roll\" title=\"Rock and roll\">rock and roll</a>\" and music of that style, is fired from <a href=\"https://wikipedia.org/wiki/WABC_(AM)\" title=\"WABC (AM)\">WABC</a> radio over allegations he had participated in the <a href=\"https://wikipedia.org/wiki/Payola\" title=\"Payola\">payola</a> scandal.", "no_year_html": "American disc jockey <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had popularized the term \"<a href=\"https://wikipedia.org/wiki/Rock_and_roll\" title=\"Rock and roll\">rock and roll</a>\" and music of that style, is fired from <a href=\"https://wikipedia.org/wiki/WABC_(AM)\" title=\"WABC (AM)\">WABC</a> radio over allegations he had participated in the <a href=\"https://wikipedia.org/wiki/Payola\" title=\"Payola\">payola</a> scandal.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Freed"}, {"title": "Rock and roll", "link": "https://wikipedia.org/wiki/Rock_and_roll"}, {"title": "WABC (AM)", "link": "https://wikipedia.org/wiki/WABC_(AM)"}, {"title": "Payola", "link": "https://wikipedia.org/wiki/Payola"}]}, {"year": "1961", "text": "The \"La Ronde\" opens in Honolulu, first revolving restaurant in the United States.", "html": "1961 - The \"<a href=\"https://wikipedia.org/wiki/<PERSON>_Ronde_(restaurant)\" title=\"La Ronde (restaurant)\">La Ronde</a>\" opens in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>, first <a href=\"https://wikipedia.org/wiki/Revolving_restaurant\" title=\"Revolving restaurant\">revolving restaurant</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(restaurant)\" title=\"La Ronde (restaurant)\">La Ronde</a>\" opens in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu</a>, first <a href=\"https://wikipedia.org/wiki/Revolving_restaurant\" title=\"Revolving restaurant\">revolving restaurant</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "La Ronde (restaurant)", "link": "https://wikipedia.org/wiki/La_Ronde_(restaurant)"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}, {"title": "Revolving restaurant", "link": "https://wikipedia.org/wiki/Revolving_restaurant"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1962", "text": "The Chinese People's Liberation Army declares a unilateral ceasefire in the Sino-Indian War.", "html": "1962 - The Chinese <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> declares a unilateral <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">ceasefire</a> in the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>.", "no_year_html": "The Chinese <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> declares a unilateral <a href=\"https://wikipedia.org/wiki/Ceasefire\" title=\"Ceasefire\">ceasefire</a> in the <a href=\"https://wikipedia.org/wiki/Sino-Indian_War\" title=\"Sino-Indian War\">Sino-Indian War</a>.", "links": [{"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}, {"title": "Ceasefire", "link": "https://wikipedia.org/wiki/Ceasefire"}, {"title": "Sino-Indian War", "link": "https://wikipedia.org/wiki/Sino-Indian_War"}]}, {"year": "1964", "text": "The Verrazzano-Narrows Bridge opens to traffic. At the time it is the world's longest bridge span.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge\" title=\"Verrazzano-Narrows Bridge\">Verrazzano-Narrows Bridge</a> opens to traffic. At the time it is the world's longest bridge span.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge\" title=\"Verrazzano-Narrows Bridge\">Verrazzano-Narrows Bridge</a> opens to traffic. At the time it is the world's longest bridge span.", "links": [{"title": "Verrazzano-Narrows Bridge", "link": "https://wikipedia.org/wiki/Verrazzano-Narrows_Bridge"}]}, {"year": "1964", "text": "Second Vatican Council: The third session of the Roman Catholic Church's ecumenical council closes.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Second_Vatican_Council\" title=\"Second Vatican Council\">Second Vatican Council</a>: The third session of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>'s <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a> closes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Vatican_Council\" title=\"Second Vatican Council\">Second Vatican Council</a>: The third session of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a>'s <a href=\"https://wikipedia.org/wiki/Ecumenical_council\" title=\"Ecumenical council\">ecumenical council</a> closes.", "links": [{"title": "Second Vatican Council", "link": "https://wikipedia.org/wiki/Second_Vatican_Council"}, {"title": "Roman Catholic Church", "link": "https://wikipedia.org/wiki/Roman_Catholic_Church"}, {"title": "Ecumenical council", "link": "https://wikipedia.org/wiki/Ecumenical_council"}]}, {"year": "1967", "text": "Vietnam War: American General <PERSON> tells news reporters: \"I am absolutely certain that whereas in 1965 the enemy was winning, today he is certainly losing.\"", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American General <a href=\"https://wikipedia.org/wiki/William_Westmoreland\" title=\"William <PERSON>\"><PERSON></a> tells news reporters: \"I am absolutely certain that whereas in 1965 the enemy was winning, today he is certainly losing.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: American General <a href=\"https://wikipedia.org/wiki/William_Westmoreland\" title=\"William <PERSON>\"><PERSON></a> tells news reporters: \"I am absolutely certain that whereas in 1965 the enemy was winning, today he is certainly losing.\"", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>moreland"}]}, {"year": "1969", "text": "U.S. President <PERSON> and Japanese Premier <PERSON><PERSON><PERSON> agree on the return of Okinawa to Japanese control in 1972. The U.S. retains rights to bases on the island, but these are to be nuclear-free.", "html": "1969 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Japanese Premier <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Sat%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> agree on the return of <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a> to Japanese control in 1972. The U.S. retains rights to bases on the island, but these are to be nuclear-free.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Japanese Premier <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Sat%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> agree on the return of <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a> to Japanese control in 1972. The U.S. retains rights to bases on the island, but these are to be nuclear-free.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eisaku_Sat%C5%8D"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}]}, {"year": "1969", "text": "The first permanent ARPANET link is established between UCLA and SRI.", "html": "1969 - The first permanent <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> link is established between <a href=\"https://wikipedia.org/wiki/UCLA\" class=\"mw-redirect\" title=\"UCLA\">UCLA</a> and <a href=\"https://wikipedia.org/wiki/SRI_International\" title=\"SRI International\">SRI</a>.", "no_year_html": "The first permanent <a href=\"https://wikipedia.org/wiki/ARPANET\" title=\"ARPANET\">ARPANET</a> link is established between <a href=\"https://wikipedia.org/wiki/UCLA\" class=\"mw-redirect\" title=\"UCLA\">UCLA</a> and <a href=\"https://wikipedia.org/wiki/SRI_International\" title=\"SRI International\">SRI</a>.", "links": [{"title": "ARPANET", "link": "https://wikipedia.org/wiki/ARPANET"}, {"title": "UCLA", "link": "https://wikipedia.org/wiki/UCLA"}, {"title": "SRI International", "link": "https://wikipedia.org/wiki/SRI_International"}]}, {"year": "1970", "text": "Vietnam War: Operation Ivory Coast: A joint United States Air Force and Army team raids the Sơn Tây prisoner-of-war camp in an attempt to free American prisoners of war thought to be held there.", "html": "1970 - Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Ivory_Coast\" title=\"Operation Ivory Coast\">Operation Ivory Coast</a>: A joint United States Air Force and Army team raids the <a href=\"https://wikipedia.org/wiki/S%C6%A1n_T%C3%A2y_(Hanoi)\" class=\"mw-redirect\" title=\"Sơn Tây (Hanoi)\">Sơn Tây</a> <a href=\"https://wikipedia.org/wiki/Prisoner-of-war_camp\" title=\"Prisoner-of-war camp\">prisoner-of-war camp</a> in an attempt to free American prisoners of war thought to be held there.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/Operation_Ivory_Coast\" title=\"Operation Ivory Coast\">Operation Ivory Coast</a>: A joint United States Air Force and Army team raids the <a href=\"https://wikipedia.org/wiki/S%C6%A1n_T%C3%A2y_(Hanoi)\" class=\"mw-redirect\" title=\"Sơn Tây (Hanoi)\">Sơn Tây</a> <a href=\"https://wikipedia.org/wiki/Prisoner-of-war_camp\" title=\"Prisoner-of-war camp\">prisoner-of-war camp</a> in an attempt to free American prisoners of war thought to be held there.", "links": [{"title": "Operation Ivory Coast", "link": "https://wikipedia.org/wiki/Operation_Ivory_Coast"}, {"title": "Sơn <PERSON> (Hanoi)", "link": "https://wikipedia.org/wiki/S%C6%A1n_T%C3%A2y_(Hanoi)"}, {"title": "Prisoner-of-war camp", "link": "https://wikipedia.org/wiki/Prisoner-of-war_camp"}]}, {"year": "1971", "text": "Indian troops, partly aided by <PERSON><PERSON><PERSON> (Bengali guerrillas), defeat the Pakistan army in the Battle of Garibpur.", "html": "1971 - Indian troops, partly aided by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bahini\" title=\"<PERSON><PERSON><PERSON> Bahini\"><PERSON><PERSON><PERSON> Bahin<PERSON></a> (<a href=\"https://wikipedia.org/wiki/Bengal\" title=\"Bengal\">Bengali</a> <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare\" title=\"Guerrilla warfare\">guerrillas</a>), defeat the Pakistan army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Garibpur\" title=\"Battle of Garibpur\">Battle of Garibpur</a>.", "no_year_html": "Indian troops, partly aided by <a href=\"https://wikipedia.org/wiki/Mu<PERSON><PERSON>_Bahini\" title=\"Mukt<PERSON> Bahini\"><PERSON><PERSON><PERSON> Bahini</a> (<a href=\"https://wikipedia.org/wiki/Bengal\" title=\"Bengal\">Bengali</a> <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare\" title=\"Guerrilla warfare\">guerrillas</a>), defeat the Pakistan army in the <a href=\"https://wikipedia.org/wiki/Battle_of_Garibpur\" title=\"Battle of Garibpur\">Battle of Garibpur</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mukti_<PERSON><PERSON>i"}, {"title": "Bengal", "link": "https://wikipedia.org/wiki/Bengal"}, {"title": "Guerrilla warfare", "link": "https://wikipedia.org/wiki/Guerrilla_warfare"}, {"title": "Battle of Garibpur", "link": "https://wikipedia.org/wiki/Battle_of_Garibpur"}]}, {"year": "1972", "text": "Voters in South Korea overwhelmingly approve a new constitution, giving legitimacy to <PERSON> and the Fourth Republic.", "html": "1972 - Voters in South Korea overwhelmingly approve a new constitution, giving legitimacy to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Fourth_Republic_of_South_Korea\" class=\"mw-redirect\" title=\"Fourth Republic of South Korea\">Fourth Republic</a>.", "no_year_html": "Voters in South Korea overwhelmingly approve a new constitution, giving legitimacy to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Fourth_Republic_of_South_Korea\" class=\"mw-redirect\" title=\"Fourth Republic of South Korea\">Fourth Republic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Fourth Republic of South Korea", "link": "https://wikipedia.org/wiki/Fourth_Republic_of_South_Korea"}]}, {"year": "1974", "text": "The Birmingham pub bombings kill 21 people. The Birmingham Six are sentenced to life in prison for the crime but subsequently acquitted.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Birmingham_pub_bombings\" title=\"Birmingham pub bombings\">Birmingham pub bombings</a> kill 21 people. The <a href=\"https://wikipedia.org/wiki/Birmingham_Six\" title=\"Birmingham Six\">Birmingham Six</a> are sentenced to life in prison for the crime but subsequently acquitted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Birmingham_pub_bombings\" title=\"Birmingham pub bombings\">Birmingham pub bombings</a> kill 21 people. The <a href=\"https://wikipedia.org/wiki/Birmingham_Six\" title=\"Birmingham Six\">Birmingham Six</a> are sentenced to life in prison for the crime but subsequently acquitted.", "links": [{"title": "Birmingham pub bombings", "link": "https://wikipedia.org/wiki/Birmingham_pub_bombings"}, {"title": "Birmingham Six", "link": "https://wikipedia.org/wiki/Birmingham_Six"}]}, {"year": "1977", "text": "Minister of Internal Affairs <PERSON> announces that the national anthems of New Zealand shall be the traditional anthem \"God Save the Queen\" and \"God Defend New Zealand\".", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Minister_of_Internal_Affairs_(New_Zealand)\" title=\"Minister of Internal Affairs (New Zealand)\">Minister of Internal Affairs</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthems</a> of New Zealand shall be the traditional anthem \"<a href=\"https://wikipedia.org/wiki/God_Save_the_King\" title=\"God Save the King\">God Save the Queen</a>\" and \"<a href=\"https://wikipedia.org/wiki/God_Defend_New_Zealand\" title=\"God Defend New Zealand\">God Defend New Zealand</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minister_of_Internal_Affairs_(New_Zealand)\" title=\"Minister of Internal Affairs (New Zealand)\">Minister of Internal Affairs</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that the <a href=\"https://wikipedia.org/wiki/National_anthem\" title=\"National anthem\">national anthems</a> of New Zealand shall be the traditional anthem \"<a href=\"https://wikipedia.org/wiki/God_Save_the_King\" title=\"God Save the King\">God Save the Queen</a>\" and \"<a href=\"https://wikipedia.org/wiki/God_Defend_New_Zealand\" title=\"God Defend New Zealand\">God Defend New Zealand</a>\".", "links": [{"title": "Minister of Internal Affairs (New Zealand)", "link": "https://wikipedia.org/wiki/Minister_of_Internal_Affairs_(New_Zealand)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National anthem", "link": "https://wikipedia.org/wiki/National_anthem"}, {"title": "God Save the King", "link": "https://wikipedia.org/wiki/God_Save_the_King"}, {"title": "God Defend New Zealand", "link": "https://wikipedia.org/wiki/<PERSON>_Defend_New_Zealand"}]}, {"year": "1979", "text": "The United States Embassy in Islamabad, Pakistan, is attacked by a mob and set on fire, killing four.", "html": "1979 - The United States Embassy in <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, Pakistan, is attacked by a mob and <a href=\"https://wikipedia.org/wiki/1979_U.S._embassy_burning_in_Islamabad\" title=\"1979 U.S. embassy burning in Islamabad\">set on fire</a>, killing four.", "no_year_html": "The United States Embassy in <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a>, Pakistan, is attacked by a mob and <a href=\"https://wikipedia.org/wiki/1979_U.S._embassy_burning_in_Islamabad\" title=\"1979 U.S. embassy burning in Islamabad\">set on fire</a>, killing four.", "links": [{"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}, {"title": "1979 U.S. embassy burning in Islamabad", "link": "https://wikipedia.org/wiki/1979_U.S._embassy_burning_in_Islamabad"}]}, {"year": "1980", "text": "A deadly fire breaks out at the MGM Grand Hotel in Paradise, Nevada (now Bally's Las Vegas). Eighty-five people are killed and more than 650 are injured in the worst disaster in Nevada history.", "html": "1980 - A <a href=\"https://wikipedia.org/wiki/MGM_Grand_fire\" title=\"MGM Grand fire\">deadly fire</a> breaks out at the MGM Grand Hotel in <a href=\"https://wikipedia.org/wiki/Paradise,_Nevada\" title=\"Paradise, Nevada\">Paradise, Nevada</a> (now <a href=\"https://wikipedia.org/wiki/Bally%27s_Las_Vegas\" class=\"mw-redirect\" title=\"Bally's Las Vegas\">Bally's Las Vegas</a>). Eighty-five people are killed and more than 650 are injured in the worst disaster in Nevada history.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/MGM_Grand_fire\" title=\"MGM Grand fire\">deadly fire</a> breaks out at the MGM Grand Hotel in <a href=\"https://wikipedia.org/wiki/Paradise,_Nevada\" title=\"Paradise, Nevada\">Paradise, Nevada</a> (now <a href=\"https://wikipedia.org/wiki/Bally%27s_Las_Vegas\" class=\"mw-redirect\" title=\"Bally's Las Vegas\">Bally's Las Vegas</a>). Eighty-five people are killed and more than 650 are injured in the worst disaster in Nevada history.", "links": [{"title": "MGM Grand fire", "link": "https://wikipedia.org/wiki/MGM_Grand_fire"}, {"title": "Paradise, Nevada", "link": "https://wikipedia.org/wiki/Paradise,_Nevada"}, {"title": "Bally's Las Vegas", "link": "https://wikipedia.org/wiki/Bally%27s_Las_Vegas"}]}, {"year": "1985", "text": "United States Navy intelligence analyst <PERSON> is arrested for spying after being caught giving Israel classified information on Arab nations. He is subsequently sentenced to life in prison.", "html": "1985 - United States Navy intelligence analyst <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spying</a> after being caught giving <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> classified information on <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arab</a> nations. He is subsequently sentenced to life in prison.", "no_year_html": "United States Navy intelligence analyst <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">spying</a> after being caught giving <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> classified information on <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arab</a> nations. He is subsequently sentenced to life in prison.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Arab", "link": "https://wikipedia.org/wiki/Arab"}]}, {"year": "1986", "text": "National Security Council member <PERSON> and his secretary start to shred documents allegedly implicating them in the Iran-Contra affair.", "html": "1986 - National Security Council member <a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a> and his secretary start to shred documents allegedly implicating them in the <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>.", "no_year_html": "National Security Council member <a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a> and his secretary start to shred documents allegedly implicating them in the <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_North"}, {"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}]}, {"year": "1990", "text": "Bangkok Airways Flight 125 crashes on approach to Samui Airport, killing 38.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Bangkok_Airways_Flight_125\" title=\"Bangkok Airways Flight 125\">Bangkok Airways Flight 125</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Samui_Airport\" title=\"Samui Airport\">Samui Airport</a>, killing 38.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangkok_Airways_Flight_125\" title=\"Bangkok Airways Flight 125\">Bangkok Airways Flight 125</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Samui_Airport\" title=\"Samui Airport\">Samui Airport</a>, killing 38.", "links": [{"title": "Bangkok Airways Flight 125", "link": "https://wikipedia.org/wiki/Bangkok_Airways_Flight_125"}, {"title": "Samui Airport", "link": "https://wikipedia.org/wiki/Samui_Airport"}]}, {"year": "1992", "text": "A major tornado strikes the Houston, Texas area during the afternoon. Over the next two days the largest tornado outbreak ever to occur in the US during November spawns over 100 tornadoes.", "html": "1992 - A major <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a>, <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> area during the afternoon. Over the next two days the largest <a href=\"https://wikipedia.org/wiki/November_1992_tornado_outbreak\" class=\"mw-redirect\" title=\"November 1992 tornado outbreak\">tornado outbreak</a> ever to occur in the US during November spawns over 100 tornadoes.", "no_year_html": "A major <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Houston\" title=\"Houston\">Houston</a>, <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a> area during the afternoon. Over the next two days the largest <a href=\"https://wikipedia.org/wiki/November_1992_tornado_outbreak\" class=\"mw-redirect\" title=\"November 1992 tornado outbreak\">tornado outbreak</a> ever to occur in the US during November spawns over 100 tornadoes.", "links": [{"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Houston", "link": "https://wikipedia.org/wiki/Houston"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}, {"title": "November 1992 tornado outbreak", "link": "https://wikipedia.org/wiki/November_1992_tornado_outbreak"}]}, {"year": "1995", "text": "The Dayton Agreement is initialed at the Wright-Patterson Air Force Base, near Dayton, Ohio, ending three and a half years of war in Bosnia and Herzegovina.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/Dayton_Agreement\" title=\"Dayton Agreement\">Dayton Agreement</a> is initialed at the <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_Air_Force_Base\" title=\"Wright-Patterson Air Force Base\">Wright-Patterson Air Force Base</a>, near <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>, ending three and a half years of war in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dayton_Agreement\" title=\"Dayton Agreement\">Dayton Agreement</a> is initialed at the <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_Air_Force_Base\" title=\"Wright-Patterson Air Force Base\">Wright-Patterson Air Force Base</a>, near <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>, ending three and a half years of war in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Dayton Agreement", "link": "https://wikipedia.org/wiki/Dayton_Agreement"}, {"title": "Wright-Patterson Air Force Base", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_Air_Force_Base"}, {"title": "Dayton, Ohio", "link": "https://wikipedia.org/wiki/Dayton,_Ohio"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1996", "text": "Humberto Vidal explosion: Thirty-three people die when a Humberto Vidal shoe shop in Río Piedras, Puerto Rico explodes.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Humberto_Vidal_explosion\" title=\"Humberto Vidal explosion\">Humberto Vidal explosion</a>: Thirty-three people die when a Humberto Vidal shoe shop in <a href=\"https://wikipedia.org/wiki/R%C3%ADo_Piedras,_Puerto_Rico\" title=\"Río Piedras, Puerto Rico\">Río Piedras, Puerto Rico</a> explodes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Humberto_Vidal_explosion\" title=\"Humberto Vidal explosion\">Humberto Vidal explosion</a>: Thirty-three people die when a Humberto Vidal shoe shop in <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON>_<PERSON>,_Puerto_Rico\" title=\"Río Piedras, Puerto Rico\">Río Piedras, Puerto Rico</a> explodes.", "links": [{"title": "<PERSON><PERSON><PERSON> explosion", "link": "https://wikipedia.org/wiki/Humberto_Vidal_explosion"}, {"title": "Río Piedras, Puerto Rico", "link": "https://wikipedia.org/wiki/R%C3%ADo_Piedras,_Puerto_Rico"}]}, {"year": "1998", "text": "Finnish satanist <PERSON><PERSON><PERSON> kills a 23-year-old man and performs a ritual-like cutting and eating of body parts in Hyvinkää, Finland.", "html": "1998 - Finnish <a href=\"https://wikipedia.org/wiki/Satanism\" title=\"Satanism\">satanist</a> <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> kills a 23-year-old man and performs a <a href=\"https://wikipedia.org/wiki/Ritual\" title=\"Ritual\">ritual</a>-like cutting and <a href=\"https://wikipedia.org/wiki/Human_cannibalism\" title=\"Human cannibalism\">eating of body parts</a> in <a href=\"https://wikipedia.org/wiki/Hyvink%C3%A4%C3%A4\" title=\"Hyvinkää\">Hyvinkää, Finland</a>.", "no_year_html": "Finnish <a href=\"https://wikipedia.org/wiki/Satanism\" title=\"Satanism\">satanist</a> <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> kills a 23-year-old man and performs a <a href=\"https://wikipedia.org/wiki/Ritual\" title=\"Ritual\">ritual</a>-like cutting and <a href=\"https://wikipedia.org/wiki/Human_cannibalism\" title=\"Human cannibalism\">eating of body parts</a> in <a href=\"https://wikipedia.org/wiki/Hyvink%C3%A4%C3%A4\" title=\"Hyvinkää\">Hyvinkää, Finland</a>.", "links": [{"title": "Satanism", "link": "https://wikipedia.org/wiki/Satanism"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jarno_<PERSON>g"}, {"title": "Ritual", "link": "https://wikipedia.org/wiki/Ritual"}, {"title": "Human cannibalism", "link": "https://wikipedia.org/wiki/Human_cannibalism"}, {"title": "Hyvinkää", "link": "https://wikipedia.org/wiki/Hyvink%C3%A4%C3%A4"}]}, {"year": "2002", "text": "NATO invites Bulgaria, Estonia, Latvia, Lithuania, Romania, Slovakia and Slovenia to become members.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> invites <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a> and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> to become members.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> invites <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a> and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> to become members.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}]}, {"year": "2002", "text": "<PERSON>, founder of Los Zetas and high-member of the Gulf Cartel, is killed in a shoot-out with the Mexican Army and the police.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Decena\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> and high-member of the <a href=\"https://wikipedia.org/wiki/Gulf_Cartel\" title=\"Gulf Cartel\">Gulf Cartel</a>, is killed in a shoot-out with the <a href=\"https://wikipedia.org/wiki/Mexican_Army\" title=\"Mexican Army\">Mexican Army</a> and the police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Decena\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> and high-member of the <a href=\"https://wikipedia.org/wiki/Gulf_Cartel\" title=\"Gulf Cartel\">Gulf Cartel</a>, is killed in a shoot-out with the <a href=\"https://wikipedia.org/wiki/Mexican_Army\" title=\"Mexican Army\">Mexican Army</a> and the police.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_Guzm%C3%A1n_Decena"}, {"title": "Los Zetas", "link": "https://wikipedia.org/wiki/Los_Zetas"}, {"title": "Gulf Cartel", "link": "https://wikipedia.org/wiki/Gulf_Cartel"}, {"title": "Mexican Army", "link": "https://wikipedia.org/wiki/Mexican_Army"}]}, {"year": "2004", "text": "The second round of the Ukrainian presidential election is held, giving rise to massive protests and controversy over the election's integrity.", "html": "2004 - The second round of the <a href=\"https://wikipedia.org/wiki/2004_Ukrainian_presidential_election\" title=\"2004 Ukrainian presidential election\">Ukrainian presidential election</a> is held, giving rise to massive protests and controversy over the election's integrity.", "no_year_html": "The second round of the <a href=\"https://wikipedia.org/wiki/2004_Ukrainian_presidential_election\" title=\"2004 Ukrainian presidential election\">Ukrainian presidential election</a> is held, giving rise to massive protests and controversy over the election's integrity.", "links": [{"title": "2004 Ukrainian presidential election", "link": "https://wikipedia.org/wiki/2004_Ukrainian_presidential_election"}]}, {"year": "2004", "text": "Dominica is hit by the most destructive earthquake in its history. The northern half of the island sustains the most damage, especially the town of Portsmouth. In neighboring Guadeloupe, one person is killed.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> is hit by the most destructive <a href=\"https://wikipedia.org/wiki/Earthquake\" title=\"Earthquake\">earthquake</a> in its history. The northern half of the island sustains the most damage, especially the town of <a href=\"https://wikipedia.org/wiki/Portsmouth,_Dominica\" title=\"Portsmouth, Dominica\">Portsmouth</a>. In neighboring <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, one person is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominica\" title=\"Dominica\">Dominica</a> is hit by the most destructive <a href=\"https://wikipedia.org/wiki/Earthquake\" title=\"Earthquake\">earthquake</a> in its history. The northern half of the island sustains the most damage, especially the town of <a href=\"https://wikipedia.org/wiki/Portsmouth,_Dominica\" title=\"Portsmouth, Dominica\">Portsmouth</a>. In neighboring <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, one person is killed.", "links": [{"title": "Dominica", "link": "https://wikipedia.org/wiki/Dominica"}, {"title": "Earthquake", "link": "https://wikipedia.org/wiki/Earthquake"}, {"title": "Portsmouth, Dominica", "link": "https://wikipedia.org/wiki/Portsmouth,_Dominica"}, {"title": "Guadeloupe", "link": "https://wikipedia.org/wiki/Guadeloupe"}]}, {"year": "2004", "text": "The Paris Club agrees to write off 80% (up to $100 billion) of Iraq's external debt.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Paris_Club\" title=\"Paris Club\">Paris Club</a> agrees to write off 80% (up to $100 billion) of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s external debt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paris_Club\" title=\"Paris Club\">Paris Club</a> agrees to write off 80% (up to $100 billion) of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>'s external debt.", "links": [{"title": "Paris Club", "link": "https://wikipedia.org/wiki/Paris_Club"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2004", "text": "China Eastern Airlines Flight 5210 crashes after takeoff from Baotou Donghe Airport, killing 55.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5210\" title=\"China Eastern Airlines Flight 5210\">China Eastern Airlines Flight 5210</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Baotou_Donghe_Airport\" class=\"mw-redirect\" title=\"Baotou Donghe Airport\">Baotou Donghe Airport</a>, killing 55.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5210\" title=\"China Eastern Airlines Flight 5210\">China Eastern Airlines Flight 5210</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Baotou_Donghe_Airport\" class=\"mw-redirect\" title=\"Baotou Donghe Airport\">Baotou Donghe Airport</a>, killing 55.", "links": [{"title": "China Eastern Airlines Flight 5210", "link": "https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5210"}, {"title": "Baotou Donghe Airport", "link": "https://wikipedia.org/wiki/Baotou_Donghe_Airport"}]}, {"year": "2006", "text": "Anti-Syrian Lebanese politician and government minister <PERSON> is assassinated in suburban Beirut.", "html": "2006 - Anti-Syrian Lebanese politician and government minister <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in suburban <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>.", "no_year_html": "Anti-Syrian Lebanese politician and government minister <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated in suburban <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "2009", "text": "A mine explosion in Heilongjiang, China kills 108.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/2009_Heilongjiang_mine_explosion\" title=\"2009 Heilongjiang mine explosion\">mine explosion in Heilongjiang, China</a> kills 108.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2009_Heilongjiang_mine_explosion\" title=\"2009 Heilongjiang mine explosion\">mine explosion in Heilongjiang, China</a> kills 108.", "links": [{"title": "2009 Heilongjiang mine explosion", "link": "https://wikipedia.org/wiki/2009_Heilongjiang_mine_explosion"}]}, {"year": "2012", "text": "At least 28 are wounded after a bomb is thrown onto a bus in Tel Aviv.", "html": "2012 - At least 28 are wounded after a <a href=\"https://wikipedia.org/wiki/2012_Tel_Aviv_bus_bombing\" title=\"2012 Tel Aviv bus bombing\">bomb is thrown onto a bus</a> in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>.", "no_year_html": "At least 28 are wounded after a <a href=\"https://wikipedia.org/wiki/2012_Tel_Aviv_bus_bombing\" title=\"2012 Tel Aviv bus bombing\">bomb is thrown onto a bus</a> in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>.", "links": [{"title": "2012 Tel Aviv bus bombing", "link": "https://wikipedia.org/wiki/2012_Tel_Aviv_bus_bombing"}, {"title": "Tel Aviv", "link": "https://wikipedia.org/wiki/Tel_Aviv"}]}, {"year": "2013", "text": "Fifty-four people are killed when the roof of a shopping center collapses in Riga, Latvia.", "html": "2013 - Fifty-four people are killed when the <a href=\"https://wikipedia.org/wiki/Zolit%C5%ABde_shopping_centre_roof_collapse\" title=\"Zolitūde shopping centre roof collapse\">roof of a shopping center collapses</a> in Riga, Latvia.", "no_year_html": "Fifty-four people are killed when the <a href=\"https://wikipedia.org/wiki/Zolit%C5%ABde_shopping_centre_roof_collapse\" title=\"Zolitūde shopping centre roof collapse\">roof of a shopping center collapses</a> in Riga, Latvia.", "links": [{"title": "Zolitūde shopping centre roof collapse", "link": "https://wikipedia.org/wiki/Zolit%C5%ABde_shopping_centre_roof_collapse"}]}, {"year": "2013", "text": "Massive protests start in Ukraine after President <PERSON> suspended signing the Ukraine-European Union Association Agreement.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Euromaidan\" title=\"Euromaidan\">Massive protests</a> start in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> after <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> suspended signing the <a href=\"https://wikipedia.org/wiki/Ukraine%E2%80%93European_Union_Association_Agreement\" class=\"mw-redirect\" title=\"Ukraine-European Union Association Agreement\">Ukraine-European Union Association Agreement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Euromaidan\" title=\"Euromaidan\">Massive protests</a> start in <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> after <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> suspended signing the <a href=\"https://wikipedia.org/wiki/Ukraine%E2%80%93European_Union_Association_Agreement\" class=\"mw-redirect\" title=\"Ukraine-European Union Association Agreement\">Ukraine-European Union Association Agreement</a>.", "links": [{"title": "Euromaidan", "link": "https://wikipedia.org/wiki/Euromaidan"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ukraine-European Union Association Agreement", "link": "https://wikipedia.org/wiki/Ukraine%E2%80%93European_Union_Association_Agreement"}]}, {"year": "2014", "text": "A stampede in Kwekwe, Zimbabwe caused by the police firing tear gas kills at least eleven people and injures 40 others.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/Kwekwe_stadium_stampede\" title=\"Kwekwe stadium stampede\">stampede in Kwekwe, Zimbabwe</a> caused by the police firing <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a> kills at least eleven people and injures 40 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Kwekwe_stadium_stampede\" title=\"Kwekwe stadium stampede\">stampede in Kwekwe, Zimbabwe</a> caused by the police firing <a href=\"https://wikipedia.org/wiki/Tear_gas\" title=\"Tear gas\">tear gas</a> kills at least eleven people and injures 40 others.", "links": [{"title": "Kwekwe stadium stampede", "link": "https://wikipedia.org/wiki/Kwekwe_stadium_stampede"}, {"title": "Tear gas", "link": "https://wikipedia.org/wiki/Tear_gas"}]}, {"year": "2015", "text": "The government of Belgium imposes a security lockdown on Brussels, including the closure of shops, schools, and public transportation, due to potential terrorist attacks.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Federal_Government_of_Belgium\" title=\"Federal Government of Belgium\">government of Belgium</a> imposes a security <a href=\"https://wikipedia.org/wiki/Brussels_lockdown\" class=\"mw-redirect\" title=\"Brussels lockdown\">lockdown</a> on <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, including the closure of shops, schools, and public transportation, due to potential terrorist attacks.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Government_of_Belgium\" title=\"Federal Government of Belgium\">government of Belgium</a> imposes a security <a href=\"https://wikipedia.org/wiki/Brussels_lockdown\" class=\"mw-redirect\" title=\"Brussels lockdown\">lockdown</a> on <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, including the closure of shops, schools, and public transportation, due to potential terrorist attacks.", "links": [{"title": "Federal Government of Belgium", "link": "https://wikipedia.org/wiki/Federal_Government_of_Belgium"}, {"title": "Brussels lockdown", "link": "https://wikipedia.org/wiki/Brussels_lockdown"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}]}, {"year": "2017", "text": "<PERSON> formally resigns as President of Zimbabwe, after thirty-seven years in office.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> formally resigns as President of <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, after thirty-seven years in office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> formally resigns as President of <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>, after thirty-seven years in office.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}]}, {"year": "2019", "text": "Israeli Prime Minister <PERSON> is indicted on charges of bribery, fraud, and breach of trust.", "html": "2019 - Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted on charges of bribery, fraud, and breach of trust.", "no_year_html": "Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted on charges of bribery, fraud, and breach of trust.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "Tesla launches the SUV Cybertruck. A gaffe occurs during the launch event when its \"unbreakable\" windows shatter during demonstration.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Tesla,_Inc.\" title=\"Tesla, Inc.\">Tesla</a> launches the <a href=\"https://wikipedia.org/wiki/Sport_utility_vehicle\" class=\"mw-redirect\" title=\"Sport utility vehicle\">SUV</a> <a href=\"https://wikipedia.org/wiki/Tesla_Cybertruck\" title=\"Tesla Cybertruck\">Cybertruck</a>. A gaffe occurs during the launch event when its \"unbreakable\" windows shatter during demonstration.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tesla,_Inc.\" title=\"Tesla, Inc.\">Tesla</a> launches the <a href=\"https://wikipedia.org/wiki/Sport_utility_vehicle\" class=\"mw-redirect\" title=\"Sport utility vehicle\">SUV</a> <a href=\"https://wikipedia.org/wiki/Tesla_Cybertruck\" title=\"Tesla Cybertruck\">Cybertruck</a>. A gaffe occurs during the launch event when its \"unbreakable\" windows shatter during demonstration.", "links": [{"title": "Tesla, Inc.", "link": "https://wikipedia.org/wiki/Tesla,_Inc."}, {"title": "Sport utility vehicle", "link": "https://wikipedia.org/wiki/Sport_utility_vehicle"}, {"title": "Tesla <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Cybertruck"}]}, {"year": "2021", "text": "An SUV plows through a Christmas parade in Waukesha, Wisconsin, killing six and injuring 62.", "html": "2021 - An SUV <a href=\"https://wikipedia.org/wiki/Waukesha_Christmas_parade_attack\" title=\"Waukesha Christmas parade attack\">plows through a Christmas parade</a> in <a href=\"https://wikipedia.org/wiki/Waukesha,_Wisconsin\" title=\"Waukesha, Wisconsin\">Waukesha, Wisconsin</a>, killing six and injuring 62.", "no_year_html": "An SUV <a href=\"https://wikipedia.org/wiki/Waukesha_Christmas_parade_attack\" title=\"Waukesha Christmas parade attack\">plows through a Christmas parade</a> in <a href=\"https://wikipedia.org/wiki/Waukesha,_Wisconsin\" title=\"Waukesha, Wisconsin\">Waukesha, Wisconsin</a>, killing six and injuring 62.", "links": [{"title": "Waukesha Christmas parade attack", "link": "https://wikipedia.org/wiki/Waukesha_Christmas_parade_attack"}, {"title": "Waukesha, Wisconsin", "link": "https://wikipedia.org/wiki/Waukesha,_Wisconsin"}]}, {"year": "2022", "text": "A magnitude 5.6 earthquake on the Indonesian island of Java kills between 335 and 602 people.", "html": "2022 - A <a href=\"https://wikipedia.org/wiki/2022_West_Java_earthquake\" title=\"2022 West Java earthquake\">magnitude 5.6 earthquake</a> on the Indonesian island of Java kills between 335 and 602 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2022_West_Java_earthquake\" title=\"2022 West Java earthquake\">magnitude 5.6 earthquake</a> on the Indonesian island of Java kills between 335 and 602 people.", "links": [{"title": "2022 West Java earthquake", "link": "https://wikipedia.org/wiki/2022_West_Java_earthquake"}]}], "Births": [{"year": "1495", "text": "<PERSON>, English bishop and historian (d. 1563)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and historian (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and historian (d. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1567", "text": "<PERSON>, French saint, founded the Society of the Sisters of Saint Ursula of the Blessed Virgin (d. 1621)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saint, founded the <a href=\"https://wikipedia.org/wiki/Society_of_the_Sisters_of_Saint_Ursula_of_the_Blessed_Virgin\" title=\"Society of the Sisters of Saint Ursula of the Blessed Virgin\">Society of the Sisters of Saint Ursula of the Blessed Virgin</a> (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French saint, founded the <a href=\"https://wikipedia.org/wiki/Society_of_the_Sisters_of_Saint_Ursula_of_the_Blessed_Virgin\" title=\"Society of the Sisters of Saint Ursula of the Blessed Virgin\">Society of the Sisters of Saint Ursula of the Blessed Virgin</a> (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ge"}, {"title": "Society of the Sisters of Saint Ursula of the Blessed Virgin", "link": "https://wikipedia.org/wiki/Society_of_the_Sisters_of_<PERSON>_<PERSON>_of_the_Blessed_Virgin"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON>, Dutch poet (d. 1669)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch poet (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch poet (d. 1669)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iers"}]}, {"year": "1692", "text": "<PERSON>, Italian poet and academic (d. 1768)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and academic (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and academic (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON><PERSON>, French writer and philosopher (d. 1778)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/Vol<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French writer and philosopher (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vol<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French writer and philosopher (d. 1778)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Voltaire"}]}, {"year": "1718", "text": "<PERSON>, German composer, critic, and theorist (d. 1795)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, critic, and theorist (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, critic, and theorist (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, American physician and politician, 6th Governor of New Hampshire (d. 1795)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1760", "text": "<PERSON>, American sergeant (d. 1850)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, German theologian, philosopher, and scholar (d. 1834)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian, philosopher, and scholar (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian, philosopher, and scholar (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, American surgeon, \"Father of Gastric Physiology\" (d. 1853)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon, \"Father of Gastric Physiology\" (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon, \"Father of Gastric Physiology\" (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, Canadian businessman, founded the Cunard Line (d. 1865)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded the <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded the <a href=\"https://wikipedia.org/wiki/Cunard_Line\" title=\"Cunard Line\">Cunard Line</a> (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cunard Line", "link": "https://wikipedia.org/wiki/Cunard_Line"}]}, {"year": "1811", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish politician, physicist, and revolutionary activist (d. 1857)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish politician, physicist, and revolutionary activist (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish politician, physicist, and revolutionary activist (d. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American lawyer, anthropologist, and theorist (d. 1881)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, anthropologist, and theorist (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, anthropologist, and theorist (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON><PERSON>, American businesswoman and financier (d. 1916)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and financier (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman and financier (d. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Princess <PERSON> of England (d. 1901)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of England (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of England (d. 1901)", "links": [{"title": "<PERSON>, Princess <PERSON>", "link": "https://wikipedia.org/wiki/Victoria,_Princess_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian cardinal and theologian (d. 1926)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian cardinal and theologian (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian cardinal and theologian (d. 1926)", "links": [{"title": "Désir<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9sir%C3%A9-<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Spanish guitarist and composer (d. 1909)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Francisco_T%C3%A1rrega\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Spanish guitarist and composer (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_T%C3%A1rrega\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Spanish guitarist and composer (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_T%C3%A1rrega"}]}, {"year": "1853", "text": "<PERSON> of Egypt (d. 1917)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egypt\" title=\"<PERSON> of Egypt\"><PERSON> of Egypt</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egypt\" title=\"<PERSON> of Egypt\"><PERSON> of Egypt</a> (d. 1917)", "links": [{"title": "<PERSON> of Egypt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Egypt"}]}, {"year": "1854", "text": "<PERSON> (d. 1922)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_XV\" title=\"Pope Benedict XV\">Pope <PERSON></a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict XV\">Pope <PERSON></a> (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian poet and author (d. 1900)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Sigbj%C3%B8<PERSON>_<PERSON>\" title=\"Sigbj<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian poet and author (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigbj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON>gb<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian poet and author (d. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigbj%C3%B8rn_<PERSON><PERSON><PERSON><PERSON>er"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 17th <PERSON><PERSON><PERSON><PERSON> (d. 1914)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Konishiki_Yasokichi_I\" title=\"Konishiki Yasokichi I\"><PERSON><PERSON><PERSON> Yasokichi <PERSON></a>, Japanese sumo wrestler, the 17th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Konishiki_Yasokichi_I\" title=\"Konishiki Yasokichi I\"><PERSON><PERSON><PERSON> Yasokichi I</a>, Japanese sumo wrestler, the 17th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON> Yasokichi I", "link": "https://wikipedia.org/wiki/Konishiki_<PERSON><PERSON><PERSON><PERSON>_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1870", "text": "<PERSON>, Lithuanian-American activist and author (d. 1936)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American activist and author (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American activist and author (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Australian cricketer and politician (d. 1946)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and politician (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and politician (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, English cricketer and politician (d. 1947)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and politician (d. 1947)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Norwegian author and educator (d. 1939)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and educator (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and educator (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, German composer and educator (d. 1933)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Sig<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>g<PERSON><PERSON>\"><PERSON>g<PERSON><PERSON></a>, German composer and educator (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sig<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>g<PERSON><PERSON>\"><PERSON>g<PERSON><PERSON></a>, German composer and educator (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sig<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, German lawyer and politician, German Minister of Justice (d. 1949)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)\" title=\"Federal Ministry of Justice (Germany)\">German Minister of Justice</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)\" title=\"Federal Ministry of Justice (Germany)\">German Minister of Justice</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Justice (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Justice_(Germany)"}]}, {"year": "1886", "text": "<PERSON>, English author and politician (d. 1968)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American politician (d. 1984)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Russian-American activist (d. 1980)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American activist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American activist (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Belgian painter (d. 1967)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ma<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Magritte"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American actress (d. 1967)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist and politician, 1st Chief Minister of Odisha (d. 1987)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ma<PERSON>ab\" title=\"<PERSON><PERSON><PERSON><PERSON> Mahatab\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Odisha\" class=\"mw-redirect\" title=\"List of Chief Ministers of Odisha\">Chief Minister of Odisha</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Mahatab\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Odisha\" class=\"mw-redirect\" title=\"List of Chief Ministers of Odisha\">Chief Minister of Odisha</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hare<PERSON><PERSON><PERSON>_<PERSON>hatab"}, {"title": "List of Chief Ministers of Odisha", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Odisha"}]}, {"year": "1902", "text": "<PERSON>, Canadian sportscaster (d. 1985)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Russian soldier, economist, and politician (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier, economist, and politician (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier, economist, and politician (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Polish-American novelist and short story writer, Nobel Prize laureate (d. 1991)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1904", "text": "<PERSON>, American saxophonist and clarinet player (d. 1969)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, British biographer (d. 2006)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British biographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British biographer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American songwriter and music producer (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Buck Ram\"><PERSON></a>, American songwriter and music producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Buck Ram\"><PERSON></a>, American songwriter and music producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Italian-American author and illustrator (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American author and illustrator (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Politi"}]}, {"year": "1908", "text": "<PERSON>, American author and educator (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actress and dancer (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English director, producer, and screenwriter (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English director, producer, and screenwriter (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Estonian mathematician, author, and academic (d. 1975)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian mathematician, author, and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian mathematician, author, and academic (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ro"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Turkish physician and politician, Turkish Minister of Health (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)\" title=\"Ministry of Health (Turkey)\">Turkish Minister of Health</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nusret_Fi%C5%9Fek"}, {"title": "Ministry of Health (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Turkey)"}]}, {"year": "1914", "text": "<PERSON>, French physician and philosopher (d. 1995)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henri <PERSON>\"><PERSON></a>, French physician and philosopher (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henri <PERSON>\"><PERSON></a>, French physician and philosopher (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Laborit"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Australian footballer and coach (d. 1973)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and coach (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and coach (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American football player and soldier (d. 1998)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Korean politician, diplomat, and soldier (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean politician, diplomat, and soldier (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean politician, diplomat, and soldier (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American director and producer (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 1988)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player and manager (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1921", "text": "<PERSON>, American pilot (d. 1975)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American basketball player and coach (d. 2002)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Yugoslav politician, 28th Prime Minister of Yugoslavia (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia\" title=\"Prime Minister of Yugoslavia\">Prime Minister of Yugoslavia</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Planinc"}, {"title": "Prime Minister of Yugoslavia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Yugoslavia"}]}, {"year": "1924", "text": "<PERSON>, English author and academic (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian general and politician, 5th Federal Secretary of People's Defence (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Yugoslavia)\" class=\"mw-redirect\" title=\"Ministry of Defense (Yugoslavia)\">Federal Secretary of People's Defence</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian general and politician, 5th <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Yugoslavia)\" class=\"mw-redirect\" title=\"Ministry of Defense (Yugoslavia)\">Federal Secretary of People's Defence</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veljko_Kadijevi%C4%87"}, {"title": "Ministry of Defense (Yugoslavia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defense_(Yugoslavia)"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Finnish actor (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish actor (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1926", "text": "<PERSON>, American cardinal (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American businesswoman (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Georgia_Frontiere\" title=\"Georgia Frontiere\">Georgia Frontiere</a>, American businesswoman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_Frontiere\" title=\"Georgia Frontiere\">Georgia Frontiere</a>, American businesswoman (d. 2008)", "links": [{"title": "Georgia Frontiere", "link": "https://wikipedia.org/wiki/Georgia_Frontiere"}]}, {"year": "1929", "text": "<PERSON>, American author and academic (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Marilyn_French\" title=\"Marilyn French\"><PERSON></a>, American author and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marilyn_French\" title=\"Marilyn French\"><PERSON></a>, American author and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Canadian historian, journalist, and politician (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, journalist, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, journalist, and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Slovenian journalist, author, and playwright (d. 1990)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o%C5%BEanc\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian journalist, author, and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o%C5%BEanc\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian journalist, author, and playwright (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marjan_Ro%C5%BEanc"}]}, {"year": "1931", "text": "<PERSON>, American archaeologist and academic (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Georgian chemist and physicist (d. 1985)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>az_<PERSON>dze\" title=\"<PERSON>az Dogonadze\"><PERSON><PERSON></a>, Georgian chemist and physicist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>az_<PERSON>dze\" title=\"<PERSON>az Dogonadze\"><PERSON><PERSON></a>, Georgian chemist and physicist (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>az_<PERSON>dze"}]}, {"year": "1931", "text": "<PERSON>, <PERSON>, English businessman", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian pianist and composer (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist and composer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English author and screenwriter (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and screenwriter (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, Danish composer (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish composer (d. 2016)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American country music singer-songwriter (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American educator and mathematician (d. 2002)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and mathematician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Falcon<PERSON>\"><PERSON><PERSON></a>, American educator and mathematician (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1934", "text": "<PERSON>, American actor, director, and playwright", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian cricketer (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Chinese-Australian surgeon (d. 1991)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian surgeon (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian surgeon (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian politician (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Polish-English actress (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American actress, producer, and activist", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American educator and politician, 30th Treasurer of Pennsylvania (d. 1987)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 30th <a href=\"https://wikipedia.org/wiki/Treasurer_of_Pennsylvania\" title=\"Treasurer of Pennsylvania\">Treasurer of Pennsylvania</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician, 30th <a href=\"https://wikipedia.org/wiki/Treasurer_of_Pennsylvania\" title=\"Treasurer of Pennsylvania\">Treasurer of Pennsylvania</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Treasurer of Pennsylvania", "link": "https://wikipedia.org/wiki/Treasurer_of_Pennsylvania"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Dominican comedian and television host (d. 2010)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican comedian and television host (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican comedian and television host (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American basketball player (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American commander and author (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Russian ballerina, choreographer, and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina, choreographer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ballerina, choreographer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English-American actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American songwriter, musician, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American songwriter, musician, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, German educator and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Heidemarie_Wieczorek-<PERSON>l\" title=\"Heidemarie Wieczorek-Zeul\"><PERSON><PERSON><PERSON><PERSON> Wiecz<PERSON>-<PERSON></a>, German educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heidemarie_Wie<PERSON>-<PERSON>\" title=\"Heidemarie Wieczorek-Zeul\"><PERSON><PERSON><PERSON><PERSON></a>, German educator and politician", "links": [{"title": "Heidemarie Wieczorek-Zeul", "link": "https://wikipedia.org/wiki/Heidemarie_Wiecz<PERSON><PERSON>-<PERSON>l"}]}, {"year": "1943", "text": "<PERSON>, American businessman and politician, 48th Governor of Tennessee", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1943", "text": "<PERSON>, French race car driver", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American lawyer and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American artist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American actress, singer, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>wn\"><PERSON><PERSON></a>, American actress, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Hawn\"><PERSON><PERSON></a>, American actress, singer, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American jazz drummer (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American jazz drummer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American jazz drummer (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Lebanese general and politician, 16th President of Lebanon", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese general and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese general and politician, 16th <a href=\"https://wikipedia.org/wiki/President_of_Lebanon\" title=\"President of Lebanon\">President of Lebanon</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Lebanon", "link": "https://wikipedia.org/wiki/President_of_Lebanon"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Egyptian lawyer and judge (d. 2015)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer and judge (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lawyer and judge (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and musician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of Abersoch, Welsh banker and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Abersoch\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of Abersoch\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Abersoch</a>, Welsh banker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Abersoch\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of Abersoch\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Abersoch</a>, Welsh banker and politician", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON> of Abersoch", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Abe<PERSON>och"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Norwegian lawyer and jurist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian lawyer and jurist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American actress and singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Lorna Lu<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rna_Luft"}]}, {"year": "1953", "text": "<PERSON>, English-American journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, English journalist, author, and poet", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and poet", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American basketball player, coach, and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian radio and television host and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Glenn_<PERSON>\" title=\"Glenn Ridge\"><PERSON></a>, Australian radio and television host and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glenn_Ridge\" title=\"Glenn Ridge\"><PERSON></a>, Australian radio and television host and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glenn_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jones\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Estonian footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English rugby player, author, and educator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player, author, and educator", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1960", "text": "<PERSON>, American actor, director, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American bass player and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Portuguese footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>_Pinto\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>_Pinto\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American Christian music singer-songwriter, musician, record producer, actor, author, and social activist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian music singer-songwriter, musician, record producer, actor, author, and social activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Christian music singer-songwriter, musician, record producer, actor, author, and social activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English football player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1962)\" title=\"<PERSON> (footballer, born 1962)\"><PERSON></a>, English football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1962)\" title=\"<PERSON> (footballer, born 1962)\"><PERSON></a>, English football player", "links": [{"title": "<PERSON> (footballer, born 1962)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1962)"}]}, {"year": "1963", "text": "<PERSON>, Manx motorcycle racer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Haitian-American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Olden_Polynice\" title=\"Olden Polynice\"><PERSON><PERSON> Pol<PERSON></a>, Haitian-American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olden_Polynice\" title=\"Olden Polynice\"><PERSON><PERSON> Polynice</a>, Haitian-American basketball player and coach", "links": [{"title": "Olden Polynice", "link": "https://wikipedia.org/wiki/Olden_Polynice"}]}, {"year": "1964", "text": "<PERSON>, English actress, television and radio presenter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, television and radio presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, television and radio presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6rk\" title=\"Björk\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6rk\" title=\"Björk\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter", "links": [{"title": "Björk", "link": "https://wikipedia.org/wiki/Bj%C3%B6rk"}]}, {"year": "1965", "text": "<PERSON>, American basketball player (d. 1993)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Troy_<PERSON>kman"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Russian chess player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chess player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian chess player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ev<PERSON>_<PERSON>v"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thanasis Kolitsidakis\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Thanasis Kolitsidakis\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thanasis_Ko<PERSON>dakis"}]}, {"year": "1967", "text": "<PERSON>, American race car driver (d. 2023)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ken <PERSON>\"><PERSON></a>, American race car driver (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ken <PERSON>\"><PERSON></a>, American race car driver (d. 2023)", "links": [{"title": "Ken <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Tripp_Cromer\" title=\"Tripp Cromer\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tripp_Cromer\" title=\"Tripp Cromer\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>p_Cromer"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist (d. 2021)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American model and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Belgian politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand-English cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English singer-songwriter, bass player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1968", "text": "<PERSON>, American boxer, sportscaster, and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, sportscaster, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer, sportscaster, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American baseball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American baseball player and actor", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1970", "text": "<PERSON>, Filipino journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian cricketer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player, actor, and talk show host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English author and critic", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Rain_Phoenix\" title=\"Rain Phoenix\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rain_Phoenix\" title=\"Rain Phoenix\"><PERSON></a>, American actress and singer", "links": [{"title": "Rain Phoenix", "link": "https://wikipedia.org/wiki/Rain_Phoenix"}]}, {"year": "1974", "text": "<PERSON>, Mexican actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_de_Tavira"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Romanian long-distance runner", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian long-distance runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ahi"}]}, {"year": "1976", "text": "<PERSON>, German footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English figure skater", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_footballer)\" title=\"<PERSON> (Australian footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (Australian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_footballer)"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Spanish actress and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_Jim%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luc%C3%ADa_Jim%C3%A9nez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luc%C3%ADa_Jim%C3%A9nez"}]}, {"year": "1979", "text": "<PERSON>, Italian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>romile_Swift\" title=\"Stromile Swift\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stromile_Swift\" title=\"Stromile Swift\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stromile_Swift"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alex_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American author and director", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Costa Rican footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Costa_Rican_footballer)\" title=\"<PERSON> (Costa Rican footballer)\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(Costa_Rican_footballer)\" title=\"<PERSON> (Costa Rican footballer)\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON> (Costa Rican footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%<PERSON><PERSON><PERSON>_(Costa_Rican_footballer)"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Latvian javelin thrower", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ain%C4%81rs_Kovals\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ain%C4%81rs_Kovals\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ain%C4%81rs_Kovals"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Mexican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3n"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Romanian fashion designer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u\" title=\"<PERSON><PERSON><PERSON> Ciolacu\"><PERSON><PERSON><PERSON></a>, Romanian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ciolacu\"><PERSON><PERSON><PERSON></a>, Romanian fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ioana_Ciolacu"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Greek archer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek archer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American wrestler and television personality", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bella\"><PERSON><PERSON></a>, American wrestler and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bella\"><PERSON><PERSON></a>, American wrestler and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American wrestler and television personality", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bella\"><PERSON></a>, American wrestler and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Spanish motorcycle racer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1984", "text": "<PERSON>, American actress, singer, and director", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American actress and singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian singer-songwriter and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Navas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Navas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Navas"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American YouTuber, comedian, actress, and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>, comedian, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/YouTuber\" title=\"YouTuber\">YouTuber</a>, comedian, actress, and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "YouTuber", "link": "https://wikipedia.org/wiki/YouTuber"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist (d. 2014)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English actor and musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_<PERSON>dio"}]}, {"year": "1987", "text": "<PERSON>, Swiss footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian chess player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball)"}]}, {"year": "1988", "text": "<PERSON>, Canadian skier", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Len_V%C3%A4ljas\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Len_V%C3%A4ljas\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Len_V%C3%A4ljas"}]}, {"year": "1988", "text": "<PERSON>, American soccer player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/D%C3%A1rvin_Ch%C3%A1vez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A1rvin_Ch%C3%A1vez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A1rvin_Ch%C3%A1vez"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Venezuelan baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Pirela\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Pirela\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Pirela"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1989)\" title=\"<PERSON> (basketball, born 1989)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1989)\" title=\"<PERSON> (basketball, born 1989)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1989)", "link": "https://wikipedia.org/wiki/<PERSON>_(basketball,_born_1989)"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English cyclist", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> King\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Dani King\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English field hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Twigg\" title=\"<PERSON> Twigg\"><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Twigg\" title=\"<PERSON> Twigg\"><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Twigg"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Ethiopian sprinter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>z_<PERSON>yana"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Te<PERSON>po\" title=\"<PERSON><PERSON>po\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Te<PERSON>po\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peni_Terepo"}]}, {"year": "1994", "text": "<PERSON>, Swedish ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Sa%C3%BAl_%C3%91%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa%C3%BAl_%C3%91%C3%ADguez\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sa%C3%BAl_%C3%91%C3%ADguez"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teller\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teller\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian cyclist", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Ognjen_Ili%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ognje<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ognjen_Ili%C4%87"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American soccer player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soccer player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "2000", "text": "<PERSON>, English-Danish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Riley\" title=\"<PERSON>\"><PERSON></a>, English-Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Riley\" title=\"<PERSON>\"><PERSON></a>, English-Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matt_O%27Riley"}]}, {"year": "2004", "text": "<PERSON>, English footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "615", "text": "<PERSON><PERSON><PERSON>, Irish missionary and saint (b. 543)", "html": "615 - <a href=\"https://wikipedia.org/wiki/Columbanus\" title=\"Columbanus\"><PERSON><PERSON><PERSON></a>, Irish missionary and saint (b. 543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbanus\" title=\"Columbanus\"><PERSON><PERSON><PERSON></a>, Irish missionary and saint (b. 543)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Columbanus"}]}, {"year": "933", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Arab imam and scholar (b. 853)", "html": "933 - <a href=\"https://wikipedia.org/wiki/Al<PERSON>Tahaw<PERSON>\" title=\"Al-Tahawi\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Arab imam and scholar (b. 853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Tahawi\" title=\"Al-Tahawi\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Arab imam and scholar (b. 853)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-<PERSON>hawi"}]}, {"year": "1011", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (b. 950)", "html": "1011 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 950)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "1136", "text": "<PERSON>, English archbishop (b. 1070)", "html": "1136 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1070)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1070)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1150", "text": "<PERSON> of Navarre (b. 1112)", "html": "1150 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Ram%C3%<PERSON>rez_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1112)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Ram%C3%<PERSON>rez_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1112)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/Garc%C3%ADa_Ram%C3%ADrez_of_Navarre"}]}, {"year": "1325", "text": "<PERSON><PERSON> of Moscow, Prince of Moscow and Vladimir", "html": "1325 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Moscow\" title=\"<PERSON><PERSON> of Moscow\"><PERSON><PERSON> of Moscow</a>, Prince of Moscow and Vladimir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Moscow\" title=\"<PERSON><PERSON> of Moscow\"><PERSON><PERSON> of Moscow</a>, Prince of Moscow and Vladimir", "links": [{"title": "<PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>ry_of_Moscow"}]}, {"year": "1361", "text": "<PERSON>, Duke of Burgundy (b. 1346)", "html": "1361 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1346)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1346)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1555", "text": "<PERSON><PERSON>, German mineralogist, philologist, and scholar (b. 1490)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Agricola\" title=\"<PERSON>ius Agricola\"><PERSON><PERSON></a>, German mineralogist, philologist, and scholar (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gricola\" title=\"<PERSON>ius Agricola\"><PERSON><PERSON></a>, German mineralogist, philologist, and scholar (b. 1490)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgius_Agricola"}]}, {"year": "1566", "text": "<PERSON><PERSON><PERSON>, Italian poet and author (b. 1507)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (b. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (b. 1507)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1579", "text": "<PERSON>, English merchant and financier (b. 1519)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and financier (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English merchant and financier (b. 1519)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, 8th Earl of Kent, English politician, Lord Lieutenant of Bedfordshire (b. 1583)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Kent\" title=\"<PERSON>, 8th Earl of Kent\"><PERSON>, 8th Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire\" title=\"Lord Lieutenant of Bedfordshire\">Lord Lieutenant of Bedfordshire</a> (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Kent\" title=\"<PERSON>, 8th Earl of Kent\"><PERSON>, 8th Earl of Kent</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire\" title=\"Lord Lieutenant of Bedfordshire\">Lord Lieutenant of Bedfordshire</a> (b. 1583)", "links": [{"title": "<PERSON>, 8th Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Earl_of_Kent"}, {"title": "Lord Lieutenant of Bedfordshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Bedfordshire"}]}, {"year": "1652", "text": "<PERSON>, Polish mathematician, physician, and astronomer (b. 1585)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%BCek\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician, physician, and astronomer (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%BCek\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician, physician, and astronomer (b. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_B<PERSON>%C5%BCek"}]}, {"year": "1695", "text": "<PERSON>, English organist and composer (b. 1659)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1710", "text": "<PERSON>, Italian organist and composer (b. 1637)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1775", "text": "<PERSON>, English botanist and author (b. 1719)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, English botanist and author (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(botanist)\" title=\"<PERSON> (botanist)\"><PERSON></a>, English botanist and author (b. 1719)", "links": [{"title": "<PERSON> (botanist)", "link": "https://wikipedia.org/wiki/<PERSON>(botanist)"}]}, {"year": "1782", "text": "<PERSON>, French engineer (b. 1709)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (b. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer (b. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, German poet and author (b. 1777)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Russian poet and playwright (b. 1769)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and playwright (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and playwright (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Japanese academic and politician (b. 1830)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Yoshida_Sh%C5%8Din\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese academic and politician (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoshida_Sh%C5%8Din\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese academic and politician (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yoshida_Sh%C5%8Din"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, French priest and activist (b. 1802)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and activist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and activist (b. 1802)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, Czech historian and poet (b. 1811)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADr_Erb<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech historian and poet (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADr_Erben\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech historian and poet (b. 1811)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADr_E<PERSON>en"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Spanish painter (b. 1838)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Mari%C3%A0_Fortuny\" class=\"mw-redirect\" title=\"Marià Fortuny\"><PERSON><PERSON></a>, Spanish painter (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari%C3%A0_Fortuny\" class=\"mw-redirect\" title=\"Marià Fortuny\"><PERSON><PERSON></a>, Spanish painter (b. 1838)", "links": [{"title": "Marià Fortuny", "link": "https://wikipedia.org/wiki/Mari%C3%A0_Fortuny"}]}, {"year": "1881", "text": "<PERSON><PERSON>, German-Austrian geologist and ethnographer (b. 1794)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Ami_<PERSON>u%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian geologist and ethnographer (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ami_<PERSON>u%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian geologist and ethnographer (b. 1794)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ami_Bou%C3%A9"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician, 24th Vice President of the United States (b. 1844)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/G<PERSON>ret_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1844)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ar<PERSON>_Hobart"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1907", "text": "<PERSON>, Australian cricketer (b. 1847)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1847)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1907", "text": "<PERSON>-<PERSON>, German painter (b. 1876)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter (b. 1876)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German-Russian geologist and botanist (b. 1832)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1832)\" class=\"mw-redirect\" title=\"<PERSON> (1832)\"><PERSON></a>, German-Russian geologist and botanist (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1832)\" class=\"mw-redirect\" title=\"<PERSON> (1832)\"><PERSON></a>, German-Russian geologist and botanist (b. 1832)", "links": [{"title": "<PERSON> (1832)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1832)"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Norwegian-Danish painter (b. 1851)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON>in_Kr%C3%B8yer\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian-Danish painter (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Kr%C3%B8yer\" title=\"<PERSON><PERSON><PERSON> Se<PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian-Danish painter (b. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P<PERSON><PERSON>_<PERSON>ver<PERSON>_Kr%C3%B8yer"}]}, {"year": "1916", "text": "<PERSON> of Austria (b. 1830)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (b. 1830)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1922", "text": "<PERSON>, Mexican journalist and activist (b. 1874)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mag%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist and activist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mag%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist and activist (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ricardo_<PERSON>_Mag%C3%B3n"}]}, {"year": "1926", "text": "<PERSON>, American golfer (b. 1886)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, <PERSON> (b. 1858)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> XXVII, <PERSON>\"><PERSON>, Prince <PERSON></a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>X<PERSON>,_Prince_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Heinrich XXVII, <PERSON>\"><PERSON>, <PERSON><PERSON></a> (b. 1858)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>X<PERSON>,_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian politician, 10th Premier of Western Australia (b. 1876)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1938", "text": "<PERSON>, Polish-American pianist and composer (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actress and playwright (b. 1860)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and playwright (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Davis"}]}, {"year": "1942", "text": "Count <PERSON>, Austrian-Hungarian politician, Foreign Minister of Austria-Hungary (b. 1863)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian-Hungarian politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Austria-Hungary\" title=\"List of foreign ministers of Austria-Hungary\">Foreign Minister of Austria-Hungary</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian-Hungarian politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Austria-Hungary\" title=\"List of foreign ministers of Austria-Hungary\">Foreign Minister of Austria-Hungary</a> (b. 1863)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>"}, {"title": "List of foreign ministers of Austria-Hungary", "link": "https://wikipedia.org/wiki/List_of_foreign_ministers_of_Austria-Hungary"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, South African general and politician, 3rd Prime Minister of South Africa (b. 1866)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. B<PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, South African general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. B<PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, South African general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (b. 1866)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Irish suffragist, trade unionist, and Irish republican (b. 1887)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish suffragist, trade unionist, and Irish republican (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish suffragist, trade unionist, and Irish republican (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>,  American humorist, newspaper columnist, and actor (b. 1889)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist, newspaper columnist, and actor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist, newspaper columnist, and actor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American boxer (b. 1920)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1920)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1945", "text": "<PERSON>, American author (b. 1873)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Ellen_<PERSON>\" title=\"Ellen <PERSON>\"><PERSON></a>, American author (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ellen_<PERSON>\" title=\"Ellen <PERSON>\"><PERSON></a>, American author (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American general (b. 1889)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexander <PERSON>\"><PERSON></a>, American general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexander Patch\"><PERSON></a>, American general (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian politician, 22nd Premier of Queensland (b. 1879)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1951", "text": "<PERSON>, French soldier who died during the Indochina War", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier who died during the Indochina War", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier who died during the Indochina War", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Italian race car driver (b. 1903)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese polygraph (b. 1868)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira\" title=\"<PERSON><PERSON><PERSON><PERSON> Cabreira\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese polygraph (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira\" title=\"<PERSON><PERSON><PERSON><PERSON> Cabreira\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese polygraph (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Cabreira"}]}, {"year": "1953", "text": "<PERSON>, American clarinet player and composer (b. 1893)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American general and politician, 6th Governor-General of the Philippines (b. 1873)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Governor-General of the Philippines</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Philippines\" title=\"Governor-General of the Philippines\">Governor-General of the Philippines</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of the Philippines", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Philippines"}]}, {"year": "1958", "text": "<PERSON>, American baseball player, manager, and sportscaster (b. 1909)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American boxer, referee, and actor (b. 1909)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer, referee, and actor (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer, referee, and actor (b. 1909)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1962", "text": "<PERSON>, Canadian canoeist (b. 1904)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian canoeist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian canoeist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Estonian composer and educator (b. 1885)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and educator (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American ornithologist and author (b. 1890)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American architect and public housing advocate (b. 1905)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and public housing advocate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and public housing advocate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON> <PERSON><PERSON>, Jr., American author (b. 1896)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>, Jr.\"><PERSON><PERSON> <PERSON><PERSON>, Jr.</a>, American author (b. 1896)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1970", "text": "<PERSON><PERSON>, Canadian lacrosse and ice hockey player (b. 1887)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Newsy_Lalonde\" title=\"<PERSON>y <PERSON>e\"><PERSON><PERSON></a>, Canadian lacrosse and ice hockey player (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newsy_Lalonde\" title=\"Newsy Lalonde\"><PERSON><PERSON></a>, Canadian lacrosse and ice hockey player (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Newsy_Lalonde"}]}, {"year": "1970", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian physicist and academic, Nobel Prize laureate (b. 1888)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/C._V._Raman\" title=\"C. V. Raman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._V._Raman\" title=\"C. V. Raman\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>n"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1973", "text": "<PERSON>, American lawyer and politician (b. 1902)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American radio host (b. 1897)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Gambling\" title=\"<PERSON> Gam<PERSON>\"><PERSON></a>, American radio host (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ambling\" title=\"<PERSON> Gambling\"><PERSON></a>, American radio host (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bling"}]}, {"year": "1974", "text": "<PERSON>, Swiss-Dutch pianist and composer (b. 1890)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Swiss-Dutch pianist and composer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Swiss-Dutch pianist and composer (b. 1890)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Icelandic author (b. 1889)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic author (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic author (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Mexican actress (b. 1895)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Sara_Garc%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sara_Garc%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican actress (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_Garc%C3%ADa"}]}, {"year": "1981", "text": "<PERSON>, American actor and comedian (b. 1906)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English activist and author (b. 1894)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and author (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist and author (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player (b. 1967)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (b. 1967)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter and actor (b. 1904)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American singer-songwriter and actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American singer-songwriter and actor (b. 1904)", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)"}]}, {"year": "1987", "text": "<PERSON>, American politician and 42nd Governor of Alabama (b. 1908)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "1988", "text": "<PERSON>, American baseball player and scout (b. 1903)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian director, producer, and screenwriter (b. 1928)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, Canadian director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American author and illustrator (b. 1931)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian wrestler and referee (b. 1954)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and referee (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and referee (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American businessman and philanthropist (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Laotian soldier and politician, 2nd President of Laos (b. 1920)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Laotian soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Laos\" title=\"President of Laos\">President of Laos</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Laotian soldier and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Laos\" title=\"President of Laos\">President of Laos</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hane"}, {"title": "President of Laos", "link": "https://wikipedia.org/wiki/President_of_Laos"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter and drummer (b. 1956)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and drummer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and drummer (b. 1956)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1993", "text": "<PERSON>, American actor (b. 1934)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Dutch-American astronomer and academic (b. 1899)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American astronomer and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American astronomer and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor and manager (b. 1935)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_manager)\" title=\"<PERSON> (music manager)\"><PERSON></a>, English actor and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_manager)\" title=\"<PERSON> (music manager)\"><PERSON></a>, English actor and manager (b. 1935)", "links": [{"title": "<PERSON> (music manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(music_manager)"}]}, {"year": "1995", "text": "<PERSON>, Indian-English diplomat, British ambassador to Kazakhstan (b. 1940)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Indian-English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Kazakhstan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Kazakhstan\">British ambassador to Kazakhstan</a> (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Indian-English diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Kazakhstan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Kazakhstan\">British ambassador to Kazakhstan</a> (b. 1940)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_(diplomat)"}, {"title": "List of Ambassadors of the United Kingdom to Kazakhstan", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Kazakhstan"}]}, {"year": "1996", "text": "<PERSON>, English organist and composer (b 1916)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English organist and composer (b 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English organist and composer (b 1916)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Pakistani-English physicist and academic, Nobel Prize laureate (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1999", "text": "<PERSON>, English actor, author, and illustrator (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, author, and illustrator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, author, and illustrator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Spanish economist and politician (b. 1937)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish economist and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Czech runner (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1topek\" title=\"<PERSON>\"><PERSON></a>, Czech runner (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1topek\" title=\"<PERSON>\"><PERSON></a>, Czech runner (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_Z%C3%A1topek"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American singer-songwriter and pianist (b. 1916)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Scottish soldier (b. 1896)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)\" title=\"<PERSON> (veteran)\"><PERSON></a>, Scottish soldier (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)\" title=\"<PERSON> (veteran)\"><PERSON></a>, Scottish soldier (b. 1896)", "links": [{"title": "<PERSON> (veteran)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(veteran)"}]}, {"year": "2005", "text": "<PERSON>, American journalist and academic (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Somalian-Djiboutian politician, 1st President of Djibouti (b. 1916)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ptido<PERSON>\" title=\"<PERSON> Go<PERSON> Aptido<PERSON>\"><PERSON></a>, Somalian-Djiboutian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Djibouti\" class=\"mw-redirect\" title=\"President of Djibouti\">President of Djibouti</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ptido<PERSON>\" title=\"<PERSON>ido<PERSON>\"><PERSON></a>, Somalian-Djiboutian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Djibouti\" class=\"mw-redirect\" title=\"President of Djibouti\">President of Djibouti</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aptidon"}, {"title": "President of Djibouti", "link": "https://wikipedia.org/wiki/President_of_Djibouti"}]}, {"year": "2006", "text": "<PERSON>, Lebanese lawyer and politician (b. 1972)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Spanish actor, director, and screenwriter (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Fern%C3%A1n_G%C3%B3mez"}]}, {"year": "2007", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1928)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2007", "text": "<PERSON>, New Zealand cricketer (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Russian engineer and astronaut (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American author (b. 1949)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Church_Mailer\" title=\"<PERSON> Church Mailer\"><PERSON></a>, American author (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Church_Mailer\" title=\"<PERSON> Church Mailer\"><PERSON></a>, American author (b. 1949)", "links": [{"title": "<PERSON> Church Mailer", "link": "https://wikipedia.org/wiki/Norris_Church_Mailer"}]}, {"year": "2010", "text": "<PERSON>, American activist and politician (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(libertarian)\" class=\"mw-redirect\" title=\"<PERSON> (libertarian)\"><PERSON></a>, American activist and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(libertarian)\" class=\"mw-redirect\" title=\"<PERSON> (libertarian)\"><PERSON></a>, American activist and politician (b. 1943)", "links": [{"title": "<PERSON> (libertarian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(libertarian)"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American painter and author, co-founded the DuSable Museum of African American History (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author, co-founded the <a href=\"https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History\" class=\"mw-redirect\" title=\"DuSable Museum of African American History\">DuSable Museum of African American History</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and author, co-founded the <a href=\"https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History\" class=\"mw-redirect\" title=\"DuSable Museum of African American History\">DuSable Museum of African American History</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DuSable Museum of African American History", "link": "https://wikipedia.org/wiki/DuSable_Museum_of_African_American_History"}]}, {"year": "2011", "text": "<PERSON>, American science fiction and fantasy author (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction and fantasy author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction and fantasy author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director, producer, and screenwriter (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2012", "text": "<PERSON>, American pianist (b. 1990)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Austin_Peralta\" title=\"Austin Peralta\"><PERSON></a>, American pianist (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Peralta\" title=\"Austin Peralta\"><PERSON></a>, American pianist (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Peralta"}]}, {"year": "2013", "text": "<PERSON>, American journalist and author (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (b. 1935)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2013", "text": "<PERSON>, Norwegian-American businessman and philanthropist, founded The Kavli Foundation (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Ka<PERSON>li_Foundation_(United_States)\" title=\"Kavli Foundation (United States)\">The Kavli Foundation</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Foundation_(United_States)\" title=\"Kavli Foundation (United States)\">The Kavli Foundation</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kavli Foundation (United States)", "link": "https://wikipedia.org/wiki/Kavli_Foundation_(United_States)"}]}, {"year": "2013", "text": "<PERSON>, American astronomer and author (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and author (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American basketball player and coach (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Vern_<PERSON>\" title=\"<PERSON>ern Mikkelsen\"><PERSON><PERSON></a>, American basketball player and coach (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ern_<PERSON>\" title=\"<PERSON>ern Mikkelsen\"><PERSON><PERSON></a>, American basketball player and coach (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French composer (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Vietnamese general (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_%C4%90%C3%ADnh\" title=\"Tôn <PERSON>h<PERSON>\">T<PERSON><PERSON><PERSON></a>, Vietnamese general (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_%C4%90%C3%ADnh\" title=\"Tôn Thấ<PERSON>\">T<PERSON><PERSON></a>, Vietnamese general (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B4n_Th%E1%BA%A5t_%C4%90%C3%ADnh"}]}, {"year": "2013", "text": "<PERSON>, Canadian-American wrestler (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American wrestler (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American soldier and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American soldier and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English general (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1929)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}]}, {"year": "2015", "text": "<PERSON>, Canadian director, producer, and screenwriter (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian-Pakistani poet and politician (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aheem\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani poet and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-Pakistani poet and politician (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>m"}]}, {"year": "2015", "text": "<PERSON>, American boxer and police officer (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and police officer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and police officer (b. 1938)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "2015", "text": "<PERSON>, English screenwriter and producer (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Read\"><PERSON></a>, English screenwriter and producer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American violinist and conductor (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and conductor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Pakistani mountaineer and adventurer (b. 1963)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani mountaineer and adventurer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani mountaineer and adventurer (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, French Trappist monk and survivor of the Thibirine monks (b. 1924)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Trappist_monk\" class=\"mw-redirect\" title=\"Trappist monk\">Trappist monk</a> and survivor of the <a href=\"https://wikipedia.org/wiki/Murder_of_the_monks_of_Tibhirine\" title=\"Murder of the monks of Tibhirine\">Thibirine monks</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Trappist_monk\" class=\"mw-redirect\" title=\"Trappist monk\">Trappist monk</a> and survivor of the <a href=\"https://wikipedia.org/wiki/Murder_of_the_monks_of_Tibhirine\" title=\"Murder of the monks of Tibhirine\">Thibirine monks</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Trappist monk", "link": "https://wikipedia.org/wiki/Trappist_monk"}, {"title": "Murder of the monks of Tibhirine", "link": "https://wikipedia.org/wiki/Murder_of_the_monks_of_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American artist, author and restaurateur (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, author and restaurateur (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, author and restaurateur (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}