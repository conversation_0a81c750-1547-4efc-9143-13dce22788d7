{"date": "July 4", "url": "https://wikipedia.org/wiki/July_4", "data": {"Events": [{"year": "362 BC", "text": "Battle of Mantinea: The Thebans, led by <PERSON><PERSON><PERSON><PERSON><PERSON>, defeated the Spartans.", "html": "362 BC - 362 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Mantinea_(362_BC)\" title=\"Battle of Mantinea (362 BC)\">Battle of Mantinea</a>: The <a href=\"https://wikipedia.org/wiki/Thebes,_Greece\" title=\"Thebes, Greece\">Thebans</a>, led by <a href=\"https://wikipedia.org/wiki/Epaminondas\" title=\"Epaminondas\">Epaminondas</a>, defeated the <a href=\"https://wikipedia.org/wiki/Sparta\" title=\"Sparta\">Spartans</a>.", "no_year_html": "362 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Mantinea_(362_BC)\" title=\"Battle of Mantinea (362 BC)\">Battle of Mantinea</a>: The <a href=\"https://wikipedia.org/wiki/Thebes,_Greece\" title=\"Thebes, Greece\">Thebans</a>, led by <a href=\"https://wikipedia.org/wiki/Epaminondas\" title=\"Epaminondas\">Epaminondas</a>, defeated the <a href=\"https://wikipedia.org/wiki/Sparta\" title=\"Sparta\">Spartans</a>.", "links": [{"title": "Battle of Mantinea (362 BC)", "link": "https://wikipedia.org/wiki/Battle_of_Mantinea_(362_BC)"}, {"title": "Thebes, Greece", "link": "https://wikipedia.org/wiki/Thebes,_Greece"}, {"title": "Epaminondas", "link": "https://wikipedia.org/wiki/Epaminondas"}, {"title": "Sparta", "link": "https://wikipedia.org/wiki/Sparta"}]}, {"year": "414", "text": "Emperor <PERSON><PERSON><PERSON> II, age 13, yields power to his older sister <PERSON><PERSON><PERSON>, who reigned as regent and proclaimed herself empress (Augusta) of the Eastern Roman Empire.", "html": "414 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON>ius II\"><PERSON><PERSON><PERSON> II</a>, age 13, yields power to his older sister <a href=\"https://wikipedia.org/wiki/Pulcheria\" title=\"Pulcheria\"><PERSON><PERSON><PERSON></a>, who reigned as <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> and proclaimed herself empress (<i><a href=\"https://wikipedia.org/wiki/List_of_Augustae\" title=\"List of Augustae\">Augusta</a></i>) of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, age 13, yields power to his older sister <a href=\"https://wikipedia.org/wiki/Pulcheria\" title=\"Pulcheria\"><PERSON><PERSON><PERSON></a>, who reigned as <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> and proclaimed herself empress (<i><a href=\"https://wikipedia.org/wiki/List_of_Augustae\" title=\"List of Augustae\">Augusta</a></i>) of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}, {"title": "P<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pulcheria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "List of Augustae", "link": "https://wikipedia.org/wiki/List_of_Augustae"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "836", "text": "Pactum Sicardi, a peace treaty between the Principality of Benevento and the Duchy of Naples, is signed.", "html": "836 - <i><a href=\"https://wikipedia.org/wiki/Pactum_Sicardi\" title=\"Pactum Sicardi\">Pactum Sicardi</a></i>, a peace treaty between the <a href=\"https://wikipedia.org/wiki/Duchy_of_Benevento\" title=\"Duchy of Benevento\">Principality of Benevento</a> and the <a href=\"https://wikipedia.org/wiki/Duchy_of_Naples\" title=\"Duchy of Naples\">Duchy of Naples</a>, is signed.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Pactum_Sicardi\" title=\"Pactum Sicardi\">Pactum Sicardi</a></i>, a peace treaty between the <a href=\"https://wikipedia.org/wiki/Duchy_of_Benevento\" title=\"Duchy of Benevento\">Principality of Benevento</a> and the <a href=\"https://wikipedia.org/wiki/Duchy_of_Naples\" title=\"Duchy of Naples\">Duchy of Naples</a>, is signed.", "links": [{"title": "Pactum Sicardi", "link": "https://wikipedia.org/wiki/Pactum_Sicardi"}, {"title": "Duchy of Benevento", "link": "https://wikipedia.org/wiki/Duchy_of_Benevento"}, {"title": "Duchy of Naples", "link": "https://wikipedia.org/wiki/Duchy_of_Naples"}]}, {"year": "993", "text": "<PERSON> of Augsburg is canonized as a saint.", "html": "993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Augsburg\" title=\"<PERSON> of Augsburg\"><PERSON> of Augsburg</a> is <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Augsburg\" title=\"<PERSON> of Augsburg\"><PERSON> of Augsburg</a> is <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint.", "links": [{"title": "<PERSON> of Augsburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Augsburg"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}]}, {"year": "1054", "text": "A supernova, called SN 1054, is seen by Chinese Song dynasty, Arab, and possibly Amerindian observers near the star Zeta Tauri. For several months it remains bright enough to be seen during the day. Its remnants form the Crab Nebula.", "html": "1054 - A supernova, called <a href=\"https://wikipedia.org/wiki/SN_1054\" title=\"SN 1054\">SN 1054</a>, is seen by Chinese <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>, <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arab</a>, and possibly <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">Amerindian</a> observers near the star <a href=\"https://wikipedia.org/wiki/Zeta_Tauri\" title=\"Zeta Tauri\">Zeta Tauri</a>. For several months it remains bright enough to be seen during the day. Its remnants form the <a href=\"https://wikipedia.org/wiki/Crab_Nebula\" title=\"Crab Nebula\">Crab Nebula</a>.", "no_year_html": "A supernova, called <a href=\"https://wikipedia.org/wiki/SN_1054\" title=\"SN 1054\">SN 1054</a>, is seen by Chinese <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>, <a href=\"https://wikipedia.org/wiki/Arab\" class=\"mw-redirect\" title=\"Arab\">Arab</a>, and possibly <a href=\"https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas\" title=\"Indigenous peoples of the Americas\">Amerindian</a> observers near the star <a href=\"https://wikipedia.org/wiki/Zeta_Tauri\" title=\"Zeta Tauri\">Zeta Tauri</a>. For several months it remains bright enough to be seen during the day. Its remnants form the <a href=\"https://wikipedia.org/wiki/Crab_Nebula\" title=\"Crab Nebula\">Crab Nebula</a>.", "links": [{"title": "SN 1054", "link": "https://wikipedia.org/wiki/SN_1054"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}, {"title": "Arab", "link": "https://wikipedia.org/wiki/Arab"}, {"title": "Indigenous peoples of the Americas", "link": "https://wikipedia.org/wiki/Indigenous_peoples_of_the_Americas"}, {"title": "Zeta Tauri", "link": "https://wikipedia.org/wiki/Zeta_Tauri"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Crab_Nebula"}]}, {"year": "1120", "text": "<PERSON> of Capua is anointed as prince after his infant nephew's death.", "html": "1120 - <a href=\"https://wikipedia.org/wiki/Jordan_II_of_Capua\" title=\"<PERSON> II of Capua\"><PERSON> of Capua</a> is anointed as prince after his infant nephew's death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_II_of_Capua\" title=\"Jordan II of Capua\"><PERSON> of Capua</a> is anointed as prince after his infant nephew's death.", "links": [{"title": "Jordan II of Capua", "link": "https://wikipedia.org/wiki/Jordan_II_of_Capua"}]}, {"year": "1187", "text": "The Crusades: Battle of Hattin: <PERSON><PERSON><PERSON> defeats <PERSON> of Lusignan, King of Jerusalem.", "html": "1187 - The <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusades</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hattin\" title=\"Battle of Hattin\">Battle of Hattin</a>: <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladin\"><PERSON><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>, <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Crusades\" title=\"Crusades\">Crusades</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hattin\" title=\"Battle of Hattin\">Battle of Hattin</a>: <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"Saladi<PERSON>\"><PERSON><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>, <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a>.", "links": [{"title": "Crusades", "link": "https://wikipedia.org/wiki/Crusades"}, {"title": "Battle of Hattin", "link": "https://wikipedia.org/wiki/Battle_of_Hattin"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "<PERSON> of Lusignan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "King of Jerusalem", "link": "https://wikipedia.org/wiki/King_of_Jerusalem"}]}, {"year": "1253", "text": "Battle of West-Capelle: <PERSON> of Avesnes defeats <PERSON> of Dampierre.", "html": "1253 - Battle of West-Capelle: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON> of Avesnes</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON> of Dampierre</a>.", "no_year_html": "Battle of West-Capelle: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON> of Avesnes</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON> of <PERSON>rre</a>.", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hai<PERSON><PERSON>"}, {"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Flanders"}]}, {"year": "1333", "text": "Genkō War: Forces loyal to Emperor <PERSON><PERSON><PERSON><PERSON> seize Tōshō-ji during the Siege of Kamakura. <PERSON><PERSON><PERSON> and other members of the <PERSON><PERSON><PERSON> clan commit suicide, ending the rule of the Kamakura shogunate.", "html": "1333 - <a href=\"https://wikipedia.org/wiki/Genk%C5%8D_War\" title=\"Genkō War\">Genkō War</a>: Forces loyal to <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor <PERSON>-Daigo\">Emperor <PERSON><PERSON>Dai<PERSON></a> seize <a href=\"https://wikipedia.org/wiki/T%C5%8Dsh%C5%8D-ji\" title=\"Tōshō-ji\">Tōshō-ji</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_Kamakura_(1333)\" title=\"Siege of Kamakura (1333)\">Siege of Kamakura</a>. <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Takatoki\" title=\"Hōjō Takatoki\">Hō<PERSON> Takatoki</a> and other members of the <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_clan\" title=\"Hōjō clan\">Hōjō clan</a> commit suicide, ending the <a href=\"https://wikipedia.org/wiki/Kamakura_period\" title=\"Kamakura period\">rule</a> of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genk%C5%8D_War\" title=\"Genkō War\">Genkō War</a>: Forces loyal to <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor Go-Daigo\">Emperor <PERSON><PERSON>Dai<PERSON></a> seize <a href=\"https://wikipedia.org/wiki/T%C5%8Dsh%C5%8D-ji\" title=\"Tōshō-ji\">Tōshō-ji</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_Kamakura_(1333)\" title=\"Siege of Kamakura (1333)\">Siege of Kamakura</a>. <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Takatoki\" title=\"Hōjō Takatoki\">Hō<PERSON> Takatoki</a> and other members of the <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_clan\" title=\"Hōjō clan\">Hōjō clan</a> commit suicide, ending the <a href=\"https://wikipedia.org/wiki/Kamakura_period\" title=\"Kamakura period\">rule</a> of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a>.", "links": [{"title": "Genkō War", "link": "https://wikipedia.org/wiki/Genk%C5%8D_War"}, {"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}, {"title": "Tōshō-ji", "link": "https://wikipedia.org/wiki/T%C5%8Dsh%C5%8D-ji"}, {"title": "Siege of Kamakura (1333)", "link": "https://wikipedia.org/wiki/Siege_of_Kamakura_(1333)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_Ta<PERSON>oki"}, {"title": "<PERSON><PERSON><PERSON> clan", "link": "https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_clan"}, {"title": "Kamakura period", "link": "https://wikipedia.org/wiki/Kamakura_period"}, {"title": "Kamakura shogunate", "link": "https://wikipedia.org/wiki/Kamakura_shogunate"}]}, {"year": "1359", "text": "<PERSON> of Forlì surrenders to the Papal commander <PERSON>.", "html": "1359 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_Ordelaffi\" title=\"<PERSON> II Ordelaffi\">Francesco II Ordelaffi</a> of <a href=\"https://wikipedia.org/wiki/Forl%C3%AC\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> surrenders to the Papal commander <a href=\"https://wikipedia.org/wiki/Gil_%C3%81l<PERSON><PERSON>_<PERSON>_de_Albornoz\" title=\"<PERSON> Albornoz\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_Ordelaffi\" title=\"Francesco II Ordelaffi\">Francesco II Ordelaffi</a> of <a href=\"https://wikipedia.org/wiki/Forl%C3%AC\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> surrenders to the Papal commander <a href=\"https://wikipedia.org/wiki/Gil_%C3%81l<PERSON><PERSON>_<PERSON>_de_Albornoz\" title=\"<PERSON> Albornoz\"><PERSON></a>.", "links": [{"title": "Francesco II Ordelaffi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i"}, {"title": "Forlì", "link": "https://wikipedia.org/wiki/Forl%C3%AC"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gil_%C3%81<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1456", "text": "Ottoman-Hungarian wars: The Siege of Nándorfehérvár (Belgrade) begins.", "html": "1456 - <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Hungarian_wars\" class=\"mw-redirect\" title=\"Ottoman-Hungarian wars\">Ottoman-Hungarian wars</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Belgrade_(1456)\" title=\"Siege of Belgrade (1456)\">Siege of Nándorfehérvár (Belgrade)</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Hungarian_wars\" class=\"mw-redirect\" title=\"Ottoman-Hungarian wars\">Ottoman-Hungarian wars</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Belgrade_(1456)\" title=\"Siege of Belgrade (1456)\">Siege of Nándorfehérvár (Belgrade)</a> begins.", "links": [{"title": "Ottoman-Hungarian wars", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Hungarian_wars"}, {"title": "Siege of Belgrade (1456)", "link": "https://wikipedia.org/wiki/Siege_of_Belgrade_(1456)"}]}, {"year": "1534", "text": "<PERSON> is elected King of Denmark and Norway in the town of Rye.", "html": "1534 - <a href=\"https://wikipedia.org/wiki/Election_of_Christian_III\" title=\"Election of Christian III\"><PERSON> is elected King of Denmark and Norway</a> in the town of <a href=\"https://wikipedia.org/wiki/Old_Rye\" title=\"Old Rye\"><PERSON>ye</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Election_of_Christian_III\" title=\"Election of Christian III\"><PERSON> is elected King of Denmark and Norway</a> in the town of <a href=\"https://wikipedia.org/wiki/Old_Rye\" title=\"Old Rye\"><PERSON><PERSON></a>.", "links": [{"title": "Election of Christian III", "link": "https://wikipedia.org/wiki/Election_of_<PERSON>_III"}, {"title": "Old Rye", "link": "https://wikipedia.org/wiki/<PERSON>_Rye"}]}, {"year": "1584", "text": "<PERSON> and <PERSON> arrive at Roanoke Island.", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrive at <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrive at <a href=\"https://wikipedia.org/wiki/Roanoke_Island\" title=\"Roanoke Island\">Roanoke Island</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roanoke Island", "link": "https://wikipedia.org/wiki/Roanoke_Island"}]}, {"year": "1610", "text": "The Battle of Klushino is fought between forces of the Polish-Lithuanian Commonwealth and Russia during the Polish-Russian War.", "html": "1610 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Klushino\" title=\"Battle of Klushino\">Battle of Klushino</a> is fought between forces of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> and <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> during the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Russian_War_(1609%E2%80%931618)\" title=\"Polish-Russian War (1609-1618)\">Polish-Russian War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Klushino\" title=\"Battle of Klushino\">Battle of Klushino</a> is fought between forces of the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth\" title=\"Polish-Lithuanian Commonwealth\">Polish-Lithuanian Commonwealth</a> and <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> during the <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Russian_War_(1609%E2%80%931618)\" title=\"Polish-Russian War (1609-1618)\">Polish-Russian War</a>.", "links": [{"title": "Battle of Klushino", "link": "https://wikipedia.org/wiki/Battle_of_Klushino"}, {"title": "Polish-Lithuanian Commonwealth", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian_Commonwealth"}, {"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}, {"title": "Polish-Russian War (1609-1618)", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Russian_War_(1609%E2%80%931618)"}]}, {"year": "1634", "text": "The city of Trois-Rivières is founded in New France (now Quebec, Canada).", "html": "1634 - The city of <a href=\"https://wikipedia.org/wiki/Trois-Rivi%C3%A8res\" title=\"Trois-Rivières\">Trois-Rivières</a> is founded in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> (now Quebec, Canada).", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Trois-Rivi%C3%A8res\" title=\"Trois-Rivières\">Trois-Rivières</a> is founded in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a> (now Quebec, Canada).", "links": [{"title": "Trois-Rivières", "link": "https://wikipedia.org/wiki/Trois-Rivi%C3%A8res"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}]}, {"year": "1744", "text": "The Treaty of Lancaster, in which the Iroquois cede lands between the Allegheny Mountains and the Ohio River to the British colonies, was signed in Lancaster, Pennsylvania.", "html": "1744 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lancaster\" class=\"mw-redirect\" title=\"Treaty of Lancaster\">Treaty of Lancaster</a>, in which the <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> cede lands between the <a href=\"https://wikipedia.org/wiki/Allegheny_Mountains\" title=\"Allegheny Mountains\">Allegheny Mountains</a> and the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> to the British colonies, was signed in <a href=\"https://wikipedia.org/wiki/Lancaster,_Pennsylvania\" title=\"Lancaster, Pennsylvania\">Lancaster, Pennsylvania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lancaster\" class=\"mw-redirect\" title=\"Treaty of Lancaster\">Treaty of Lancaster</a>, in which the <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> cede lands between the <a href=\"https://wikipedia.org/wiki/Allegheny_Mountains\" title=\"Allegheny Mountains\">Allegheny Mountains</a> and the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> to the British colonies, was signed in <a href=\"https://wikipedia.org/wiki/Lancaster,_Pennsylvania\" title=\"Lancaster, Pennsylvania\">Lancaster, Pennsylvania</a>.", "links": [{"title": "Treaty of Lancaster", "link": "https://wikipedia.org/wiki/Treaty_of_Lancaster"}, {"title": "Iroquois", "link": "https://wikipedia.org/wiki/Iroquois"}, {"title": "Allegheny Mountains", "link": "https://wikipedia.org/wiki/Allegheny_Mountains"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}, {"title": "Lancaster, Pennsylvania", "link": "https://wikipedia.org/wiki/Lancaster,_Pennsylvania"}]}, {"year": "1774", "text": "Orangetown Resolutions are adopted in the Province of New York, one of many protests against the British Parliament's Coercive Acts.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Orangetown_Resolutions\" title=\"Orangetown Resolutions\">Orangetown Resolutions</a> are adopted in the <a href=\"https://wikipedia.org/wiki/Province_of_New_York\" title=\"Province of New York\">Province of New York</a>, one of many protests against the <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a>'s <a href=\"https://wikipedia.org/wiki/Coercive_Acts\" class=\"mw-redirect\" title=\"Coercive Acts\">Coercive Acts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orangetown_Resolutions\" title=\"Orangetown Resolutions\">Orangetown Resolutions</a> are adopted in the <a href=\"https://wikipedia.org/wiki/Province_of_New_York\" title=\"Province of New York\">Province of New York</a>, one of many protests against the <a href=\"https://wikipedia.org/wiki/Parliament_of_Great_Britain\" title=\"Parliament of Great Britain\">British Parliament</a>'s <a href=\"https://wikipedia.org/wiki/Coercive_Acts\" class=\"mw-redirect\" title=\"Coercive Acts\">Coercive Acts</a>.", "links": [{"title": "Orangetown Resolutions", "link": "https://wikipedia.org/wiki/Orangetown_Resolutions"}, {"title": "Province of New York", "link": "https://wikipedia.org/wiki/Province_of_New_York"}, {"title": "Parliament of Great Britain", "link": "https://wikipedia.org/wiki/Parliament_of_Great_Britain"}, {"title": "Coercive Acts", "link": "https://wikipedia.org/wiki/Coercive_Acts"}]}, {"year": "1776", "text": "American Revolution: The United States Declaration of Independence is adopted by the Second Continental Congress.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">United States Declaration of Independence</a> is adopted by the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: The <a href=\"https://wikipedia.org/wiki/United_States_Declaration_of_Independence\" title=\"United States Declaration of Independence\">United States Declaration of Independence</a> is adopted by the <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "United States Declaration of Independence", "link": "https://wikipedia.org/wiki/United_States_Declaration_of_Independence"}, {"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}]}, {"year": "1778", "text": "American Revolutionary War: U.S. forces under <PERSON> capture Kaskaskia during the Illinois campaign.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: U.S. forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Kaskaskia,_Illinois\" title=\"Kaskaskia, Illinois\">Kaskaskia</a> during the <a href=\"https://wikipedia.org/wiki/Illinois_campaign\" title=\"Illinois campaign\">Illinois campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: U.S. forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture <a href=\"https://wikipedia.org/wiki/Kaskaskia,_Illinois\" title=\"Kaskaskia, Illinois\">Kaskaskia</a> during the <a href=\"https://wikipedia.org/wiki/Illinois_campaign\" title=\"Illinois campaign\">Illinois campaign</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kaskaskia, Illinois", "link": "https://wikipedia.org/wiki/Kaskaskia,_Illinois"}, {"title": "Illinois campaign", "link": "https://wikipedia.org/wiki/Illinois_campaign"}]}, {"year": "1802", "text": "The United States Military Academy opens at West Point, New York.", "html": "1802 - The <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a> opens at <a href=\"https://wikipedia.org/wiki/West_Point,_New_York\" title=\"West Point, New York\">West Point, New York</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a> opens at <a href=\"https://wikipedia.org/wiki/West_Point,_New_York\" title=\"West Point, New York\">West Point, New York</a>.", "links": [{"title": "United States Military Academy", "link": "https://wikipedia.org/wiki/United_States_Military_Academy"}, {"title": "West Point, New York", "link": "https://wikipedia.org/wiki/West_Point,_New_York"}]}, {"year": "1803", "text": "The Louisiana Purchase is announced to the American people.", "html": "1803 - The <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> is announced to the American people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> is announced to the American people.", "links": [{"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}]}, {"year": "1817", "text": "In Rome, New York, construction on the Erie Canal begins.", "html": "1817 - In <a href=\"https://wikipedia.org/wiki/Rome,_New_York\" title=\"Rome, New York\">Rome, New York</a>, construction on the <a href=\"https://wikipedia.org/wiki/Erie_Canal\" title=\"Erie Canal\">Erie Canal</a> begins.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Rome,_New_York\" title=\"Rome, New York\">Rome, New York</a>, construction on the <a href=\"https://wikipedia.org/wiki/Erie_Canal\" title=\"Erie Canal\">Erie Canal</a> begins.", "links": [{"title": "Rome, New York", "link": "https://wikipedia.org/wiki/Rome,_New_York"}, {"title": "Erie Canal", "link": "https://wikipedia.org/wiki/Erie_Canal"}]}, {"year": "1818", "text": "US Flag Act of 1818 goes into effect creating a 13 stripe flag with a star for each state. New stars would be added on July 4 after a new state had been admitted.", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Flag_Acts\" title=\"Flag Acts\">US Flag Act of 1818</a> goes into effect creating a 13 stripe flag with a star for each state. New stars would be added on July 4 after a new state had been admitted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flag_Acts\" title=\"Flag Acts\">US Flag Act of 1818</a> goes into effect creating a 13 stripe flag with a star for each state. New stars would be added on July 4 after a new state had been admitted.", "links": [{"title": "Flag Acts", "link": "https://wikipedia.org/wiki/Flag_Acts"}]}, {"year": "1827", "text": "Slavery is abolished in the State of New York.", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">Slavery</a> is abolished in the <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">State of New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">Slavery</a> is abolished in the <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">State of New York</a>.", "links": [{"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}, {"title": "New York (state)", "link": "https://wikipedia.org/wiki/New_York_(state)"}]}, {"year": "1831", "text": "<PERSON> writes \"My Country, 'Tis of Thee\" for the Boston, Massachusetts July 4 festivities.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes \"<a href=\"https://wikipedia.org/wiki/America_(My_Country,_%27Tis_of_Thee)\" class=\"mw-redirect\" title=\"America (My Country, 'Tis of Thee)\">My Country, 'Tis of Thee</a>\" for the Boston, Massachusetts July 4 festivities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes \"<a href=\"https://wikipedia.org/wiki/America_(My_Country,_%27Tis_of_Thee)\" class=\"mw-redirect\" title=\"America (My Country, 'Tis of Thee)\">My Country, 'Tis of Thee</a>\" for the Boston, Massachusetts July 4 festivities.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "America (My Country, '<PERSON><PERSON> of Thee)", "link": "https://wikipedia.org/wiki/America_(My_Country,_%27Tis_of_Thee)"}]}, {"year": "1832", "text": "<PERSON> delivers the first public lecture in the US to advocate the rights of women.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first public lecture in the US to advocate the rights of women.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the first public lecture in the US to advocate the rights of women.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "Durham University established by Act of Parliament; the first recognized university to be founded in England since Cambridge over 600 years earlier.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Durham_University\" title=\"Durham University\">Durham University</a> established by <a href=\"https://wikipedia.org/wiki/Act_of_Parliament_(UK)\" class=\"mw-redirect\" title=\"Act of Parliament (UK)\">Act of Parliament</a>; the first recognized university to be founded in <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> since <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a> over 600 years earlier.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Durham_University\" title=\"Durham University\">Durham University</a> established by <a href=\"https://wikipedia.org/wiki/Act_of_Parliament_(UK)\" class=\"mw-redirect\" title=\"Act of Parliament (UK)\">Act of Parliament</a>; the first recognized university to be founded in <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> since <a href=\"https://wikipedia.org/wiki/University_of_Cambridge\" title=\"University of Cambridge\">Cambridge</a> over 600 years earlier.", "links": [{"title": "Durham University", "link": "https://wikipedia.org/wiki/Durham_University"}, {"title": "Act of Parliament (UK)", "link": "https://wikipedia.org/wiki/Act_of_Parliament_(UK)"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "University of Cambridge", "link": "https://wikipedia.org/wiki/University_of_Cambridge"}]}, {"year": "1837", "text": "Grand Junction Railway, the world's first long-distance railway, opens between Birmingham and Liverpool.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Grand_Junction_Railway\" title=\"Grand Junction Railway\">Grand Junction Railway</a>, the world's first long-distance railway, opens between <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a> and <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Junction_Railway\" title=\"Grand Junction Railway\">Grand Junction Railway</a>, the world's first long-distance railway, opens between <a href=\"https://wikipedia.org/wiki/Birmingham\" title=\"Birmingham\">Birmingham</a> and <a href=\"https://wikipedia.org/wiki/Liverpool\" title=\"Liverpool\">Liverpool</a>.", "links": [{"title": "Grand Junction Railway", "link": "https://wikipedia.org/wiki/Grand_Junction_Railway"}, {"title": "Birmingham", "link": "https://wikipedia.org/wiki/Birmingham"}, {"title": "Liverpool", "link": "https://wikipedia.org/wiki/Liverpool"}]}, {"year": "1838", "text": "The Iowa Territory is organized.", "html": "1838 - The <a href=\"https://wikipedia.org/wiki/Iowa_Territory\" title=\"Iowa Territory\">Iowa Territory</a> is organized.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Iowa_Territory\" title=\"Iowa Territory\">Iowa Territory</a> is organized.", "links": [{"title": "Iowa Territory", "link": "https://wikipedia.org/wiki/Iowa_Territory"}]}, {"year": "1845", "text": "<PERSON> moves into a small cabin on Walden Pond in Concord, Massachusetts. <PERSON><PERSON>'s account of his two years there, <PERSON>, will become a touchstone of the environmental movement.", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves into a small cabin on <a href=\"https://wikipedia.org/wiki/Walden_Pond\" title=\"Walden Pond\">Walden Pond</a> in Concord, Massachusetts. <PERSON><PERSON>'s account of his two years there, <i><a href=\"https://wikipedia.org/wiki/Walden\" title=\"Walden\">Walden</a></i>, will become a touchstone of the environmental movement.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> moves into a small cabin on <a href=\"https://wikipedia.org/wiki/Walden_Pond\" title=\"Walden Pond\">Walden Pond</a> in Concord, Massachusetts. <PERSON><PERSON>'s account of his two years there, <i><a href=\"https://wikipedia.org/wiki/Walden\" title=\"Walden\">Walden</a></i>, will become a touchstone of the environmental movement.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Walden Pond", "link": "https://wikipedia.org/wiki/Walden_Pond"}, {"title": "Walden", "link": "https://wikipedia.org/wiki/Walden"}]}, {"year": "1855", "text": "The first edition of <PERSON>'s book of poems, <PERSON> of Grass, is published in Brooklyn.", "html": "1855 - The first edition of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>'s book of poems, <i><a href=\"https://wikipedia.org/wiki/Leaves_of_Grass\" title=\"Leaves of Grass\">Leaves of Grass</a></i>, is published in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>.", "no_year_html": "The first edition of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s book of poems, <i><a href=\"https://wikipedia.org/wiki/Leaves_of_Grass\" title=\"Leaves of Grass\">Leaves of Grass</a></i>, is published in <a href=\"https://wikipedia.org/wiki/Brooklyn\" title=\"Brooklyn\">Brooklyn</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leaves of Grass", "link": "https://wikipedia.org/wiki/Leaves_of_Grass"}, {"title": "Brooklyn", "link": "https://wikipedia.org/wiki/Brooklyn"}]}, {"year": "1862", "text": "<PERSON> tells <PERSON> a story that would grow into <PERSON>'s Adventures in Wonderland and its sequels.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells <a href=\"https://wikipedia.org/wiki/Alice_Liddell\" title=\"<PERSON> Liddell\"><PERSON></a> a story that would grow into <i><a href=\"https://wikipedia.org/wiki/Alice%27s_Adventures_in_Wonderland\" title=\"Alice's Adventures in Wonderland\">Alice's Adventures in Wonderland</a></i> and its sequels.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells <a href=\"https://wikipedia.org/wiki/Alice_Liddell\" title=\"<PERSON>ell\"><PERSON></a> a story that would grow into <i><a href=\"https://wikipedia.org/wiki/Alice%27s_Adventures_in_Wonderland\" title=\"Alice's Adventures in Wonderland\">Alice's Adventures in Wonderland</a></i> and its sequels.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Adventures in Wonderland", "link": "https://wikipedia.org/wiki/Alice%27s_Adventures_in_Wonderland"}]}, {"year": "1863", "text": "American Civil War: Siege of Vicksburg: The Confederate army in Vicksburg, Mississippi surrenders to Union forces under <PERSON> after 47 days of siege, contributing to the Union capture of the Mississippi River.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Vicksburg\" title=\"Siege of Vicksburg\">Siege of Vicksburg</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army in <a href=\"https://wikipedia.org/wiki/Vicksburg,_Mississippi\" title=\"Vicksburg, Mississippi\">Vicksburg, Mississippi</a> surrenders to <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces under <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\">Ulysses S<PERSON> Grant</a> after 47 days of siege, contributing to the Union capture of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Vicksburg\" title=\"Siege of Vicksburg\">Siege of Vicksburg</a>: The <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army in <a href=\"https://wikipedia.org/wiki/Vicksburg,_Mississippi\" title=\"Vicksburg, Mississippi\">Vicksburg, Mississippi</a> surrenders to <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces under <a href=\"https://wikipedia.org/wiki/Ulysses_S._Grant\" title=\"Ulysses S. Grant\">Ulysses S<PERSON> Grant</a> after 47 days of siege, contributing to the Union capture of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Siege of Vicksburg", "link": "https://wikipedia.org/wiki/Siege_of_Vicksburg"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Vicksburg, Mississippi", "link": "https://wikipedia.org/wiki/Vicksburg,_Mississippi"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1863", "text": "American Civil War: Union forces repulse a Confederate army at the Battle of Helena in Arkansas. The battle thwarts a Rebel attempt to relieve pressure on the besieged city of Vicksburg, and paves the way for the Union capture of Little Rock.", "html": "1863 - American Civil War: Union forces repulse a Confederate army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Helena\" title=\"Battle of Helena\">Battle of Helena</a> in Arkansas. The battle thwarts a Rebel attempt to relieve pressure on the besieged city of Vicksburg, and paves the way for the Union capture of <a href=\"https://wikipedia.org/wiki/Little_Rock,_Arkansas\" title=\"Little Rock, Arkansas\">Little Rock</a>.", "no_year_html": "American Civil War: Union forces repulse a Confederate army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Helena\" title=\"Battle of Helena\">Battle of Helena</a> in Arkansas. The battle thwarts a Rebel attempt to relieve pressure on the besieged city of Vicksburg, and paves the way for the Union capture of <a href=\"https://wikipedia.org/wiki/Little_Rock,_Arkansas\" title=\"Little Rock, Arkansas\">Little Rock</a>.", "links": [{"title": "Battle of Helena", "link": "https://wikipedia.org/wiki/Battle_of_Helena"}, {"title": "Little Rock, Arkansas", "link": "https://wikipedia.org/wiki/Little_Rock,_Arkansas"}]}, {"year": "1863", "text": "American Civil War: Retreat from Gettysburg: The Confederate Army of Northern Virginia under <PERSON> withdraws from the battlefield after losing the Battle of Gettysburg, signaling an end to his last invasion of the North.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Retreat_from_Gettysburg\" title=\"Retreat from Gettysburg\">Retreat from Gettysburg</a>: The Confederate <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg#Confederate_retreat\" title=\"Battle of Gettysburg\">withdraws from the battlefield</a> after losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a>, signaling an end to his last <a href=\"https://wikipedia.org/wiki/Gettysburg_campaign\" title=\"Gettysburg campaign\">invasion</a> of the <a href=\"https://wikipedia.org/wiki/Northern_United_States#American_Civil_War\" title=\"Northern United States\">North</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Retreat_from_Gettysburg\" title=\"Retreat from Gettysburg\">Retreat from Gettysburg</a>: The Confederate <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg#Confederate_retreat\" title=\"Battle of Gettysburg\">withdraws from the battlefield</a> after losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Gettysburg\" title=\"Battle of Gettysburg\">Battle of Gettysburg</a>, signaling an end to his last <a href=\"https://wikipedia.org/wiki/Gettysburg_campaign\" title=\"Gettysburg campaign\">invasion</a> of the <a href=\"https://wikipedia.org/wiki/Northern_United_States#American_Civil_War\" title=\"Northern United States\">North</a>.", "links": [{"title": "Retreat from Gettysburg", "link": "https://wikipedia.org/wiki/Retreat_from_Gettysburg"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg#Confederate_retreat"}, {"title": "Battle of Gettysburg", "link": "https://wikipedia.org/wiki/Battle_of_Gettysburg"}, {"title": "Gettysburg campaign", "link": "https://wikipedia.org/wiki/Gettysburg_campaign"}, {"title": "Northern United States", "link": "https://wikipedia.org/wiki/Northern_United_States#American_Civil_War"}]}, {"year": "1879", "text": "Anglo-Zulu War: The Zululand capital of Ulundi is captured by British troops and burned to the ground, ending the war and forcing King <PERSON><PERSON><PERSON><PERSON> to flee.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: The Zululand capital of <a href=\"https://wikipedia.org/wiki/Ulundi\" title=\"Ulundi\">Ulundi</a> is captured by British troops and burned to the ground, ending the war and forcing King <a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\">Cetshwayo</a> to flee.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: The Zululand capital of <a href=\"https://wikipedia.org/wiki/Ulundi\" title=\"Ulundi\">Ulundi</a> is captured by British troops and burned to the ground, ending the war and forcing King <a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\">Cetshwayo</a> to flee.", "links": [{"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulundi"}, {"title": "Cetshwayo", "link": "https://wikipedia.org/wiki/Cetshwayo"}]}, {"year": "1881", "text": "In Alabama, the Tuskegee Institute opens.", "html": "1881 - In <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a>, the <a href=\"https://wikipedia.org/wiki/Tuskegee_Institute\" class=\"mw-redirect\" title=\"Tuskegee Institute\">Tuskegee Institute</a> opens.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a>, the <a href=\"https://wikipedia.org/wiki/Tuskegee_Institute\" class=\"mw-redirect\" title=\"Tuskegee Institute\">Tuskegee Institute</a> opens.", "links": [{"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "Tuskegee Institute", "link": "https://wikipedia.org/wiki/Tuskegee_Institute"}]}, {"year": "1886", "text": "The Canadian Pacific Railway's first scheduled train from Montreal arrives in Port Moody on the Pacific coast, after six days of travel.", "html": "1886 - The <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Railway\" title=\"Canadian Pacific Railway\">Canadian Pacific Railway</a>'s first scheduled train from Montreal arrives in <a href=\"https://wikipedia.org/wiki/Port_Moody\" title=\"Port Moody\">Port Moody</a> on the Pacific coast, after six days of travel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Railway\" title=\"Canadian Pacific Railway\">Canadian Pacific Railway</a>'s first scheduled train from Montreal arrives in <a href=\"https://wikipedia.org/wiki/Port_Moody\" title=\"Port Moody\">Port Moody</a> on the Pacific coast, after six days of travel.", "links": [{"title": "Canadian Pacific Railway", "link": "https://wikipedia.org/wiki/Canadian_Pacific_Railway"}, {"title": "Port Moody", "link": "https://wikipedia.org/wiki/Port_Moody"}]}, {"year": "1887", "text": "The founder of Pakistan, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joins Sindh-Madrasa-tul-Islam, Karachi.", "html": "1887 - The founder of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Quaid-<PERSON><PERSON><PERSON><PERSON></a>, joins <a href=\"https://wikipedia.org/wiki/Sindh-Madrasa-tul-Islam\" class=\"mw-redirect\" title=\"Sindh-Madrasa-tul-Islam\">Sindh-Madrasa-tul-Islam</a>, <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "no_year_html": "The founder of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Quaid-<PERSON><PERSON><PERSON><PERSON></a>, joins <a href=\"https://wikipedia.org/wiki/Sindh-Madrasa-tul-Islam\" class=\"mw-redirect\" title=\"Sindh-Madrasa-tul-Islam\">Sindh-Madrasa-tul-Islam</a>, <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sindh-Madrasa-tul-Islam", "link": "https://wikipedia.org/wiki/Sindh-Madrasa-tul-Islam"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}]}, {"year": "1892", "text": "Western Samoa changes the International Date Line, causing Monday (July 4) to occur twice, resulting in a leap year with 367 days.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Western_Samoa\" class=\"mw-redirect\" title=\"Western Samoa\">Western Samoa</a> changes the <a href=\"https://wikipedia.org/wiki/International_Date_Line\" title=\"International Date Line\">International Date Line</a>, causing Monday (July 4) to occur twice, resulting in a leap year with 367 days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Western_Samoa\" class=\"mw-redirect\" title=\"Western Samoa\">Western Samoa</a> changes the <a href=\"https://wikipedia.org/wiki/International_Date_Line\" title=\"International Date Line\">International Date Line</a>, causing Monday (July 4) to occur twice, resulting in a leap year with 367 days.", "links": [{"title": "Western Samoa", "link": "https://wikipedia.org/wiki/Western_Samoa"}, {"title": "International Date Line", "link": "https://wikipedia.org/wiki/International_Date_Line"}]}, {"year": "1894", "text": "The short-lived Republic of Hawaii is proclaimed by <PERSON>.", "html": "1894 - The short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Hawaii\" title=\"Republic of Hawaii\">Republic of Hawaii</a> is proclaimed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Hawaii\" title=\"Republic of Hawaii\">Republic of Hawaii</a> is proclaimed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Republic of Hawaii", "link": "https://wikipedia.org/wiki/Republic_of_Hawaii"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "En route from New York to Le Havre, the SS La Bourgogne collides with another ship and sinks off the coast of Sable Island, with the loss of 549 lives.", "html": "1898 - En route from New York to <a href=\"https://wikipedia.org/wiki/Le_Havre\" title=\"Le Havre\">Le Havre</a>, the <a href=\"https://wikipedia.org/wiki/SS_La_Bourgogne\" title=\"SS La Bourgogne\">SS <i>La Bourgogne</i></a> collides with another ship and sinks off the coast of <a href=\"https://wikipedia.org/wiki/Sable_Island\" title=\"Sable Island\">Sable Island</a>, with the loss of 549 lives.", "no_year_html": "En route from New York to <a href=\"https://wikipedia.org/wiki/Le_Havre\" title=\"Le Havre\">Le Havre</a>, the <a href=\"https://wikipedia.org/wiki/SS_La_Bourgogne\" title=\"SS La Bourgogne\">SS <i>La Bourgogne</i></a> collides with another ship and sinks off the coast of <a href=\"https://wikipedia.org/wiki/Sable_Island\" title=\"Sable Island\">Sable Island</a>, with the loss of 549 lives.", "links": [{"title": "Le Havre", "link": "https://wikipedia.org/wiki/Le_<PERSON>vre"}, {"title": "SS La Bourgogne", "link": "https://wikipedia.org/wiki/SS_La_Bourgogne"}, {"title": "Sable Island", "link": "https://wikipedia.org/wiki/Sable_Island"}]}, {"year": "1901", "text": "<PERSON> becomes American governor of the Philippines.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes American governor of the Philippines.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes American governor of the Philippines.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "The Philippine-American War is officially concluded.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a> is officially concluded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a> is officially concluded.", "links": [{"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}]}, {"year": "1910", "text": "The <PERSON><PERSON><PERSON><PERSON> riots occur after African-American boxer <PERSON> knocks out white boxer <PERSON> in the 15th round. Between 11 and 26 people are killed and hundreds more injured.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Johnson%E2%80%93J<PERSON><PERSON><PERSON>_riots\" title=\"<PERSON><PERSON> riots\"><PERSON><PERSON><PERSON> riots</a> occur after African-American boxer <PERSON> knocks out white boxer <PERSON> in the 15th round. Between 11 and 26 people are killed and hundreds more injured.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>%E2%80%93J<PERSON><PERSON><PERSON>_riots\" title=\"<PERSON><PERSON><PERSON> riots\"><PERSON><PERSON><PERSON> riots</a> occur after African-American boxer <PERSON> knocks out white boxer <PERSON> in the 15th round. Between 11 and 26 people are killed and hundreds more injured.", "links": [{"title": "Johnson<PERSON><PERSON><PERSON> riots", "link": "https://wikipedia.org/wiki/Johnson%E2%80%93J<PERSON><PERSON><PERSON>_riots"}]}, {"year": "1911", "text": "A massive heat wave strikes the northeastern United States, killing 380 people in eleven days and breaking temperature records in several cities.", "html": "1911 - A <a href=\"https://wikipedia.org/wiki/1911_Eastern_North_America_heat_wave\" title=\"1911 Eastern North America heat wave\">massive heat wave</a> strikes the northeastern United States, killing 380 people in eleven days and breaking temperature records in several cities.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1911_Eastern_North_America_heat_wave\" title=\"1911 Eastern North America heat wave\">massive heat wave</a> strikes the northeastern United States, killing 380 people in eleven days and breaking temperature records in several cities.", "links": [{"title": "1911 Eastern North America heat wave", "link": "https://wikipedia.org/wiki/1911_Eastern_North_America_heat_wave"}]}, {"year": "1913", "text": "President <PERSON> addresses American Civil War veterans at the Great Reunion of 1913.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> addresses <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> veterans at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> addresses <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a> veterans at the <a href=\"https://wikipedia.org/wiki/Great_Reunion_of_1913\" class=\"mw-redirect\" title=\"Great Reunion of 1913\">Great Reunion of 1913</a>.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Great Reunion of 1913", "link": "https://wikipedia.org/wiki/Great_Reunion_of_1913"}]}, {"year": "1914", "text": "The funeral of <PERSON><PERSON>ke <PERSON> and his wife <PERSON> takes place in Vienna, six days after their assassinations in Sarajevo.", "html": "1914 - The funeral of <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON></a> and his wife <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Hohenberg\" title=\"<PERSON>, Duchess of Hohenberg\">Sophie</a> takes place in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, six days after their assassinations in <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "no_year_html": "The funeral of <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON></a> and his wife <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duchess_<PERSON>_Hohen<PERSON>\" title=\"<PERSON>, Duchess of Hohenberg\">Sophie</a> takes place in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, six days after their assassinations in <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "links": [{"title": "Archduke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archdu<PERSON>_<PERSON>_<PERSON>_of_Austria"}, {"title": "<PERSON>, Duchess of Hohenberg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duchess_of_Hohenberg"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}, {"title": "Sarajevo", "link": "https://wikipedia.org/wiki/Sarajevo"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON> died at the age of 73 and Ottoman sultan <PERSON><PERSON><PERSON> ascends to the throne.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_V\" title=\"Mehmed V\"><PERSON><PERSON><PERSON> V</a> died at the age of 73 and <a href=\"https://wikipedia.org/wiki/Ottoman_sultan\" class=\"mw-redirect\" title=\"Ottoman sultan\">Ottoman sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehm<PERSON> VI</a> ascends to the throne.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_V\" title=\"Mehmed V\"><PERSON><PERSON><PERSON> V</a> died at the age of 73 and <a href=\"https://wikipedia.org/wiki/Ottoman_sultan\" class=\"mw-redirect\" title=\"Ottoman sultan\">Ottoman sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehm<PERSON> VI</a> ascends to the throne.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mehmed_V"}, {"title": "Ottoman sultan", "link": "https://wikipedia.org/wiki/Ottoman_sultan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "World War I: The Battle of Hamel, a successful attack by the Australian Corps against German positions near the town of Le Hamel on the Western Front.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hamel\" title=\"Battle of Hamel\">Battle of Hamel</a>, a successful attack by the <a href=\"https://wikipedia.org/wiki/Australian_Corps\" title=\"Australian Corps\">Australian Corps</a> against German positions near the town of <a href=\"https://wikipedia.org/wiki/Le_Hamel,_Somme\" title=\"Le Hamel, Somme\"><PERSON> Ham<PERSON></a> on the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hamel\" title=\"Battle of Hamel\">Battle of Hamel</a>, a successful attack by the <a href=\"https://wikipedia.org/wiki/Australian_Corps\" title=\"Australian Corps\">Australian Corps</a> against German positions near the town of <a href=\"https://wikipedia.org/wiki/Le_Hamel,_Somme\" title=\"Le Hamel, Somme\">Le Ham<PERSON></a> on the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Hamel", "link": "https://wikipedia.org/wiki/Battle_of_Hamel"}, {"title": "Australian Corps", "link": "https://wikipedia.org/wiki/Australian_Corps"}, {"title": "Le Hamel, Somme", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Somme"}, {"title": "Western Front (World War I)", "link": "https://wikipedia.org/wiki/Western_Front_(World_War_I)"}]}, {"year": "1927", "text": "First flight of the Lockheed Vega.", "html": "1927 - First flight of the <a href=\"https://wikipedia.org/wiki/Lockheed_Vega\" title=\"Lockheed Vega\">Lockheed Vega</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Lockheed_Vega\" title=\"Lockheed Vega\">Lockheed Vega</a>.", "links": [{"title": "Lockheed Vega", "link": "https://wikipedia.org/wiki/Lockheed_Vega"}]}, {"year": "1939", "text": "<PERSON>, recently diagnosed with Amyotrophic lateral sclerosis, informs a crowd at Yankee Stadium that he considers himself \"The luckiest man on the face of the earth\", then announces his retirement from major league baseball.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, recently diagnosed with <a href=\"https://wikipedia.org/wiki/Amyotrophic_lateral_sclerosis\" class=\"mw-redirect\" title=\"Amyotrophic lateral sclerosis\">Amyotrophic lateral sclerosis</a>, informs a crowd at <a href=\"https://wikipedia.org/wiki/Yankee_Stadium_(1923)\" title=\"Yankee Stadium (1923)\">Yankee Stadium</a> that he considers himself \"The luckiest man on the face of the earth\", then announces his retirement from major league <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, recently diagnosed with <a href=\"https://wikipedia.org/wiki/Amyotrophic_lateral_sclerosis\" class=\"mw-redirect\" title=\"Amyotrophic lateral sclerosis\">Amyotrophic lateral sclerosis</a>, informs a crowd at <a href=\"https://wikipedia.org/wiki/Yankee_Stadium_(1923)\" title=\"Yankee Stadium (1923)\">Yankee Stadium</a> that he considers himself \"The luckiest man on the face of the earth\", then announces his retirement from major league <a href=\"https://wikipedia.org/wiki/Baseball\" title=\"Baseball\">baseball</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amyotrophic lateral sclerosis", "link": "https://wikipedia.org/wiki/Amyotrophic_lateral_sclerosis"}, {"title": "Yankee Stadium (1923)", "link": "https://wikipedia.org/wiki/Yankee_Stadium_(1923)"}, {"title": "Baseball", "link": "https://wikipedia.org/wiki/Baseball"}]}, {"year": "1941", "text": "Nazi crimes against the Polish nation: Nazi troops massacre Polish scientists and writers in the captured Ukrainian city of Lviv.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Nazi_crimes_against_the_Polish_nation\" class=\"mw-redirect\" title=\"Nazi crimes against the Polish nation\">Nazi crimes against the Polish nation</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi</a> troops <a href=\"https://wikipedia.org/wiki/Massacre_of_Lw%C3%B3w_professors\" title=\"Massacre of Lwów professors\">massacre Polish scientists and writers</a> in the captured <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_crimes_against_the_Polish_nation\" class=\"mw-redirect\" title=\"Nazi crimes against the Polish nation\">Nazi crimes against the Polish nation</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi</a> troops <a href=\"https://wikipedia.org/wiki/Massacre_of_Lw%C3%B3w_professors\" title=\"Massacre of Lwów professors\">massacre Polish scientists and writers</a> in the captured <a href=\"https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic\" title=\"Ukrainian Soviet Socialist Republic\">Ukrainian</a> city of <a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>.", "links": [{"title": "Nazi crimes against the Polish nation", "link": "https://wikipedia.org/wiki/Nazi_crimes_against_the_Polish_nation"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Massacre of Lwów professors", "link": "https://wikipedia.org/wiki/Massacre_of_Lw%C3%B3w_professors"}, {"title": "Ukrainian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Ukrainian_Soviet_Socialist_Republic"}, {"title": "Lviv", "link": "https://wikipedia.org/wiki/Lviv"}]}, {"year": "1941", "text": "World War II: The Burning of the Riga synagogues: The Great Choral Synagogue in German-occupied Riga is burnt with 300 Jews locked in the basement.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Burning_of_the_Riga_synagogues\" title=\"Burning of the Riga synagogues\">Burning of the Riga synagogues</a>: The Great Choral Synagogue in German-occupied <a href=\"https://wikipedia.org/wiki/Riga\" title=\"Riga\">Riga</a> is burnt with 300 Jews locked in the basement.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Burning_of_the_Riga_synagogues\" title=\"Burning of the Riga synagogues\">Burning of the Riga synagogues</a>: The Great Choral Synagogue in German-occupied <a href=\"https://wikipedia.org/wiki/Riga\" title=\"Riga\">Riga</a> is burnt with 300 Jews locked in the basement.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Burning of the Riga synagogues", "link": "https://wikipedia.org/wiki/Burning_of_the_Riga_synagogues"}, {"title": "Riga", "link": "https://wikipedia.org/wiki/Riga"}]}, {"year": "1942", "text": "World War II: The 250-day Siege of Sevastopol in the Crimea ends when the city falls to Axis forces.", "html": "1942 - World War II: The 250-day <a href=\"https://wikipedia.org/wiki/Siege_of_Sevastopol_(1941%E2%80%9342)\" class=\"mw-redirect\" title=\"Siege of Sevastopol (1941-42)\">Siege of Sevastopol</a> in the <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> ends when the city falls to <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis</a> forces.", "no_year_html": "World War II: The 250-day <a href=\"https://wikipedia.org/wiki/Siege_of_Sevastopol_(1941%E2%80%9342)\" class=\"mw-redirect\" title=\"Siege of Sevastopol (1941-42)\">Siege of Sevastopol</a> in the <a href=\"https://wikipedia.org/wiki/Crimea\" title=\"Crimea\">Crimea</a> ends when the city falls to <a href=\"https://wikipedia.org/wiki/Axis_powers\" title=\"Axis powers\">Axis</a> forces.", "links": [{"title": "Siege of Sevastopol (1941-42)", "link": "https://wikipedia.org/wiki/Siege_of_Sevastopol_(1941%E2%80%9342)"}, {"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea"}, {"title": "Axis powers", "link": "https://wikipedia.org/wiki/Axis_powers"}]}, {"year": "1943", "text": "World War II: The Battle of Kursk, the largest full-scale battle in history and the world's largest tank battle, begins in the village of Prokhorovka.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kursk\" title=\"Battle of Kursk\">Battle of Kursk</a>, the largest full-scale battle in history and the world's largest tank battle, begins in the village of <a href=\"https://wikipedia.org/wiki/Prokhorovka\" title=\"Prokhorovka\">Prokhorovka</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Kursk\" title=\"Battle of Kursk\">Battle of Kursk</a>, the largest full-scale battle in history and the world's largest tank battle, begins in the village of <a href=\"https://wikipedia.org/wiki/Prokhorovka\" title=\"Prokhorovka\">Prokhorovka</a>.", "links": [{"title": "Battle of Kursk", "link": "https://wikipedia.org/wiki/Battle_of_Kursk"}, {"title": "Prokhorovka", "link": "https://wikipedia.org/wiki/Prokhorovka"}]}, {"year": "1943", "text": "World War II: In Gibraltar, a Royal Air Force B-24 Liberator bomber crashes into the sea in an apparent accident moments after takeoff, killing sixteen passengers on board, including general <PERSON><PERSON><PERSON><PERSON>, the commander-in-chief of the Polish Army and the Prime Minister of the Polish government-in-exile; only the pilot survives.", "html": "1943 - World War II: In <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>, a <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> <a href=\"https://wikipedia.org/wiki/Consolidated_B-24_Liberator\" title=\"Consolidated B-24 Liberator\">B-24 Liberator</a> bomber <a href=\"https://wikipedia.org/wiki/1943_Gibraltar_Liberator_AL523_crash\" title=\"1943 Gibraltar Liberator AL523 crash\">crashes</a> into the sea in an apparent accident moments after takeoff, killing sixteen passengers on board, including general <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Si<PERSON>ski\" title=\"Wła<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, the commander-in-chief of the Polish Army and the Prime Minister of the Polish government-in-exile; only the pilot survives.", "no_year_html": "World War II: In <a href=\"https://wikipedia.org/wiki/Gibraltar\" title=\"Gibraltar\">Gibraltar</a>, a <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> <a href=\"https://wikipedia.org/wiki/Consolidated_B-24_Liberator\" title=\"Consolidated B-24 Liberator\">B-24 Liberator</a> bomber <a href=\"https://wikipedia.org/wiki/1943_Gibraltar_Liberator_AL523_crash\" title=\"1943 Gibraltar Liberator AL523 crash\">crashes</a> into the sea in an apparent accident moments after takeoff, killing sixteen passengers on board, including general <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Si<PERSON>ski\" title=\"Wła<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, the commander-in-chief of the Polish Army and the Prime Minister of the Polish government-in-exile; only the pilot survives.", "links": [{"title": "Gibraltar", "link": "https://wikipedia.org/wiki/Gibraltar"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Consolidated B-24 Liberator", "link": "https://wikipedia.org/wiki/Consolidated_B-24_Liberator"}, {"title": "1943 Gibraltar Liberator AL523 crash", "link": "https://wikipedia.org/wiki/1943_Gibraltar_Liberator_AL523_crash"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "The Kielce pogrom against Jewish Holocaust survivors in Poland.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Kielce_pogrom\" title=\"Kielce pogrom\"><PERSON><PERSON> pogrom</a> against Jewish Holocaust survivors in Poland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kielce_pogrom\" title=\"Kielce pogrom\"><PERSON><PERSON> pogrom</a> against Jewish Holocaust survivors in Poland.", "links": [{"title": "<PERSON><PERSON> pogrom", "link": "https://wikipedia.org/wiki/Kielce_pogrom"}]}, {"year": "1946", "text": "After 381 years of near-continuous colonial rule by various powers, the Philippines attains full independence from the United States.", "html": "1946 - After 381 years of near-continuous colonial rule by various powers, the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> attains full independence from the United States.", "no_year_html": "After 381 years of near-continuous colonial rule by various powers, the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> attains full independence from the United States.", "links": [{"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1947", "text": "The \"Indian Independence Bill\" is presented before the British House of Commons, proposing the independence of the Provinces of British India into two sovereign countries: India and Pakistan.", "html": "1947 - The \"Indian Independence Bill\" is presented before the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>, proposing the independence of the <a href=\"https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India\" title=\"Presidencies and provinces of British India\">Provinces of British India</a> into two sovereign countries: India and <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Pakistan</a>.", "no_year_html": "The \"Indian Independence Bill\" is presented before the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">British House of Commons</a>, proposing the independence of the <a href=\"https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India\" title=\"Presidencies and provinces of British India\">Provinces of British India</a> into two sovereign countries: India and <a href=\"https://wikipedia.org/wiki/Dominion_of_Pakistan\" title=\"Dominion of Pakistan\">Pakistan</a>.", "links": [{"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}, {"title": "Presidencies and provinces of British India", "link": "https://wikipedia.org/wiki/Presidencies_and_provinces_of_British_India"}, {"title": "Dominion of Pakistan", "link": "https://wikipedia.org/wiki/Dominion_of_Pakistan"}]}, {"year": "1950", "text": "Cold War: Radio Free Europe first broadcasts.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Radio_Free_Europe/Radio_Liberty\" title=\"Radio Free Europe/Radio Liberty\">Radio Free Europe</a> first broadcasts.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Radio_Free_Europe/Radio_Liberty\" title=\"Radio Free Europe/Radio Liberty\">Radio Free Europe</a> first broadcasts.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Radio Free Europe/Radio Liberty", "link": "https://wikipedia.org/wiki/Radio_Free_Europe/Radio_Liberty"}]}, {"year": "1951", "text": "Cold War: A court in Czechoslovakia sentences American journalist <PERSON> to ten years in prison on charges of espionage.", "html": "1951 - Cold War: A court in <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovakia</a> sentences American journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to ten years in prison on charges of <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "no_year_html": "Cold War: A court in <a href=\"https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic\" title=\"Czechoslovak Socialist Republic\">Czechoslovakia</a> sentences American journalist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to ten years in prison on charges of <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "links": [{"title": "Czechoslovak Socialist Republic", "link": "https://wikipedia.org/wiki/Czechoslovak_Socialist_Republic"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1951", "text": "<PERSON> announces the invention of the junction transistor.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the invention of the <a href=\"https://wikipedia.org/wiki/Bipolar_junction_transistor\" title=\"Bipolar junction transistor\">junction transistor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the invention of the <a href=\"https://wikipedia.org/wiki/Bipolar_junction_transistor\" title=\"Bipolar junction transistor\">junction transistor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bipolar junction transistor", "link": "https://wikipedia.org/wiki/Bipolar_junction_transistor"}]}, {"year": "1954", "text": "Food rationing in Great Britain ends, with the lifting of restrictions on sale and purchase of meat, 14 years after it began early in World War II, and nearly a decade after the war's end.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom\" title=\"Rationing in the United Kingdom\">Food rationing in Great Britain</a> ends, with the lifting of restrictions on sale and purchase of meat, 14 years after it began early in <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, and nearly a decade after the war's end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom\" title=\"Rationing in the United Kingdom\">Food rationing in Great Britain</a> ends, with the lifting of restrictions on sale and purchase of meat, 14 years after it began early in <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>, and nearly a decade after the war's end.", "links": [{"title": "Rationing in the United Kingdom", "link": "https://wikipedia.org/wiki/Rationing_in_the_United_Kingdom"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1960", "text": "Due to the post-Independence Day admission of Hawaii as the 50th U.S. state on August 21, 1959, the 50-star flag of the United States debuts in Philadelphia, almost ten and a half months later (see Flag Acts (United States)).", "html": "1960 - Due to the post-Independence Day admission of Hawaii as the 50th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> on <a href=\"https://wikipedia.org/wiki/August_21\" title=\"August 21\">August 21</a>, 1959, the 50-star <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">flag of the United States</a> debuts in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, almost ten and a half months later (see <a href=\"https://wikipedia.org/wiki/Flag_Acts_(United_States)\" class=\"mw-redirect\" title=\"Flag Acts (United States)\">Flag Acts (United States)</a>).", "no_year_html": "Due to the post-Independence Day admission of Hawaii as the 50th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> on <a href=\"https://wikipedia.org/wiki/August_21\" title=\"August 21\">August 21</a>, 1959, the 50-star <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">flag of the United States</a> debuts in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, almost ten and a half months later (see <a href=\"https://wikipedia.org/wiki/Flag_Acts_(United_States)\" class=\"mw-redirect\" title=\"Flag Acts (United States)\">Flag Acts (United States)</a>).", "links": [{"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "August 21", "link": "https://wikipedia.org/wiki/August_21"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "Flag Acts (United States)", "link": "https://wikipedia.org/wiki/Flag_Acts_(United_States)"}]}, {"year": "1961", "text": "On its maiden voyage, the Soviet nuclear-powered submarine K-19 suffers a complete loss of coolant to its reactor. The crew are able to effect repairs, but 22 of them die of radiation poisoning over the following two years.", "html": "1961 - On its maiden voyage, the Soviet nuclear-powered submarine <a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\"><i>K-19</i></a> suffers a complete loss of coolant to its reactor. The crew are able to effect repairs, but 22 of them die of <a href=\"https://wikipedia.org/wiki/Radiation_poisoning\" class=\"mw-redirect\" title=\"Radiation poisoning\">radiation poisoning</a> over the following two years.", "no_year_html": "On its maiden voyage, the Soviet nuclear-powered submarine <a href=\"https://wikipedia.org/wiki/Soviet_submarine_K-19\" title=\"Soviet submarine K-19\"><i>K-19</i></a> suffers a complete loss of coolant to its reactor. The crew are able to effect repairs, but 22 of them die of <a href=\"https://wikipedia.org/wiki/Radiation_poisoning\" class=\"mw-redirect\" title=\"Radiation poisoning\">radiation poisoning</a> over the following two years.", "links": [{"title": "Soviet submarine K-19", "link": "https://wikipedia.org/wiki/Soviet_submarine_K-19"}, {"title": "Radiation poisoning", "link": "https://wikipedia.org/wiki/Radiation_poisoning"}]}, {"year": "1966", "text": "U.S. President <PERSON> signs the Freedom of Information Act into United States law. The act went into effect the next year.", "html": "1966 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Freedom of Information Act</a> into United States law. The act went into effect the next year.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)\" title=\"Freedom of Information Act (United States)\">Freedom of Information Act</a> into United States law. The act went into effect the next year.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Freedom of Information Act (United States)", "link": "https://wikipedia.org/wiki/Freedom_of_Information_Act_(United_States)"}]}, {"year": "1976", "text": "Israeli commandos raid Entebbe airport in Uganda, rescuing all but four of the passengers and crew of an Air France jetliner seized by Palestinian terrorists.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli commandos</a> <a href=\"https://wikipedia.org/wiki/Entebbe_raid\" title=\"Entebbe raid\">raid Entebbe</a> airport in <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a>, rescuing all but four of the passengers and crew of an <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> jetliner seized by Palestinian terrorists.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israeli commandos</a> <a href=\"https://wikipedia.org/wiki/Entebbe_raid\" title=\"Entebbe raid\">raid Entebbe</a> airport in <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a>, rescuing all but four of the passengers and crew of an <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> jetliner seized by Palestinian terrorists.", "links": [{"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "<PERSON><PERSON><PERSON> raid", "link": "https://wikipedia.org/wiki/Entebbe_raid"}, {"title": "Uganda", "link": "https://wikipedia.org/wiki/Uganda"}, {"title": "Air France", "link": "https://wikipedia.org/wiki/Air_France"}]}, {"year": "1977", "text": "The George Jackson Brigade plants a bomb at the main power substation for the Washington state capitol in Olympia, in solidarity with a prison strike at the Walla Walla State Penitentiary Intensive Security Unit.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brigade\" title=\"George Jackson Brigade\">George <PERSON></a> plants a bomb at the main power substation for the <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> state capitol in <a href=\"https://wikipedia.org/wiki/Olympia,_Washington\" title=\"Olympia, Washington\">Olympia</a>, in solidarity with a prison strike at the <a href=\"https://wikipedia.org/wiki/Walla_Walla_State_Penitentiary\" class=\"mw-redirect\" title=\"Walla Walla State Penitentiary\">Walla Walla State Penitentiary</a> Intensive Security Unit.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George Jackson Brigade\">George <PERSON></a> plants a bomb at the main power substation for the <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> state capitol in <a href=\"https://wikipedia.org/wiki/Olympia,_Washington\" title=\"Olympia, Washington\">Olympia</a>, in solidarity with a prison strike at the <a href=\"https://wikipedia.org/wiki/Walla_Walla_State_Penitentiary\" class=\"mw-redirect\" title=\"Walla Walla State Penitentiary\">Walla Walla State Penitentiary</a> Intensive Security Unit.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}, {"title": "Olympia, Washington", "link": "https://wikipedia.org/wiki/Olympia,_Washington"}, {"title": "Walla Walla State Penitentiary", "link": "https://wikipedia.org/wiki/Walla_Walla_State_Penitentiary"}]}, {"year": "1982", "text": "Three Iranian diplomats and a journalist are kidnapped in Lebanon by Phalange forces, and their fate remains unknown.", "html": "1982 - Three Iranian diplomats and a journalist are <a href=\"https://wikipedia.org/wiki/Iranian_diplomats_kidnapping_(1982)\" class=\"mw-redirect\" title=\"Iranian diplomats kidnapping (1982)\">kidnapped in Lebanon</a> by <a href=\"https://wikipedia.org/wiki/Kataeb_Party\" title=\"Kataeb Party\">Phalange forces</a>, and their fate remains unknown.", "no_year_html": "Three Iranian diplomats and a journalist are <a href=\"https://wikipedia.org/wiki/Iranian_diplomats_kidnapping_(1982)\" class=\"mw-redirect\" title=\"Iranian diplomats kidnapping (1982)\">kidnapped in Lebanon</a> by <a href=\"https://wikipedia.org/wiki/Kataeb_Party\" title=\"Kataeb Party\">Phalange forces</a>, and their fate remains unknown.", "links": [{"title": "Iranian diplomats kidnapping (1982)", "link": "https://wikipedia.org/wiki/Iranian_diplomats_kidnapping_(1982)"}, {"title": "Kataeb Party", "link": "https://wikipedia.org/wiki/Kataeb_Party"}]}, {"year": "1982", "text": "Space Shuttle program: Columbia lands at Edwards Air Force Base at the end of the program's final test flight, STS-4. President <PERSON> declares the Space Shuttle to be operational.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i> lands at <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Edwards Air Force Base</a> at the end of the program's final test flight, <a href=\"https://wikipedia.org/wiki/STS-4\" title=\"STS-4\">STS-4</a>. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the Space Shuttle to be operational.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i> lands at <a href=\"https://wikipedia.org/wiki/Edwards_Air_Force_Base\" title=\"Edwards Air Force Base\">Edwards Air Force Base</a> at the end of the program's final test flight, <a href=\"https://wikipedia.org/wiki/STS-4\" title=\"STS-4\">STS-4</a>. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the Space Shuttle to be operational.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "Edwards Air Force Base", "link": "https://wikipedia.org/wiki/Edwards_Air_Force_Base"}, {"title": "STS-4", "link": "https://wikipedia.org/wiki/STS-4"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Rwandan genocide: Kigali, the Rwandan capital, is captured by the Rwandan Patriotic Front, ending the genocide in the city.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a>: <a href=\"https://wikipedia.org/wiki/Kigali\" title=\"Kigali\">Kigali</a>, the Rwandan capital, is captured by the <a href=\"https://wikipedia.org/wiki/Rwandan_Patriotic_Front\" title=\"Rwandan Patriotic Front\">Rwandan Patriotic Front</a>, ending the genocide in the city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a>: <a href=\"https://wikipedia.org/wiki/Kigali\" title=\"Kigali\">Kigali</a>, the Rwandan capital, is captured by the <a href=\"https://wikipedia.org/wiki/Rwandan_Patriotic_Front\" title=\"Rwandan Patriotic Front\">Rwandan Patriotic Front</a>, ending the genocide in the city.", "links": [{"title": "Rwandan genocide", "link": "https://wikipedia.org/wiki/Rwandan_genocide"}, {"title": "Kigali", "link": "https://wikipedia.org/wiki/Kigali"}, {"title": "Rwandan Patriotic Front", "link": "https://wikipedia.org/wiki/Rwandan_Patriotic_Front"}]}, {"year": "1997", "text": "NASA's Pathfinder space probe lands on the surface of Mars.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Pathfinder\" title=\"Mars Pathfinder\">Pathfinder</a> space probe lands on the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Mars_Pathfinder\" title=\"Mars Pathfinder\">Pathfinder</a> space probe lands on the surface of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Pathfinder", "link": "https://wikipedia.org/wiki/Mars_Pathfinder"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "1998", "text": "Japan launches the Nozomi probe to Mars, joining the United States and Russia as a space exploring nation.", "html": "1998 - Japan launches the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\">Nozomi</a> probe to <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, joining the United States and Russia as a <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a> exploring nation.", "no_year_html": "Japan launches the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\"><PERSON><PERSON>mi</a> probe to <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>, joining the United States and Russia as a <a href=\"https://wikipedia.org/wiki/Outer_space\" title=\"Outer space\">space</a> exploring nation.", "links": [{"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "Outer space", "link": "https://wikipedia.org/wiki/Outer_space"}]}, {"year": "2001", "text": "Vladivostok Air Flight 352 crashes on approach to Irkutsk Airport killing all 145 people on board.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Vladivostok_Air_Flight_352\" title=\"Vladivostok Air Flight 352\">Vladivostok Air Flight 352</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/International_Airport_Irkutsk\" title=\"International Airport Irkutsk\">Irkutsk Airport</a> killing all 145 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladivostok_Air_Flight_352\" title=\"Vladivostok Air Flight 352\">Vladivostok Air Flight 352</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/International_Airport_Irkutsk\" title=\"International Airport Irkutsk\">Irkutsk Airport</a> killing all 145 people on board.", "links": [{"title": "Vladivostok Air Flight 352", "link": "https://wikipedia.org/wiki/Vladivostok_Air_Flight_352"}, {"title": "International Airport Irkutsk", "link": "https://wikipedia.org/wiki/International_Airport_Irkutsk"}]}, {"year": "2002", "text": "A Boeing 707 crashes near Bangui M'Poko International Airport in Bangui, Central African Republic, killing 28.", "html": "2002 - A <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> <a href=\"https://wikipedia.org/wiki/2002_Prestige_Airlines_Boeing_707_crash\" title=\"2002 Prestige Airlines Boeing 707 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bangui_M%27Poko_International_Airport\" title=\"Bangui M'Poko International Airport\">Bangui M'Poko International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bangui\" title=\"Bangui\">Bangui</a>, <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>, killing 28.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> <a href=\"https://wikipedia.org/wiki/2002_Prestige_Airlines_Boeing_707_crash\" title=\"2002 Prestige Airlines Boeing 707 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bangui_M%27Poko_International_Airport\" title=\"Bangui M'Poko International Airport\">Bangui M'Poko International Airport</a> in <a href=\"https://wikipedia.org/wiki/Bangui\" title=\"Bangui\">Bangui</a>, <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>, killing 28.", "links": [{"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "2002 Prestige Airlines Boeing 707 crash", "link": "https://wikipedia.org/wiki/2002_Prestige_Airlines_Boeing_707_crash"}, {"title": "Bangui M'Poko International Airport", "link": "https://wikipedia.org/wiki/Bangui_M%27Poko_International_Airport"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bangui"}, {"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}]}, {"year": "2004", "text": "The cornerstone of the Freedom Tower is laid on the World Trade Center site in New York City.", "html": "2004 - The cornerstone of the <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">Freedom Tower</a> is laid on the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_site\" title=\"World Trade Center site\">World Trade Center site</a> in New York City.", "no_year_html": "The cornerstone of the <a href=\"https://wikipedia.org/wiki/One_World_Trade_Center\" title=\"One World Trade Center\">Freedom Tower</a> is laid on the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_site\" title=\"World Trade Center site\">World Trade Center site</a> in New York City.", "links": [{"title": "One World Trade Center", "link": "https://wikipedia.org/wiki/One_World_Trade_Center"}, {"title": "World Trade Center site", "link": "https://wikipedia.org/wiki/World_Trade_Center_site"}]}, {"year": "2004", "text": "Greece beats Portugal in the UEFA Euro 2004 Final and becomes European Champion for first time in its history.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Greece_national_football_team\" title=\"Greece national football team\">Greece</a> beats <a href=\"https://wikipedia.org/wiki/Portugal_national_football_team\" title=\"Portugal national football team\">Portugal</a> in the <a href=\"https://wikipedia.org/wiki/UEFA_Euro_2004_Final\" class=\"mw-redirect\" title=\"UEFA Euro 2004 Final\">UEFA Euro 2004 Final</a> and becomes European Champion for first time in its history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greece_national_football_team\" title=\"Greece national football team\">Greece</a> beats <a href=\"https://wikipedia.org/wiki/Portugal_national_football_team\" title=\"Portugal national football team\">Portugal</a> in the <a href=\"https://wikipedia.org/wiki/UEFA_Euro_2004_Final\" class=\"mw-redirect\" title=\"UEFA Euro 2004 Final\">UEFA Euro 2004 Final</a> and becomes European Champion for first time in its history.", "links": [{"title": "Greece national football team", "link": "https://wikipedia.org/wiki/Greece_national_football_team"}, {"title": "Portugal national football team", "link": "https://wikipedia.org/wiki/Portugal_national_football_team"}, {"title": "UEFA Euro 2004 Final", "link": "https://wikipedia.org/wiki/UEFA_Euro_2004_Final"}]}, {"year": "2005", "text": "The Deep Impact collider hits the comet Tempel 1.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Deep_Impact_(spacecraft)\" title=\"Deep Impact (spacecraft)\">Deep Impact</a> collider hits the <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> <a href=\"https://wikipedia.org/wiki/Tempel_1\" title=\"Tempel 1\">Tempel 1</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Deep_Impact_(spacecraft)\" title=\"Deep Impact (spacecraft)\">Deep Impact</a> collider hits the <a href=\"https://wikipedia.org/wiki/Comet\" title=\"Comet\">comet</a> <a href=\"https://wikipedia.org/wiki/Tempel_1\" title=\"Tempel 1\">Tempel 1</a>.", "links": [{"title": "Deep Impact (spacecraft)", "link": "https://wikipedia.org/wiki/Deep_Impact_(spacecraft)"}, {"title": "Comet", "link": "https://wikipedia.org/wiki/Comet"}, {"title": "Tempel 1", "link": "https://wikipedia.org/wiki/Tempel_1"}]}, {"year": "2006", "text": "Space Shuttle program: Discovery launches STS-121 to the International Space Station. The event gained wide media attention as it was the only shuttle launch in the program's history to occur on the United States' Independence Day.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/OV-103\" class=\"mw-redirect\" title=\"OV-103\">Discovery</a></i> launches <a href=\"https://wikipedia.org/wiki/STS-121\" title=\"STS-121\">STS-121</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. The event gained wide media attention as it was the only shuttle launch in the program's history to occur on the United States' <a href=\"https://wikipedia.org/wiki/Independence_Day_(United_States)\" title=\"Independence Day (United States)\">Independence Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <i><a href=\"https://wikipedia.org/wiki/OV-103\" class=\"mw-redirect\" title=\"OV-103\">Discovery</a></i> launches <a href=\"https://wikipedia.org/wiki/STS-121\" title=\"STS-121\">STS-121</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>. The event gained wide media attention as it was the only shuttle launch in the program's history to occur on the United States' <a href=\"https://wikipedia.org/wiki/Independence_Day_(United_States)\" title=\"Independence Day (United States)\">Independence Day</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "OV-103", "link": "https://wikipedia.org/wiki/OV-103"}, {"title": "STS-121", "link": "https://wikipedia.org/wiki/STS-121"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "Independence Day (United States)", "link": "https://wikipedia.org/wiki/Independence_Day_(United_States)"}]}, {"year": "2008", "text": "A bomb explodes at a concert in Minsk's Independence Square, injuring 50 people.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/2008_Minsk_bombing\" title=\"2008 Minsk bombing\">bomb explodes</a> at a concert in <a href=\"https://wikipedia.org/wiki/Independence_Square,_Minsk\" title=\"Independence Square, Minsk\">Minsk's Independence Square</a>, injuring 50 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2008_Minsk_bombing\" title=\"2008 Minsk bombing\">bomb explodes</a> at a concert in <a href=\"https://wikipedia.org/wiki/Independence_Square,_Minsk\" title=\"Independence Square, Minsk\">Minsk's Independence Square</a>, injuring 50 people.", "links": [{"title": "2008 Minsk bombing", "link": "https://wikipedia.org/wiki/2008_Minsk_bombing"}, {"title": "Independence Square, Minsk", "link": "https://wikipedia.org/wiki/Independence_Square,_Minsk"}]}, {"year": "2009", "text": "The Statue of Liberty's crown reopens to the public after eight years of closure due to security concerns following the September 11 attacks.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a>'s crown reopens to the public after eight years of closure due to security concerns following the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a>'s crown reopens to the public after eight years of closure due to security concerns following the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "links": [{"title": "Statue of Liberty", "link": "https://wikipedia.org/wiki/Statue_of_Liberty"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "2009", "text": "The first of four days of bombings begins on the southern Philippine island group of Mindanao.", "html": "2009 - The first of <a href=\"https://wikipedia.org/wiki/2009_Mindanao_bombings\" title=\"2009 Mindanao bombings\">four days of bombings</a> begins on the southern <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> island group of <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>.", "no_year_html": "The first of <a href=\"https://wikipedia.org/wiki/2009_Mindanao_bombings\" title=\"2009 Mindanao bombings\">four days of bombings</a> begins on the southern <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippine</a> island group of <a href=\"https://wikipedia.org/wiki/Mindanao\" title=\"Mindanao\">Mindanao</a>.", "links": [{"title": "2009 Mindanao bombings", "link": "https://wikipedia.org/wiki/2009_Mindanao_bombings"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Mindanao", "link": "https://wikipedia.org/wiki/Mindanao"}]}, {"year": "2012", "text": "The discovery of particles consistent with the Higgs boson at the Large Hadron Collider is announced at CERN.", "html": "2012 - The discovery of particles consistent with the <a href=\"https://wikipedia.org/wiki/Higgs_boson\" title=\"Higgs boson\">Higgs boson</a> at the <a href=\"https://wikipedia.org/wiki/Large_Hadron_Collider\" title=\"Large Hadron Collider\">Large Hadron Collider</a> is announced at <a href=\"https://wikipedia.org/wiki/CERN\" title=\"CERN\">CERN</a>.", "no_year_html": "The discovery of particles consistent with the <a href=\"https://wikipedia.org/wiki/Higgs_boson\" title=\"Higgs boson\">Higgs boson</a> at the <a href=\"https://wikipedia.org/wiki/Large_Hadron_Collider\" title=\"Large Hadron Collider\">Large Hadron Collider</a> is announced at <a href=\"https://wikipedia.org/wiki/CERN\" title=\"CERN\">CERN</a>.", "links": [{"title": "Higgs boson", "link": "https://wikipedia.org/wiki/Higgs_boson"}, {"title": "Large Hadron Collider", "link": "https://wikipedia.org/wiki/Large_Hadron_Collider"}, {"title": "CERN", "link": "https://wikipedia.org/wiki/CERN"}]}, {"year": "2015", "text": "Chile claims its first title in international football by defeating Argentina in the 2015 Copa América Final.", "html": "2015 - Chile claims its first title in international <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> by defeating Argentina in the <a href=\"https://wikipedia.org/wiki/2015_Copa_Am%C3%A9rica\" title=\"2015 Copa América\">2015 Copa América</a> Final.", "no_year_html": "Chile claims its first title in international <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">football</a> by defeating Argentina in the <a href=\"https://wikipedia.org/wiki/2015_Copa_Am%C3%A9rica\" title=\"2015 Copa América\">2015 Copa América</a> Final.", "links": [{"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}, {"title": "2015 Copa América", "link": "https://wikipedia.org/wiki/2015_Copa_Am%C3%A9rica"}]}, {"year": "2024", "text": "The Labour Party, led by <PERSON><PERSON>, wins a landslide majority in the 2024 United Kingdom general election, ending 14 years of Conservative government.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, wins a <a href=\"https://wikipedia.org/wiki/Landslide_victory\" title=\"Landslide victory\">landslide majority</a> in the <a href=\"https://wikipedia.org/wiki/2024_United_Kingdom_general_election\" title=\"2024 United Kingdom general election\">2024 United Kingdom general election</a>, ending 14 years of <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative</a> government.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Labour_Party_(UK)\" title=\"Labour Party (UK)\">Labour Party</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, wins a <a href=\"https://wikipedia.org/wiki/Landslide_victory\" title=\"Landslide victory\">landslide majority</a> in the <a href=\"https://wikipedia.org/wiki/2024_United_Kingdom_general_election\" title=\"2024 United Kingdom general election\">2024 United Kingdom general election</a>, ending 14 years of <a href=\"https://wikipedia.org/wiki/Conservative_Party_(UK)\" title=\"Conservative Party (UK)\">Conservative</a> government.", "links": [{"title": "Labour Party (UK)", "link": "https://wikipedia.org/wiki/Labour_Party_(UK)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mer"}, {"title": "Landslide victory", "link": "https://wikipedia.org/wiki/Landslide_victory"}, {"title": "2024 United Kingdom general election", "link": "https://wikipedia.org/wiki/2024_United_Kingdom_general_election"}, {"title": "Conservative Party (UK)", "link": "https://wikipedia.org/wiki/Conservative_Party_(UK)"}]}], "Births": [{"year": "68", "text": "<PERSON><PERSON>, Roman daughter of <PERSON><PERSON><PERSON> (d. 119)", "html": "68 - <a href=\"https://wikipedia.org/wiki/Salon<PERSON>_Matidia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ulpia <PERSON>\"><PERSON><PERSON><PERSON></a> (d. 119)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Matidia\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ul<PERSON>\"><PERSON><PERSON><PERSON></a> (d. 119)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salonia_Matidia"}, {"title": "Ulpia <PERSON>", "link": "https://wikipedia.org/wiki/Ulpia_Marciana"}]}, {"year": "1095", "text": "<PERSON><PERSON>, Muslim poet, author and faris (Knight) (d. 1188)", "html": "1095 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Muslim poet, author and faris (Knight) (d. 1188)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Muslim poet, author and faris (Knight) (d. 1188)", "links": [{"title": "<PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1330", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (d. 1367)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ash<PERSON><PERSON> Yoshiakira\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1367)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshiakira\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1367)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ra"}]}, {"year": "1477", "text": "<PERSON>, Bavarian historian and philologist (d. 1534)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bavarian historian and philologist (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bavarian historian and philologist (d. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON><PERSON>, Ottoman sultan (d. 1595)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/Murad_III\" title=\"Murad III\"><PERSON><PERSON> III</a>, Ottoman sultan (d. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murad_III\" title=\"Murad III\"><PERSON><PERSON></a>, Ottoman sultan (d. 1595)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murad_III"}]}, {"year": "1656", "text": "<PERSON>, Royal Navy admiral (d. 1720)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Navy admiral (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Navy admiral (d. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON><PERSON><PERSON>, French organist and composer (d. 1772)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (d. 1772)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1715", "text": "<PERSON>, German poet and academic (d. 1769)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Christian_F%C3%BCrchte<PERSON><PERSON>_Gellert\" title=\"Christian <PERSON>ürchtegot<PERSON> Gellert\"><PERSON></a>, German poet and academic (d. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_F%C3%BCrchte<PERSON><PERSON>_Gellert\" title=\"Christian <PERSON>ürchtegot<PERSON> Gellert\"><PERSON></a>, German poet and academic (d. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_F%C3%BCrchte<PERSON><PERSON>_Gellert"}]}, {"year": "1719", "text": "<PERSON><PERSON><PERSON>, French playwright (d. 1797)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French playwright (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French playwright (d. 1797)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, American lawyer, jurist and politician (d. 1819)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Congressman)\" class=\"mw-redirect\" title=\"<PERSON> (Congressman)\"><PERSON></a>, American lawyer, jurist and politician (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Congressman)\" class=\"mw-redirect\" title=\"<PERSON> (Congressman)\"><PERSON></a>, American lawyer, jurist and politician (d. 1819)", "links": [{"title": "<PERSON> (Congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Congressman)"}]}, {"year": "1753", "text": "<PERSON><PERSON><PERSON>, French inventor, best known as a pioneer in balloon flight (d. 1809)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French inventor, best known as a pioneer in balloon flight (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French inventor, best known as a pioneer in balloon flight (d. 1809)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1790", "text": "<PERSON>, Welsh geographer and surveyor (d. 1866)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George Everest\"><PERSON></a>, Welsh geographer and surveyor (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Everest\" title=\"George Everest\"><PERSON></a>, Welsh geographer and surveyor (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON> of Sweden (d. 1859)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a> (d. 1859)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1804", "text": "<PERSON>, American novelist and short story writer (d. 1864)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, Italian general and politician (d. 1882)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian general and politician (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian general and politician (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON>, American businessman, founded Canadian Club whisky (d. 1899)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Canadian_Club\" title=\"Canadian Club\">Canadian Club whisky</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Canadian_Club\" title=\"Canadian Club\">Canadian Club whisky</a> (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Canadian Club", "link": "https://wikipedia.org/wiki/Canadian_Club"}]}, {"year": "1826", "text": "<PERSON>, American songwriter and composer (d. 1864)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, German philosopher (d. 1918)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Irish philanthropist and humanitarian (d. 1905)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish philanthropist and humanitarian (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish philanthropist and humanitarian (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, American circus ringmaster, co-founded Ringling Bros. and Barnum & Bailey Circus (d. 1906)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American circus ringmaster, co-founded <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American circus ringmaster, co-founded <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_Circus\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ringling Bros. and Barnum & Bailey Circus", "link": "https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Romanian physician and biologist (d. 1926)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Victor_<PERSON>%C8%99\" title=\"<PERSON>\"><PERSON></a>, Romanian physician and biologist (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victor_Babe%C8%99\" title=\"<PERSON>\"><PERSON></a>, Romanian physician and biologist (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_Babe%C8%99"}]}, {"year": "1868", "text": "<PERSON>, American astronomer and academic (d. 1921)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Canadian runner and soldier (d. 1949)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Longboat\"><PERSON></a>, Canadian runner and soldier (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Longboat\"><PERSON></a>, Canadian runner and soldier (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, English engineer (d. 1955)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, American lawyer and politician, 30th President of the United States (d. 1933)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 30th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1874", "text": "<PERSON>, Australian journalist and politician, 27th Premier of Tasmania (d. 1952)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1952)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1880", "text": "<PERSON>, Austrian philosopher from the Vienna Circle (d. 1975)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_<PERSON>raft"}]}, {"year": "1881", "text": "<PERSON>, American general (d. 1968)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a>, American general (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American general (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, American sculptor, cartoonist, and engineer (d. 1970)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American sculptor, cartoonist, and engineer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American sculptor, cartoonist, and engineer (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Italian engineer and businessman (d. 1965)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pio <PERSON>\"><PERSON><PERSON></a>, Italian engineer and businessman (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Pio Pi<PERSON>\"><PERSON><PERSON></a>, Italian engineer and businessman (d. 1965)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pio_Pion"}]}, {"year": "1888", "text": "<PERSON>, Italian-American actor and singer (d. 1945)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor and singer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actor and singer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American songwriter and composer (d. 1996)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Caesar\"><PERSON></a>, American songwriter and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Chinese journalist, author, and critic (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>\"><PERSON></a>, Chinese journalist, author, and critic (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Du<PERSON>\"><PERSON></a>, Chinese journalist, author, and critic (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Indian activist (d. 1924)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uri_Sitara<PERSON>_Raju"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Puerto Rican-American historian and activist (d. 1997)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American historian and activist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American historian and activist (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, British actress, singer, and dancer (d. 1952)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress, singer, and dancer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress, singer, and dancer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician (d. 1998)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_<PERSON>da\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_<PERSON>da\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON><PERSON>_Nanda"}]}, {"year": "1898", "text": "<PERSON>, American supercentenarian (d. 2015)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American supercentenarian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American supercentenarian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Indigenous Australian who was one of the Stolen Generation, reunited with family aged 107 (d. 2007)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indigenous Australian who was one of the <a href=\"https://wikipedia.org/wiki/Stolen_Generation\" class=\"mw-redirect\" title=\"Stolen Generation\">Stolen Generation</a>, reunited with family aged 107 (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indigenous Australian who was one of the <a href=\"https://wikipedia.org/wiki/Stolen_Generation\" class=\"mw-redirect\" title=\"Stolen Generation\">Stolen Generation</a>, reunited with family aged 107 (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Stolen Generation", "link": "https://wikipedia.org/wiki/Stolen_Generation"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American folk artist (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American folk artist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American folk artist (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American gangster (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American actor and politician (d. 1992)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and politician (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Belgian organist, composer, and educator (d. 1986)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor_<PERSON>\" title=\"<PERSON>lor Pee<PERSON>\"><PERSON><PERSON></a>, Belgian organist, composer, and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lor_<PERSON>\" title=\"<PERSON>lor Pee<PERSON>\"><PERSON><PERSON></a>, Belgian organist, composer, and educator (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flor_Peeters"}]}, {"year": "1904", "text": "<PERSON>, English actress (d. 1976)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American sailor and author (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, 2nd Baron <PERSON>, British diplomat and public servant (d. 1996)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, British diplomat and public servant (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, British diplomat and public servant (d. 1996)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American critic, essayist, short story writer, and educator (d. 1975)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, short story writer, and educator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American critic, essayist, short story writer, and educator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lling"}]}, {"year": "1906", "text": "<PERSON>, American chemist and meteorologist (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and meteorologist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and meteorologist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American discus thrower (d. 1948)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(discus_thrower)\" title=\"<PERSON> (discus thrower)\"><PERSON></a>, American discus thrower (d. 1948)", "links": [{"title": "<PERSON> (discus thrower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(discus_thrower)"}]}, {"year": "1907", "text": "<PERSON>, American author and critic (d. 1996)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Welsh composer, pianist and satirist (d. 1963)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh composer, pianist and satirist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh composer, pianist and satirist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American sociologist and scholar (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and scholar (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and scholar (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 2010)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Australian public servant (d. 1989)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)\" title=\"<PERSON> (public servant)\"><PERSON></a>, Australian public servant (d. 1989)", "links": [{"title": "<PERSON> (public servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(public_servant)"}]}, {"year": "1911", "text": "<PERSON>, American singer and producer (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Alaskan-American civil rights activist (d. 1958)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alaskan-American civil rights activist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alaskan-American civil rights activist (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Italian automobile designer (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian automobile designer (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American actor and singer-songwriter (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and singer-songwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American typist and broadcaster (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American typist and broadcaster (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_D%27Aquino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American typist and broadcaster (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iva_Toguri_D%27Aquino"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American journalist and radio host (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and radio host (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and radio host (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American race car driver (d. 1984)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American race car driver (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "King <PERSON><PERSON><PERSON><PERSON><PERSON> of Tonga, (d. 2006)", "html": "1918 - King <a href=\"https://wikipedia.org/wiki/Taufa%27ahau_Tupou_IV\" class=\"mw-redirect\" title=\"Taufa'ahau Tupou IV\">Taufa'ahau Tupou IV</a> of Tonga, (d. 2006)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Taufa%27ahau_Tupou_IV\" class=\"mw-redirect\" title=\"Taufa'ahau Tupou IV\">Taufa'ahau Tupou IV</a> of Tonga, (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taufa%27ahau_Tupou_IV"}]}, {"year": "1918", "text": "<PERSON>, English cricketer (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English cricketer (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American journalist and radio host, created <PERSON> (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dear <PERSON>\">Dear <PERSON></a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and radio host, created <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dear <PERSON>\">Dear <PERSON></a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dear Abby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American basketball player and referee (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Drucker\" title=\"<PERSON><PERSON> Drucker\"><PERSON><PERSON></a>, American basketball player and referee (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Drucker\" title=\"<PERSON><PERSON> Drucker\"><PERSON><PERSON></a>, American basketball player and referee (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Drucker"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American businesswoman (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businesswoman (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German footballer and manager (d. 1977)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American politician (d. 2019)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, French economist and mathematician, Nobel Prize laureate (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_<PERSON>u\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_De<PERSON>u"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Iranian sports shooter", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian sports shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian sports shooter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1921", "text": "<PERSON> of Asyut (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Metropolitan_<PERSON>_of_Asyut\" title=\"Metropolitan Mikhail of Asyut\">Metropolitan <PERSON> of Asyut</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metropolitan_<PERSON>_of_Asyut\" title=\"Metropolitan Mikhail of Asyut\">Metropolitan <PERSON> of Asyut</a> (d. 2014)", "links": [{"title": "<PERSON> of Asyut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Asyu<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actor, playwright, and producer (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatrical_producer)\" title=\"<PERSON> (theatrical producer)\"><PERSON></a>, American actor, playwright, and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatrical_producer)\" title=\"<PERSON> (theatrical producer)\"><PERSON></a>, American actor, playwright, and producer (d. 2011)", "links": [{"title": "<PERSON> (theatrical producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatrical_producer)"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Hungarian violinist and conductor (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(violinist)\" title=\"<PERSON><PERSON><PERSON> (violinist)\"><PERSON><PERSON><PERSON></a>, Hungarian violinist and conductor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(violinist)\" title=\"<PERSON><PERSON><PERSON> (violinist)\"><PERSON><PERSON><PERSON></a>, Hungarian violinist and conductor (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON> (violinist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(violinist)"}]}, {"year": "1922", "text": "<PERSON><PERSON> <PERSON>, American politician (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American politician (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Swiss lawyer and politician (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Cuban author and screenwriter (d. 2021)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Del<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American military officer and fighter pilot (d. 2025)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American military officer and fighter pilot (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American military officer and fighter pilot (d. 2025)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Slovene poet, writer, translator, journalist and politician (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Ciril_Zlobec\" title=\"<PERSON><PERSON><PERSON> Zlobec\"><PERSON><PERSON><PERSON></a>, Slovene poet, writer, translator, journalist and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>iril_Zlobec\" title=\"<PERSON><PERSON><PERSON> Zlobec\"><PERSON><PERSON><PERSON></a>, Slovene poet, writer, translator, journalist and politician (d. 2018)", "links": [{"title": "Ciril Zlobec", "link": "https://wikipedia.org/wiki/Ciril_Zlobec"}]}, {"year": "1925", "text": "<PERSON>, American tennis player (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>node\" title=\"<PERSON> Knode\"><PERSON></a>, American tennis player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>node\" title=\"<PERSON> Knode\"><PERSON></a>, American tennis player (d. 2015)", "links": [{"title": "<PERSON> Knode", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Knode"}]}, {"year": "1926", "text": "<PERSON>, Argentinian-Spanish footballer and coach (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9fano\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish footballer and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9fano\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Spanish footballer and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_St%C3%A9fano"}]}, {"year": "1926", "text": "<PERSON>, American race car driver and businessman (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Underwood\" title=\"Lake Underwood\"><PERSON></a>, American race car driver and businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Underwood\" title=\"Lake Underwood\"><PERSON></a>, American race car driver and businessman (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Underwood"}]}, {"year": "1927", "text": "<PERSON>, Italian actress and photographer (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and photographer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress and photographer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American playwright and screenwriter (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer and politician (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer and politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino politician; 11th Vice President of the Philippines", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON>.</a>, Filipino politician; 11th Vice President of the Philippines", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON>.</a>, Filipino politician; 11th Vice President of the Philippines", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>of<PERSON><PERSON>_<PERSON><PERSON>_Jr."}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Syrian Army Officer (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian Army Officer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian Army Officer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Sri Lankan physician and academic (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan physician and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Shan <PERSON>\"><PERSON></a>, Sri Lankan physician and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player and manager (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American football player, coach, and manager (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Davis"}]}, {"year": "1929", "text": "<PERSON>, American baseball player (d. 1998)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American businessman (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Northern Ireland-born American actor (d. 1977)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Ireland-born American actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Ireland-born American actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American football player and soldier (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, French author, director, and screenwriter (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author, director, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French author, director, and screenwriter (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English cricketer (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 2017)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian runner (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Aur%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian runner (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aur%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian runner (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aur%C3%A8le_<PERSON>sche"}]}, {"year": "1934", "text": "<PERSON>, American academic and politician (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor and screenwriter (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1935", "text": "<PERSON>, Grenadian politician, 2nd Governor-General of Grenada (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_Grenada\" title=\"Governor-General of Grenada\">Governor-General of Grenada</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor-General_of_Grenada\" title=\"Governor-General of Grenada\">Governor-General of Grenada</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-General of Grenada", "link": "https://wikipedia.org/wiki/Governor-General_of_Grenada"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish soprano and actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Zdzis%C5%82awa_Donat\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Donat\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zdzis%C5%82awa_Donat\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Donat\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish soprano and actress", "links": [{"title": "Zdzisława Donat", "link": "https://wikipedia.org/wiki/Zdzis%C5%82awa_Donat"}]}, {"year": "1937", "text": "<PERSON>, American philosopher and academic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "Queen <PERSON><PERSON> of Norway", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Norway\" title=\"Queen <PERSON><PERSON> of Norway\">Queen <PERSON><PERSON> of Norway</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Norway\" title=\"Queen <PERSON><PERSON> of Norway\">Queen <PERSON><PERSON> of Norway</a>", "links": [{"title": "Queen <PERSON><PERSON> of Norway", "link": "https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Norway"}]}, {"year": "1937", "text": "<PERSON>, American journalist and historian", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Australian journalist (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian journalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(newsreader)\" title=\"<PERSON> (newsreader)\"><PERSON></a>, Australian journalist (d. 2010)", "links": [{"title": "<PERSON> (newsreader)", "link": "https://wikipedia.org/wiki/<PERSON>_(newsreader)"}]}, {"year": "1938", "text": "<PERSON>, English biologist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and producer (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian ice hockey player (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2020)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1941", "text": "<PERSON>, American politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Croatian-Slovenian poet and academic (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Toma%C5%BE_%C5%A0alamun\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Slovenian poet and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toma%C5%BE_%C5%A0alamun\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Slovenian poet and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toma%C5%BE_%C5%A0alamun"}]}, {"year": "1941", "text": "<PERSON>, Czech singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1%C4%8D<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Czech singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1%C4%8D<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Czech singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_Sedl%C3%A1%C4%8D<PERSON>_(musician)"}]}, {"year": "1941", "text": "<PERSON>, American soldier, lawyer, and activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American football player and coach (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Little\"><PERSON></a>, American football player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Little\"><PERSON></a>, American football player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French-Polish academic and politician, Polish Minister of Foreign Affairs (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish academic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish academic and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)\" title=\"Ministry of Foreign Affairs (Poland)\">Polish Minister of Foreign Affairs</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Poland)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Poland)"}]}, {"year": "1942", "text": "Prince <PERSON> Kent", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Kent\" title=\"Prince <PERSON> of Kent\">Prince <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Kent\" title=\"Prince <PERSON> of Kent\">Prince <PERSON></a>", "links": [{"title": "Prince <PERSON> Kent", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Kent"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, German trombonist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German trombonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German trombonist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and sportscaster", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English historian, author, and photographer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American lawyer, journalist, and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer, journalist, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American jazz and funk trombonist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Jazz\" title=\"Jazz\">jazz</a> and <a href=\"https://wikipedia.org/wiki/Funk\" title=\"Funk\">funk</a> <a href=\"https://wikipedia.org/wiki/Trombonist\" class=\"mw-redirect\" title=\"Trombonist\">trombonist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Jazz\" title=\"Jazz\">jazz</a> and <a href=\"https://wikipedia.org/wiki/Funk\" title=\"Funk\">funk</a> <a href=\"https://wikipedia.org/wiki/Trombonist\" class=\"mw-redirect\" title=\"Trombonist\">trombonist</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jazz", "link": "https://wikipedia.org/wiki/Jazz"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Funk"}, {"title": "Trombonist", "link": "https://wikipedia.org/wiki/Trombonist"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1970)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1970)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON>, Romanian-Israeli fencer and coach (d. 1972)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli fencer and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-Israeli fencer and coach (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American author and activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American businessman and philanthropist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Estonian actor and director (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Lembit_Ulfsak\" title=\"Lembit Ulfsak\"><PERSON><PERSON><PERSON></a>, Estonian actor and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lembit_Ulfsak\" title=\"Lembit Ulfsak\"><PERSON><PERSON><PERSON></a>, Estonian actor and director (d. 2017)", "links": [{"title": "Lembit Ulfsak", "link": "https://wikipedia.org/wiki/Lembit_Ulfsak"}]}, {"year": "1948", "text": "<PERSON>, French race car driver", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Swedish singer and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Tommy_K%C3%B6rberg\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_K%C3%B6rberg\" title=\"<PERSON>\"><PERSON></a>, Swedish singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tommy_K%C3%B6rberg"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English basketball player and swimmer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English basketball player and swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English basketball player and swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian-English radio and television host", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian tennis player and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian tennis player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian tennis player and politician", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}]}, {"year": "1951", "text": "<PERSON>, American R&B drummer and percussionist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B drummer and percussionist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B drummer and percussionist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1951", "text": "<PERSON>, Romanian-American political scientist, sociologist, and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Vladimir_Tism%C4%83neanu\" title=\"<PERSON>\"><PERSON></a>, Romanian-American political scientist, sociologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_Tism%C4%83neanu\" title=\"<PERSON>\"><PERSON></a>, Romanian-American political scientist, sociologist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Tism%C4%83neanu"}]}, {"year": "1951", "text": "<PERSON>, American lawyer and politician, 6th Lieutenant Governor of Maryland", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Maryland\" title=\"Lieutenant Governor of Maryland\">Lieutenant Governor of Maryland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Maryland\" title=\"Lieutenant Governor of Maryland\">Lieutenant Governor of Maryland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Maryland", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Maryland"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Colombian lawyer and politician, 39th President of Colombia", "html": "1952 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian lawyer and politician, 39th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1952", "text": "<PERSON>, English actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author and activist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English lawyer and politician, Minister for the Cabinet Office", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for the Cabinet Office", "link": "https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office"}]}, {"year": "1954", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American model, actress, and dancer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morganna"}]}, {"year": "1954", "text": "<PERSON><PERSON>, 21st Chief of Naval Staff of the Indian Navy", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 21st <a href=\"https://wikipedia.org/wiki/Chief_of_Naval_Staff_(India)\" class=\"mw-redirect\" title=\"Chief of Naval Staff (India)\">Chief of Naval Staff</a> of the <a href=\"https://wikipedia.org/wiki/Indian_Navy\" title=\"Indian Navy\">Indian Navy</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 21st <a href=\"https://wikipedia.org/wiki/Chief_of_Naval_Staff_(India)\" class=\"mw-redirect\" title=\"Chief of Naval Staff (India)\">Chief of Naval Staff</a> of the <a href=\"https://wikipedia.org/wiki/Indian_Navy\" title=\"Indian Navy\">Indian Navy</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief of Naval Staff (India)", "link": "https://wikipedia.org/wiki/Chief_of_Naval_Staff_(India)"}, {"title": "Indian Navy", "link": "https://wikipedia.org/wiki/Indian_Navy"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Finnish politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4luoma\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4luoma\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_Hein%C3%A4luoma"}]}, {"year": "1955", "text": "<PERSON>, Australian cyclist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, British academic and educator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British academic and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Estonian politician and diplomat, 25th Estonian Minister of Foreign Affairs", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician and diplomat, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician and diplomat, 25th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1958", "text": "<PERSON>, Greenlandic Ombudsman", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic Ombudsman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greenlandic Ombudsman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian guitarist, saxophonist, and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist, saxophonist, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian guitarist, saxophonist, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_Pengilly"}]}, {"year": "1958", "text": "<PERSON>, Canadian soccer player, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Spanish actress and singer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Victoria_Abril\" title=\"Victoria Abril\"><PERSON></a>, Spanish actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Abril\" title=\"Victoria Abril\"><PERSON></a>, Spanish actress and singer", "links": [{"title": "Victoria Abril", "link": "https://wikipedia.org/wiki/Victoria_Abril"}]}, {"year": "1960", "text": "<PERSON>, Austrian race car driver (d. 1994)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English-American video game designer, created the Ultima series", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Ultima_(series)\" title=\"Ultima (series)\">Ultima series</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Ultima_(series)\" title=\"Ultima (series)\">Ultima series</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ultima (series)", "link": "https://wikipedia.org/wiki/Ultima_(series)"}]}, {"year": "1962", "text": "<PERSON>, American tennis player and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pam_<PERSON>ver"}]}, {"year": "1963", "text": "<PERSON>, French tennis player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Spanish-Venezuelan political scientist and journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Laureano_M%C3%A1rquez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Venezuelan political scientist and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lau<PERSON>no_M%C3%A1rquez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-Venezuelan political scientist and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laureano_M%C3%A1rquez"}]}, {"year": "1963", "text": "<PERSON>, Puerto Rican-American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Haitian-Dominican human rights activist (d. 2011)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian-Dominican human rights activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian-Dominican human rights activist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American soccer player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>man"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Lebanese fashion designer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Albanian politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edi_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ghter"}]}, {"year": "1964", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>ria<PERSON>_Karataidis\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ria<PERSON>_<PERSON>taidis\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kiriakos_Karataidis"}]}, {"year": "1965", "text": "<PERSON><PERSON>, English actor and playwright", "html": "1965 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Scottish actress and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish actress and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German-Greek footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Minas_Hantzidis\" title=\"<PERSON>\"><PERSON></a>, German-Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minas_Hantzidis\" title=\"<PERSON> Hantzidis\"><PERSON></a>, German-Greek footballer", "links": [{"title": "Minas <PERSON>", "link": "https://wikipedia.org/wiki/Minas_Hantzidis"}]}, {"year": "1966", "text": "<PERSON>, American actor (d. 2016)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Mexican baseball player and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cast<PERSON>\" title=\"<PERSON>ny Castilla\"><PERSON><PERSON></a>, Mexican baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Castilla\" title=\"<PERSON>ny Castilla\"><PERSON><PERSON></a>, Mexican baseball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ny_Castilla"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON>, French athlete", "html": "1967 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French athlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American football player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Al_Golden\" title=\"Al Golden\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Golden\" title=\"Al Golden\"><PERSON></a>, American football player and coach", "links": [{"title": "Al Golden", "link": "https://wikipedia.org/wiki/Al_Golden"}]}, {"year": "1969", "text": "<PERSON>, American football player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Zimbabwean footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian canoe racer and engineer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian canoe racer and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian canoe racer and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian-American ice hockey player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Japanese musician, singer, songwriter, record producer and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Gackt\" title=\"Gack<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese musician, singer, songwriter, record producer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gackt\" title=\"Gack<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese musician, singer, songwriter, record producer and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gackt"}]}, {"year": "1973", "text": "<PERSON>, English-Jamaican footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1973)\" title=\"<PERSON> (footballer, born 1973)\"><PERSON></a>, English-Jamaican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1973)\" title=\"<PERSON> (footballer, born 1973)\"><PERSON></a>, English-Jamaican footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1973)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1973)"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian ice dancer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"An<PERSON><PERSON><PERSON> Krylova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"An<PERSON><PERSON><PERSON> Krylova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice dancer and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anjelik<PERSON>_Krylova"}]}, {"year": "1973", "text": "<PERSON>, Danish race car driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American tennis player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/La%27R<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/La%27R<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/La%27R<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese motorcycle racer (d. 2003)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese motorcycle racer (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian skier", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Yevgen<PERSON>_<PERSON>eva-Arbuzova\" class=\"mw-redirect\" title=\"Ye<PERSON><PERSON><PERSON>dvedeva-Arbuzova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON><PERSON>_<PERSON>-Arbuzova\" class=\"mw-redirect\" title=\"Ye<PERSON><PERSON><PERSON>dvedeva-Arbuzova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian skier", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-Arbuzova", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Brazilian tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Mpenza"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Estonian politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Venezuelan footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Bermudan footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON>eed<PERSON>\"><PERSON><PERSON><PERSON></a>, Bermudan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>eed<PERSON>\"><PERSON><PERSON><PERSON></a>, Bermudan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wa<PERSON>_<PERSON>e"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Angolan footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ded%C3%A9_(Angolan_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Angolan footballer)\"><PERSON><PERSON><PERSON></a>, Angolan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ded%C3%A9_(Angolan_footballer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (Angolan footballer)\"><PERSON><PERSON><PERSON></a>, Angolan footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (Angolan footballer)", "link": "https://wikipedia.org/wiki/Ded%C3%A9_(Angolan_footballer)"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Brock_<PERSON>\" title=\"Brock Berlin\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brock_Berlin\" title=\"Brock Berlin\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brock_Berlin"}]}, {"year": "1981", "text": "<PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C3%9F"}]}, {"year": "1981", "text": "<PERSON>, Dominican baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Francisco_Cruceta\" title=\"Francisco Cruceta\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Cruceta\" title=\"Francisco Cruceta\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Cruceta"}]}, {"year": "1981", "text": "<PERSON>, American football player (d. 2016)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)\" title=\"<PERSON> (defensive end)\"><PERSON></a>, American football player (d. 2016)", "links": [{"title": "<PERSON> (defensive end)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_end)"}]}, {"year": "1982", "text": "<PERSON>, Georgian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Russian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Russian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Russian cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1982", "text": "<PERSON>, New Zealand rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1982", "text": "<PERSON> \"The Situation\" <PERSON><PERSON><PERSON>, American model, author and television personality", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22The_Situation%22_So<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON> \"The Situation\" Sorrentino'><PERSON> \"The Situation\" <PERSON><PERSON><PERSON></a>, American model, author and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22The_Situation%22_So<PERSON><PERSON>\" class=\"mw-redirect\" title='<PERSON> \"The Situation\" Sorrentino'><PERSON> \"The Situation\" <PERSON><PERSON><PERSON></a>, American model, author and television personality", "links": [{"title": "<PERSON> \"The Situation\" <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22The_Situation%22_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Botswanan sprinter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Amant<PERSON>_Montsho\" title=\"Amant<PERSON> Montsho\"><PERSON><PERSON><PERSON></a>, Botswanan sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amant<PERSON>_Montsho\" title=\"Amant<PERSON> Montsho\"><PERSON><PERSON><PERSON></a>, Botswanan sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amantle_Montsho"}]}, {"year": "1983", "text": "<PERSON>, Chilean footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Indian-English journalist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Japanese singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Timorese footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Timorese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Timorese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ace"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Colombian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Wason_Renter%C3%ADa\" title=\"Wason Rentería\"><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wason_Renter%C3%ADa\" title=\"Wason Rentería\"><PERSON><PERSON> Renter<PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON> Rentería", "link": "https://wikipedia.org/wiki/Wason_Renter%C3%ADa"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/%C3%96mer_A%C5%9F%C4%B1k\" title=\"Ömer Aşık\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96mer_A%C5%9F%C4%B1k\" title=\"Ömer Aşık\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON>mer A<PERSON>ı<PERSON>", "link": "https://wikipedia.org/wiki/%C3%96mer_A%C5%9F%C4%B1k"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Vietnamese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Salvadoran tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9valo\" title=\"<PERSON>\"><PERSON></a>, Salvadoran tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9valo\" title=\"<PERSON>\"><PERSON></a>, Salvadoran tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Ar%C3%A9valo"}]}, {"year": "1986", "text": "<PERSON>, Dutch footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Terrance Knighton\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Terra<PERSON> Knighton\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Ethiopian runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ayalew\"><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ayalew\"><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Georgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1988", "text": "<PERSON><PERSON>, French-Mexican actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Mexican actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Mexican actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Liechtensteiner footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liechtensteiner footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liechtensteiner footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benjamin_B%C3%BCchel"}]}, {"year": "1990", "text": "<PERSON>, American ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Ghanaian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Belarusian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Paraguayan footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Paraguayan footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/%C3%93sca<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American rapper, singer, songwriter and record producer ", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Malone\"><PERSON></a>, American rapper, singer, songwriter and record producer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Malone\"><PERSON></a>, American rapper, singer, songwriter and record producer ", "links": [{"title": "<PERSON> Malone", "link": "https://wikipedia.org/wiki/<PERSON>_Malone"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Japanese musician ", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese musician ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese musician ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Russian singer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "673", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, king of Kent", "html": "673 - <a href=\"https://wikipedia.org/wiki/Ecg<PERSON>ht_of_Kent\" title=\"Ecgber<PERSON> of Kent\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Kent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecg<PERSON>ht_of_Kent\" title=\"Ecgber<PERSON> of Kent\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Kent", "links": [{"title": "Ecgberht of Kent", "link": "https://wikipedia.org/wiki/Ecgberht_of_Kent"}]}, {"year": "907", "text": "<PERSON><PERSON><PERSON><PERSON>, margrave of Bavaria", "html": "907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Margrave_of_Bavaria\" title=\"<PERSON><PERSON><PERSON><PERSON>, Margrave of Bavaria\"><PERSON><PERSON><PERSON><PERSON></a>, margrave of <a href=\"https://wikipedia.org/wiki/Duchy_of_Bavaria\" title=\"Duchy of Bavaria\">Bavaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Margrave_of_Bavaria\" title=\"<PERSON><PERSON><PERSON><PERSON>, Margrave of Bavaria\"><PERSON><PERSON><PERSON><PERSON></a>, margrave of <a href=\"https://wikipedia.org/wiki/Duchy_of_Bavaria\" title=\"Duchy of Bavaria\">Bavaria</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Margrave of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON><PERSON>_of_Bavaria"}, {"title": "Duchy of Bavaria", "link": "https://wikipedia.org/wiki/Duchy_of_Bavaria"}]}, {"year": "907", "text": "<PERSON><PERSON>, archbishop of Salzburg", "html": "907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Salzburg)\" title=\"<PERSON><PERSON> (archbishop of Salzburg)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Salzburg\" title=\"Roman Catholic Archdiocese of Salzburg\">Salzburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Salzburg)\" title=\"<PERSON><PERSON> (archbishop of Salzburg)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Salzburg\" title=\"Roman Catholic Archdiocese of Salzburg\">Salzburg</a>", "links": [{"title": "<PERSON><PERSON> (archbishop of Salzburg)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(archbishop_of_Salzburg)"}, {"title": "Roman Catholic Archdiocese of Salzburg", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Salzburg"}]}, {"year": "910", "text": "<PERSON><PERSON>, Chinese warlord (b. 877)", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese warlord (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese warlord (b. 877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "940", "text": "<PERSON>, Chinese general (b. 871)", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "943", "text": "<PERSON><PERSON><PERSON> of Goryeo, Korean king (b. 877)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ta<PERSON>o_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 877)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Taejo_of_Goryeo"}]}, {"year": "945", "text": "<PERSON><PERSON>, Chinese Buddhist monk and emperor", "html": "945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Bhikkhu\" title=\"<PERSON>hikkhu\">Buddhist monk</a> and emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese <a href=\"https://wikipedia.org/wiki/Bhikkhu\" title=\"<PERSON>hikkhu\">Buddhist monk</a> and emperor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bhikkhu"}]}, {"year": "965", "text": "<PERSON>, pope of the Catholic Church", "html": "965 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\"><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "973", "text": "<PERSON> of Augsburg, German bishop and saint (b. 890)", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Augsburg\" title=\"<PERSON> of Augsburg\"><PERSON> of Augsburg</a>, German bishop and saint (b. 890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Augsburg\" title=\"<PERSON> of Augsburg\"><PERSON> of Augsburg</a>, German bishop and saint (b. 890)", "links": [{"title": "<PERSON> of Augsburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Augsburg"}]}, {"year": "975", "text": "<PERSON><PERSON><PERSON> of Goryeo, Korean king (b. 925)", "html": "975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Goryeo\" title=\"<PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (b. 925)", "links": [{"title": "<PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Gwangjong_of_Goryeo"}]}, {"year": "1187", "text": "<PERSON><PERSON><PERSON> of Châtillon, French knight (b. 1125)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/Raynald_of_Ch%C3%A2tillon\" title=\"<PERSON><PERSON><PERSON> of Châtillon\"><PERSON><PERSON><PERSON> of Châtillon</a>, French knight (b. 1125)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ray<PERSON><PERSON>_of_Ch%C3%A2tillon\" title=\"<PERSON><PERSON><PERSON> of Châtillon\"><PERSON><PERSON><PERSON> of Châtillon</a>, French knight (b. 1125)", "links": [{"title": "<PERSON><PERSON><PERSON> of Châtillon", "link": "https://wikipedia.org/wiki/Raynald_of_Ch%C3%A2tillon"}]}, {"year": "1307", "text": "<PERSON> of Bohemia (b. 1281)", "html": "1307 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1281)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1281)", "links": [{"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bohemia"}]}, {"year": "1336", "text": "<PERSON> of Portugal (b. 1271)", "html": "1336 - <PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Portugal\" title=\"Elizabeth of Portugal\"><PERSON> of Portugal</a> (b. 1271)", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Portugal\" title=\"Elizabeth of Portugal\"><PERSON> of Portugal</a> (b. 1271)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Elizabeth_of_Portugal"}]}, {"year": "1429", "text": "<PERSON>, ruler of Epirus (b. 1372)", "html": "1429 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ruler of Epirus (b. 1372)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, ruler of Epirus (b. 1372)", "links": [{"title": "Carlo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1533", "text": "<PERSON>, English priest, writer, and martyr (b. 1503)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest, writer, and martyr (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English priest, writer, and martyr (b. 1503)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1541", "text": "<PERSON>, Spanish general and explorer (b. 1495)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and explorer (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and explorer (b. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1546", "text": "<PERSON><PERSON><PERSON>, Ottoman admiral (b. 1478)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/Hayreddin_Barbarossa\" title=\"Hayreddin Barbarossa\"><PERSON><PERSON><PERSON> Barbarossa</a>, Ottoman admiral (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hayreddin_Barbarossa\" title=\"Hayreddin Barbarossa\"><PERSON><PERSON><PERSON> Barbarossa</a>, Ottoman admiral (b. 1478)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hayreddin_Barbarossa"}]}, {"year": "1551", "text": "<PERSON>, 1st Baron <PERSON>, English politician (b. 1514)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician (b. 1514)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1603", "text": "<PERSON>, Flemish composer and educator (b. 1521)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer and educator (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer and educator (b. 1521)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, English composer (b. c. 1540)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. c. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. c. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1644", "text": "<PERSON>, English academic, antiquarian and archivist (b. 1581)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic, antiquarian and archivist (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic, antiquarian and archivist (b. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, French missionary and saint, one of the eight Canadian Martyrs (b. 1601)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and saint, one of the eight <a href=\"https://wikipedia.org/wiki/Canadian_Martyrs\" title=\"Canadian Martyrs\">Canadian Martyrs</a> (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and saint, one of the eight <a href=\"https://wikipedia.org/wiki/Canadian_Martyrs\" title=\"Canadian Martyrs\">Canadian Martyrs</a> (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canadian Martyrs", "link": "https://wikipedia.org/wiki/Canadian_Martyrs"}]}, {"year": "1742", "text": "<PERSON>, Italian monk, mathematician, and engineer (b. 1671)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, mathematician, and engineer (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, mathematician, and engineer (b. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, French playwright and author (b. 1680)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ricault_Destouches\" title=\"<PERSON>\"><PERSON></a>, French playwright and author (b. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ricault_Destouches\" title=\"<PERSON>uche<PERSON>\"><PERSON></a>, French playwright and author (b. 1680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Philippe_N%C3%A9<PERSON>ult_Destouches"}]}, {"year": "1761", "text": "<PERSON>, English author and painter (b. 1689)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and painter (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and painter (b. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1780", "text": "Prince <PERSON> of Lorraine (b. 1712)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of Lorraine\">Prince <PERSON> of Lorraine</a> (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Lorraine\" title=\"Prince <PERSON> of Lorraine\">Prince <PERSON> of Lorraine</a> (b. 1712)", "links": [{"title": "Prince <PERSON> of Lorraine", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Lorraine"}]}, {"year": "1787", "text": "<PERSON>, Prince of Soubise, Marshal of France (b. 1715)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Soubise\" title=\"<PERSON>, Prince of Soubise\"><PERSON>, Prince of Soubise</a>, Marshal of France (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Soubise\" title=\"<PERSON>, Prince of Soubise\"><PERSON>, Prince of Soubise</a>, Marshal of France (b. 1715)", "links": [{"title": "<PERSON>, Prince of Soubise", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1821", "text": "<PERSON>, English painter and academic (b. 1742)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, American lawyer and politician, 2nd President of the United States (b. 1735)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1826", "text": "<PERSON>, American architect, lawyer, and politician, 3rd President of the United States (b. 1743)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, lawyer, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1831", "text": "<PERSON>, American soldier, lawyer, and politician, 5th President of the United States (b. 1758)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Monroe\"><PERSON></a>, American soldier, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, French historian and politician (b. 1768)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Ren%C3%A9_de_<PERSON>d\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and politician (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois-Ren%C3%A9_de_<PERSON>d\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French historian and politician (b. 1768)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois-Ren%C3%A9_de_Chateaubriand"}]}, {"year": "1850", "text": "<PERSON>, English entomologist and author (b. 1759)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, English entomologist and author (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(entomologist)\" title=\"<PERSON> (entomologist)\"><PERSON></a>, English entomologist and author (b. 1759)", "links": [{"title": "<PERSON> (entomologist)", "link": "https://wikipedia.org/wiki/<PERSON>(entomologist)"}]}, {"year": "1854", "text": "<PERSON>, German academic and jurist (b. 1781)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and jurist (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and jurist (b. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, American lawyer, judge, and politician, 21st United States Secretary of State (b. 1786)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1881", "text": "<PERSON>, Finnish philosopher and politician (b. 1806)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish philosopher and politician (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish philosopher and politician (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American composer and author (b. 1797)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "Poundmaker, Canadian tribal chief (b. 1797)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Poundmaker\" title=\"Poundmaker\">Poundmaker</a>, Canadian tribal chief (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Poundmaker\" title=\"Poundmaker\">Poundmaker</a>, Canadian tribal chief (b. 1797)", "links": [{"title": "Poundmaker", "link": "https://wikipedia.org/wiki/Poundmaker"}]}, {"year": "1891", "text": "<PERSON>, American lawyer and politician, 15th Vice President of the United States (b. 1809)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1901", "text": "<PERSON>, German linguist and academic (b. 1843)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, German linguist and academic (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, German linguist and academic (b. 1843)", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)"}]}, {"year": "1902", "text": "<PERSON>, Indian monk and saint (b. 1863)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Swami_<PERSON><PERSON><PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and saint (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami_<PERSON><PERSON><PERSON>\" title=\"Swami <PERSON>\">Swami <PERSON></a>, Indian monk and saint (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, French geographer and author (b. 1830)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and author (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus\" title=\"<PERSON><PERSON><PERSON> Re<PERSON>lus\"><PERSON><PERSON><PERSON></a>, French geographer and author (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lis%C3%A9e_Reclus"}]}, {"year": "1910", "text": "<PERSON>, American lawyer and jurist, Chief Justice of the United States (b. 1833)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON> the <PERSON>, <PERSON><PERSON> (b. c. 1820)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Iroijlaplap\" title=\"Iroijlaplap\">iroij<PERSON>lap</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1820</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON> the Great</a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Iroijlaplap\" title=\"Iroijlaplap\">iroij<PERSON>lap</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1820</span>)", "links": [{"title": "<PERSON><PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Great"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>lap"}]}, {"year": "1910", "text": "<PERSON>, Italian astronomer and historian (b. 1835)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and historian (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and historian (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American soldier and poet (b. 1888)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and poet (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and poet (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, German lieutenant and pilot (b. 1894)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ofen\"><PERSON><PERSON></a>, German lieutenant and pilot (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>thofen\"><PERSON><PERSON></a>, German lieutenant and pilot (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Italian activist and saint (b. 1901)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist and saint (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist and saint (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, French-Polish physicist and chemist, Nobel Prize laureate (b. 1867)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Polish physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1935", "text": "<PERSON>, Norwegian opera singer and music teacher (b. 1856)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian opera singer and music teacher (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian opera singer and music teacher (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Austrian philosopher and politician, Austrian Minister of Foreign Affairs (b. 1881)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Europe,_Integration_and_Foreign_Affairs\" class=\"mw-redirect\" title=\"Federal Ministry for Europe, Integration and Foreign Affairs\">Austrian Minister of Foreign Affairs</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_for_Europe,_Integration_and_Foreign_Affairs\" class=\"mw-redirect\" title=\"Federal Ministry for Europe, Integration and Foreign Affairs\">Austrian Minister of Foreign Affairs</a> (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry for Europe, Integration and Foreign Affairs", "link": "https://wikipedia.org/wiki/Federal_Ministry_for_Europe,_Integration_and_Foreign_Affairs"}]}, {"year": "1938", "text": "<PERSON>, French tennis player (b. 1899)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Polish mathematician and academic (b. 1881)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%81omnicki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and academic (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%81omnicki\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mathematician and academic (b. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_%C5%81omnicki"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish general and politician, 9th Prime Minister of the Second Republic of Poland (b. 1881)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Si<PERSON>ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish general and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Second Republic of Poland</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Si<PERSON>ski\" title=\"W<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish general and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland\" class=\"mw-redirect\" title=\"List of Prime Ministers of Poland\">Prime Minister of the Second Republic of Poland</a> (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}, {"title": "List of Prime Ministers of Poland", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Poland"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Welsh footballer and coach (b. 1906)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27C<PERSON>agh<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh footballer and coach (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27C<PERSON>agh<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh footballer and coach (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ta<PERSON>_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Brazilian journalist and author (b. 1882)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Monte<PERSON>_Lobato\" title=\"<PERSON>iro Lobato\"><PERSON><PERSON></a>, Brazilian journalist and author (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Monte<PERSON>_Lobato\" title=\"Monteiro Lobato\"><PERSON><PERSON></a>, Brazilian journalist and author (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Monteiro_Lobato"}]}, {"year": "1949", "text": "<PERSON>, Dutch rower and engineer (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch rower and engineer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch rower and engineer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, 1st Baron <PERSON>, New Zealand general and politician, 7th Governor-General of New Zealand (b. 1889)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, New Zealand general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, New Zealand general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (b. 1889)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1963", "text": "<PERSON>, American activist and martyr (b. 1927)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and martyr (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and martyr (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Indian activist, designed the Flag of India (b. 1876)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Venkayya\" title=\"<PERSON>ali Venkayya\"><PERSON><PERSON> V<PERSON>kayya</a>, Indian activist, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_India\" title=\"Flag of India\">Flag of India</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_V<PERSON>kayya\" title=\"<PERSON><PERSON> Venkayya\"><PERSON><PERSON></a>, Indian activist, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_India\" title=\"Flag of India\">Flag of India</a> (b. 1876)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ping<PERSON>_<PERSON>kayya"}, {"title": "Flag of India", "link": "https://wikipedia.org/wiki/Flag_of_India"}]}, {"year": "1964", "text": "<PERSON><PERSON>, French actress and singer (b. 1893)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ay"}]}, {"year": "1969", "text": "<PERSON>, French director and screenwriter (b. 1890)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American painter and illustrator (b. 1905)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American sailor and businessman (b. 1884)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and businessman (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and businessman (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American anthologist and author (b. 1909)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American anthologist and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, American anthologist and author (b. 1909)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_Der<PERSON>h"}]}, {"year": "1971", "text": "<PERSON>, American admiral and politician (b. 1877)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, English author (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French actor (b. 1892)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Israeli colonel (b. 1946)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli colonel (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli colonel (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Polish poet and playwright (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Antoni_S%C5%82onimski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish poet and playwright (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antoni_S%C5%82onimski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish poet and playwright (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_S%C5%82onimski"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Ukrainian physicist and academic (b. 1918)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian physicist and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian physicist and academic (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Chinese footballer and manager (b. 1905)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Belgian linguist and author (b. 1895)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian linguist and author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian linguist and author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter (b. 1949)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, French musicologist, critique musical and physicist (b. 1933)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French musicologist, critique musical and physicist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French musicologist, critique musical and physicist (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Belgian organist and composer (b. 1903)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor_<PERSON>ee<PERSON>\" title=\"<PERSON>lor Peeters\"><PERSON><PERSON></a>, Belgian organist and composer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lor_<PERSON>\" title=\"<PERSON>lor Pee<PERSON>\"><PERSON><PERSON></a>, Belgian organist and composer (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flor_Peeters"}]}, {"year": "1986", "text": "<PERSON>, Belarusian-American mathematician and academic (b. 1899)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American mathematician and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-American mathematician and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American wrestler (b. 1954)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American journalist and author (b. 1924)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Chinese-Australian surgeon and physician (b. 1936)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian surgeon and physician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Australian surgeon and physician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American cartoonist (b. 1920)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Art_Sansom\" title=\"Art Sansom\"><PERSON></a>, American cartoonist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Sansom\" title=\"Art Sansom\"><PERSON></a>, American cartoonist (b. 1920)", "links": [{"title": "Art Sansom", "link": "https://wikipedia.org/wiki/Art_Sansom"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Argentinian bandoneon player and composer (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>tor Piazzolla\"><PERSON><PERSON></a>, Argentinian <a href=\"https://wikipedia.org/wiki/Bandoneon\" title=\"Bandoneon\">bandoneon</a> player and composer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Piazzolla\"><PERSON><PERSON></a>, Argentinian <a href=\"https://wikipedia.org/wiki/Bandoneon\" title=\"Bandoneon\">bandoneon</a> player and composer (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Astor_<PERSON>lla"}, {"title": "Bandoneon", "link": "https://wikipedia.org/wiki/Bandoneon"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Canadian historian, genealogist, and politician (b. 1903)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, genealogist, and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian, genealogist, and politician (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American wrestling referee (b. 1964)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling referee (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling referee (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Hungarian-American actress and singer (b. 1919)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress and singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actress and singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American painter and television host (b. 1942)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and television host (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and television host (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American journalist (b. 1934)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English zoologist and neurophysiologist (b. 1907)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and neurophysiologist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English zoologist and neurophysiologist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American illustrator and educator (b. 1917)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leo Garel\"><PERSON></a>, American illustrator and educator (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leo Garel\"><PERSON></a>, American illustrator and educator (b. 1917)", "links": [{"title": "Leo Garel", "link": "https://wikipedia.org/wiki/Leo_Garel"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, Polish journalist and author (b. 1919)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ling-Grudzi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ling-Grud<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>taw_Herling-Grudzi%C5%84ski"}]}, {"year": "2002", "text": "<PERSON>, Canadian organist and composer (b. 1919)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian organist and composer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian organist and composer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Jr., American general (b. 1912)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American general (b. 1912)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "2003", "text": "<PERSON>, American author and radio host (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and radio host (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French singer (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter, pianist, and producer (b. 1944)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Swiss violinist and conductor (b. 1920)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss violinist and conductor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss violinist and conductor (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian ice hockey player (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Go<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American football player and coach (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American author and poet (b. 1940)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American politician (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1916)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American football player (b. 1980)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, German-English soldier and journalist (b. 1923)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, German-English soldier and journalist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, German-English soldier and journalist (b. 1923)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>(journalist)"}]}, {"year": "2009", "text": "<PERSON>, American actress (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "2009", "text": "<PERSON>, American businessman and talent agent, founded ABKCO Records (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and talent agent, founded <a href=\"https://wikipedia.org/wiki/ABKCO_Records\" title=\"ABKCO Records\">ABKCO Records</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and talent agent, founded <a href=\"https://wikipedia.org/wiki/ABKCO_Records\" title=\"ABKCO Records\">ABKCO Records</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "ABKCO Records", "link": "https://wikipedia.org/wiki/ABKCO_Records"}]}, {"year": "2009", "text": "<PERSON>, American guitarist (b. 1946)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American football player (b. 1973)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Swedish author and actor (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish author and actor (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasse_Str%C3%B6mstedt"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Congolese poet and politician (b. 1938)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese poet and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese poet and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American physician and author (b. 1927)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian poet and author (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and author (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American boxer (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, South Korean footballer (b. 1987)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyeong\" title=\"<PERSON><PERSON>-hyeong\"><PERSON><PERSON></a>, South Korean footballer (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyeong\" title=\"<PERSON><PERSON>hyeong\"><PERSON><PERSON></a>, South Korean footballer (b. 1987)", "links": [{"title": "<PERSON><PERSON>ong", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-hyeong"}]}, {"year": "2012", "text": "<PERSON>, English actor, director, and screenwriter (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Welsh rugby player and sportscaster (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Onllwyn_Brace\" title=\"Onllwyn Brace\"><PERSON><PERSON><PERSON></a>, Welsh rugby player and sportscaster (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onllwyn_Brace\" title=\"Onllwyn Brace\"><PERSON><PERSON><PERSON></a>, Welsh rugby player and sportscaster (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brace"}]}, {"year": "2013", "text": "<PERSON>, English footballer and manager (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American dermatologist and academic (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dermatologist)\" title=\"<PERSON> (dermatologist)\"><PERSON></a>, American dermatologist and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dermatologist)\" title=\"<PERSON> (dermatologist)\"><PERSON></a>, American dermatologist and academic (b. 1940)", "links": [{"title": "<PERSON> (dermatologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dermatologist)"}]}, {"year": "2013", "text": "<PERSON>, American general (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish singer (b. 1960)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Italian author, screenwriter, and actor (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author, screenwriter, and actor (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and critic (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (writer)\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and critic (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON> <PERSON><PERSON> (writer)\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and critic (b. 1951)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1936)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2014", "text": "<PERSON>, American businessman (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Bulgarian judge and politician (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian judge and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian judge and politician (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American historian, author, and academic (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Iranian film director, screenwriter, poet, and photographer (b. 1940)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian film director, screenwriter, poet, and photographer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian film director, screenwriter, poet, and photographer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American R&B, funk, and jazz drummer (b. 1973)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B, funk, and jazz drummer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American R&amp;B, funk, and jazz drummer (b. 1973)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Soviet and Russian author (b. 1919)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet and Russian author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet and Russian author (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American MLB player and NBA player (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MLB player and NBA player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MLB player and NBA player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Belgian footballer (b. 1927)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Dutch cinematographer (b. 1940)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>by_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cinematographer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>by_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cinematographer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Robby_M%C3%BCller"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Indonesian politician, former parliament speaker and government minister (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian politician, former parliament speaker and government minister (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian politician, former parliament speaker and government minister (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, Latvian ice hockey goaltender (b. 1996)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Mat%C4%ABss_Kivlenieks\" title=\"<PERSON><PERSON><PERSON>iek<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey goaltender (b. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mat%C4%ABss_Kivlenieks\" title=\"<PERSON><PERSON><PERSON>iek<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian ice hockey goaltender (b. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mat%C4%ABss_Kivlenieks"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Brazilian prelate of the Catholic Church (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A1udio_Hummes\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian prelate of the Catholic Church (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A1udio_Hummes\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian prelate of the Catholic Church (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A1udio_Hummes"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Japanese manga artist (b. 1961)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}