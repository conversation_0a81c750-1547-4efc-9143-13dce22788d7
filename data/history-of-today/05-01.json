{"date": "May 1", "url": "https://wikipedia.org/wiki/May_1", "data": {"Events": [{"year": "305", "text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> retire from the office of Roman emperor.", "html": "305 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> retire from the office of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>ian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> retire from the office of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}]}, {"year": "880", "text": "The Nea Ekklesia is inaugurated in Constantinople, setting the model for all later cross-in-square Orthodox churches.", "html": "880 - The <a href=\"https://wikipedia.org/wiki/Nea_Ekklesia\" title=\"Nea Ekklesia\">Nea Ekklesia</a> is inaugurated in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, setting the model for all later <a href=\"https://wikipedia.org/wiki/Cross-in-square\" title=\"Cross-in-square\">cross-in-square</a> Orthodox churches.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nea_Ekklesia\" title=\"Nea Ekklesia\">Nea Ekklesia</a> is inaugurated in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>, setting the model for all later <a href=\"https://wikipedia.org/wiki/Cross-in-square\" title=\"Cross-in-square\">cross-in-square</a> Orthodox churches.", "links": [{"title": "<PERSON>ea <PERSON>", "link": "https://wikipedia.org/wiki/Nea_Ekklesia"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Cross-in-square", "link": "https://wikipedia.org/wiki/Cross-in-square"}]}, {"year": "1169", "text": "Norman mercenaries land at Bannow Bay in Leinster, marking the beginning of the Norman invasion of Ireland.", "html": "1169 - <a href=\"https://wikipedia.org/wiki/Normans\" title=\"Normans\">Norman</a> mercenaries land at <a href=\"https://wikipedia.org/wiki/Bannow\" title=\"Bannow\">Bannow Bay</a> in <a href=\"https://wikipedia.org/wiki/Leinster\" title=\"Leinster\">Leinster</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Norman_invasion_of_Ireland\" class=\"mw-redirect\" title=\"Norman invasion of Ireland\">Norman invasion of Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Normans\" title=\"Normans\">Norman</a> mercenaries land at <a href=\"https://wikipedia.org/wiki/Bannow\" title=\"Bannow\">Bannow Bay</a> in <a href=\"https://wikipedia.org/wiki/Leinster\" title=\"Leinster\">Leinster</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Norman_invasion_of_Ireland\" class=\"mw-redirect\" title=\"Norman invasion of Ireland\">Norman invasion of Ireland</a>.", "links": [{"title": "Normans", "link": "https://wikipedia.org/wiki/Normans"}, {"title": "Bannow", "link": "https://wikipedia.org/wiki/Bannow"}, {"title": "Leinster", "link": "https://wikipedia.org/wiki/Leinster"}, {"title": "Norman invasion of Ireland", "link": "https://wikipedia.org/wiki/Norman_invasion_of_Ireland"}]}, {"year": "1328", "text": "Wars of Scottish Independence end: By the Treaty of Edinburgh-Northampton, England recognises Scotland as an independent state.", "html": "1328 - <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a> end: By the <a href=\"https://wikipedia.org/wiki/Treaty_of_Edinburgh%E2%80%93Northampton\" title=\"Treaty of Edinburgh-Northampton\">Treaty of Edinburgh-Northampton</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> recognises <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> as an <a href=\"https://wikipedia.org/wiki/Independence\" title=\"Independence\">independent state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a> end: By the <a href=\"https://wikipedia.org/wiki/Treaty_of_Edinburgh%E2%80%93Northampton\" title=\"Treaty of Edinburgh-Northampton\">Treaty of Edinburgh-Northampton</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> recognises <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> as an <a href=\"https://wikipedia.org/wiki/Independence\" title=\"Independence\">independent state</a>.", "links": [{"title": "Wars of Scottish Independence", "link": "https://wikipedia.org/wiki/Wars_of_Scottish_Independence"}, {"title": "Treaty of Edinburgh-Northampton", "link": "https://wikipedia.org/wiki/Treaty_of_Edinburgh%E2%80%93Northampton"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "Independence", "link": "https://wikipedia.org/wiki/Independence"}]}, {"year": "1486", "text": "<PERSON> presents his plans discovering a western route to the Indies to the Spanish Queen <PERSON> of Castile.", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents his plans discovering a western route to the Indies to the Spanish Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> presents his plans discovering a western route to the Indies to the Spanish Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1669", "text": "<PERSON>'s raid on Lake Maracaibo, the Spanish Armada de Barlovento is defeated by an English Privateer fleet led by Captain <PERSON>.", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_raid_on_Lake_Maracaibo\" title=\"<PERSON>'s raid on Lake Maracaibo\"><PERSON>'s raid on Lake Maracaibo</a>, the Spanish <a href=\"https://wikipedia.org/wiki/Armada_de_Barlovento\" title=\"Armada de Barlovento\">Armada de Barlovento</a> is defeated by an English Privateer fleet led by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_raid_on_Lake_Maracaibo\" title=\"<PERSON>'s raid on Lake Maracaibo\"><PERSON>'s raid on Lake Maracaibo</a>, the Spanish <a href=\"https://wikipedia.org/wiki/Armada_de_Barlovento\" title=\"Armada de Barlovento\">Armada de Barlovento</a> is defeated by an English Privateer fleet led by Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>'s raid on Lake Maracaibo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_raid_on_Lake_Maracaibo"}, {"title": "Armada de Barlovento", "link": "https://wikipedia.org/wiki/Armada_de_Barlovento"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "The Act of Union joining England and Scotland to form the Kingdom of Great Britain takes effect.", "html": "1707 - The <a href=\"https://wikipedia.org/wiki/Act_of_Union_1707\" class=\"mw-redirect\" title=\"Act of Union 1707\">Act of Union</a> joining England and Scotland to form the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> takes effect.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Act_of_Union_1707\" class=\"mw-redirect\" title=\"Act of Union 1707\">Act of Union</a> joining England and Scotland to form the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> takes effect.", "links": [{"title": "Act of Union 1707", "link": "https://wikipedia.org/wiki/Act_of_Union_1707"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1753", "text": "Publication of Species Plantarum by Linnaeus, and the formal start date of plant taxonomy adopted by the International Code of Botanical Nomenclature.", "html": "1753 - Publication of <i>Species Plantarum</i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_Linnaeus\" title=\"Carl Linnaeus\">Linnaeus</a>, and the formal start date of <a href=\"https://wikipedia.org/wiki/Plant_taxonomy\" title=\"Plant taxonomy\">plant taxonomy</a> adopted by the <a href=\"https://wikipedia.org/wiki/International_Code_of_Botanical_Nomenclature\" class=\"mw-redirect\" title=\"International Code of Botanical Nomenclature\">International Code of Botanical Nomenclature</a>.", "no_year_html": "Publication of <i>Species Plantarum</i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_Linnaeus\" title=\"Carl Linnaeus\">Linnaeus</a>, and the formal start date of <a href=\"https://wikipedia.org/wiki/Plant_taxonomy\" title=\"Plant taxonomy\">plant taxonomy</a> adopted by the <a href=\"https://wikipedia.org/wiki/International_Code_of_Botanical_Nomenclature\" class=\"mw-redirect\" title=\"International Code of Botanical Nomenclature\">International Code of Botanical Nomenclature</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Plant taxonomy", "link": "https://wikipedia.org/wiki/Plant_taxonomy"}, {"title": "International Code of Botanical Nomenclature", "link": "https://wikipedia.org/wiki/International_Code_of_Botanical_Nomenclature"}]}, {"year": "1807", "text": "The Slave Trade Act 1807 takes effect, abolishing the slave trade within the British Empire.", "html": "1807 - The <a href=\"https://wikipedia.org/wiki/Slave_Trade_Act_1807\" title=\"Slave Trade Act 1807\">Slave Trade Act 1807</a> takes effect, abolishing the slave trade within the British Empire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Slave_Trade_Act_1807\" title=\"Slave Trade Act 1807\">Slave Trade Act 1807</a> takes effect, abolishing the slave trade within the British Empire.", "links": [{"title": "Slave Trade Act 1807", "link": "https://wikipedia.org/wiki/Slave_Trade_Act_1807"}]}, {"year": "1820", "text": "Execution of the Cato Street Conspirators, who plotted to kill the British Cabinet and Prime Minister Lord <PERSON>.", "html": "1820 - Execution of the <a href=\"https://wikipedia.org/wiki/Cato_Street_Conspiracy\" title=\"Cato Street Conspiracy\">Cato Street Conspirators</a>, who plotted to kill the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> <a href=\"https://wikipedia.org/wiki/Cabinet_of_the_United_Kingdom\" title=\"Cabinet of the United Kingdom\">Cabinet</a> and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of <PERSON>\">Lord <PERSON></a>.", "no_year_html": "Execution of the <a href=\"https://wikipedia.org/wiki/Cato_Street_Conspiracy\" title=\"Cato Street Conspiracy\">Cato Street Conspirators</a>, who plotted to kill the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">British</a> <a href=\"https://wikipedia.org/wiki/Cabinet_of_the_United_Kingdom\" title=\"Cabinet of the United Kingdom\">Cabinet</a> and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool\" title=\"<PERSON>, 2nd Earl of <PERSON>\">Lord <PERSON></a>.", "links": [{"title": "Cato Street Conspiracy", "link": "https://wikipedia.org/wiki/Cato_Street_Conspiracy"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Cabinet of the United Kingdom", "link": "https://wikipedia.org/wiki/Cabinet_of_the_United_Kingdom"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>, 2nd Earl of Liverpool", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Liverpool"}]}, {"year": "1840", "text": "The Penny Black, the first official adhesive postage stamp, is issued in the United Kingdom.", "html": "1840 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_Black\" title=\"Penny Black\"><PERSON></a>, the first official adhesive <a href=\"https://wikipedia.org/wiki/Postage_stamp\" title=\"Postage stamp\">postage stamp</a>, is issued in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_Black\" title=\"Penny Black\"><PERSON></a>, the first official adhesive <a href=\"https://wikipedia.org/wiki/Postage_stamp\" title=\"Postage stamp\">postage stamp</a>, is issued in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Postage stamp", "link": "https://wikipedia.org/wiki/Postage_stamp"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1844", "text": "Hong Kong Police Force, the world's second modern police force and Asia's first, is established.", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Hong_Kong_Police_Force\" title=\"Hong Kong Police Force\">Hong Kong Police Force</a>, the world's second modern <a href=\"https://wikipedia.org/wiki/Police\" title=\"Police\">police force</a> and Asia's first, is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_Kong_Police_Force\" title=\"Hong Kong Police Force\">Hong Kong Police Force</a>, the world's second modern <a href=\"https://wikipedia.org/wiki/Police\" title=\"Police\">police force</a> and Asia's first, is established.", "links": [{"title": "Hong Kong Police Force", "link": "https://wikipedia.org/wiki/Hong_Kong_Police_Force"}, {"title": "Police", "link": "https://wikipedia.org/wiki/Police"}]}, {"year": "1846", "text": "The few remaining Mormons left in Nauvoo, Illinois, formally dedicate the Nauvoo Temple.", "html": "1846 - The few remaining <a href=\"https://wikipedia.org/wiki/Mormons\" title=\"Mormons\">Mormons</a> left in <a href=\"https://wikipedia.org/wiki/Nauvoo,_Illinois\" title=\"Nauvoo, Illinois\">Nauvoo, Illinois</a>, formally dedicate the <a href=\"https://wikipedia.org/wiki/Nauvoo_Temple\" title=\"Nauvoo Temple\">Nauvoo Temple</a>.", "no_year_html": "The few remaining <a href=\"https://wikipedia.org/wiki/Mormons\" title=\"Mormons\">Mormons</a> left in <a href=\"https://wikipedia.org/wiki/Nauvoo,_Illinois\" title=\"Nauvoo, Illinois\">Nauvoo, Illinois</a>, formally dedicate the <a href=\"https://wikipedia.org/wiki/Nauvoo_Temple\" title=\"Nauvoo Temple\">Nauvoo Temple</a>.", "links": [{"title": "Mormons", "link": "https://wikipedia.org/wiki/Mormons"}, {"title": "Nauvoo, Illinois", "link": "https://wikipedia.org/wiki/Nauvoo,_Illinois"}, {"title": "Nauvoo Temple", "link": "https://wikipedia.org/wiki/Nauvoo_Temple"}]}, {"year": "1851", "text": "Queen <PERSON> opens The Great Exhibition at The Crystal Palace in London.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a> opens <a href=\"https://wikipedia.org/wiki/The_Great_Exhibition\" class=\"mw-redirect\" title=\"The Great Exhibition\">The Great Exhibition</a> at <a href=\"https://wikipedia.org/wiki/The_Crystal_Palace\" title=\"The Crystal Palace\">The Crystal Palace</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen Victoria\">Queen <PERSON></a> opens <a href=\"https://wikipedia.org/wiki/The_Great_Exhibition\" class=\"mw-redirect\" title=\"The Great Exhibition\">The Great Exhibition</a> at <a href=\"https://wikipedia.org/wiki/The_Crystal_Palace\" title=\"The Crystal Palace\">The Crystal Palace</a> in London.", "links": [{"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}, {"title": "The Great Exhibition", "link": "https://wikipedia.org/wiki/The_Great_Exhibition"}, {"title": "The Crystal Palace", "link": "https://wikipedia.org/wiki/The_Crystal_Palace"}]}, {"year": "1863", "text": "American Civil War: The Battle of Chancellorsville between <PERSON>'s Confederate Army of Northern Virginia and the Union Army of the Potomac under <PERSON> begins.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> and the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a> between <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> and the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Chancellorsville", "link": "https://wikipedia.org/wiki/Battle_of_Chancellorsville"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "American Civil War: During the Vicksburg campaign, Union forces under <PERSON> win at the Battle of Port Gibson and establish a firm presence on the east side of the Mississippi River.", "html": "1863 - American Civil War: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_campaign\" title=\"Vicksburg campaign\">Vicksburg campaign</a>, Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> win at the <a href=\"https://wikipedia.org/wiki/Battle_of_Port_Gibson\" title=\"Battle of Port Gibson\">Battle of Port Gibson</a> and establish a firm presence on the east side of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "no_year_html": "American Civil War: During the <a href=\"https://wikipedia.org/wiki/Vicksburg_campaign\" title=\"Vicksburg campaign\">Vicksburg campaign</a>, Union forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> win at the <a href=\"https://wikipedia.org/wiki/Battle_of_Port_Gibson\" title=\"Battle of Port Gibson\">Battle of Port Gibson</a> and establish a firm presence on the east side of the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>.", "links": [{"title": "Vicksburg campaign", "link": "https://wikipedia.org/wiki/Vicksburg_campaign"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battle of Port Gibson", "link": "https://wikipedia.org/wiki/Battle_of_Port_Gibson"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1865", "text": "The Empire of Brazil, Argentina, and Uruguay sign the Treaty of the Triple Alliance.", "html": "1865 - The <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, and <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_the_Triple_Alliance\" title=\"Treaty of the Triple Alliance\">Treaty of the Triple Alliance</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Empire of Brazil</a>, <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentina</a>, and <a href=\"https://wikipedia.org/wiki/Uruguay\" title=\"Uruguay\">Uruguay</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_the_Triple_Alliance\" title=\"Treaty of the Triple Alliance\">Treaty of the Triple Alliance</a>.", "links": [{"title": "Empire of Brazil", "link": "https://wikipedia.org/wiki/Empire_of_Brazil"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Uruguay", "link": "https://wikipedia.org/wiki/Uruguay"}, {"title": "Treaty of the Triple Alliance", "link": "https://wikipedia.org/wiki/Treaty_of_the_Triple_Alliance"}]}, {"year": "1866", "text": "The Memphis Race Riots begin. Over three days, 46 blacks and two whites were killed. Reports of the atrocities influenced passage of the Fourteenth Amendment to the United States Constitution.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/Memphis_riots_of_1866\" class=\"mw-redirect\" title=\"Memphis riots of 1866\">Memphis Race Riots</a> begin. Over three days, 46 blacks and two whites were killed. Reports of the atrocities influenced passage of the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth Amendment to the United States Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Memphis_riots_of_1866\" class=\"mw-redirect\" title=\"Memphis riots of 1866\">Memphis Race Riots</a> begin. Over three days, 46 blacks and two whites were killed. Reports of the atrocities influenced passage of the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">Fourteenth Amendment to the United States Constitution</a>.", "links": [{"title": "Memphis riots of 1866", "link": "https://wikipedia.org/wiki/Memphis_riots_of_1866"}, {"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1885", "text": "The original Chicago Board of Trade Building opens for business.", "html": "1885 - The original <a href=\"https://wikipedia.org/wiki/Chicago_Board_of_Trade_Building\" title=\"Chicago Board of Trade Building\">Chicago Board of Trade Building</a> opens for business.", "no_year_html": "The original <a href=\"https://wikipedia.org/wiki/Chicago_Board_of_Trade_Building\" title=\"Chicago Board of Trade Building\">Chicago Board of Trade Building</a> opens for business.", "links": [{"title": "Chicago Board of Trade Building", "link": "https://wikipedia.org/wiki/Chicago_Board_of_Trade_Building"}]}, {"year": "1886", "text": "Rallies are held throughout the United States demanding the eight-hour work day, culminating in the Haymarket affair in Chicago, in commemoration of which May 1 is celebrated as International Workers' Day in many countries.", "html": "1886 - Rallies are held throughout the United States demanding the eight-hour work day, culminating in the <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, in commemoration of which May 1 is celebrated as <a href=\"https://wikipedia.org/wiki/International_Workers%27_Day\" title=\"International Workers' Day\">International Workers' Day</a> in many countries.", "no_year_html": "Rallies are held throughout the United States demanding the eight-hour work day, culminating in the <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, in commemoration of which May 1 is celebrated as <a href=\"https://wikipedia.org/wiki/International_Workers%27_Day\" title=\"International Workers' Day\">International Workers' Day</a> in many countries.", "links": [{"title": "Haymarket affair", "link": "https://wikipedia.org/wiki/Haymarket_affair"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "International Workers' Day", "link": "https://wikipedia.org/wiki/International_Workers%27_Day"}]}, {"year": "1894", "text": "<PERSON><PERSON>'s Army, the first significant American protest march, arrives in Washington, D.C.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Army\" title=\"<PERSON><PERSON>'s Army\"><PERSON><PERSON>'s Army</a>, the first significant American <a href=\"https://wikipedia.org/wiki/Demonstration_(protest)\" class=\"mw-redirect\" title=\"Demonstration (protest)\">protest march</a>, arrives in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Army\" title=\"<PERSON><PERSON>'s Army\"><PERSON><PERSON>'s Army</a>, the first significant American <a href=\"https://wikipedia.org/wiki/Demonstration_(protest)\" class=\"mw-redirect\" title=\"Demonstration (protest)\">protest march</a>, arrives in <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>", "links": [{"title": "<PERSON><PERSON>'s Army", "link": "https://wikipedia.org/wiki/Coxey%27s_Army"}, {"title": "Demonstration (protest)", "link": "https://wikipedia.org/wiki/Demonstration_(protest)"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1896", "text": "<PERSON><PERSON>, Shah of Iran, is assassinated in Shah Abdol-Azim Shrine by <PERSON>, a follower of <PERSON>.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>Qajar\" title=\"<PERSON><PERSON>-<PERSON> Qajar\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Qajar_dynasty\" title=\"Qajar dynasty\"><PERSON> of Iran</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Shah_Abdol-Azim_Shrine\" title=\"Shah Abdol-Azim Shrine\">Shah Abdol-Azim Shrine</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i\" title=\"<PERSON>\"><PERSON></a>, a follower of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>Qajar\" title=\"<PERSON><PERSON> Qaja<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Qajar_dynasty\" title=\"Qajar dynasty\"><PERSON> of Iran</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Shah_Abdol-Azim_Shrine\" title=\"Shah Abdol-Azim Shrine\">Shah Abdol-Azim Shrine</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>i\" title=\"<PERSON>\"><PERSON></a>, a follower of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>_<PERSON>"}, {"title": "Qajar dynasty", "link": "https://wikipedia.org/wiki/Qajar_dynasty"}, {"title": "Shah Abdol-Azim Shrine", "link": "https://wikipedia.org/wiki/Shah_<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1898", "text": "Spanish-American War: Battle of Manila Bay: The Asiatic Squadron of the United States Navy destroys the Pacific Squadron of the Spanish Navy after a seven-hour battle. Spain loses all seven of its ships, and 381 Spanish sailors die. There are no American vessel losses or combat deaths.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_Bay\" title=\"Battle of Manila Bay\">Battle of Manila Bay</a>: The <a href=\"https://wikipedia.org/wiki/Asiatic_Squadron\" title=\"Asiatic Squadron\">Asiatic Squadron</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> destroys the <a href=\"https://wikipedia.org/wiki/Pacific_Squadron\" title=\"Pacific Squadron\">Pacific Squadron</a> of the <a href=\"https://wikipedia.org/wiki/Spanish_Navy\" title=\"Spanish Navy\">Spanish Navy</a> after a seven-hour battle. Spain loses all seven of its ships, and 381 Spanish sailors die. There are no American vessel losses or combat deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_Bay\" title=\"Battle of Manila Bay\">Battle of Manila Bay</a>: The <a href=\"https://wikipedia.org/wiki/Asiatic_Squadron\" title=\"Asiatic Squadron\">Asiatic Squadron</a> of the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> destroys the <a href=\"https://wikipedia.org/wiki/Pacific_Squadron\" title=\"Pacific Squadron\">Pacific Squadron</a> of the <a href=\"https://wikipedia.org/wiki/Spanish_Navy\" title=\"Spanish Navy\">Spanish Navy</a> after a seven-hour battle. Spain loses all seven of its ships, and 381 Spanish sailors die. There are no American vessel losses or combat deaths.", "links": [{"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}, {"title": "Battle of Manila Bay", "link": "https://wikipedia.org/wiki/Battle_of_Manila_Bay"}, {"title": "Asiatic Squadron", "link": "https://wikipedia.org/wiki/Asiatic_Squadron"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Pacific Squadron", "link": "https://wikipedia.org/wiki/Pacific_Squadron"}, {"title": "Spanish Navy", "link": "https://wikipedia.org/wiki/Spanish_Navy"}]}, {"year": "1900", "text": "The Scofield Mine disaster kills over 200 men in Scofield, Utah in what is to date the fifth-worst mining accident in United States history.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Scofield_Mine_disaster\" title=\"Scofield Mine disaster\">Scofield Mine disaster</a> kills over 200 men in <a href=\"https://wikipedia.org/wiki/Scofield,_Utah\" title=\"Scofield, Utah\">Scofield, Utah</a> in what is to date the fifth-worst <a href=\"https://wikipedia.org/wiki/Mining_accident\" title=\"Mining accident\">mining accident</a> in United States history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Scofield_Mine_disaster\" title=\"Scofield Mine disaster\">Scofield Mine disaster</a> kills over 200 men in <a href=\"https://wikipedia.org/wiki/Scofield,_Utah\" title=\"Scofield, Utah\">Scofield, Utah</a> in what is to date the fifth-worst <a href=\"https://wikipedia.org/wiki/Mining_accident\" title=\"Mining accident\">mining accident</a> in United States history.", "links": [{"title": "Scofield Mine disaster", "link": "https://wikipedia.org/wiki/Scofield_Mine_disaster"}, {"title": "Scofield, Utah", "link": "https://wikipedia.org/wiki/Scofield,_Utah"}, {"title": "Mining accident", "link": "https://wikipedia.org/wiki/Mining_accident"}]}, {"year": "1915", "text": "RMS Lusitania departs from New York City on her 202nd, and final, crossing of the North Atlantic. Six days later, the ship is torpedoed off the coast of Ireland with the loss of 1,198 lives.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> departs from <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> on her 202nd, and final, crossing of the <a href=\"https://wikipedia.org/wiki/North_Atlantic\" class=\"mw-redirect\" title=\"North Atlantic\">North Atlantic</a>. Six days later, the ship is torpedoed off the coast of <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a> with the loss of 1,198 lives.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/RMS_Lusitania\" title=\"RMS Lusitania\">RMS <i>Lusitania</i></a> departs from <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> on her 202nd, and final, crossing of the <a href=\"https://wikipedia.org/wiki/North_Atlantic\" class=\"mw-redirect\" title=\"North Atlantic\">North Atlantic</a>. Six days later, the ship is torpedoed off the coast of <a href=\"https://wikipedia.org/wiki/Ireland\" title=\"Ireland\">Ireland</a> with the loss of 1,198 lives.", "links": [{"title": "RMS Lusitania", "link": "https://wikipedia.org/wiki/RMS_Lusitania"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "North Atlantic", "link": "https://wikipedia.org/wiki/North_Atlantic"}, {"title": "Ireland", "link": "https://wikipedia.org/wiki/Ireland"}]}, {"year": "1919", "text": "German troops enter Munich to suppress the Bavarian Soviet Republic.", "html": "1919 - German troops enter <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> to suppress the <a href=\"https://wikipedia.org/wiki/Bavarian_Soviet_Republic\" title=\"Bavarian Soviet Republic\">Bavarian Soviet Republic</a>.", "no_year_html": "German troops enter <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> to suppress the <a href=\"https://wikipedia.org/wiki/Bavarian_Soviet_Republic\" title=\"Bavarian Soviet Republic\">Bavarian Soviet Republic</a>.", "links": [{"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "Bavarian Soviet Republic", "link": "https://wikipedia.org/wiki/Bavarian_Soviet_Republic"}]}, {"year": "1921", "text": "The Jaffa riots commence.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Jaffa_riots\" title=\"Jaffa riots\">Jaffa riots</a> commence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jaffa_riots\" title=\"Jaffa riots\">Jaffa riots</a> commence.", "links": [{"title": "Jaffa riots", "link": "https://wikipedia.org/wiki/Jaf<PERSON>_riots"}]}, {"year": "1925", "text": "The All-China Federation of Trade Unions is officially founded. Today it is the largest trade union in the world, with 134 million members.", "html": "1925 - The <a href=\"https://wikipedia.org/wiki/All-China_Federation_of_Trade_Unions\" title=\"All-China Federation of Trade Unions\">All-China Federation of Trade Unions</a> is officially founded. Today it is the largest <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade union</a> in the world, with 134 million members.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/All-China_Federation_of_Trade_Unions\" title=\"All-China Federation of Trade Unions\">All-China Federation of Trade Unions</a> is officially founded. Today it is the largest <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade union</a> in the world, with 134 million members.", "links": [{"title": "All-China Federation of Trade Unions", "link": "https://wikipedia.org/wiki/All-China_Federation_of_Trade_Unions"}, {"title": "Trade union", "link": "https://wikipedia.org/wiki/Trade_union"}]}, {"year": "1929", "text": "The 7.2 Mw  Kopet Dag earthquake shakes the Iran-Turkmenistan border region with a maximum Mercalli intensity of IX (Violent), killing up to 3,800 and injuring 1,121.", "html": "1929 - The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1929_Kopet_Dag_earthquake\" title=\"1929 Kopet Dag earthquake\">Kopet Dag earthquake</a> shakes the Iran-Turkmenistan border region with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing up to 3,800 and injuring 1,121.", "no_year_html": "The 7.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1929_Kopet_Dag_earthquake\" title=\"1929 Kopet Dag earthquake\">Kopet Dag earthquake</a> shakes the Iran-Turkmenistan border region with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing up to 3,800 and injuring 1,121.", "links": [{"title": "1929 Kopet Dag earthquake", "link": "https://wikipedia.org/wiki/1929_Kopet_Dag_earthquake"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1930", "text": "\"<PERSON><PERSON><PERSON>\" is officially proposed for the name of the newly discovered dwarf planet by <PERSON><PERSON><PERSON> in the Lowell Observatory Observation Circular. The name quickly catches on.", "html": "1930 - \"Pluto\" is officially proposed for the name of <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">the newly discovered dwarf planet</a> by <a href=\"https://wikipedia.org/wiki/Vesto_Slipher\" title=\"Vesto Slipher\">Vesto Slipher</a> in the <i><a href=\"https://wikipedia.org/wiki/Lowell_Observatory\" title=\"Lowell Observatory\">Lowell Observatory</a> Observation Circular</i>. The name quickly catches on.", "no_year_html": "\"Pluto\" is officially proposed for the name of <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">the newly discovered dwarf planet</a> by <a href=\"https://wikipedia.org/wiki/Vesto_Slipher\" title=\"Vesto Slipher\">Vesto Slipher</a> in the <i><a href=\"https://wikipedia.org/wiki/Lowell_Observatory\" title=\"Lowell Observatory\">Lowell Observatory</a> Observation Circular</i>. The name quickly catches on.", "links": [{"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vesto_Slipher"}, {"title": "Lowell Observatory", "link": "https://wikipedia.org/wiki/Lowell_Observatory"}]}, {"year": "1931", "text": "The Empire State Building is dedicated in New York City.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/Empire_State_Building\" title=\"Empire State Building\">Empire State Building</a> is dedicated in New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Empire_State_Building\" title=\"Empire State Building\">Empire State Building</a> is dedicated in New York City.", "links": [{"title": "Empire State Building", "link": "https://wikipedia.org/wiki/Empire_State_Building"}]}, {"year": "1945", "text": "World War II: German radio broadcasts news of <PERSON>'s death, falsely stating that he has \"fallen at his command post in the Reich Chancellery fighting to the last breath against Bolshevism and for Germany\". The Soviet flag is raised over the Reich Chancellery, by order of <PERSON>.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German radio broadcasts news of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death, falsely stating that he has \"fallen at his command post in the <a href=\"https://wikipedia.org/wiki/Reich_Chancellery\" title=\"Reich Chancellery\">Reich Chancellery</a> fighting to the last breath against <a href=\"https://wikipedia.org/wiki/Bolshevism\" title=\"Bolshevism\">Bolshevism</a> and for Germany\". The <a href=\"https://wikipedia.org/wiki/Flag_of_the_Soviet_Union\" title=\"Flag of the Soviet Union\">Soviet flag</a> is raised over the Reich Chancellery, by order of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: German radio broadcasts news of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death, falsely stating that he has \"fallen at his command post in the <a href=\"https://wikipedia.org/wiki/Reich_Chancellery\" title=\"Reich Chancellery\">Reich Chancellery</a> fighting to the last breath against <a href=\"https://wikipedia.org/wiki/Bolshevism\" title=\"Bolshevism\">Bolshevism</a> and for Germany\". The <a href=\"https://wikipedia.org/wiki/Flag_of_the_Soviet_Union\" title=\"Flag of the Soviet Union\">Soviet flag</a> is raised over the Reich Chancellery, by order of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reich Chancellery", "link": "https://wikipedia.org/wiki/Reich_Chancellery"}, {"title": "Bolshevism", "link": "https://wikipedia.org/wiki/Bolshevism"}, {"title": "Flag of the Soviet Union", "link": "https://wikipedia.org/wiki/Flag_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "World War II: Up to 2,500 people die in a mass suicide in Demmin following the advance of the Red Army.", "html": "1945 - World War II: Up to 2,500 people die in a <a href=\"https://wikipedia.org/wiki/Mass_suicide_in_Demmin\" title=\"Mass suicide in Demmin\">mass suicide in Demmin</a> following the advance of the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "no_year_html": "World War II: Up to 2,500 people die in a <a href=\"https://wikipedia.org/wiki/Mass_suicide_in_Demmin\" title=\"Mass suicide in Demmin\">mass suicide in Demmin</a> following the advance of the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "links": [{"title": "Mass suicide in Demmin", "link": "https://wikipedia.org/wiki/Mass_suicide_in_<PERSON><PERSON><PERSON>"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1946", "text": "Start of three-year Pilbara strike of Indigenous Australians.", "html": "1946 - Start of three-year <a href=\"https://wikipedia.org/wiki/1946_Pilbara_strike\" class=\"mw-redirect\" title=\"1946 Pilbara strike\">Pilbara strike</a> of <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Indigenous Australians</a>.", "no_year_html": "Start of three-year <a href=\"https://wikipedia.org/wiki/1946_Pilbara_strike\" class=\"mw-redirect\" title=\"1946 Pilbara strike\">Pilbara strike</a> of <a href=\"https://wikipedia.org/wiki/Indigenous_Australians\" title=\"Indigenous Australians\">Indigenous Australians</a>.", "links": [{"title": "1946 Pilbara strike", "link": "https://wikipedia.org/wiki/1946_Pi<PERSON>bara_strike"}, {"title": "Indigenous Australians", "link": "https://wikipedia.org/wiki/Indigenous_Australians"}]}, {"year": "1947", "text": "Portella della Ginestra massacre against May Day celebrations in Sicily by the bandit and separatist leader <PERSON> where 11 persons are killed and 33 wounded.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Portella_della_Ginestra_massacre\" title=\"Portella della Ginestra massacre\">Portella della Ginestra massacre</a> against <a href=\"https://wikipedia.org/wiki/May_Day\" title=\"May Day\">May Day</a> celebrations in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a> by the bandit and separatist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> where 11 persons are killed and 33 wounded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portella_della_Ginestra_massacre\" title=\"Portella della Ginestra massacre\">Portella della Ginestra massacre</a> against <a href=\"https://wikipedia.org/wiki/May_Day\" title=\"May Day\">May Day</a> celebrations in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a> by the bandit and separatist leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> where 11 persons are killed and 33 wounded.", "links": [{"title": "Portella della Ginestra massacre", "link": "https://wikipedia.org/wiki/Portella_della_Ginestra_massacre"}, {"title": "May Day", "link": "https://wikipedia.org/wiki/May_Day"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "The polio vaccine developed by <PERSON> is made available to the public.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> developed by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is made available to the public.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Polio_vaccine\" title=\"Polio vaccine\">polio vaccine</a> developed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is made available to the public.", "links": [{"title": "Polio vaccine", "link": "https://wikipedia.org/wiki/Polio_vaccine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "A Vickers VC.1 Viking crashes while attempting to return to Blackbushe Airport in Yateley, killing 34.", "html": "1957 - A <a href=\"https://wikipedia.org/wiki/Vickers_VC.1_Viking\" title=\"Vickers VC.1 Viking\">Vickers VC.1 Viking</a> <a href=\"https://wikipedia.org/wiki/1957_Blackbushe_Viking_accident\" title=\"1957 Blackbushe Viking accident\">crashes</a> while attempting to return to <a href=\"https://wikipedia.org/wiki/Blackbushe_Airport\" title=\"Blackbushe Airport\">Blackbushe Airport</a> in <a href=\"https://wikipedia.org/wiki/Yateley\" title=\"Yateley\">Ya<PERSON><PERSON></a>, killing 34.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Vickers_VC.1_Viking\" title=\"Vickers VC.1 Viking\">Vickers VC.1 Viking</a> <a href=\"https://wikipedia.org/wiki/1957_Blackbushe_Viking_accident\" title=\"1957 Blackbushe Viking accident\">crashes</a> while attempting to return to <a href=\"https://wikipedia.org/wiki/Blackbushe_Airport\" title=\"Blackbushe Airport\">Blackbushe Airport</a> in <a href=\"https://wikipedia.org/wiki/Yateley\" title=\"Yateley\">Ya<PERSON><PERSON></a>, killing 34.", "links": [{"title": "Vickers VC.1 Viking", "link": "https://wikipedia.org/wiki/Vickers_VC.1_Viking"}, {"title": "1957 Blackbushe Viking accident", "link": "https://wikipedia.org/wiki/1957_Blackbushe_Viking_accident"}, {"title": "Blackbushe Airport", "link": "https://wikipedia.org/wiki/Blackbushe_Airport"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yateley"}]}, {"year": "1960", "text": "Cold War: U-2 incident: <PERSON>, in a Lockheed U-2 spyplane, is shot down over the Sverdlovsk Oblast, Soviet Union, sparking a diplomatic crisis.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/1960_U-2_incident\" title=\"1960 U-2 incident\">U-2 incident</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">Lockheed U-2</a> <a href=\"https://wikipedia.org/wiki/Spyplane\" class=\"mw-redirect\" title=\"Spyplane\">spyplane</a>, is shot down over the <a href=\"https://wikipedia.org/wiki/Sverdlovsk_Oblast\" title=\"Sverdlovsk Oblast\">Sverdlovsk Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, sparking a diplomatic crisis.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/1960_U-2_incident\" title=\"1960 U-2 incident\">U-2 incident</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in a <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">Lockheed U-2</a> <a href=\"https://wikipedia.org/wiki/Spyplane\" class=\"mw-redirect\" title=\"Spyplane\">spyplane</a>, is shot down over the <a href=\"https://wikipedia.org/wiki/Sverdlovsk_Oblast\" title=\"Sverdlovsk Oblast\">Sverdlovsk Oblast</a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, sparking a diplomatic crisis.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "1960 U-2 incident", "link": "https://wikipedia.org/wiki/1960_U-2_incident"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lockheed U-2", "link": "https://wikipedia.org/wiki/Lockheed_U-2"}, {"title": "Spyplane", "link": "https://wikipedia.org/wiki/Spyplane"}, {"title": "Sverdlovsk Oblast", "link": "https://wikipedia.org/wiki/Sverdlovsk_Oblast"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1961", "text": "The Prime Minister of Cuba, <PERSON><PERSON>, proclaims Cuba a socialist nation and abolishes elections.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cuba\" title=\"Prime Minister of Cuba\">Prime Minister of Cuba</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, proclaims <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> a <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">socialist</a> nation and abolishes elections.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Cuba\" title=\"Prime Minister of Cuba\">Prime Minister of Cuba</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fi<PERSON>\"><PERSON><PERSON></a>, proclaims <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> a <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">socialist</a> nation and abolishes elections.", "links": [{"title": "Prime Minister of Cuba", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Cuba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Socialism", "link": "https://wikipedia.org/wiki/Socialism"}]}, {"year": "1970", "text": "Vietnam War: Protests erupt in response to U.S. and South Vietnamese forces attacking Vietnamese communists in a Cambodian Campaign.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Protests erupt in response to U.S. and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> forces attacking <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Vietnam\" title=\"Communist Party of Vietnam\">Vietnamese communists</a> in a <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">Cambodian Campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Protests erupt in response to U.S. and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> forces attacking <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Vietnam\" title=\"Communist Party of Vietnam\">Vietnamese communists</a> in a <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">Cambodian Campaign</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Communist Party of Vietnam", "link": "https://wikipedia.org/wiki/Communist_Party_of_Vietnam"}, {"title": "Cambodian Campaign", "link": "https://wikipedia.org/wiki/Cambodian_Campaign"}]}, {"year": "1971", "text": "Amtrak (the National Railroad Passenger Corporation) takes over operation of U.S. passenger rail service.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> (the National Railroad Passenger Corporation) takes over operation of <a href=\"https://wikipedia.org/wiki/Rail_transportation_in_the_United_States#Passenger_railroads\" title=\"Rail transportation in the United States\">U.S. passenger rail service</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> (the National Railroad Passenger Corporation) takes over operation of <a href=\"https://wikipedia.org/wiki/Rail_transportation_in_the_United_States#Passenger_railroads\" title=\"Rail transportation in the United States\">U.S. passenger rail service</a>.", "links": [{"title": "Amtrak", "link": "https://wikipedia.org/wiki/Amtrak"}, {"title": "Rail transportation in the United States", "link": "https://wikipedia.org/wiki/Rail_transportation_in_the_United_States#Passenger_railroads"}]}, {"year": "1975", "text": "The Särkänniemi Amusement Park opens in Tampere, Finland.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/S%C3%A4rk%C3%A4nniemi_Amusement_Park\" class=\"mw-redirect\" title=\"Särkänniemi Amusement Park\">Särkänniemi Amusement Park</a> opens in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, Finland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/S%C3%A4rk%C3%A4nniemi_Amusement_Park\" class=\"mw-redirect\" title=\"Särkänniemi Amusement Park\">Särkänniemi Amusement Park</a> opens in <a href=\"https://wikipedia.org/wiki/Tampere\" title=\"Tampere\">Tampere</a>, Finland.", "links": [{"title": "Särkänniemi Amusement Park", "link": "https://wikipedia.org/wiki/S%C3%A4rk%C3%A4nniemi_Amusement_Park"}, {"title": "Tampere", "link": "https://wikipedia.org/wiki/Tampere"}]}, {"year": "1978", "text": "Japan's <PERSON>, travelling by dog sled, becomes the first person to reach the North Pole alone.", "html": "1978 - Japan's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, travelling by <a href=\"https://wikipedia.org/wiki/Dog_sled\" title=\"Dog sled\">dog sled</a>, becomes the first person to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> alone.", "no_year_html": "Japan's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, travelling by <a href=\"https://wikipedia.org/wiki/Dog_sled\" title=\"Dog sled\">dog sled</a>, becomes the first person to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a> alone.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dog sled", "link": "https://wikipedia.org/wiki/Dog_sled"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1982", "text": "Operation Black Buck: The Royal Air Force attacks the Argentine Air Force during Falklands War.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Operation_Black_Buck\" title=\"Operation Black Buck\">Operation Black Buck</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> attacks the <a href=\"https://wikipedia.org/wiki/Argentine_Air_Force\" title=\"Argentine Air Force\">Argentine Air Force</a> during <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Black_Buck\" title=\"Operation Black Buck\">Operation Black Buck</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Air_Force\" title=\"Royal Air Force\">Royal Air Force</a> attacks the <a href=\"https://wikipedia.org/wiki/Argentine_Air_Force\" title=\"Argentine Air Force\">Argentine Air Force</a> during <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "links": [{"title": "Operation Black Buck", "link": "https://wikipedia.org/wiki/Operation_Black_Buck"}, {"title": "Royal Air Force", "link": "https://wikipedia.org/wiki/Royal_Air_Force"}, {"title": "Argentine Air Force", "link": "https://wikipedia.org/wiki/Argentine_Air_Force"}, {"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}]}, {"year": "1991", "text": "Angolan Civil War: The MPLA and UNITA agree to the Bicesse Accords, which are formally signed on May 31 in Lisbon.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Angolan_Civil_War\" title=\"Angolan Civil War\">Angolan Civil War</a>: The <a href=\"https://wikipedia.org/wiki/MPLA\" title=\"MPLA\">MPLA</a> and <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> agree to the <a href=\"https://wikipedia.org/wiki/Bicesse_Accords\" title=\"Bicesse Accords\">Bicesse Accords</a>, which are formally signed on May 31 in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angolan_Civil_War\" title=\"Angolan Civil War\">Angolan Civil War</a>: The <a href=\"https://wikipedia.org/wiki/MPLA\" title=\"MPLA\">MPLA</a> and <a href=\"https://wikipedia.org/wiki/UNITA\" title=\"UNITA\">UNITA</a> agree to the <a href=\"https://wikipedia.org/wiki/Bicesse_Accords\" title=\"Bicesse Accords\">Bicesse Accords</a>, which are formally signed on May 31 in <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a>.", "links": [{"title": "Angolan Civil War", "link": "https://wikipedia.org/wiki/Angolan_Civil_War"}, {"title": "MPLA", "link": "https://wikipedia.org/wiki/MPLA"}, {"title": "UNITA", "link": "https://wikipedia.org/wiki/UNITA"}, {"title": "Bicesse Accords", "link": "https://wikipedia.org/wiki/Bicesse_Accords"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}]}, {"year": "1993", "text": "Sri Lankan President <PERSON><PERSON><PERSON> is assassinated in Colombo in a suicide bombing carried out by the Liberation Tigers of Tamil Eelam.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">Sri Lankan President</a> <a href=\"https://wikipedia.org/wiki/Ranasing<PERSON>_Premadasa\" title=\"Ranasinghe Premadasa\"><PERSON><PERSON><PERSON>mada<PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Ranasinghe_Premadasa\" title=\"Assassination of Ranasinghe Premadasa\">assassinated</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> in a <a href=\"https://wikipedia.org/wiki/Suicide_bombing\" class=\"mw-redirect\" title=\"Suicide bombing\">suicide bombing</a> carried out by the <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">Sri Lankan President</a> <a href=\"https://wikipedia.org/wiki/Ranasing<PERSON>_Premadasa\" title=\"Ranasinghe Premadasa\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Ranasinghe_Premadasa\" title=\"Assassination of Ranasinghe Premadasa\">assassinated</a> in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a> in a <a href=\"https://wikipedia.org/wiki/Suicide_bombing\" class=\"mw-redirect\" title=\"Suicide bombing\">suicide bombing</a> carried out by the <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a>.", "links": [{"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}, {"title": "Suicide bombing", "link": "https://wikipedia.org/wiki/Suicide_bombing"}, {"title": "Liberation Tigers of Tamil Eelam", "link": "https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam"}]}, {"year": "1994", "text": "Three-time Formula One champion <PERSON><PERSON><PERSON> is killed in an accident during the San Marino Grand Prix.", "html": "1994 - Three-time <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/Ayrton_Senna\" title=\"Ayrton Senna\">Ayr<PERSON></a> is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_Senna\" title=\"Death of <PERSON><PERSON><PERSON>\">killed in an accident</a> during the <a href=\"https://wikipedia.org/wiki/1994_San_Marino_Grand_Prix\" title=\"1994 San Marino Grand Prix\">San Marino Grand Prix</a>.", "no_year_html": "Three-time <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/Ayrton_Senna\" title=\"Ayrton Senna\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_Senna\" title=\"Death of <PERSON><PERSON><PERSON>\">killed in an accident</a> during the <a href=\"https://wikipedia.org/wiki/1994_San_Marino_Grand_Prix\" title=\"1994 San Marino Grand Prix\">San Marino Grand Prix</a>.", "links": [{"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayr<PERSON>_Senna"}, {"title": "Death of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_<PERSON>na"}, {"title": "1994 San Marino Grand Prix", "link": "https://wikipedia.org/wiki/1994_San_Marino_Grand_Prix"}]}, {"year": "1997", "text": "Labour Party wins the 1997 General Election and <PERSON> is elected as Prime Minister", "html": "1997 - Labour Party wins the <a href=\"https://wikipedia.org/wiki/1997_United_Kingdom_general_election\" title=\"1997 United Kingdom general election\">1997 General Election</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as Prime Minister", "no_year_html": "Labour Party wins the <a href=\"https://wikipedia.org/wiki/1997_United_Kingdom_general_election\" title=\"1997 United Kingdom general election\">1997 General Election</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected as Prime Minister", "links": [{"title": "1997 United Kingdom general election", "link": "https://wikipedia.org/wiki/1997_United_Kingdom_general_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "The body of British climber <PERSON> is found on Mount Everest, 75 years after his disappearance in 1924.", "html": "1999 - The body of British climber <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found on <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, 75 years after his <a href=\"https://wikipedia.org/wiki/1924_British_Mount_Everest_expedition\" title=\"1924 British Mount Everest expedition\">disappearance in 1924</a>.", "no_year_html": "The body of British climber <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found on <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, 75 years after his <a href=\"https://wikipedia.org/wiki/1924_British_Mount_Everest_expedition\" title=\"1924 British Mount Everest expedition\">disappearance in 1924</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}, {"title": "1924 British Mount Everest expedition", "link": "https://wikipedia.org/wiki/1924_British_Mount_Everest_expedition"}]}, {"year": "2003", "text": "Invasion of Iraq: In what becomes known as the \"Mission Accomplished\" speech, on board the USS <PERSON> Lincoln (off the coast of California), U.S. President <PERSON> declares that \"major combat operations in Iraq have ended\".", "html": "2003 - <a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">Invasion of Iraq</a>: In what becomes known as the \"<a href=\"https://wikipedia.org/wiki/Mission_Accomplished_speech\" title=\"Mission Accomplished speech\">Mission Accomplished</a>\" speech, on board the <a href=\"https://wikipedia.org/wiki/USS_Abraham_Lincoln_(CVN-72)\" title=\"USS Abraham Lincoln (CVN-72)\">USS <i><PERSON></i></a> (off the coast of <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>), U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares that \"major combat operations in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> have ended\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2003_invasion_of_Iraq\" title=\"2003 invasion of Iraq\">Invasion of Iraq</a>: In what becomes known as the \"<a href=\"https://wikipedia.org/wiki/Mission_Accomplished_speech\" title=\"Mission Accomplished speech\">Mission Accomplished</a>\" speech, on board the <a href=\"https://wikipedia.org/wiki/USS_Abraham_Lincoln_(CVN-72)\" title=\"USS Abraham Lincoln (CVN-72)\">USS <i><PERSON></i></a> (off the coast of <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>), U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares that \"major combat operations in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> have ended\".", "links": [{"title": "2003 invasion of Iraq", "link": "https://wikipedia.org/wiki/2003_invasion_of_Iraq"}, {"title": "Mission Accomplished speech", "link": "https://wikipedia.org/wiki/Mission_Accomplished_speech"}, {"title": "<PERSON> (CVN-72)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(CVN-72)"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2004", "text": "Cyprus, Czech Republic, Estonia, Hungary, Latvia, Lithuania, Malta, Poland, Slovakia, and Slovenia join the European Union, celebrated at the residence of the Irish President in Dublin.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>, celebrated at the residence of the <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">Irish President</a> in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Czech_Republic\" title=\"Czech Republic\">Czech Republic</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Hungary\" title=\"Hungary\">Hungary</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>, <a href=\"https://wikipedia.org/wiki/Poland\" title=\"Poland\">Poland</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>, and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>, celebrated at the residence of the <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">Irish President</a> in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "links": [{"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "Czech Republic", "link": "https://wikipedia.org/wiki/Czech_Republic"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Hungary", "link": "https://wikipedia.org/wiki/Hungary"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}, {"title": "Poland", "link": "https://wikipedia.org/wiki/Poland"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "2009", "text": "Same-sex marriage is legalized in Sweden.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Sweden\" title=\"Same-sex marriage in Sweden\">Same-sex marriage</a> is legalized in Sweden.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_Sweden\" title=\"Same-sex marriage in Sweden\">Same-sex marriage</a> is legalized in Sweden.", "links": [{"title": "Same-sex marriage in Sweden", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_Sweden"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON> attempts to detonate a car bomb in Times Square, but the bomb fails to go off.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/2010_Times_Square_car_bombing_attempt\" title=\"2010 Times Square car bombing attempt\">attempts</a> to detonate a <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a> in <a href=\"https://wikipedia.org/wiki/Times_Square\" title=\"Times Square\">Times Square</a>, but the bomb fails to go off.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/2010_Times_Square_car_bombing_attempt\" title=\"2010 Times Square car bombing attempt\">attempts</a> to detonate a <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a> in <a href=\"https://wikipedia.org/wiki/Times_Square\" title=\"Times Square\">Times Square</a>, but the bomb fails to go off.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "2010 Times Square car bombing attempt", "link": "https://wikipedia.org/wiki/2010_Times_Square_car_bombing_attempt"}, {"title": "Car bomb", "link": "https://wikipedia.org/wiki/Car_bomb"}, {"title": "Times Square", "link": "https://wikipedia.org/wiki/Times_Square"}]}, {"year": "2011", "text": "Pope <PERSON> is beatified by his successor, Pope <PERSON>.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Beatification\" title=\"Beatification\">beatified</a> by his successor, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> is <a href=\"https://wikipedia.org/wiki/Beatification\" title=\"Beatification\">beatified</a> by his successor, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Beatification", "link": "https://wikipedia.org/wiki/Beatification"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "Syrian civil war: The Syrian Democratic Forces (SDF) resumes the Deir ez-Zor campaign in order to clear the remnants of the Islamic State of Iraq and the Levant (ISIL) from the Iraq-Syria border.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) resumes the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\">Deir ez-Zor campaign</a> in order to clear the remnants of the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL) from the <a href=\"https://wikipedia.org/wiki/Iraq%E2%80%93Syria_border\" title=\"Iraq-Syria border\">Iraq-Syria border</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/Syrian_Democratic_Forces\" title=\"Syrian Democratic Forces\">Syrian Democratic Forces</a> (SDF) resumes the <a href=\"https://wikipedia.org/wiki/Deir_ez-Zor_campaign_(2017%E2%80%932019)\" title=\"Deir ez-Zor campaign (2017-2019)\">Deir e<PERSON>-Zor campaign</a> in order to clear the remnants of the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (ISIL) from the <a href=\"https://wikipedia.org/wiki/Iraq%E2%80%93Syria_border\" title=\"Iraq-Syria border\">Iraq-Syria border</a>.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "Syrian Democratic Forces", "link": "https://wikipedia.org/wiki/Syrian_Democratic_Forces"}, {"title": "<PERSON><PERSON> ez<PERSON>Zor campaign (2017-2019)", "link": "https://wikipedia.org/wiki/Deir_<PERSON><PERSON>-<PERSON><PERSON>_campaign_(2017%E2%80%932019)"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}, {"title": "Iraq-Syria border", "link": "https://wikipedia.org/wiki/Iraq%E2%80%93Syria_border"}]}, {"year": "2019", "text": "Naxalite attack in Gadchiroli district of India: Sixteen army soldiers, including a driver, killed in an IED blast. Na<PERSON>ls targeted an anti-Naxal operations team.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Naxalite\" class=\"mw-redirect\" title=\"Naxalite\">Naxalite</a> attack in <a href=\"https://wikipedia.org/wiki/Gadchiroli_district\" title=\"Gadchiroli district\">Gadchiroli district</a> of India: Sixteen army soldiers, including a driver, <a href=\"https://wikipedia.org/wiki/2019_Gadchiroli_Naxal_Attack\" class=\"mw-redirect\" title=\"2019 Gadchiroli Naxal Attack\">killed in an IED blast</a>. Naxals targeted an anti-Naxal operations team.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naxalite\" class=\"mw-redirect\" title=\"Naxalite\">Naxalite</a> attack in <a href=\"https://wikipedia.org/wiki/Gadchiroli_district\" title=\"Gadchiroli district\">Gadchiroli district</a> of India: Sixteen army soldiers, including a driver, <a href=\"https://wikipedia.org/wiki/2019_Gadchiroli_Naxal_Attack\" class=\"mw-redirect\" title=\"2019 Gadchiroli Naxal Attack\">killed in an IED blast</a>. Naxals targeted an anti-Naxal operations team.", "links": [{"title": "Naxalite", "link": "https://wikipedia.org/wiki/Naxalite"}, {"title": "Gadchiroli district", "link": "https://wikipedia.org/wiki/Gadchiroli_district"}, {"title": "2019 Gadchiroli Naxal Attack", "link": "https://wikipedia.org/wiki/2019_<PERSON><PERSON><PERSON><PERSON><PERSON>_Naxal_Attack"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON><PERSON> ascends to the throne of Japan succeeding his father <PERSON><PERSON><PERSON><PERSON>, beginning the Reiwa period.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Naruhito\" title=\"Naruhito\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/2019_Japanese_imperial_transition\" title=\"2019 Japanese imperial transition\">ascends to the throne of Japan</a> succeeding his father <a href=\"https://wikipedia.org/wiki/Akihito\" title=\"Akihito\">A<PERSON><PERSON><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/Reiwa\" class=\"mw-redirect\" title=\"Re<PERSON><PERSON>\">Re<PERSON><PERSON></a> period.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naruhito\" title=\"Naru<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/2019_Japanese_imperial_transition\" title=\"2019 Japanese imperial transition\">ascends to the throne of Japan</a> succeeding his father <a href=\"https://wikipedia.org/wiki/Akihito\" title=\"Akihito\"><PERSON><PERSON><PERSON><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/Reiwa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> period.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naru<PERSON>o"}, {"title": "2019 Japanese imperial transition", "link": "https://wikipedia.org/wiki/2019_Japanese_imperial_transition"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>hito"}, {"title": "Reiwa", "link": "https://wikipedia.org/wiki/<PERSON>iwa"}]}, {"year": "2024", "text": "The 2024 Loblaw boycott, a Canadian boycott against retail corporation and grocer Loblaw Companies, begins.", "html": "2024 - The <a href=\"https://wikipedia.org/wiki/2024_Loblaw_boycott\" class=\"mw-redirect\" title=\"2024 Loblaw boycott\">2024 Loblaw boycott</a>, a Canadian boycott against retail corporation and grocer <a href=\"https://wikipedia.org/wiki/Loblaw_Companies\" title=\"Loblaw Companies\">Loblaw Companies</a>, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2024_Loblaw_boycott\" class=\"mw-redirect\" title=\"2024 Loblaw boycott\">2024 Loblaw boycott</a>, a Canadian boycott against retail corporation and grocer <a href=\"https://wikipedia.org/wiki/Loblaw_Companies\" title=\"Loblaw Companies\">Loblaw Companies</a>, begins.", "links": [{"title": "2024 Loblaw boycott", "link": "https://wikipedia.org/wiki/2024_<PERSON><PERSON><PERSON>_boycott"}, {"title": "Loblaw Companies", "link": "https://wikipedia.org/wiki/Loblaw_Companies"}]}], "Births": [{"year": "1218", "text": "<PERSON>, Count of Hainaut (d. 1257)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (d. 1257)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hainaut\" title=\"<PERSON>, Count of Hainaut\"><PERSON>, Count of Hainaut</a> (d. 1257)", "links": [{"title": "<PERSON>, Count of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hai<PERSON><PERSON>"}]}, {"year": "1218", "text": "<PERSON> of Germany (d. 1291)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (d. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (d. 1291)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}]}, {"year": "1285", "text": "<PERSON>, 9th Earl of Arundel, English politician (d. 1326)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 9th Earl of Arundel\"><PERSON>, 9th Earl of Arundel</a>, English politician (d. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 9th Earl of Arundel\"><PERSON>, 9th Earl of Arundel</a>, English politician (d. 1326)", "links": [{"title": "<PERSON>, 9th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel"}]}, {"year": "1326", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian emperor (d. 1332)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1332)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1488", "text": "<PERSON><PERSON><PERSON> of Bavaria, eldest daughter of Duke <PERSON><PERSON> of Bavaria-Munich (d. 1505)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bavaria\" title=\"<PERSON><PERSON><PERSON> of Bavaria\"><PERSON><PERSON><PERSON> of Bavaria</a>, eldest daughter of Duke <PERSON><PERSON> of Bavaria-Munich (d. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bavaria\" title=\"<PERSON><PERSON><PERSON> of Bavaria\"><PERSON><PERSON><PERSON> of Bavaria</a>, eldest daughter of Duke <PERSON><PERSON> of Bavaria-Munich (d. 1505)", "links": [{"title": "Sidonie of Bavaria", "link": "https://wikipedia.org/wiki/Sidonie_of_Bavaria"}]}, {"year": "1527", "text": "<PERSON>, German astronomer, astrologer, mathematician (d. 1579)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer, astrologer, mathematician (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer, astrologer, mathematician (d. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON><PERSON>, French theologian (d. 1602)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_elder)\" title=\"<PERSON><PERSON> (the elder)\"><PERSON><PERSON></a>, French theologian (d. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_elder)\" title=\"<PERSON><PERSON> (the elder)\"><PERSON><PERSON></a>, French theologian (d. 1602)", "links": [{"title": "<PERSON><PERSON> (the elder)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(the_elder)"}]}, {"year": "1579", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch-American farmer, co-founded New Netherland (d. 1662)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Wolphert_Gerretse\" class=\"mw-redirect\" title=\"Wolphert Gerretse\"><PERSON><PERSON><PERSON><PERSON> Gerretse</a>, Dutch-American farmer, co-founded <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wolphert_Gerretse\" class=\"mw-redirect\" title=\"Wolphert Gerretse\"><PERSON><PERSON><PERSON><PERSON> Ger<PERSON></a>, Dutch-American farmer, co-founded <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> (d. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wolphert_Gerretse"}, {"title": "New Netherland", "link": "https://wikipedia.org/wiki/New_Netherland"}]}, {"year": "1582", "text": "<PERSON>, Italian composer (d. 1643)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, Belarusian saint (d. 1612)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belarusian saint (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Belarusian saint (d. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1591", "text": "<PERSON>, German missionary and astronomer (d. 1666)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and astronomer (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and astronomer (d. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON>, English-American politician, 1st Governor of the Colony of Connecticut (d. 1653)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, English-American politician, 1st Governor of the Colony of Connecticut (d. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, English-American politician, 1st Governor of the Colony of Connecticut (d. 1653)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_(governor)"}]}, {"year": "1602", "text": "<PERSON>, English astrologer (d. 1681)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astrologer (d. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English essayist, poet, playwright, and politician (d. 1719)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, poet, playwright, and politician (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, poet, playwright, and politician (d. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, English admiral (d. 1790)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, Dutch admiral and philanthropist (d. 1819)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch admiral and philanthropist (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Dutch admiral and philanthropist (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, American poet and playwright (d. 1820)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, English-American architect, designed the United States Capitol (d. 1820)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect, designed the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect, designed the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> (d. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "1769", "text": "<PERSON>, 1st Duke of Wellington, Irish-English field marshal and politician, Prime Minister of the United Kingdom (d. 1852)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON>, 1st Duke of Wellington</a>, Irish-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON>, 1st Duke of Wellington</a>, Irish-English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1852)", "links": [{"title": "<PERSON>, 1st Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1783", "text": "<PERSON>, American hymnwriter (d. 1861)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hymnwriter (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hymnwriter (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Irish poet and author (d. 1849)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and author (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and author (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Greek satirical poet and writer (d. 1901)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek satirical poet and writer (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek satirical poet and writer (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, English-Australian politician, 8th Premier of South Australia (d. 1897)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 8th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1824", "text": "<PERSON>, English chemist and academic (d. 1904)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, Swiss mathematician and physicist (d. 1898)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and physicist (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and physicist (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, American painter and educator (d. 1894)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, French painter (d. 1906)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Jules_Breton\" title=\"Jules <PERSON>\"><PERSON></a>, French painter (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jules_Breton\" title=\"Jules Breton\"><PERSON></a>, French painter (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON>, Brazilian author and playwright (d. 1877)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and playwright (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and playwright (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_<PERSON><PERSON><PERSON>"}]}, {"year": "1829", "text": "<PERSON>, English painter and illustrator (d. 1904)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Belgian priest and poet (d. 1899)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian priest and poet (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian priest and poet (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Canadian physician and activist (d. 1903)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and activist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and activist (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, American journalist and politician (d. 1903)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, Norwegian painter (d. 1919)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Adels<PERSON>_<PERSON>n\" title=\"Adelsteen Normann\"><PERSON><PERSON><PERSON></a>, Norwegian painter (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adels<PERSON>_Normann\" title=\"Adelsteen Normann\"><PERSON><PERSON><PERSON></a>, Norwegian painter (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Normann"}]}, {"year": "1850", "text": "<PERSON>, Duke of Connaught and Strathearn (d. 1942)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Connaught_and_Strathearn\" title=\"Prince <PERSON>, Duke of Connaught and Strathearn\">Prince <PERSON>, Duke of Connaught and Strathearn</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Connaught_and_Strathearn\" title=\"Prince <PERSON>, Duke of Connaught and Strathearn\">Prince <PERSON>, Duke of Connaught and Strathearn</a> (d. 1942)", "links": [{"title": "<PERSON>, Duke of Connaught and Strathearn", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_<PERSON>_Connaught_and_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON><PERSON>, American frontierswoman and professional scout (d. 1903)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American frontierswoman and professional scout (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American frontierswoman and professional scout (d. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ami<PERSON>_Jane"}]}, {"year": "1852", "text": "<PERSON>, Spanish neuroscientist and pathologist, Nobel Prize laureate (d. 1934)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/Santiago_Ram%C3%B3n_y_Cajal\" title=\"Santiago Ramón y Cajal\"><PERSON> Ramón y Cajal</a>, Spanish neuroscientist and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Ram%C3%B3n_y_Cajal\" title=\"Santiago Ramón y Cajal\"><PERSON> Ramón y Cajal</a>, Spanish neuroscientist and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1934)", "links": [{"title": "Santiago Ramón y Cajal", "link": "https://wikipedia.org/wiki/Santiago_Ram%C3%B3n_y_Cajal"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1853", "text": "<PERSON>, Ukrainian-American journalist, actor, and playwright (d. 1909)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American journalist, actor, and playwright (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American journalist, actor, and playwright (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, American painter and academic (d. 1942)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Dutch art dealer (d. 1891)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(art_dealer)\" title=\"<PERSON> (art dealer)\"><PERSON></a>, Dutch art dealer (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(art_dealer)\" title=\"<PERSON> (art dealer)\"><PERSON></a>, Dutch art dealer (d. 1891)", "links": [{"title": "<PERSON> (art dealer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(art_dealer)"}]}, {"year": "1859", "text": "<PERSON>, French painter and sculptor (d. 1955)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, French novelist and playwright (d. 1941)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9vost\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vost\" title=\"<PERSON>\"><PERSON></a>, French novelist and playwright (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_Pr%C3%A9vost"}]}, {"year": "1864", "text": "<PERSON>, American founder of Mother's Day (d. 1948)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American founder of <a href=\"https://wikipedia.org/wiki/Mother%27s_Day\" title=\"Mother's Day\">Mother's Day</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American founder of <a href=\"https://wikipedia.org/wiki/Mother%27s_Day\" title=\"Mother's Day\">Mother's Day</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mother's Day", "link": "https://wikipedia.org/wiki/Mother%27s_Day"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Dutch theologian and scholar (d. 1948)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dan<PERSON>\" title=\"<PERSON><PERSON> Greijdanus\"><PERSON><PERSON></a>, Dutch theologian and scholar (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dan<PERSON>\" title=\"<PERSON><PERSON> Greijdanus\"><PERSON><PERSON></a>, Dutch theologian and scholar (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>us"}]}, {"year": "1871", "text": "<PERSON><PERSON>, President of Nicaragua (d. 1966)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, President of Nicaragua (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, President of Nicaragua (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_Chamor<PERSON>_Vargas"}]}, {"year": "1872", "text": "<PERSON>, Swedish composer, conductor, violinist, and painter (d. 1960)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish composer, conductor, violinist, and painter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish composer, conductor, violinist, and painter (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Alfv%C3%A9n"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Portuguese soldier and politician, 4th President of Portugal (d. 1918)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Sid%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sid%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sid%C3%B3nio_Pais"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1874", "text": "<PERSON><PERSON>, American-French painter and illustrator (d. 1970)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-French painter and illustrator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-French painter and illustrator (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Belgian target shooter (d. 1959)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian target shooter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian target shooter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American runner (d. 1972)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, American runner (d. 1972)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1881", "text": "<PERSON>, French priest, palaeontologist, and philosopher (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, palaeontologist, and philosopher (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest, palaeontologist, and philosopher (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, 5th <PERSON>, English race car driver and politician (d. 1964)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th <PERSON>\"><PERSON>, 5th <PERSON></a>, English race car driver and politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>\" title=\"<PERSON>, 5th <PERSON>\"><PERSON>, 5th <PERSON></a>, English race car driver and politician (d. 1964)", "links": [{"title": "<PERSON>, 5th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Belgian poet (d. 1922)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Pansaers\" title=\"Clément Pansa<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian poet (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Pansaers\" title=\"Clément Pansa<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian poet (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Pansaers"}]}, {"year": "1885", "text": "<PERSON>, American sculptor and painter (d. 1973)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and painter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Anglo-Irish general and diplomat, High Commissioners for Palestine and Transjordan (d. 1983)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish general and diplomat, <a href=\"https://wikipedia.org/wiki/High_Commissioners_for_Palestine_and_Transjordan\" class=\"mw-redirect\" title=\"High Commissioners for Palestine and Transjordan\">High Commissioners for Palestine and Transjordan</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish general and diplomat, <a href=\"https://wikipedia.org/wiki/High_Commissioners_for_Palestine_and_Transjordan\" class=\"mw-redirect\" title=\"High Commissioners for Palestine and Transjordan\">High Commissioners for Palestine and Transjordan</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "High Commissioners for Palestine and Transjordan", "link": "https://wikipedia.org/wiki/High_Commissioners_for_Palestine_and_Transjordan"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Italian physician (d. 1963 or 1964)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (d. 1963 or 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (d. 1963 or 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1891", "text": "<PERSON>, American historian of Spanish America (d. 1988)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of Spanish America (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of Spanish America (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Australian theatre producer and director (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, Australian theatre producer and director (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian theatre producer and director (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Soviet secret police official, head of the NKVD (d. 1940)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> secret police official, head of the <a href=\"https://wikipedia.org/wiki/<PERSON>KVD\" title=\"<PERSON><PERSON><PERSON>\">NKVD</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> secret police official, head of the <a href=\"https://wikipedia.org/wiki/NKVD\" title=\"NKVD\">NKVD</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "NKVD", "link": "https://wikipedia.org/wiki/NKVD"}]}, {"year": "1896", "text": "<PERSON>, German agronomist and politician (d. 1947)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German agronomist and politician (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German agronomist and politician (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American general (d. 1984)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American general (d. 1987)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American general (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Estonian weightlifter (d. 1972)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, Estonian weightlifter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(weightlifter)\" title=\"<PERSON> (weightlifter)\"><PERSON></a>, Estonian weightlifter (d. 1972)", "links": [{"title": "<PERSON> (weightlifter)", "link": "https://wikipedia.org/wiki/<PERSON>_(weightlifter)"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish poet and writer (d. 1967)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Aleksander_Wat\" title=\"Aleksander Wat\"><PERSON>eksan<PERSON>at</a>, Polish poet and writer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Wat\" title=\"Aleksander Wat\"><PERSON><PERSON><PERSON><PERSON>at</a>, Polish poet and writer (d. 1967)", "links": [{"title": "Aleksander Wat", "link": "https://wikipedia.org/wiki/Aleksander_Wat"}]}, {"year": "1901", "text": "<PERSON>, American poet, academic, and critic (d. 1989)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, academic, and critic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, academic, and critic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Hungarian scholar and author (d. 1945)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian scholar and author (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian scholar and author (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antal_<PERSON>b"}]}, {"year": "1905", "text": "<PERSON>, German-American director, producer, and screenwriter (d. 1988)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American director, producer, and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, German SS officer and physician (d. 1983)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and physician (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and physician (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ho<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1907", "text": "<PERSON>, American singer and actress (d. 1986)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Italian journalist and author (d. 1968)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and author (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American mathematician and academic (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kline\"><PERSON></a>, American mathematician and academic (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kline\"><PERSON></a>, American mathematician and academic (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kline"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Estonian-Soviet military pilot and politician (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pu<PERSON>\"><PERSON><PERSON></a>, Estonian-Soviet military pilot and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pu<PERSON>\"><PERSON><PERSON></a>, Estonian-Soviet military pilot and politician (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>pp"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Greek poet and playwright (d. 1990)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and playwright (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Ukrainian-American philosopher and activist (d. 1987)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American philosopher and activist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American philosopher and activist (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American astronomer and ufologist (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and ufologist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer and ufologist (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, German admiral (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, German business executive (d. 1977)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German business executive (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German business executive (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian-American actor and producer (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, Canadian-American actor and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, Canadian-American actor and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and actor (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Trinidadian navigator, judge, and diplomat (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"U<PERSON><PERSON> Cross\"><PERSON><PERSON><PERSON></a>, Trinidadian navigator, judge, and diplomat (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Cross\"><PERSON><PERSON><PERSON></a>, Trinidadian navigator, judge, and diplomat (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French actress and singer (d. 2017)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American comedian, author and talk show host (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, author and talk show host (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, author and talk show host (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Indian singer and composer (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer and composer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1919", "text": "<PERSON>, Moroccan businessman and politician, 7th Prime Minister of Morocco (d. 2018)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Morocco\" title=\"Prime Minister of Morocco\">Prime Minister of Morocco</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan businessman and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Morocco\" title=\"Prime Minister of Morocco\">Prime Minister of Morocco</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Morocco", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Morocco"}]}, {"year": "1919", "text": "<PERSON>, Irish actor (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Herlihy\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Herlihy\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_O%27Herlihy"}]}, {"year": "1921", "text": "<PERSON>, Romanian journalist and author (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian journalist and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American novelist, short story writer, and playwright (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Polish soldier (d. 1944)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish soldier (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Polish soldier (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American mathematician, computer scientist, and academic (d. 2023)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American novelist, essayist, and screenwriter (d. 1995)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Terry Southern\"><PERSON></a>, American novelist, essayist, and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American lieutenant and football player (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and football player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and football player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American commander, pilot, and astronaut (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, pilot, and astronaut (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Hungarian-American mathematician and academic", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Danish swimmer (d. 2023)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish swimmer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish swimmer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Italian actress (d. 2004)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Yugoslav-Croatian footballer (d. 1983)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav-Croatian footballer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Yugoslav-Croatian footballer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Malagasy politician, 3rd President of Madagascar (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy politician, 3rd President of Madagascar (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy politician, 3rd President of Madagascar (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, German-English sociologist and politician (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English sociologist and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English sociologist and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Trinidadian cricketer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American sprinter and football player (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter and football player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lieutenant and politician, 39th Mayor of Los Angeles and publisher (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> and publisher (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 39th <a href=\"https://wikipedia.org/wiki/Mayor_of_Los_Angeles\" title=\"Mayor of Los Angeles\">Mayor of Los Angeles</a> and publisher (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Los Angeles", "link": "https://wikipedia.org/wiki/Mayor_of_Los_Angeles"}]}, {"year": "1930", "text": "<PERSON>, American blues harp player and singer (d. 1968)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a> <PERSON>, American blues harp player and singer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a> <PERSON>, American blues harp player and singer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian politician and statesman, Minister of External Affairs, 10th Chief Minister of Karnataka, 19th Governor of Maharashtra (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician and statesman, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Minister of External Affairs</a>, 10th <a href=\"https://wikipedia.org/wiki/List_of_chief_ministers_of_Karnataka\" title=\"List of chief ministers of Karnataka\">Chief Minister of Karnataka</a>, 19th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Maharashtra\" title=\"List of governors of Maharashtra\">Governor of Maharashtra</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian politician and statesman, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Minister of External Affairs</a>, 10th <a href=\"https://wikipedia.org/wiki/List_of_chief_ministers_of_Karnataka\" title=\"List of chief ministers of Karnataka\">Chief Minister of Karnataka</a>, 19th <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Maharashtra\" title=\"List of governors of Maharashtra\">Governor of Maharashtra</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of External Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)"}, {"title": "List of chief ministers of Karnataka", "link": "https://wikipedia.org/wiki/List_of_chief_ministers_of_Karnataka"}, {"title": "List of governors of Maharashtra", "link": "https://wikipedia.org/wiki/List_of_governors_of_Maharashtra"}]}, {"year": "1932", "text": "<PERSON>, English admiral (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mexican politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas\" title=\"Cuauhtémoc <PERSON>\"><PERSON><PERSON>uhté<PERSON><PERSON></a>, Mexican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas\" title=\"Cuauhtémoc <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mexican politician", "links": [{"title": "Cuauhtémoc <PERSON>as", "link": "https://wikipedia.org/wiki/Cuauht%C3%A9moc_C%C3%A1rdenas"}]}, {"year": "1934", "text": "<PERSON>, American singer and pianist (d. 2005)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Horn\"><PERSON></a>, American singer and pianist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Horn\"><PERSON></a>, American singer and pianist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English actress and dancer (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Una_Stubbs"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actress, voice-over artist, author, and activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, voice-over artist, author, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, voice-over artist, author, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Hong Kong director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oo"}]}, {"year": "1948", "text": "<PERSON>, American sociologist and scholar", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English saxophonist, clarinet player, and composer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist, clarinet player, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist, clarinet player, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Scottish footballer and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Barbadian cricketer and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American photographer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English economist and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1954", "text": "<PERSON>, Canadian-American author and activist (d. 2011)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_fiction_author)\" title=\"<PERSON> (science fiction author)\"><PERSON></a>, Canadian-American author and activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_fiction_author)\" title=\"<PERSON> (science fiction author)\"><PERSON></a>, Canadian-American author and activist (d. 2011)", "links": [{"title": "<PERSON> (science fiction author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(science_fiction_author)"}]}, {"year": "1955", "text": "<PERSON>, Scottish politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American composer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_O%27Donnell"}]}, {"year": "1957", "text": "<PERSON>, Australian cricketer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Italian banker, director, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian banker, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian banker, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, French actress and playwright", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and playwright", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American judge", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Romanian actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maia_<PERSON>stern"}]}, {"year": "1964", "text": "<PERSON>, Dutch speed skater", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hon"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, American bass player and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/D%27ar<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>'arc<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American bass player and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27ar<PERSON>_<PERSON>\" title=\"<PERSON>'arc<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American bass player and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27ar<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Irish politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Indian actor and race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Yemeni terrorist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> <PERSON></a>, Yemeni terrorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> <PERSON></a>, Yemeni terrorist", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Cameroonian footballer (d. 2003)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>o%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>o%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian footballer (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>o%C3%A9"}]}, {"year": "1975", "text": "<PERSON>, English journalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Russian international footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian international footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American comedian", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Dale\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"James <PERSON> Dale\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American tennis player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(tennis)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Italian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Russian ice hockey player (d. 2003)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Roman_L<PERSON>ko\" title=\"Roman L<PERSON>ko\"><PERSON></a>, Russian ice hockey player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_L<PERSON>henko\" title=\"Roman Lyas<PERSON>ko\"><PERSON></a>, Russian ice hockey player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_L<PERSON>ko"}]}, {"year": "1980", "text": "<PERSON>, Belgian race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2010)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Belarusian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON><PERSON> (footballer, born 1982)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Northern Irish model and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Spanish tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Croatian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American drag queen", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drag queen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drag queen", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, French swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, South Korean actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON>ji<PERSON>\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin\" title=\"<PERSON>ji<PERSON>\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON>n", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jin"}]}, {"year": "1983", "text": "<PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Human_Tornado\" title=\"Human Tornado\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Human_Tornado\" title=\"Human Tornado\"><PERSON></a>, American wrestler", "links": [{"title": "Human Tornado", "link": "https://wikipedia.org/wiki/Human_Tornado"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Ecuadorian footballer (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Christian_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_Ben%C3%ADtez\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Ben%C3%ADtez"}]}, {"year": "1986", "text": "<PERSON>, Dutch politician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Italian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Israeli tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e%27er\" title=\"<PERSON><PERSON>'er\"><PERSON><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e%27er\" title=\"<PERSON><PERSON>'er\"><PERSON><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shahar_Pe%27er"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Indian actress and film producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and film producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Victoria_Mon%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Mon%C3%A9t\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Mon%C3%A9t"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>ooter_<PERSON><PERSON>\" title=\"Scooter Gennett\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oot<PERSON>_<PERSON><PERSON>\" title=\"Scooter Gennett\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ooter_<PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Australian actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stasey\" title=\"<PERSON><PERSON><PERSON> Stasey\"><PERSON><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>asey\" title=\"<PERSON><PERSON><PERSON> Stasey\"><PERSON><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stasey"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcus_Stroman"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian-Swedish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/YN<PERSON>_<PERSON>\" title=\"YN<PERSON>ly\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/YN<PERSON>_<PERSON>\" title=\"YN<PERSON>ly\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/YN<PERSON>_<PERSON>ly"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American social media influencer and dancer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Charli_D%27Amelio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American social media influencer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charli_<PERSON>%27Amelio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American social media influencer and dancer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charli_D%27Amelio"}]}, {"year": "2005", "text": "<PERSON>, Czech tennis player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1"}]}], "Deaths": [{"year": "408", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (b. 377)", "html": "408 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>us\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 377)", "links": [{"title": "Arcadius", "link": "https://wikipedia.org/wiki/Arcadius"}]}, {"year": "558", "text": "<PERSON><PERSON>, missionary and saint", "html": "558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Missionary\" title=\"Missionary\">missionary</a> and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Missionary"}]}, {"year": "908", "text": "<PERSON>, Chinese prince and pretender", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince and <a href=\"https://wikipedia.org/wiki/Pretender\" title=\"Pretender\">pretender</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince and <a href=\"https://wikipedia.org/wiki/Pretender\" title=\"Pretender\">pretender</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pretender", "link": "https://wikipedia.org/wiki/Pretender"}]}, {"year": "1118", "text": "<PERSON> of Scotland (b. 1080)", "html": "1118 - <a href=\"https://wikipedia.org/wiki/Matilda_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1080)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matilda_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON> of Scotland</a> (b. 1080)", "links": [{"title": "Matilda of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_of_Scotland"}]}, {"year": "1171", "text": "<PERSON><PERSON><PERSON>, King of Leinster (b. 1110)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mu<PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Leinster\" class=\"mw-redirect\" title=\"King of Leinster\">King of Leinster</a> (b. 1110)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Leinster\" class=\"mw-redirect\" title=\"King of Leinster\">King of Leinster</a> (b. 1110)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diarma<PERSON>_<PERSON>_<PERSON>"}, {"title": "King of Leinster", "link": "https://wikipedia.org/wiki/King_of_Leinster"}]}, {"year": "1187", "text": "<PERSON>, Grand Master of the Knights Hospitaller", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Grand_Master_(order)\" class=\"mw-redirect\" title=\"Grand Master (order)\">Grand Master</a> of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Grand_Master_(order)\" class=\"mw-redirect\" title=\"Grand Master (order)\">Grand Master</a> of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights Hospitaller</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Grand Master (order)", "link": "https://wikipedia.org/wiki/Grand_Master_(order)"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1255", "text": "<PERSON>, English prelate and statesman", "html": "1255 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prelate and statesman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prelate and statesman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1277", "text": "<PERSON> of Serbia (b. 1223)", "html": "1277 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1_I\" title=\"<PERSON> I\"><PERSON></a> of Serbia (b. 1223)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1_I\" title=\"<PERSON>š I\"><PERSON></a> of Serbia (b. 1223)", "links": [{"title": "<PERSON>š <PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Uro%C5%A1_I"}]}, {"year": "1278", "text": "<PERSON> of Villehardouin", "html": "1278 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Villehardouin\" title=\"<PERSON> of Villehardouin\"><PERSON> of Villehardouin</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Villehardouin\" title=\"<PERSON> of Villehardouin\"><PERSON> of Villehardouin</a>", "links": [{"title": "<PERSON> of Villehardouin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1308", "text": "<PERSON> of Germany (b. 1255)", "html": "1308 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (b. 1255)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (b. 1255)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Germany"}]}, {"year": "1312", "text": "<PERSON> of Bribir", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C5%A0ubi%C4%87_of_Bribir\" title=\"<PERSON> of Bribir\"><PERSON> of Bribir</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C5%A0ubi%C4%87_of_Bribir\" title=\"<PERSON> of Bribir\"><PERSON> of Bribir</a>", "links": [{"title": "<PERSON> of Bribir", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C5%A0ubi%C4%87_of_<PERSON><PERSON>bir"}]}, {"year": "1539", "text": "<PERSON> of Portugal (b. 1503)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1503)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Isabella_of_Portugal"}]}, {"year": "1555", "text": "<PERSON> <PERSON><PERSON> (b. 1501)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lus_II\" title=\"<PERSON> <PERSON>lus II\"><PERSON> <PERSON><PERSON> II</a> (b. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lus_II\" title=\"Pope Marcellus II\"><PERSON> <PERSON><PERSON> II</a> (b. 1501)", "links": [{"title": "<PERSON> <PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II"}]}, {"year": "1572", "text": "<PERSON> (b. 1504)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_V\" title=\"Pope Pius V\">Pope <PERSON> V</a> (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_V\" title=\"Pope Pius V\">Pope <PERSON> V</a> (b. 1504)", "links": [{"title": "Pope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1668", "text": "<PERSON><PERSON>, Flemish painter (b. 1604)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/Frans_Luycx\" title=\"Frans Luycx\"><PERSON><PERSON></a>, Flemish painter (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frans_Luycx\" title=\"Frans Luycx\"><PERSON><PERSON></a>, Flemish painter (b. 1604)", "links": [{"title": "Fr<PERSON>", "link": "https://wikipedia.org/wiki/Frans_Luycx"}]}, {"year": "1730", "text": "<PERSON>, French painter and engraver (b. 1645)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Troy\" title=\"<PERSON>\"><PERSON></a>, French painter and engraver (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Troy\" title=\"<PERSON>\"><PERSON></a>, French painter and engraver (b. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_de_Troy"}]}, {"year": "1731", "text": "<PERSON>, German violinist and composer (b. 1677)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (b. 1677)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "<PERSON>, 3rd Earl of Carlisle, English politician, First Lord of the Treasury (b. 1669)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Carlisle\" title=\"<PERSON>, 3rd Earl of Carlisle\"><PERSON>, 3rd Earl of Carlisle</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (b. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_<PERSON>_Carlisle\" title=\"<PERSON>, 3rd Earl <PERSON> Carlisle\"><PERSON>, 3rd Earl of Carlisle</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (b. 1669)", "links": [{"title": "<PERSON>, 3rd Earl of Carlisle", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Carlisle"}, {"title": "First Lord of the Treasury", "link": "https://wikipedia.org/wiki/First_Lord_of_the_Treasury"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON>, Polish-German historian, economist, and jurist (b. 1719)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German historian, economist, and jurist (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-German historian, economist, and jurist (b. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON>, French general (b. 1768)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8res\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1768)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8res"}]}, {"year": "1838", "text": "<PERSON>, French obstetrician and naturalist (b. 1797)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French obstetrician and naturalist (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French obstetrician and naturalist (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s"}]}, {"year": "1856", "text": "<PERSON>, American minister and theologian (b. 1774)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker_minister)\" title=\"<PERSON> (Quaker minister)\"><PERSON></a>, American minister and theologian (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker_minister)\" title=\"<PERSON> (Quaker minister)\"><PERSON></a>, American minister and theologian (b. 1774)", "links": [{"title": "<PERSON> (Quaker minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_(Quaker_minister)"}]}, {"year": "1873", "text": "<PERSON>, Scottish-English missionary and explorer (b. 1813)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English missionary and explorer (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English missionary and explorer (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, German physiologist and physician (b. 1824)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON>ner\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ludwig_B%C3%<PERSON><PERSON>ner"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Czech composer and academic (b. 1841)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and academic (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech composer and academic (b. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Dvo%C5%99%C3%A1k"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek philanthropist (b. 1831)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philanthropist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek philanthropist (b. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>lis"}]}, {"year": "1913", "text": "<PERSON>, American lieutenant (b. 1850)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "Princess <PERSON> Connaught (b. 1882)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Connaught\" title=\"Princess <PERSON> of Connaught\">Princess <PERSON> of Connaught</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Connaught\" title=\"Princess <PERSON> of Connaught\">Princess <PERSON> of Connaught</a> (b. 1882)", "links": [{"title": "Princess <PERSON> of Connaught", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_Con<PERSON>ught"}]}, {"year": "1935", "text": "<PERSON>, French cyclist (b. 1889)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lissier\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lissier\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_P%C3%A9lissier"}]}, {"year": "1943", "text": "<PERSON>, Norwegian religious leader, founded the Brunstad Christian Church (b. 1871)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Brunstad_Christian_Church\" title=\"Brunstad Christian Church\">Brunstad Christian Church</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Brunstad_Christian_Church\" title=\"Brunstad Christian Church\">Brunstad Christian Church</a> (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Brunstad Christian Church", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Christian_Church"}]}, {"year": "1944", "text": "<PERSON>, Greek communist and trade unionist (b. 1909)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek communist and trade unionist (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek communist and trade unionist (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German lawyer and politician, Chancellor of Germany (b. 1897)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German wife of <PERSON> (b. 1901)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American painter and illustrator (b. 1876)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shin<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Everett_<PERSON>n"}]}, {"year": "1955", "text": "<PERSON>, American stockbroker and survivor of the sinking of the RMS Titanic (b. 1883)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stockbroker and survivor of the <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Titanic\" title=\"Sinking of the Titanic\">sinking of the RMS <i>Titanic</i></a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stockbroker and survivor of the <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Titanic\" title=\"Sinking of the Titanic\">sinking of the RMS <i>Titanic</i></a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sinking of the Titanic", "link": "https://wikipedia.org/wiki/Sinking_of_the_Titanic"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, American pole vaulter (b. 1883)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pole vaulter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pole vaulter (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English architect, designed the Bristol Central Library (b. 1875)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Bristol_Central_Library\" title=\"Bristol Central Library\">Bristol Central Library</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Bristol_Central_Library\" title=\"Bristol Central Library\">Bristol Central Library</a> (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bristol Central Library", "link": "https://wikipedia.org/wiki/Bristol_Central_Library"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Filipino lawyer and politician (b. 1879)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> K. <PERSON>\"><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1879)", "links": [{"title": "Lope K. Santos", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer and bandleader (b. 1911)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bandleader (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian-American ice hockey player, coach, and manager (b. 1895)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English author and politician (b. 1886)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Korean prince (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Un\"><PERSON></a>, Korean prince (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Un\"><PERSON></a>, Korean prince (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Danish painter and sculptor (b. 1914)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jo<PERSON>\"><PERSON><PERSON></a>, Danish painter and sculptor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish painter and sculptor (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American surgeon and activist (b. 1908)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American surgeon and activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American surgeon and activist (b. 1908)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Greek poet and politician (b. 1939)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and politician (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Armenian composer and conductor (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian composer and conductor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian composer and conductor (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aram_Khachaturian"}]}, {"year": "1982", "text": "<PERSON>, Scottish viola player and educator (b. 1903)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish viola player and educator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish viola player and educator (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian-Swedish runner (b. 1891)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Swedish runner (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Swedish runner (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English journalist and author (b. 1897)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, English comedian, actress and music hall performer (b. 1905)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actress and music hall performer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actress and music hall performer (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American songwriter and producer (b. 1916)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian sailor and architect (b. 1936)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sailor and architect (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sailor and architect (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American journalist (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist (b. 1912)", "links": [{"title": "<PERSON> (editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)"}]}, {"year": "1989", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan civil servant (b. 1930)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/V._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan civil servant (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan civil servant (b. 1930)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Canadian farmer and politician (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Canadian farmer and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Canadian farmer and politician (b. 1904)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(politician)"}]}, {"year": "1990", "text": "<PERSON>, Italian-American tenor and actor (b. 1926)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American tenor and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American tenor and actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American director and screenwriter (b. 1896)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French metallurgist and politician, Prime Minister of France (b. 1925)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A9govoy\" title=\"<PERSON>\"><PERSON></a>, French metallurgist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A9govoy\" title=\"<PERSON>\"><PERSON></a>, French metallurgist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_B%C3%A9r%C3%A9govoy"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Sri Lankan politician, 3rd President of Sri Lanka (b. 1924)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Brazilian race car driver (b. 1960)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayr<PERSON>_Senna"}]}, {"year": "1995", "text": "<PERSON>, Italian-American painter (b. 1892)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American painter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American painter (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Canadian sociologist, philosopher, and poet (b. 1927)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sociologist, philosopher, and poet (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sociologist, philosopher, and poet (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American author and activist (b. 1935)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cleaver\" title=\"<PERSON><PERSON> Cleaver\"><PERSON><PERSON></a>, American author and activist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cleaver\" title=\"<PERSON><PERSON> Cleaver\"><PERSON><PERSON></a>, American author and activist (b. 1935)", "links": [{"title": "<PERSON><PERSON> Cleaver", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Cleaver"}]}, {"year": "2000", "text": "<PERSON>, American bodybuilder and actor (b. 1926)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Indian poet and author (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American wrestler and manager (b. 1960)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Miss Elizabeth\">Miss <PERSON></a>, American wrestler and manager (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Miss Elizabeth\">Miss <PERSON></a>, American wrestler and manager (b. 1960)", "links": [{"title": "Miss <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Dutch cyclist (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American psychologist and academic (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>(psychologist)\" class=\"mw-redirect\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(psychologist)\" class=\"mw-redirect\" title=\"<PERSON> (psychologist)\"><PERSON></a>, American psychologist and academic (b. 1914)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>(psychologist)"}]}, {"year": "2008", "text": "<PERSON>, Maltese judge and politician, 1st President of Malta (b. 1909)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese judge and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese judge and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Malta\" title=\"President of Malta\">President of Malta</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Malta", "link": "https://wikipedia.org/wiki/President_of_Malta"}]}, {"year": "2008", "text": "<PERSON>, German soldier and economist (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and economist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and economist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American actress (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English boxer (b. 1934)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English sportscaster (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian engineer and politician, 29th Lieutenant Governor of Nova Scotia (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician, 29th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia\" title=\"Lieutenant Governor of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer and politician, 29th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia\" title=\"Lieutenant Governor of Nova Scotia\">Lieutenant Governor of Nova Scotia</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Nova Scotia", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Nova_Scotia"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, German-Israeli lawyer and politician (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Morde<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Morde<PERSON><PERSON> V<PERSON>hu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Israeli lawyer and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Morde<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Morde<PERSON><PERSON> V<PERSON>hu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Israeli lawyer and politician (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American rapper  (b. 1978)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rapper)\" class=\"mw-redirect\" title=\"<PERSON> (rapper)\"><PERSON></a>, American rapper (b. 1978)", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rapper)"}]}, {"year": "2013", "text": "<PERSON>, French footballer and manager (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Nigerian lawyer and politician, 5th Governor of Kwara State (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kwara_State\" class=\"mw-redirect\" title=\"List of Governors of Kwara State\">Governor of Kwara State</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kwara_State\" class=\"mw-redirect\" title=\"List of Governors of Kwara State\">Governor of Kwara State</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta"}, {"title": "List of Governors of Kwara State", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Kwara_State"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Tunisian-American computer scientist and academic (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisian-American computer scientist and academic (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tunisian-American computer scientist and academic (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Israeli actor, director, and screenwriter (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and screenwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor, director, and screenwriter (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an"}]}, {"year": "2014", "text": "<PERSON>, Mexican footballer and coach (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and coach (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and coach (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-Manx motorcycle racer (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Manx motorcycle racer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Manx motorcycle racer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Azerbaijani political scientist, academic, and diplomat (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>afa_<PERSON>ulu<PERSON>e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani political scientist, academic, and diplomat (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Azerbaijani political scientist, academic, and diplomat (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vafa_Guluzade"}]}, {"year": "2015", "text": "<PERSON>, Mexican actress, singer, director, and screenwriter (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, singer, director, and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican actress, singer, director, and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actress (b. 1931)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olympia_Dukakis"}]}, {"year": "2023", "text": "<PERSON>, Canadian singer-songwriter and guitarist (b. 1938)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>foot"}]}]}}